*** Settings ***
Documentation     Test output.xml compatibility with old Robot versions and otherwise.
...
...               RF version specific output.xml files have been generated by running
...               ``./rundevel.py atest/testdata/misc/`` in appropriate version control tag.
Resource          rebot_resource.robot

*** Test Cases ***
RF 3.2 compatibility
    Run Rebot And Validate Statistics    rebot/output-3.2.2.xml    172    10    validate=False

RF 4.0 compatibility
    Run Rebot And Validate Statistics    rebot/output-4.0.xml      172    10

RF 5.0 compatibility
    Run Rebot And Validate Statistics    rebot/output-5.0.xml      175    10

Suite only
    Run Rebot And Validate Statistics    rebot/suite_only.xml      179    10    3

Message directly under test
    Run Rebot And Validate Statistics    rebot/issue-3762.xml      1      0
    ${tc} =    Check Test Case    test A
    Check Log Message    ${tc[0]}       Hi from test          WARN
    Check Log Message    ${tc[1, 0]}    Hi from keyword       WARN
    Check Log Message    ${tc[2]}       Hi from test again    INFO

*** Keywords ***
Run Rebot And Validate Statistics
    [Arguments]    ${path}    ${passed}    ${failed}    ${skipped}=0    ${validate}=True
    Run Rebot    ${EMPTY}    ${path}    validate output=${validate}
    ${total}    ${passed}    ${failed}    ${skipped} =
    ...    Evaluate    ${passed} + ${failed} + ${skipped}, ${passed}, ${failed}, ${skipped}
    Should Be Equal    ${SUITE.statistics.total}      ${total}
    Should Be Equal    ${SUITE.statistics.passed}     ${passed}
    Should Be Equal    ${SUITE.statistics.failed}     ${failed}
    Should Be Equal    ${SUITE.statistics.skipped}    ${skipped}
