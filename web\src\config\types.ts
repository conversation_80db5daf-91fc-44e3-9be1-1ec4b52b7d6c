export interface ComponentSchema {
  type: string
  label: string
  category: string
  description: string
  icon: string
  color: string
  inputs: string[]
  outputs: string[]
  config: {
    [key: string]: {
      type: string
      label: string
      default?: any
      placeholder?: string
      description?: string
      min?: number
      max?: number
      options?: Array<{ label: string; value: string | number }>
      condition?: Record<string, any>
    }
  }
}
