import { createApp } from 'vue';
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 自定义scss样式
import '@/styles/index.scss'
// 自定义iconfont样式
import '@/assets/fonts/iconfont.css';
// 自定义panel-iconfont样式 左侧面板指令图标
import '@/assets/action-fonts/iconfont.css';

import { api } from '@/api';

import Vant from 'vant';
import moment from 'moment';
window.moment = moment;

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Vant)

const InitReady = () => {
  return new Promise(resolve => {
    Promise.all([
      api.initUserInfo(),
    ]).finally(async ()=>{
      resolve(true)
    })
  })
};

InitReady().then(()=>{
  app.mount('#app')
})
