/**
 * 系统操作组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const runCommandSchema: ComponentConfigSchema = {
  componentType: 'run_command',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '命令执行的基本参数',
      icon: 'Terminal',
      order: 1,
      collapsible: false,
    },
    {
      id: 'environment',
      label: '环境设置',
      description: '命令执行环境的配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '命令执行的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    command: {
      type: 'textarea',
      label: '执行命令',
      description: '要执行的系统命令',
      placeholder: 'dir\necho "Hello World"',
      required: true,
      group: 'basic',
      order: 1,
      rows: 3,
      validation: [
        {
          type: 'required',
          message: '执行命令不能为空',
        },
      ],
    },

    shell: {
      type: 'boolean',
      label: '使用Shell',
      description: '是否通过系统Shell执行命令',
      help: '启用后可以使用管道、重定向等Shell功能',
      group: 'basic',
      order: 2,
      default: true,
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '命令执行的最大等待时间',
      group: 'basic',
      order: 3,
      default: 30,
      min: 1,
      max: 3600,
      unit: '秒',
    },

    capture_output: {
      type: 'boolean',
      label: '捕获输出',
      description: '是否捕获命令的标准输出和错误输出',
      group: 'basic',
      order: 4,
      default: true,
    },

    working_directory: {
      type: 'file',
      label: '工作目录',
      description: '命令执行的工作目录',
      placeholder: '留空使用当前目录',
      group: 'environment',
      order: 1,
      accept: 'directory',
    },

    environment_variables: {
      type: 'json',
      label: '环境变量',
      description: '命令执行时的环境变量（JSON格式）',
      placeholder: `{
  "PATH": "/usr/bin",
  "HOME": "/home/<USER>"
}`,
      group: 'environment',
      order: 2,
      rows: 6,
    },

    inherit_environment: {
      type: 'boolean',
      label: '继承环境变量',
      description: '是否继承当前进程的环境变量',
      group: 'environment',
      order: 3,
      default: true,
    },

    encoding: {
      type: 'select',
      label: '字符编码',
      description: '命令输出的字符编码',
      group: 'advanced',
      order: 1,
      default: 'utf-8',
      options: [
        { label: 'UTF-8', value: 'utf-8', description: '通用Unicode编码' },
        { label: 'GBK', value: 'gbk', description: '中文Windows系统编码' },
        { label: 'ASCII', value: 'ascii', description: '基本ASCII编码' },
        { label: 'Latin-1', value: 'latin-1', description: '西欧语言编码' },
      ],
    },

    check_return_code: {
      type: 'boolean',
      label: '检查返回码',
      description: '是否检查命令返回码，非零时抛出错误',
      group: 'advanced',
      order: 2,
      default: true,
    },

    log_command: {
      type: 'boolean',
      label: '记录命令',
      description: '是否在日志中记录执行的命令',
      group: 'advanced',
      order: 3,
      default: true,
    },

    log_output: {
      type: 'boolean',
      label: '记录输出',
      description: '是否在日志中记录命令输出',
      group: 'advanced',
      order: 4,
      default: false,
    },

    run_as_admin: {
      type: 'boolean',
      label: '管理员权限',
      description: '是否以管理员权限执行命令（Windows）',
      help: '需要UAC确认或已有管理员权限',
      group: 'advanced',
      order: 5,
      default: false,
    },
  },

  presets: {
    simple: {
      label: '简单命令',
      description: '执行简单系统命令的配置',
      config: {
        shell: true,
        timeout: 30,
        capture_output: true,
        check_return_code: true,
      },
    },

    script: {
      label: '脚本执行',
      description: '执行复杂脚本的配置',
      config: {
        shell: true,
        timeout: 300,
        capture_output: true,
        log_command: true,
        log_output: true,
        inherit_environment: true,
      },
    },

    admin: {
      label: '管理员命令',
      description: '需要管理员权限的命令配置',
      config: {
        shell: true,
        timeout: 60,
        capture_output: true,
        run_as_admin: true,
        check_return_code: true,
      },
    },
  },


}

export const processListSchema: ComponentConfigSchema = {
  componentType: 'process_list',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '进程列表获取的基本参数',
      icon: 'List',
      order: 1,
      collapsible: false,
    },
    {
      id: 'filter',
      label: '过滤条件',
      description: '进程过滤的配置',
      icon: 'Filter',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '进程信息的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    filter_name: {
      type: 'string',
      label: '进程名称过滤',
      description: '按进程名称过滤（支持通配符）',
      placeholder: 'chrome.exe 或 *python*',
      group: 'filter',
      order: 1,
    },

    filter_user: {
      type: 'string',
      label: '用户过滤',
      description: '按运行用户过滤进程',
      placeholder: 'Administrator 或 SYSTEM',
      group: 'filter',
      order: 2,
    },

    filter_status: {
      type: 'select',
      label: '状态过滤',
      description: '按进程状态过滤',
      group: 'filter',
      order: 3,
      default: 'all',
      options: [
        { label: '全部', value: 'all', description: '显示所有状态的进程' },
        { label: '运行中', value: 'running', description: '只显示运行中的进程' },
        { label: '睡眠', value: 'sleeping', description: '只显示睡眠状态的进程' },
        { label: '僵尸', value: 'zombie', description: '只显示僵尸进程' },
      ],
    },

    include_children: {
      type: 'boolean',
      label: '包含子进程',
      description: '是否包含子进程信息',
      group: 'basic',
      order: 1,
      default: false,
    },

    include_threads: {
      type: 'boolean',
      label: '包含线程信息',
      description: '是否包含进程的线程信息',
      group: 'advanced',
      order: 1,
      default: false,
    },

    include_memory: {
      type: 'boolean',
      label: '包含内存信息',
      description: '是否包含进程的内存使用信息',
      group: 'advanced',
      order: 2,
      default: true,
    },

    include_cpu: {
      type: 'boolean',
      label: '包含CPU信息',
      description: '是否包含进程的CPU使用信息',
      group: 'advanced',
      order: 3,
      default: true,
    },

    sort_by: {
      type: 'select',
      label: '排序方式',
      description: '进程列表的排序方式',
      group: 'advanced',
      order: 4,
      default: 'pid',
      options: [
        { label: '进程ID', value: 'pid', description: '按进程ID排序' },
        { label: '进程名称', value: 'name', description: '按进程名称排序' },
        { label: '内存使用', value: 'memory', description: '按内存使用量排序' },
        { label: 'CPU使用', value: 'cpu', description: '按CPU使用率排序' },
        { label: '创建时间', value: 'create_time', description: '按进程创建时间排序' },
      ],
    },

    sort_order: {
      type: 'select',
      label: '排序顺序',
      description: '排序的顺序',
      group: 'advanced',
      order: 5,
      default: 'asc',
      options: [
        { label: '升序', value: 'asc', description: '从小到大排序' },
        { label: '降序', value: 'desc', description: '从大到小排序' },
      ],
    },

    limit: {
      type: 'number',
      label: '结果限制',
      description: '返回的最大进程数量（0表示不限制）',
      group: 'advanced',
      order: 6,
      default: 0,
      min: 0,
      max: 10000,
    },
  },

  presets: {
    all_processes: {
      label: '所有进程',
      description: '获取所有进程的基本信息',
      config: {
        include_children: false,
        include_memory: true,
        include_cpu: true,
        sort_by: 'name',
        sort_order: 'asc',
      },
    },

    memory_usage: {
      label: '内存使用排序',
      description: '按内存使用量排序的进程列表',
      config: {
        include_memory: true,
        include_cpu: false,
        sort_by: 'memory',
        sort_order: 'desc',
        limit: 20,
      },
    },

    specific_process: {
      label: '特定进程',
      description: '查找特定名称的进程',
      config: {
        include_children: true,
        include_memory: true,
        include_cpu: true,
        include_threads: true,
        sort_by: 'pid',
      },
    },
  },
}

export const pythonExecuteSchema: ComponentConfigSchema = {
  componentType: 'python_execute',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: 'Python代码执行的基本参数,最终输出结果必须赋值到result参数,result内容必须是字典',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      // description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    /*{
      id: 'environment',
      label: '执行环境',
      description: 'Python执行环境的配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: 'Python执行的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },*/
  ],

  fields: {
    code: {
      type: 'textarea',
      label: 'Python代码',
      description: '要执行的Python代码片段,最终输出结果必须赋值到result参数,result内容必须是字典',
      placeholder: 'result = {\n "arg1":"参数1"\n}',
      group: 'basic',
      order: 1,
      rows: 8,
      language: 'python',
      variableSupport: true,
      validation: [
        {
          type: 'custom',
          validator: (value: string, config: any) => {
            // 确保config存在
            if (!config) return true

            if (!value && !config?.script_file) {
              return '必须指定Python代码或脚本文件'
            }
            if (value && config?.script_file) {
              return '不能同时指定代码和脚本文件'
            }
            return true
          },
        },
      ],
    },

    // script_file: {
    //   type: 'file',
    //   label: 'Python脚本文件',
    //   description: '要执行的Python脚本文件路径',
    //   placeholder: '选择.py文件',
    //   group: 'basic',
    //   order: 2,
    //   accept: '.py',
    //   validation: [
    //     {
    //       type: 'custom',
    //       validator: (value: string, config: any) => {
    //         // 确保config存在
    //         if (!config) return true
    //
    //         if (!value && !config?.code) {
    //           return '必须指定Python代码或脚本文件'
    //         }
    //         if (value && config?.code) {
    //           return '不能同时指定代码和脚本文件'
    //         }
    //         return true
    //       },
    //     },
    //   ],
    // },

    /*python_path: {
      type: 'string',
      label: 'Python解释器路径',
      description: 'Python解释器的路径或命令',
      placeholder: 'python 或 python3 或 C:\\Python39\\python.exe',
      group: 'environment',
      order: 1,
      default: 'python',
    },*/

    /*timeout: {
      type: 'number',
      label: '超时时间',
      description: 'Python代码执行的最大等待时间',
      group: 'basic',
      order: 3,
      default: 60,
      min: 1,
      max: 3600,
      unit: '秒',
    },*/

    capture_output: {
      type: 'boolean',
      label: '捕获输出',
      description: '是否捕获Python代码的输出和错误信息',
      group: 'basic',
      order: 4,
      default: true,
    },

    // working_directory: {
    //   type: 'file',
    //   label: '工作目录',
    //   description: 'Python代码执行的工作目录',
    //   placeholder: '留空使用当前目录',
    //   group: 'environment',
    //   order: 2,
    //   accept: 'directory',
    //   variableSupport: true,
    // },
    //
    // environment_vars: {
    //   type: 'json',
    //   label: '环境变量',
    //   description: 'Python执行时的环境变量（JSON格式）',
    //   placeholder: '{ "PYTHONPATH": "/path/to/modules", "DEBUG": "1" }',
    //   group: 'environment',
    //   order: 3,
    //   rows: 4,
    //   variableSupport: true,
    // },
    //
    // arguments: {
    //   type: 'string',
    //   label: '命令行参数',
    //   description: '传递给Python脚本的命令行参数',
    //   placeholder: '--verbose --config config.json',
    //   group: 'environment',
    //   order: 4,
    //   variableSupport: true,
    // },
    //
    // install_requirements: {
    //   type: 'boolean',
    //   label: '自动安装依赖',
    //   description: '是否自动安装requirements.txt中的依赖',
    //   group: 'advanced',
    //   order: 1,
    //   default: false,
    // },
    //
    // virtual_env: {
    //   type: 'file',
    //   label: '虚拟环境路径',
    //   description: '使用指定的Python虚拟环境',
    //   placeholder: '选择虚拟环境目录',
    //   group: 'advanced',
    //   order: 2,
    //   accept: 'directory',
    // },
    //
    // encoding: {
    //   type: 'select',
    //   label: '字符编码',
    //   description: 'Python输出的字符编码',
    //   group: 'advanced',
    //   order: 3,
    //   default: 'utf-8',
    //   options: [
    //     { label: 'UTF-8', value: 'utf-8', description: '通用Unicode编码' },
    //     { label: 'GBK', value: 'gbk', description: '中文Windows系统编码' },
    //     { label: 'ASCII', value: 'ascii', description: '基本ASCII编码' },
    //   ],
    // },
    //
    // check_syntax: {
    //   type: 'boolean',
    //   label: '语法检查',
    //   description: '执行前检查Python代码语法',
    //   group: 'advanced',
    //   order: 4,
    //   default: true,
    // },

    // 输出变量配置
    output_variable: {
      type: 'variables_map',
      hideRealKey: true,
      label: '输出变量名(提取)',
      description: '将Python输出结果存储到指定变量',
      placeholder: '',
      group: 'response',
      order: 5,
      outputVariable: true,
    },

    // error_variable: {
    //   type: 'string',
    //   label: '错误变量名',
    //   description: '将Python错误信息存储到指定变量',
    //   placeholder: 'python_error',
    //   group: 'basic',
    //   order: 6,
    //   outputVariable: true,
    // },

    // return_code_variable: {
    //   type: 'string',
    //   label: '返回码变量名',
    //   description: '将Python执行返回码存储到指定变量',
    //   placeholder: 'python_return_code',
    //   group: 'basic',
    //   order: 7,
    //   outputVariable: true,
    // },
  },

  presets: {
    simple: {
      label: '简单脚本',
      description: '执行简单Python代码的配置',
      config: {
        timeout: 30,
        capture_output: true,
        check_syntax: true,
      },
    },

    data_processing: {
      label: '数据处理',
      description: '数据处理脚本的配置',
      config: {
        timeout: 300,
        capture_output: true,
        install_requirements: true,
        check_syntax: true,
      },
    },

    ml_script: {
      label: '机器学习',
      description: '机器学习脚本的配置',
      config: {
        timeout: 1800,
        capture_output: true,
        install_requirements: true,
        environment_vars: '{"CUDA_VISIBLE_DEVICES": "0"}',
      },
    }
  },
  test:true

}

export const pythonEvaluateSchema: ComponentConfigSchema = {
  componentType: 'python_evaluate',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: 'Python表达式求值的基本参数',
      icon: 'Calculator',
      order: 1,
      collapsible: false,
    },
    {
      id: 'context',
      label: '执行上下文',
      description: '表达式执行的上下文配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
  ],

  fields: {
    expression: {
      type: 'textarea',
      label: 'Python表达式',
      description: '要求值的Python表达式',
      placeholder: '2 + 2\nlen("Hello World")\nmax([1, 2, 3, 4, 5])',
      required: true,
      group: 'basic',
      order: 1,
      rows: 3,
      language: 'python',
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'Python表达式不能为空',
        },
      ],
    },

    variables: {
      type: 'json',
      label: '变量上下文',
      description: '表达式中可用的变量（JSON格式）',
      placeholder: '{ "x": 10, "y": 20, "name": "test" }',
      group: 'context',
      order: 1,
      rows: 4,
      variableSupport: true,
    },

    modules: {
      type: 'string',
      label: '导入模块',
      description: '需要导入的Python模块（逗号分隔）',
      placeholder: 'math, datetime, json',
      group: 'context',
      order: 2,
    },

    result_variable: {
      type: 'string',
      label: '结果变量名',
      description: '将求值结果存储到指定变量',
      placeholder: 'calculation_result',
      group: 'basic',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'required',
          message: '结果变量名不能为空',
        },
        {
          type: 'pattern',
          pattern: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    safe_mode: {
      type: 'boolean',
      label: '安全模式',
      description: '启用安全模式，限制可执行的操作',
      group: 'context',
      order: 3,
      default: true,
    },
  },

  presets: {
    simple: {
      label: '简单计算',
      description: '简单数学表达式求值',
      config: {
        safe_mode: true,
      },
    },

    advanced: {
      label: '高级计算',
      description: '使用数学库的复杂计算',
      config: {
        modules: 'math, statistics',
        safe_mode: false,
      },
    },
  },

  examples: [
    {
      title: '数学计算',
      description: '基本数学运算',
      config: {
        expression: '(10 + 5) * 2',
        result_variable: 'math_result',
      },
    },
    {
      title: '字符串处理',
      description: '字符串操作',
      config: {
        expression: '"Hello World".upper().replace(" ", "_")',
        result_variable: 'string_result',
      },
    },
    {
      title: '使用变量',
      description: '在表达式中使用变量',
      config: {
        expression: 'x * y + z',
        variables: '{"x": 10, "y": 5, "z": 3}',
        result_variable: 'var_result',
      },
    },
  ],
}

export const pythonImportSchema: ComponentConfigSchema = {
  componentType: 'python_import',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Python模块导入的基本参数',
      icon: 'Package',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '模块导入的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
  ],

  fields: {
    module_name: {
      type: 'string',
      label: '模块名称',
      description: '要导入的Python模块名称',
      placeholder: 'requests, pandas, numpy',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '模块名称不能为空',
        },
      ],
    },

    alias: {
      type: 'string',
      label: '别名',
      description: '为导入的模块设置别名',
      placeholder: 'pd, np, plt',
      group: 'basic',
      order: 2,
    },

    from_module: {
      type: 'string',
      label: '从模块导入',
      description: '从指定模块中导入子模块或函数',
      placeholder: 'sklearn.model_selection',
      group: 'basic',
      order: 3,
    },

    install_if_missing: {
      type: 'boolean',
      label: '自动安装',
      description: '如果模块不存在，是否自动安装',
      group: 'advanced',
      order: 1,
      default: false,
    },

    version: {
      type: 'string',
      label: '指定版本',
      description: '安装时指定模块版本',
      placeholder: '>=1.0.0, <2.0.0',
      group: 'advanced',
      order: 2,
    },

    pip_options: {
      type: 'string',
      label: 'pip选项',
      description: '额外的pip安装选项',
      placeholder: '--upgrade --no-cache-dir',
      group: 'advanced',
      order: 3,
    },
  },

  presets: {
    data_science: {
      label: '数据科学',
      description: '常用数据科学库导入配置',
      config: {
        install_if_missing: true,
      },
    },

    web_scraping: {
      label: '网页抓取',
      description: '网页抓取相关库导入配置',
      config: {
        install_if_missing: true,
        pip_options: '--upgrade',
      },
    },
  },

  examples: [
    {
      title: '导入pandas',
      description: '导入pandas数据处理库',
      config: {
        module_name: 'pandas',
        alias: 'pd',
        install_if_missing: true,
      },
    },
    {
      title: '导入特定函数',
      description: '从模块中导入特定函数',
      config: {
        module_name: 'train_test_split',
        from_module: 'sklearn.model_selection',
        install_if_missing: true,
      },
    },
  ],
}



export const javascriptExecuteSchema: ComponentConfigSchema = {
  componentType: 'javascript_execute',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'JavaScript代码执行的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },

    {
      id: 'response',
      label: '指令输出',
      // description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    // {
    //   id: 'environment',
    //   label: '执行环境',
    //   description: 'JavaScript执行环境的配置',
    //   icon: 'Setting',
    //   order: 2,
    //   collapsible: true,
    //   collapsed: true,
    // },
    // {
    //   id: 'advanced',
    //   label: '高级选项',
    //   description: 'JavaScript执行的高级配置',
    //   icon: 'Setting',
    //   order: 3,
    //   collapsible: true,
    //   collapsed: true,
    //   advanced: true,
    // },
  ],

  fields: {
    code: {
      type: 'textarea',
      label: 'JavaScript代码',
      description: '要执行的JavaScript代码片段',
      placeholder: 'console.log("Hello World");\nconst result = 1 + 1;',
      group: 'basic',
      order: 1,
      rows: 8,
      language: 'javascript',
      variableSupport: true,
      validation: [
        {
          type: 'custom',
          validator: (value: string, config: any) => {
            // 确保config存在
            if (!config) return true

            if (!value && !config?.script_file) {
              return '必须指定JavaScript代码或脚本文件'
            }
            if (value && config?.script_file) {
              return '不能同时指定代码和脚本文件'
            }
            return true
          },
        },
      ],
    },

    // script_file: {
    //   type: 'file',
    //   label: 'JavaScript脚本文件',
    //   description: '要执行的JavaScript文件路径',
    //   placeholder: '选择.js文件',
    //   group: 'basic',
    //   order: 2,
    //   accept: '.js',
    // },

    // node_path: {
    //   type: 'string',
    //   label: 'Node.js路径',
    //   description: 'Node.js执行路径',
    //   placeholder: 'node 或 /usr/bin/node',
    //   group: 'environment',
    //   order: 1,
    //   default: 'node',
    // },

    capture_output: {
      type: 'boolean',
      label: '捕获输出',
      description: '是否捕获JavaScript代码的输出和错误信息',
      group: 'basic',
      order: 4,
      default: true,
    },

    // working_directory: {
    //   type: 'file',
    //   label: '工作目录',
    //   description: 'JavaScript代码执行的工作目录',
    //   placeholder: '留空使用当前目录',
    //   group: 'environment',
    //   order: 2,
    //   accept: 'directory',
    //   variableSupport: true,
    // },

    // arguments: {
    //   type: 'string',
    //   label: '命令行参数',
    //   description: '传递给JavaScript脚本的命令行参数',
    //   placeholder: '--verbose --config config.json',
    //   group: 'environment',
    //   order: 3,
    //   variableSupport: true,
    // },

    // encoding: {
    //   type: 'select',
    //   label: '字符编码',
    //   description: 'JavaScript输出的字符编码',
    //   group: 'advanced',
    //   order: 1,
    //   default: 'utf-8',
    //   options: [
    //     { label: 'UTF-8', value: 'utf-8', description: '通用Unicode编码' },
    //     { label: 'GBK', value: 'gbk', description: '中文Windows系统编码' },
    //     { label: 'ASCII', value: 'ascii', description: '基本ASCII编码' },
    //   ],
    // },

    // // 输出变量配置
    // output_variable: {
    //   type: 'string',
    //   label: '输出变量名',
    //   description: '将JavaScript输出结果存储到指定变量',
    //   placeholder: 'js_result',
    //   group: 'basic',
    //   order: 5,
    // },

    // error_variable: {
    //   type: 'string',
    //   label: '错误变量名',
    //   description: '将JavaScript错误信息存储到指定变量',
    //   placeholder: 'js_error',
    //   group: 'basic',
    //   order: 6,
    // },
    output_variable: {
      type: 'stringArray',
      label: '输出变量',
      description: '将Javascript输出结果存储到指定变量',
      placeholder: 'javascript_result',
      group: 'response',
      order: 5,
      addVariableDatas: [],
      variableSupport: true,
      outputVariable: true,
    },

    // return_code_variable: {
    //   type: 'string',
    //   label: '返回码变量名',
    //   description: '将JavaScript执行返回码存储到指定变量',
    //   placeholder: 'js_return_code',
    //   group: 'basic',
    //   order: 7,
    // },
  },

  presets: {
    simple: {
      label: '简单脚本',
      description: '执行简单JavaScript代码的配置',
      config: {
        code: 'console.log(1)',
        script_file: null,
        working_directory: null,
        timeout: 30,
        capture_output: true,
      },
    },

    node_script: {
      label: 'Node.js脚本',
      description: '执行Node.js脚本的配置',
      config: {
        code: null,
        script_file: 'script.js',
        working_directory: './scripts',
        timeout: 300,
      },
    },
  },

  examples: [
    {
      title: '简单计算',
      description: '执行简单的数学计算',
      config: {
        code: 'const result = 10 + 20;\nconsole.log(`计算结果: ${result}`);',
        script_file: null,
        working_directory: null,
        timeout: 10,
      },
    },
    {
      title: '文件处理',
      description: '使用Node.js处理文件',
      config: {
        code: 'const fs = require("fs");\nconst files = fs.readdirSync(".");\nconsole.log(`当前目录文件数: ${files.length}`);',
        script_file: null,
        working_directory: null,
        timeout: 30,
      },
    },
  ],
};
