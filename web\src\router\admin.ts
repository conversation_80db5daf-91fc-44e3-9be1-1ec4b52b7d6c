import { createRouter, createWebHashHistory } from 'vue-router'
import { useLoadingStore } from '@/stores/loading';

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/versionManager',
    },
    {
      path: '/versionManager',
      name: 'versionManager',
      component: () => import('../views/admin/versionManager/index.vue'),
      meta: {
        title: '版本管理'
      },
     },
    {
      path: '/subscribe',
      name: 'subscribe',
      component: () => import('../views/admin/subscribe/index.vue'),
      meta: {
        title: '订阅管理'
      },
     },
    {
      path: '/servicePackage',
      name: 'servicePackage',
      component: () => import('../views/admin/servicePackage/index.vue'),
      meta: {
        title: '服务包管理'
      },
     },
    {
      path: '/instruction',
      name: 'instruction',
      component: () => import('../views/admin/instruction/index.vue'),
      meta: {
        title: '指令管理'
      },
     },
     
    {
      path: '/content',
      name: 'content',
      component: () => import('../views/admin/content/index.vue'),
      meta: {
        title: '内容管理'
      },
     },
     {
      path: '/envConfig',
      name: 'envConfig',
      component: () => import('../views/admin/envConfig/index.vue'),
      meta: {
        title: '环境配置'
      },
     },
     
     {
      path: '/customer',
      name: 'customer',
      component: () => import('../views/admin/customer/index.vue'),
      meta: {
        title: '客户申请'
      },
     }

  ],
})
// 3. 在 router.js 中添加全局前置守卫
router.beforeEach(async (to, from, next) => {

  try {
    const loadingStore = useLoadingStore()
    if(loadingStore.isFirst) {
      // 模拟异步操作（如获取用户信息、验证权限等）
      setTimeout(()=>{
        // loadingStore.setLatest(false);
      },1200)
      // loadingStore.startLoading()
      // await simulateAsyncOperation()
      loadingStore.setFirst(false)
    }else{

    }

    // 继续路由跳转
    next()
  } catch (error) {
    // 错误处理
  } finally {
  }
})

router.afterEach(() => {
  const loadingStore = useLoadingStore()


  // loadingStore.finishLoading()
})

function simulateAsyncOperation() {
  return new Promise(resolve => {
    setTimeout(resolve, 2000) // 模拟2秒的加载时间
  })
}

export default router
