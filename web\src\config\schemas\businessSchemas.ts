/**
 * 信息模型组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const monitorDataSchema: ComponentConfigSchema = {
  componentType: 'monitor_data',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '监测数据的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'data',
      label: '请求数据',
      description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'headers',
      label: '请求头',
      description: 'HTTP请求头的配置',
      icon: 'Document',
      order: 3,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'response',
      label: '响应处理',
      description: '响应数据的处理和变量存储',
      icon: 'Download',
      order: 4,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '请求的高级配置',
      icon: 'Setting',
      order: 5,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    url: {
      type: 'string',
      label: 'API地址',
      description: '要请求的API端点URL，支持变量替换 ${variableName}',
      placeholder: 'https://api.example.com/data 或 ${api_base_url}/users',
      required: true,
      group: 'basic',
      order: 1,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'API地址不能为空',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时连接秒数',
      description: '请求的最大等待时间',
      required: true,
      group: 'basic',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },

    data_type: {
      type: 'radio',
      label: '数据类型',
      description: '',
      group: 'data',
      order: 1,
      default: 'json',
      options: [
        { label: 'JSON', value: 'json', description: 'JSON格式数据' },
        { label: '表单数据', value: 'form', description: 'application/x-www-form-urlencoded' },
        { label: '原始数据', value: 'raw', description: '原始文本数据' },
        { label: '文件上传', value: 'multipart', description: 'multipart/form-data' },
      ],
    },

    json_data: {
      type: 'json',
      label: 'JSON数据',
      description: '要发送的JSON数据，支持变量替换 ${variableName}',
      placeholder: `{
  "name": "\${user_name}",
  "age": \${user_age}
}`,
      group: 'data',
      order: 2,
      rows: 6,
      variableSupport: true,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'json',
        },
      ],
    },

    form_data: {
      type: 'json',
      label: '表单数据',
      description: '表单字段数据（JSON格式）',
      placeholder: `{
  "username": "john",
  "password": "secret",
}`,
      group: 'data',
      order: 3,
      rows: 6,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'form',
        },
      ],
    },

    raw_data: {
      type: 'textarea',
      label: '原始数据',
      description: '要发送的原始文本数据',
      placeholder: '输入原始数据内容',
      group: 'data',
      order: 4,
      rows: 6,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'raw',
        },
      ],
    },

    file_path: {
      type: 'file',
      label: '上传文件',
      description: '要上传的文件路径',
      group: 'data',
      order: 5,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'multipart',
        },
      ],
    },

    headers: {
      type: 'json',
      label: '请求头',
      description: 'HTTP请求头（JSON格式），支持变量替换 ${variableName}',
      placeholder: `{
  "Authorization": "Bearer \${api_token}",
  "Content-Type": "application/json"
}`,
      group: 'headers',
      order: 1,
      rows: 6,
      variableSupport: true,
    },

    verify_ssl: {
      type: 'boolean',
      label: '验证SSL证书',
      description: '是否验证HTTPS证书',
      group: 'advanced',
      order: 1,
      default: true,
    },

    retry_count: {
      type: 'number',
      label: '重试次数',
      description: '请求失败时的重试次数',
      group: 'advanced',
      order: 2,
      default: 0,
      min: 0,
      max: 5,
    },

    response_variable: {
      type: 'string',
      label: '响应变量名',
      description: '存储完整响应对象的变量名（可选）',
      placeholder: 'api_response',
      group: 'response',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    response_content_variable: {
      type: 'string',
      label: '响应内容变量名',
      description: '存储响应内容的变量名（可选）',
      placeholder: 'response_content',
      group: 'response',
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    response_status_variable: {
      type: 'string',
      label: '状态码变量名',
      description: '存储HTTP状态码的变量名（可选）',
      placeholder: 'status_code',
      group: 'response',
      order: 3,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    response_headers_variable: {
      type: 'string',
      label: '响应头变量名',
      description: '存储响应头的变量名（可选）',
      placeholder: 'response_headers',
      group: 'response',
      order: 4,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    json_path_extracts: {
      type: 'textarea',
      label: 'JSON字段提取',
      description: 'JSON路径和变量名映射，格式：path1:var1,path2:var2',
      placeholder: 'data.id:user_id,data.name:user_name',
      group: 'response',
      order: 5,
      rows: 3,
    },
  },

  presets: {},


}

export const dataForecastSchema: ComponentConfigSchema = {
  componentType: 'data_forecast',
  version: '1.0.0',
  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '监测数据的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      description: '',
      icon: 'Setting',
      order: 2,
      collapsible: false,
    }
  ],
  fields: {
    forecast_type: {
      type: 'select',
      label: '数据类型',
      description: '',
      group: 'basic',
      order: 1,
      default: '流量',
      required: true,
      options: [
        { label: '流量', value: '流量'},
        { label: '压力', value: '压力'},
        { label: '液位', value: '液位'},
      ],
    },
    forecast_format: {
      type: 'radio',
      label: '数据格式',
      description: '',
      group: 'basic',
      order: 2,
      default: '时间序列',
      options: [
        { label: '时间序列', value: '时间序列', description: '时间格式' },
        { label: '数组', value: '数组', description: '数组格式' },
      ]
    },
    his_win: {
      type: 'json',
      label: '历史数据',
      description: '',
      placeholder: '[120, 122, 124, 123, 121, 120, 119, 118, 117, 116,  115, 114, 113, 112, 111, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100, 99,  98,  97,  96, 95,  94,  93,  92,  91,  90]',
      group: 'basic',
      order: 3,
      rows: 4,
      required: true,
      formatJson: '[]',
      variableSupport: true,
    },
    day_count: {
      type: 'number',
      label: '数据天数',
      description: '',
      group: 'basic',
      order: 4,
      required: true,
      default: 30,
      min: 1,
      conditions: [
        {
          field: 'forecast_format',
          operator: 'not_equals',
          value: '时间序列',
        },
      ],
    },
    set_step: {
      type: 'select',
      label: '输出数据时间步长',
      description: '',
      group: 'basic',
      order: 5,
      required: true,
      default: 1,
      options: [
        { label: '1 分钟', value: 1 },
        { label: '5 分钟', value: 5 },
        { label: '10 分钟', value: 10 },
        { label: '15 分钟', value: 15 },
        { label: '30 分钟', value: 30 },
        { label: '1 小时', value: 60 },
        { label: '2 小时', value: 120 },
        { label: '4 小时', value: 240 },
        { label: '1 天', value: 1440 },
      ]
    },
    forecast_data: {
      type: 'string',
      label: '预测值变量名',
      placeholder: 'forecast_data',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    // forecast_img: {
    //   type:'string',
    //   label: '数据预测曲线截图变量名',
    //   placeholder: 'forecast_img',
    //   group:'response',
    //   required: true,
    //   order: 3,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },
    // forecast_avg: {
    //   type:'string',
    //   label: '预测平均值变量名',
    //   placeholder: 'forecast_avg',
    //   group:'response',
    //   required: true,
    //   order: 4,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },
    // forecast_sum: {
    //   type:'string',
    //   label: '预测累计值变量名',
    //   placeholder: 'forecast_sum',
    //   group:'response',
    //   required: true,
    //   order: 5,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },
    // forecast_max: {
    //   type:'string',
    //   label: '预测最大值变量名',
    //   placeholder: 'forecast_max',
    //   group:'response',
    //   required: true,
    //   order: 6,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },
    // forecast_min: {
    //   type:'string',
    //   label: '预测最小值变量名',
    //   placeholder: 'forecast_min',
    //   group:'response',
    //   required: true,
    //   order: 7,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // }
  },
  presets: {},
}

export const limitIntervalSchema: ComponentConfigSchema = {
  componentType: 'limit_interval',
  version: '1.0.0',
  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '监测数据的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id:'response',
      label: '指令输出',
      description: '',
      icon: 'Setting',
      order: 2,
      collapsible: false,
    }
  ],
  fields: {
    data_type: {
      type:'select',
      label: '数据类型',
      description: '',
      group: 'basic',
      order: 1,
      default: '流量',
      required: true,
      options: [
        { label: '流量', value: '流量'},
        { label: '压力', value: '压力'},
      ],
    },
    data_format: {
      type: 'radio',
      label: '数据格式',
      description: '',
      group: 'basic',
      order: 2,
      default: '时间序列',
      options: [
        { label: '时间序列', value: '时间序列', description: '时间格式' },
        { label: '数组', value: '数组', description: '数组格式' },
      ]
    },
    his_win: {
      type: 'json',
      label: '历史数据',
      description: '',
      placeholder: '[120, 122, 124, 123, 121, 120, 119, 118, 117, 116,  115, 114, 113, 112, 111, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100, 99,  98,  97,  96, 95,  94,  93,  92,  91,  90]',
      group: 'basic',
      order: 3,
      rows: 4,
      required: true,
      formatJson: '[]',
      variableSupport: true,
    },
    day_count: {
      type: 'number',
      label: '数据天数',
      description: '',
      group: 'basic',
      order: 4,
      required: true,
      default: 30,
      min: 1,
      conditions: [
        {
          field: 'data_format',
          operator: 'not_equals',
          value: '时间序列',
        },
      ],
    },
    percent: {
      type: 'number',
      label: '扩展系数',
      description: '',
      group: 'basic',
      order: 4,
      required: true,
      default: 30,
      min: 0.5,
      max: 2,
      step: 0.1,
    },

    warning_time: {
      type: 'string',
      label: '置信区间时间变量名',
      placeholder: 'warning_time',
      group: 'response',
      required: true,
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    warning_up: {
      type: 'string',
      label: '置信区间下限变量名',
      placeholder: 'warning_up',
      group: 'response',
      required: true,
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    warning_down: {
      type: 'string',
      label: '置信区间上限变量名',
      placeholder: 'warning_down',
      group: 'response',
      required: true,
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    outlier_count: {
      type: 'string',
      label: '异常值数量变量名',
      placeholder: 'outlier_count',
      group: 'response',
      required: true,
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

  }
}

export const dataWashSchema: ComponentConfigSchema = {
  componentType: 'data_wash',
  version: '1.0.0',
  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '数据清洗的相关配置',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      description: '',
      icon: 'Setting',
      order: 2,
      collapsible: false,
    }
  ],
  fields: {
    wash_type: {
      type: 'select',
      label: '数据类型',
      description: '',
      group: 'basic',
      order: 1,
      default: '流量',
      required: true,
      options: [
        { label: '流量', value: '流量'},
        { label: '压力', value: '压力'},
      ],
    },
    wash_format: {
      type: 'radio',
      label: '数据格式',
      description: '',
      group: 'basic',
      order: 2,
      default: '时间序列',
      options: [
        { label: '时间序列', value: '时间序列', description: '时间格式' },
        { label: '数组', value: '数组', description: '数组格式' },
      ]
    },
    his_win: {
      type: 'json',
      label: '历史数据',
      description: '',
      placeholder: '[120, 122, 124, 123, 121, 120, 119, 118, 117, 116,  115, 114, 113, 112, 111, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100, 99,  98,  97,  96, 95,  94,  93,  92,  91,  90]',
      group: 'basic',
      order: 3,
      rows: 4,
      required: true,
      formatJson: '[]',
      variableSupport: true,
    },
    day_count: {
      type: 'number',
      label: '数据天数',
      description: '',
      group: 'basic',
      order: 4,
      required: true,
      default: 30,
      min: 1,
      conditions: [
        {
          field: 'data_format',
          operator: 'not_equals',
          value: '时间序列',
        },
      ],
    },
    fill_in_data: {
      type: 'select',
      label: '填充数据',
      description: '',
      group: 'basic',
      order: 2,
      default: '不填充',
      options: [
        { label: '不填充', value: '不填充' },
        { label: '填充短时缺失数据', value: '填充短时缺失数据' },
        { label: '根据历史同周填充长时间缺失数据', value: '根据历史同周填充长时间缺失数据' },
        { label: '根据日周期填充长时间缺失数据', value: '根据日周期填充长时间缺失数据' },
      ]
    },
    set_step: {
      type: 'select',
      label: '输出数据时间步长',
      description: '',
      group: 'basic',
      order: 5,
      required: true,
      default: 1,
      options: [
        { label: '1 分钟', value: 1 },
        { label: '5 分钟', value: 5 },
        { label: '10 分钟', value: 10 },
        { label: '15 分钟', value: 15 },
        { label: '30 分钟', value: 30 },
        { label: '1 小时', value: 60 },
        { label: '2 小时', value: 120 },
        { label: '4 小时', value: 240 },
        { label: '1 天', value: 1440 },
      ]
    },
    allow_max_data: {
      type: 'string',
      label: '允许出现的最大值',
      description: '',
      placeholder: '可选',
      group: 'basic',
      order: 7,
      variableSupport: true,
    },
    allow_min_data: {
      type:'string',
      label: '允许出现的最小值',
      description: '',
      placeholder: '可选',
      group: 'basic',
      order: 8,
      variableSupport: true,
    },

    wash_win: {
      type: 'string',
      label: '清洗后数据',
      placeholder: 'wash_win',
      description: '数组类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    wash_time: {
      type: 'string',
      label: '清洗后数据时间',
      placeholder: 'wash_time',
      description: '数组类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    miss_count: {
      type: 'string',
      label: '缺失数据数量',
      placeholder: 'miss_count',
      description: '数值类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    invalid_count: {
      type: 'string',
      label: '无效数据数量',
      placeholder: 'invalid_count',
      description: '数值类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    valid_count: {
      type: 'string',
      label: '有效数据数量',
      placeholder: 'valid_count',
      description: '数值类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    grade: {
      type: 'string',
      label: '历史数据评分',
      placeholder: 'grade',
      description: '数值类型',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
  },
  presets: {},
}

export const waterShutoffValveSchema: ComponentConfigSchema = {
  componentType: 'water_shutoff_valve',
  version: '1.0.0',
  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '停水关阀的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      description: '',
      icon: 'Setting',
      order: 2,
      collapsible: false,
    }
  ],
  fields: {
    input_type: {
      type: 'radio',
      label: '数据格式',
      description: '',
      group: 'basic',
      order: 2,
      options: [
        { label: '管道中心坐标', value: '管道中心坐标'},
        { label: '管道模型ID', value: '管道模型ID'},
        { label: '管道GIS-编号', value: '管道GIS-编号'},
      ]
    },
    input_data: {
      type: 'string',
      label: '输入数据',
      description: '坐标格式：[xxx,xxx]',
      placeholder: '',
      group: 'basic',
      order: 3,
      rows: 4,
      required: true,
      variableSupport: true,
    },

    response_content_variable: {
      type: 'string',
      label: '返回结果变量名',
      placeholder: 'response_content_variable',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
  },
  presets: {},
}
