import { request } from '@/utils/axios';
import utils from '@/utils/utils';

const saasApi = {
    //客户申请查询
    AIAgentWimtaskCustomerApplicationQuery(params) {
        return request.post('/wimai/api/task/customerApplication/query', params);
    },
    //客户申请更新
    AIAgentWimtaskCustomerApplicationUpdate(params) {
        return request.post('/wimai/api/task/customerApplication/update', params);
    },
    // 测试运行
    AITestRun(params) {
        const data = params
        return request.post('http://localhost:39876/execute_node', data);
    },
    DmpTenantDetailByName(){
        return request.get('/uniwim/dmp/tenant/detailByName?name=', {})
    },
    //获取用户
    DmpUserByMobile(params){
        return request.get('/uniwim/dmp/user/getByMobile', {
            mobile:params.mobile
        })
    },
    // 智能体后台内容管理查询接口
    AIAgentLearningContentQuery(params) {
        return request.post('/wimai/api/task/learningContent/query', params);
    },
    // 智能体后台内容管理新增接口
    AIAgentLearningContentAdd(params) {
        return request.post('/wimai/api/task/learningContent/insert', params);
    },
    // 智能体后台内容管理修改接口
    AIAgentLearningContentUpdate(params) {
        return request.post('/wimai/api/task/learningContent/update', params);
    },
    // 智能体后台内容管理删除接口
    AIAgentLearningContentDelete(params) {
        return request.post('/wimai/api/task/learningContent/delete', params);
    },

    // 智能体后台栏目管理查询接口
    AIAgentLearningCategoryQuery(params) {
        return request.post('/wimai/api/task/learningCategory/query', params);
    },
    // 智能体后台栏目管理新增接口
    AIAgentLearningCategoryAdd(params) {
        return request.post('/wimai/api/task/learningCategory/insert', params);
    },
    // 智能体后台栏目管理修改接口
    AIAgentLearningCategoryUpdate(params) {
        return request.post('/wimai/api/task/learningCategory/update', params);
    },
    // 智能体后台栏目管理删除接口
    AIAgentLearningCategoryDelete(params) {
        return request.post('/wimai/api/task/learningCategory/delete', params);
    },

    // 智能体后台分类管理查询接口
    AIAgentResourceCategoryQuery(params) {
        return request.post('/wimai/api/task/resource/category/query', params);
    },
    // 智能体后台分类管理新增接口
    AIAgentResourceCategoryAdd(params) {
        return request.post('/wimai/api/task/resource/category/add', params);
    },
    // 智能体后台分类管理修改接口
    AIAgentResourceCategoryUpdate(params) {
        return request.post('/wimai/api/task/resource/category/update', params);
    },
    // 智能体后台分类管理删除接口
    AIAgentResourceCategoryDelete(params) {
        return request.post('/wimai/api/task/resource/category/delete', params);
    },

    // 智能体后台模板管理及服务包管理查询接口
    AIAgentTaskTemplateQuery(params) {
        return request.post('/wimai/api/task/template/query', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理新增
    AIAgentTaskTemplateInsert(params) {
        return request.post('/wimai/api/task/template/insert', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理修改
    AIAgentTaskTemplateUpdate(params) {
        return request.post('/wimai/api/task/template/update', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理删除
    AIAgentTaskTemplateDelete(params) {
        return request.post('/wimai/api/task/template/delete', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理详情
    AIAgentTaskTemplateDetail({id}) {
        return request.get(`/wimai/api/task/template/detail?id=${id}`);
    },

    // 环境配置
    AIAgentConfigQuery(params) {
        return request.post('/wimai/api/task/config/query', params);
    },
    AIAgentConfigAdd(params) {
        return request.post('/wimai/api/task/config/add', params);
    },
    AIAgentConfigUpdate(params) {
        return request.post('/wimai/api/task/config/update', params);
    },
    AIAgentConfigDelete(params) {
        return request.post(`/wimai/api/task/config/delete`, params);
    },
    AIAgentConfigInitInner(params) {
        return request.post(`/wimai/api/task/config/initInner`, params);
    },
    AIAgentConfigGetAllCode(params) {
        return request.get(`/wimai/api/task/config/getAllCode`, params);
    },
    // 智能体后台指令查询接口
    AIAgentWimtaskCommandQuery(params) {
        return request.post('/wimai/api/task/command/query', params);
    },
    // 智能体后台指令更新接口
    AIAgentWimtaskCommandUpdate(params) {
        return request.post('/wimai/api/task/command/update', params);
    },
    // 智能体后台指令更新接口
    AIAgentWimtaskCommandInsert(params) {
        return request.post('/wimai/api/task/command/insert', params);
    },
    // 智能体后台指令删除接口
    AIAgentWimtaskCommandDelete(params) {
        return request.post('/wimai/api/task/command/delete', params);
    },
    // 智能体后台授权接口
    AIAgentAddAuthResource(params) {
        return request.post('/wimai/api/task/authTarget/addAuthResource', params);
    },
    // 智能体后台取消授权接口
    AIAgentRemoveAuthResource(params) {
        return request.post('/wimai/api/task/authTarget/removeAuthResource', params);
    },
    // 智能体后台授权管理查询接口
    AIAgentWimtaskAuthTargetQuery(params) {
        return request.post('/wimai/api/task/authTarget/query', params);
    },
    // 智能体后台授权管理新增接口
    AIAgentWimtaskAuthTargetInsert(params) {
        return request.post('/wimai/api/task/authTarget/insert', params);
    },
    // 智能体后台授权管理更新接口
    AIAgentWimtaskAuthTargetUpdate(params) {
        return request.post('/wimai/api/task/authTarget/update', params);
    },
    // 智能体后台授权管理删除接口
    AIAgentWimtaskAuthTargetDelete(params) {
        return request.post('/wimai/api/task/authTarget/delete', params);
    },
    // 智能体后台服务包管理查询接口
    AIAgentServicePackageQuery(params) {
        return request.post('/wimai/api/task/servicePackage/query', params);
    },
    // 智能体后台服务包管理新增
    AIAgentServicePackageInsert(params) {
        return request.post('/wimai/api/task/servicePackage/insert', params);
    },
    // 智能体后台服务包管理修改
    AIAgentServicePackageUpdate(params) {
        return request.post('/wimai/api/task/servicePackage/update', params);
    },
    // 智能体后台服务包管理删除
    AIAgentServicePackageDelete(params) {
        return request.post('/wimai/api/task/servicePackage/delete', params);
    },
    // 智能体后台服务包管理详情
    AIAgentServicePackageDetail({id}) {
        return request.get(`/wimai/api/task/servicePackage/detail?id=${id}`);
    },

    // 智能体后台模板版本管理查询接口
    AIAgentTaskTemplateVersionQuery(params) {
        return request.post('/wimai/api/task/templateVersion/query', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理新增
    AIAgentTaskTemplateVersionInsert(params) {
        return request.post('/wimai/api/task/templateVersion/insert', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理修改
    AIAgentTaskTemplateVersionUpdate(params) {
        return request.post('/wimai/api/task/templateVersion/update', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理删除
    AIAgentTaskTemplateVersionDelete(params) {
        return request.post('/wimai/api/task/templateVersion/delete', params);
    },
    // 智能体后台智能体后台模板管理及服务包管理详情
    AIAgentTaskTemplateVersionDetail({id}) {
        return request.get(`/wimai/api/task/templateVersion/detail?id=${id}`);
    },

    // 智能体后台执行安装包版本查询
    AIAgentPackagePublishingQuery(params) {
        return request.post('/wimai/api/task/packagePublishing/query', params);
    },
    // 智能体后台执行安装包版本新增
    AIAgentPackagePublishingInsert(params) {
        return request.post('/wimai/api/task/packagePublishing/insert', params);
    },
    // 智能体后台执行安装包版本修改
    AIAgentPackagePublishingUpdate(params) {
        return request.post('/wimai/api/task/packagePublishing/update', params);
    },
    // 智能体后台执行安装包版本修改
    AIAgentPackagePublishingDelete(params) {
        return request.post('/wimai/api/task/packagePublishing/delete', params);
    },
    // 智能体后台执行安装包版本详情
    AIAgentPackagePublishingDetail({id}) {
        return request.get(`/wimai/api/task/packagePublishing/detail?id=${id}`);
    },
    
    // 埋点相关
    accessLogSaveLog({...params}, channel = 'web') {
        const data = {...params}
        delete data.uuid;
        return request.post('/uniwim/ump/accessLog/saveLog', data);
    },
    // ai大模型 相关
    // 获取配置列表
    AIConfigList() {
        return request.get('/wimai/api/sysConfig/list', {});
    },
    // 更新配置
    AIUpdateConfig(params) {
        return request.post('/wimai/api/sysConfig/batchSaveOrUpdate', params);
    },
    // Wim菜单配置查询
    WimAIMenuQuery(params) {
        return request.post('/wimai/api/menuConfig/query', params);
    },
    // Wim菜单配置更新
    WimAIMenuUpdate(params) {
        return request.post('/wimai/api/menuConfig/saveBatch', params);
    },
    // 分享到wimpic
    AIShareToWimPic({ name, content, images = [] }, apiHost = '') {
        return request
            .post(`${apiHost}/uniwim/wimpic/document/insert/markdown`, {
                type: '1',
                name: name ? name.replace(/\//g, '|') : '',
                content,
                images,
            })
            .then((res) => {
                let framesLength = window.frames ? window.frames.length : 0;
                let params = {};
                let message = '*#hd#*' + JSON.stringify({ action: 'REFRESH_DOC_PAGE', params }); //固定写法
                for (let i = 0; i < framesLength; i++) {
                    window.frames[i].postMessage(message, '*');
                    console.warn({ message });
                }
                return res;
            });
    },
    // 发送对话
    AISend({ ...args }, controller = null) {
        // debug 调试模式
        if(args['content'] === 'debug'){
            if(localStorage.getItem('debug123456') === '1'){
                localStorage.removeItem('debug123456')
            }else{
                localStorage.setItem('debug123456', '1')
            }
        }
        let formData = new FormData();
        for (let key in args) {
            args[key] && formData.append(key, key === 'file' ? args[key] : args[key].trim());
        }
        let isTimeout = false;
        const id = setTimeout(() => {
            isTimeout = true;
            controller?.abort()
        }, 60000);
        // 流式
        return fetch(utils.getHost() + '/wimai/api/sendStream', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            body: formData,
            signal: controller?.signal,
        }).catch(error => {
            if (isTimeout && error.name === 'AbortError') {
                throw new Error('当前访问人数过多，请稍候再试!');
            }
            throw error;
        }).finally(() => {
            clearTimeout(id);
        });
    },
    // 抽奖
    AISendLottery({ content, sessionId, file, meetingId }) {
        let formData = new FormData();
        formData.append('content', content || '');
        formData.append('meetingId', meetingId || '');
        sessionId && formData.append('sessionId', sessionId);
        file && formData.append('file', file);
        //直接
        return request.post('/wimai/api/send', formData, {
            headers: { 'content-type': 'application/x-www-form-urlencoded' }
        });
    },
    // 重新生成对话内容
    AIRegeneratorMessage({ ...args }, controller) {
        let isTimeout = false;
        const t = setTimeout(() => {
            isTimeout = true;
            controller?.abort()
        }, 60000);
        // 流式
        let url = utils.getHost() + `/wimai/api/regeneratorMessageStream`;
        Object.keys(args).forEach((key, index) => {
            url += `${index === 0 ? '?' : '&'}${key}=${args[key].trim()}`
        })

        // 记录日志-重新生成
        this.AISessionRecordUpdate({
            id: args.recordId,
            regenerated: 1,
        })

        return fetch(url, {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            signal: controller?.signal
        }).catch(error => {
            if (isTimeout && error.name === 'AbortError') {
                throw new Error('当前访问人数过多，请稍候再试!');
            }
            throw error;
        }).finally(() => {
            clearTimeout(t);
        });
    },
    // 推荐对话
    AIRecommend(id) {
        return request.post(`/wimai/api/recommend`, {});
    },
    // 推荐简报模板
    AIReportTemplate(id) {
        return request.get(`/wimai/api/config/template`, {});
    },
    // 历史对话列表
    AIListSession(params) {
        return request.post('/wimai/api/listSession', params);
    },
    // 历史对话详情
    AIPageSessionRecord(params) {
        return request.post('/wimai/api/pageSessionRecord', params);
    },
    // 更新历史对话
    AIUpdateSession(params) {
        return request.post('/wimai/api/updateSession', params);
    },
    // 删除历史对话
    AIDeleteSession(id) {
        return request.post('/wimai/api/deleteSession?id=' + id, {});
    },
    // 点赞，点踩
    AIIsLikeRecord(id, isLike = -1) {
        return request.post(`/wimai/api/isLikeRecord?id=${id}&isLike=${isLike}`, {});
    },
    // 出题
    AIExamination({ questionType, num, prompt, modelType = '', file}, signal) {
        let formData = new FormData();
        formData.append('questionType', questionType);
        // formData.append('num', num || 1);
        formData.append('prompt', prompt);
        formData.append('modelType', modelType);
        file && formData.append('file', file);
        return fetch(utils.getHost() + '/wimai/api/examination', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            body: formData,
            signal
        });
    },
    // 根据主题获取推荐知识列表
    AIRagSearchTitle(topic) {
        let formData = new FormData();
        formData.append('topic', topic);
        return request.post('/wimai/api/ragSearchTitle', formData, {
            headers: { 'content-type': 'application/x-www-form-urlencoded' }
        });
    },
    // 根据知识列表出题
    AIRagExamination({ questionType, num = 1, topic = '根据文档出题', titleJson = '{}', modelType = '' }, signal) {
        return fetch(utils.getHost() + '/wimai/api/ragExamination', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                'content-type': 'application/json; charset=UTF-8',
                FROM_CHANNEL: 'web'
            },
            body: JSON.stringify({
                questionType,
                // num,
                topic,
                titleJson,
                modelType
            }),
            signal
        });
    },
    // 获取所有题库名称列表
    AIKcExamTopicLibrary(tenantId = '') {
        return request.get(`/uniwim/knowledge/api/kcExamTopicLibrary/selectAll?tenantId=${tenantId}`, {});
    },
    // 导出到题库
    AIKcExamTopicGenerateByAi(params) {
        return request.post(`/uniwim/knowledge/api/kcExamTopic/generateByAi`, params);
    },
    // ai抽奖手机签到
    AILotterySign(mobile) {
        return request.post('/wimai/api/sign?phone=' + mobile, {});
    },
    // ai抽奖签到人数
    AILotterySignNum(id) {
        if (id) {
            return request.get('/uniwim/ump/meeting/getTotalAndMobile?id=' + id);
        }
        return request.post('/wimai/api/signNum', {});
    },
    // 知识库推荐
    AIRagRecommend(id) {
        return request.post(`/wimai/api/ragRecommend`, {});
    },
    // 知识库推荐
    AIKnowledgeSuggest({datasetId, index, size = 6}){
        return request.post(`/wimai/api/KnowledgeSuggest?datasetId=${datasetId}`, {
            index,
            size
        });
    },
    // 知识类别获取
    AIKnowledgeType() {
        return request.get(`/wimai/api/knowledgeType`, {});
    },
    // 发送知识库对话
    AISendRag({ content, sessionId, modelType, knowledgeType }, signal = null) {
        let formData = new FormData();
        formData.append('content', content || '');
        formData.append('modelType', modelType || '');
        knowledgeType && formData.append('knowledgeType', knowledgeType || '');
        sessionId && formData.append('sessionId', sessionId);
        // 流式
        return fetch(utils.getHost() + '/wimai/api/sendRagStream', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            body: formData,
            signal
        });
    },
    // 内部模型发送知识库对话
    AISendInfoModel({ prompt }) {
        return request.post('/wimai/api/infoModel', { prompt }, { meta: { isMessage: false } });
    },
    // 重新生成知识库对话内容
    AIRegeneratorRagMessage(id, modelType = null, signal = null) {
        // 流式
        let url = utils.getHost() + `/wimai/api/regeneratorRagMessageStream?recordId=${id}`;
        if (modelType) {
            url += `&modelType=${modelType}`;
        }
        return fetch(url, {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            signal
        });
    },
    // 简报
    AIBriefing({ metric, type, prompt, excel = null, modelType = '', agentParams = null, sessionId }, controller = null) {
        let formData = new FormData();
        formData.append('prompt', prompt.trim());
        formData.append('modelType', modelType);
        agentParams && formData.append('agentParams', agentParams);
        sessionId && formData.append('sessionId', sessionId);
        if (excel) {
            formData.append('excel', excel);
        } else {
            metric && formData.append('metric', metric);
            formData.append('type', type);
        }
        let isTimeout = false;
        const id = setTimeout(() => {
            isTimeout = true;
            controller?.abort()
        }, 60000);
        // 流式
        return fetch(utils.getHost() + '/wimai/api/briefingStream', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web'
            },
            body: formData,
            signal: controller?.signal
        }).catch(error => {
            if (isTimeout && error.name === 'AbortError') {
                throw new Error('当前访问人数过多，请稍候再试!');
            }
            throw error;
        }).finally(() => {
            clearTimeout(id);
        });
    },
    // 获取全部指标
    AIAllMetric(params = {}) {
        let formData = new FormData();
        Object.keys(params).forEach((key) => {
            formData.append(key, params[key]);
        })
        return request.post(
            '/wimai/api/allMetric',
            formData,
            {
                headers: { 'content-type': 'application/x-www-form-urlencoded' }
            }
        );
    },
    // 根据描述获取指标
    AIClassification(prompt = '', modelType = '', signal) {
        let formData = new FormData();
        formData.append('prompt', prompt);
        modelType && formData.append('modelType', modelType);
        return request.post('/wimai/api/classification', formData, {
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
            signal
        });
    },
    // 保存模板
    AIBriefingTemplateSave(params) {
        return request.post('/wimai/api/messageBriefingTemplate/save', params);
    },
    // 删除模板
    AIBriefingTemplateDelete(params) {
        return request.post('/wimai/api/messageBriefingTemplate/delete', params);
    },
    // 查询模板
    AIBriefingTemplateQuery() {
        return request.get('/wimai/api/messageBriefingTemplate/query', {});
    },
    // 查询设备比选
    AIDevice(params, signal = null) {
        // 流式
        return fetch(utils.getHost() + '/wimai/api/deviceStream', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                FROM_CHANNEL: 'web',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params),
            signal
        });
    },
    // 获取菜单对应模版数据配置
    AIMenuTemplate(params) {
        return request.post('/wimai/api/template/query', params);
    },
    AIMenuTemplateSaveBatch(params) {
        return request.post('/wimai/api/template/saveBatch', params);
    },
    AIMenuTemplateDelete(params) {
        return request.post('/wimai/api/template/delete', params);
    },
    // 获取菜单对应模版数据配置
    AIAgentParam(params) {
        return request.post('/wimai/api/agentParam/query', params);
    },
    AIAgentParamSaveBatch(params) {
        return request.post('/wimai/api/agentParam/saveBatch', params);
    },
    AIAgentParamDelete(params) {
        return request.post('/wimai/api/agentParam/delete', params);
    },
    // 转人工客服
    AIMessageFriend(){
        // /uniwim/message/messageFriend/query
        return request.get('/uniwim/message/messageFriend/query', {
            keyword: '19820001157'
        })
    },
    // ai语音转文字
    AIVoiceToText(file){
        let formData = new FormData();
        formData.append('file', file);
        return request.post('/wimai/api/tool/asr', formData, {
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
        });
    },
    AITextToSoundStream(content, signal = null) {
        // 替换字符中的特殊内容
        function replaceSymbols(str) {
            // 正则表达式中需要转义的符号
            const escapeChars = [
                '.', '*', '+', '?', '|', '(', ')', '[', ']', '{', '}', '^', '$', '-', '#'
            ];
            const replacements = {
                '°C': '摄氏度',
                'm³': '立方米',
                'm²': '平方米',
                'km²': '平方千米',
                'kg': '千克',
                'mm': '毫米',
                '+': '加',
                '全屏预览': '',
            };
            // 自定义替换词，通过main.js入口请求接口获取并设置到window对象上
            if(window.uniwimAICustomReplaceWords?.length){
                window.uniwimAICustomReplaceWords.forEach(f => {
                    if(!f.source || !f.value) return
                    replacements[f.source] = f.value
                })
            }
            if(!str) return str
            // 对需要转义的符号进行转义
            const escapedKeys = Object.keys(replacements).map(key => {
                if (escapeChars.includes(key)) {
                    return '\\' + key;
                }
                return key;
            });
            const regex = new RegExp(escapedKeys.join('|'), 'g');
            return str.replace(regex, (match) => {
                // 去掉转义字符以获取正确的替换值
                const originalMatch = match.replace('\\', '');
                return replacements[originalMatch];
            });
        }
        return fetch(utils.getHost() + '/wimai/api/tool/ttsStream', {
            method: 'POST',
            headers: {
                Authorization: utils.GetAuthorization(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({text: replaceSymbols(content)}),
            signal
        });
    },
    AITextToSound(content, signal){
                // 替换字符中的特殊内容
        function replaceSymbols(str) {
            // 正则表达式中需要转义的符号
            const escapeChars = [
                '.', '*', '+', '?', '|', '(', ')', '[', ']', '{', '}', '^', '$', '-', '#'
            ];
            const replacements = {
                '°C': '摄氏度',
                'm³': '立方米',
                'm²': '平方米',
                'km²': '平方千米',
                'kg': '千克',
                'mm': '毫米',
                '+': '加',
                '全屏预览': '',
            };
            // 自定义替换词，通过main.js入口请求接口获取并设置到window对象上
            if(window.uniwimAICustomReplaceWords?.length){
                window.uniwimAICustomReplaceWords.forEach(f => {
                    if(!f.source || !f.value) return
                    replacements[f.source] = f.value
                })
            }
            if(!str) return str
            // 对需要转义的符号进行转义
            const escapedKeys = Object.keys(replacements).map(key => {
                if (escapeChars.includes(key)) {
                    return '\\' + key;
                }
                return key;
            });
            const regex = new RegExp(escapedKeys.join('|'), 'g');
            return str.replace(regex, (match) => {
                // 去掉转义字符以获取正确的替换值
                const originalMatch = match.replace('\\', '');
                return replacements[originalMatch];
            });
        }
        return request.post('/wimai/api/tool/xfTtsUrl', {
            text: replaceSymbols(content),
        }, {
            signal
        }).catch((e)=>{
            if(e?.message === "canceled") return 'canceled'
            return e
        });
        // let formData = new FormData();
        // formData.append('content', content);
        // return request.post('/wimai/api/tool/ttsUrl', formData, {
        //     headers: {
        //         'content-type': 'application/x-www-form-urlencoded',
        //     },
        //     // responseType: 'arraybuffer',
        //     signal
        // }).catch((e)=>{
        //     if(e?.message === "canceled") return 'canceled'
        //     return null
        // });

        // return fetch(utils.getHost() + '/wimai/api/tool/tts', {
        //     method: 'POST',
        //     headers: {
        //         Authorization: utils.GetAuthorization(),
        //         // FROM_CHANNEL: 'web',
        //     },
        //     body: formData,
        //     signal
        // });
    },
    // 孪生图相关接口
    // 获取孪生图地图数据
    TwinsMapData(params) {
        return request.post('/uniwim/imb/toolbar/label/list', params, {
            headers: {
                'Authorization': utils.GetAuthorization(),
                'tenant-id': utils.GetQueryString("uniwim_tenant_id", "hash") || utils.GetQueryString("uniwim_tenant_id"),
            },
        });
    },
    // 孪生图模型对话
    AIMapReport(params, signal = null) {
        // 流式 /uniwim/message/llm/twinGraph/MapReport
        return fetch(utils.getHost() + '/wimai/api/twinGraph/MapReport', {
            method: 'POST',
            headers: {
                'Authorization': utils.GetAuthorization(),
                // 'tenant-id': utils.getUniWimTenantId(),
                'FROM_CHANNEL': 'web',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params),
            signal,
        })
    },
    // AI 生成在线文档
    AIInsertMarkdown(params) {
        // return request.post('/wimpic/document/insert/markdown', params, {
        //     headers: {
        //         'Authorization': utils.GetAuthorization(),
        //         'tenant-id': window.localStorage.getItem('UniWimTenantId'),
        //         'Content-Type': 'application/json'
        //     },
        // })
        return fetch(utils.getHost() + '/uniwim/wimpic/document/insert/markdown', {
            method: 'POST',
            headers: {
                'Authorization': utils.GetAuthorization(),
                'tenant-id': localStorage.getItem('UniWimTenantId'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params),
        })
    },
    AISendUser(params) {
        // return request.post('/wimpic/document/share/sendUser', params)
        return fetch(utils.getHost() + '/uniwim/wimpic/document/share/sendUser', {
            method: 'POST',
            headers: {
                'Authorization': utils.GetAuthorization(),
                'tenant-id': localStorage.getItem('UniWimTenantId'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params),
        })
    },
    AISuggestedMessage(params, signal = null) {
        return request.get('/wimai/api/suggestedMessage', params, { signal });
    },

    // AI智能体接口
    // 文件上传接口
    fileUpload(params) {
        return request.post('/uniwim/upload', params);
    },
    // 创建AI智能体
    AICreateAgent(params) {
        return request.post('/wimai/api/agentConfig/create', params);
    },
    // 更新AI智能体
    AIUpdateAgent(params) {
        return request.post('/wimai/api/agentConfig/update', params);
    },
    // 查询用户可用的智能体
    AIGetAllUsableAgent(params, configId = ''){
        return configId
            ? request.post('/wimai/api/agentConfigShare/acceptShare?configId=' + configId, params)
            : request.post('/wimai/api/agentConfig/getAllUsable', params)
    },
    // 删除自定义智能体
    // 删除历史对话
    AIDeleteAgent(configId) {
        return request.post('/wimai/api/agentConfig/delete?configId=' + configId, {});
    },
    // 获取可见知识库列表
    AIKnowledgeList(params) {
        return request.get('/wimai/api/dataset/getDatasetsVisible', params);
    },
    // 润色文案
    AIAgentPolish(params, text) {
        return request.post('/wimai/api/agentConfig/polish?text='+text, params);
    },
    // 纠错
    AICorrect(params) {
        return request.post('/wimai/api/dataset/correction', params);
    },
    // 日志记录
    AISessionRecordUpdate(params) {
        request.post('/wimai/api/sessionRecord/update', params).finally(r => {});
    },
    // 智能体执行任务查询
    AIAgentMissionQuery(params) {
        return request.post('/wimai/api/task/query', params);
    },
    AIAgentMissionAdd(params) {
        return request.post('/wimai/api/task/add', params);
    },
    AIAgentMissionUpdate(params) {
        return request.post('/wimai/api/task/updateBaseInfo', params);
    },
    AIAgentMissionUpdateRobot(params) {
        return request.post('/wimai/api/task/updateRobot', params);
    },
    AIAgentMissionUpdateActive(params) {
        return request.post('/wimai/api/task/updateActive', params);
    },
    AIAgentMissionDelete(params) {
        const ids = params?.id ? [params?.id] : [];
        return request.post('/wimai/api/task/delete', ids);
    },
    AIAgentMissionDetail({ id }) {
        return request.get(`/wimai/api/task/detail?id=${id}`);
    },
    AIAgentMissionExecute({ id }) {
        return request.post(`/wimai/api/task/execute?id=${id}`);
    },
    // 智能体执行任务记录查询
    AIAgentMissionHistoryQuery(params) {
        return request.post('/wimai/api/taskHistory/query', params);
    },
    // 智能体执行过程日志查询
    AIAgentMissionLogQuery(params){
        // return request.get(`/wimai/api/taskHistory/logs?id=${id}`)
        return request.post(`/wimai/api/task/excLogs/query`, params)
    },
     // 智能体日志跟踪台账查询接口
    AIAgentSessionRecordPageQuery(params){
        return request.post(`/wimai/api/sessionRecord/page`, params)
    },
    // 智能体执行过程日志对应文件查询
    AIAgentMissionLogFile(id) {
        return request.get(`/wimai/api/task/excLogs/file?id=${id}`, {}, {
          responseType: 'arraybuffer'
        })
    },
    // 智能体执行过程日志对应文件查询
    AIAgentMissionExcFileFile(id) {
        return request.get(`/wimai/api/task/excFile/download?executionId=${id}`, {}, {
          responseType: 'arraybuffer'
        })
    },

    // 任务监控页面查询任务执行所有文件目录
    AIAgentMissionExcAllFile(params) {
        return request.post(`/exe_files`, params)
    },



    // 智能体纠错权限查询
    AIAgentCorrentAuth(){
        return request.get('/uniwim/ump/menu/userButtonPermissionList?applicationType=web').then(r => r).catch(r => null);
    },

    // 任务执行器发送信息给一诺
    chatSendToMine(type){
        return request.get(`/uniwim/message/chat/sendToMine?type=${type}`)
    },



    // 全局配置
    // 获取配置列表
    AITaskConfigList() {
        return request.get('/wimai/api/task/config/list', {});
    },
    // 更新配置列表
    AITaskUpdateConfig(params){
        return request.post('/wimai/api/task/config/batchSaveOrUpdate', params)
    },


    // 模板管理器
    // 统一转发crud接口
    AIDtemplateCrud(params){
        return request.post('/wimai/api/template/crud', params)
    },


    // 文件上传
    AITaskUpload(params) {
        return request.post('/wimai/api/task/upload', params, {
            headers: {
                'Authorization': utils.GetAuthorization(),
                "FROM_CHANNEL": "web"
            },
        })
    }
};
export default saasApi;
