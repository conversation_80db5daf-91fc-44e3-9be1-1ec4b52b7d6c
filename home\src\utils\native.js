// 原生调用方法
import utils from "./utils";

const _printEnable = false;
const _cache = {};
const _cache2 = {};

const fns = [
    "save", "delete", "query", "rotate", "startNavi", "getLocation", "isCollected", "sysInfo",
    "hideHeader", "scanCode", "copy", "share", "userInfo", "userBehaviorRecord", "search", "openWebview", "closeWebview",
    "collect", "nfcData", "goback", "refreshBadge", "signature", "saveLocalData", "queryLocalData", "deleteLocalData",
    "bluetooth", "btDisConnected", "bluetoothState", "manualPost", "btGetParams", "manualPostRes", "btSound", "soundPlay", "vibratorSound", "dictList",
    "btScan", "shareFile", "btUpdate", "spectrum", "trace", "delFile", "interceptBack", "bgNavi", "keepScreenOn",
    "video", "showVideo", "sound", "screenshot", "bgNaviClose", "regeocode", "watermarkCamera",
    "download", "sqlite_execsql", "sqlite_query", "multi_file", "v88s_zdsjcx", "v88s_params", "v88s_tc", "logger", "singleDownload", "clearCache",
    "resSave", "sqlite_close", "unzip", "queryCustomer", "get_blan", "rpc_blan", "rfm_getDevices", "rfm_openDoor","fmkz_params","fmkz_sscx","fmkz_tc","analRelated",
    "ocr_watermeter", "eranntex_params", "connectUHFTag", "disconnectUHFTag", "readUHFTag", "openBluetooth",
    "tlv_params", "tlv_dataQuery", "tlv_tc", "tlv_noiseAudio", "r800c_printer", "qx_rtk", "ycbDatasUpload","ycbDatasDebug", "spon_audio", "get_local_file",
    "sound_record_start", "sound_record_stop", "sound_record_cancel", "getGpsState", "openScheme", "wenzhen_get_data", "intent_open_url", "nfcWriteData",
    "b32_printer_connect", "b32_printer_disconnect", "b32_printer", "bt_scan_classic",
    "change_sn", "deviceList", "appConfig", "wechat_login", "appleSignIn", "im_init", "im_login", "im_openPage", "im_getInfo", "screenoff", "screenon", "shortVideo",
    "open_miniprogram", "im_unreadnum", "speech_recognizer", "get_imei", "waterMarkBg", "onLinkRestore", "appBadges", "shortVideoRecord", "faceVerify", "header",
    "loginStatusChange", "local_res_zip_version", "sangfor_login", "sangfor_status", "resetApp", "finish_app", "reloadWebView", "changeClient",
    "chat_room_join", "check_permission", "openFileBySystem", "niimbot_m2_printer_connect", "niimbot_m2_printer", "niimbot_m2_printer_disconnect", "request_permission",
    "kill_app",
];

// 原生主动调用js的方法
const fns2 = [
    "search", "collect", "btDisConnected", "btReceiver", "btGetParams", "manualPostRes", "handPostResp", "gpsnPostResp",
    "btSound", "btScan", "handCollectResp", "bluetooth", "onBtState", "interceptBack", "bgNavi", "getLocation", "btUpdate",
    "screenoff", "screenon", "bgNaviClose", "btRawData", "onSingleDownload", "sqlite_close", "unzip", "btNoiseLog",
    "get_blan", "rpc_blan", "onfmkz_sscx", "connectUHFTag", "disconnectUHFTag", "readUHFTag", "nfcData",
    "tlv_onDataReport", "qx_rtk", "sound_record_stop", "onAppEnterBackground", "onAppEnterForeground", "tlv_dataQuery", "onNfcWriteData",
    "onBtScanClassic",
    "onShortVideoEvents", "onShortVideoRecord", "analRelated", "onReceiveMsg", "wechat_login", "multi_file", "im_onEventListener", "im_unreadnum", "speech_recognizer",
    "onLinkRestore", "onClickMessage", "onReceiveMessage", "onRMQUserState", "onRMQUserAllState", "onRMQMessage", "onReconnect", "onShutdown", "sangfor_change", "onUnzip",
    "request_permission"
];

const postMessage = ({cb, method, params}) => {
    let msgid = parseInt(Math.random() * Math.pow(10, 17)) + new Date().getTime();
    if(method === 'getLocation'){
        msgid = 'mue_getLocation'
        if(params && params.msgid) {
            msgid = params.msgid
        }
    }
    _cache[msgid] = cb;
    try {
        let p = JSON.stringify({msgid, method, params});
        utils.isIosAndMac() ? window.webkit.messageHandlers.postMessage.postMessage(p)
            : window.native.postMessage(p);
    } catch(e){
        console.log(e);
    }
};
window.response = ({msgid, method, params}) => {
    // 原生回调传入一个json对象（id, 方法名，返回数据）
    fns2.some(v => {
        if(v === method){
            _cache2[method] = params;
            return true;
        }
    });
    const cb = _cache[msgid];
    if(cb){
        let gon = cb(params);
        // 如果多次回调，返回true 不删除回调入口
        gon !== true && delete _cache[msgid];
    }
    else{
        // 分发至子系统的
        iframeSendMessage({ msgid, method, params });
        delete _cache[msgid];
    }
};

//执行具体监听方法
window.response2 = ({method, cb}) => {
    if(!method || !cb){
        return;
    }

    Object.defineProperty(_cache2, method, {
        configurable: true,
        enumerable: true,
        set(val){
            if(val){
                cb(val);
                delete _cache[method];
            }
        }
    });
};

// 原生传入改主题
window.changeTheme = (t) => {
    let hash = location.hash.split("?");
    let query = hash[1] || "";
    if(!query){
        hash[1] = `theme=${t}`;
        location.hash = hash.join("?");
        return;
    }
    let params = {};
    query = query.split("&");
    for(let i = 0; i < query.length; i++){
        let [k, v] = query[i].split("=");
        params[k] = v;
    }
    params.theme = t;
    hash[1] = Object.entries(params).map(([k, v]) => {
        return `${k}=${v}`;
    }).join("&");
    location.replace(hash.join("?"));
};

/// ***** iframe 原生交互 接收/分发 *****
// 分发
function iframeSendMessage(data) {
    if (_printEnable) console.log("发送消息 至 iframe: ", data);
    if (window.frames == null || window.frames.length == 0) { return; }
    for (let index = 0; index < window.frames.length; index++) {
        const ele = window.frames[index];
        ele.postMessage(data, "*");
    }
}

// 接收
function iframeReceiveMessage(event) {
    if (_printEnable) console.log("iframe 接收消息 ", event.data);
    let data = event.data;
    if (data == null) return;
    let msgid = data["msgid"], method = data["method"], params = data["params"];
    if (method == null || params == null) return;
    window.response({ msgid : msgid || "", method, params });
    // if (msgid !== null && msgid !== "") {
    //     window.response({ msgid, method, params });
    // } else {
    //     window.response2({ method, cb: params });
    // }
}
window.addEventListener("message", iframeReceiveMessage, false);

//派发
let methods = {};
fns.forEach((f) => {
    methods[f] = ({params, cb}) => { //h5规定格式
        postMessage({method: f, params, cb});
    };
});

export default methods;
