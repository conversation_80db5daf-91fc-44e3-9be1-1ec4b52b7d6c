from fastapi import Response

import requests
from loguru import logger
from config.env_config import get_config_item, AI_RUL

from datetime import datetime
from openai import OpenAI
import json
import re
from servers.websocket import ws_broadcast_proxy

client = OpenAI(base_url=f"{get_config_item(AI_RUL)}/v1", api_key="none")


def ai_analyze_exec(
    question: str, prompt: str, node_id: str, history_id: str, task_id: str
) -> str:
    messages = [
        {"role": "system", "content": f"{prompt}/no_think"},
        {"role": "user", "content": question},
    ]

    full_text = ""

    try:
        stream = client.chat.completions.create(
            model="qwen3-32b", messages=messages, stream=True
        )

        for chunk in stream:
            choices = getattr(chunk, "choices", None)
            if not choices or len(choices) == 0:
                continue

            delta = getattr(choices[0].delta, "content", None)
            if delta:
                full_text += delta
                data = {
                    "type": "ai",
                    "history_id": history_id,
                    "taskId": task_id,
                    "nodeId": node_id,
                    "state": "progress",
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "text": delta,
                }
                ws_broadcast_proxy.broadcast_data(data)

        # 结束标志广播
        end_data = {
            "type": "ai",
            "history_id": history_id,
            "taskId": task_id,
            "nodeId": node_id,
            "state": "end",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "text": "",
        }
        ws_broadcast_proxy.broadcast_data(end_data)
        full_text = re.sub(r"<(think|detail|debug)>.*?</\1>", "", full_text, flags=re.DOTALL)
        return full_text

    except Exception as e:
        err_data = {
            "type": "ai",
            "history_id": history_id,
            "taskId": task_id,
            "nodeId": node_id,
            "state": "end",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "text": f"[异常] AI流式输出失败：{e}",
        }
        ws_broadcast_proxy.broadcast_data(err_data, ensure_ascii=False)
        return f"AI分析异常: {e}"
