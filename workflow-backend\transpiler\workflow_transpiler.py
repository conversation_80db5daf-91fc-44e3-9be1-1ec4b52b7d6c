"""
工作流转译器 - 将JSON工作流转换为Robot Framework代码
"""

import json
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from loguru import logger

# from lxml.xmlerror import element_size

from models import workflow
from utils.path import get_resource_path
from .node_topology import NodeTopology
from .variable_recorder import record_vars

from models.workflow import (
    WorkflowData,
    WorkflowNode,
    WorkflowEdge,
    ComponentDefinition,
    ValidationResult,
    ValidationError,
    ExecutionNode,
    WorkflowNodeConditions,
    WorkflowCondition,
)
from .rpa_components import get_all_rpa_components
from .variable_manager import VariableManager, VariableScope

from config import globals


class WorkflowTranspiler:
    """工作流转译器"""

    def __init__(self):
        self.components: Dict[str, ComponentDefinition] = {}
        self.templates_dir = Path(__file__).parent / "templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True,
        )

        # 初始化变量管理器
        self.variable_manager = VariableManager()

        # 加载组件定义
        self._load_component_definitions()

        logger.info(f"转译器初始化完成，加载了 {len(self.components)} 个组件")

    def _load_component_definitions(self):
        """加载组件定义 - 使用RPA Framework"""
        # 加载所有RPA Framework组件
        self.components.update(get_all_rpa_components())

        # 记录加载的组件数量
        logger.info(
            f"转译器初始化完成，加载了 {len(self.components)} 个RPA Framework组件"
        )

        # 如果需要添加自定义组件，可以在这里继续添加
        # self.components.update({
        #     "custom_component": ComponentDefinition(...)
        # })

    async def transpile(self, workflow: WorkflowData, task_id: str) -> str:
        """转译工作流为Robot Framework代码"""
        logger.info(f"开始转译工作流: {workflow.metadata.name}")

        # 验证工作流
        validation_result = self.validate_workflow(workflow)
        if not validation_result.is_valid:
            error_messages = [error.message for error in validation_result.errors]
            raise ValueError(f"工作流验证失败: {'; '.join(error_messages)}")

        # 构建执行顺序
        execution_order = self._build_execution_order(workflow.nodes, workflow.edges)

        # 生成Robot Framework代码
        robot_code = self._generate_robot_code(workflow, execution_order, task_id)

        logger.info("工作流转译完成")
        return robot_code

    async def transpile_node(self, nodes: List[ExecutionNode], task_id: str) -> str:
        for node in nodes:
            errors = self._validate_node(node.node)
            if errors:
                error_messages = [error.message for error in errors]
                raise ValueError(f"单元测试失败: {'; '.join(error_messages)}")

        return self._generate_robot_node_code(nodes, task_id)

    def validate_workflow(self, workflow: WorkflowData) -> ValidationResult:
        """验证工作流"""
        errors = []
        warnings = []

        # 检查是否有节点
        if not workflow.nodes:
            errors.append(
                ValidationError(message="工作流中没有任何节点", severity="error")
            )
            return ValidationResult(is_valid=False, errors=errors)

        # 验证每个节点
        for node in workflow.nodes:
            node_errors = self._validate_node(node)
            errors.extend(node_errors)

        # 检查循环依赖
        if self._has_circular_dependency(workflow.nodes, workflow.edges):
            errors.append(
                ValidationError(message="工作流中存在循环依赖", severity="error")
            )

        # 检查开始和结束节点
        start_nodes = [
            node
            for node in workflow.nodes
            if node.data.componentType == "workflow_start"
        ]
        end_nodes = [
            node for node in workflow.nodes if node.data.componentType == "workflow_end"
        ]

        # 检查开始节点
        if len(start_nodes) == 0:
            errors.append(
                ValidationError(message="工作流必须包含一个开始节点", severity="error")
            )
        elif len(start_nodes) > 1:
            errors.append(
                ValidationError(message="工作流只能包含一个开始节点", severity="error")
            )

        # 检查结束节点
        if len(end_nodes) == 0:
            warnings.append(
                ValidationError(message="建议为工作流添加结束节点", severity="warning")
            )

        # 检查开始节点不应有输入连接
        for start_node in start_nodes:
            input_edges = [
                edge for edge in workflow.edges if edge.target == start_node.id
            ]
            if input_edges:
                errors.append(
                    ValidationError(
                        node_id=start_node.id,
                        message="开始节点不能有输入连接",
                        severity="error",
                    )
                )

        # 检查结束节点不应有输出连接
        for end_node in end_nodes:
            output_edges = [
                edge for edge in workflow.edges if edge.source == end_node.id
            ]
            if output_edges:
                errors.append(
                    ValidationError(
                        node_id=end_node.id,
                        message="结束节点不能有输出连接",
                        severity="error",
                    )
                )

        # 检查孤立节点（排除开始和结束节点的特殊情况）
        isolated_nodes = self._find_isolated_nodes(workflow.nodes, workflow.edges)
        for node_id in isolated_nodes:
            node = next((n for n in workflow.nodes if n.id == node_id), None)
            if node and node.data.componentType not in [
                "workflow_start",
                "workflow_end",
            ]:
                warnings.append(
                    ValidationError(
                        node_id=node_id,
                        message=f"节点 {node_id} 没有连接到其他节点",
                        severity="warning",
                    )
                )

        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)

    def _validate_node(self, node: WorkflowNode) -> List[ValidationError]:
        """验证单个节点"""
        errors = []

        # 检查组件类型是否存在
        component_type = node.data.componentType
        if not component_type or component_type not in self.components:
            errors.append(
                ValidationError(
                    node_id=node.id,
                    message=f"未知的组件类型: {component_type}",
                    severity="error",
                )
            )
            return errors

        # 获取组件定义
        component = self.components[component_type]

        # 验证必需的配置项
        for field_name, field_config in component.config_schema.items():
            if field_config.get("required", False):
                if field_name not in node.data.config:
                    errors.append(
                        ValidationError(
                            node_id=node.id,
                            field=field_name,
                            message=f"缺少必需的配置项: {field_name}",
                            severity="error",
                        )
                    )
                elif (
                    node.data.config[field_name] == ""
                    or node.data.config[field_name] is None
                ):
                    errors.append(
                        ValidationError(
                            node_id=node.id,
                            field=field_name,
                            message=f"必需的配置项不能为空: {field_name}",
                            severity="error",
                        )
                    )

        return errors

    def _build_execution_order(
        self, nodes: List[WorkflowNode], edges: List[WorkflowEdge]
    ) -> List[str]:
        """构建节点执行顺序（拓扑排序）"""
        # 创建节点映射
        node_map = {node.id: node for node in nodes}

        # 如果没有边连接，按照节点类型的逻辑顺序排列
        if not edges:
            return self._build_default_execution_order(nodes)

        # 构建邻接表
        graph = {node.id: [] for node in nodes}
        in_degree = {node.id: 0 for node in nodes}

        for edge in edges:
            if edge.type in ["control", "workflow"]:  # 考虑控制流边和工作流边
                graph[edge.source].append(edge.target)
                in_degree[edge.target] += 1

        # 找到开始节点，确保它们优先执行
        start_nodes = [
            node.id for node in nodes if node.data.componentType == "workflow_start"
        ]
        end_nodes = [
            node.id for node in nodes if node.data.componentType == "workflow_end"
        ]

        # 拓扑排序，优先处理开始节点
        queue = []

        # 首先添加开始节点
        for node_id in start_nodes:
            if in_degree[node_id] == 0:
                queue.append(node_id)

        # 然后添加其他入度为0的节点（但不包括结束节点）
        for node_id, degree in in_degree.items():
            if degree == 0 and node_id not in start_nodes and node_id not in end_nodes:
                queue.append(node_id)

        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 按优先级排序邻居节点（结束节点放在最后）
            neighbors = graph[current]
            neighbors.sort(
                key=lambda x: (
                    1 if node_map[x].data.componentType == "workflow_end" else 0,
                    x,  # 按节点ID排序作为次要条件
                )
            )

            for neighbor in neighbors:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    # 结束节点放在队列末尾
                    if node_map[neighbor].data.componentType == "workflow_end":
                        queue.append(neighbor)
                    else:
                        queue.insert(0, neighbor)

        # 最后添加入度为0的结束节点
        for node_id in end_nodes:
            if in_degree[node_id] == 0 and node_id not in result:
                result.append(node_id)

        # 如果还有节点没有被处理，说明存在循环依赖
        if len(result) != len(nodes):
            remaining_nodes = [
                node_id for node_id in in_degree if in_degree[node_id] > 0
            ]
            logger.warning(f"检测到循环依赖，剩余节点: {remaining_nodes}")
            # 将剩余节点按原始顺序添加
            result.extend(remaining_nodes)

        return result

    def _build_default_execution_order(self, nodes: List[WorkflowNode]) -> List[str]:
        """当没有边连接时，按照逻辑顺序构建执行顺序"""

        # 按组件类型的优先级排序
        def get_priority(node: WorkflowNode) -> int:
            component_type = node.data.componentType
            if component_type == "workflow_start":
                return 0
            elif component_type in ["new_browser", "create_browser"]:
                return 1
            elif component_type in ["new_page", "open_page"]:
                return 2
            elif component_type in ["click_element", "click"]:
                return 3
            elif component_type in ["comment", "handle_popup"]:
                return 5
            elif component_type == "workflow_end":
                return 999  # 结束节点最后执行
            else:
                return 10  # 其他节点

        # 按优先级和节点ID排序
        sorted_nodes = sorted(nodes, key=lambda n: (get_priority(n), n.id))
        return [node.id for node in sorted_nodes]

    def _has_circular_dependency(
        self, nodes: List[WorkflowNode], edges: List[WorkflowEdge]
    ) -> bool:
        """检查是否存在循环依赖"""
        graph = {node.id: [] for node in nodes}

        for edge in edges:
            if edge.type in ["control", "workflow"]:
                graph[edge.source].append(edge.target)

        # 使用DFS检测循环
        visited = set()
        rec_stack = set()

        def has_cycle(node_id: str) -> bool:
            visited.add(node_id)
            rec_stack.add(node_id)

            for neighbor in graph[node_id]:
                if neighbor not in visited:
                    if has_cycle(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True

            rec_stack.remove(node_id)
            return False

        for node in nodes:
            if node.id not in visited:
                if has_cycle(node.id):
                    return True

        return False

    def _find_isolated_nodes(
        self, nodes: List[WorkflowNode], edges: List[WorkflowEdge]
    ) -> List[str]:
        """查找孤立节点"""
        connected_nodes = set()

        for edge in edges:
            connected_nodes.add(edge.source)
            connected_nodes.add(edge.target)

        isolated = []
        for node in nodes:
            if node.id not in connected_nodes and len(nodes) > 1:
                isolated.append(node.id)

        return isolated

    def _generate_robot_node_code(
        self, exe_nodes: List[ExecutionNode], task_id: str
    ) -> str:
        components_list = []
        for exe_node in exe_nodes:
            node = exe_node.node
            component = self.components.get(node.data.componentType)
            component_template = Template(component.robot_template)
            # 首先合并默认值到配置中
            merged_config = self._merge_default_values(
                node.data.config, component.config_schema
            )
            # 然后对配置进行变量替换
            substituted_config = self.variable_manager.substitute_in_config(
                merged_config
            )
            node.data.config = substituted_config

            rendered_template = component_template.render(**substituted_config)
            final_template = self.variable_manager.substitute_variables(
                rendered_template
            )

            processed_component = ComponentDefinition(
                type=component.type,
                label=component.label,
                description=component.description,
                category=component.category,
                icon=component.icon,
                config_schema=component.config_schema,
                inputs=component.inputs,
                outputs=component.outputs,
                robot_template=final_template,
            )
            components_list.append(processed_component)
        # 准备模板数据
        template_data = {
            "nodes": exe_nodes,
            "components": self.components,
            "processed_components": components_list,
            "task_id": task_id,
        }

        # 渲染主模板
        template = self.jinja_env.get_template("workflow.robot.node.j2")
        robot_code = template.render(**template_data)
        return robot_code

    def _generate_robot_code(
        self, workflow: WorkflowData, execution_order: List[str], task_id: str
    ) -> str:
        """生成Robot Framework代码"""
        # 创建节点映射
        node_map = {node.id: node for node in workflow.nodes}

        # 第一步：预处理所有节点，收集变量定义
        for node_id in execution_order:
            if node_id in node_map:
                node = node_map[node_id]

                component = self.components.get(node.data.componentType)
                if component:
                    # 合并默认值到配置中
                    merged_config = self._merge_default_values(
                        node.data.config, component.config_schema
                    )

                    # 如果是变量设置组件，先注册变量
                    if component.type in [
                        "set_variable",
                        "input_variable",
                        "user_input",
                        "user_choice",
                        "user_confirm",
                    ]:
                        self._handle_variable_component(node, merged_config)

        # 第二步：预处理组件模板 - 为每个节点渲染组件模板
        processed_components = {}
        nodes_list = []

        for node_id in execution_order:
            if node_id in node_map:
                node = node_map[node_id]
                component = self.components.get(node.data.componentType)

                if component and component.robot_template:
                    # 使用节点配置渲染组件模板，支持变量替换
                    try:
                        # 首先合并默认值到配置中
                        merged_config = self._merge_default_values(
                            node.data.config, component.config_schema
                        )

                        # 然后对配置进行变量替换
                        substituted_config = self.variable_manager.substitute_in_config(
                            merged_config
                        )

                        component_template = Template(component.robot_template)
                        rendered_template = component_template.render(
                            **substituted_config
                        )

                        # 对渲染后的模板再次进行变量替换
                        final_template = self.variable_manager.substitute_variables(
                            rendered_template
                        )

                        # 调试日志
                        logger.debug(
                            f"组件 {component.type} 原始模板长度: {len(component.robot_template)}"
                        )
                        logger.debug(
                            f"组件 {component.type} 渲染后模板长度: {len(rendered_template)}"
                        )
                        logger.debug(
                            f"组件 {component.type} 最终模板长度: {len(final_template)}"
                        )

                        # 特别调试 fill_text 组件
                        if component.type == "fill_text":
                            logger.info(f"Fill_text组件配置: {substituted_config}")
                            logger.info(f"Fill_text组件最终模板: {final_template}")

                        if component.type == "word_create":
                            logger.info(f"Word组件最终模板内容: {final_template}")

                        # 创建一个新的组件副本，包含渲染后的模板
                        processed_component = ComponentDefinition(
                            type=component.type,
                            label=component.label,
                            description=component.description,
                            category=component.category,
                            icon=component.icon,
                            config_schema=component.config_schema,
                            inputs=component.inputs,
                            outputs=component.outputs,
                            robot_template=final_template,
                        )
                        # 使用节点ID作为键存储处理后的组件
                        processed_components[node.id] = processed_component

                        # 更新节点配置为替换后的配置，以便模板使用
                        node.data.config = substituted_config

                        node.data.inputs = record_vars(node, task_id, "")

                    except Exception as e:
                        logger.error(f"渲染组件模板失败 {component.type}: {e}")
                        processed_components[node.id] = component
                else:
                    processed_components[node.id] = component

                # 合并outputs
                if component.type == "excel_create" or component.type == "excel_write":
                    node.data.outputs.append("file_path")
                    node.data.outputs.append("file_name")
                nodes_list.append(node)

        options = {"variables": {}}

        if workflow.variables:
            # workflow.variables示例如上，判断是否存在name为token的变量，不存在就追加一个local范围的变量token
            # todo token变量的值需要每次从缓存中获取，或传递进来
            for var in workflow.variables:
                if var["name"] == "token":
                    if not var["value"]:
                        var["value"] = globals.token

            if not any(var["name"] == "token" for var in workflow.variables):
                workflow.variables.append(
                    {
                        "name": "token",
                        "value": globals.token,
                        "type": "string",
                        "scope": "local",
                        "description": "获取当前用户token信息",
                        "isSystem": True,
                    }
                )

            # 设置变量到options
            variables = workflow.variables

            # variables结构如上，获取启用local类型变量的name和value，创建新的变量字典options
            options["variables"] = {
                var["name"]: var["value"]
                for var in variables
                if var["scope"] == "local"
            }

        edge_map, node_map = {}, {}

        for i in range(len(workflow.edges)):
            edge = workflow.edges[i]
            edge_map[edge.id] = edge

        for i in range(len(workflow.nodes)):
            node = workflow.nodes[i]
            node_map[node.id] = node

        main_queue, sub_queue_map, conditions_map = NodeTopology(
            edge_map, nodes_list
        ).generate_topology()

        is_use_browser = self._is_use_browser(nodes_list)

        # 准备模板数据
        template_data = {
            "use_browser": is_use_browser,
            "options": options,
            "workflow": workflow,
            "conditions_map": conditions_map,
            "main_sequence": main_queue,
            "sub_sequence_map": sub_queue_map,
            "node_map": node_map,
            "nodes": nodes_list,
            "components": self.components,
            "processed_components": processed_components,
            "execution_order": execution_order,
            "task_id": task_id,
        }

        if is_use_browser:
            template_data["browser_url"] = get_resource_path(
                "plugins/browsers/chromium-1169/chrome-win/chrome.exe"
            )
            # logger.info(f"浏览器的路径{template_data.get('browser_url')}")
            # template_data["browser_url"] = (
            #     "C:/Program Files/Google/Chrome/Application/chrome.exe"
            # )

        logger.info(f"浏览器的路径{template_data.get('browser_url')}")

        # 渲染主模板
        template = self.jinja_env.get_template("workflow.robot.j2")
        robot_code = template.render(**template_data)

        return robot_code

    def _is_use_browser(self, node_list: List[WorkflowNode]):
        for node in node_list:
            if node.data.componentType == "new_browser":
                return True
        return False

    def _merge_default_values(
        self, config: Dict[str, Any], schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """将组件schema中的默认值合并到配置中"""
        merged_config = config.copy()

        for field_name, field_config in schema.items():
            # 如果配置中没有这个字段，但schema中有默认值，则使用默认值
            if field_name not in merged_config and "default" in field_config:
                merged_config[field_name] = field_config["default"]

        return merged_config

    def get_available_components(self) -> Dict[str, ComponentDefinition]:
        """获取可用的组件定义"""
        return self.components.copy()

    def add_component(self, component: ComponentDefinition):
        """添加新的组件定义"""
        self.components[component.type] = component
        logger.info(f"添加组件: {component.type}")

    def remove_component(self, component_type: str):
        """移除组件定义"""
        if component_type in self.components:
            del self.components[component_type]
            logger.info(f"移除组件: {component_type}")

    def get_component_template(self, component_type: str) -> Optional[str]:
        """获取组件的Robot Framework模板"""
        if component_type in self.components:
            return self.components[component_type].robot_template
        return None

    def _handle_variable_component(self, node: WorkflowNode, config: Dict[str, Any]):
        """处理变量设置组件，更新变量管理器"""
        try:
            variable_name = config.get("variable_name")
            if not variable_name:
                return

            # 确定变量值
            if node.data.componentType in ["user_input", "user_choice", "user_confirm"]:
                # 对于用户交互组件，优先使用用户提供的值
                value = config.get("user_provided_value")
                if value is None:
                    # 如果没有用户提供的值，使用默认值
                    value = config.get("default_value", "")
                logger.info(
                    f"用户交互组件 {node.id} 设置变量 '{variable_name}' = '{value}'"
                )
            elif node.data.componentType == "input_variable":
                # 对于输入变量，使用默认值或占位符
                value = config.get("default_value", f"{{user_input_{variable_name}}}")
            else:
                # 对于设置变量，使用配置的值
                value = config.get("value", "")

            # 确定变量类型和作用域
            var_type = config.get("value_type", "string")
            scope_str = config.get("scope", "local")
            scope = VariableScope.LOCAL

            if scope_str == "global":
                scope = VariableScope.GLOBAL
            elif scope_str == "workflow":
                scope = VariableScope.WORKFLOW
            elif scope_str == "environment":
                scope = VariableScope.ENVIRONMENT

            # 设置变量
            self.variable_manager.set_variable(
                name=variable_name,
                value=value,
                var_type=var_type,
                scope=scope,
                source_node_id=node.id,
                description=config.get("description"),
            )

            logger.info(f"Variable '{variable_name}' registered from node {node.id}")

        except Exception as e:
            logger.error(f"Failed to handle variable component {node.id}: {e}")

    def get_workflow_variables(self) -> Dict[str, Any]:
        """获取当前工作流的所有变量"""
        return {
            name: var.value
            for name, var in self.variable_manager.list_variables().items()
        }

    def clear_variables(self):
        """清除所有变量"""
        self.variable_manager.clear_variables()
