<template>
  <div class="settings-page">
    <div class="settings-header">
      <h1>设置</h1>
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
    </div>

    <div class="settings-content">
      <el-tabs v-model="activeTab" tab-position="left">
        <el-tab-pane label="通用设置" name="general">
          <div class="settings-section">
            <h3>应用设置</h3>
            <el-form :model="generalSettings" label-width="120px">
              <el-form-item label="语言">
                <el-select v-model="generalSettings.language" style="width: 200px">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </el-form-item>

              <el-form-item label="主题">
                <el-select v-model="generalSettings.theme" style="width: 200px">
                  <el-option label="浅色主题" value="light" />
                  <el-option label="深色主题" value="dark" />
                  <el-option label="跟随系统" value="auto" />
                </el-select>
              </el-form-item>

              <el-form-item label="自动保存">
                <el-switch v-model="generalSettings.autoSave" />
                <span class="form-help">每5分钟自动保存工作流</span>
              </el-form-item>

              <el-form-item label="启动时恢复">
                <el-switch v-model="generalSettings.restoreOnStartup" />
                <span class="form-help">启动时恢复上次的工作流</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="执行设置" name="execution">
          <div class="settings-section">
            <h3>执行引擎</h3>
            <el-form :model="executionSettings" label-width="120px">
              <el-form-item label="Python路径">
                <el-input
                  v-model="executionSettings.pythonPath"
                  placeholder="python"
                  style="width: 300px"
                />
                <span class="form-help">Python解释器路径</span>
              </el-form-item>

              <el-form-item label="默认超时">
                <el-input-number
                  v-model="executionSettings.defaultTimeout"
                  :min="1"
                  :max="3600"
                  controls-position="right"
                />
                <span class="form-help">秒</span>
              </el-form-item>

              <el-form-item label="并行执行">
                <el-switch v-model="executionSettings.parallelExecution" />
                <span class="form-help">允许同时执行多个工作流</span>
              </el-form-item>

              <el-form-item label="日志级别">
                <el-select v-model="executionSettings.logLevel" style="width: 200px">
                  <el-option label="TRACE" value="TRACE" />
                  <el-option label="DEBUG" value="DEBUG" />
                  <el-option label="INFO" value="INFO" />
                  <el-option label="WARN" value="WARN" />
                  <el-option label="ERROR" value="ERROR" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="浏览器设置" name="browser">
          <div class="settings-section">
            <h3>浏览器配置</h3>
            <el-form :model="browserSettings" label-width="120px">
              <el-form-item label="默认浏览器">
                <el-select v-model="browserSettings.defaultBrowser" style="width: 200px">
                  <el-option label="Chrome" value="chrome" />
                  <el-option label="Firefox" value="firefox" />
                  <el-option label="Safari" value="safari" />
                  <el-option label="Edge" value="edge" />
                </el-select>
              </el-form-item>

              <el-form-item label="无头模式">
                <el-switch v-model="browserSettings.headless" />
                <span class="form-help">默认以无头模式运行浏览器</span>
              </el-form-item>

              <el-form-item label="窗口大小">
                <div class="window-size-inputs">
                  <el-input-number
                    v-model="browserSettings.windowWidth"
                    :min="800"
                    :max="3840"
                    controls-position="right"
                  />
                  <span>×</span>
                  <el-input-number
                    v-model="browserSettings.windowHeight"
                    :min="600"
                    :max="2160"
                    controls-position="right"
                  />
                </div>
              </el-form-item>

              <el-form-item label="用户代理">
                <el-input
                  v-model="browserSettings.userAgent"
                  type="textarea"
                  :rows="3"
                  placeholder="留空使用默认用户代理"
                  style="width: 400px"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="关于" name="about">
          <div class="settings-section">
            <div class="about-content">
              <div class="app-info">
                <h2>Phoenix RPA Designer</h2>
                <p class="version">版本 1.0.0</p>
                <p class="description">新一代可视化自动化设计器，让RPA开发变得简单高效。</p>
              </div>

              <div class="tech-stack">
                <h3>技术栈</h3>
                <ul>
                  <li>前端：Electron + Vue 3 + TypeScript</li>
                  <li>后端：Python + FastAPI</li>
                  <li>执行引擎：Robot Framework</li>
                  <li>UI组件：Element Plus</li>
                  <li>流程图：VueFlow</li>
                </ul>
              </div>

              <div class="links">
                <el-button type="primary" @click="openGitHub">
                  <el-icon>
                    <Link />
                  </el-icon>
                  GitHub
                </el-button>
                <el-button @click="openDocs">
                  <el-icon>
                    <Document />
                  </el-icon>
                  文档
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="settings-footer">
      <el-button @click="resetSettings">重置设置</el-button>
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Link, Document } from '@element-plus/icons-vue'

const activeTab = ref('general')

// 设置数据
const generalSettings = reactive({
  language: 'zh-CN',
  theme: 'light',
  autoSave: true,
  restoreOnStartup: true,
})

const executionSettings = reactive({
  pythonPath: 'python',
  defaultTimeout: 30,
  parallelExecution: false,
  logLevel: 'INFO',
})

const browserSettings = reactive({
  defaultBrowser: 'chrome',
  headless: false,
  windowWidth: 1920,
  windowHeight: 1080,
  userAgent: '',
})

// 方法
const saveSettings = () => {
  // TODO: 实现设置保存逻辑
  ElMessage.success('设置已保存')
}

const resetSettings = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？此操作不可撤销。', '重置设置', {
      confirmButtonText: '重置',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 重置为默认值
    Object.assign(generalSettings, {
      language: 'zh-CN',
      theme: 'light',
      autoSave: true,
      restoreOnStartup: true,
    })

    Object.assign(executionSettings, {
      pythonPath: 'python',
      defaultTimeout: 30,
      parallelExecution: false,
      logLevel: 'INFO',
    })

    Object.assign(browserSettings, {
      defaultBrowser: 'chrome',
      headless: false,
      windowWidth: 1920,
      windowHeight: 1080,
      userAgent: '',
    })

    ElMessage.success('设置已重置')
  } catch {
    // 用户取消
  }
}

const openGitHub = () => {
  // TODO: 打开GitHub链接
  ElMessage.info('GitHub链接功能开发中...')
}

const openDocs = () => {
  // TODO: 打开文档链接
  ElMessage.info('文档链接功能开发中...')
}
</script>

<style scoped>
.settings-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.settings-header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.settings-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-help {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
}

.window-size-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.about-content {
  text-align: center;
}

.app-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.version {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #909399;
}

.description {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.tech-stack {
  margin: 32px 0;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.tech-stack h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.tech-stack ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.tech-stack li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.links {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #e4e7ed;
}

/* Element Plus 样式调整 */
:deep(.el-tabs--left .el-tabs__content) {
  padding-left: 20px;
}

:deep(.el-tabs--left .el-tabs__nav-wrap) {
  margin-right: 20px;
}

:deep(.el-tabs--left .el-tabs__item) {
  text-align: left;
  padding: 0 20px;
}
</style>
