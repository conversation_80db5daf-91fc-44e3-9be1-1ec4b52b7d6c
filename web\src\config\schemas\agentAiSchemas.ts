/**
 * 数据库操作组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const aiAnalysisSchema: ComponentConfigSchema = {
  componentType: 'ai_analyze',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'output',
      label: '指令输出',
      description: '',
      icon: 'Document',
      order: 2,
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    question: {
      type: 'textarea',
      label: '问题',
      description: '',
      placeholder: '请输入需要AI帮你分析的问题？',
      required: true,
      group: 'input',
      order: 1,
      rows: 3,
      variableSupport: true
    },
    prompt: {
      type: 'textarea',
      label: '提示词',
      description: '',
      placeholder: '请描述您的角色身份，以及想如何回答上面的问题',
      group: 'input',
      // required: true,
      order: 1,
      rows: 5,
      variableSupport: true
    },
    ai_analyze_response: {
      type: 'string',
      label: '响应内容变量',
      description: '',
      placeholder: 'AI_Text',
      required: true,
      group: 'output',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    // timeout: {
    //   type: 'number',
    //   label: '超时时间',
    //   description: '',
    //   // required: true,
    //   group: 'other',
    //   order: 1,
    //   default: 30,
    //   min: 1,
    //   max: 3000,
    //   unit: '秒',
    // },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    },
  },

  presets: {

  },
  examples: [

  ],
}
export const imgReconitionSchema: ComponentConfigSchema = {
  componentType: 'img_recognition',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'output',
      label: '指令输出',
      description: '',
      icon: 'Document',
      order: 2,
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    // 输入变量配置
    img: {
      type: 'stringArray',
      label: '选择图片',
      description: '请输入需要识别的图片地址',
      placeholder: '待识别图片地址',
      group: 'input',
      order: 1,
      required: true,
      addVariableDatas: [],
      variableSupport: true,
      outputVariable: true,
    },
    prompt: {
      type: 'textarea',
      label: '识别要求',
      description: '',
      placeholder: '请描述要识别的内容提示词',
      group: 'input',
      required: true,
      order: 2,
      rows: 5,
      variableSupport: true
    },

    recognition_result: {
      type: 'string',
      label: '响应内容变量',
      description: '',
      placeholder: 'recognition_result',
      required: true,
      group: 'output',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '',
      // required: true,
      group: 'other',
      order: 1,
      default: 15,
      min: 1,
      max: 3000,
      unit: '秒',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    },
  },

  presets: {

  },
  examples: [

  ],
}
export const invoiceReconitionSchema: ComponentConfigSchema = {
  componentType: 'invoice_recognition',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'output',
      label: '指令输出',
      description: '',
      icon: 'Document',
      order: 2,
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    // 输入变量配置
    img: {
      type: 'string',
      label: '待识别发票目录',
      description: '请输入需要识别的发票目录',
      placeholder: '待识别发票目录',
      group: 'input',
      order: 1,
      required:true,
      // addVariableDatas: [],
      variableSupport: true,
      outputVariable: true,
    },
    ticket_type: {
     type: 'select',
      label: '发票类型',
      description: '发票类型',
      group: 'input',
      order: 2,
      required:true,
      default: '',
      options: [
        { label: '火车票', value: 'trainTickets', description: '火车票' },
        { label: '出租车', value: 'taxiTickets', description: '出租车' },
        { label: '增值税发票', value: 'invoice', description: '增值税发票' },
        { label: '银行回单', value: 'bankreceipt', description: '银行回单' },
        { label: '过路费', value: 'highwayToll', description: '过路费' }
      ],
    },
    // prompt: {
    //   type: 'textarea',
    //   label: '提示词',
    //   description: '',
    //   placeholder: '请描述要识别的内容，',
    //   group: 'input',
    //   required: true,
    //   order: 2,
    //   rows: 5,
    //   variableSupport: true
    // },

    recognition_results: {
      type: 'string',
      label: '响应内容变量',
      description: '',
      placeholder: 'recognition_results',
      required: true,
      group: 'output',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '',
      // required: true,
      group: 'other',
      order: 1,
      default: 15,
      min: 1,
      max: 3000,
      unit: '秒',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    },
  },

  presets: {

  },
  examples: [

  ],
}
export const textToSpeechSchema: ComponentConfigSchema = {
  componentType: 'text_to_speech',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    }
  ],

  fields: {
    input: {
      type: 'textarea',
      label: '播报内容',
      description: '',
      placeholder: '请输入需要播报的内容',
      required: true,
      group: 'input',
      order: 1,
      rows: 3,
      variableSupport: true
    },
    timeout: {
      type: 'number',
      label: '超时时间',
      description: '',
      // required: true,
      group: 'other',
      order: 1,
      default: 30,
      min: 1,
      max: 3000,
      unit: '秒',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    },
  },

  presets: {

  },
  examples: [

  ],
}
