<!doctype html>
<html id="library-documentation-top" lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="Generator" content>
    <!-- JS MODEL -->
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKcAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAAqAAAAAAAAAAAAAAAAAAAALIAAAD/AAAA4AAAANwAAADcAAAA3AAAANwAAADcAAAA3AAAANwAAADcAAAA4AAAAP8AAACxAAAAAAAAAKYAAAD/AAAAuwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC/AAAA/wAAAKkAAAD6AAAAzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN8AAAD/AAAA+gAAAMMAAAAAAAAAAgAAAGsAAABrAAAAawAAAGsAAABrAAAAawAAAGsAAABrAAAADAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAIsAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAANEAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAAAAAAAAMgAAADIAAAAyAAAAMgAAADIAAAAyAAAAMgAAADIAAAAFAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAADwAAAB8AAAAAAAAAGAAAABcAAAAAAAAAH8AAABKAAAAAAAAAAAAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAADCAAAA/wAAACkAAADqAAAA4QAAAAAAAAD7AAAA/wAAALAAAAAGAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAIwAAAP4AAAD/AAAA/wAAAGAAAAAAAAAAAAAAAMkAAAD/AAAAigAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAAAAAAAIAAAAcAAAABkAAAAAAAAAAAAAAAAAAAAAAAAAEgAAAAAAAAAAAAAA2gAAAP8AAAD7AAAAywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN4AAAD/AAAAqwAAAP8AAACvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALIAAAD/AAAAsgAAAAAAAAC5AAAA/wAAAMoAAADAAAAAwAAAAMAAAADAAAAAwAAAAMAAAADAAAAAwAAAAMkAAAD/AAAAvAAAAAAAAAAAAAAAAAAAAKwAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAArQAAAAAAAAAAwAMAAIABAAAf+AAAP/wAAD/8AAAgBAAAP/wAAD/8AAA//AAAJIwAADHEAAA//AAAP/wAAB/4AACAAQAAwAMAAA==">
  </head>
  <body>
    <style>#javascript-disabled{color:#000;background:#eee;border:1px solid #ccc;width:600px;margin:100px auto 0;padding:20px}#javascript-disabled h1{float:none;width:100%}#javascript-disabled ul{font-size:1.2em}#javascript-disabled li{margin:.5em 0}#javascript-disabled b{font-style:italic}</style>

    <div id="javascript-disabled">
      <h1>Opening library documentation failed</h1>
      <ul>
        <li>Verify that you have <b>JavaScript enabled</b> in your browser.</li>
        <li>
          Make sure you are using a <b>modern enough browser</b>. If using
          Internet Explorer, version 11 is required.
        </li>
        <li>
          Check are there messages in your browser's
          <b>JavaScript error log</b>. Please report the problem if you suspect
          you have encountered a bug.
        </li>
      </ul>
    </div>

    <script type="text/javascript">document.getElementById("javascript-disabled").style.display="none",window.addEventListener("hashchange",function(){document.getElementsByClassName("hamburger-menu")[0].checked=!1},!1),window.addEventListener("hashchange",function(){if(0==window.location.hash.indexOf("#type-")){let e="#type-modal-"+decodeURI(window.location.hash.slice(6)),n=document.querySelector(".data-types").querySelector(e);n&&showModal(n)}},!1);</script>

    <style>:root{--background-color:white;--text-color:black;--border-color:#e0e0e2;--light-background-color:#f3f3f3;--robot-highlight:#00c0b5;--highlighted-color:var(--text-color);--highlighted-background-color:yellow;--less-important-text-color:gray;--link-color:#00e}[data-theme=dark]{--background-color:#1c2227;--text-color:#e2e1d7;--border-color:#4e4e4e;--light-background-color:#002b36;--robot-highlight:yellow;--highlighted-color:var(--background-color);--highlighted-background-color:yellow;--less-important-text-color:#5b6a6f;--link-color:#52adff;--lightningcss-light: ;--lightningcss-dark:initial;--lightningcss-light: ;--lightningcss-dark:initial;color-scheme:dark}body{background:var(--background-color);color:var(--text-color);margin:0;font-family:system-ui,-apple-system,sans-serif}input,button,select{background:var(--background-color);color:var(--text-color)}a{color:var(--link-color)}.base-container{display:flex}.libdoc-overview{background:#fff;background:var(--background-color);flex-direction:column;height:100vh;display:flex;position:sticky;top:0}.libdoc-overview h4{margin-top:.5rem;margin-bottom:.5rem}.keyword-search-box{border:1px solid var(--border-color);border-radius:3px;justify-content:space-between;height:30px;margin-top:.5rem;display:flex}#tags-shortcuts-container{border:1px solid var(--border-color);border-radius:3px;height:30px;margin-top:.5rem}.search-input{text-indent:4px;border:none;flex:1}.clear-search{border:none}#shortcuts-container{flex-direction:column;height:100%;display:flex}.libdoc-details{max-width:1000px;margin-top:60px;padding-left:2%;padding-right:2%;overflow:auto}.libdoc-title{color:var(--text-color);align-items:center;width:300px;height:36px;margin:.5rem;padding:.5rem;text-decoration:none;display:flex;position:fixed;top:0;left:0}#language-container{z-index:1000;position:fixed;top:0;right:0}#language-container button{border:none;padding-top:15px;padding-right:15px}#language-container svg{width:20px;height:20px}#language-container svg path{stroke:var(--text-color);fill:var(--background-color)}#language-container ul{background-color:var(--background-color);margin:0;padding:10px;list-style:none}#language-container a{cursor:pointer;color:var(--less-important-text-color);text-decoration:none}#language-container a.selected{color:var(--text-color)}.hamburger-menu{z-index:100;display:none;position:fixed}input.hamburger-menu{cursor:pointer;opacity:0;z-index:2;-webkit-touch-callout:none;width:67px;height:46px;display:none;position:fixed;top:0;right:0}span.hamburger-menu{background:#000;background:var(--text-color);z-index:1;transform-origin:4px 0;border-radius:2px;width:31px;height:2px;margin-bottom:5px;transition:transform .3s cubic-bezier(.77,.2,.05,1),opacity .35s;position:fixed;right:20px}span.hamburger-menu-1{transform-origin:0 0;top:14px}span.hamburger-menu-2{top:24px}span.hamburger-menu-3{transform-origin:0 100%;top:34px}input.hamburger-menu:checked~span.hamburger-menu-1{opacity:1;background:var(--text-color);transform:rotate(45deg)translate(2px,-3px)}input.hamburger-menu:checked~span.hamburger-menu-2{opacity:0;transform:rotate(0)scale(.2)}input.hamburger-menu:checked~span.hamburger-menu-3{background:var(--text-color);transform:rotate(-45deg)translate(2px,3px)}.libdoc-title>svg{width:42px;height:42px;padding-top:2px}#robot-svg-path{fill:var(--text-color);stroke:none;fill-opacity:1;fill-rule:nonzero}.keywords-overview{border:1px solid var(--border-color);border-radius:3px;flex-direction:column;flex:1;height:0;max-height:calc(100vh - 60px - .5rem);margin:60px 0 .5rem .5rem;padding-left:.5rem;padding-right:.5rem;display:flex}.keywords-overview-header-row{justify-content:space-between;display:flex}.shortcuts{flex:1;max-width:320px;margin:0;padding-left:0;font-size:.9em;list-style:none;overflow:auto}.shortcuts.keyword-wall{flex:unset}.shortcuts a{white-space:nowrap;color:var(--text-color);padding:.5rem;text-decoration:none;display:block}.shortcuts a:hover{background:var(--light-background-color)}.shortcuts a:first-letter{letter-spacing:.1em;font-weight:700}.shortcuts.keyword-wall a{padding:0 .5rem .5rem 0}.shortcuts.keyword-wall a:after{content:"·";padding-left:.5rem}.enum-type-members,.dt-usages-list{padding-left:1em;list-style:none}.dt-usages-list>li{margin-bottom:.2em}.dt-usages a{color:var(--text-color);font-size:.9em;text-decoration:none;display:inline-block}.dt-usages a:first-letter{letter-spacing:.1em;font-weight:700}.arguments-list-container{margin-bottom:1.33rem;overflow-y:auto}.arguments-list{display:-ms-inline-grid;-ms-grid-columns:1fr 1fr 1fr;grid-template-columns:auto auto auto;row-gap:3px;display:inline-grid}.typed-dict-annotation>span,.enum-type-members span,.arguments-list .arg-name{-ms-grid-column:1;white-space:nowrap;border-radius:3px;grid-column:1;justify-self:start;padding-left:.5rem;padding-right:.5rem}.arguments-list .arg-default-container{-ms-grid-column:2;grid-column:2;display:flex}.optional-key{font-style:italic}.arguments-list .arg-default-eq{background:var(--background-color);margin-left:2rem;margin-right:.5rem}.arguments-list .arg-default-value{border-radius:3px;padding-left:.5rem;padding-right:.5rem}.arguments-list .base-arg-data{min-width:150px;display:flex}.arguments-list .arg-type,.return-type .arg-type{-ms-grid-column:3;background:var(--background-color);white-space:nowrap;-webkit-text-size-adjust:none;grid-column:3;margin-left:2rem}.tags .kw-tags{margin-left:2rem;display:flex}.tag-link{cursor:pointer}.tag-link:hover{text-decoration:underline}.arguments-list .arg-kind{color:#0000;text-shadow:0 0 0 var(--less-important-text-color);padding:0;font-size:.8em}@media only screen and (width>=900px){.libdoc-details{z-index:1;background:var(--background-color)}#toggle-keyword-shortcuts{border:1px solid var(--border-color);border-radius:3px;margin-top:3px;margin-bottom:3px}#toggle-keyword-shortcuts:hover{background:var(--light-background-color)}.shortcuts.keyword-wall{flex-wrap:wrap;width:320px;max-width:none;display:flex}}@media only screen and (width>=1200px){.shortcuts.keyword-wall{width:640px}}@media only screen and (width<=899px){.libdoc-overview,#toggle-keyword-shortcuts{display:none}.libdoc-title{border-bottom:1px solid var(--border-color);background:#fff;background:var(--background-color);width:100%;margin:0;padding:.5rem}.libdoc-title>svg{margin-right:60px}.libdoc-details{padding-left:.5rem}input.hamburger-menu,.hamburger-menu{display:block}.hamburger-menu:checked~.libdoc-overview{width:100%;height:100vh;display:block;position:fixed}.keywords-overview{border:none;margin:60px 0 0}.shortcuts{overscroll-behavior:none;max-width:100vw}#language-container{width:100px;right:50px}#language-container button{cursor:pointer}}.metadata{margin-top:.5rem}.metadata th{text-align:left;padding-right:1em}a.name,span.name{font-style:italic}.libdoc-details a img{border:1px solid #c30!important}a:hover,a:active{color:var(--text-color);text-decoration:underline}a:hover{text-decoration:underline!important}.normal-first-letter:first-letter{letter-spacing:0!important;font-weight:400!important}.shortcut-list-toggle,.tag-list-toggle{margin-bottom:1em;font-size:.9em}input.switch{display:none}.slider{background-color:var(--border-color);width:36px;height:18px;display:inline-block;position:relative;top:5px}.slider:before{background-color:var(--background-color);content:"";width:12px;height:12px;position:absolute;top:3px;left:3px}input.switch:checked+.slider:before{background-color:var(--background-color);left:21px}.keywords{flex-direction:column;display:flex}.kw-overview{flex-direction:column;justify-content:start;display:flex}@media only screen and (width>=899px){.kw-overview{max-width:850px;margin-right:1.5rem}}.kw-docs{flex-direction:column;display:flex;overflow-y:auto}.dt-name:link,.kw-name:link,.dt-name:visited,.kw-name:visited{color:var(--text-color);text-decoration:none}.kw{align-items:baseline;min-width:250px;display:flex}h4{margin-right:.5rem}.keyword-container{border:1px solid var(--border-color);border-radius:3px;flex-direction:column;margin-bottom:.5rem;padding:.5rem 1rem;scroll-margin-top:60px;display:flex}.keyword-container:target{box-shadow:0 0 4px var(--robot-highlight)}.data-type-content,.keyword-content{flex-direction:column;display:flex}.data-type-container{border-top:1px solid var(--border-color);flex-direction:column;margin-bottom:.5rem;padding:.5rem 1rem;scroll-margin-top:60px;display:flex}.kw-row{border:1px solid var(--border-color);border-radius:3px;flex-direction:column;justify-content:start;margin-bottom:.5rem;padding:.5rem 1rem;text-decoration:none;display:flex}.kw a{color:inherit;font-weight:700;text-decoration:none}.args{min-width:200px}.enum-type-members span,.args span,.return-type span,.args a{background:var(--light-background-color);padding:0 .1em;font-family:monospace;font-size:1.1em}.arg-type,span.type,a.type{background:0 0;padding:0;font-size:1em}.typed-dict-item .td-type:after{content:","}.typed-dict-item .td-type:nth-last-child(2):after{content:""}.td-item:before{content:"  ";white-space:pre}.typed-dict-item{background:var(--light-background-color);padding:.4rem;font-family:monospace;font-size:1.1em;display:block}.args span .highlight{background:var(--highlighted-background-color);color:var(--highlighted-color)}.tags,.return-type{align-items:baseline;display:flex}.tags a{color:inherit;padding:0 .1em;text-decoration:none}.footer{font-size:.9em}.doc div>:last-child{margin-bottom:0}.highlight{background:var(--highlighted-background-color);color:var(--highlighted-color)}.data-type{font-style:italic}.no-match{color:var(--less-important-text-color)!important}.no-match .dt-name,.no-match .kw-name{color:var(--less-important-text-color)}.modal-icon{cursor:pointer;background:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" height=\"100%\" width=\"100%\"><path stroke=\"black\" fill=\"none\" stroke-width=\"2px\" stroke-linecap=\"round\" d=\"M1 8 L1 1 L8 1 M16 1 L23 1 L23 8 M23 16 L23 23 L16 23 M8 23 L1 23 L1 16\"></path><path fill=\"black\" stroke=\"none\" stroke-width=\"1px\" transform=\"scale(1.3) translate(-3 -2.5)\" d=\"M19 7.97zm-8 9.2-4-2.3v-4.63l4 2.33v4.6zm1-6.33L8.04 8.53 12 6.25l3.96 2.28L12 10.84zm5 4.03-4 2.3v-4.6l4-2.33v4.63z\"></path></svg>");border:none;width:1rem;height:1rem;margin:0 .25rem;padding:0;font-size:12px;font-weight:600}@media (prefers-color-scheme:dark){.modal-icon{background:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" height=\"100%\" width=\"100%\"><path stroke=\"%23e2e1d7\" fill=\"none\" stroke-width=\"2px\" stroke-linecap=\"round\" d=\"M1 8 L1 1 L8 1 M16 1 L23 1 L23 8 M23 16 L23 23 L16 23 M8 23 L1 23 L1 16\"></path><path fill=\"%23e2e1d7\" stroke=\"none\" stroke-width=\"1px\" transform=\"scale(1.3) translate(-3 -2.5)\" d=\"M19 7.97zm-8 9.2-4-2.3v-4.63l4 2.33v4.6zm1-6.33L8.04 8.53 12 6.25l3.96 2.28L12 10.84zm5 4.03-4 2.3v-4.6l4-2.33v4.63z\"></path></svg>")}}.modal-background,.modal{opacity:0;pointer-events:none;transition:opacity .2s}.modal-background{z-index:1;background-color:#000000b3;position:fixed;inset:0}.modal{background-color:var(--background-color);border:1px solid var(--border-color);z-index:2;border-radius:3px;flex-flow:column;width:720px;max-width:calc(100vw - 2rem);height:calc(100vh - 6rem);margin:0 auto;transition-delay:.1s;display:flex;overflow:auto}.modal-content{margin-bottom:3rem}.modal>.modal-content>.data-type-container{border-top:none}.modal-close-button-wrapper{justify-content:flex-end;display:flex}.modal-close-button-container{width:720px;max-width:calc(100vw - 2rem);margin:0 auto;overflow:auto}.modal-close-button{border:1px solid var(--border-color);cursor:pointer;border-radius:3px;margin:.5rem 0;padding:.25rem .5rem}.modal-background.visible,.modal.visible{opacity:1;pointer-events:all}#data-types-container,.hidden{display:none}</style>
    <style>#introduction-container>h2,.doc>h1,.doc>h2,.section>h1,.section>h2{margin-top:4rem;margin-bottom:1rem}.doc>h3,.section>h3{margin-top:3rem;margin-bottom:1rem}.doc>h4,.section>h4{margin-top:2rem;margin-bottom:1rem}.doc>p,.section>p{margin-top:1rem;margin-bottom:.5rem}.doc>:first-child{margin-top:.1em}.doc table{border-collapse:collapse;empty-cells:show;background:0 0;border:none;font-size:.9em;display:block;overflow-y:auto}.doc table th,.doc table td{border:1px solid var(--border-color);background:0 0;height:1.2em;padding:.1em .3em}.doc table th{text-align:center;letter-spacing:.1em}.doc pre{letter-spacing:.05em;background:var(--light-background-color);border-radius:3px;padding:.3rem;font-size:1.1em;overflow-y:auto}.doc code,.docutils.literal{letter-spacing:.05em;background:var(--light-background-color);border-radius:3px;padding:1px;font-size:1.1em}.doc li{list-style-type:square;list-style-position:inside}.doc img{border:1px solid #ccc}.doc hr{background:#ccc;border:0;height:1px}</style>
    <style>.code .hll{background-color:#ffc}.code{background:#f8f8f8}.code .c{color:#408080;font-style:italic}.code .err{border:1px solid red}.code .k{color:green;font-weight:700}.code .o{color:#666}.code .ch,.code .cm{color:#408080;font-style:italic}.code .cp{color:#bc7a00}.code .cpf,.code .c1,.code .cs{color:#408080;font-style:italic}.code .gd{color:#a00000}.code .ge{font-style:italic}.code .gr{color:red}.code .gh{color:navy;font-weight:700}.code .gi{color:#00a000}.code .go{color:#888}.code .gp{color:navy;font-weight:700}.code .gs{font-weight:700}.code .gu{color:purple;font-weight:700}.code .gt{color:#04d}.code .kc,.code .kd,.code .kn{color:green;font-weight:700}.code .kp{color:green}.code .kr{color:green;font-weight:700}.code .kt{color:#b00040}.code .m{color:#666}.code .s{color:#ba2121}.code .na{color:#7d9029}.code .nb{color:green}.code .nc{color:#00f;font-weight:700}.code .no{color:#800}.code .nd{color:#a2f}.code .ni{color:#999;font-weight:700}.code .ne{color:#d2413a;font-weight:700}.code .nf{color:#00f}.code .nl{color:#a0a000}.code .nn{color:#00f;font-weight:700}.code .nt{color:green;font-weight:700}.code .nv{color:#19177c}.code .ow{color:#a2f;font-weight:700}.code .w{color:#bbb}.code .mb,.code .mf,.code .mh,.code .mi,.code .mo{color:#666}.code .sa,.code .sb,.code .sc,.code .dl{color:#ba2121}.code .sd{color:#ba2121;font-style:italic}.code .s2{color:#ba2121}.code .se{color:#b62;font-weight:700}.code .sh{color:#ba2121}.code .si{color:#b68;font-weight:700}.code .sx{color:green}.code .sr{color:#b68}.code .s1{color:#ba2121}.code .ss{color:#19177c}.code .bp{color:green}.code .fm{color:#00f}.code .vc,.code .vg,.code .vi,.code .vm{color:#19177c}.code .il{color:#666}@media (prefers-color-scheme:dark){.code .hll{background-color:#073642}.code{color:#839496;background:#002b36}.code .c{color:#586e75;font-style:italic}.code .err{color:#839496;background-color:#dc322f}.code .esc,.code .g{color:#839496}.code .k{color:#859900}.code .l,.code .n{color:#839496}.code .o{color:#586e75}.code .x,.code .p{color:#839496}.code .ch,.code .cm{color:#586e75;font-style:italic}.code .cp{color:#d33682}.code .cpf{color:#586e75}.code .c1,.code .cs{color:#586e75;font-style:italic}.code .gd{color:#dc322f}.code .ge{color:#839496;font-style:italic}.code .gr{color:#dc322f}.code .gh{color:#839496;font-weight:700}.code .gi{color:#859900}.code .go,.code .gp{color:#839496}.code .gs{color:#839496;font-weight:700}.code .gu{color:#839496;text-decoration:underline}.code .gt{color:#268bd2}.code .kc,.code .kd{color:#2aa198}.code .kn{color:#cb4b16}.code .kp,.code .kr{color:#859900}.code .kt{color:#b58900}.code .ld{color:#839496}.code .m,.code .s{color:#2aa198}.code .na{color:#839496}.code .nb,.code .nc,.code .no,.code .nd,.code .ni,.code .ne,.code .nf,.code .nl,.code .nn{color:#268bd2}.code .nx,.code .py{color:#839496}.code .nt,.code .nv{color:#268bd2}.code .ow{color:#859900}.code .w{color:#839496}.code .mb,.code .mf,.code .mh,.code .mi,.code .mo,.code .sa,.code .sb,.code .sc,.code .dl{color:#2aa198}.code .sd{color:#586e75}.code .s2,.code .se,.code .sh,.code .si,.code .sx{color:#2aa198}.code .sr{color:#cb4b16}.code .s1,.code .ss{color:#2aa198}.code .bp,.code .fm,.code .vc,.code .vg,.code .vi,.code .vm{color:#268bd2}.code .il{color:#2aa198}}</style>
    <style media="print">body{margin:0;padding:0;font-size:8pt}a{text-decoration:none}#search,#open-search{display:none}</style>

    <div id="root"></div>
    <script type="text/x-handlebars-template" id="base-template">
      <div class="base-container">
        <div id="language-container">
        </div>
        <input
          id="hamburger-menu-input"
          class="hamburger-menu"
          type="checkbox"
        />
        <span class="hamburger-menu hamburger-menu-1"></span>
        <span class="hamburger-menu hamburger-menu-2"></span>
        <span class="hamburger-menu hamburger-menu-3"></span>
        <div class="libdoc-overview"><div id="shortcuts-container"></div></div>
        <div class="libdoc-details">
          <table class="metadata">
            {{#if version}}<tr><th>{{t "libVersion"}}:</th><td
                >{{version}}</td></tr>{{/if}}
            {{#if scope}}<tr><th>{{t "libScope"}}:</th><td
                >{{scope}}</td></tr>{{/if}}
          </table>
          <div id="introduction-container">
            <h2 id="introduction">{{t "intro"}}</h2>
            <div class="doc">{{{doc}}}</div>
          </div>
          <div id="importing-container"></div>
          <div id="keywords-container"></div>
          <div id="data-types-container"></div>
          <div id="footer-container"></div>
        </div>
        <a class="libdoc-title" href="#library-documentation-top">
          <h1>{{name}}</h1>
          <svg
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:cc="http://creativecommons.org/ns#"
            xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
            xmlns:svg="http://www.w3.org/2000/svg"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 202.4325 202.34125"
            height="42"
            width="42"
            xml:space="preserve"
            version="1.1"
          ><metadata id="metadata8"><rdf:RDF><cc:Work rdf:about=""><dc:format
                  >image/svg+xml</dc:format><dc:type
                    rdf:resource="http://purl.org/dc/dcmitype/StillImage"
                  /></cc:Work></rdf:RDF></metadata><defs id="defs6"><clipPath
                id="clipPath16"
                clipPathUnits="userSpaceOnUse"
              ><path
                  id="path18"
                  d="m 0,161.873 161.946,0 L 161.946,0 0,0 0,161.873 Z"
                /></clipPath></defs><g
              transform="matrix(1.25,0,0,-1.25,0,202.34125)"
              id="g10"
            ><g id="g12"><g clip-path="url(#clipPath16)" id="g14"><g
                    transform="translate(52.4477,88.1268)"
                    id="g20"
                  ><path
                      id="robot-svg-path"
                      d="m 0,0 c 0,7.6 6.179,13.779 13.77,13.779 7.6,0 13.779,-6.179 13.779,-13.779 0,-2.769 -2.238,-5.007 -4.998,-5.007 -2.761,0 -4.999,2.238 -4.999,5.007 0,2.078 -1.695,3.765 -3.782,3.765 C 11.693,3.765 9.997,2.078 9.997,0 9.997,-2.769 7.76,-5.007 4.999,-5.007 2.238,-5.007 0,-2.769 0,0 m 57.05,-23.153 c 0,-2.771 -2.237,-5.007 -4.998,-5.007 l -46.378,0 c -2.761,0 -4.999,2.236 -4.999,5.007 0,2.769 2.238,5.007 4.999,5.007 l 46.378,0 c 2.761,0 4.998,-2.238 4.998,-5.007 M 35.379,-2.805 c -1.545,2.291 -0.941,5.398 1.35,6.943 l 11.594,7.83 c 2.273,1.58 5.398,0.941 6.943,-1.332 1.545,-2.29 0.941,-5.398 -1.35,-6.943 l -11.594,-7.83 c -0.852,-0.586 -1.829,-0.87 -2.788,-0.87 -1.607,0 -3.187,0.781 -4.155,2.202 m 31.748,-30.786 c 0,-0.945 -0.376,-1.852 -1.045,-2.522 l -8.617,-8.617 c -0.669,-0.668 -1.576,-1.045 -2.523,-1.045 l -52.833,0 c -0.947,0 -1.854,0.377 -2.523,1.045 l -8.617,8.617 c -0.669,0.67 -1.045,1.577 -1.045,2.522 l 0,52.799 c 0,0.947 0.376,1.854 1.045,2.522 l 8.617,8.619 c 0.669,0.668 1.576,1.044 2.523,1.044 l 52.833,0 c 0.947,0 1.854,-0.376 2.523,-1.044 l 8.617,-8.619 c 0.669,-0.668 1.045,-1.575 1.045,-2.522 l 0,-52.799 z m 7.334,61.086 -11.25,11.25 c -1.705,1.705 -4.018,2.663 -6.428,2.663 l -56.523,0 c -2.412,0 -4.725,-0.959 -6.43,-2.665 L -17.412,27.494 c -1.704,-1.705 -2.661,-4.016 -2.661,-6.427 l 0,-56.515 c 0,-2.411 0.958,-4.725 2.663,-6.428 l 11.25,-11.25 c 1.705,-1.705 4.017,-2.662 6.428,-2.662 l 56.515,0 c 2.41,0 4.723,0.957 6.428,2.662 l 11.25,11.25 c 1.705,1.703 2.663,4.017 2.663,6.428 l 0,56.514 c 0,2.412 -0.958,4.724 -2.663,6.429"
                    /></g></g></g></g></svg>
        </a>
      </div>
    </script>
    <script type="text/x-handlebars-template" id="language-template">
      <button title="{{t 'chooseLanguage'}}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 420 420">
        <path stroke-width="26"
              d="M209,15a195,195 0 1,0 2,0z"/>
        <path stroke-width="18"
              d="m210,15v390m195-195H15M59,90a260,260 0 0,0 302,0 m0,240 a260,260 0 0,0-302,0M195,20a250,250 0 0,0 0,382 m30,0 a250,250 0 0,0 0-382"/>
      </svg>
      </button>
      <ul class="hidden">
        {{#each languages}}
          <li class="{{#ifEquals this selected}}selected{{/ifEquals}}"><a>{{this}}</a></li>
        {{/each}}
      </ul>
    </script>
    <script type="text/x-handlebars-template" id="importing-template">
      <h2 id="Importing">{{t "importing"}}</h2>
      <div class="keywords">
          {{#each inits}}
          <div class="kw-row">
            <div class="kw-overview">
            {{#if this.args.length}}
                <div class="args">
                  <h4>{{t "arguments"}}</h4>
                  <div class="arguments-list-container">
                    <div class="arguments-list">
                    {{#each this.args}}
                      {{> arg }}
                    {{/each}}
                    </div>
                  </div>
                </div>
            {{/if}}
            {{#if this.doc}}
              <div class="kw-docs">
                <h4>{{t "doc"}}</h4>
                <div class="kwdoc doc">{{{this.doc}}}</div>
              </div>
            {{/if}}
          </div>
          {{/each}}
      </div>
    </script>
    <script type="text/x-handlebars-template" id="shortcuts-template">
      <div class="keywords-overview">
        <div class="keyword-search-box">
          <input
            placeholder="{{t 'search'}}"
            type="text"
            class="search-input"
          />
          <button class="clear-search">&#10005;</button>
        </div>
        {{#if tags.length}}
          <select id="tags-shortcuts-container">
          </select>
        {{/if}}
        <div class="keywords-overview-header-row">
          <h4>{{t "keywords"}}
            (<span id="keyword-statistics-header"></span>)
          </h4>
          <button id="toggle-keyword-shortcuts">+</button>
        </div>
        <ul class="shortcuts" id="keyword-shortcuts-container">
        </ul>
      </div>
    </script>
    <script type="text/x-handlebars-template" id="keyword-shortcuts-template">
      {{#each keywords}}
        {{#unless this.hidden}}
          <li>
            <a
              href="#{{encodeURIComponent this.name}}"
              class="match"
              title="{{value.shortdoc}}"
            >{{this.name}}</a>
          </li>
        {{/unless}}
      {{/each}}
      {{#each keywords}}
        {{#if this.hidden}}
          <li>
            <a
              href="#{{encodeURIComponent this.name}}"
              class="no-match"
              title="{{value.shortdoc}}"
            >{{this.name}}</a>
          </li>
        {{/if}}
      {{/each}}
    </script>
    <script type="text/x-handlebars-template" id="keywords-template">
      <h2 id="Keywords">{{t "keywords"}}</h2>
      <div class="keywords">
          {{#each keywords}}
          {{#unless this.hidden}}
          {{>keyword this}}
          {{/unless}}
          {{/each}}
          {{#each keywords}}
          {{#if this.hidden}}
          {{>keyword this}}
          {{/if}}
          {{/each}}
      </div>
    </script>
    <script type="text/x-handlebars-template" id="keyword-template">
      <div class="keyword-container {{#if hidden}}no-{{/if}}match" id="{{name}}">
         <div class="keyword-name">
              <h2>
              <a class="kw-name" href="#{{encodeURIComponent name}}"
                 title="{{t 'kwLink'}}">{{name}}</a>
              </h2>
         </div>
         <div class="keyword-content">
            <div class="kw-overview">
              {{#if args.length}}
                <div class="args">
                  <h4>{{t "arguments"}}</h4>
                  <div class="arguments-list-container">
                    <div class="arguments-list">
                    {{#each args}}
                      {{> arg this}}
                    {{/each}}
                    </div>
                  </div>
                </div>
              {{/if}}
              {{#if returnType}}
              <div class="return-type">
                <h4>{{t "returnType"}}</h4>
                <span class="arg-type">
                {{>typeInfo returnType}}
                </span>
              </div>
              {{/if}}
            </div>
            {{#if tags.length}}
            <div class="tags">
              <h4>{{t "tags"}}</h4>
              <span class="kw-tags">
              {{#each tags}}
                <span class="tag-link"
                  title="Show keywords with this tag">{{this}}</span>{{#unless @last}},<br>{{/unless}}
              {{/each}}
              </span>
            </div>
          {{/if}}
        </div>
            {{#if doc}}
              <div class="kw-docs">
                <h4>{{t "doc"}}</h4>
                <div class="kwdoc doc">{{{doc}}}</div>
              </div>
            {{/if}}
         </div>
      </div>
    </script>
    <script type="text/x-handlebars-template" id="argument-template">
          <span class="arg-name {{#if required}}arg-required{{else}}arg-optional{{/if}}" title="{{t 'argName'}}">
          {{#ifEquals kind "VAR_POSITIONAL"}}<span class="arg-kind" title="{{t 'varArgs'}}">*</span>{{/ifEquals}}
          {{#ifEquals kind "VAR_NAMED"}}<span class="arg-kind" title="{{t 'varNamedArgs'}}">**</span>{{/ifEquals}}
          {{#ifEquals kind "NAMED_ONLY"}}<span class="arg-kind" title="{{t 'namedOnlyArg'}}">&#x1F3F7;</span>{{/ifEquals}}
          {{#ifEquals kind "POSITIONAL_ONLY"}}<span class="arg-kind" title="{{t 'posOnlyArg'}}">&#x27F6;</span>{{/ifEquals}}
          {{name}}
          </span>
        {{#ifNotNull defaultValue}}
        <div class="arg-default-container">
          <span class="arg-default-eq">=</span>
          <span class="arg-default-value" title="{{t 'defaultTitle'}}">{{defaultValue}}</span>
        </div>
      {{/ifNotNull}}

      {{#if type}}
        <span class="arg-type">
            {{> typeInfo type}}
        </span>
      {{/if}}
    </script>
    <script type="text/x-handlebars-template" id="tags-shortcuts-template">
      <option value="" {{#ifEquals selectedTag ""}}selected{{/ifEquals}}>- Show all tags -</option>
      {{#each tags}}
        <option {{#ifEquals ../selectedTag this}}selected{{/ifEquals}}>{{this}}
        </option>
      {{/each}}
    </script>
    <script type="text/x-handlebars-template" id="type-info-template">
      {{~#if union}}
        {{#each nested}}
          {{> typeInfo this}}
          {{#unless @last}}|{{/unless}}
        {{/each}}
      {{else~}}
        {{#if typedoc~}}
          <a style="cursor: pointer;" class="type" data-typedoc={{typedoc}} title="{{t 'typeInfoDialog'}}">{{name}}</a>
        {{~else}}
          <span class="type">{{name}}</span>
        {{/if}}
        {{#if nested.length}}
        [
        {{~#each nested}}
            {{~> typeInfo this}}
            {{~#unless @last}},&nbsp;{{/unless}}
          {{~/each~}}
          ]
        {{/if~}}
      {{~/if~}}
    </script>
    <script type="text/x-handlebars-template" id="data-types-template">
      {{#if typedocs.length}}
        <h2 id="Data types">{{t "dataTypes"}}</h2>
        <div class="data-types">
          {{#each typedocs}}
            {{> dataType this}}
          {{/each}}
        </div>
      {{/if}}
    </script>
    <script type="text/x-handlebars-template" id="data-type-template">
      <div class="data-type-container {{#if hidden}}no-{{/if}}match" id="type-modal-{{name}}">
         <div class="data-type-name">
              <h2>{{name}} ({{type}})</h2>
         </div>
         <div class="data-type-content">
            {{#if doc}}
              <div class="dt-docs">
                <h4>{{t "doc"}}</h4>
                <div class="dtdoc doc">{{{doc}}}</div>
              </div>
            {{/if}}
            {{#if members}}
              <div class="dt-members">
                <h4>{{t "allowedValues"}}</h4>
                <ul class="enum-type-members">
                  {{#each members}}
                  <li>
                    <span class="enum-member">{{this.name}}</span>
                    {{#ifContains ../accepts "integer"}}
                      &nbsp; (<span class="enum-member">{{value.value}}</span>)
                    {{/ifContains}}
                  </li>
                  {{/each}}
                </ul>
              </div>
            {{else}}
              {{#if items}}
              <div class="dt-items">
                <h4>{{t "dictStructure"}}</h4>
                  <div class="typed-dict-annotation">
                  <span class="typed-dict-item">
                    {
                      {{#each items}}<br><span
                    {{#if required}}
                      class="td-item {{#if required}}required-key{{else}}optional-key{{/if}}"
                      title="{{#if required}}required-key{{else}}optional-key{{/if}}"
                    {{else}}
                      class="td-item"
                    {{/if}}
                   >'{{key}}': </span>
                     <span class="td-type">&lt;{{type}}&gt;</span>
                     {{/each}}<br>
                    }</span>
                  </div>
              </div>
              {{/if}}
            {{/if}}
            {{#if accepts.length}}
              <div class="dt-docs">
                <h4>{{t "convertedTypes"}}</h4>
                <ul class="dt-usages-list">
                {{#each accepts}}
                  <li>{{this}}</li>
                {{/each}}
                </ul>
              </div>
            {{/if}}
            {{#if usages.length}}
              <div class="dt-usages">
                <h4>{{t "usages"}}</h4>
                <ul class="dt-usages-list">
                  {{#each usages}}
                    <li><a href="#{{encodeURIComponent this}}">{{this}}</a></li>
                  {{/each}}
                </ul>
              </div>
            {{/if}}
         </div>
      </div>
    </script>
    <script type="text/x-handlebars-template" id="footer-template">
      <p class="footer">
        {{t "generatedBy"}}
        <a
          href="http://robotframework.org/robotframework/#built-in-tools"
        >Libdoc</a>
        {{t "on"}}
        {{generated}}.
      </p>
    </script>
    <script type="module">let e;function t(e,t,r,n){Object.defineProperty(e,t,{get:r,set:n,enumerable:!0,configurable:!0})}function r(e){return e&&e.__esModule?e.default:e}var n,o,i,a,s,l,c,u,h,p,d,f,g,m,v,y,k,_,S,b,w,E,x,C,L,P,A,O,I,N,M,T,R,B,D,j,$,H,V,F,q,U,K,G,W,J,z,X,Y,Z,Q,ee,et,er,en,eo,ei,ea,es,el,ec,eu,eh,ep,ed,ef=globalThis,eg={},em={},ev=ef.parcelRequirefba8;null==ev&&((ev=function(e){if(e in eg)return eg[e].exports;if(e in em){var t=em[e];delete em[e];var r={id:e,exports:{}};return eg[e]=r,t.call(r.exports,r,r.exports),r.exports}var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}).register=function(e,t){em[e]=t},ef.parcelRequirefba8=ev);var ey=ev.register;ey("ieWO2",function(e,r){var n,o,i;t(e.exports,"SourceMapGenerator",()=>n,e=>n=e),t(e.exports,"SourceMapConsumer",()=>o,e=>o=e),t(e.exports,"SourceNode",()=>i,e=>i=e),n=ev("i8dtv").SourceMapGenerator,o=ev("3DjxD").SourceMapConsumer,i=ev("76tK5").SourceNode}),ey("i8dtv",function(e,r){t(e.exports,"SourceMapGenerator",()=>n,e=>n=e);var n,o=ev("jTqXJ"),i=ev("hvjlv"),a=ev("7Tyww").ArraySet,s=ev("je4qX").MappingList;function l(e){e||(e={}),this._file=i.getArg(e,"file",null),this._sourceRoot=i.getArg(e,"sourceRoot",null),this._skipValidation=i.getArg(e,"skipValidation",!1),this._sources=new a,this._names=new a,this._mappings=new s,this._sourcesContents=null}l.prototype._version=3,l.fromSourceMap=function(e){var t=e.sourceRoot,r=new l({file:e.file,sourceRoot:t});return e.eachMapping(function(e){var n={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(n.source=e.source,null!=t&&(n.source=i.relative(t,n.source)),n.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(n.name=e.name)),r.addMapping(n)}),e.sources.forEach(function(n){var o=n;null!==t&&(o=i.relative(t,n)),r._sources.has(o)||r._sources.add(o);var a=e.sourceContentFor(n);null!=a&&r.setSourceContent(n,a)}),r},l.prototype.addMapping=function(e){var t=i.getArg(e,"generated"),r=i.getArg(e,"original",null),n=i.getArg(e,"source",null),o=i.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,r,n,o),null==n||(n=String(n),this._sources.has(n)||this._sources.add(n)),null==o||(o=String(o),this._names.has(o)||this._names.add(o)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=r&&r.line,originalColumn:null!=r&&r.column,source:n,name:o})},l.prototype.setSourceContent=function(e,t){var r=e;null!=this._sourceRoot&&(r=i.relative(this._sourceRoot,r)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[i.toSetString(r)]=t):this._sourcesContents&&(delete this._sourcesContents[i.toSetString(r)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},l.prototype.applySourceMap=function(e,t,r){var n=t;if(null==t){if(null==e.file)throw Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');n=e.file}var o=this._sourceRoot;null!=o&&(n=i.relative(o,n));var s=new a,l=new a;this._mappings.unsortedForEach(function(t){if(t.source===n&&null!=t.originalLine){var a=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=a.source&&(t.source=a.source,null!=r&&(t.source=i.join(r,t.source)),null!=o&&(t.source=i.relative(o,t.source)),t.originalLine=a.line,t.originalColumn=a.column,null!=a.name&&(t.name=a.name))}var c=t.source;null==c||s.has(c)||s.add(c);var u=t.name;null==u||l.has(u)||l.add(u)},this),this._sources=s,this._names=l,e.sources.forEach(function(t){var n=e.sourceContentFor(t);null!=n&&(null!=r&&(t=i.join(r,t)),null!=o&&(t=i.relative(o,t)),this.setSourceContent(t,n))},this)},l.prototype._validateMapping=function(e,t,r,n){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!e||!("line"in e)||!("column"in e)||!(e.line>0)||!(e.column>=0)||t||r||n)&&(!e||!("line"in e)||!("column"in e)||!t||!("line"in t)||!("column"in t)||!(e.line>0)||!(e.column>=0)||!(t.line>0)||!(t.column>=0)||!r))throw Error("Invalid mapping: "+JSON.stringify({generated:e,source:r,original:t,name:n}))},l.prototype._serializeMappings=function(){for(var e,t,r,n,a=0,s=1,l=0,c=0,u=0,h=0,p="",d=this._mappings.toArray(),f=0,g=d.length;f<g;f++){if(t=d[f],e="",t.generatedLine!==s)for(a=0;t.generatedLine!==s;)e+=";",s++;else if(f>0){if(!i.compareByGeneratedPositionsInflated(t,d[f-1]))continue;e+=","}e+=o.encode(t.generatedColumn-a),a=t.generatedColumn,null!=t.source&&(n=this._sources.indexOf(t.source),e+=o.encode(n-h),h=n,e+=o.encode(t.originalLine-1-c),c=t.originalLine-1,e+=o.encode(t.originalColumn-l),l=t.originalColumn,null!=t.name&&(r=this._names.indexOf(t.name),e+=o.encode(r-u),u=r)),p+=e}return p},l.prototype._generateSourcesContent=function(e,t){return e.map(function(e){if(!this._sourcesContents)return null;null!=t&&(e=i.relative(t,e));var r=i.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,r)?this._sourcesContents[r]:null},this)},l.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},l.prototype.toString=function(){return JSON.stringify(this.toJSON())},n=l}),ey("jTqXJ",function(e,r){t(e.exports,"encode",()=>n,e=>n=e),t(e.exports,"decode",()=>o,e=>o=e);var n,o,i=ev("Q1Wfs");n=function(e){var t,r="",n=e<0?(-e<<1)+1:(e<<1)+0;do t=31&n,(n>>>=5)>0&&(t|=32),r+=i.encode(t);while(n>0)return r},o=function(e,t,r){var n,o,a,s,l=e.length,c=0,u=0;do{if(t>=l)throw Error("Expected more digits in base 64 VLQ value.");if(-1===(s=i.decode(e.charCodeAt(t++))))throw Error("Invalid base64 digit: "+e.charAt(t-1));a=!!(32&s),s&=31,c+=s<<u,u+=5}while(a)r.value=(o=(n=c)>>1,(1&n)==1?-o:o),r.rest=t}}),ey("Q1Wfs",function(e,r){t(e.exports,"encode",()=>n,e=>n=e),t(e.exports,"decode",()=>o,e=>o=e);var n,o,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n=function(e){if(0<=e&&e<i.length)return i[e];throw TypeError("Must be between 0 and 63: "+e)},o=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}}),ey("hvjlv",function(e,r){t(e.exports,"getArg",()=>n,e=>n=e),t(e.exports,"urlParse",()=>o,e=>o=e),t(e.exports,"isAbsolute",()=>s,e=>s=e),t(e.exports,"normalize",()=>i,e=>i=e),t(e.exports,"join",()=>a,e=>a=e),t(e.exports,"relative",()=>l,e=>l=e),t(e.exports,"toSetString",()=>c,e=>c=e),t(e.exports,"fromSetString",()=>u,e=>u=e),t(e.exports,"compareByOriginalPositions",()=>h,e=>h=e),t(e.exports,"compareByGeneratedPositionsDeflated",()=>p,e=>p=e),t(e.exports,"compareByGeneratedPositionsInflated",()=>d,e=>d=e),t(e.exports,"parseSourceMapInput",()=>f,e=>f=e),t(e.exports,"computeSourceURL",()=>g,e=>g=e),n=function(e,t,r){if(t in e)return e[t];if(3==arguments.length)return r;throw Error('"'+t+'" is a required argument.')};var n,o,i,a,s,l,c,u,h,p,d,f,g,m=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,v=/^data:.+\,.+$/;function y(e){var t=e.match(m);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function k(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function _(e){var t=e,r=y(e);if(r){if(!r.path)return e;t=r.path}for(var n,o=s(t),i=t.split(/\/+/),a=0,l=i.length-1;l>=0;l--)"."===(n=i[l])?i.splice(l,1):".."===n?a++:a>0&&(""===n?(i.splice(l+1,a),a=0):(i.splice(l,2),a--));return(""===(t=i.join("/"))&&(t=o?"/":"."),r)?(r.path=t,k(r)):t}function S(e,t){""===e&&(e="."),""===t&&(t=".");var r=y(t),n=y(e);if(n&&(e=n.path||"/"),r&&!r.scheme)return n&&(r.scheme=n.scheme),k(r);if(r||t.match(v))return t;if(n&&!n.host&&!n.path)return n.host=t,k(n);var o="/"===t.charAt(0)?t:_(e.replace(/\/+$/,"")+"/"+t);return n?(n.path=o,k(n)):o}o=y,i=_,a=S,s=function(e){return"/"===e.charAt(0)||m.test(e)},l=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var r=0;0!==t.indexOf(e+"/");){var n=e.lastIndexOf("/");if(n<0||(e=e.slice(0,n)).match(/^([^\/]+:\/)?\/*$/))return t;++r}return Array(r+1).join("../")+t.substr(e.length+1)};var b=!("__proto__"in Object.create(null));function w(e){return e}function E(e){if(!e)return!1;var t=e.length;if(t<9||95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var r=t-10;r>=0;r--)if(36!==e.charCodeAt(r))return!1;return!0}function x(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}c=b?w:function(e){return E(e)?"$"+e:e},u=b?w:function(e){return E(e)?e.slice(1):e},h=function(e,t,r){var n=x(e.source,t.source);return 0!==n||0!=(n=e.originalLine-t.originalLine)||0!=(n=e.originalColumn-t.originalColumn)||r||0!=(n=e.generatedColumn-t.generatedColumn)||0!=(n=e.generatedLine-t.generatedLine)?n:x(e.name,t.name)},p=function(e,t,r){var n=e.generatedLine-t.generatedLine;return 0!==n||0!=(n=e.generatedColumn-t.generatedColumn)||r||0!==(n=x(e.source,t.source))||0!=(n=e.originalLine-t.originalLine)||0!=(n=e.originalColumn-t.originalColumn)?n:x(e.name,t.name)},d=function(e,t){var r=e.generatedLine-t.generatedLine;return 0!==r||0!=(r=e.generatedColumn-t.generatedColumn)||0!==(r=x(e.source,t.source))||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)?r:x(e.name,t.name)},f=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},g=function(e,t,r){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),r){var n=y(r);if(!n)throw Error("sourceMapURL could not be parsed");if(n.path){var o=n.path.lastIndexOf("/");o>=0&&(n.path=n.path.substring(0,o+1))}t=S(k(n),t)}return _(t)}}),ey("7Tyww",function(e,r){t(e.exports,"ArraySet",()=>n,e=>n=e);var n,o=ev("hvjlv"),i=Object.prototype.hasOwnProperty,a="undefined"!=typeof Map;function s(){this._array=[],this._set=a?new Map:Object.create(null)}s.fromArray=function(e,t){for(var r=new s,n=0,o=e.length;n<o;n++)r.add(e[n],t);return r},s.prototype.size=function(){return a?this._set.size:Object.getOwnPropertyNames(this._set).length},s.prototype.add=function(e,t){var r=a?e:o.toSetString(e),n=a?this.has(e):i.call(this._set,r),s=this._array.length;(!n||t)&&this._array.push(e),n||(a?this._set.set(e,s):this._set[r]=s)},s.prototype.has=function(e){if(a)return this._set.has(e);var t=o.toSetString(e);return i.call(this._set,t)},s.prototype.indexOf=function(e){if(a){var t=this._set.get(e);if(t>=0)return t}else{var r=o.toSetString(e);if(i.call(this._set,r))return this._set[r]}throw Error('"'+e+'" is not in the set.')},s.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw Error("No element indexed by "+e)},s.prototype.toArray=function(){return this._array.slice()},n=s}),ey("je4qX",function(e,r){t(e.exports,"MappingList",()=>n,e=>n=e);var n,o=ev("hvjlv");function i(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}i.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},i.prototype.add=function(e){var t,r,n,i,a;(r=(t=this._last).generatedLine,n=e.generatedLine,i=t.generatedColumn,a=e.generatedColumn,n>r||n==r&&a>=i||0>=o.compareByGeneratedPositionsInflated(t,e))?this._last=e:this._sorted=!1,this._array.push(e)},i.prototype.toArray=function(){return this._sorted||(this._array.sort(o.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},n=i}),ey("3DjxD",function(e,r){t(e.exports,"SourceMapConsumer",()=>n,e=>n=e);var n,o=ev("hvjlv"),i=ev("4khg5"),a=ev("7Tyww").ArraySet,s=ev("jTqXJ"),l=ev("db1rV").quickSort;function c(e,t){var r=e;return"string"==typeof e&&(r=o.parseSourceMapInput(e)),null!=r.sections?new p(r,t):new u(r,t)}function u(e,t){var r=e;"string"==typeof e&&(r=o.parseSourceMapInput(e));var n=o.getArg(r,"version"),i=o.getArg(r,"sources"),s=o.getArg(r,"names",[]),l=o.getArg(r,"sourceRoot",null),c=o.getArg(r,"sourcesContent",null),u=o.getArg(r,"mappings"),h=o.getArg(r,"file",null);if(n!=this._version)throw Error("Unsupported version: "+n);l&&(l=o.normalize(l)),i=i.map(String).map(o.normalize).map(function(e){return l&&o.isAbsolute(l)&&o.isAbsolute(e)?o.relative(l,e):e}),this._names=a.fromArray(s.map(String),!0),this._sources=a.fromArray(i,!0),this._absoluteSources=this._sources.toArray().map(function(e){return o.computeSourceURL(l,e,t)}),this.sourceRoot=l,this.sourcesContent=c,this._mappings=u,this._sourceMapURL=t,this.file=h}function h(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function p(e,t){var r=e;"string"==typeof e&&(r=o.parseSourceMapInput(e));var n=o.getArg(r,"version"),i=o.getArg(r,"sections");if(n!=this._version)throw Error("Unsupported version: "+n);this._sources=new a,this._names=new a;var s={line:-1,column:0};this._sections=i.map(function(e){if(e.url)throw Error("Support for url field in sections not implemented.");var r=o.getArg(e,"offset"),n=o.getArg(r,"line"),i=o.getArg(r,"column");if(n<s.line||n===s.line&&i<s.column)throw Error("Section offsets must be ordered and non-overlapping.");return s=r,{generatedOffset:{generatedLine:n+1,generatedColumn:i+1},consumer:new c(o.getArg(e,"map"),t)}})}c.fromSourceMap=function(e,t){return u.fromSourceMap(e,t)},c.prototype._version=3,c.prototype.__generatedMappings=null,Object.defineProperty(c.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),c.prototype.__originalMappings=null,Object.defineProperty(c.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),c.prototype._charIsMappingSeparator=function(e,t){var r=e.charAt(t);return";"===r||","===r},c.prototype._parseMappings=function(e,t){throw Error("Subclasses must implement _parseMappings")},c.GENERATED_ORDER=1,c.ORIGINAL_ORDER=2,c.GREATEST_LOWER_BOUND=1,c.LEAST_UPPER_BOUND=2,c.prototype.eachMapping=function(e,t,r){switch(r||c.GENERATED_ORDER){case c.GENERATED_ORDER:n=this._generatedMappings;break;case c.ORIGINAL_ORDER:n=this._originalMappings;break;default:throw Error("Unknown order of iteration.")}var n,i=this.sourceRoot;n.map(function(e){var t=null===e.source?null:this._sources.at(e.source);return{source:t=o.computeSourceURL(i,t,this._sourceMapURL),generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,t||null)},c.prototype.allGeneratedPositionsFor=function(e){var t=o.getArg(e,"line"),r={source:o.getArg(e,"source"),originalLine:t,originalColumn:o.getArg(e,"column",0)};if(r.source=this._findSourceIndex(r.source),r.source<0)return[];var n=[],a=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",o.compareByOriginalPositions,i.LEAST_UPPER_BOUND);if(a>=0){var s=this._originalMappings[a];if(void 0===e.column)for(var l=s.originalLine;s&&s.originalLine===l;)n.push({line:o.getArg(s,"generatedLine",null),column:o.getArg(s,"generatedColumn",null),lastColumn:o.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++a];else for(var c=s.originalColumn;s&&s.originalLine===t&&s.originalColumn==c;)n.push({line:o.getArg(s,"generatedLine",null),column:o.getArg(s,"generatedColumn",null),lastColumn:o.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++a]}return n},n=c,u.prototype=Object.create(c.prototype),u.prototype.consumer=c,u.prototype._findSourceIndex=function(e){var t,r=e;if(null!=this.sourceRoot&&(r=o.relative(this.sourceRoot,r)),this._sources.has(r))return this._sources.indexOf(r);for(t=0;t<this._absoluteSources.length;++t)if(this._absoluteSources[t]==e)return t;return -1},u.fromSourceMap=function(e,t){var r=Object.create(u.prototype),n=r._names=a.fromArray(e._names.toArray(),!0),i=r._sources=a.fromArray(e._sources.toArray(),!0);r.sourceRoot=e._sourceRoot,r.sourcesContent=e._generateSourcesContent(r._sources.toArray(),r.sourceRoot),r.file=e._file,r._sourceMapURL=t,r._absoluteSources=r._sources.toArray().map(function(e){return o.computeSourceURL(r.sourceRoot,e,t)});for(var s=e._mappings.toArray().slice(),c=r.__generatedMappings=[],p=r.__originalMappings=[],d=0,f=s.length;d<f;d++){var g=s[d],m=new h;m.generatedLine=g.generatedLine,m.generatedColumn=g.generatedColumn,g.source&&(m.source=i.indexOf(g.source),m.originalLine=g.originalLine,m.originalColumn=g.originalColumn,g.name&&(m.name=n.indexOf(g.name)),p.push(m)),c.push(m)}return l(r.__originalMappings,o.compareByOriginalPositions),r},u.prototype._version=3,Object.defineProperty(u.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),u.prototype._parseMappings=function(e,t){for(var r,n,i,a,c,u=1,p=0,d=0,f=0,g=0,m=0,v=e.length,y=0,k={},_={},S=[],b=[];y<v;)if(";"===e.charAt(y))u++,y++,p=0;else if(","===e.charAt(y))y++;else{for((r=new h).generatedLine=u,a=y;a<v&&!this._charIsMappingSeparator(e,a);a++);if(i=k[n=e.slice(y,a)])y+=n.length;else{for(i=[];y<a;)s.decode(e,y,_),c=_.value,y=_.rest,i.push(c);if(2===i.length)throw Error("Found a source, but no line and column");if(3===i.length)throw Error("Found a source and line, but no column");k[n]=i}r.generatedColumn=p+i[0],p=r.generatedColumn,i.length>1&&(r.source=g+i[1],g+=i[1],r.originalLine=d+i[2],d=r.originalLine,r.originalLine+=1,r.originalColumn=f+i[3],f=r.originalColumn,i.length>4&&(r.name=m+i[4],m+=i[4])),b.push(r),"number"==typeof r.originalLine&&S.push(r)}l(b,o.compareByGeneratedPositionsDeflated),this.__generatedMappings=b,l(S,o.compareByOriginalPositions),this.__originalMappings=S},u.prototype._findMapping=function(e,t,r,n,o,a){if(e[r]<=0)throw TypeError("Line must be greater than or equal to 1, got "+e[r]);if(e[n]<0)throw TypeError("Column must be greater than or equal to 0, got "+e[n]);return i.search(e,t,o,a)},u.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var r=this._generatedMappings[e+1];if(t.generatedLine===r.generatedLine){t.lastGeneratedColumn=r.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},u.prototype.originalPositionFor=function(e){var t={generatedLine:o.getArg(e,"line"),generatedColumn:o.getArg(e,"column")},r=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",o.compareByGeneratedPositionsDeflated,o.getArg(e,"bias",c.GREATEST_LOWER_BOUND));if(r>=0){var n=this._generatedMappings[r];if(n.generatedLine===t.generatedLine){var i=o.getArg(n,"source",null);null!==i&&(i=this._sources.at(i),i=o.computeSourceURL(this.sourceRoot,i,this._sourceMapURL));var a=o.getArg(n,"name",null);return null!==a&&(a=this._names.at(a)),{source:i,line:o.getArg(n,"originalLine",null),column:o.getArg(n,"originalColumn",null),name:a}}}return{source:null,line:null,column:null,name:null}},u.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e})},u.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;var r,n=this._findSourceIndex(e);if(n>=0)return this.sourcesContent[n];var i=e;if(null!=this.sourceRoot&&(i=o.relative(this.sourceRoot,i)),null!=this.sourceRoot&&(r=o.urlParse(this.sourceRoot))){var a=i.replace(/^file:\/\//,"");if("file"==r.scheme&&this._sources.has(a))return this.sourcesContent[this._sources.indexOf(a)];if((!r.path||"/"==r.path)&&this._sources.has("/"+i))return this.sourcesContent[this._sources.indexOf("/"+i)]}if(t)return null;throw Error('"'+i+'" is not in the SourceMap.')},u.prototype.generatedPositionFor=function(e){var t=o.getArg(e,"source");if((t=this._findSourceIndex(t))<0)return{line:null,column:null,lastColumn:null};var r={source:t,originalLine:o.getArg(e,"line"),originalColumn:o.getArg(e,"column")},n=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",o.compareByOriginalPositions,o.getArg(e,"bias",c.GREATEST_LOWER_BOUND));if(n>=0){var i=this._originalMappings[n];if(i.source===r.source)return{line:o.getArg(i,"generatedLine",null),column:o.getArg(i,"generatedColumn",null),lastColumn:o.getArg(i,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},p.prototype=Object.create(c.prototype),p.prototype.constructor=c,p.prototype._version=3,Object.defineProperty(p.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var r=0;r<this._sections[t].consumer.sources.length;r++)e.push(this._sections[t].consumer.sources[r]);return e}}),p.prototype.originalPositionFor=function(e){var t={generatedLine:o.getArg(e,"line"),generatedColumn:o.getArg(e,"column")},r=i.search(t,this._sections,function(e,t){return e.generatedLine-t.generatedOffset.generatedLine||e.generatedColumn-t.generatedOffset.generatedColumn}),n=this._sections[r];return n?n.consumer.originalPositionFor({line:t.generatedLine-(n.generatedOffset.generatedLine-1),column:t.generatedColumn-(n.generatedOffset.generatedLine===t.generatedLine?n.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},p.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},p.prototype.sourceContentFor=function(e,t){for(var r=0;r<this._sections.length;r++){var n=this._sections[r].consumer.sourceContentFor(e,!0);if(n)return n}if(t)return null;throw Error('"'+e+'" is not in the SourceMap.')},p.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var r=this._sections[t];if(-1!==r.consumer._findSourceIndex(o.getArg(e,"source"))){var n=r.consumer.generatedPositionFor(e);if(n)return{line:n.line+(r.generatedOffset.generatedLine-1),column:n.column+(r.generatedOffset.generatedLine===n.line?r.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},p.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var r=0;r<this._sections.length;r++)for(var n=this._sections[r],i=n.consumer._generatedMappings,a=0;a<i.length;a++){var s=i[a],c=n.consumer._sources.at(s.source);c=o.computeSourceURL(n.consumer.sourceRoot,c,this._sourceMapURL),this._sources.add(c),c=this._sources.indexOf(c);var u=null;s.name&&(u=n.consumer._names.at(s.name),this._names.add(u),u=this._names.indexOf(u));var h={source:c,generatedLine:s.generatedLine+(n.generatedOffset.generatedLine-1),generatedColumn:s.generatedColumn+(n.generatedOffset.generatedLine===s.generatedLine?n.generatedOffset.generatedColumn-1:0),originalLine:s.originalLine,originalColumn:s.originalColumn,name:u};this.__generatedMappings.push(h),"number"==typeof h.originalLine&&this.__originalMappings.push(h)}l(this.__generatedMappings,o.compareByGeneratedPositionsDeflated),l(this.__originalMappings,o.compareByOriginalPositions)}}),ey("4khg5",function(e,r){var n,o,i;t(e.exports,"GREATEST_LOWER_BOUND",()=>n,e=>n=e),t(e.exports,"LEAST_UPPER_BOUND",()=>o,e=>o=e),t(e.exports,"search",()=>i,e=>i=e),n=1,o=2,i=function(e,t,r,i){if(0===t.length)return -1;var a=function e(t,r,n,i,a,s){var l=Math.floor((r-t)/2)+t,c=a(n,i[l],!0);return 0===c?l:c>0?r-l>1?e(l,r,n,i,a,s):s==o?r<i.length?r:-1:l:l-t>1?e(t,l,n,i,a,s):s==o?l:t<0?-1:t}(-1,t.length,e,t,r,i||n);if(a<0)return -1;for(;a-1>=0&&0===r(t[a],t[a-1],!0);)--a;return a}}),ey("db1rV",function(e,r){var n;function o(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}t(e.exports,"quickSort",()=>n,e=>n=e),n=function(e,t){!function e(t,r,n,i){if(n<i){var a=Math.round(n+Math.random()*(i-n)),s=n-1;o(t,a,i);for(var l=t[i],c=n;c<i;c++)0>=r(t[c],l)&&o(t,s+=1,c);o(t,s+1,c);var u=s+1;e(t,r,n,u-1),e(t,r,u+1,i)}}(e,t,0,e.length-1)}}),ey("76tK5",function(e,r){t(e.exports,"SourceNode",()=>n,e=>n=e);var n,o=ev("i8dtv").SourceMapGenerator,i=ev("hvjlv"),a=/(\r?\n)/,s="$$$isSourceNode$$$";function l(e,t,r,n,o){this.children=[],this.sourceContents={},this.line=null==e?null:e,this.column=null==t?null:t,this.source=null==r?null:r,this.name=null==o?null:o,this[s]=!0,null!=n&&this.add(n)}l.fromStringWithSourceMap=function(e,t,r){var n=new l,o=e.split(a),s=0,c=function(){return e()+(e()||"");function e(){return s<o.length?o[s++]:void 0}},u=1,h=0,p=null;return t.eachMapping(function(e){if(null!==p){if(u<e.generatedLine)d(p,c()),u++,h=0;else{var t=o[s]||"",r=t.substr(0,e.generatedColumn-h);o[s]=t.substr(e.generatedColumn-h),h=e.generatedColumn,d(p,r),p=e;return}}for(;u<e.generatedLine;)n.add(c()),u++;if(h<e.generatedColumn){var t=o[s]||"";n.add(t.substr(0,e.generatedColumn)),o[s]=t.substr(e.generatedColumn),h=e.generatedColumn}p=e},this),s<o.length&&(p&&d(p,c()),n.add(o.splice(s).join(""))),t.sources.forEach(function(e){var o=t.sourceContentFor(e);null!=o&&(null!=r&&(e=i.join(r,e)),n.setSourceContent(e,o))}),n;function d(e,t){if(null===e||void 0===e.source)n.add(t);else{var o=r?i.join(r,e.source):e.source;n.add(new l(e.originalLine,e.originalColumn,o,t,e.name))}}},l.prototype.add=function(e){if(Array.isArray(e))e.forEach(function(e){this.add(e)},this);else if(e[s]||"string"==typeof e)e&&this.children.push(e);else throw TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);return this},l.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else if(e[s]||"string"==typeof e)this.children.unshift(e);else throw TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);return this},l.prototype.walk=function(e){for(var t,r=0,n=this.children.length;r<n;r++)(t=this.children[r])[s]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},l.prototype.join=function(e){var t,r,n=this.children.length;if(n>0){for(r=0,t=[];r<n-1;r++)t.push(this.children[r]),t.push(e);t.push(this.children[r]),this.children=t}return this},l.prototype.replaceRight=function(e,t){var r=this.children[this.children.length-1];return r[s]?r.replaceRight(e,t):"string"==typeof r?this.children[this.children.length-1]=r.replace(e,t):this.children.push("".replace(e,t)),this},l.prototype.setSourceContent=function(e,t){this.sourceContents[i.toSetString(e)]=t},l.prototype.walkSourceContents=function(e){for(var t=0,r=this.children.length;t<r;t++)this.children[t][s]&&this.children[t].walkSourceContents(e);for(var n=Object.keys(this.sourceContents),t=0,r=n.length;t<r;t++)e(i.fromSetString(n[t]),this.sourceContents[n[t]])},l.prototype.toString=function(){var e="";return this.walk(function(t){e+=t}),e},l.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},r=new o(e),n=!1,i=null,a=null,s=null,l=null;return this.walk(function(e,o){t.code+=e,null!==o.source&&null!==o.line&&null!==o.column?((i!==o.source||a!==o.line||s!==o.column||l!==o.name)&&r.addMapping({source:o.source,original:{line:o.line,column:o.column},generated:{line:t.line,column:t.column},name:o.name}),i=o.source,a=o.line,s=o.column,l=o.name,n=!0):n&&(r.addMapping({generated:{line:t.line,column:t.column}}),i=null,n=!1);for(var c=0,u=e.length;c<u;c++)10===e.charCodeAt(c)?(t.line++,t.column=0,c+1===u?(i=null,n=!1):n&&r.addMapping({source:o.source,original:{line:o.line,column:o.column},generated:{line:t.line,column:t.column},name:o.name})):t.column++}),this.walkSourceContents(function(e,t){r.setSourceContent(e,t)}),{code:t.code,map:r}},n=l});var ek=class{constructor(e=""){this.prefix="robot-framework-",e&&(this.prefix+=e+"-"),this.storage=this.getStorage()}getStorage(){try{return localStorage.setItem(this.prefix,this.prefix),localStorage.removeItem(this.prefix),localStorage}catch(e){return{}}}get(e,t){var r=this.storage[this.fullKey(e)];return void 0===r?t:r}set(e,t){this.storage[this.fullKey(e)]=t}fullKey(e){return this.prefix+e}},e_={};e_=JSON.parse('{"en":{"code":"en","intro":"Introduction","libVersion":"Library version","libScope":"Library scope","importing":"Importing","arguments":"Arguments","doc":"Documentation","keywords":"Keywords","tags":"Tags","returnType":"Return Type","kwLink":"Link to this keyword","argName":"Argument name","varArgs":"Variable number of arguments","varNamedArgs":"Variable number of named arguments","namedOnlyArg":"Named only argument","posOnlyArg":"Positional only argument","defaultTitle":"Default value that is used if no value is given","typeInfoDialog":"Click to show type information","search":"Search","dataTypes":"Data types","allowedValues":"Allowed Values","dictStructure":"Dictionary Structure","convertedTypes":"Converted Types","usages":"Usages","generatedBy":"Generated by","on":"on","chooseLanguage":"Choose language"},"fi":{"code":"fi","intro":"Johdanto","libVersion":"Kirjaston versio","libScope":"Kirjaston laajuus","importing":"Käyttöönotto","arguments":"Argumentit","doc":"Dokumentaatio","keywords":"Avainsanat","tags":"Tagit","returnType":"Paluuarvo","kwLink":"Linkki tähän avainsanaan","argName":"Argumentin nimi","varArgs":"Vaihteleva määrä argumentteja","varNamedArgs":"Vaihteleva määrä nimettyjä argumentteja","namedOnlyArg":"Vain nimettyjä argumentteja","posOnlyArg":"Vain positionaalisia argumentteja","defaultTitle":"Oletusarvo, jota käytetään jos arvoa ei anneta","typeInfoDialog":"Näytä tyyppitieto","search":"Etsi","dataTypes":"Datatyypit","allowedValues":"Sallitut arvot","dictStructure":"Sanakirjarakenne","convertedTypes":"Muunnetut tyypit","usages":"Käyttöpaikat","generatedBy":"Luotu","on":"","chooseLanguage":"Valitse kieli"},"fr":{"code":"fr","intro":"Introduction","libVersion":"Version de la bibliothèque","libScope":"Portée de la bibliothèque","importing":"Importation","arguments":"Arguments","doc":"Documentation","keywords":"Mots-clés","tags":"Tags","returnType":"Type de retour","kwLink":"Lien vers ce mot-clé","argName":"Nom de l\'argument","varArgs":"Nombre variable d\'arguments","varNamedArgs":"Nombre variable d\'arguments nommés","namedOnlyArg":"Argument nommé uniquement","posOnlyArg":"Argument positionnel uniquement","defaultTitle":"Valeur par défaut utilisée si aucune valeur n\'est donnée","typeInfoDialog":"Cliquez pour afficher les informations de type","search":"Rechercher","dataTypes":"Types de données","allowedValues":"Valeurs autorisées","dictStructure":"Structure du dictionnaire","convertedTypes":"Types convertis","usages":"Utilisations","generatedBy":"Généré par","on":"le","chooseLanguage":"Choisir la langue"},"it":{"code":"it","intro":"Introduzione","libVersion":"Versione della libreria","libScope":"Ambito della libreria","importing":"Importazione","arguments":"Argomenti","doc":"Documentazione","keywords":"Parole chiave","tags":"Tag","returnType":"Tipo di ritorno","kwLink":"Link a questa parola chiave","argName":"Nome dell\'argomento","varArgs":"Numero variabile di argomenti","varNamedArgs":"Numero variabile di argomenti nominati","namedOnlyArg":"Argomento solo nominato","posOnlyArg":"Argomento solo posizionale","defaultTitle":"Valore predefinito utilizzato se non viene fornito un valore","typeInfoDialog":"Clicca per mostrare le informazioni sul tipo","search":"Cerca","dataTypes":"Tipi di dati","allowedValues":"Valori consentiti","dictStructure":"Struttura del dizionario","convertedTypes":"Tipi convertiti","usages":"Utilizzi","generatedBy":"Generato da","on":"su","chooseLanguage":"Scegli la lingua"},"nl":{"code":"nl","intro":"Introductie","libVersion":"Bibliotheekversie","libScope":"Bibliotheekbereik","importing":"Importeren","arguments":"Parameters","doc":"Documentatie","keywords":"Actiewoorden","tags":"Labels","returnType":"Andwoord type","kwLink":"Link naar actiewoord","argName":"Benoemde parameters","varArgs":"Variabel aantal parameters","varNamedArgs":"Variable aantal benoemde parameters","namedOnlyArg":"Alleen benoemde parameters","posOnlyArg":"Aleen positionele parameters","defaultTitle":"Standaard waarde welke wordt gebruikt als geen waarde is gegeven","typeInfoDialog":"Klik om informatie over dit type te zien","search":"Zoeken","dataTypes":"Datatypen","allowedValues":"Geldige waarden","dictStructure":"Woordenboek Structuur","convertedTypes":"Geconverteerde typen","usages":"Gebruikt in","generatedBy":"Gegenereerd door","on":"op","chooseLanguage":"Kies taal"},"pt-br":{"code":"pt-BR","intro":"Introdução","libVersion":"Versão da Biblioteca","libScope":"Escopo da Biblioteca","importing":"Importação","arguments":"Argumentos","doc":"Documentação","keywords":"Palavras-Chave","tags":"Etiquetas","returnType":"Tipo de Retorno","kwLink":"Ligação para a palavra-chave","argName":"Nome do Argumento","varArgs":"Argumentos em quantidade variável","varNamedArgs":"Argumentos nomeados em quantidade variável","namedOnlyArg":"Apenas argumentos nomeados","posOnlyArg":"Apenas argumentos posicionais","defaultTitle":"Valor padrão que é usado se nenhum tiver sido informado","typeInfoDialog":"Clicar para mostrar informação de tipo","search":"Pesquisar","dataTypes":"Tipos de dados","allowedValues":"Valores permitidos","dictStructure":"Estrutura de Dicionário","convertedTypes":"Tipos Convertidos","usages":"Usos","generatedBy":"Gerado por","on":"ligado","chooseLanguage":"Escolher idioma"},"pt-pt":{"code":"pt-PT","intro":"Introdução","libVersion":"Versão da Biblioteca","libScope":"Âmbito da Biblioteca","importing":"Importação","arguments":"Argumentos","doc":"Documentação","keywords":"Palavras-Chave","tags":"Etiquetas","returnType":"Tipo de Retorno","kwLink":"Ligação para a palavra-chave","argName":"Nome do Argumento","varArgs":"Argumentos em quantidade variável","varNamedArgs":"Argumentos nomeados em quantidade variável","namedOnlyArg":"Apenas argumentos nomeados","posOnlyArg":"Apenas argumentos posicionais","defaultTitle":"Valor por omissão que é usado se nenhum tiver sido dado","typeInfoDialog":"Clicar para mostrar informação de tipo","search":"Procurar","dataTypes":"Tipos de dados","allowedValues":"Valores permitidos","dictStructure":"Estrutura de Dicionário","convertedTypes":"Tipos Convertidos","usages":"Utilização","generatedBy":"Gerado por","on":"ligado","chooseLanguage":"Escolher língua"}}');class eS{constructor(e){this.setLanguage(e)}static getInstance(e){return eS.instance||(eS.instance=new eS(e||"en")),eS.instance}translate(e){return e in this.language?this.language[e]:(console.log("Warning, missing translation for",e),"")}setLanguage(e){if(this.language&&e==this.language.code)return!1;let t=!1;return Object.keys(r(e_)).forEach(n=>{n.toLowerCase()===e.toLowerCase()&&(this.language=r(e_)[n],t=!0)}),t}getLanguageCodes(){return Object.keys(r(e_))}currentLanguage(){return this.language.code}}var eb={};n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},i=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s=function(){function e(t){var r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5e3;o(this,e),this.ctx=t,this.iframes=r,this.exclude=n,this.iframesTimeout=i}return i(e,[{key:"getContexts",value:function(){var e=void 0,t=[];return void 0!==this.ctx&&this.ctx?NodeList.prototype.isPrototypeOf(this.ctx)?e=Array.prototype.slice.call(this.ctx):e=Array.isArray(this.ctx)?this.ctx:"string"==typeof this.ctx?Array.prototype.slice.call(document.querySelectorAll(this.ctx)):[this.ctx]:e=[],e.forEach(function(e){var r=t.filter(function(t){return t.contains(e)}).length>0;-1!==t.indexOf(e)||r||t.push(e)}),t}},{key:"getIframeContents",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},n=void 0;try{var o=e.contentWindow;if(n=o.document,!o||!n)throw Error("iframe inaccessible")}catch(e){r()}n&&t(n)}},{key:"isIframeBlank",value:function(e){var t="about:blank",r=e.getAttribute("src").trim();return e.contentWindow.location.href===t&&r!==t&&r}},{key:"observeIframeLoad",value:function(e,t,r){var n=this,o=!1,i=null,a=function a(){if(!o){o=!0,clearTimeout(i);try{n.isIframeBlank(e)||(e.removeEventListener("load",a),n.getIframeContents(e,t,r))}catch(e){r()}}};e.addEventListener("load",a),i=setTimeout(a,this.iframesTimeout)}},{key:"onIframeReady",value:function(e,t,r){try{"complete"===e.contentWindow.document.readyState?this.isIframeBlank(e)?this.observeIframeLoad(e,t,r):this.getIframeContents(e,t,r):this.observeIframeLoad(e,t,r)}catch(e){r()}}},{key:"waitForIframes",value:function(e,t){var r=this,n=0;this.forEachIframe(e,function(){return!0},function(e){n++,r.waitForIframes(e.querySelector("html"),function(){--n||t()})},function(e){e||t()})}},{key:"forEachIframe",value:function(t,r,n){var o=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){},a=t.querySelectorAll("iframe"),s=a.length,l=0;a=Array.prototype.slice.call(a);var c=function(){--s<=0&&i(l)};s||c(),a.forEach(function(t){e.matches(t,o.exclude)?c():o.onIframeReady(t,function(e){r(t)&&(l++,n(e)),c()},c)})}},{key:"createIterator",value:function(e,t,r){return document.createNodeIterator(e,t,r,!1)}},{key:"createInstanceOnIframe",value:function(t){return new e(t.querySelector("html"),this.iframes)}},{key:"compareNodeIframe",value:function(e,t,r){return!!(e.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_PRECEDING&&(null===t||t.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_FOLLOWING))}},{key:"getIteratorNode",value:function(e){var t=e.previousNode(),r=void 0;return r=null===t?e.nextNode():e.nextNode()&&e.nextNode(),{prevNode:t,node:r}}},{key:"checkIframeFilter",value:function(e,t,r,n){var o=!1,i=!1;return(n.forEach(function(e,t){e.val===r&&(o=t,i=e.handled)}),this.compareNodeIframe(e,t,r))?(!1!==o||i?!1===o||i||(n[o].handled=!0):n.push({val:r,handled:!0}),!0):(!1===o&&n.push({val:r,handled:!1}),!1)}},{key:"handleOpenIframes",value:function(e,t,r,n){var o=this;e.forEach(function(e){e.handled||o.getIframeContents(e.val,function(e){o.createInstanceOnIframe(e).forEachNode(t,r,n)})})}},{key:"iterateThroughNodes",value:function(e,t,r,n,o){for(var i,a=this,s=this.createIterator(t,e,n),l=[],c=[],u=void 0,h=void 0;h=(i=a.getIteratorNode(s)).prevNode,u=i.node;)this.iframes&&this.forEachIframe(t,function(e){return a.checkIframeFilter(u,h,e,l)},function(t){a.createInstanceOnIframe(t).forEachNode(e,function(e){return c.push(e)},n)}),c.push(u);c.forEach(function(e){r(e)}),this.iframes&&this.handleOpenIframes(l,e,r,n),o()}},{key:"forEachNode",value:function(e,t,r){var n=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){},i=this.getContexts(),a=i.length;a||o(),i.forEach(function(i){var s=function(){n.iterateThroughNodes(e,i,t,r,function(){--a<=0&&o()})};n.iframes?n.waitForIframes(i,s):s()})}}],[{key:"matches",value:function(e,t){var r=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector;if(!r)return!1;var n=!1;return("string"==typeof t?[t]:t).every(function(t){return!r.call(e,t)||(n=!0,!1)}),n}}]),e}(),l=function(){function e(t){o(this,e),this.ctx=t,this.ie=!1;var r=window.navigator.userAgent;(r.indexOf("MSIE")>-1||r.indexOf("Trident")>-1)&&(this.ie=!0)}return i(e,[{key:"log",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"debug",r=this.opt.log;this.opt.debug&&(void 0===r?"undefined":n(r))==="object"&&"function"==typeof r[t]&&r[t]("mark.js: "+e)}},{key:"escapeStr",value:function(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}},{key:"createRegExp",value:function(e){return"disabled"!==this.opt.wildcards&&(e=this.setupWildcardsRegExp(e)),e=this.escapeStr(e),Object.keys(this.opt.synonyms).length&&(e=this.createSynonymsRegExp(e)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),this.opt.diacritics&&(e=this.createDiacriticsRegExp(e)),e=this.createMergedBlanksRegExp(e),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.createJoinersRegExp(e)),"disabled"!==this.opt.wildcards&&(e=this.createWildcardsRegExp(e)),e=this.createAccuracyRegExp(e)}},{key:"createSynonymsRegExp",value:function(e){var t=this.opt.synonyms,r=this.opt.caseSensitive?"":"i",n=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(var o in t)if(t.hasOwnProperty(o)){var i=t[o],a="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(o):this.escapeStr(o),s="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(i):this.escapeStr(i);""!==a&&""!==s&&(e=e.replace(RegExp("("+this.escapeStr(a)+"|"+this.escapeStr(s)+")","gm"+r),n+"("+this.processSynomyms(a)+"|"+this.processSynomyms(s)+")"+n))}return e}},{key:"processSynomyms",value:function(e){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),e}},{key:"setupWildcardsRegExp",value:function(e){return(e=e.replace(/(?:\\)*\?/g,function(e){return"\\"===e.charAt(0)?"?":"\x01"})).replace(/(?:\\)*\*/g,function(e){return"\\"===e.charAt(0)?"*":"\x02"})}},{key:"createWildcardsRegExp",value:function(e){var t="withSpaces"===this.opt.wildcards;return e.replace(/\u0001/g,t?"[\\S\\s]?":"\\S?").replace(/\u0002/g,t?"[\\S\\s]*?":"\\S*")}},{key:"setupIgnoreJoinersRegExp",value:function(e){return e.replace(/[^(|)\\]/g,function(e,t,r){var n=r.charAt(t+1);return/[(|)\\]/.test(n)||""===n?e:e+"\0"})}},{key:"createJoinersRegExp",value:function(e){var t=[],r=this.opt.ignorePunctuation;return Array.isArray(r)&&r.length&&t.push(this.escapeStr(r.join(""))),this.opt.ignoreJoiners&&t.push("\\u00ad\\u200b\\u200c\\u200d"),t.length?e.split(/\u0000+/).join("["+t.join("")+"]*"):e}},{key:"createDiacriticsRegExp",value:function(e){var t=this.opt.caseSensitive?"":"i",r=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"],n=[];return e.split("").forEach(function(o){r.every(function(r){if(-1!==r.indexOf(o)){if(n.indexOf(r)>-1)return!1;e=e.replace(RegExp("["+r+"]","gm"+t),"["+r+"]"),n.push(r)}return!0})}),e}},{key:"createMergedBlanksRegExp",value:function(e){return e.replace(/[\s]+/gmi,"[\\s]+")}},{key:"createAccuracyRegExp",value:function(e){var t=this,r=this.opt.accuracy,n="string"==typeof r?r:r.value,o="string"==typeof r?[]:r.limiters,i="";switch(o.forEach(function(e){i+="|"+t.escapeStr(e)}),n){case"partially":default:return"()("+e+")";case"complementary":return"()([^"+(i="\\s"+(i||this.escapeStr("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿")))+"]*"+e+"[^"+i+"]*)";case"exactly":return"(^|\\s"+i+")("+e+")(?=$|\\s"+i+")"}}},{key:"getSeparatedKeywords",value:function(e){var t=this,r=[];return e.forEach(function(e){t.opt.separateWordSearch?e.split(" ").forEach(function(e){e.trim()&&-1===r.indexOf(e)&&r.push(e)}):e.trim()&&-1===r.indexOf(e)&&r.push(e)}),{keywords:r.sort(function(e,t){return t.length-e.length}),length:r.length}}},{key:"isNumeric",value:function(e){return Number(parseFloat(e))==e}},{key:"checkRanges",value:function(e){var t=this;if(!Array.isArray(e)||"[object Object]"!==Object.prototype.toString.call(e[0]))return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(e),[];var r=[],n=0;return e.sort(function(e,t){return e.start-t.start}).forEach(function(e){var o=t.callNoMatchOnInvalidRanges(e,n),i=o.start,a=o.end;o.valid&&(e.start=i,e.length=a-i,r.push(e),n=a)}),r}},{key:"callNoMatchOnInvalidRanges",value:function(e,t){var r=void 0,n=void 0,o=!1;return e&&void 0!==e.start?(n=(r=parseInt(e.start,10))+parseInt(e.length,10),this.isNumeric(e.start)&&this.isNumeric(e.length)&&n-t>0&&n-r>0?o=!0:(this.log("Ignoring invalid or overlapping range: "+JSON.stringify(e)),this.opt.noMatch(e))):(this.log("Ignoring invalid range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:r,end:n,valid:o}}},{key:"checkWhitespaceRanges",value:function(e,t,r){var n=void 0,o=!0,i=r.length,a=t-i,s=parseInt(e.start,10)-a;return(n=(s=s>i?i:s)+parseInt(e.length,10))>i&&(n=i,this.log("End range automatically set to the max value of "+i)),s<0||n-s<0||s>i||n>i?(o=!1,this.log("Invalid range: "+JSON.stringify(e)),this.opt.noMatch(e)):""===r.substring(s,n).replace(/\s+/g,"")&&(o=!1,this.log("Skipping whitespace only range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:s,end:n,valid:o}}},{key:"getTextNodes",value:function(e){var t=this,r="",n=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,function(e){n.push({start:r.length,end:(r+=e.textContent).length,node:e})},function(e){return t.matchesExclude(e.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},function(){e({value:r,nodes:n})})}},{key:"matchesExclude",value:function(e){return s.matches(e,this.opt.exclude.concat(["script","style","title","head","html"]))}},{key:"wrapRangeInTextNode",value:function(e,t,r){var n=this.opt.element?this.opt.element:"mark",o=e.splitText(t),i=o.splitText(r-t),a=document.createElement(n);return a.setAttribute("data-markjs","true"),this.opt.className&&a.setAttribute("class",this.opt.className),a.textContent=o.textContent,o.parentNode.replaceChild(a,o),i}},{key:"wrapRangeInMappedTextNode",value:function(e,t,r,n,o){var i=this;e.nodes.every(function(a,s){var l=e.nodes[s+1];if(void 0===l||l.start>t){if(!n(a.node))return!1;var c=t-a.start,u=(r>a.end?a.end:r)-a.start,h=e.value.substr(0,a.start),p=e.value.substr(u+a.start);if(a.node=i.wrapRangeInTextNode(a.node,c,u),e.value=h+p,e.nodes.forEach(function(t,r){r>=s&&(e.nodes[r].start>0&&r!==s&&(e.nodes[r].start-=u),e.nodes[r].end-=u)}),r-=u,o(a.node.previousSibling,a.start),!(r>a.end))return!1;t=a.end}return!0})}},{key:"wrapMatches",value:function(e,t,r,n,o){var i=this,a=0===t?0:t+1;this.getTextNodes(function(t){t.nodes.forEach(function(t){t=t.node;for(var o=void 0;null!==(o=e.exec(t.textContent))&&""!==o[a];)if(r(o[a],t)){var s=o.index;if(0!==a)for(var l=1;l<a;l++)s+=o[l].length;n((t=i.wrapRangeInTextNode(t,s,s+o[a].length)).previousSibling),e.lastIndex=0}}),o()})}},{key:"wrapMatchesAcrossElements",value:function(e,t,r,n,o){var i=this,a=0===t?0:t+1;this.getTextNodes(function(t){for(var s=void 0;null!==(s=e.exec(t.value))&&""!==s[a];){var l=s.index;if(0!==a)for(var c=1;c<a;c++)l+=s[c].length;var u=l+s[a].length;i.wrapRangeInMappedTextNode(t,l,u,function(e){return r(s[a],e)},function(t,r){e.lastIndex=r,n(t)})}o()})}},{key:"wrapRangeFromIndex",value:function(e,t,r,n){var o=this;this.getTextNodes(function(i){var a=i.value.length;e.forEach(function(e,n){var s=o.checkWhitespaceRanges(e,a,i.value),l=s.start,c=s.end;s.valid&&o.wrapRangeInMappedTextNode(i,l,c,function(r){return t(r,e,i.value.substring(l,c),n)},function(t){r(t,e)})}),n()})}},{key:"unwrapMatches",value:function(e){for(var t=e.parentNode,r=document.createDocumentFragment();e.firstChild;)r.appendChild(e.removeChild(e.firstChild));t.replaceChild(r,e),this.ie?this.normalizeTextNode(t):t.normalize()}},{key:"normalizeTextNode",value:function(e){if(e){if(3===e.nodeType)for(;e.nextSibling&&3===e.nextSibling.nodeType;)e.nodeValue+=e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);else this.normalizeTextNode(e.firstChild);this.normalizeTextNode(e.nextSibling)}}},{key:"markRegExp",value:function(e,t){var r=this;this.opt=t,this.log('Searching with expression "'+e+'"');var n=0,o="wrapMatches";this.opt.acrossElements&&(o="wrapMatchesAcrossElements"),this[o](e,this.opt.ignoreGroups,function(e,t){return r.opt.filter(t,e,n)},function(e){n++,r.opt.each(e)},function(){0===n&&r.opt.noMatch(e),r.opt.done(n)})}},{key:"mark",value:function(e,t){var r=this;this.opt=t;var n=0,o="wrapMatches",i=this.getSeparatedKeywords("string"==typeof e?[e]:e),a=i.keywords,s=i.length,l=this.opt.caseSensitive?"":"i";this.opt.acrossElements&&(o="wrapMatchesAcrossElements"),0===s?this.opt.done(n):function e(t){var i=RegExp(r.createRegExp(t),"gm"+l),c=0;r.log('Searching with expression "'+i+'"'),r[o](i,1,function(e,o){return r.opt.filter(o,t,n,c)},function(e){c++,n++,r.opt.each(e)},function(){0===c&&r.opt.noMatch(t),a[s-1]===t?r.opt.done(n):e(a[a.indexOf(t)+1])})}(a[0])}},{key:"markRanges",value:function(e,t){var r=this;this.opt=t;var n=0,o=this.checkRanges(e);o&&o.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(o)),this.wrapRangeFromIndex(o,function(e,t,n,o){return r.opt.filter(e,t,n,o)},function(e,t){n++,r.opt.each(e,t)},function(){r.opt.done(n)})):this.opt.done(n)}},{key:"unmark",value:function(e){var t=this;this.opt=e;var r=this.opt.element?this.opt.element:"*";r+="[data-markjs]",this.opt.className&&(r+="."+this.opt.className),this.log('Removal selector "'+r+'"'),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,function(e){t.unwrapMatches(e)},function(e){var n=s.matches(e,r),o=t.matchesExclude(e);return!n||o?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},this.opt.done)}},{key:"opt",set:function(e){this._opt=a({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:function(){},noMatch:function(){},filter:function(){return!0},done:function(){},debug:!1,log:window.console},e)},get:function(){return this._opt}},{key:"iterator",get:function(){return new s(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}}]),e}(),eb=function(e){var t=this,r=new l(e);return this.mark=function(e,n){return r.mark(e,n),t},this.markRegExp=function(e,n){return r.markRegExp(e,n),t},this.markRanges=function(e,n){return r.markRanges(e,n),t},this.unmark=function(e){return r.unmark(e),t},this};var ew={};function eE(e){return e&&e.__esModule?e:{default:e}}ew.__esModule=!0;var ex={};function eC(e){return e&&e.__esModule?e:{default:e}}function eL(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}ex.__esModule=!0;var eP={};function eA(e){return e&&e.__esModule?e:{default:e}}t(eP,"__esModule",()=>k,e=>k=e),t(eP,"HandlebarsEnvironment",()=>_,e=>_=e),t(eP,"VERSION",()=>S,e=>S=e),t(eP,"COMPILER_REVISION",()=>b,e=>b=e),t(eP,"LAST_COMPATIBLE_COMPILER_REVISION",()=>w,e=>w=e),t(eP,"REVISION_CHANGES",()=>E,e=>E=e),t(eP,"log",()=>x,e=>x=e),t(eP,"createFrame",()=>C,e=>C=e),t(eP,"logger",()=>L,e=>L=e),k=!0,_=ts;var eO={};t(eO,"__esModule",()=>P,e=>P=e),t(eO,"extend",()=>A,e=>A=e),t(eO,"indexOf",()=>O,e=>O=e),t(eO,"escapeExpression",()=>I,e=>I=e),t(eO,"isEmpty",()=>N,e=>N=e),t(eO,"createFrame",()=>M,e=>M=e),t(eO,"blockParams",()=>T,e=>T=e),t(eO,"appendContextPath",()=>R,e=>R=e),t(eO,"toString",()=>B,e=>B=e),t(eO,"isFunction",()=>D,e=>D=e),t(eO,"isArray",()=>j,e=>j=e),P=!0,A=eR,O=function(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return -1},I=function(e){if("string"!=typeof e){if(e&&e.toHTML)return e.toHTML();if(null==e)return"";if(!e)return e+"";e=""+e}return eM.test(e)?e.replace(eN,eT):e},N=function(e){return!e&&0!==e||!!ej(e)&&0===e.length},M=function(e){var t=eR({},e);return t._parent=e,t},T=function(e,t){return e.path=t,e},R=function(e,t){return(e?e+".":"")+t};var eI={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},eN=/[&<>"'`=]/g,eM=/[&<>"'`=]/;function eT(e){return eI[e]}function eR(e){for(var t=1;t<arguments.length;t++)for(var r in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],r)&&(e[r]=arguments[t][r]);return e}var eB=Object.prototype.toString;B=eB;var eD=function(e){return"function"==typeof e};eD(/x/)&&(D=eD=function(e){return"function"==typeof e&&"[object Function]"===eB.call(e)}),D=eD;var ej=Array.isArray||function(e){return!!e&&"object"==typeof e&&"[object Array]"===eB.call(e)};j=ej;var e$={};e$.__esModule=!0;var eH=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function eV(e,t){var r=t&&t.loc,n=void 0,o=void 0,i=void 0,a=void 0;r&&(n=r.start.line,o=r.end.line,i=r.start.column,a=r.end.column,e+=" - "+n+":"+i);for(var s=Error.prototype.constructor.call(this,e),l=0;l<eH.length;l++)this[eH[l]]=s[eH[l]];Error.captureStackTrace&&Error.captureStackTrace(this,eV);try{r&&(this.lineNumber=n,this.endLineNumber=o,Object.defineProperty?(Object.defineProperty(this,"column",{value:i,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:a,enumerable:!0})):(this.column=i,this.endColumn=a))}catch(e){}}eV.prototype=Error(),e$.default=eV;var eF=eA(e$=e$.default);function eq(e){return e&&e.__esModule?e:{default:e}}$=function(e){eK.default(e),eJ.default(e),eY.default(e),e2.default(e),e0.default(e),e4.default(e),e7.default(e)},H=function(e,t,r){e.helpers[t]&&(e.hooks[t]=e.helpers[t],r||delete e.helpers[t])};var eU={};eU.__esModule=!0,eU.default=function(e){e.registerHelper("blockHelperMissing",function(t,r){var n=r.inverse,o=r.fn;if(!0===t)return o(this);if(!1===t||null==t)return n(this);if(j(t))return t.length>0?(r.ids&&(r.ids=[r.name]),e.helpers.each(t,r)):n(this);if(r.data&&r.ids){var i=M(r.data);i.contextPath=R(r.data.contextPath,r.name),r={data:i}}return o(t,r)})};var eK=eq(eU=eU.default),eG={};eG.__esModule=!0;var eW=(c=e$)&&c.__esModule?c:{default:c};eG.default=function(e){e.registerHelper("each",function(e,t){if(!t)throw new eW.default("Must pass iterator to #each");var r=t.fn,n=t.inverse,o=0,i="",a=void 0,s=void 0;function l(t,n,o){a&&(a.key=t,a.index=n,a.first=0===n,a.last=!!o,s&&(a.contextPath=s+t)),i+=r(e[t],{data:a,blockParams:T([e[t],t],[s+t,null])})}if(t.data&&t.ids&&(s=R(t.data.contextPath,t.ids[0])+"."),D(e)&&(e=e.call(this)),t.data&&(a=M(t.data)),e&&"object"==typeof e){if(j(e))for(var c,u=e.length;o<u;o++)o in e&&l(o,o,o===e.length-1);else if("function"==typeof Symbol&&e[Symbol.iterator]){for(var h=[],p=e[Symbol.iterator](),d=p.next();!d.done;d=p.next())h.push(d.value);e=h;for(var u=e.length;o<u;o++)l(o,o,o===e.length-1)}else c=void 0,Object.keys(e).forEach(function(e){void 0!==c&&l(c,o-1),c=e,o++}),void 0!==c&&l(c,o-1,!0)}return 0===o&&(i=n(this)),i})};var eJ=eq(eG=eG.default),ez={};ez.__esModule=!0;var eX=(u=e$)&&u.__esModule?u:{default:u};ez.default=function(e){e.registerHelper("helperMissing",function(){if(1!=arguments.length)throw new eX.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})};var eY=eq(ez=ez.default),eZ={};eZ.__esModule=!0;var eQ=(h=e$)&&h.__esModule?h:{default:h};eZ.default=function(e){e.registerHelper("if",function(e,t){if(2!=arguments.length)throw new eQ.default("#if requires exactly one argument");return(D(e)&&(e=e.call(this)),!t.hash.includeZero&&!e||N(e))?t.inverse(this):t.fn(this)}),e.registerHelper("unless",function(t,r){if(2!=arguments.length)throw new eQ.default("#unless requires exactly one argument");return e.helpers.if.call(this,t,{fn:r.inverse,inverse:r.fn,hash:r.hash})})};var e2=eq(eZ=eZ.default),e1={};e1.__esModule=!0,e1.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],r=arguments[arguments.length-1],n=0;n<arguments.length-1;n++)t.push(arguments[n]);var o=1;null!=r.hash.level?o=r.hash.level:r.data&&null!=r.data.level&&(o=r.data.level),t[0]=o,e.log.apply(e,t)})};var e0=eq(e1=e1.default),e3={};e3.__esModule=!0,e3.default=function(e){e.registerHelper("lookup",function(e,t,r){return e?r.lookupProperty(e,t):e})};var e4=eq(e3=e3.default),e8={};e8.__esModule=!0;var e5=(p=e$)&&p.__esModule?p:{default:p};e8.default=function(e){e.registerHelper("with",function(e,t){if(2!=arguments.length)throw new e5.default("#with requires exactly one argument");D(e)&&(e=e.call(this));var r=t.fn;if(N(e))return t.inverse(this);var n=t.data;return t.data&&t.ids&&((n=M(t.data)).contextPath=R(t.data.contextPath,t.ids[0])),r(e,{data:n,blockParams:T([e],[n&&n.contextPath])})})};var e7=eq(e8=e8.default);V=function(e){e9.default(e)};var e6={};e6.__esModule=!0,e6.default=function(e){e.registerDecorator("inline",function(e,t,r,n){var o=e;return t.partials||(t.partials={},o=function(n,o){var i=r.partials;r.partials=A({},i,t.partials);var a=e(n,o);return r.partials=i,a}),t.partials[n.args[0]]=n.fn,o})};var e9=(d=e6=e6.default)&&d.__esModule?d:{default:d},te={};te.__esModule=!0;var tt={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(e){if("string"==typeof e){var t=O(tt.methodMap,e.toLowerCase());e=t>=0?t:parseInt(e,10)}return e},log:function(e){if(e=tt.lookupLevel(e),"undefined"!=typeof console&&tt.lookupLevel(tt.level)<=e){var t=tt.methodMap[e];console[t]||(t="log");for(var r=arguments.length,n=Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console[t].apply(console,n)}}};te.default=tt;var tr=eA(te=te.default);F=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var r=Object.create(null);return r.__proto__=!1,{properties:{whitelist:K(r,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:K(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},q=function(e,t,r){return"function"==typeof e?ti(t.methods,r):ti(t.properties,r)},U=function(){Object.keys(to).forEach(function(e){delete to[e]})},K=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return A.apply(void 0,[Object.create(null)].concat(t))};var tn=(f=te)&&f.__esModule?f:{default:f},to=Object.create(null);function ti(e,t){return void 0!==e.whitelist[t]?!0===e.whitelist[t]:void 0!==e.defaultValue?e.defaultValue:(!0!==to[t]&&(to[t]=!0,tn.default.log("error",'Handlebars: Access has been denied to resolve the property "'+t+'" because it is not an "own property" of its parent.\nYou can add a runtime option to disable the check or this warning:\nSee https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details')),!1)}S="4.7.8",b=8,w=7,E={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var ta="[object Object]";function ts(e,t,r){this.helpers=e||{},this.partials=t||{},this.decorators=r||{},$(this),V(this)}ts.prototype={constructor:ts,logger:tr.default,log:tr.default.log,registerHelper:function(e,t){if(B.call(e)===ta){if(t)throw new eF.default("Arg not supported with multiple helpers");A(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(B.call(e)===ta)A(this.partials,e);else{if(void 0===t)throw new eF.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(B.call(e)===ta){if(t)throw new eF.default("Arg not supported with multiple decorators");A(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){U()}},x=tr.default.log,C=M,L=tr.default;var tl=eL(eP),tc={};function tu(e){this.string=e}tc.__esModule=!0,tu.prototype.toString=tu.prototype.toHTML=function(){return""+this.string},tc.default=tu;var th=eC(tc=tc.default),tp=eC(e$),td=eL(eO),tf={};t(tf,"__esModule",()=>G,e=>G=e),t(tf,"checkRevision",()=>W,e=>W=e),t(tf,"template",()=>J,e=>J=e),t(tf,"wrapProgram",()=>z,e=>z=e),t(tf,"resolvePartial",()=>X,e=>X=e),t(tf,"invokePartial",()=>Y,e=>Y=e),t(tf,"noop",()=>Z,e=>Z=e),G=!0,W=function(e){var t=e&&e[0]||1,r=b;if(!(t>=w)||!(t<=b)){if(t<w){var n=E[r],o=E[t];throw new tm.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+n+") or downgrade your runtime to an older version ("+o+").")}throw new tm.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},J=function(e,t){if(!t)throw new tm.default("No environment passed to template");if(!e||!e.main)throw new tm.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var r=e.compiler&&7===e.compiler[0],n={strict:function(e,t,r){if(!e||!(t in e))throw new tm.default('"'+t+'" not defined in '+e,{loc:r});return n.lookupProperty(e,t)},lookupProperty:function(e,t){var r=e[t];if(null==r||Object.prototype.hasOwnProperty.call(e,t)||q(r,n.protoAccessControl,t))return r},lookup:function(e,t){for(var r=e.length,o=0;o<r;o++)if(null!=(e[o]&&n.lookupProperty(e[o],t)))return e[o][t]},lambda:function(e,t){return"function"==typeof e?e.call(t):e},escapeExpression:tg.escapeExpression,invokePartial:function(r,n,o){o.hash&&(n=tg.extend({},n,o.hash),o.ids&&(o.ids[0]=!0)),r=t.VM.resolvePartial.call(this,r,n,o);var i=tg.extend({},o,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),a=t.VM.invokePartial.call(this,r,n,i);if(null==a&&t.compile&&(o.partials[o.name]=t.compile(r,e.compilerOptions,t),a=o.partials[o.name](n,i)),null!=a){if(o.indent){for(var s=a.split("\n"),l=0,c=s.length;l<c&&(s[l]||l+1!==c);l++)s[l]=o.indent+s[l];a=s.join("\n")}return a}throw new tm.default("The partial "+o.name+" could not be compiled when running in runtime-only mode")},fn:function(t){var r=e[t];return r.decorator=e[t+"_d"],r},programs:[],program:function(e,t,r,n,o){var i=this.programs[e],a=this.fn(e);return t||o||n||r?i=tv(this,e,a,t,r,n,o):i||(i=this.programs[e]=tv(this,e,a)),i},data:function(e,t){for(;e&&t--;)e=e._parent;return e},mergeIfNeeded:function(e,t){var r=e||t;return e&&t&&e!==t&&(r=tg.extend({},t,e)),r},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function o(t){var r,i=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],a=i.data;o._setup(i),!i.partial&&e.useData&&((r=a)&&"root"in r||((r=r?C(r):{}).root=t),a=r);var s=void 0,l=e.useBlockParams?[]:void 0;function c(t){return""+e.main(n,t,n.helpers,n.partials,a,l,s)}return e.useDepths&&(s=i.depths?t!=i.depths[0]?[t].concat(i.depths):i.depths:[t]),(c=tk(e.main,c,n,i.depths||[],a,l))(t,i)}return o.isTop=!0,o._setup=function(o){if(o.partial)n.protoAccessControl=o.protoAccessControl,n.helpers=o.helpers,n.partials=o.partials,n.decorators=o.decorators,n.hooks=o.hooks;else{var i=tg.extend({},t.helpers,o.helpers);(function(e,t){Object.keys(e).forEach(function(r){var n,o=e[r];e[r]=(n=t.lookupProperty,Q(o,function(e){return tg.extend({lookupProperty:n},e)}))})})(i,n),n.helpers=i,e.usePartial&&(n.partials=n.mergeIfNeeded(o.partials,t.partials)),(e.usePartial||e.useDecorators)&&(n.decorators=tg.extend({},t.decorators,o.decorators)),n.hooks={},n.protoAccessControl=F(o);var a=o.allowCallsToHelperMissing||r;H(n,"helperMissing",a),H(n,"blockHelperMissing",a)}},o._child=function(t,r,o,i){if(e.useBlockParams&&!o)throw new tm.default("must pass block params");if(e.useDepths&&!i)throw new tm.default("must pass parent depths");return tv(n,t,e[t],r,0,o,i)},o},z=tv,X=function(e,t,r){return e?e.call||r.name||(r.name=e,e=r.partials[e]):e="@partial-block"===r.name?r.data["partial-block"]:r.partials[r.name],e},Y=function(e,t,r){var n,o=r.data&&r.data["partial-block"];r.partial=!0,r.ids&&(r.data.contextPath=r.ids[0]||r.data.contextPath);var i=void 0;if(r.fn&&r.fn!==ty&&(r.data=C(r.data),n=r.fn,i=r.data["partial-block"]=function(e){var t=arguments.length<=1||void 0===arguments[1]?{}:arguments[1];return t.data=C(t.data),t.data["partial-block"]=o,n(e,t)},n.partials&&(r.partials=tg.extend({},r.partials,n.partials))),void 0===e&&i&&(e=i),void 0===e)throw new tm.default("The partial "+r.name+" could not be found");if(e instanceof Function)return e(t,r)},Z=ty;var tg=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(eO),tm=(g=e$)&&g.__esModule?g:{default:g};function tv(e,t,r,n,o,i,a){function s(t){var o=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],s=a;return a&&t!=a[0]&&!(t===e.nullContext&&null===a[0])&&(s=[t].concat(a)),r(e,t,e.helpers,e.partials,o.data||n,i&&[o.blockParams].concat(i),s)}return(s=tk(r,s,e,a,n,i)).program=t,s.depth=a?a.length:0,s.blockParams=o||0,s}function ty(){return""}function tk(e,t,r,n,o,i){if(e.decorator){var a={};t=e.decorator(t,a,r,n&&n[0],o,i,n),tg.extend(t,a)}return t}Q=function(e,t){return"function"!=typeof e?e:function(){var r=arguments[arguments.length-1];return arguments[arguments.length-1]=t(r),e.apply(this,arguments)}};var t_=eL(tf),tS={};tS.__esModule=!0,tS.default=function(e){"object"!=typeof globalThis&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var t=globalThis.Handlebars;e.noConflict=function(){return globalThis.Handlebars===e&&(globalThis.Handlebars=t),e}};var tb=eC(tS=tS.default);function tw(){var e=new tl.HandlebarsEnvironment;return td.extend(e,tl),e.SafeString=th.default,e.Exception=tp.default,e.Utils=td,e.escapeExpression=td.escapeExpression,e.VM=t_,e.template=function(t){return t_.template(t,e)},e}var tE=tw();tE.create=tw,tb.default(tE),tE.default=tE,ex.default=tE;var tx=eE(ex=ex.default),tC={};tC.__esModule=!0;var tL={helpers:{helperExpression:function(e){return"SubExpression"===e.type||("MustacheStatement"===e.type||"BlockStatement"===e.type)&&!!(e.params&&e.params.length||e.hash)},scopedId:function(e){return/^\.|this\b/.test(e.original)},simpleId:function(e){return 1===e.parts.length&&!tL.helpers.scopedId(e)&&!e.depth}}};tC.default=tL;var tP=eE(tC=tC.default);function tA(e){return e&&e.__esModule?e:{default:e}}var tO={};tO.__esModule=!0;var tI=function(){var e,t={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(e,t,r,n,o,i,a){var s=i.length-1;switch(o){case 1:return i[s-1];case 2:this.$=n.prepareProgram(i[s]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=i[s];break;case 9:this.$={type:"CommentStatement",value:n.stripComment(i[s]),strip:n.stripFlags(i[s],i[s]),loc:n.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:i[s],value:i[s],loc:n.locInfo(this._$)};break;case 11:this.$=n.prepareRawBlock(i[s-2],i[s-1],i[s],this._$);break;case 12:this.$={path:i[s-3],params:i[s-2],hash:i[s-1]};break;case 13:this.$=n.prepareBlock(i[s-3],i[s-2],i[s-1],i[s],!1,this._$);break;case 14:this.$=n.prepareBlock(i[s-3],i[s-2],i[s-1],i[s],!0,this._$);break;case 15:this.$={open:i[s-5],path:i[s-4],params:i[s-3],hash:i[s-2],blockParams:i[s-1],strip:n.stripFlags(i[s-5],i[s])};break;case 16:case 17:this.$={path:i[s-4],params:i[s-3],hash:i[s-2],blockParams:i[s-1],strip:n.stripFlags(i[s-5],i[s])};break;case 18:this.$={strip:n.stripFlags(i[s-1],i[s-1]),program:i[s]};break;case 19:var l=n.prepareBlock(i[s-2],i[s-1],i[s],i[s],!1,this._$),c=n.prepareProgram([l],i[s-1].loc);c.chained=!0,this.$={strip:i[s-2].strip,program:c,chain:!0};break;case 21:this.$={path:i[s-1],strip:n.stripFlags(i[s-2],i[s])};break;case 22:case 23:this.$=n.prepareMustache(i[s-3],i[s-2],i[s-1],i[s-4],n.stripFlags(i[s-4],i[s]),this._$);break;case 24:this.$={type:"PartialStatement",name:i[s-3],params:i[s-2],hash:i[s-1],indent:"",strip:n.stripFlags(i[s-4],i[s]),loc:n.locInfo(this._$)};break;case 25:this.$=n.preparePartialBlock(i[s-2],i[s-1],i[s],this._$);break;case 26:this.$={path:i[s-3],params:i[s-2],hash:i[s-1],strip:n.stripFlags(i[s-4],i[s])};break;case 29:this.$={type:"SubExpression",path:i[s-3],params:i[s-2],hash:i[s-1],loc:n.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:i[s],loc:n.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:n.id(i[s-2]),value:i[s],loc:n.locInfo(this._$)};break;case 32:this.$=n.id(i[s-1]);break;case 35:this.$={type:"StringLiteral",value:i[s],original:i[s],loc:n.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(i[s]),original:Number(i[s]),loc:n.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:"true"===i[s],original:"true"===i[s],loc:n.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:n.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:n.locInfo(this._$)};break;case 42:this.$=n.preparePath(!0,i[s],this._$);break;case 43:this.$=n.preparePath(!1,i[s],this._$);break;case 44:i[s-2].push({part:n.id(i[s]),original:i[s],separator:i[s-1]}),this.$=i[s-2];break;case 45:this.$=[{part:n.id(i[s]),original:i[s]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:i[s-1].push(i[s]);break;case 98:case 100:this.$=[i[s]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(e,t){throw Error(e)},parse:function(e){var t=this,r=[0],n=[null],o=[],i=this.table,a="",s=0,l=0,c=0;this.lexer.setInput(e),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,void 0===this.lexer.yylloc&&(this.lexer.yylloc={});var u=this.lexer.yylloc;o.push(u);var h=this.lexer.options&&this.lexer.options.ranges;"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var p,d,f,g,m,v,y,k,_,S={};;){if(f=r[r.length-1],this.defaultActions[f]?g=this.defaultActions[f]:(null==p&&(p=function(){var e;return"number"!=typeof(e=t.lexer.lex()||1)&&(e=t.symbols_[e]||e),e}()),g=i[f]&&i[f][p]),void 0===g||!g.length||!g[0]){var b="";if(!c){for(v in _=[],i[f])this.terminals_[v]&&v>2&&_.push("'"+this.terminals_[v]+"'");b=this.lexer.showPosition?"Parse error on line "+(s+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+_.join(", ")+", got '"+(this.terminals_[p]||p)+"'":"Parse error on line "+(s+1)+": Unexpected "+(1==p?"end of input":"'"+(this.terminals_[p]||p)+"'"),this.parseError(b,{text:this.lexer.match,token:this.terminals_[p]||p,line:this.lexer.yylineno,loc:u,expected:_})}}if(g[0]instanceof Array&&g.length>1)throw Error("Parse Error: multiple actions possible at state: "+f+", token: "+p);switch(g[0]){case 1:r.push(p),n.push(this.lexer.yytext),o.push(this.lexer.yylloc),r.push(g[1]),p=null,d?(p=d,d=null):(l=this.lexer.yyleng,a=this.lexer.yytext,s=this.lexer.yylineno,u=this.lexer.yylloc,c>0&&c--);break;case 2:if(y=this.productions_[g[1]][1],S.$=n[n.length-y],S._$={first_line:o[o.length-(y||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(y||1)].first_column,last_column:o[o.length-1].last_column},h&&(S._$.range=[o[o.length-(y||1)].range[0],o[o.length-1].range[1]]),void 0!==(m=this.performAction.call(S,a,l,s,this.yy,g[1],n,o)))return m;y&&(r=r.slice(0,-1*y*2),n=n.slice(0,-1*y),o=o.slice(0,-1*y)),r.push(this.productions_[g[1]][0]),n.push(S.$),o.push(S._$),k=i[r[r.length-2]][r[r.length-1]],r.push(k);break;case 3:return!0}}return!0}},r=((e={EOF:1,parseError:function(e,t){if(this.yy.parser)this.yy.parser.parseError(e,t);else throw Error(e)},setInput:function(e){return this._input=e,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,r=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t-1),this.offset-=t;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),r.length-1&&(this.yylineno-=r.length-1);var o=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===n.length?this.yylloc.first_column:0)+n[n.length-r.length].length-r[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[o[0],o[0]+this.yyleng-t]),this},more:function(){return this._more=!0,this},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var e,t,r,n,o,i=this._currentRules(),a=0;a<i.length&&(!(r=this._input.match(this.rules[i[a]]))||t&&!(r[0].length>t[0].length)||(t=r,n=a,this.options.flex));a++);return t?((o=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=o.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:o?o[o.length-1].length-o[o.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],e=this.performAction.call(this,this.yy,this,i[n],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),e)?e:void 0:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return void 0!==e?e:this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(e){this.begin(e)}}).options={},e.performAction=function(e,t,r,n){function o(e,r){return t.yytext=t.yytext.substring(e,t.yyleng-r+e)}switch(r){case 0:if("\\\\"===t.yytext.slice(-2)?(o(0,1),this.begin("mu")):"\\"===t.yytext.slice(-1)?(o(0,1),this.begin("emu")):this.begin("mu"),t.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:if(this.popState(),"raw"===this.conditionStack[this.conditionStack.length-1])return 15;return o(5,9),"END_RAW_BLOCK";case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(t.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return t.yytext=o(1,2).replace(/\\"/g,'"'),80;case 32:return t.yytext=o(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return t.yytext=t.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},e.rules=[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],e.conditions={mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}},e);function n(){this.yy={}}return t.lexer=r,n.prototype=t,t.Parser=n,new n}();tO.default=tI;var tN=tA(tO=tO.default),tM={};tM.__esModule=!0;var tT={};tT.__esModule=!0;var tR=(m=e$)&&m.__esModule?m:{default:m};function tB(){this.parents=[]}function tD(e){this.acceptRequired(e,"path"),this.acceptArray(e.params),this.acceptKey(e,"hash")}function tj(e){tD.call(this,e),this.acceptKey(e,"program"),this.acceptKey(e,"inverse")}function t$(e){this.acceptRequired(e,"name"),this.acceptArray(e.params),this.acceptKey(e,"hash")}tB.prototype={constructor:tB,mutating:!1,acceptKey:function(e,t){var r=this.accept(e[t]);if(this.mutating){if(r&&!tB.prototype[r.type])throw new tR.default('Unexpected node type "'+r.type+'" found when accepting '+t+" on "+e.type);e[t]=r}},acceptRequired:function(e,t){if(this.acceptKey(e,t),!e[t])throw new tR.default(e.type+" requires "+t)},acceptArray:function(e){for(var t=0,r=e.length;t<r;t++)this.acceptKey(e,t),!e[t]&&(e.splice(t,1),t--,r--)},accept:function(e){if(e){if(!this[e.type])throw new tR.default("Unknown type: "+e.type,e);this.current&&this.parents.unshift(this.current),this.current=e;var t=this[e.type](e);if(this.current=this.parents.shift(),!this.mutating||t)return t;if(!1!==t)return e}},Program:function(e){this.acceptArray(e.body)},MustacheStatement:tD,Decorator:tD,BlockStatement:tj,DecoratorBlock:tj,PartialStatement:t$,PartialBlockStatement:function(e){t$.call(this,e),this.acceptKey(e,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:tD,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(e){this.acceptArray(e.pairs)},HashPair:function(e){this.acceptRequired(e,"value")}},tT.default=tB;var tH=(v=tT=tT.default)&&v.__esModule?v:{default:v};function tV(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];this.options=e}function tF(e,t,r){void 0===t&&(t=e.length);var n=e[t-1],o=e[t-2];return n?"ContentStatement"===n.type?(o||!r?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(n.original):void 0:r}function tq(e,t,r){void 0===t&&(t=-1);var n=e[t+1],o=e[t+2];return n?"ContentStatement"===n.type?(o||!r?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(n.original):void 0:r}function tU(e,t,r){var n=e[null==t?0:t+1];if(n&&"ContentStatement"===n.type&&(r||!n.rightStripped)){var o=n.value;n.value=n.value.replace(r?/^\s+/:/^[ \t]*\r?\n?/,""),n.rightStripped=n.value!==o}}function tK(e,t,r){var n=e[null==t?e.length-1:t-1];if(n&&"ContentStatement"===n.type&&(r||!n.leftStripped)){var o=n.value;return n.value=n.value.replace(r?/\s+$/:/[ \t]+$/,""),n.leftStripped=n.value!==o,n.leftStripped}}tV.prototype=new tH.default,tV.prototype.Program=function(e){var t=!this.options.ignoreStandalone,r=!this.isRootSeen;this.isRootSeen=!0;for(var n=e.body,o=0,i=n.length;o<i;o++){var a=n[o],s=this.accept(a);if(s){var l=tF(n,o,r),c=tq(n,o,r),u=s.openStandalone&&l,h=s.closeStandalone&&c,p=s.inlineStandalone&&l&&c;s.close&&tU(n,o,!0),s.open&&tK(n,o,!0),t&&p&&(tU(n,o),tK(n,o)&&"PartialStatement"===a.type&&(a.indent=/([ \t]+$)/.exec(n[o-1].original)[1])),t&&u&&(tU((a.program||a.inverse).body),tK(n,o)),t&&h&&(tU(n,o),tK((a.inverse||a.program).body))}}return e},tV.prototype.BlockStatement=tV.prototype.DecoratorBlock=tV.prototype.PartialBlockStatement=function(e){this.accept(e.program),this.accept(e.inverse);var t=e.program||e.inverse,r=e.program&&e.inverse,n=r,o=r;if(r&&r.chained)for(n=r.body[0].program;o.chained;)o=o.body[o.body.length-1].program;var i={open:e.openStrip.open,close:e.closeStrip.close,openStandalone:tq(t.body),closeStandalone:tF((n||t).body)};if(e.openStrip.close&&tU(t.body,null,!0),r){var a=e.inverseStrip;a.open&&tK(t.body,null,!0),a.close&&tU(n.body,null,!0),e.closeStrip.open&&tK(o.body,null,!0),!this.options.ignoreStandalone&&tF(t.body)&&tq(n.body)&&(tK(t.body),tU(n.body))}else e.closeStrip.open&&tK(t.body,null,!0);return i},tV.prototype.Decorator=tV.prototype.MustacheStatement=function(e){return e.strip},tV.prototype.PartialStatement=tV.prototype.CommentStatement=function(e){var t=e.strip||{};return{inlineStandalone:!0,open:t.open,close:t.close}},tM.default=tV;var tG=tA(tM=tM.default),tW={};t(tW,"__esModule",()=>et,e=>et=e),t(tW,"SourceLocation",()=>er,e=>er=e),t(tW,"id",()=>en,e=>en=e),t(tW,"stripFlags",()=>eo,e=>eo=e),t(tW,"stripComment",()=>ei,e=>ei=e),t(tW,"preparePath",()=>ea,e=>ea=e),t(tW,"prepareMustache",()=>es,e=>es=e),t(tW,"prepareRawBlock",()=>el,e=>el=e),t(tW,"prepareBlock",()=>ec,e=>ec=e),t(tW,"prepareProgram",()=>eu,e=>eu=e),t(tW,"preparePartialBlock",()=>eh,e=>eh=e),et=!0,er=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},en=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},eo=function(e,t){return{open:"~"===e.charAt(2),close:"~"===t.charAt(t.length-3)}},ei=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},ea=function(e,t,r){r=this.locInfo(r);for(var n=e?"@":"",o=[],i=0,a=0,s=t.length;a<s;a++){var l=t[a].part,c=t[a].original!==l;if(n+=(t[a].separator||"")+l,c||".."!==l&&"."!==l&&"this"!==l)o.push(l);else{if(o.length>0)throw new tJ.default("Invalid path: "+n,{loc:r});".."===l&&i++}}return{type:"PathExpression",data:e,depth:i,parts:o,original:n,loc:r}},es=function(e,t,r,n,o,i){var a=n.charAt(3)||n.charAt(2);return{type:/\*/.test(n)?"Decorator":"MustacheStatement",path:e,params:t,hash:r,escaped:"{"!==a&&"&"!==a,strip:o,loc:this.locInfo(i)}},el=function(e,t,r,n){tz(e,r);var o={type:"Program",body:t,strip:{},loc:n=this.locInfo(n)};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:o,openStrip:{},inverseStrip:{},closeStrip:{},loc:n}},ec=function(e,t,r,n,o,i){n&&n.path&&tz(e,n);var a=/\*/.test(e.open);t.blockParams=e.blockParams;var s=void 0,l=void 0;if(r){if(a)throw new tJ.default("Unexpected inverse block on decorator",r);r.chain&&(r.program.body[0].closeStrip=n.strip),l=r.strip,s=r.program}return o&&(o=s,s=t,t=o),{type:a?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:s,openStrip:e.strip,inverseStrip:l,closeStrip:n&&n.strip,loc:this.locInfo(i)}},eu=function(e,t){if(!t&&e.length){var r=e[0].loc,n=e[e.length-1].loc;r&&n&&(t={source:r.source,start:{line:r.start.line,column:r.start.column},end:{line:n.end.line,column:n.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},eh=function(e,t,r,n){return tz(e,r),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:r&&r.strip,loc:this.locInfo(n)}};var tJ=(y=e$)&&y.__esModule?y:{default:y};function tz(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var r={loc:e.path.loc};throw new tJ.default(e.path.original+" doesn't match "+t,r)}}var tX=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(tW);ee=tN.default;var tY={};function tZ(e,t){return"Program"===e.type?e:(tN.default.yy=tY,tY.locInfo=function(e){return new tY.SourceLocation(t&&t.srcName,e)},tN.default.parse(e))}function tQ(e,t){var r=tZ(e,t);return new tG.default(t).accept(r)}function t2(e){return e&&e.__esModule?e:{default:e}}A(tY,tX),ep=function(e,t,r){if(null==e||"string"!=typeof e&&"Program"!==e.type)throw new t1.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var n=r.parse(e,t),o=new r.Compiler().compile(n,t);return new r.JavaScriptCompiler().compile(o,t)},ed=function(e,t,r){if(void 0===t&&(t={}),null==e||"string"!=typeof e&&"Program"!==e.type)throw new t1.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=A({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var n=void 0;function o(){var n=r.parse(e,t),o=new r.Compiler().compile(n,t),i=new r.JavaScriptCompiler().compile(o,t,void 0,!0);return r.template(i)}function i(e,t){return n||(n=o()),n.call(this,e,t)}return i._setup=function(e){return n||(n=o()),n._setup(e)},i._child=function(e,t,r,i){return n||(n=o()),n._child(e,t,r,i)},i};var t1=t2(e$),t0=t2(tC),t3=[].slice;function t4(){}function t8(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}t4.prototype={compiler:t4,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var r=0;r<t;r++){var n=this.opcodes[r],o=e.opcodes[r];if(n.opcode!==o.opcode||!function e(t,r){if(t===r)return!0;if(j(t)&&j(r)&&t.length===r.length){for(var n=0;n<t.length;n++)if(!e(t[n],r[n]))return!1;return!0}}(n.args,o.args))return!1}t=this.children.length;for(var r=0;r<t;r++)if(!this.children[r].equals(e.children[r]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=A(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),r=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[r]=t,this.useDepths=this.useDepths||t.useDepths,r},accept:function(e){if(!this[e.type])throw new t1.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,r=t.length,n=0;n<r;n++)this.accept(t[n]);return this.options.blockParams.shift(),this.isSimple=1===r,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){t8(e);var t=e.program,r=e.inverse;t=t&&this.compileProgram(t),r=r&&this.compileProgram(r);var n=this.classifySexpr(e);"helper"===n?this.helperSexpr(e,t,r):"simple"===n?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",r),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,r),this.opcode("pushProgram",t),this.opcode("pushProgram",r),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),r=this.setupFullMustacheParams(e,t,void 0),n=e.path;this.useDecorators=!0,this.opcode("registerDecorator",r.length,n.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var r=e.params;if(r.length>1)throw new t1.default("Unsupported number of partial arguments: "+r.length,e);r.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):r.push({type:"PathExpression",parts:[],depth:0}));var n=e.name.original,o="SubExpression"===e.name.type;o&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var i=e.indent||"";this.options.preventIndent&&i&&(this.opcode("appendContent",i),i=""),this.opcode("invokePartial",o,n,i),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){t8(e);var t=this.classifySexpr(e);"simple"===t?this.simpleSexpr(e):"helper"===t?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,r){var n=e.path,o=n.parts[0];this.opcode("getContext",n.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",r),n.strict=!0,this.accept(n),this.opcode("invokeAmbiguous",o,null!=t||null!=r)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,r){var n=this.setupFullMustacheParams(e,t,r),o=e.path,i=o.parts[0];if(this.options.knownHelpers[i])this.opcode("invokeKnownHelper",n.length,i);else if(this.options.knownHelpersOnly)throw new t1.default("You specified knownHelpersOnly, but used the unknown helper "+i,e);else o.strict=!0,o.falsy=!0,this.accept(o),this.opcode("invokeHelper",n.length,o.original,t0.default.helpers.simpleId(o))},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],r=t0.default.helpers.scopedId(e),n=!e.depth&&!r&&this.blockParamIndex(t);n?this.opcode("lookupBlockParam",n,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,r):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,r=0,n=t.length;for(this.opcode("pushHash");r<n;r++)this.pushParam(t[r].value);for(;r--;)this.opcode("assignToHash",t[r].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:t3.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=t0.default.helpers.simpleId(e.path),r=t&&!!this.blockParamIndex(e.path.parts[0]),n=!r&&t0.default.helpers.helperExpression(e),o=!r&&(n||t);if(o&&!n){var i=e.path.parts[0],a=this.options;a.knownHelpers[i]?n=!0:a.knownHelpersOnly&&(o=!1)}return n?"helper":o?"ambiguous":"simple"},pushParams:function(e){for(var t=0,r=e.length;t<r;t++)this.pushParam(e[t])},pushParam:function(e){var t=null!=e.value?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),"SubExpression"===e.type&&this.accept(e);else{if(this.trackIds){var r=void 0;if(!e.parts||t0.default.helpers.scopedId(e)||e.depth||(r=this.blockParamIndex(e.parts[0])),r){var n=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",r,n)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,r,n){var o=e.params;return this.pushParams(o),this.opcode("pushProgram",t),this.opcode("pushProgram",r),e.hash?this.accept(e.hash):this.opcode("emptyHash",n),o},blockParamIndex:function(e){for(var t=0,r=this.options.blockParams.length;t<r;t++){var n=this.options.blockParams[t],o=n&&O(n,e);if(n&&o>=0)return[t,o]}}};var t5={};function t7(e){return e&&e.__esModule?e:{default:e}}t5.__esModule=!0;var t6=t7(e$),t9={};t9.__esModule=!0;var re=void 0;try{"function"==typeof define&&define.amd||(re=ev("ieWO2").SourceNode)}catch(e){}function rt(e,t,r){if(j(e)){for(var n=[],o=0,i=e.length;o<i;o++)n.push(t.wrap(e[o],r));return n}return"boolean"==typeof e||"number"==typeof e?e+"":e}function rr(e){this.srcFile=e,this.source=[]}re||((re=function(e,t,r,n){this.src="",n&&this.add(n)}).prototype={add:function(e){j(e)&&(e=e.join("")),this.src+=e},prepend:function(e){j(e)&&(e=e.join("")),this.src=e+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),rr.prototype={isEmpty:function(){return!this.source.length},prepend:function(e,t){this.source.unshift(this.wrap(e,t))},push:function(e,t){this.source.push(this.wrap(e,t))},merge:function(){var e=this.empty();return this.each(function(t){e.add(["  ",t,"\n"])}),e},each:function(e){for(var t=0,r=this.source.length;t<r;t++)e(this.source[t])},empty:function(){var e=this.currentLocation||{start:{}};return new re(e.start.line,e.start.column,this.srcFile)},wrap:function(e){var t=arguments.length<=1||void 0===arguments[1]?this.currentLocation||{start:{}}:arguments[1];return e instanceof re?e:(e=rt(e,this,t),new re(t.start.line,t.start.column,this.srcFile,e))},functionCall:function(e,t,r){return r=this.generateList(r),this.wrap([e,t?"."+t+"(":"(",r,")"])},quotedString:function(e){return'"'+(e+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(e){var t=this,r=[];Object.keys(e).forEach(function(n){var o=rt(e[n],t);"undefined"!==o&&r.push([t.quotedString(n),":",o])});var n=this.generateList(r);return n.prepend("{"),n.add("}"),n},generateList:function(e){for(var t=this.empty(),r=0,n=e.length;r<n;r++)r&&t.add(","),t.add(rt(e[r],this));return t},generateArray:function(e){var t=this.generateList(e);return t.prepend("["),t.add("]"),t}},t9.default=rr;var rn=t7(t9=t9.default);function ro(e){this.value=e}function ri(){}ri.prototype={nameLookup:function(e,t){return this.internalNameLookup(e,t)},depthedLookup:function(e){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(e),")"]},compilerInfo:function(){var e=b,t=E[e];return[e,t]},appendToBuffer:function(e,t,r){return(j(e)||(e=[e]),e=this.source.wrap(e,t),this.environment.isSimple)?["return ",e,";"]:r?["buffer += ",e,";"]:(e.appendToBuffer=!0,e)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(e,t){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",e,",",JSON.stringify(t),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(e,t,r,n){this.environment=e,this.options=t,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!n,this.name=this.environment.name,this.isChild=!!r,this.context=r||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(e,t),this.useDepths=this.useDepths||e.useDepths||e.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||e.useBlockParams;var o=e.opcodes,i=void 0,a=void 0,s=void 0,l=void 0;for(s=0,l=o.length;s<l;s++)i=o[s],this.source.currentLocation=i.loc,a=a||i.loc,this[i.opcode].apply(this,i.args);if(this.source.currentLocation=a,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new t6.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),";\n"]),this.decorators.push("return fn;"),n?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend("function(fn, props, container, depth0, data, blockParams, depths) {\n"),this.decorators.push("}\n"),this.decorators=this.decorators.merge()));var c=this.createFunctionContext(n);if(this.isChild)return c;var u={compiler:this.compilerInfo(),main:c};this.decorators&&(u.main_d=this.decorators,u.useDecorators=!0);var h=this.context,p=h.programs,d=h.decorators;for(s=0,l=p.length;s<l;s++)p[s]&&(u[s]=p[s],d[s]&&(u[s+"_d"]=d[s],u.useDecorators=!0));return this.environment.usePartial&&(u.usePartial=!0),this.options.data&&(u.useData=!0),this.useDepths&&(u.useDepths=!0),this.useBlockParams&&(u.useBlockParams=!0),this.options.compat&&(u.compat=!0),n?u.compilerOptions=this.options:(u.compiler=JSON.stringify(u.compiler),this.source.currentLocation={start:{line:1,column:0}},u=this.objectLiteral(u),t.srcName?(u=u.toStringWithSourceMap({file:t.destName})).map=u.map&&u.map.toString():u=u.toString()),u},preamble:function(){this.lastContext=0,this.source=new rn.default(this.options.srcName),this.decorators=new rn.default(this.options.srcName)},createFunctionContext:function(e){var t=this,r="",n=this.stackVars.concat(this.registers.list);n.length>0&&(r+=", "+n.join(", "));var o=0;Object.keys(this.aliases).forEach(function(e){var n=t.aliases[e];n.children&&n.referenceCount>1&&(r+=", alias"+ ++o+"="+e,n.children[0]="alias"+o)}),this.lookupPropertyFunctionIsUsed&&(r+=", "+this.lookupPropertyFunctionVarDeclaration());var i=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&i.push("blockParams"),this.useDepths&&i.push("depths");var a=this.mergeSource(r);return e?(i.push(a),Function.apply(this,i)):this.source.wrap(["function(",i.join(","),") {\n  ",a,"}"])},mergeSource:function(e){var t=this.environment.isSimple,r=!this.forceBuffer,n=void 0,o=void 0,i=void 0,a=void 0;return this.source.each(function(e){e.appendToBuffer?(i?e.prepend("  + "):i=e,a=e):(i&&(o?i.prepend("buffer += "):n=!0,a.add(";"),i=a=void 0),o=!0,t||(r=!1))}),r?i?(i.prepend("return "),a.add(";")):o||this.source.push('return "";'):(e+=", buffer = "+(n?"":this.initializeBuffer()),i?(i.prepend("return buffer + "),a.add(";")):this.source.push("return buffer;")),e&&this.source.prepend("var "+e.substring(2)+(n?"":";\n")),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return"\n      lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    }\n    ".trim()},blockValue:function(e){var t=this.aliasable("container.hooks.blockHelperMissing"),r=[this.contextName(0)];this.setupHelperArgs(e,0,r);var n=this.popStack();r.splice(1,0,n),this.push(this.source.functionCall(t,"call",r))},ambiguousBlockValue:function(){var e=this.aliasable("container.hooks.blockHelperMissing"),t=[this.contextName(0)];this.setupHelperArgs("",0,t,!0),this.flushInline();var r=this.topStack();t.splice(1,0,r),this.pushSource(["if (!",this.lastHelper,") { ",r," = ",this.source.functionCall(e,"call",t),"}"])},appendContent:function(e){this.pendingContent?e=this.pendingContent+e:this.pendingLocation=this.source.currentLocation,this.pendingContent=e},append:function(){if(this.isInline())this.replaceStack(function(e){return[" != null ? ",e,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var e=this.popStack();this.pushSource(["if (",e," != null) { ",this.appendToBuffer(e,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(e){this.lastContext=e},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(e,t,r,n){var o=0;n||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(e[o++])),this.resolvePath("context",e,o,t,r)},lookupBlockParam:function(e,t){this.useBlockParams=!0,this.push(["blockParams[",e[0],"][",e[1],"]"]),this.resolvePath("context",t,1)},lookupData:function(e,t,r){e?this.pushStackLiteral("container.data(data, "+e+")"):this.pushStackLiteral("data"),this.resolvePath("data",t,0,!0,r)},resolvePath:function(e,t,r,n,o){var i=this;if(this.options.strict||this.options.assumeObjects){this.push(function(e,t,r,n,o){var i=t.popStack(),a=r.length;for(e&&a--;n<a;n++)i=t.nameLookup(i,r[n],o);return e?[t.aliasable("container.strict"),"(",i,", ",t.quotedString(r[n]),", ",JSON.stringify(t.source.currentLocation)," )"]:i}(this.options.strict&&o,this,t,r,e));return}for(var a=t.length;r<a;r++)this.replaceStack(function(o){var a=i.nameLookup(o,t[r],e);return n?[" && ",a]:[" != null ? ",a," : ",o]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(e,t){this.pushContext(),this.pushString(t),"SubExpression"!==t&&("string"==typeof e?this.pushString(e):this.pushStackLiteral(e))},emptyHash:function(e){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(e?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var e=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(e.ids)),this.stringParams&&(this.push(this.objectLiteral(e.contexts)),this.push(this.objectLiteral(e.types))),this.push(this.objectLiteral(e.values))},pushString:function(e){this.pushStackLiteral(this.quotedString(e))},pushLiteral:function(e){this.pushStackLiteral(e)},pushProgram:function(e){null!=e?this.pushStackLiteral(this.programExpression(e)):this.pushStackLiteral(null)},registerDecorator:function(e,t){var r=this.nameLookup("decorators",t,"decorator"),n=this.setupHelperArgs(t,e);this.decorators.push(["fn = ",this.decorators.functionCall(r,"",["fn","props","container",n])," || fn;"])},invokeHelper:function(e,t,r){var n=this.popStack(),o=this.setupHelper(e,t),i=[];r&&i.push(o.name),i.push(n),this.options.strict||i.push(this.aliasable("container.hooks.helperMissing"));var a=["(",this.itemsSeparatedBy(i,"||"),")"],s=this.source.functionCall(a,"call",o.callParams);this.push(s)},itemsSeparatedBy:function(e,t){var r=[];r.push(e[0]);for(var n=1;n<e.length;n++)r.push(t,e[n]);return r},invokeKnownHelper:function(e,t){var r=this.setupHelper(e,t);this.push(this.source.functionCall(r.name,"call",r.callParams))},invokeAmbiguous:function(e,t){this.useRegister("helper");var r=this.popStack();this.emptyHash();var n=this.setupHelper(0,e,t),o=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",e,"helper")," || ",r,")"];this.options.strict||(o[0]="(helper = ",o.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",o,n.paramsInit?["),(",n.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",n.callParams)," : helper))"])},invokePartial:function(e,t,r){var n=[],o=this.setupParams(t,1,n);e&&(t=this.popStack(),delete o.name),r&&(o.indent=JSON.stringify(r)),o.helpers="helpers",o.partials="partials",o.decorators="container.decorators",e?n.unshift(t):n.unshift(this.nameLookup("partials",t,"partial")),this.options.compat&&(o.depths="depths"),o=this.objectLiteral(o),n.push(o),this.push(this.source.functionCall("container.invokePartial","",n))},assignToHash:function(e){var t=this.popStack(),r=void 0,n=void 0,o=void 0;this.trackIds&&(o=this.popStack()),this.stringParams&&(n=this.popStack(),r=this.popStack());var i=this.hash;r&&(i.contexts[e]=r),n&&(i.types[e]=n),o&&(i.ids[e]=o),i.values[e]=t},pushId:function(e,t,r){"BlockParam"===e?this.pushStackLiteral("blockParams["+t[0]+"].path["+t[1]+"]"+(r?" + "+JSON.stringify("."+r):"")):"PathExpression"===e?this.pushString(t):"SubExpression"===e?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:ri,compileChildren:function(e,t){for(var r=e.children,n=void 0,o=void 0,i=0,a=r.length;i<a;i++){n=r[i],o=new this.compiler;var s=this.matchExistingProgram(n);if(null==s){this.context.programs.push("");var l=this.context.programs.length;n.index=l,n.name="program"+l,this.context.programs[l]=o.compile(n,t,this.context,!this.precompile),this.context.decorators[l]=o.decorators,this.context.environments[l]=n,this.useDepths=this.useDepths||o.useDepths,this.useBlockParams=this.useBlockParams||o.useBlockParams,n.useDepths=this.useDepths,n.useBlockParams=this.useBlockParams}else n.index=s.index,n.name="program"+s.index,this.useDepths=this.useDepths||s.useDepths,this.useBlockParams=this.useBlockParams||s.useBlockParams}},matchExistingProgram:function(e){for(var t=0,r=this.context.environments.length;t<r;t++){var n=this.context.environments[t];if(n&&n.equals(e))return n}},programExpression:function(e){var t=this.environment.children[e],r=[t.index,"data",t.blockParams];return(this.useBlockParams||this.useDepths)&&r.push("blockParams"),this.useDepths&&r.push("depths"),"container.program("+r.join(", ")+")"},useRegister:function(e){this.registers[e]||(this.registers[e]=!0,this.registers.list.push(e))},push:function(e){return e instanceof ro||(e=this.source.wrap(e)),this.inlineStack.push(e),e},pushStackLiteral:function(e){this.push(new ro(e))},pushSource:function(e){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),e&&this.source.push(e)},replaceStack:function(e){var t=["("],r=void 0,n=void 0,o=void 0;if(!this.isInline())throw new t6.default("replaceStack on non-inline");var i=this.popStack(!0);if(i instanceof ro)t=["(",r=[i.value]],o=!0;else{n=!0;var a=this.incrStack();t=["((",this.push(a)," = ",i,")"],r=this.topStack()}var s=e.call(this,r);o||this.popStack(),n&&this.stackSlot--,this.push(t.concat(s,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var e=this.inlineStack;this.inlineStack=[];for(var t=0,r=e.length;t<r;t++){var n=e[t];if(n instanceof ro)this.compileStack.push(n);else{var o=this.incrStack();this.pushSource([o," = ",n,";"]),this.compileStack.push(o)}}},isInline:function(){return this.inlineStack.length},popStack:function(e){var t=this.isInline(),r=(t?this.inlineStack:this.compileStack).pop();if(!e&&r instanceof ro)return r.value;if(!t){if(!this.stackSlot)throw new t6.default("Invalid stack pop");this.stackSlot--}return r},topStack:function(){var e=this.isInline()?this.inlineStack:this.compileStack,t=e[e.length-1];return t instanceof ro?t.value:t},contextName:function(e){return this.useDepths&&e?"depths["+e+"]":"depth"+e},quotedString:function(e){return this.source.quotedString(e)},objectLiteral:function(e){return this.source.objectLiteral(e)},aliasable:function(e){var t=this.aliases[e];return t?t.referenceCount++:((t=this.aliases[e]=this.source.wrap(e)).aliasable=!0,t.referenceCount=1),t},setupHelper:function(e,t,r){var n=[],o=this.setupHelperArgs(t,e,n,r);return{params:n,paramsInit:o,name:this.nameLookup("helpers",t,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(n)}},setupParams:function(e,t,r){var n={},o=[],i=[],a=[],s=!r,l=void 0;s&&(r=[]),n.name=this.quotedString(e),n.hash=this.popStack(),this.trackIds&&(n.hashIds=this.popStack()),this.stringParams&&(n.hashTypes=this.popStack(),n.hashContexts=this.popStack());var c=this.popStack(),u=this.popStack();(u||c)&&(n.fn=u||"container.noop",n.inverse=c||"container.noop");for(var h=t;h--;)l=this.popStack(),r[h]=l,this.trackIds&&(a[h]=this.popStack()),this.stringParams&&(i[h]=this.popStack(),o[h]=this.popStack());return s&&(n.args=this.source.generateArray(r)),this.trackIds&&(n.ids=this.source.generateArray(a)),this.stringParams&&(n.types=this.source.generateArray(i),n.contexts=this.source.generateArray(o)),this.options.data&&(n.data="data"),this.useBlockParams&&(n.blockParams="blockParams"),n},setupHelperArgs:function(e,t,r,n){var o=this.setupParams(e,t,r);return(o.loc=JSON.stringify(this.source.currentLocation),o=this.objectLiteral(o),n)?(this.useRegister("options"),r.push("options"),["options=",o]):r?(r.push(o),""):o}},function(){for(var e="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),t=ri.RESERVED_WORDS={},r=0,n=e.length;r<n;r++)t[e[r]]=!0}(),ri.isValidJavaScriptVariableName=function(e){return!ri.RESERVED_WORDS[e]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(e)},t5.default=ri;var ra=eE(t5=t5.default),rs=eE(tT),rl=eE(tS),rc=tx.default.create;function ru(){var e=rc();return e.compile=function(t,r){return ed(t,r,e)},e.precompile=function(t,r){return ep(t,r,e)},e.AST=tP.default,e.Compiler=t4,e.JavaScriptCompiler=ra.default,e.Parser=ee,e.parse=tQ,e.parseWithoutProcessing=tZ,e}var rh=ru();function rp(e){let t=document.getElementById("modal-background"),r=document.getElementById("modal"),n=document.getElementById("modal-content");t.classList.add("visible"),r.classList.add("visible"),n.appendChild(e.cloneNode(!0)),document.body.style.overflow="hidden"}function rd(){let e=document.getElementById("modal-background"),t=document.getElementById("modal"),r=document.getElementById("modal-content");e.classList.remove("visible"),t.classList.remove("visible"),document.body.style.overflow="auto",0==window.location.hash.indexOf("#type-")&&history.pushState("",document.title,window.location.pathname),setTimeout(()=>{r.innerHTML=""},200)}rh.create=ru,rl.default(rh),rh.Visitor=rs.default,rh.default=rh,ew.default=rh,ew=ew.default;const rf=function(t,r){clearTimeout(e),e=setTimeout(t,r)};var rg=class{constructor(e,t,r){this.libdoc=e,this.storage=t,this.translations=r,this.initTemplating(r)}initTemplating(e){r(ew).registerHelper("t",function(t){return e.translate(t)}),r(ew).registerHelper("encodeURIComponent",function(e){return encodeURIComponent(e)}),r(ew).registerHelper("ifEquals",function(e,t,r){return e==t?r.fn(this):r.inverse(this)}),r(ew).registerHelper("ifNotNull",function(e,t){return null!==e?t.fn(this):t.inverse(this)}),r(ew).registerHelper("ifContains",function(e,t,r){return -1!=e.indexOf(t)?r.fn(this):r.inverse(this)}),this.registerPartial("arg","argument-template"),this.registerPartial("typeInfo","type-info-template"),this.registerPartial("keyword","keyword-template"),this.registerPartial("dataType","data-type-template")}registerPartial(e,t){let n=document.getElementById(t)?.innerHTML;r(ew).registerPartial(e,r(ew).compile(n))}render(){document.title=this.libdoc.name,this.setTheme(),this.renderTemplates(),this.initTagSearch(),this.initHashEvents(),this.initLanguageMenu(),setTimeout(()=>{"open"===this.storage.get("keyword-wall")&&this.openKeywordWall()},0),function(){let e=document.createElement("div");e.id="modal-background",e.classList.add("modal-background"),e.addEventListener("click",({target:e})=>{e?.id==="modal-background"&&rd()});let t=document.createElement("button");t.innerHTML=`<svg xmlns="
    http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="2em" height="2em" className="block" data-v-2754030d="" data-v-512b0344="">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
                  data-v-2754030d="" fill="var(--text-color)"></path></svg>`,t.classList.add("modal-close-button");let r=document.createElement("div");r.classList.add("modal-close-button-container"),r.appendChild(t),t.addEventListener("click",()=>{rd()}),e.appendChild(r),r.addEventListener("click",()=>{rd()});let n=document.createElement("div");n.id="modal",n.classList.add("modal"),n.addEventListener("click",({target:e})=>{"A"===e.tagName.toUpperCase()&&rd()});let o=document.createElement("div");o.id="modal-content",o.classList.add("modal-content"),n.appendChild(o),e.appendChild(n),document.body.appendChild(e),document.addEventListener("keydown",({key:e})=>{"Escape"===e&&rd()})}()}renderTemplates(){this.renderLibdocTemplate("base",this.libdoc,"#root"),this.renderImporting(),this.renderShortcuts(),this.renderKeywords(),this.renderLibdocTemplate("data-types"),document.querySelectorAll(".dtdoc pre, .kwdoc pre").forEach(e=>{e.textContent=e.textContent.split("\n").map(e=>e.trim()).join("\n")}),this.renderLibdocTemplate("footer")}initHashEvents(){window.addEventListener("hashchange",function(){document.getElementsByClassName("hamburger-menu")[0].checked=!1},!1),window.addEventListener("hashchange",function(){if(0==window.location.hash.indexOf("#type-")){let e="#type-modal-"+decodeURI(window.location.hash.slice(6)),t=document.querySelector(".data-types").querySelector(e);t&&rp(t)}},!1),this.scrollToHash()}initTagSearch(){let e=new URLSearchParams(window.location.search),t="";e.has("tag")&&(t=e.get("tag"),this.tagSearch(t,window.location.hash)),this.libdoc.tags.length&&(this.libdoc.selectedTag=t,this.renderLibdocTemplate("tags-shortcuts"),document.getElementById("tags-shortcuts-container").onchange=e=>{let t=e.target.selectedOptions[0].value;""!=t?this.tagSearch(t):this.clearTagSearch()})}initLanguageMenu(){this.renderTemplate("language",{languages:this.translations.getLanguageCodes()}),document.querySelectorAll("#language-container ul a").forEach(e=>{e.innerHTML===this.translations.currentLanguage()&&e.classList.toggle("selected"),e.addEventListener("click",()=>{this.translations.setLanguage(e.innerHTML)&&this.render()})}),document.querySelector("#language-container button").addEventListener("click",()=>{document.querySelector("#language-container ul").classList.toggle("hidden")})}renderImporting(){this.renderLibdocTemplate("importing"),this.registerTypeDocHandlers("#importing-container")}renderShortcuts(){this.renderLibdocTemplate("shortcuts"),document.getElementById("toggle-keyword-shortcuts").addEventListener("click",()=>this.toggleShortcuts()),document.querySelector(".clear-search").addEventListener("click",()=>this.clearSearch()),document.querySelector(".search-input").addEventListener("keydown",()=>rf(()=>this.searching(),150)),this.renderLibdocTemplate("keyword-shortcuts"),document.querySelectorAll("a.match").forEach(e=>e.addEventListener("click",this.closeMenu))}registerTypeDocHandlers(e){document.querySelectorAll(`${e} a.type`).forEach(e=>e.addEventListener("click",e=>{let t=e.target.dataset.typedoc;rp(document.querySelector(`#type-modal-${t}`))}))}renderKeywords(e=null){null==e&&(e=this.libdoc),this.renderLibdocTemplate("keywords",e),document.querySelectorAll(".kw-tags span").forEach(e=>{e.addEventListener("click",e=>{this.tagSearch(e.target.innerText)})}),this.registerTypeDocHandlers("#keywords-container"),document.getElementById("keyword-statistics-header").innerText=""+this.libdoc.keywords.length}setTheme(){document.documentElement.setAttribute("data-theme",this.getTheme())}getTheme(){return null!=this.libdoc.theme?this.libdoc.theme:window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}scrollToHash(){if(window.location.hash){let e=window.location.hash.substring(1),t=document.getElementById(decodeURIComponent(e));null!=t&&t.scrollIntoView()}}tagSearch(e,t){document.getElementsByClassName("search-input")[0].value="";let r={tags:!0,tagsExact:!0},n=window.location.pathname+"?tag="+e+(t||"");this.markMatches(e,r),this.highlightMatches(e,r),history.replaceState&&history.replaceState(null,"",n),document.getElementById("keyword-shortcuts-container").scrollTop=0}clearTagSearch(){document.getElementsByClassName("search-input")[0].value="",history.replaceState&&history.replaceState(null,"",window.location.pathname),this.resetKeywords()}searching(){this.searchTime=Date.now();let e=document.getElementsByClassName("search-input")[0].value,t={name:!0,args:!0,doc:!0,tags:!0};e?requestAnimationFrame(()=>{this.markMatches(e,t,this.searchTime,()=>{this.highlightMatches(e,t,this.searchTime),document.getElementById("keyword-shortcuts-container").scrollTop=0})}):this.resetKeywords()}highlightMatches(e,t,n){if(n&&n!==this.searchTime)return;let o=document.querySelectorAll("#shortcuts-container .match"),i=document.querySelectorAll("#keywords-container .match");if(t.name&&(new(r(eb))(o).mark(e),new(r(eb))(i).mark(e)),t.args&&new(r(eb))(document.querySelectorAll("#keywords-container .match .args")).mark(e),t.doc&&new(r(eb))(document.querySelectorAll("#keywords-container .match .doc")).mark(e),t.tags){let n=document.querySelectorAll("#keywords-container .match .tags a, #tags-shortcuts-container .match .tags a");if(t.tagsExact){let t=[];n.forEach(r=>{r.textContent?.toUpperCase()==e.toUpperCase()&&t.push(r)}),new(r(eb))(t).mark(e)}else new(r(eb))(n).mark(e)}}markMatches(e,t,r,n){if(r&&r!==this.searchTime)return;let o=e.replace(/[-[\]{}()+?*.,\\^$|#]/g,"\\$&");t.tagsExact&&(o="^"+o+"$");let i=RegExp(o,"i"),a=i.test.bind(i),s={},l=0;s.keywords=this.libdoc.keywords.map(e=>{let r={...e};return r.hidden=!(t.name&&a(r.name))&&!(t.args&&a(r.args))&&!(t.doc&&a(r.doc))&&!(t.tags&&r.tags.some(a)),!r.hidden&&l++,r}),this.renderLibdocTemplate("keyword-shortcuts",s),this.renderKeywords(s),this.libdoc.tags.length&&(this.libdoc.selectedTag=t.tagsExact?e:"",this.renderLibdocTemplate("tags-shortcuts")),document.getElementById("keyword-statistics-header").innerText=l+" / "+s.keywords.length,0===l&&(document.querySelector("#keywords-container table").innerHTML=""),n&&requestAnimationFrame(n)}closeMenu(){document.getElementById("hamburger-menu-input").checked=!1}openKeywordWall(){document.getElementsByClassName("shortcuts")[0].classList.add("keyword-wall"),this.storage.set("keyword-wall","open"),document.getElementById("toggle-keyword-shortcuts").innerText="-"}closeKeywordWall(){document.getElementsByClassName("shortcuts")[0].classList.remove("keyword-wall"),this.storage.set("keyword-wall","close"),document.getElementById("toggle-keyword-shortcuts").innerText="+"}toggleShortcuts(){document.getElementsByClassName("shortcuts")[0].classList.contains("keyword-wall")?this.closeKeywordWall():this.openKeywordWall()}resetKeywords(){this.renderLibdocTemplate("keyword-shortcuts"),this.renderKeywords(),this.libdoc.tags.length&&(this.libdoc.selectedTag="",this.renderLibdocTemplate("tags-shortcuts")),history.replaceState&&history.replaceState(null,"",location.pathname)}clearSearch(){document.getElementsByClassName("search-input")[0].value="";let e=document.getElementById("tags-shortcuts-container");e&&(e.selectedIndex=0),this.resetKeywords()}renderLibdocTemplate(e,t=null,r=""){null==t&&(t=this.libdoc),this.renderTemplate(e,t,r)}renderTemplate(e,t,n=""){let o=document.getElementById(`${e}-template`)?.innerHTML,i=r(ew).compile(o);""===n&&(n=`#${e}-container`),document.body.querySelector(n).innerHTML=i(t)}};!function(e){let t=new ek("libdoc"),r=eS.getInstance(e.lang);new rg(e,t,r).render()}(libdoc);</script>
  </body>
</html>
