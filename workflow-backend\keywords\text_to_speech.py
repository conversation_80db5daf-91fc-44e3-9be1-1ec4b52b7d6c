import io
import wave

import pygame
import requests

from loguru import logger
from config.env_config import get_config_item, YN_URL


def make_request(text):
    headers = {
        "Authorization": "api.example.com",
        "content-type": "application/json",
        "Connection": "keep-alive",
    }

    base_url = f"{get_config_item(YN_URL)}/wimai/api/tool/ttsStream"
    data = {"text": text}
    return {"url": base_url, "headers": headers, "data": data}


def text_to_speech(text=""):
    play_text(text)


def play_text(text):

    request = make_request(text=text)

    response = requests.post(
        url=request["url"], headers=request["headers"], json=request["data"]
    )
    if response.status_code == 200:
        output_buffer = io.BytesIO()
        # 打开WAV文件并写入结构
        with wave.open(output_buffer, "wb") as wf:
            # 设置fmt块参数
            wf.setnchannels(1)  # 单声道
            wf.setsampwidth(2)  # 16位深度
            wf.setframerate(22050)

            # 写入音频数据（自动生成DATA块）
            wf.writeframes(response.content)
            wf.close()
        try:
            output_buffer.seek(0)
            pygame.mixer.init()
            pygame.mixer.music.load(output_buffer)
            pygame.mixer.music.play()
            # Keep the script running while the music plays
            while pygame.mixer.music.get_busy():
                pygame.time.Clock().tick(1)

        except pygame.error as e:
            logger.info("Error loading or playing the audio file:{}".format(e))
            pass
    else:
        logger.info(f"request not successful {response.status_code}")
        pass
