"""
单节点执行API
用于执行工作流中的单个节点，获取其输出数据
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import json

from loguru import logger

from executor.task_executor import TaskExecutor, ExecuteParam
from transpiler.workflow_transpiler import WorkflowTranspiler
from models.workflow import WorkflowData

router = APIRouter(prefix="/api", tags=["execution"])

# 使用现有的执行器
executor = TaskExecutor()
transpiler = WorkflowTranspiler()


@router.post("/execute-node")
async def execute_single_node(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行单个节点
    """
    try:
        node_id = request.get("nodeId")
        nodes = request.get("nodes", [])
        edges = request.get("edges", [])

        if not node_id:
            raise HTTPException(status_code=400, detail="缺少nodeId参数")

        if not nodes:
            raise HTTPException(status_code=400, detail="缺少nodes参数")

        # 找到目标节点
        target_node = None
        for node in nodes:
            if node["id"] == node_id:
                target_node = node
                break

        if not target_node:
            raise HTTPException(status_code=404, detail=f"未找到节点: {node_id}")

        print(f"执行节点: {node_id}")
        print(f"节点类型: {target_node.get('data', {}).get('componentType')}")

        # 创建临时工作流，只包含必要的节点
        temp_workflow = create_minimal_workflow(target_node, nodes, edges)

        # 使用现有的工作流转译器和执行器
        workflow_data = WorkflowData(**temp_workflow)
        task_id = f"single_node_{node_id}"
        robot_code = await transpiler.transpile(workflow_data, task_id)

        print(f"生成的Robot代码:\n{robot_code}")

        # 执行工作流
        execute_param = ExecuteParam(
            robot_code=robot_code, task_id=task_id, token="Bearer test", options={}
        )
        r = await executor.execute_in_queue(execute_param)

        if r.get("error", "") != "":
            logger.warning("提交任务失败")
            return {
                "success": False,
                "nodeId": node_id,
                "error": r.get("error", ""),
                "output": {},
                "logs": [],
            }

        execution_id = r.get("execution_id", "")

        # 等待执行完成
        import asyncio

        max_wait = 60  # 最多等待60秒
        wait_time = 0

        while wait_time < max_wait:
            execution_result = await executor.get_execution_status(execution_id)

            if execution_result.status.value in ["success", "failed", "cancelled"]:
                # 获取监听器中的输出变量
                output_data = {}
                if execution_id in executor.listeners:
                    monitor = executor.listeners[execution_id]
                    if hasattr(monitor, "variables"):
                        output_data = monitor.variables

                return {
                    "success": execution_result.status.value == "success",
                    "nodeId": node_id,
                    "output": output_data,
                    "logs": [log.message for log in execution_result.logs],
                    "status": execution_result.status.value,
                }

            await asyncio.sleep(1)
            wait_time += 1

        return {
            "success": False,
            "nodeId": node_id,
            "error": "执行超时",
            "output": {},
            "logs": [],
        }

    except Exception as e:
        print(f"执行节点失败: {e}")
        import traceback

        traceback.print_exc()
        return {"success": False, "error": str(e)}


def create_minimal_workflow(
    target_node: Dict[str, Any],
    all_nodes: List[Dict[str, Any]],
    all_edges: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """
    创建最小化的工作流，只包含执行目标节点所需的节点
    """
    # 找到所有依赖的节点
    required_nodes = []
    required_edges = []

    # 添加开始节点
    start_node = {
        "id": "temp_start",
        "type": "start",
        "position": {"x": 0, "y": 0},
        "data": {
            "label": "开始",
            "componentType": "workflow_start",
            "config": {},
            "category": "control",
            "inputs": [],
            "outputs": ["workflow_context"],
            "isSystemNode": True,
        },
    }
    required_nodes.append(start_node)

    # 添加目标节点
    required_nodes.append(target_node)

    # 添加结束节点
    end_node = {
        "id": "temp_end",
        "type": "end",
        "position": {"x": 200, "y": 0},
        "data": {
            "label": "结束",
            "componentType": "workflow_end",
            "config": {},
            "category": "control",
            "inputs": ["workflow_context"],
            "outputs": [],
            "isSystemNode": True,
        },
    }
    required_nodes.append(end_node)

    # 添加连接边
    required_edges.extend(
        [
            {
                "id": "temp_edge_1",
                "source": "temp_start",
                "target": target_node["id"],
                "type": "control",
            },
            {
                "id": "temp_edge_2",
                "source": target_node["id"],
                "target": "temp_end",
                "type": "control",
            },
        ]
    )

    return {
        "nodes": required_nodes,
        "edges": required_edges,
        "name": f"临时执行_{target_node['id']}",
        "description": f"临时工作流用于执行节点 {target_node['id']}",
    }


async def execute_workflow(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行工作流并返回结果
    """
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(workflow_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        # 创建临时输出目录
        output_dir = tempfile.mkdtemp()

        try:
            # 执行工作流
            cmd = [
                "python",
                "-m",
                "robot",
                "--outputdir",
                output_dir,
                "--variable",
                f"WORKFLOW_FILE:{temp_file}",
                "--listener",
                "executor.execution_listener.ExecutionListener",
                "executor/workflow_executor.robot",
            ]

            print(f"执行命令: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=Path(__file__).parent.parent,
                timeout=120,  # 2分钟超时
            )

            print(f"执行结果: return_code={result.returncode}")
            print(f"stdout: {result.stdout}")
            if result.stderr:
                print(f"stderr: {result.stderr}")

            # 解析执行结果
            output_data = {}
            logs = []

            # 从stdout中提取变量输出
            if result.stdout:
                lines = result.stdout.split("\n")
                for line in lines:
                    if '"output":' in line and "{" in line:
                        try:
                            # 尝试提取JSON输出
                            start = line.find("{")
                            if start != -1:
                                json_str = line[start:]
                                data = json.loads(json_str)
                                if "output" in data:
                                    output_data.update(data["output"])
                        except json.JSONDecodeError:
                            continue

                    logs.append(line)

            return {
                "success": result.returncode == 0,
                "output": output_data,
                "logs": logs,
                "return_code": result.returncode,
            }

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file)
                import shutil

                shutil.rmtree(output_dir, ignore_errors=True)
            except:
                pass

    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="工作流执行超时")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行工作流失败: {str(e)}")


@router.post("/execute-node-simple")
async def execute_node_simple(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    简化版节点执行，直接返回模拟数据用于测试
    """
    try:
        node_id = request.get("nodeId")
        nodes = request.get("nodes", [])

        # 找到目标节点
        target_node = None
        for node in nodes:
            if node["id"] == node_id:
                target_node = node
                break

        if not target_node:
            raise HTTPException(status_code=404, detail=f"未找到节点: {node_id}")

        node_type = target_node.get("data", {}).get("componentType")

        # 根据节点类型返回模拟数据
        if node_type in ["http_post", "http_get"]:
            return {
                "success": True,
                "nodeId": node_id,
                "output": {
                    "response": {
                        "id": "test-response-id",
                        "object": "chat.completion",
                        "created": 1234567890,
                        "model": "deepseek-chat",
                        "choices": [
                            {
                                "index": 0,
                                "message": {
                                    "role": "assistant",
                                    "content": "这是一个测试响应内容",
                                },
                                "finish_reason": "stop",
                            }
                        ],
                        "usage": {
                            "prompt_tokens": 10,
                            "completion_tokens": 20,
                            "total_tokens": 30,
                        },
                    }
                },
            }

        elif node_type == "db_query":
            return {
                "success": True,
                "nodeId": node_id,
                "output": {
                    "query_result": [
                        {
                            "id": 1,
                            "name": "测试记录1",
                            "value": "值1",
                            "create_time": "2024-01-01 10:00:00",
                        },
                        {
                            "id": 2,
                            "name": "测试记录2",
                            "value": "值2",
                            "create_time": "2024-01-02 11:00:00",
                        },
                    ]
                },
            }

        else:
            return {
                "success": True,
                "nodeId": node_id,
                "output": {
                    "data": {
                        "message": f"节点 {node_id} 执行成功",
                        "type": node_type,
                        "timestamp": "2024-01-01T10:00:00Z",
                    }
                },
            }

    except Exception as e:
        return {"success": False, "error": str(e)}
