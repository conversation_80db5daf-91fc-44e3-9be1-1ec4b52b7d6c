class RecLibrary1:

    def keyword_only_in_library_1(self):
        print("Keyword from library 1")

    def keyword_in_both_libraries(self):
        print("Keyword from library 1")

    def keyword_in_all_resources_and_libraries(self):
        print("Keyword from library 1")

    def keyword_everywhere(self):
        print("Keyword from library 1")

    def no_operation(self):
        print("Overrides keyword from BuiltIn library")

    def similar_kw_3(self):
        pass

    def action(self):
        pass

    def do_action(self):
        pass

    def action_and_expect_problems(self):
        pass

    def action_and_ignore_problems(self):
        pass

    def wait_until_action_succeeds(self):
        pass

    def do_stuff(self):
        pass

    def open_application(self):
        pass

    def ask_user_for_input(self):
        pass

    def boot_up_server(self):
        pass

    def shut_down_server(self):
        pass

    def minimize_window(self):
        pass

    def maximize_window(self):
        pass

    def open_window(self):
        pass

    def create_data(self):
        pass

    def delete_data(self):
        pass

    def update_data(self):
        pass

    def modify_data(self):
        pass

    def get_data(self):
        pass

    def read_data(self):
        pass

    def record(self, message, level):
        pass

    def nothing(self):
        pass
