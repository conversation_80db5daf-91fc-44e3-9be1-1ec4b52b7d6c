from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, List, Any

from pydantic.dataclasses import dataclass


class ExecutionState(str, Enum):
    """执行状态枚举"""

    New = "new"
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(str, Enum):
    """日志级别枚举"""

    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"


@dataclass
class ActionResult:
    # 必须传入的字段（无默认值，实例化时必须指定）
    node_id: str  # 节点ID（必填）

    # 有默认值的字段（可选参数）
    state: ExecutionState = ExecutionState.New  # 默认初始状态
    condition_key: str = ""  # 条件键（默认空）
    errors: List[str] = field(default_factory=list)  # 错误列表（默认空列表）
    inputs: Dict[str, Any] = field(default_factory=dict)  # 输入变量（默认空字典）
    outputs: Dict[str, Any] = field(default_factory=dict)  # 输出变量（默认空字典）
    messages: List[str] = field(default_factory=list)  # 执行日志（默认空列表）


@dataclass
class AgentMetadata:
    name: str = "未命名工作流"
    description: str = ""
    version: str = "1.0.0"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class ActionParam:
    type: str = ""
    required: bool = False
    default: str = ""


@dataclass
class ActionNode:
    id: str = ""
    type: str = ""
    label: str = ""
    category: str = ""
    description: Optional[str] = None
    componentType: Optional[str] = None
    inputs: List[str] = field(default_factory=list)
    outputs: List[str] = field(default_factory=list)
    config: Dict[str, Any] = field(default_factory=dict)
    options: Dict[str, Any] = field(default_factory=dict)
    next_condition_nodes: Dict[str, List[str]] = field(default_factory=dict)
    next_nodes: List[str] = field(default_factory=list)
    previous_nodes: List[str] = field(default_factory=list)
    is_condition_node: bool = False


@dataclass
class Agent:

    task_id: str
    # 必须传入的字段（无默认值）
    node_map: Dict[str, ActionNode]  # 工作流节点（必填）

    # 可选字段（有默认值）
    variables: Dict[str, Any] = field(default_factory=dict)
    metadata: AgentMetadata = field(default_factory=AgentMetadata)  # 自动创建默认元数据

    def __post_init__(self):
        # 验证nodes不能为None或空
        if not self.node_map:
            raise ValueError("不能建立没有节点的agent（nodes必须包含至少一个节点）")
