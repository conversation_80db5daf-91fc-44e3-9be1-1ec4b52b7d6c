{"name": "WimTask-rpa-designer", "version": "1.0.0", "description": "WimTask - 可视化自动化设计器", "private": true, "workspaces": ["web"], "scripts": {"install:all": "npm install && cd web && npm install && cd ../workflow-backend && pip install -r requirements.txt", "dev": "concurrently \"npm run dev:web\" \"npm run dev:workflow-backend\"", "dev:web": "cd web && npm run dev", "dev:workflow-backend": "cd workflow-backend && python main.py", "build": "npm run build:web", "build:web": "cd web && npm run build", "build:electron": "cd web && npm run build:electron", "clean": "npm run clean:web && npm run clean:workflow-backend", "clean:web": "cd web && rm -rf node_modules dist dist-electron", "clean:workflow-backend": "cd workflow-backend && rm -rf __pycache__ outputs/* logs/*", "lint": "cd web && npm run lint", "test": "npm run test:web", "test:web": "cd web && npm run test"}, "keywords": ["RPA", "Robot Framework", "Automation", "Visual Designer", "Electron", "<PERSON><PERSON>", "FastAPI", "Python"], "author": "WimTask Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}