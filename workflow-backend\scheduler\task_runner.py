import multiprocessing
import asyncio
import sys
from pathlib import Path

import time
from typing import Optional, Dict, Tuple
import threading

from loguru import logger

from config import globals

from executor.lifecycle import NodeStatus
from executor.task_executor import TaskExecutor, ExecuteParam, QueuedTaskManager
from models.workflow import create_execution_id
from servers.websocket import ws_broadcast_proxy


def setup_subprocess_logger():
    logger.remove()

    if sys.stdout is not None:  # 检查stdout是否存在
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
        )

    # 添加文件输出
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)

    logger.add(
        log_dir / "Scheduler.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="20 MB",
        retention="1 days",
        compression="zip",
    )


class SubProcessTaskRunner:

    def __init__(self, cmd_queue, status_queue, t=""):
        self.task_manager = QueuedTaskManager()
        self.task_executor = TaskExecutor()
        self.cmd_queue = cmd_queue
        self.status_queue = status_queue
        self.token = t

    def subprocess_runner(self):
        setup_subprocess_logger()
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        logger.info(f"[定时执行进程 {multiprocessing.current_process().pid}] 启动")

        loop.create_task(self.task_manager.start())
        globals.token = self.token

        async def run_task(execute_param: ExecuteParam):

            if execute_param.execution_id:
                execution_id = execute_param.execution_id
            else:
                execution_id = create_execution_id()

            execute_param.execution_id = execution_id

            execute_param.step_end_callback = on_event

            if execute_param.token is not None and execute_param.token != "":
                globals.token = execute_param.token

            err = await self.task_manager.submit(self.task_executor, execute_param)

            if err is not None:
                return {"execution_id": execution_id, "error": err}

            qsize = await self.task_manager.qsize()

            logger.info(f"成功提交一个任务，现在队列中有{qsize}个任务")

            return {"execution_id": execution_id, "qsize": qsize}

        # 处理主进程命令
        async def process_commands():
            while True:
                if not self.cmd_queue.empty():
                    cmd = self.cmd_queue.get()

                    # 发送消息给指定客户端
                    if cmd["type"] == "submit_task":
                        params = cmd["data"]
                        await run_task(params)

                    # 关闭服务器命令
                    elif cmd["type"] == "shutdown":
                        await self.task_manager.exit()
                        return

                    elif cmd["type"] == "config":
                        params = cmd["data"]
                        for k, v in params.items():
                            globals.token[k] = v

                await asyncio.sleep(0.01)

        def send_state(data: Dict):
            self.status_queue.put({"type": "run_state", "data": data}, block=False)

        def on_event(data: dict):
            ns = NodeStatus(
                node_id=data.get("nodeId", ""),
                node_type=data.get("nodeType", ""),
                node_name=data.get("title", ""),
                history_id=data.get("historyId", ""),
                task_id=data.get("missionId", ""),
                describe=data.get("describe", ""),
            )

            if data.get("end", "0") == "1":
                ns.flow_end(
                    state=data.get("state", ""),
                    msg=data.get("exception", ""),
                    errors=data.get("errors", []),
                )
            else:
                state = data.get("state", "unknown")
                if state == "failed":
                    ns.failed(data.get("exception", ""))
                elif state == "progress":
                    ns.progress(
                        inputs=data.get("input", {}),
                        outputs=data.get("output", {}),
                    )
                elif state == "passed":
                    ns.done(
                        inputs=data.get("input", {}),
                        outputs=data.get("output", {}),
                    )

            send_state(ns.to_dict())

        ws_broadcast_proxy.set_broadcast_func(send_state)

        # 服务器主逻辑
        async def server_main():
            task = asyncio.create_task(process_commands())
            await task

        try:
            loop.run_until_complete(server_main())
        finally:
            loop.close()
            logger.info(f"[定时执行进程 {multiprocessing.current_process().pid}] 退出")


class TaskRunner:
    def __init__(self, on_state_change=None):
        self.process: Optional[multiprocessing.Process] = None

        # 进程间通信队列
        self.cmd_queue = multiprocessing.Queue()  # 主进程 -> 子进程（命令）
        self.status_queue = multiprocessing.Queue(100)  # 子进程 -> 主进程（状态通知）
        self.on_state_change = on_state_change

    def start(self, on_state_change=None):
        if on_state_change is not None:
            self.on_state_change = on_state_change
        """启动子进程服务器"""
        if self.process and self.process.is_alive():
            logger.info("[主进程] 定时执行进程已在运行")
            return
        s = SubProcessTaskRunner(self.cmd_queue, self.status_queue, globals.token)
        self.process = multiprocessing.Process(
            target=s.subprocess_runner, name=f"ST-Server"
        )
        self.process.start()
        self.start_status_listener()

    def stop(self, timeout: int = 10):
        """停止服务器"""
        if not (self.process and self.process.is_alive()):
            logger.info("[主进程] 定时执行进程未在运行")
            return

        # 发送关闭命令
        self.cmd_queue.put({"type": "shutdown"})
        logger.info(f"[主进程] 定时执行进程进程退出（最多 {timeout} 秒）")
        self.process.join(timeout)

        if self.process.is_alive():
            logger.info("[主进程] 定时执行进程退出超时，强制终止")
            self.process.terminate()
            self.process.join(2)

        self.process = None
        logger.info("[主进程] 定时执行进程已停止")

    # 主进程调用的API：向所有客户端广播消息
    async def submit(self, params):
        """主进程调用：向所有连接的客户端发送消息"""
        if not (self.process and self.process.is_alive()):
            logger.info(f"[主进程] 定时任务执行进程没有启动，无法提交任务{params}")
            return

        if params.execution_id:
            execution_id = params.execution_id
        else:
            execution_id = create_execution_id()

        self.cmd_queue.put({"type": "submit_task", "data": params})

        return {"execution_id": execution_id, "success": True}

    # 可选：主进程监听状态变化（如客户端连接/断开）
    def start_status_listener(self):

        def listener():
            logger.info("[主进程] 状态监听线程启动")
            while self.process and self.process.is_alive():
                if not self.status_queue.empty():
                    status = self.status_queue.get()
                    if status["type"] == "run_state":
                        logger.info(
                            f"[主进程] 定时任务执行器有状态变更{status.get('data',{})}"
                        )
                        self.on_state_change(status.get("data", {}))
                time.sleep(0.1)
            logger.info("[主进程] 状态监听线程停止")

        threading.Thread(target=listener, daemon=True).start()


task_runner = TaskRunner()
