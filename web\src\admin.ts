import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { ElMessageBox } from 'element-plus'
import App from './Admin.vue'
import router from './router/admin'

import '@vueup/vue-quill/dist/vue-quill.snow.css'
// 自定义scss样式
import '@/styles/index.scss'
// 自定义iconfont样式
import '@/assets/fonts/iconfont.css';
// 自定义panel-iconfont样式 左侧面板指令图标
import '@/assets/action-fonts/iconfont.css';
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { VideoExtend, QuillVideoWatch } from 'quill-video-extend-module'
import { QuillEditor,Quill } from '@vueup/vue-quill'
import imageResize from "quill-image-resize";
import { Video } from '@/assets/js/quill-video-resize.js'
import "@/assets/css/quill-video-resize.css";
// import VideoResize from '@/assets/js/video-resize/VideoResize.js';

// Quill.register('modules/VideoResize', VideoResize);
// register with Quill
Quill.register({ 'formats/video': Video });

Quill.register('modules/VideoExtend', VideoExtend)

// --- 步骤 1 & 2: 导入 Font 模块并定义字体列表 ---

// 获取 Quill 的字体格式模块
const Font = Quill.import('attributors/style/font');

// 定义你想要的字体列表
// 这里的 'SimSun' 是宋体, 'KaiTi' 是楷体, 'Microsoft YaHei' 是微软雅黑
const fontNames = [
  'SourceHanSansSC-Regular',
  'Arial', 
  'SimSun', // 宋体
  'KaiTi', // 楷体
  'Microsoft YaHei', // 微软雅黑
];

// --- 步骤 3: 设置白名单并注册 ---

// 将字体列表设置为白名单
Font.whitelist = fontNames; 

// 重新注册 Font 模块以应用我们的白名单
Quill.register(Font, true);

// 1. 导入字体大小模块并设置白名单

const SizeStyle = Quill.import('attributors/style/size')
const customSizes = ['10px', '12px', '14px', '16px', '18px', '20px', '24px', '30px', '36px']
SizeStyle.whitelist = customSizes
// 2. 注册字体大小模块
Quill.register(SizeStyle, true)

// --- 步骤 1: 创建自定义的行高 Attributor ---

// 定义我们希望在下拉菜单中显示的行高值
const allowedLineHeights = ['1', '1.5', '2', '2.5', '3'];

// Quill 的 Attributor 用于将格式应用到文本上。
// 因为 line-height 是作用于整个段落的，所以我们使用 BlockAttributor。
const Parchment = Quill.import('parchment');
const LineHeightStyle = new Parchment.Attributor.Style(
  'lineheight', // 属性名
  'line-height', // CSS 样式名
  {
    scope: Parchment.Scope.BLOCK, // 作用域：块级元素
    whitelist: allowedLineHeights // 白名单：允许的值
  }
);
// --- 步骤 2: 注册我们自定义的 Attributor ---

Quill.register(LineHeightStyle, true);
Quill.register("modules/imageResize", imageResize);

// Vue Flow styles
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'
import '@vue-flow/node-resizer/dist/style.css'

// 注册配置Schema
import { registerAllSchemas } from './config/schemaRegistry'

import moment from 'moment';
window.moment = moment;


// sessionStorage.setItem('UniWimAuthorization', `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5rV1eqbMyRYuvCMNh9wd-a0vB_il53a0XhQGbSHmBuw`)
// @ts-ignore
import systemApi from "@/api/system";
// @ts-ignore
import saasApi from '@/api/index';

const InitReady = () => {
  let configList = []
  return new Promise(resolve => {
    Promise.all([
      systemApi.initUserInfo(),
      saasApi.AITaskConfigList(),
    ]).then(([userInfo, configs, template]) => {
      // useUserStore().setUserInfo(userInfo);
      if(configs && configs.length){
        configList = configs
        useUserStore().setConfigs(configList);
      }
    }).finally(async ()=>{
      resolve(true)
    })
  })
};

const app = createApp(App)
app.component('QuillEditor', QuillEditor)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)

app.use(ElementPlus, { zIndex: 3000, locale: zhCn })
window.addEventListener('unhandledrejection', event => {
  // 捕获动态导入失败的错误
  if (event && event.reason && event.reason.message.includes('Failed to fetch dynamically imported module')) {
    ElMessageBox.confirm(
      `应用已更新，请刷新后继续操作。`,
      `提示`,
      {
        closeOnClickModal:false,
        closeOnPressEscape:false,
        showCancelButton:false,
        showClose:false,
        confirmButtonText: '确定',
        type: 'warning',
      },
    ).then(res=>{
      location.reload()
    })
    
    // 防止默认处理（如控制台输出）
    event.preventDefault()
  }
})
// 注册配置Schema
registerAllSchemas()

import { useUserStore } from "@/stores/user";

InitReady().then(()=>{
  app.mount('#app')
})
