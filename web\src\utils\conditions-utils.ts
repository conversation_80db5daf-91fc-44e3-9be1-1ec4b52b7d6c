/**
 * 条件表达式处理工具类
 */
export class ConditionUtils {

  static displayConditionExpression(device_data: any, item: any, expression: string): boolean {
    // console.warn({expression});
    try {
      // 尝试解析JSON格式的多条件表达式
      let conditions: any[] = [];

      try {
        conditions = JSON.parse(expression);
      } catch (e) {
        return true;
      }

      /**
       * conditions结构如下:
       * [{
       *   "field":"#{Response[].code}",
       *   "relation": "==或>或<或!=",
       *   "value":"比较值"
       * }]
       * 1. 取field，去掉#{}，取其中的值，并去掉第一个点前面的值
       * 2. 从device_data或item中取得对应field的值，
       * 3. 根据relation关系，判断结果
       */

      let validResult = true;

      for (const condition of conditions) {
        const field = condition.field?.split('.')[1] || condition.field || '';
        let value = this.get_variable_value(field, device_data);

        if (value === null || value === undefined || value.startsWith('${') || value.startsWith('#{')) {
          value = this.get_variable_value(field, item);
        }

        const result = this.evaluate_condition(value, condition.relation, condition.value);

        if (!result) {
          validResult = false;
          break;
        }
      }

      return validResult;
    } catch (e) {
      console.warn(`显示条件表达式失败: ${e}`);
      return true;
    }
  }

  /**
   * 从变量映射中获取变量值
   *
   * @param variableName 变量名
   * @param variableMapping 变量映射
   * @returns 变量值
   */
  static get_variable_value(variableName: string, variableMapping: any): string {
    // 直接匹配
    if (variableMapping && variableName in variableMapping) {
      return String(variableMapping[variableName]);
    }

    // 尝试部分匹配（处理复杂路径）
    if (variableMapping) {
      for (const key in variableMapping) {
        if (key.includes(variableName) || variableName.includes(key)) {
          return String(variableMapping[key]);
        }
      }
    }

    // 没有找到，返回原始变量名
    return `\$\{${variableName}\}`;
  }

  /**
   * 评估单个条件
   *
   * @param value 要比较的值
   * @param operator 操作符
   * @param compareValue 比较值
   * @param minValue 区间最小值
   * @param maxValue 区间最大值
   * @returns 条件是否满足
   */
  static evaluate_condition(value: any, operator: string, compareValue?: any, minValue?: any, maxValue?: any): boolean {
    try {
      // 处理空值检查
      if (operator === 'is_empty') {
        return value === null || value === undefined || String(value).trim() === '';
      } else if (operator === 'not_empty') {
        return value !== null && value !== undefined && String(value).trim() !== '';
      }

      // 处理区间条件
      if (operator === 'between' || operator === 'not_between') {
        if (minValue === undefined || maxValue === undefined) {
          console.error(`❌ 区间条件缺少参数: min_value=${minValue}, max_value=${maxValue}`);
          return false;
        }

        try {
          const valNum = parseFloat(value);
          const minNum = parseFloat(minValue);
          const maxNum = parseFloat(maxValue);
          const inRange = minNum <= valNum && valNum <= maxNum;
          const result = operator === 'between' ? inRange : !inRange;
          console.log(`🔍 区间判断: ${value}(${valNum}) 在 [${minValue}(${minNum}), ${maxValue}(${maxNum})] 内? ${inRange}, 操作符: ${operator}, 结果: ${result}`);
          return result;
        } catch (e) {
          console.error(`❌ 区间条件数值转换失败: value=${value}, min_value=${minValue}, max_value=${maxValue}, error=${e}`);
          return false;
        }
      }

      // 处理字符串包含
      if (operator === 'contains') {
        return String(value).includes(String(compareValue));
      } else if (operator === 'not_contains') {
        return !String(value).includes(String(compareValue));
      }

      // 处理数值和字符串比较
      if (operator === '==') {
        return this.compare_values_equal(value, compareValue);
      } else if (operator === '!=') {
        return !this.compare_values_equal(value, compareValue);
      } else if (['>', '<', '>=', '<='].includes(operator)) {
        return this.compare_values_numeric(value, compareValue, operator);
      }

      return false;
    } catch (e) {
      console.warn(`条件评估失败: ${e}`);
      return false;
    }
  }

  /**
   * 比较两个值是否相等
   *
   * @param value 第一个值
   * @param compareValue 第二个值
   * @returns 是否相等
   */
  private static compare_values_equal(value: any, compareValue: any): boolean {
    // 尝试数值比较
    try {
      const numValue = parseFloat(value);
      const numCompareValue = parseFloat(compareValue);

      if (!isNaN(numValue) && !isNaN(numCompareValue)) {
        return numValue === numCompareValue;
      }
    } catch {}

    // 尝试布尔比较
    // if (['true', 'false'].includes(String(value).toLowerCase()) && ['true', 'false'].includes(String(compareValue).toLowerCase())) {
    //   return String(value).toLowerCase() === String(compareValue).toLowerCase();
    // }

    // 默认字符串比较
    return String(value) === String(compareValue);
  }

  /**
   * 数值比较
   *
   * @param value 第一个值
   * @param compareValue 第二个值
   * @param operator 比较操作符
   * @returns 比较结果
   */
  private static compare_values_numeric(value: any, compareValue: any, operator: string): boolean {
    try {
      const numValue = parseFloat(value);
      const numCompareValue = parseFloat(compareValue);

      if (isNaN(numValue) || isNaN(numCompareValue)) {
        console.error(`❌ 数值比较失败，无法将值转换为数字: value=${value}, compareValue=${compareValue}`);
        return false;
      }

      switch (operator) {
        case '>': return numValue > numCompareValue;
        case '<': return numValue < numCompareValue;
        case '>=': return numValue >= numCompareValue;
        case '<=': return numValue <= numCompareValue;
        default: return false;
      }
    } catch (e) {
      console.error(`❌ 数值比较异常: value=${value}, compareValue=${compareValue}, operator=${operator}, error=${e}`);
      return false;
    }
  }
}
