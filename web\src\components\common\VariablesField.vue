<template>
  <div class="conditions-field">
    <div class="condition-list">
      <div
        v-for="(conditionGroup, groupIndex) in modelValue"
        :key="groupIndex"
        :class="getGroupClass(groupIndex)"
        class="condition-group"
      >

        <div class="sub-conditions">
          <div
            :class="getChildGroupClass(0)"
            class="sub-condition-item"
          >
            <div class="sub-condition-content-group">
              <div class="sub-condition-content">
                <VariableInputField
                  class="sub-condition-input-field"
                  :model-value="conditionGroup.field"
                  :noAdd="true"
                  disabled
                  @update:model-value="
                    (val) => {
                      conditionGroup.field = val
                    }
                  "
                  :field-config="{ type: 'string', variableSupport: true, placeholder: '右侧选择变量' }"
                  type="string"
                />
              </div>
              <div class="sub-condition-content">
                <el-input
                  v-model="conditionGroup.value"
                  placeholder="输入值"
                  size="small"
                />
              </div>
            </div>
          </div>
          <div class="sub-conditions-actions">
            <el-button
              size="small"
              :type="getGroupClass(groupIndex)['is-hovered'] ? 'danger' : ''"
              text
              @click="removeConditionGroup(groupIndex)"
              @mouseenter="handleMouseEnter(groupIndex)"
              @mouseleave="handleMouseLeave"
            >
              <el-icon>
                <Delete />
              </el-icon>
              <span>移除</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-button
      class="add-condition-button"
      color="#F2F4F7"
      size="default"
      @click="addConditionGroup"
      style="margin-top: 8px"
    >
      <el-icon color="#333333">
        <Plus />
      </el-icon>
      <span>添加变量</span>
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { Delete, Plus } from '@element-plus/icons-vue'
import VariableInputField from './VariableInputField.vue'
import { ref } from 'vue'

interface Props {
  modelValue: Array<{
    field: string | null
    value: string | null
  }>
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const addConditionGroup = () => {
  const newGroup = {
    field: null,
    value: null,
  }
  emit('update:modelValue', [...(props.modelValue || []), newGroup])
}

const removeConditionGroup = (groupIndex: number) => {
  const newGroups = [...props.modelValue]
  newGroups.splice(groupIndex, 1)
  hoveredGroupIndex.value = null

  emit('update:modelValue', newGroups)
}

// 修改后
const onOperatorChange = (operator: string, value: string | null) => {
  if (['isEmpty', 'isNotEmpty'].includes(operator)) {
    // 直接返回null值，不需要修改对象属性
    return null
  }
  emit('update:modelValue', [...props.modelValue])
  return value
}

// 添加hover状态管理
const hoveredGroupIndex = ref<number | null>(null)
const handleMouseEnter = (index: number) => {
  hoveredGroupIndex.value = index
}
const handleMouseLeave = () => {
  hoveredGroupIndex.value = null
}
// 添加动态类名计算
const getGroupClass = (index: number) => {
  return {
    'is-hovered': hoveredGroupIndex.value === index,
  }
}
const hoveredChildGroupIndex = ref<number | null>(null)
const getChildGroupClass = (index: number) => {
  return {
    'is-hovered': hoveredChildGroupIndex.value === index,
  }
}
</script>

<style scoped>
.conditions-field {
  width: 100%;
}

.condition-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 6px;
}

.condition-group {
  border-bottom: 1px solid #ebeef5;
  padding: 12px 5px;
  position: relative;
  transition: background-color 0.2s ease;
    &.no-border{
        margin-top: 14px;
        border-top: 1px solid #ebeef5;
        border-bottom: none;
        padding: 12px 5px;
        margin-bottom: 12px;
    }
}

.condition-group.is-hovered {
  background-color: #fef3f2;
  border-radius: 8px;
}

.condition-header {
  font-size: 12px;
  font-weight: 500;
  width: 60px;
  position: absolute;
  height: calc(100% - 56px);

  .condition-flag {
    font-weight: bold;
    line-height: 1.2;

    .case {
      font-weight: normal;
      font-size: 11px;
      line-height: 1;
      color: #676f83;
    }
  }
}

.condition-actions {
  display: flex;
  align-items: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background: #ffffff;
  right: 1px;
  border: 4px solid #fff;
  border-radius: 24px;
}

.condition-relation-line {
  border: 1px solid #dddee1;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border-right-width: 0;
  position: absolute;
  height: calc(100% - 30px);
  top: 15px;
  width: 10px;
  z-index: 1;
  right: 2px;
}

.relation-switch {
  border: 2px solid #ebeef5;
  border-radius: 24px;
  line-height: 1;
  padding: 2px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--el-color-primary);
  font-weight: bold;
  font-size: 12px;
}

.sub-conditions {

}

.sub-conditions-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sub-condition-item {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
  margin-bottom: 8px;
}

.sub-condition-item.is-hovered {
  .sub-condition-content-group {
    background: #fef3f2;
  }
}

.sub-condition-content-group {
  background: #f1f3f6;
  border-radius: 8px;
  flex: 1;
  :deep(.el-input__wrapper) {
    background: none;
    box-shadow: none;
  }
  :deep(.el-input.is-disabled){
    .el-input__wrapper {
      background: none !important;
      .el-input__inner {
        color: inherit;
        -webkit-text-fill-color: var(--el-input-text-color, var(--el-text-color-regular));
        &::placeholder{
          -webkit-text-fill-color: #999999;
        }
      }
    }
  }
}

.sub-condition-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 4px 4px 4px 0;

  .el-select {
    width: 105px;
    border-left: 1px solid #e3e6ed;
  }

  :deep(.el-select__wrapper) {
    background: none;
    box-shadow: none;
  }

  & ~ .sub-condition-content {
    border-top: 1px solid #e3e6ed;
  }
}
:deep(.sub-condition-input-field){
  .el-input-group__append{
    background: none;
    border-radius: 4px;
    box-shadow: none;
  }
}

.field-description {
  margin-top: 4px;
  font-size: 11px;
  color: #606266;
  line-height: 1.4;
}

.add-condition-button {
  width: 100%;
  color: #333333;

  .el-icon {
    margin-right: 4px;
  }
}
</style>
