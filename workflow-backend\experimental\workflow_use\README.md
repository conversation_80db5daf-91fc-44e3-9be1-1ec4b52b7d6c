# Workflow-Use 集成文档

## 🎯 概述

本集成将 [workflow-use](https://github.com/browser-use/workflow-use) 项目完全集成到 WimTask 中，**完全替换**原有的实验性浏览器录制实现，提供确定性、快速、可靠的工作流录制和执行能力。

## 🔄 重要变更

**⚠️ 完全替换策略**: 原有的 `browser_recording_service.py` 已被完全替换为基于 workflow-use 的新实现。前端无需修改，所有API接口保持兼容。

## ✨ 核心特性

### 🚀 确定性执行
- **快速重放**: 不依赖AI的确定性操作执行
- **高可靠性**: 基于CSS选择器和元素定位的精确操作
- **无AI依赖**: 默认使用非AI方案，符合用户偏好

### 🎬 智能录制
- **浏览器扩展录制**: 通过浏览器扩展捕获用户操作
- **智能选择器生成**: 自动生成稳定的CSS选择器
- **操作优化**: 自动合并和优化录制的操作序列

### 🔄 格式转换
- **双向转换**: WimTask ↔ Workflow-Use 格式互转
- **兼容性**: 保持与现有WimTask工作流的兼容性
- **无缝集成**: 透明的API接口，无需修改前端代码

### ⚡ 可选AI回退
- **智能回退**: 当确定性步骤失败时自动切换到AI代理
- **可配置**: 可以完全禁用AI功能，使用纯确定性执行
- **渐进增强**: 从确定性执行开始，需要时才使用AI

## 📦 安装和配置

### 🚀 快速安装（推荐）

```bash
cd workflow-backend/experimental/workflow_use
python quick_setup.py
```

### 📋 手动安装

```bash
# 1. 安装基本依赖
pip install aiofiles fastapi typer pydantic playwright

# 2. 安装Playwright浏览器
playwright install chromium

# 3. 验证安装
python -c "from services.workflow_use_recording_service import workflow_use_recording_service; print('✅ 安装成功')"
```

### 3. 配置

编辑 `config.json` 文件：

```json
{
  "enabled": true,
  "ai_fallback": false,
  "browser": "chromium",
  "recording": {
    "auto_screenshot": true,
    "smart_selectors": true,
    "element_highlighting": true,
    "timeout": 30000
  },
  "execution": {
    "deterministic_only": true,
    "fast_replay": true,
    "self_healing": false,
    "max_retries": 3
  },
  "integration": {
    "replace_browser_recording": false,
    "compatibility_mode": true,
    "fallback_to_original": true
  }
}
```

## 🚀 使用方法

### API 接口

#### 1. 检查状态
```http
GET /api/workflow-use/status
```

#### 2. 获取能力信息
```http
GET /api/workflow-use/capabilities
```

#### 3. 启动录制
```http
POST /api/workflow-use/recording/start
Content-Type: application/json

{
  "browser": "chromium",
  "start_url": "https://example.com",
  "enable_ai_fallback": false
}
```

#### 4. 停止录制
```http
POST /api/workflow-use/recording/stop
```

#### 5. 执行工作流
```http
POST /api/workflow-use/workflow/execute
Content-Type: application/json

{
  "workflow_definition": {
    "name": "示例工作流",
    "description": "这是一个示例工作流",
    "version": "1.0.0",
    "steps": [...],
    "input_schema": []
  },
  "inputs": {}
}
```

#### 6. 格式转换
```http
POST /api/workflow-use/convert/wimtask-to-workflow-use
Content-Type: application/json

{
  "wimtask_nodes": [...]
}
```

### Python API

```python
from experimental.workflow_use import WorkflowUseService

# 创建服务实例
service = WorkflowUseService(enable_ai_fallback=False)

# 启动录制
result = await service.start_recording_session({
    "browser": "chromium",
    "start_url": "https://example.com"
})

# 停止录制并获取工作流节点
result = await service.stop_recording_session()
workflow_nodes = result.get('workflow_nodes', [])

# 执行工作流
workflow_definition = {...}
result = await service.execute_workflow(workflow_definition)
```

## 🔧 配置选项

### 基本配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | false | 是否启用workflow-use集成 |
| `ai_fallback` | boolean | false | 是否启用AI回退功能 |
| `browser` | string | "chromium" | 默认浏览器类型 |

### 录制配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `recording.auto_screenshot` | boolean | true | 自动截屏 |
| `recording.smart_selectors` | boolean | true | 智能选择器生成 |
| `recording.element_highlighting` | boolean | true | 元素高亮显示 |
| `recording.timeout` | number | 30000 | 操作超时时间(ms) |

### 执行配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `execution.deterministic_only` | boolean | true | 仅使用确定性执行 |
| `execution.fast_replay` | boolean | true | 快速重放模式 |
| `execution.self_healing` | boolean | false | 自愈功能（未来版本） |
| `execution.max_retries` | number | 3 | 最大重试次数 |

### 集成配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `integration.replace_browser_recording` | boolean | false | 完全替换原有录制 |
| `integration.compatibility_mode` | boolean | true | 兼容模式 |
| `integration.fallback_to_original` | boolean | true | 失败时回退到原实现 |

## 🔄 集成策略

### 1. 渐进式集成（推荐）

- 保持现有浏览器录制功能
- workflow-use作为可选增强功能
- 用户可以选择使用哪种录制方式
- 失败时自动回退到原有实现

### 2. 完全替换

- 将workflow-use设为默认录制方式
- 隐藏原有录制选项
- 提供更一致的用户体验

### 3. 并行运行

- 同时提供两种录制方式
- 用户可以比较和选择
- 便于测试和验证

## 🐛 故障排除

### 常见问题

#### 1. 导入错误
```
ImportError: No module named 'workflow_use'
```
**解决方案**: 运行安装脚本或手动安装依赖

#### 2. 浏览器启动失败
```
Error: Browser not found
```
**解决方案**: 安装Playwright浏览器
```bash
playwright install chromium
```

#### 3. 录制无响应
**解决方案**:
- 检查WebSocket连接
- 确认浏览器扩展已加载
- 查看浏览器控制台错误

#### 4. 工作流执行失败
**解决方案**:
- 检查CSS选择器是否有效
- 确认页面元素存在
- 启用AI回退功能

### 日志调试

启用调试日志：
```python
import logging
logging.getLogger('workflow_use').setLevel(logging.DEBUG)
```

查看详细错误信息：
```bash
tail -f logs/phoenix_engine.log
```

## 🔮 未来计划

### 短期目标
- [ ] 完善错误处理和重试机制
- [ ] 添加更多浏览器支持
- [ ] 优化录制性能
- [ ] 增强选择器稳定性

### 中期目标
- [ ] 实现自愈功能
- [ ] 添加工作流调试工具
- [ ] 支持并行执行
- [ ] 集成更多AI模型

### 长期目标
- [ ] 可视化工作流编辑器
- [ ] 云端工作流存储
- [ ] 跨平台支持
- [ ] 企业级功能

## 📄 许可证

本集成遵循与WimTask项目相同的许可证。Workflow-Use项目有其自己的许可证，请参考其仓库。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个集成。

## 📞 支持

如有问题，请：
1. 查看本文档的故障排除部分
2. 检查项目Issue
3. 联系开发团队
