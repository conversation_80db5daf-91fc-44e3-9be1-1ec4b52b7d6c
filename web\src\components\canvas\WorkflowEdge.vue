<template>
  <g @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
    <!-- 不可见的宽路径用于增加点击区域 -->
    <path
      :d="edgePath"
      stroke="transparent"
      :stroke-width="20"
      fill="none"
      class="workflow-edge-hitbox"
      @click="handleEdgeClick"
    />

    <!-- 主路径 -->
    <path
      :d="edgePath"
      :stroke="edgeColor"
      :stroke-width="strokeWidth"
      :stroke-dasharray="strokeDasharray"
      fill="none"
      :marker-end="markerEnd"
      class="workflow-edge-path"
      @click="handleEdgeClick"
    />

    <!-- 选中时的高亮路径 -->
    <path
      v-if="selected"
      :d="edgePath"
      stroke="#409eff"
      :stroke-width="strokeWidth + 2"
      fill="none"
      class="workflow-edge-selection"
      opacity="0.6"
    />

    <!-- 边标签 -->
    <foreignObject
      v-if="data?.label"
      :x="labelX - 30"
      :y="labelY - 10"
      width="60"
      height="20"
      class="workflow-edge-label"
    >
      <div class="edge-label-content">
        {{ data.label }}
      </div>
    </foreignObject>

    <!-- 剪刀删除按钮 -->
    <foreignObject
      v-if="showScissors"
      :x="labelX - 18"
      :y="labelY - 18"
      width="36"
      height="36"
      class="workflow-edge-controls"
    >
      <button
        class="edge-scissors-btn"
        :class="{ 'is-hovered': isHovered }"
        @click="handleDelete"
        @mouseenter="onScissorsEnter"
        @mouseleave="onScissorsLeave"
        title="点击剪刀删除连接"
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.89-2 2-2 2 .89 2 2-.89 2-2 2zm0 12c-1.1 0-2-.89-2-2s.89-2 2-2 2 .89 2 2-.89 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.********.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3h-3z"
          />
        </svg>
      </button>
    </foreignObject>
  </g>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Props {
  id: string
  sourceX: number
  sourceY: number
  targetX: number
  targetY: number
  data?: {
    label?: string
    type?: 'control' | 'data'
    animated?: boolean
  }
  selected?: boolean
}

interface Emits {
  (e: 'delete', edgeId: string): void

  (e: 'click', edgeId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  data: () => ({ type: 'control' }),
})

const emit = defineEmits<Emits>()

// 鼠标悬停状态
const isHovered = ref(false)
const isScissorsHovered = ref(false)

// 计算边的路径
const edgePath = computed(() => {
  const { sourceX, sourceY, targetX, targetY } = props

  // 计算控制点，创建平滑的贝塞尔曲线
  const deltaX = targetX - sourceX
  const deltaY = targetY - sourceY

  // 控制点偏移量
  const controlOffset = Math.min(Math.abs(deltaX) * 0.5, 100)

  const sourceControlX = sourceX + controlOffset
  const sourceControlY = sourceY
  const targetControlX = targetX - controlOffset
  const targetControlY = targetY

  return `M ${sourceX} ${sourceY} C ${sourceControlX} ${sourceControlY}, ${targetControlX} ${targetControlY}, ${targetX} ${targetY}`
})

// 边的颜色
const edgeColor = computed(() => {
  if (props.selected) {
    return '#409eff'
  }

  if (isHovered.value || isScissorsHovered.value) {
    return '#409eff'
  }

  switch (props.data?.type) {
    case 'data':
      return '#67c23a'
    case 'control':
    default:
      return '#a6a9af'
  }
})

// 线条宽度
const strokeWidth = computed(() => {
  if (props.selected) {
    return 5
  }
  if (isHovered.value || isScissorsHovered.value) {
    return 4
  }
  return 3
})

// 虚线样式
const strokeDasharray = computed(() => {
  if (props.data?.type === 'data') {
    return '5,5'
  }
  return 'none'
})

// 箭头标记
const markerEnd = computed(() => {
  return 'url(#arrowhead)'
})

// 标签位置
const labelX = computed(() => {
  return (props.sourceX + props.targetX) / 2
})

const labelY = computed(() => {
  return (props.sourceY + props.targetY) / 2
})

// 显示剪刀的条件
const showScissors = computed(() => {
  return props.selected || isHovered.value || isScissorsHovered.value
})

// 事件处理
const handleDelete = (event: Event) => {
  event.stopPropagation()
  emit('delete', props.id)
}

const handleEdgeClick = (event: Event) => {
  event.stopPropagation()
  emit('click', props.id)
}

const onMouseEnter = () => {
  isHovered.value = true
}

const onMouseLeave = () => {
  isHovered.value = false
}

const onScissorsEnter = () => {
  isScissorsHovered.value = true
}

const onScissorsLeave = () => {
  isScissorsHovered.value = false
}
</script>

<style scoped>
.workflow-edge-hitbox {
  cursor: pointer;
}

.workflow-edge-path {
  transition: all 0.2s ease;
  cursor: pointer;
  pointer-events: none; /* 让hitbox处理点击 */
}

.workflow-edge-selection {
  pointer-events: none;
  transition: all 0.2s ease;
}

.workflow-edge-label {
  pointer-events: none;
}

.edge-label-content {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  text-align: center;
  color: #606266;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.workflow-edge-controls {
  pointer-events: auto;
}

.edge-scissors-btn {
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  margin: 4px;
  border-radius: 50%;
  background: #ffffff;
  color: #f56c6c;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 2px solid #f56c6c;
  transition: all 0.2s ease;
  position: relative;
}

.edge-scissors-btn:hover {
  background: #f56c6c;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 1px 1px rgba(245, 108, 108, 0.3);
}

.edge-scissors-btn:active {
  transform: scale(0.95);
}

.edge-scissors-btn svg {
  transition: transform 0.2s ease;
}

.edge-scissors-btn:hover svg {
  transform: rotate(20deg);
}

.edge-scissors-btn.is-hovered {
  animation: scissorsPulse 1.5s infinite;
}

@keyframes scissorsPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 1px 1px rgba(245, 108, 108, 0.25);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }
}
</style>
