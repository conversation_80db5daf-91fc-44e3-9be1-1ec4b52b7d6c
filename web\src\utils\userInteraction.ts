/**
 * 用户交互处理工具
 * 在工作流执行前处理用户输入组件
 */

import { ElMessageBox, ElMessage } from 'element-plus'
import type { WorkflowNode } from '@/stores/workflow'

export interface UserInputRequest {
  nodeId: string
  variableName: string
  promptMessage: string
  defaultValue?: string
  inputType: 'text' | 'password' | 'number' | 'email'
  required: boolean
  hidden: boolean
}

export interface UserChoiceRequest {
  nodeId: string
  variableName: string
  promptMessage: string
  choices: string[]
  defaultChoice?: string
}

export interface UserConfirmRequest {
  nodeId: string
  variableName: string
  promptMessage: string
  defaultAnswer: boolean
}

export interface UserInteractionResult {
  [variableName: string]: any
}

/**
 * 处理工作流中的用户交互组件
 */
export class UserInteractionHandler {
  /**
   * 扫描工作流节点，找出所有用户交互组件
   */
  static findUserInteractionNodes(nodes: WorkflowNode[]): {
    inputNodes: WorkflowNode[]
    choiceNodes: WorkflowNode[]
    confirmNodes: WorkflowNode[]
  } {
    const inputNodes: WorkflowNode[] = []
    const choiceNodes: WorkflowNode[] = []
    const confirmNodes: WorkflowNode[] = []

    nodes.forEach((node) => {
      switch (node.data.componentType) {
        case 'user_input':
          inputNodes.push(node)
          break
        case 'user_choice':
          choiceNodes.push(node)
          break
        case 'user_confirm':
          confirmNodes.push(node)
          break
      }
    })

    return { inputNodes, choiceNodes, confirmNodes }
  }

  /**
   * 处理所有用户交互，返回用户输入的值
   */
  static async handleUserInteractions(nodes: WorkflowNode[]): Promise<UserInteractionResult> {
    const { inputNodes, choiceNodes, confirmNodes } = this.findUserInteractionNodes(nodes)
    const results: UserInteractionResult = {}

    // 处理用户输入
    for (const node of inputNodes) {
      const result = await this.handleUserInput(node)
      if (result) {
        results[result.variableName] = result.value
      }
    }

    // 处理用户选择
    for (const node of choiceNodes) {
      const result = await this.handleUserChoice(node)
      if (result) {
        results[result.variableName] = result.value
      }
    }

    // 处理用户确认
    for (const node of confirmNodes) {
      const result = await this.handleUserConfirm(node)
      if (result) {
        results[result.variableName] = result.value
      }
    }

    return results
  }

  /**
   * 处理用户输入组件
   */
  static async handleUserInput(node: WorkflowNode): Promise<{
    variableName: string
    value: any
  } | null> {
    const config = node.data.config
    const variableName = config.variable_name || '' // 变量名可选
    const promptMessage = config.prompt_message || '请输入'
    const defaultValue = config.default_value || ''
    const inputType = config.input_type || 'text'
    const required = config.required !== false
    const multiline = config.multiline === true

    try {
      let inputValue: string

      if (inputType === 'password' || config.hidden) {
        // 密码输入
        const { value } = await ElMessageBox.prompt(promptMessage, '用户输入', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'password',
          inputValue: defaultValue,
          inputValidator: required
            ? (value: string) => {
                if (!value || value.trim() === '') {
                  return '输入不能为空'
                }
                return true
              }
            : undefined,
        })
        inputValue = value
      } else if (multiline || inputType === 'multiline') {
        // 多行文本输入 - 使用自定义对话框
        inputValue = await this.showMultilineInputDialog(promptMessage, defaultValue, required)
      } else {
        // 普通单行输入
        const { value } = await ElMessageBox.prompt(promptMessage, '用户输入', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: inputType === 'number' ? 'number' : 'text',
          inputValue: defaultValue,
          inputValidator: required
            ? (value: string) => {
                if (!value || value.trim() === '') {
                  return '输入不能为空'
                }
                if (inputType === 'email') {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                  if (!emailRegex.test(value)) {
                    return '请输入有效的邮箱地址'
                  }
                }
                return true
              }
            : undefined,
        })
        inputValue = value
      }

      // 类型转换
      let finalValue: any = inputValue
      if (inputType === 'number') {
        finalValue = Number(inputValue)
        if (isNaN(finalValue)) {
          throw new Error('输入的不是有效数字')
        }
      }

      // 如果没有设置变量名，则不存储变量，只显示输入结果
      if (variableName) {
        ElMessage.success(`已获取用户输入并存储到变量: ${variableName} = ${finalValue}`)
        return { variableName, value: finalValue }
      } else {
        ElMessage.success(`已获取用户输入: ${finalValue}`)
        return { variableName: `user_input_${Date.now()}`, value: finalValue } // 临时变量名
      }
    } catch (error) {
      if (error === 'cancel') {
        ElMessage.info('用户取消了输入')
        return null
      }
      ElMessage.error(`获取用户输入失败: ${error}`)
      throw error
    }
  }

  /**
   * 显示多行文本输入对话框
   */
  static async showMultilineInputDialog(
    promptMessage: string,
    defaultValue: string,
    required: boolean,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      // 创建一个自定义的多行输入对话框
      const dialogHtml = `
        <div style="margin: 20px 0;">
          <p style="margin-bottom: 10px; color: #606266;">${promptMessage}</p>
          <textarea
            id="multiline-input"
            style="width: 100%; height: 120px; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px; resize: vertical; font-family: inherit;"
            placeholder="请输入内容..."
          >${defaultValue}</textarea>
        </div>
      `

      ElMessageBox({
        title: '用户输入',
        message: dialogHtml,
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const textarea = document.getElementById('multiline-input') as HTMLTextAreaElement
            const value = textarea?.value || ''

            if (required && (!value || value.trim() === '')) {
              ElMessage.error('输入不能为空')
              return false
            }

            resolve(value)
            done()
          } else {
            reject('cancel')
            done()
          }
        },
      }).catch(() => {
        reject('cancel')
      })

      // 聚焦到文本框
      setTimeout(() => {
        const textarea = document.getElementById('multiline-input') as HTMLTextAreaElement
        if (textarea) {
          textarea.focus()
          textarea.setSelectionRange(textarea.value.length, textarea.value.length)
        }
      }, 100)
    })
  }

  /**
   * 处理用户选择组件
   */
  static async handleUserChoice(node: WorkflowNode): Promise<{
    variableName: string
    value: any
  } | null> {
    const config = node.data.config
    const variableName = config.variable_name
    const promptMessage = config.prompt_message || '请选择'
    const choices = config.choices ? config.choices.split(',').map((c: string) => c.trim()) : []
    const defaultChoice = config.default_choice

    if (choices.length === 0) {
      ElMessage.error('没有可选择的选项')
      return null
    }

    try {
      const { value } = await ElMessageBox.confirm(
        `${promptMessage}\n\n可选项：${choices.join(', ')}`,
        '用户选择',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        },
      )

      // 简化版本：使用默认选择或第一个选项
      const selectedValue = defaultChoice || choices[0]

      ElMessage.success(`用户选择: ${variableName} = ${selectedValue}`)
      return { variableName, value: selectedValue }
    } catch (error) {
      if (error === 'cancel') {
        ElMessage.info('用户取消了选择')
        return null
      }
      ElMessage.error(`获取用户选择失败: ${error}`)
      throw error
    }
  }

  /**
   * 处理用户确认组件
   */
  static async handleUserConfirm(node: WorkflowNode): Promise<{
    variableName: string
    value: any
  } | null> {
    const config = node.data.config
    const variableName = config.variable_name
    const promptMessage = config.prompt_message || '确定要继续吗？'
    const defaultAnswer = config.default_answer !== false

    try {
      await ElMessageBox.confirm(promptMessage, '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      ElMessage.success(`用户确认: ${variableName} = true`)
      return { variableName, value: true }
    } catch (error) {
      if (error === 'cancel') {
        ElMessage.info(`用户取消: ${variableName} = false`)
        return { variableName, value: false }
      }
      ElMessage.error(`获取用户确认失败: ${error}`)
      throw error
    }
  }

  /**
   * 将用户交互结果注入到工作流节点配置中
   */
  static injectUserInputs(
    nodes: WorkflowNode[],
    userInputs: UserInteractionResult,
  ): WorkflowNode[] {
    return nodes.map((node) => {
      const newNode = { ...node }

      // 如果是用户交互组件，将用户输入的值设置为默认值
      if (['user_input', 'user_choice', 'user_confirm'].includes(<string>node.data.componentType)) {
        const variableName = node.data.config.variable_name
        if (variableName && userInputs[variableName] !== undefined) {
          newNode.data = {
            ...node.data,
            config: {
              ...node.data.config,
              user_provided_value: userInputs[variableName],
            },
          }
        }
      }

      return newNode
    })
  }

  /**
   * 检查工作流是否包含用户交互组件
   */
  static hasUserInteractions(nodes: WorkflowNode[]): boolean {
    return nodes.some((node) =>
      ['user_input', 'user_choice', 'user_confirm'].includes(<string>node.data.componentType),
    )
  }
}
