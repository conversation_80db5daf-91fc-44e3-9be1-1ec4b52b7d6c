{"name": "wimTask", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint  --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.6.2", "@vueup/vue-quill": "^1.2.0", "axios": "^1.6.2", "crypto-js": "^4.2.0", "element-plus": "^2.10.2", "jsencrypt": "^3.3.2", "less": "^4.3.0", "moment": "^2.30.1", "pinia": "^3.0.1", "quill-image-resize": "^3.0.9", "react-dom": "^19.1.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "snowflake-id": "^1.1.0", "vue": "^3.5.13", "vue-json-viewer": "^3.0.4", "vue-qr": "^4.0.9", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass-embedded": "^1.89.2", "terser": "^5.42.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.2.4", "vue-tsc": "^2.2.8", "wait-on": "^8.0.3"}}