export const TaskActions = [{
    "label": "数据",
    "value": "data",
    "disabled": false,
    "children": [{
        "label": "查询业务数据",
        "value": "model",
        "disabled": false,
        "inputs": [["category", "类别"]],
    }, {
        "label": "查询监测数据",
        "value": "query_monitoring_data",
        "disabled": true,
        "inputs": [["sensor", "传感器"], ["interval", "数据间隔"]],
    }]
}, {
    "label": "提醒",
    "value": "remind",
    "disabled": false,
    "children": [{
        "label": "消息通知",
        "value": "msg",
        "disabled": false,
        "inputs": [["object", "通知对象"]], // todo 通知方式 标题 通知内容
    }, {
        "label": "拨打电话",
        "value": "call_phone",
        "disabled": true,
        "inputs": [["object", "通知对象"], ["content", "通知内容"]]
    }, {
        // 弹窗提醒
        "label": "弹窗提醒",
        "value": "popup_remind",
        "disabled": true,
        "inputs": [["object", "通知对象"], ["title", "标题"], ["content", "通知内容"], ["voice", "声音"], ["duration", "持续时间"], ["position", "显示位置"]],
    }, {
        // 任务栏通知
        "label": "任务栏通知",
        "value": "taskbar_notification",
        "disabled": true,
        "inputs": [],
    }, {
        // 悬浮提示
        "label": "悬浮提示",
        "value": "floating_tip",
        "disabled": true,
        "inputs": [],
    }, {
        // 报时
        "label": "报时",
        "value": "time_report",
        "disabled": true,
        "inputs": [],
    }, {
        // 挡屏休息
        "label": "挡屏休息",
        "value": "screen_rest",
        "disabled": true,
        "inputs": [],
    }, {
        // 倒计时提醒
        "label": "倒计时提醒",
        "value": "countdown_remind",
        "disabled": true,
        "inputs": [],
    }, {
        // 正计时提醒
        "label": "正计时提醒",
        "value": "positive_counting_remind",
        "disabled": true,
        "inputs": [],
    }]
}, {
    "label": "网页",
    "value": "web",
    "disabled": false,
    "children": [{
        "label": "打开网页",
        "value": "open_web",
        "disabled": false,
        "inputs": [["type", "类型"], ["code", "应用编码"], ["url", "页面地址"]],
    }, {
        "label": "页面登录",
        "value": "login_web",
        "disabled": true,
        "inputs": [["url", "地址"], ["acc", "账号"], ["pwd", "密码"]],
    }, {
        "label": "网页搜索",
        "value": "search_web",
        "disabled": true,
        "inputs": [["content", "内容"], ["browser", "浏览器"], ["searchEngine", "搜索引擎"]],
    }, {
        "label": "页面点击",
        "value": "close_web",
        "disabled": true,
        "inputs": [["element", "元素内容"]]
    }, {
        "label": "获取页面内容",
        "value": "get_web_content",
        "disabled": true,
        "inputs": [["url", "页面地址"]]
    }]
}, {
    "label": "工具",
    "value": "tools",
    "disabled": false,
    "children": [{
        "label": "截图",
        "value": "screenshot",
        "disabled": true,
        "inputs": []
    }, {
        // 接口调用
        "label": "接口调用",
        "value": "http_call",
        "disabled": false,
        "inputs": [["url", "接口地址"], ["method", "请求方法"], ["headers", "请求头"], ["params", "请求参数"]],
    }, {
        // 派发工单
        "label": "派发工单",
        "value": "dispatch_ticket",
        "disabled": true,
        "inputs": [],
    }, {
        // 发送邮件
        "label": "发送邮件",
        "value": "send_email",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 程序
    "label": "程序",
    "value": "program",
    "disabled": false,
    "children": [{
        // 运行程序
        "label": "运行程序",
        "value": "run_program",
        "disabled": true,
        "inputs": [],
    }, {
        // 打开应用
        "label": "打开应用",
        "value": "open_application",
        "disabled": true,
        "inputs": [],
    }, {
        // 运行cmd命令
        "label": "运行cmd命令",
        "value": "run_cmd_command",
        "disabled": true,
        "inputs": [],
    }, {
        // 运行PowerShell命令
        "label": "运行PowerShell命令",
        "value": "run_powershell_command",
        "disabled": true,
        "inputs": [],
    }, {
        // 运行Python脚本
        "label": "运行Python脚本",
        "value": "run_python_script",
        "disabled": true,
        "inputs": [],
    }, {
        // 运行AutoHotkey脚本
        "label": "运行AutoHotkey脚本",
        "value": "run_autohotkey_script",
        "disabled": true,
        "inputs": [],
    }, {
        // 结束进程
        "label": "结束进程",
        "value": "end_process",
        "disabled": true,
        "inputs": [],
    }, {
        // 重启进程
        "label": "重启进程",
        "value": "restart_process",
        "disabled": true,
        "inputs": [],
    }, {
        // 冻结|解冻进程
        "label": "冻结|解冻进程",
        "value": "freeze_unfreeze_process",
        "disabled": true,
        "inputs": [],
    }, {
        // 启动服务
        "label": "启动服务",
        "value": "start_service",
        "disabled": true,
        "inputs": [],
    }, {
        // 停止服务
        "label": "停止服务",
        "value": "stop_service",
        "disabled": true,
        "inputs": [],
    }, {
        // 重启服务
        "label": "重启服务",
        "value": "restart_service",
        "disabled": true,
        "inputs": [],
    }]
}, {
    "label": "AI",
    "value": "ai",
    "disabled": false,
    "children": [{
        // 图片分析
        "label": "图片分析",
        "value": "ai_analyze_image",
        "disabled": true,
        "inputs": [["pic", "图片"], ["tips", "提示词"]],
    }, {
        // 联网搜索
        "label": "联网搜索",
        "value": "ai_search_web",
        "disabled": true,
        "inputs": [["content", "内容"]],
    }, {
        // 文档阅读
        "label": "文档阅读",
        "value": "ai_read_doc",
        "disabled": true,
        "inputs": [["doc", "文档"], ["tips", "提示词"]],
    }, {
        // AI分析
        "label": "AI分析",
        "value": "ai_analyze",
        "disabled": true,
        "inputs": [["preSteps", "前置步骤"], ["tips", "提示词"]],
    }]
}, {
    // 电源
    "label": "电源",
    "value": "power",
    "disabled": false,
    "children": [{
        // 关机
        "label": "关机",
        "value": "shutdown",
        "disabled": true,
        "inputs": [],
    }, {
        // 关机对话框
        "label": "关机对话框",
        "value": "shutdown_dialog",
        "disabled": true,
        "inputs": [],
    }, {
        // 重启
        "label": "重启",
        "value": "restart",
        "disabled": true,
        "inputs": [],
    }, {
        // 睡眠
        "label": "睡眠",
        "value": "sleep",
        "disabled": true,
        "inputs": [],
    }, {
        // 休眠
        "label": "休眠",
        "value": "hibernate",
        "disabled": true,
        "inputs": [],
    }, {
        // 注销
        "label": "注销",
        "value": "logout",
        "disabled": true,
        "inputs": [],
    }, {
        // 锁定
        "label": "锁定",
        "value": "lock",
        "disabled": true,
        "inputs": [],
    }, {
        // 唤醒电脑
        "label": "唤醒电脑",
        "value": "wakeup",
        "disabled": true,
        "inputs": [],
    }, {
        // 网络唤醒
        "label": "网络唤醒",
        "value": "network_wakeup",
        "disabled": true,
        "inputs": [],
    }, {
        // 远程关机
        "label": "远程关机",
        "value": "remote_shutdown",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 备份
    "label": "备份",
    "value": "backup",
    "disabled": false,
    "children": [{
        // 备份文件
        "label": "备份文件",
        "value": "backup_file",
        "disabled": true,
        "inputs": [],
    }, {
        // 备份文件夹
        "label": "备份文件夹",
        "value": "backup_folder",
        "disabled": true,
        "inputs": [],
    }, {
        // 备份系统目录
        "label": "备份系统目录",
        "value": "backup_system_directory",
        "disabled": true,
        "inputs": [],
    }, {
        // 备份注册表
        "label": "备份注册表",
        "value": "backup_registry",
        "disabled": true,
        "inputs": [],
    }, {
        // 同步文件夹
        "label": "同步文件夹",
        "value": "sync_folder",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 声音
    "label": "声音",
    "value": "sound",
    "disabled": false,
    "children": [{
        // 增加系统音量
        "label": "增加系统音量",
        "value": "increase_system_volume",
        "disabled": true,
        "inputs": [],
    }, {
        // 减小系统音量
        "label": "减小系统音量",
        "value": "decrease_system_volume",
        "disabled": true,
        "inputs": [],
    }, {
        // 设置系统音量
        "label": "设置系统音量",
        "value": "set_system_volume",
        "disabled": true,
        "inputs": [],
    }, {
        // 显示系统当前音量
        "label": "显示系统当前音量",
        "value": "show_system_volume",
        "disabled": true,
        "inputs": [],
    }, {
        // 开启系统禁音
        "label": "开启系统禁音",
        "value": "mute_system",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭系统禁音
        "label": "关闭系统禁音",
        "value": "unmute_system",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换系统静音
        "label": "切换系统静音",
        "value": "toggle_system_mute",
        "disabled": true,
        "inputs": [],
    }, {
        // 打开音量合成器
        "label": "打开音量合成器",
        "value": "open_volume_computer",
        "disabled": true,
        "inputs": [],
    }, {
        // 静音进程
        "label": "静音进程",
        "value": "mute_process",
        "disabled": true,
        "inputs": [],
    }, {
        // 静音当前窗口
        "label": "静音当前窗口",
        "value": "mute_current_window",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 窗口
    "label": "窗口",
    "value": "window",
    "disabled": false,
    "children": [{
        // 最小化窗口
        "label": "最小化窗口",
        "value": "minimize_window",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭窗口
        "label": "关闭窗口",
        "value": "close_window",
        "disabled": true,
        "inputs": [],
    }, {
        // 结束窗口及进程
        "label": "结束窗口及进程",
        "value": "end_window_and_process",
        "disabled": true,
        "inputs": [],
    }, {
        // 隐藏窗口到托盘
        "label": "隐藏窗口到托盘",
        "value": "hide_window_to_tray",
        "disabled": true,
        "inputs": [],
    }, {
        // 隐藏|显示窗口
        "label": "隐藏|显示窗口",
        "value": "hide_show_window",
        "disabled": true,
        "inputs": [],
    }, {
        // 激活窗口
        "label": "激活窗口",
        "value": "activate_window",
        "disabled": true,
        "inputs": [],
    }, {
        // 置顶窗口
        "label": "置顶窗口",
        "value": "top_window",
        "disabled": true,
        "inputs": [],
    }, {
        // 设置窗口位置和大小
        "label": "设置窗口位置和大小",
        "value": "set_window_position_and_size",
        "disabled": true,
        "inputs": [],
    }, {
        // 设置窗口状态(最小|最大|还原)
        "label": "设置窗口状态(最小|最大|还原)",
        "value": "set_window_state",
        "disabled": true,
        "inputs": [],
    }, {
        // 设置窗口透明度
        "label": "设置窗口透明度",
        "value": "set_window_transparency",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取窗口位置
        "label": "获取窗口位置",
        "value": "get_window_position",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取窗口大小
        "label": "获取窗口大小",
        "value": "get_window_size",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取窗口所属进程信息
        "label": "获取窗口所属进程信息",
        "value": "get_window_process_info",
        "disabled": true,
        "inputs": [],
    }, {
        // 显示当前窗口信息
        "label": "显示当前窗口信息",
        "value": "show_current_window_info",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 显示
    "label": "显示",
    "value": "display",
    "disabled": false,
    "children": [{
        // 更换墙纸
        "label": "更换墙纸",
        "value": "change_wallpaper",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换明暗主题色
        "label": "切换明暗主题色",
        "value": "toggle_light_dark_theme",
        "disabled": true,
        "inputs": [],
    }, {
        // 激活显示器
        "label": "激活显示器",
        "value": "activate_display",
        "disabled": true,
        "inputs": [],
    }, {
        // 睡眠显示器
        "label": "睡眠显示器",
        "value": "sleep_display",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭|打开显示器电源
        "label": "关闭|打开显示器电源",
        "value": "close_open_display_power",
        "disabled": true,
        "inputs": [],
    }, {
        // 调节显示器亮度
        "label": "调节显示器亮度",
        "value": "adjust_display_brightness",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换显示器配置
        "label": "切换显示器配置",
        "value": "toggle_display_configuration",
        "disabled": true,
        "inputs": [],
    }, {
        // 激活屏幕
        "label": "激活屏幕",
        "value": "activate_screen",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭屏幕
        "label": "关闭屏幕",
        "value": "close_screen",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用屏幕
        "label": "禁用屏幕",
        "value": "disable_screen",
        "disabled": true,
        "inputs": [],
    }, {
        // 启用屏幕
        "label": "启用屏幕",
        "value": "enable_screen",
        "disabled": true,
        "inputs": [],
    }],
}, {
    // 媒体
    "label": "媒体",
    "value": "media",
    "disabled": false,
    "children": [{
        // 播放声音
        "label": "播放声音",
        "value": "play_sound",
        "disabled": true,
        "inputs": [],
    }, {
        // 播放控制
        "label": "播放控制",
        "value": "play_control",
        "disabled": true,
        "inputs": [],
    }, {
        // 屏幕截图
        "label": "屏幕截图",
        "value": "screen_screenshot",
        "disabled": true,
        "inputs": [],
    }, {
        // 屏幕录像
        "label": "屏幕录像",
        "value": "screen_recording",
        "disabled": true,
        "inputs": [],
    }, {
        // 摄像头拍照
        "label": "摄像头拍照",
        "value": "camera_take_photo",
        "disabled": true,
        "inputs": [],
    }, {
        // 摄像头录像
        "label": "摄像头录像",
        "value": "camera_recording",
        "disabled": true,
        "inputs": [],
    }, {
        // 麦克风录音
        "label": "麦克风录音",
        "value": "microphone_recording",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 文件
    "label": "文件",
    "value": "file",
    "disabled": false,
    "children": [{
        // 打开文件
        "label": "打开文件",
        "value": "open_file",
        "disabled": true,
        "inputs": [],
    }, {
        // 打开文件夹
        "label": "打开文件夹",
        "value": "open_folder",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭文件夹
        "label": "关闭文件夹",
        "value": "close_folder",
        "disabled": true,
        "inputs": [],
    }, {
        // 解压缩文件
        "label": "解压缩文件",
        "value": "unzip_file",
        "disabled": true,
        "inputs": [],
    }, {
        // 创建文件夹
        "label": "创建文件夹",
        "value": "create_folder",
        "disabled": true,
        "inputs": [],
    }, {
        // 创建快捷方式
        "label": "创建快捷方式",
        "value": "create_shortcut",
        "disabled": true,
        "inputs": [],
    }, {
        // 创建符号链接
        "label": "创建符号链接",
        "value": "create_symbolic_link",
        "disabled": true,
        "inputs": [],
    }, {
        // 删除文件
        "label": "删除文件",
        "value": "delete_file",
        "disabled": true,
        "inputs": [],
    }, {
        // 删除目录
        "label": "删除目录",
        "value": "delete_directory",
        "disabled": true,
        "inputs": [],
    }, {
        // 重命名文件
        "label": "重命名文件",
        "value": "rename_file",
        "disabled": true,
        "inputs": [],
    }, {
        // 重命名目录
        "label": "重命名目录",
        "value": "rename_directory",
        "disabled": true,
        "inputs": [],
    }, {
        // ini文件修改
        "label": "ini文件修改",
        "value": "ini_file_modification",
        "disabled": true,
        "inputs": [],
    }, {
        // ini文件读取
        "label": "ini文件读取",
        "value": "ini_file_reading",
        "disabled": true,
        "inputs": [],
    }, {
        // 文本文件修改
        "label": "文本文件修改",
        "value": "text_file_modification",
        "disabled": true,
        "inputs": [],
    }, {
        // 文本文件读取
        "label": "文本文件读取",
        "value": "text_file_reading",
        "disabled": true,
        "inputs": [],
    }, {
        // 打开文件选择框
        "label": "打开文件选择框",
        "value": "open_file_selector",
        "disabled": true,
        "inputs": [],
    }, {
        // 打开目录选择框
        "label": "打开目录选择框",
        "value": "open_directory_selector",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取文件大小
        "label": "获取文件大小",
        "value": "get_file_size",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取文件夹大小
        "label": "获取文件夹大小",
        "value": "get_folder_size",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取文件时间
        "label": "获取文件时间",
        "value": "get_file_time",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取驱动器空间
        "label": "获取驱动器空间",
        "value": "get_drive_space",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 系统
    "label": "系统",
    "value": "system",
    "disabled": false,
    "children": [{
        // 打开系统程序
        "label": "打开系统程序",
        "value": "open_system_program",
        "disabled": true,
        "inputs": [], // todo 我的电脑、资源管理器、回收站、控制面板、命令提示符、任务管理器、服务、程序和功能、设备管理器、计算机管理、事件查看器、远程桌面连接、网络和共享中心、网络连接、注册表编辑器、画图、记事本、截图工具、屏幕键盘
    }, {
        // 切换任务栏自动隐藏
        "label": "切换任务栏自动隐藏",
        "value": "toggle_taskbar_auto_hide",
        "disabled": true,
        "inputs": [],
    }, {
        // 重启资源管理器
        "label": "重启资源管理器",
        "value": "restart_explorer",
        "disabled": true,
        "inputs": [],
    }, {
        // 注册表修改
        "label": "注册表修改",
        "value": "registry_modification",
        "disabled": true,
        "inputs": [],
    }, {
        // 注册表读取
        "label": "注册表读取",
        "value": "registry_reading",
        "disabled": true,
        "inputs": [],
    }, {
        // 剪切板写入
        "label": "剪切板写入",
        "value": "clipboard_writing",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用|恢复任务管理器
        "label": "禁用|恢复任务管理器",
        "value": "disable_restore_task_manager",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用|恢复锁定电脑功能
        "label": "禁用|恢复锁定电脑功能",
        "value": "disable_restore_lock_computer",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用|恢复注册表编辑器
        "label": "禁用|恢复注册表编辑器",
        "value": "disable_restore_registry_editor",
        "disabled": true,
        "inputs": [],
    }, {
        // 阻止|恢复屏幕保护程序
        "label": "阻止|恢复屏幕保护程序",
        "value": "block_restore_screen_saver",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 硬件
    "label": "硬件",
    "value": "hardware",
    "disabled": false,
    "children": [{
        // 显示磁盘可用空间
        "label": "显示磁盘可用空间",
        "value": "display_disk_available_space",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用触摸板
        "label": "禁用触摸板",
        "value": "disable_touchpad",
        "disabled": true,
        "inputs": [],
    }, {
        // 启用触摸板
        "label": "启用触摸板",
        "value": "enable_touchpad",
        "disabled": true,
        "inputs": [],
    }, {
        // 设置鼠标灵敏度
        "label": "设置鼠标灵敏度",
        "value": "set_mouse_sensitivity",
        "disabled": true,
        "inputs": [],
    }, {
        // 蓝牙开关
        "label": "蓝牙开关",
        "value": "bluetooth_switch",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换声音输出设备
        "label": "切换声音输出设备",
        "value": "toggle_sound_output_device",
        "disabled": true,
        "inputs": [],
    }, {
        // 弹出驱动器
        "label": "弹出驱动器",
        "value": "eject_drive",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换省电配置
        "label": "切换省电配置",
        "value": "toggle_power_saving_configuration",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用|启用硬件设备
        "label": "禁用|启用硬件设备",
        "value": "disable_enable_hardware_device",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 输入
    "label": "输入",
    "value": "input",
    "disabled": false,
    "children": [{
        // 输入文本
        "label": "输入文本",
        "value": "input_text",
        "disabled": true,
        "inputs": [],
    }, {
        // 发送按键
        "label": "发送按键",
        "value": "send_key",
        "disabled": true,
        "inputs": [],
    }, {
        // 按键按下|弹起
        "label": "按键按下|弹起",
        "value": "key_press_release",
        "disabled": true,
        "inputs": [],
    }, {
        // 屏幕按键
        "label": "屏幕按键",
        "value": "screen_key",
        "disabled": true,
        "inputs": [],
    }, {
        // 鼠标点击
        "label": "鼠标点击",
        "value": "mouse_click",
        "disabled": true,
        "inputs": [],
    }, {
        // 鼠标按下|弹起
        "label": "鼠标按下|弹起",
        "value": "mouse_down_up",
        "disabled": true,
        "inputs": [],
    }, {
        // 鼠标移动
        "label": "鼠标移动",
        "value": "mouse_move",
        "disabled": true,
        "inputs": [],
    }, {
        // 鼠标滚轮
        "label": "鼠标滚轮",
        "value": "mouse_wheel",
        "disabled": true,
        "inputs": [],
    }, {
        // 发送Wimdows消息
        "label": "发送Wimdows消息",
        "value": "send_windows_message",
        "disabled": true,
        "inputs": [],
    }, {
        // 切换输入语言
        "label": "切换输入语言",
        "value": "toggle_input_language",
        "disabled": true,
        "inputs": [],
    }, {
        // 录制键盘鼠标动作
        "label": "录制键盘鼠标动作",
        "value": "record_keyboard_mouse_action",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 清理
    "label": "清理",
    "value": "clean",
    "disabled": false,
    "children": [{
        // 清理目录
        "label": "清理目录",
        "value": "clean_directory",
        "disabled": true,
        "inputs": [],
    }, {
        // 清理内存
        "label": "清理内存",
        "value": "clean_memory",
        "disabled": true,
        "inputs": [],
    }, {
        // 清理临时文件
        "label": "清理临时文件",
        "value": "clean_temporary_files",
        "disabled": true,
        "inputs": [],
    }, {
        // 清理最近文档记录
        "label": "清理最近文档记录",
        "value": "clean_recent_document_records",
        "disabled": true,
        "inputs": [],
    }, {
        // 清空回收站
        "label": "清空回收站",
        "value": "empty_recycle_bin",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 网络
    "label": "网络",
    "value": "network",
    "disabled": false,
    "children": [{
        // 禁用网络连接
        "label": "禁用网络连接",
        "value": "disable_network_connection",
        "disabled": true,
        "inputs": [],
    }, {
        // 启用网络连接
        "label": "启用网络连接",
        "value": "enable_network_connection",
        "disabled": true,
        "inputs": [],
    }, {
        // 时间同步
        "label": "时间同步",
        "value": "time_synchronization",
        "disabled": true,
        "inputs": [],
    }, {
        // 链接无线网络
        "label": "链接无线网络",
        "value": "link_wireless_network",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取无线连接SSID
        "label": "获取无线连接SSID",
        "value": "get_wireless_connection_ssid",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取无限链接名称
        "label": "获取无限链接名称",
        "value": "get_wireless_link_name",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取网卡地址
        "label": "获取网卡地址",
        "value": "get_network_card_address",
        "disabled": true,
        "inputs": [],
    }, {
        // 获取网卡MAC地址
        "label": "获取网卡MAC地址",
        "value": "get_network_card_mac_address",
        "disabled": true,
        "inputs": [],
    }]
}, {
    // 内置
    "label": "内置",
    "value": "built_in",
    "disabled": false,
    "children": [{
        // 切换彩色主题
        "label": "切换彩色主题",
        "value": "toggle_colorful_theme",
        "disabled": true,
        "inputs": [],
    }, {
        // 开启禁音模式
        "label": "开启禁音模式",
        "value": "enable_silence_mode",
        "disabled": true,
        "inputs": [],
    }, {
        // 关闭禁音模式
        "label": "关闭禁音模式",
        "value": "disable_silence_mode",
        "disabled": true,
        "inputs": [],
    }, {
        // 恢复热健
        "label": "恢复热健",
        "value": "restore_hotkey",
        "disabled": true,
        "inputs": [],
    }, {
        // 禁用热键
        "label": "禁用热键",
        "value": "disable_hotkey",
        "disabled": true,
        "inputs": [],
    }, {
        // 启动|禁用任务计划
        "label": "启动|禁用任务计划",
        "value": "start_disable_task_schedule",
        "disabled": true,
        "inputs": [],
    }, {
        // 运行|终止任务
        "label": "运行|终止任务",
        "value": "run_terminate_task",
        "disabled": true,
        "inputs": [],
    }, {
        // 退出本软件
        "label": "退出本软件",
        "value": "exit_this_software",
        "disabled": true,
        "inputs": [],
    }, {
        // 重启本软件
        "label": "重启本软件",
        "value": "restart_this_software",
        "disabled": true,
        "inputs": [],
    }]
}]

export const FormatTaskActionName = (v) => {
    // 根据传入的v，递归从TaskActions中找到对应的label
    const findLabel = (arr, v) => {
        for (let item of arr) {
            if (item.value === v) {
                return item.label;
            }
            if (item.children) {
                const label = findLabel(item.children, v);
                if (label) {
                    return label;
                }
            }
        }
    }
    return findLabel(TaskActions, v) || v;
}