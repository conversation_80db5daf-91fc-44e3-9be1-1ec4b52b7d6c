# -*- coding: utf-8 -*-

#from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from robotlibcore import keyword
from typing import Dict, List, Optional, Any,Set
import logging
import json
import requests
import base64
import re
import logging
from config import settings  # 绝对导入
from config import globals
import requests
from datetime import datetime, timedelta
import statistics
from loguru import logger

class OutputItem:
    water_outage_volume: Optional[float] = None #停水水量
    closed_valves: Optional[any] = None #关闭阀门
    water_outage_users: Optional[any] = None #停水用户
    water_outage_pipelines: Optional[any] = None #停水管道
    water_outage_hydrants: Optional[any] = None #停水消火栓
    suggested_opened_valves: Optional[any] = None #建议开启阀门
    water_flow_pipelines_after_valve_opening: Optional[any] = None #开启阀门后通水管道

    def __init__(self,water_outage_volume:float,closed_valves:any,water_outage_users:any,water_outage_pipelines:any,water_outage_hydrants:any,suggested_opened_valves:any,water_flow_pipelines_after_valve_opening:any):
        self.water_outage_volume = water_outage_volume
        self.closed_valves = closed_valves
        self.water_outage_users = water_outage_users
        self.water_outage_pipelines = water_outage_pipelines
        self.water_outage_hydrants = water_outage_hydrants
        self.suggested_opened_valves = suggested_opened_valves
        self.water_flow_pipelines_after_valve_opening = water_flow_pipelines_after_valve_opening



        # 方法 1：添加 to_dict 方法

    def to_dict(self):
        return {
            "water_outage_volume": self.water_outage_volume,
            "closed_valves": self.closed_valves,
            "water_outage_users": self.water_outage_users,
            "water_outage_pipelines": self.water_outage_pipelines,
            "water_outage_hydrants": self.water_outage_hydrants,
            "suggested_opened_valves": self.suggested_opened_valves,
            "water_flow_pipelines_after_valve_opening": self.water_flow_pipelines_after_valve_opening
        }


@keyword("Water Shutoff Valve")
def water_shutoff_valve(input_type: str, input_data: str):
    try:

        out_item = {}

        if input_type == "管道GIS-编号":

            out_item = get_valve(input_data)

        elif input_type == "管道中心坐标":
            gis_url = "http://**************:9191/ModelService/api/GetPipeGisIDAndDiameterByCoorOutPut"

            xy_list = json.loads(input_data)
            xy_payload = {
                            "X": xy_list[0],
                            "Y": xy_list[1],
                            "Radius": 10
                         }
            # 请求头
            xy_headers = {
                "Authorization": "Bearer " + globals.token,
                "Content-Type": "application/json"
            }

            logger.info("开始请求")
            # 发起 POST 请求
            xy_response = requests.post(gis_url, headers=xy_headers, json=xy_payload)

            response_json_xy = xy_response.json().get("Response", [{}])

            gis_id = response_json_xy[0]["GisID"]

            out_item = get_valve(gis_id)
        else:
            pass

        logger.info("请求结束")
        #logger.info(out_item)

        return out_item
    except Exception as e:
        return f"water_shutoff_valve error:{str(e)}"


def custom_serializer_out_valve(obj):
    if isinstance(obj, OutputItem):
        return obj.to_dict()  # 或直接使用 obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")


def get_valve(gis_id:str):
    # 请求地址
    url = "http://**************:9191/ModelService/api/BurstPipe"
    # 请求头
    headers = {
        "Authorization": "Bearer " + globals.token,
        "Content-Type": "application/json"
    }

    payload = {
        "Id": [
            gis_id
        ],
        "TypeId": 0,
        "CanNotBeclosedId": []
    }

    logger.info("开始请求")
    # 发起 POST 请求
    response = requests.post(url, headers=headers, json=payload)

    response_json_response = response.json().get("Response", [{}])

    water_outage_volume = float(response_json_response[0]["TitleArr"][2])
    closed_valves = response_json_response[0]["ListValve"]
    water_outage_users = response_json_response[0]["ListUser"]
    water_outage_pipelines = response_json_response[0]["ListPipe"]
    water_outage_hydrants = response_json_response[0]["HydrantList"]
    suggested_opened_valves = response_json_response[0]["OpenValvesList"]
    water_flow_pipelines_after_valve_opening = response_json_response[0]["ListReleasedPipes"]

    out_item = {
        "water_outage_volume": water_outage_volume,
        "closed_valves": closed_valves,
        "water_outage_users": water_outage_users,
        "water_outage_pipelines": water_outage_pipelines,
        "water_outage_hydrants": water_outage_hydrants,
        "suggested_opened_valves": suggested_opened_valves,
        "water_flow_pipelines_after_valve_opening": water_flow_pipelines_after_valve_opening

    }

    # OutputItem(water_outage_volume, closed_valves, water_outage_users, water_outage_pipelines,
    #                       water_outage_hydrants, suggested_opened_valves,
    #                       water_flow_pipelines_after_valve_opening)

    return out_item