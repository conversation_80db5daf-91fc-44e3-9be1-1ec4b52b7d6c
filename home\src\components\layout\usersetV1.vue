<template>
    <div class="hd-userset" :class="theme">
        <slot name="extend"></slot>
        <div class="userset">
            <el-popover v-model="userSetVisible" placement="bottom-end"
                        popper-class="hd-userset-pop"
                        :width="50" trigger="hover">
                <template #reference>
                    <a class="userset-button">
                        <el-avatar :size="24" class="avatar" :icon="UserFilled"></el-avatar>
                        <span class="username">{{user.name||""}}</span>
                        <i class="iconfont icon-arrow-spread"></i>
                    </a>
                </template>
                <ul class="userset-options">
                    <li @click="setpassword">
                        <i class='iconfont icon-mima'></i>
                        <span>密码</span>
                    </li>
                    <li @click="exit">
                        <i class='iconfont icon-tuichu'></i>
                        <span>退出</span>
                    </li>
                </ul>
            </el-popover>
        </div>
        <el-dialog :title="title" :visible="visible" :show-close="closeState"
                   :close-on-press-escape="closeState" custom-class="hd-form-dialog lcs-password"
                   :close-on-click-modal="false">
            <el-form :model="crow" :rules="rules" ref="formRef"
                     label-width="80px">
                <el-form-item label="原始密码" prop="opwd">
                    <el-input v-model="crow.opwd" type="password"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="npwd">
                    <el-input v-model="crow.npwd" type="password"></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="rpwd">
                    <el-input v-model="crow.rpwd" type="password"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button v-show="closeState" size="mini" @click="visible = false">取消</el-button>
                <el-button size="mini" type="primary" @click="save()">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from "@/stores/user";
import { UserFilled } from '@element-plus/icons-vue'

// 定义 props
const props = defineProps({
    alarm: Array,
    user: Object,
    rid: String,
    alarmParams: Object,
    theme: String
});

// 定义响应式数据
const userSetVisible = ref(false);
const visible = ref(false);
const record = ref(null);
const title = ref("修改密码");
const closeState = ref(true);
const crow = ref({
    opwd: "",
    npwd: "",
    rpwd: ""
});
// 校验函数
const checkPwd = (rule, value, callback) => {
    let record = {
        pwd: encodepassword(crow.value.opwd)
    };
    // proxy.Post("/scada/user/checkpwd.json", record, (data) => {
    //     if (!data) {
    //         callback(new Error("原始密码输错!"));
    //     } else {
    //         callback();
    //     }
    // }, (error) => {
    //     callback(new Error("原始密码输错!"));
    // });
};
const checkPassWord = (rule, value, callback) => {
    var modes = 0;
    if (value.length < 8) {
        callback(new Error("密码必须八位以上且包含大小写字母、数字和符号"));
        return modes;
    }
    if (/\\d/.test(value)) {
        modes++;
    }
    if (/[a - z]/.test(value)) {
        modes++;
    }
    if (/[A - Z]/.test(value)) {
        modes++;
    }
    if (/\\W/.test(value)) {
        modes++;
    }

    if (modes != 4) {
        callback(new Error("密码必须八位以上且包含大小写字母、数字和符号"));
    } else {
        callback();
    }
};
const validateEq = (rule, value, callback) => {
    if (value === "") {
        callback(new Error("请再次输入密码!"));
    } else if (value !== crow.value.npwd) {
        callback(new Error("两次输入密码不一致!"));
    } else {
        callback();
    }
};
const rules = ref({
    opwd: [
        { required: true, message: "原始密码不能为空", trigger: "blur" },
        { validator: checkPwd, trigger: "blur" }
    ],
    npwd: [
        { required: true, message: "新密码不能为空", trigger: "blur" },
        { validator: checkPassWord, trigger: "blur" }
    ],
    rpwd: [
        { required: true, message: "确认密码不能为空", trigger: "blur" },
        { validator: validateEq, trigger: "blur" }
    ]
});
const formRef = ref(null);
const singleLoginInterval = ref(false);
const timerSingle = ref(null);
const route = useRoute();
const store = useUserStore();


const validateStrong = (rule, value, callback) => {
    if (!/^(?=.*[0-9].*)(?=.*[A-Za-z].*).{6,20}$/.test(value)) {
        callback(new Error("密码必须八位以上且包含字母及数字"));
    } else {
        callback();
    }
};



// 方法
const getLogoutUrl = () => {
    let logoutUrl = "/logout.html";
    let appid = getUrlParam("appid") || localStorage.getItem('appid');
    if (appid) {
        logoutUrl += "?appid=" + appid;
    }
    return logoutUrl;
};

const exit = () => {
    let logoutUrl = getLogoutUrl();
    window.location.replace(logoutUrl);
};


const setpassword = () => {
    visible.value = true;
    ischeckPassWord();
    crow.value.opwd = "";
    crow.value.npwd = "";
    crow.value.rpwd = "";
};

const save = () => {
    formRef.value.validate((valid) => {
        if (!valid) {
            return;
        }

        let record = {
            opwd: encodepassword(crow.value.opwd),
            npwd: encodepassword(crow.value.npwd)
        };

        // proxy.Post("/scada/user/changepwd.json", record, (data) => {
        //     window.location.pathname = "/logout.html";
        //     visible.value = false;
        // }, (error) => {
        //     // 需要根据 Vue 3 消息提示方式调整
        //     // this.$message.error(error);
        // });
    });
};

const judge = () => {
    const pwdModify = window.top.pwdModify;
    if (pwdModify == 0) {
        // let config = store.state.Layout.config;
        // var enable_initial_password = config && config.find(item => item.code == 'enable_initial_password');
        // if (!!enable_initial_password && enable_initial_password.value == '1') {
        //     closeState.value = false;
        //     title.value = "修改密码[首次登录]";
        //     setpassword();
        // }
    } else if (pwdModify == 1) {
        closeState.value = false;
        title.value = "修改密码[密码过期]";
        setpassword();
    }
};

const singleLogin = () => {
    // if (singleLoginInterval.value) {
    //     proxy.Post("/scada/user/single.json", {}, (json) => {
    //         var flag = json || false;
    //         if (flag) {
    //             // 需要根据 Vue 3 弹窗方式调整
    //             // this.$alert('对不起,您的账号已经在异地被登录.', '异地登录', {
    //             //     confirmButtonText: '确定',
    //             //     callback: action => {
    //             //         window.location.pathname = "/logout.html";
    //             //     }
    //             // });
    //         }
    //     });
    // }
};

const encodepassword = (input) => {
    var _keyStr = "NjCG7lX9WbVtnaA1TxzEY5OpuJ8Pr4oZF3s - SKdkchv2mqyLiD0efwRIBH_ = 6UgMQ";
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    var _utf8_encode = function (string) {
        string = string.replace(/\\r\\n/g, "\\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    };
    input = _utf8_encode(input);
    while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
            enc4 = 64;
        }
        output = output +
            _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
            _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
    }
    return output;
};

const ischeckPassWord = () => {
    rules.value.npwd = [
        { required: true, message: "新密码不能为空", trigger: "blur" }
    ];
};

const getUrlParam = (name) => {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    const r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
};

// 监听路由变化
watch(() => route, (n, o) => {
    judge(n);
}, { immediate: true });

// 监听 visible 变化
watch(visible, (nval) => {
    if (!nval) {
        formRef.value.clearValidate();
    }
});

// 生命周期钩子
onMounted(() => {
    singleLogin();
});

onBeforeUnmount(() => {
    if (timerSingle.value) {
        clearInterval(timerSingle.value);
    }
});

// 创建定时器
if (singleLoginInterval.value) {
    timerSingle.value = setInterval(() => {
        console.log("开始进行异地登录判断.");
        singleLogin();
    }, 1 * 5 * 1000);
}

// 初始化时检查密码强度
ischeckPassWord();
</script>

<style lang="less">
.hd-userset {
    @margin: 11px 20px 0 0;
    float: right;
    height: 100%;
    color: #999;

   .alarm {
        float: left;
        margin: @margin;
    }
    div.lcs-password {
        width: 350px;
        z-index: 9999;
    }

    div.userset {
        float: left;
        // margin: @margin;
        margin: 0 20px 0 0;
        // height: 28px;
        height: 100%;
        display: flex;
        align-items: center;
        a.userset-button {
            // height: 28px;
            box-sizing: border-box;
            padding: 5px 0;
            text-decoration: none;
            display: inline-block;
            display: flex;
            align-items: center;
            color: #999;
            .username{
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #222222;
            }
            &:hover, &.hover {
                // color: white;
            }
           .iconfont {
                width: 18px;
                height: 18px;
                font-size: 18px;
                line-height: 18px;
                margin: 2px 0 0 0;
                float: left;
                padding: 0;
               .icon-arrow-spread {
                    font-size: 18px;
                    height: 14px;
                    line-height: 14px;
                    width: 14px;
                    margin-top: 4px;
                }
            }
            > span {
                float: left;
                // line-height: 18px;
                font-size: 12px;
                // height: 18px;
                margin: 0 8px;
            }
        }
    }

   .sltrole {
        float: left;
        margin: @margin;
        width: 120px;
    }
}
div.hd-userset-pop.el-popover {
    min-width:120px;
    > ul.userset-options {
        width: 100%;
        margin: 0;
        padding: 0;
        > li {
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
            height: 30px;
            // color: @color-common;
            border-left: 2px solid transparent;
            cursor: pointer;
            &:hover {
                background: #f5f7fa;
                border-color: #4698eb;
            }
            > i {
                float: left;
                height: 18px;
                width: 18px;
                display: block;
                font-size: 18px;
                line-height: 18px;
                margin: 7px 10px 0 8px;
                overflow: visible;
            }
            > span {
                float: left;
                display: block;
                height: 20px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-top: 5px;
            }
        }
    }
}
</style>
<style lang="less" scoped>
.el-form-item--mini.el-form-item {
    margin-bottom: 28px !important;
}
</style>