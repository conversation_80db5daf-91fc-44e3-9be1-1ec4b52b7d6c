from cachetools import TTLCache
from robot.api.deco import keyword

# 创建缓存：最大200个条目，每个条目最多存活600秒
cache = TTLCache(maxsize=200, ttl=600)

def get_output(work_id,node_id):
    return cache.get(work_id + "_" + node_id + "_output")

class CustomiseLibrary:
    def __init__(self):
        pass

    @keyword
    def record_input(self,work_id,node_id,val):
        cache.pop(work_id+"_"+node_id+"_input",val)

    @keyword
    def record_output(self,work_id : str,node_id : str,val):
        if "http" in node_id:
            cache.pop(work_id + "_" + node_id + "_output", val.text)
        else:
            cache.pop(work_id + "_" + node_id + "_output", val)




