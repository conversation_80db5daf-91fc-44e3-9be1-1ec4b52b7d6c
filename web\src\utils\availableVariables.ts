import { useWorkflowStore } from '@/stores/workflow'
import type { variableType } from '@/stores/workflow'

export function getAvailableVariables() {
  const workflowStore = useWorkflowStore()
  const variables: Array<variableType> = []

  // 遍历工作流中的所有节点，收集输出变量
  workflowStore.nodes.forEach((node) => {
    const nodeData = node.data
    const config = nodeData.config || {}

    // 收集用户输入组件的变量
    if (['user_input', 'user_choice', 'user_confirm'].includes(<string>nodeData.componentType)) {
      const variableName = config.variable_name
      if (variableName) {
        variables.push({
          name: variableName,
          value: config.user_provided_value || config.default_value || '',
          type: getVariableType(config),
          scope: 'workflow',
          description: `来自用户输入: ${config.prompt_message || nodeData.label}`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集设置变量组件的变量
    if (nodeData.componentType === 'set_variable') {
      const variableName = config.variable_name
      if (variableName) {
        variables.push({
          name: variableName,
          value: config.variable_value || '',
          type: config.value_type || 'string',
          scope: config.scope || 'local',
          description: `设置的变量: ${config.description || ''}`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }
    


    // 收集HTTP请求组件的响应变量
    if (nodeData.componentType === 'http_request') {
      // if (config.response_content_variable) {
      //   variables.push({
      //     name: config.response_content_variable,
      //     value: '响应内容变量名',
      //     type: config.response_content_variable$$type || 'json',
      //     scope: 'workflow',
      //     description: `存储响应内容的变量名`,
      //     sourceNode: nodeData.label || node.id,
      //     sourceNodeId: node.id,
      //   })
      // }
      if (config.extract_variable) {
        ;(config.extract_variable || []).forEach((it: { variable: string, realkey: string, type: string, desc: string, example: object | null }) => {
          variables.push({
            name: it.variable,
            value: it.realkey,
            type: it.type || 'string',
            scope: 'workflow',
            description: it.desc || 'http_request提取的变量',
            sourceNode: nodeData.label || node.id,
            sourceNodeId: node.id,
          })
        })
      }
    }

    // 收集天气获取组件的变量
    if (nodeData.componentType === 'weather_query') {
      if (config.response_content_variable) {
        variables.push({
          name: config.response_content_variable,
          value: null,
          type: config.response_content_variable$$type || 'string',
          scope: 'workflow',
          description: `存储响应内容的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.weather_text){
        variables.push({
          name: config.weather_text,
          value: null,
          type: config.weather_text$$type || 'string',
          scope: 'workflow',
          description: `存储天气数据输出变量`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 网页截图组件的变量
    if (nodeData.componentType === 'take_screenshot') {
      if (config.screenshot_path) {
        variables.push({
          name: config.screenshot_path,
          value: null,
          type: config.screenshot_path$$type || 'string',
          scope: 'workflow',
          description: `网页截图响应内容的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集增加、减少时间组件的变量
    if (nodeData.componentType === 'add_time') {
      if (config.response_timestamp_variable) {
        variables.push({
          name: config.response_timestamp_variable,
          value: null,
          type: config.response_timestamp_variable$$type || 'number',
          scope: 'workflow',
          description: `存储响应时间戳的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.response_date_variable) {
        variables.push({
          name: config.response_date_variable,
          value: null,
          type: config.response_date_variable$$type || 'string',
          scope: 'workflow',
          description: `存储响应日期格式的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.response_week_variable) {
        variables.push({
          name: config.response_week_variable,
          value: null,
          type: config.response_week_variable$$type || 'string',
          scope: 'workflow',
          description: `存储响应星期的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集Python执行器组件的输出变量
    if (nodeData.componentType === 'python_execute') {
      if (config.output_variable) {
        ;(config.output_variable || []).forEach((it: { variable: string, realkey: string, type: string, desc: string, example: object | null } | string) => {
          if(typeof it === 'string'){
            it = {
              variable: it,
              realkey: '',
              type: 'string',
              desc: 'Python代码执行的输出结果',
            }
          }
          variables.push({
            name: it.variable,
            value: 'Python输出结果',
            type: it.type || 'string',
            scope: 'workflow',
            description: it.desc || 'Python代码执行的输出结果',
            sourceNode: nodeData.label || node.id,
            sourceNodeId: node.id,
          })
        })
      }
      if (config.error_variable) {
        variables.push({
          name: config.error_variable,
          value: 'Python错误信息',
          type: config.error_variable$$type || 'string',
          scope: 'workflow',
          description: 'Python代码执行的错误信息',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.return_code_variable) {
        variables.push({
          name: config.return_code_variable,
          value: 0,
          type: config.return_code_variable$$type || 'number',
          scope: 'workflow',
          description: 'Python代码执行的返回码',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集Python表达式求值组件的输出变量
    if (nodeData.componentType === 'python_evaluate') {
      if (config.result_variable) {
        variables.push({
          name: config.result_variable,
          value: '表达式求值结果',
          type: config.result_variable$$type || 'json',
          scope: 'workflow',
          description: 'Python表达式的求值结果',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集JavaScript表达式求值组件的输出变量
    if (nodeData.componentType === 'javascript_execute') {
      if (config.output_variable) {
        variables.push({
          name: config.output_variable,
          value: '表达式求值结果',
          type: config.output_variable$$type || 'json',
          scope: 'workflow',
          description: 'javascript表达式的求值结果',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集Python模块导入组件的输出变量
    if (nodeData.componentType === 'python_import') {
      if (config.import_success_variable) {
        variables.push({
          name: config.import_success_variable,
          value: true,
          type: config.import_success_variable$$type || 'boolean',
          scope: 'workflow',
          description: 'Python模块导入是否成功',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集图片识别组件的输出变量
    if (nodeData.componentType === 'invoice_recognition') {
      if (config.recognition_results) {
        variables.push({
          name: config.recognition_results,
          value: true,
          type: config.recognition_result$$type || 'string',
          scope: 'workflow',
          description: '发票识别响应内容变量',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集图片识别组件的输出变量
    if (nodeData.componentType === 'img_recognition') {
      if (config.recognition_result) {
        variables.push({
          name: config.recognition_result,
          value: true,
          type: config.recognition_result$$type || 'string',
          scope: 'workflow',
          description: '图片识别响应内容变量',
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集数据库查询组件的变量
    if (nodeData.componentType === 'db_query') {
      if (config.response_content_variable) {
        variables.push({
          name: config.response_content_variable,
          value: null,
          type: config.response_content_variable$$type || 'json',
          scope: 'workflow',
          description: `数据库查询结果: ${config.query || '未指定查询语句'}`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集AI分析变量
    if (nodeData.componentType === 'ai_analyze') {
      if (config.ai_analyze_response) {
        variables.push({
          name: config.ai_analyze_response,
          value: '响应内容',
          type: config.ai_analyze_response$$type || 'string',
          scope: 'workflow',
          description: `AI分析`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集Excel创建变量
    // if (nodeData.componentType === 'excel_create') {
    //   if (config.excel_response) {
    //     variables.push({
    //       name: config.excel_response,
    //       value: '响应内容',
    //       type: config.excel_response$$type || 'string',
    //       scope: 'workflow',
    //       description: `Excel创建`,
    //       sourceNode: nodeData.label || node.id,
    //       sourceNodeId: node.id,
    //     })
    //   }
    // }

    // 收集监测数据组件的响应变量
    if (nodeData.componentType === 'monitor_data') {
      if (config.response_variable) {
        variables.push({
          name: config.response_variable,
          value: '监测数据响应对象',
          type: config.response_variable$$type || 'json',
          scope: 'workflow',
          description: `监测数据HTTP响应对象`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.response_content_variable) {
        variables.push({
          name: config.response_content_variable,
          value: '监测数据响应内容',
          type: config.response_content_variable$$type || 'string',
          scope: 'workflow',
          description: `监测数据HTTP响应内容`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.response_status_variable) {
        variables.push({
          name: config.response_status_variable,
          value: 200,
          type: config.response_status_variable$$type || 'number',
          scope: 'workflow',
          description: `监测数据HTTP状态码`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.response_headers_variable) {
        variables.push({
          name: config.response_headers_variable,
          value: '监测数据响应头',
          type: config.response_headers_variable$$type || 'json',
          scope: 'workflow',
          description: `监测数据HTTP响应头`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集文本模板变量
    if (nodeData.componentType === 'text_template') {
      if (config.output_variable) {
        variables.push({
          name: config.output_variable,
          value: '输出变量',
          type: config.output_variable$$type || 'string',
          scope: 'workflow',
          description: `存储格式化后的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集置信区间的响应变量
    if (nodeData.componentType === 'limit_interval') {
      if (config.warning_time) {
        variables.push({
          name: config.warning_time,
          value: '置信区间时间',
          type: config.warning_time$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间时间变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.warning_up) {
        variables.push({
          name: config.warning_up,
          value: '置信区间下限',
          type: config.warning_up$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间下限变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.warning_down) {
        variables.push({
          name: config.warning_down,
          value: '置信区间上限',
          type: config.warning_down$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间上限变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.outlier_count) {
        variables.push({
          name: config.outlier_count,
          value: '异常值数量',
          type: config.outlier_count$$type || 'number',
          scope: 'workflow',
          description: `存储异常值数量变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集数据预测的响应变量
    if (nodeData.componentType === 'data_forecast') {
      if (config.forecast_data) {
        variables.push({
          name: config.forecast_data,
          value: '预测值',
          type: config.forecast_data$$type || 'list',
          scope: 'workflow',
          description: `存储预测值变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.forecast_img) {
        variables.push({
          name: config.forecast_img,
          value: '数据预测曲线截图',
          type: config.forecast_img$$type || 'string',
          scope: 'workflow',
          description: `存储数据预测曲线截图变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.forecast_avg) {
        variables.push({
          name: config.forecast_avg,
          value: '预测平均值',
          type: config.forecast_avg$$type || 'number',
          scope: 'workflow',
          description: `存储预测平均值变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.forecast_sum) {
        variables.push({
          name: config.forecast_sum,
          value: '预测累计值',
          type: config.forecast_sum$$type || 'number',
          scope: 'workflow',
          description: `存储预测累计值变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.forecast_max) {
        variables.push({
          name: config.forecast_max,
          value: '预测最大值',
          type: config.forecast_max$$type || 'number',
          scope: 'workflow',
          description: `存储预测最大值变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.forecast_min) {
        variables.push({
          name: config.forecast_min,
          value: '预测最小值',
          type: config.forecast_min$$type || 'number',
          scope: 'workflow',
          description: `存储预测最小值变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集数据清洗的响应变量
    if (nodeData.componentType === 'data_wash') {
      if (config.wash_win) {
        variables.push({
          name: config.wash_win,
          value: '清洗后数据',
          type: config.wash_win$$type || 'list',
          scope: 'workflow',
          description: `存储清洗后数据变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.wash_time) {
        variables.push({
          name: config.wash_time,
          value: '清洗后数据时间',
          type: config.wash_time$$type || 'list',
          scope: 'workflow',
          description: `存储清洗后数据时间变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.miss_count) {
        variables.push({
          name: config.miss_count,
          value: '缺失数据数量',
          type: config.miss_count$$type || 'number',
          scope: 'workflow',
          description: `存储缺失数据数量变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.invalid_count) {
        variables.push({
          name: config.invalid_count,
          value: '无效数据数量',
          type: config.invalid_count$$type || 'number',
          scope: 'workflow',
          description: `存储无效数据数量变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.valid_count) {
        variables.push({
          name: config.valid_count,
          value: '有效数据数量',
          type: config.valid_count$$type || 'number',
          scope: 'workflow',
          description: `存储有效数据数量变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
      if (config.grade) {
        variables.push({
          name: config.grade,
          value: '历史数据评分',
          type: config.grade$$type || 'number',
          scope: 'workflow',
          description: `存储历史数据评分变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // 收集停水关阀分析
    if (nodeData.componentType === 'water_shutoff_valve') {
      if (config.response_content_variable){
        variables.push({
          name: config.response_content_variable,
          value: '返回结果',
          type: config.response_content_variable$$type || 'json',
          scope: 'workflow',
          description: `存储关阀分析返回结果变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        })
      }
    }

    // todo 可以继续添加其他组件类型的变量收集逻辑...
  })

  // 收集局部变量
  workflowStore.variables.forEach((variable: variableType) => {
    variables.push({
      name: variable.name,
      value: variable.value,
      type: variable.type,
      scope: 'local',
      description: variable.description || '',
      isSystem: variable.isSystem,
      sourceNode: '局部变量',
      sourceNodeId: 'local_variable',
    })
  })

  return variables
}

export function getVariableType(config: any) {
  if (config.input_type === 'number') return 'number'
  if (config.input_type === 'email') return 'string'
  if (config.value_type) return config.value_type
  return 'string'
}
