<template>
  <div class="showcase-container">
    <!-- 头部导航 -->
    <header class="showcase-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="@/assets/logo.png" alt="WimTask" class="logo" />
          <h1>WimTask</h1>
        </div>
        <nav class="nav-menu">
          <a href="#overview">产品介绍</a>
          <a href="#">解决方案</a>
          <a href="#">文档资料</a>
          <a href="#">服务支持</a>
          <a href="#manager">控制台</a>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="showcase-main">
      <!-- 产品定位展示 -->
      <section id="overview" class="section hero-section">
        <div class="container">
          <div class="hero-content">
            <h2 class="hero-title">下一代AI驱动的智能任务执行平台</h2>
            <p class="hero-subtitle">
              WimTask通用平台 + 和达MCP水务专业服务包<br/>
              构建水务行业数字化转型解决方案
            </p>
            <div class="value-props">
              <div class="prop-item">
                <el-icon class="prop-icon"><Setting /></el-icon>
                <h3>平台通用性</h3>
                <p>支持多行业扩展的通用AI任务执行平台</p>
              </div>
              <div class="prop-item">
                <el-icon class="prop-icon"><Tools /></el-icon>
                <h3>专业服务性</h3>
                <p>和达MCP提供水务行业专业解决方案</p>
              </div>
              <div class="prop-item">
                <el-icon class="prop-icon"><Connection /></el-icon>
                <h3>架构分离性</h3>
                <p>通用平台与专业服务分离，降低成本</p>
              </div>
              <div class="prop-item">
                <el-icon class="prop-icon"><Share /></el-icon>
                <h3>生态开放性</h3>
                <p>支持第三方MCP服务，构建多行业生态</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 市场分析 -->
      <section id="market" class="section market-section">
        <div class="container">
          <h2 class="section-title">市场现状与用户需求</h2>
          <div class="market-content">
            <div class="market-challenges">
              <h3>当前挑战</h3>
              <div class="challenge-grid">
                <div class="challenge-item">
                  <el-icon class="challenge-icon"><Warning /></el-icon>
                  <h4>产品定位模糊</h4>
                  <p>与市场上通用RPA产品差异化不明显，未能充分体现水务行业专业性优势</p>
                </div>
                <div class="challenge-item">
                  <el-icon class="challenge-icon"><User /></el-icon>
                  <h4>用户体验痛点</h4>
                  <p>学习成本高，非技术用户难以上手，功能复杂但缺乏业务场景深度结合</p>
                </div>
                <div class="challenge-item">
                  <el-icon class="challenge-icon"><TrendCharts /></el-icon>
                  <h4>市场竞争劣势</h4>
                  <p>在通用RPA市场与成熟产品正面竞争，缺乏独特的产品卖点和竞争壁垒</p>
                </div>
              </div>
            </div>

            <div class="user-personas">
              <h3>目标用户群体</h3>
              <div class="personas-grid">
                <div class="persona-card">
                  <div class="persona-header">
                    <el-icon class="persona-icon"><Monitor /></el-icon>
                    <h4>水务企业运维管理者</h4>
                  </div>
                  <div class="persona-content">
                    <p><strong>角色特征：</strong>负责水厂日常运维管理</p>
                    <p><strong>核心需求：</strong>降低人工值班成本，提高监控效率</p>
                    <p><strong>痛点：</strong>24小时人工值班成本高，异常响应不及时</p>
                    <p><strong>典型场景：</strong>值班机器人自动化监控</p>
                  </div>
                </div>

                <div class="persona-card">
                  <div class="persona-header">
                    <el-icon class="persona-icon"><Tools /></el-icon>
                    <h4>水务技术工程师</h4>
                  </div>
                  <div class="persona-content">
                    <p><strong>角色特征：</strong>负责设备维护和技术优化</p>
                    <p><strong>核心需求：</strong>提高漏损检测精度，优化维护效率</p>
                    <p><strong>痛点：</strong>漏损检测依赖人工，定位精度不够</p>
                    <p><strong>典型场景：</strong>漏损卫士智能检测</p>
                  </div>
                </div>

                <div class="persona-card">
                  <div class="persona-header">
                    <el-icon class="persona-icon"><Management /></el-icon>
                    <h4>水务企业管理决策者</h4>
                  </div>
                  <div class="persona-content">
                    <p><strong>角色特征：</strong>关注运营效率和成本控制</p>
                    <p><strong>核心需求：</strong>数字化转型，提升整体运营效率</p>
                    <p><strong>痛点：</strong>缺乏统一的数字化平台和数据分析</p>
                    <p><strong>典型场景：</strong>全业务流程自动化</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 双层架构可视化 -->
      <section id="architecture" class="section architecture-section">
        <div class="container">
          <h2 class="section-title">双层架构设计</h2>
          <div class="architecture-diagram">
            <div class="architecture-layer application-layer">
              <h3>水务行业应用层</h3>
              <div class="layer-items">
                <div class="app-item" @click="showScenario('duty-robot')">
                  <el-icon><Monitor /></el-icon>
                  <span>值班机器人</span>
                </div>
                <div class="app-item" @click="showScenario('leak-guard')">
                  <el-icon><Warning /></el-icon>
                  <span>漏损卫士</span>
                </div>
                <div class="app-item">
                  <el-icon><Management /></el-icon>
                  <span>工程管理</span>
                </div>
                <div class="app-item">
                  <el-icon><TrendCharts /></el-icon>
                  <span>成本分析</span>
                </div>
              </div>
            </div>

            <div class="architecture-layer mcp-layer">
              <h3>和达MCP服务层</h3>
              <div class="layer-items">
                <div class="service-item">
                  <el-icon><Box /></el-icon>
                  <span>水务专业工具包</span>
                </div>
                <div class="service-item">
                  <el-icon><Document /></el-icon>
                  <span>行业知识库</span>
                </div>
                <div class="service-item">
                  <el-icon><Files /></el-icon>
                  <span>业务模板</span>
                </div>
                <div class="service-item">
                  <el-icon><Service /></el-icon>
                  <span>专家服务</span>
                </div>
              </div>
            </div>

            <div class="architecture-layer platform-layer">
              <h3>WimTask通用平台层</h3>
              <div class="layer-items">
                <div class="platform-item">
                  <el-icon><Operation /></el-icon>
                  <span>任务编排引擎</span>
                </div>
                <div class="platform-item">
                  <el-icon><Avatar /></el-icon>
                  <span>AI智能体管理</span>
                </div>
                <div class="platform-item">
                  <el-icon><Link /></el-icon>
                  <span>MCP工具集成</span>
                </div>
                <div class="platform-item">
                  <el-icon><DataBoard /></el-icon>
                  <span>数据集成平台</span>
                </div>
              </div>
            </div>

            <div class="architecture-layer tech-layer">
              <h3>基础技术层</h3>
              <div class="layer-items">
                <div class="tech-item">MCP协议栈</div>
                <div class="tech-item">A2A框架</div>
                <div class="tech-item">RPA引擎</div>
                <div class="tech-item">AI模型</div>
                <div class="tech-item">数据库</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 技术优势展示 -->
      <section id="features" class="section features-section">
        <div class="container">
          <h2 class="section-title">技术优势</h2>
          <div class="features-grid">
            <div class="feature-card" @click="showTechDetail('mcp')">
              <div class="feature-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <h3>MCP协议先发优势</h3>
              <p>率先在水务行业应用MCP技术，实现标准化工具集成</p>
              <div class="feature-tags">
                <el-tag size="small">标准化</el-tag>
                <el-tag size="small">可扩展</el-tag>
                <el-tag size="small">生态友好</el-tag>
              </div>
            </div>

            <div class="feature-card" @click="showTechDetail('a2a')">
              <div class="feature-icon">
                <el-icon><Share /></el-icon>
              </div>
              <h3>A2A多智能体协作</h3>
              <p>实现不同专业领域的智能体协同工作，提升任务执行效率</p>
              <div class="feature-tags">
                <el-tag size="small">协同工作</el-tag>
                <el-tag size="small">负载均衡</el-tag>
                <el-tag size="small">智能调度</el-tag>
              </div>
            </div>

            <div class="feature-card" @click="showTechDetail('rpa2')">
              <div class="feature-icon">
                <el-icon><Cpu /></el-icon>
              </div>
              <h3>RPA2.0智能自动化</h3>
              <p>AI驱动的新一代RPA技术，具备自适应执行能力</p>
              <div class="feature-tags">
                <el-tag size="small">AI驱动</el-tag>
                <el-tag size="small">自适应</el-tag>
                <el-tag size="small">智能化</el-tag>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 竞品分析 -->
      <section id="competitors" class="section competitors-section">
        <div class="container">
          <h2 class="section-title">竞品分析</h2>

          <!-- 国外产品对比 -->
          <div class="competitors-category">
            <h3>国外主流RPA产品</h3>
            <div class="competitors-table-wrapper">
              <table class="competitors-table">
                <thead>
                  <tr>
                    <th>产品名称</th>
                    <th>市场地位</th>
                    <th>核心优势</th>
                    <th>主要劣势</th>
                    <th>对WimTask启示</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>UiPath</strong></td>
                    <td>全球最大RPA厂商<br/>400万+开发者</td>
                    <td>• 完整RPA生态系统<br/>• AI Center集成<br/>• 强大社区生态</td>
                    <td>• 许可费用昂贵<br/>• 本土化支持不足<br/>• 中小企业门槛高</td>
                    <td>• 构建开发者生态<br/>• AI能力集成重要</td>
                  </tr>
                  <tr>
                    <td><strong>Automation Anywhere</strong></td>
                    <td>云原生RPA先驱<br/>Bot Store模式</td>
                    <td>• 云原生架构<br/>• Bot Store生态<br/>• 安全合规强</td>
                    <td>• 中国市场渗透低<br/>• 技术资源相对少</td>
                    <td>• 云原生是趋势<br/>• 平台化运营模式</td>
                  </tr>
                  <tr>
                    <td><strong>Microsoft Power Automate</strong></td>
                    <td>Office生态集成<br/>低代码平台领导者</td>
                    <td>• Office 365集成<br/>• 连接器生态丰富<br/>• 低代码平台</td>
                    <td>• 主要面向办公场景<br/>• 复杂逻辑处理有限</td>
                    <td>• 生态集成更重要<br/>• 低代码降门槛</td>
                  </tr>
                  <tr>
                    <td><strong>Blue Prism</strong></td>
                    <td>企业级RPA专家<br/>金融机构首选</td>
                    <td>• 安全性可审计性<br/>• 中央化管理<br/>• 企业级扩展</td>
                    <td>• 主要服务大企业<br/>• 学习成本较高</td>
                    <td>• 企业级特性重要<br/>• 稳定性优于功能</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 国内产品对比 -->
          <div class="competitors-category">
            <h3>国内主流RPA产品</h3>
            <div class="competitors-table-wrapper">
              <table class="competitors-table">
                <thead>
                  <tr>
                    <th>产品名称</th>
                    <th>发展历程</th>
                    <th>核心优势</th>
                    <th>主要劣势</th>
                    <th>差异化机会</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>来也科技</strong></td>
                    <td>2015年成立<br/>AI+RPA融合先锋</td>
                    <td>• AI+RPA概念较早<br/>• 智能文档处理强<br/>• 行业解决方案丰富</td>
                    <td>• 技术创新相对保守<br/>• 产品同质化程度高</td>
                    <td>• 专注水务垂直领域<br/>• MCP+A2A差异化</td>
                  </tr>
                  <tr>
                    <td><strong>弘玑Cyclone</strong></td>
                    <td>易用性标杆<br/>中小企业友好</td>
                    <td>• 界面友好易用<br/>• 中文化程度高<br/>• 快速部署能力</td>
                    <td>• 基础RPA功能<br/>• AI集成程度一般</td>
                    <td>• 用户体验是关键<br/>• 服务质量差异化</td>
                  </tr>
                  <tr>
                    <td><strong>艺赛旗</strong></td>
                    <td>2011年成立<br/>国内RPA先驱</td>
                    <td>• 桌面自动化成熟<br/>• 客户案例丰富<br/>• 品牌知名度高</td>
                    <td>• 技术创新能力有限<br/>• 产品架构相对陈旧</td>
                    <td>• 新技术架构超车<br/>• 行业专业化突破</td>
                  </tr>
                  <tr>
                    <td><strong>影刀RPA</strong></td>
                    <td>新兴力量<br/>免费模式创新</td>
                    <td>• 云端部署无需安装<br/>• 简单易用<br/>• 免费版功能丰富</td>
                    <td>• 企业级功能有限<br/>• 商业化模式待验证</td>
                    <td>• 免费模式获客<br/>• 社区运营重要</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 差异化优势 -->
          <div class="differentiation">
            <h3>WimTask差异化优势</h3>
            <div class="advantage-grid">
              <div class="advantage-item">
                <el-icon class="advantage-icon"><Star /></el-icon>
                <h4>专业化深度优势</h4>
                <ul>
                  <li>深度理解水务工艺和业务流程</li>
                  <li>掌握水务行业特有的数据模型</li>
                  <li>针对值班、巡检、维修等具体场景优化</li>
                </ul>
              </div>
              <div class="advantage-item">
                <el-icon class="advantage-icon"><Cpu /></el-icon>
                <h4>技术创新优势</h4>
                <ul>
                  <li>率先在水务行业应用MCP技术</li>
                  <li>实现不同专业领域的智能协作</li>
                  <li>具备自适应执行能力</li>
                </ul>
              </div>
              <div class="advantage-item">
                <el-icon class="advantage-icon"><Connection /></el-icon>
                <h4>生态整合优势</h4>
                <ul>
                  <li>充分利用WIM AI、WIM PIC、PMIS等现有资源</li>
                  <li>20年水务行业知识和经验积累</li>
                  <li>现有客户基础为新产品推广提供支撑</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 商业模式说明 -->
      <section id="business" class="section business-section">
        <div class="container">
          <h2 class="section-title">商业模式</h2>
          <div class="business-models">
            <div class="model-card">
              <div class="model-header">
                <el-icon class="model-icon"><Setting /></el-icon>
                <h3>平台订阅收入</h3>
              </div>
              <div class="model-content">
                <p>订阅WimTask平台，类似传统RPA平台的订阅模式</p>
                <ul>
                  <li>免费版：基础功能体验</li>
                  <li>专业版：完整平台功能</li>
                  <li>企业版：高级管理功能</li>
                </ul>
              </div>
            </div>

            <div class="model-card">
              <div class="model-header">
                <el-icon class="model-icon"><Tools /></el-icon>
                <h3>服务订阅收入</h3>
              </div>
              <div class="model-content">
                <p>订阅和达MCP服务工具包，灵活的使用空间</p>
                <ul>
                  <li>按模块订阅：选择需要的专业工具</li>
                  <li>按使用量计费：灵活的付费模式</li>
                  <li>跨平台使用：支持多AI平台调用</li>
                </ul>
              </div>
            </div>

            <div class="model-card">
              <div class="model-header">
                <el-icon class="model-icon"><Service /></el-icon>
                <h3>项目服务收入</h3>
              </div>
              <div class="model-content">
                <p>提供专业的实施和咨询服务</p>
                <ul>
                  <li>实施服务：产品部署和培训</li>
                  <li>咨询服务：业务流程优化</li>
                  <li>定制开发：特殊需求开发</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 集成优势 -->
      <section id="integration" class="section integration-section">
        <div class="container">
          <h2 class="section-title">与现有产品集成优势</h2>
          <div class="integration-grid">
            <div class="integration-card">
              <div class="integration-header">
                <el-icon class="integration-icon"><Cpu /></el-icon>
                <h3>WIM AI集成</h3>
              </div>
              <div class="integration-content">
                <p><strong>产品价值：</strong>为值班机器人和漏损卫士提供AI能力支撑</p>
                <p><strong>集成方式：</strong>通过API调用WIM AI的分析和决策能力</p>
                <div class="integration-features">
                  <span class="feature-tag">智能分析</span>
                  <span class="feature-tag">决策支持</span>
                  <span class="feature-tag">异常检测</span>
                </div>
              </div>
            </div>

            <div class="integration-card">
              <div class="integration-header">
                <el-icon class="integration-icon"><Document /></el-icon>
                <h3>WIM PIC集成</h3>
              </div>
              <div class="integration-content">
                <p><strong>产品价值：</strong>利用现有文档平台，为平台提供文档服务</p>
                <p><strong>集成方式：</strong>将平台嵌入到工具和决策流程中，如生成巡检报告</p>
                <div class="integration-features">
                  <span class="feature-tag">文档生成</span>
                  <span class="feature-tag">报告管理</span>
                  <span class="feature-tag">知识库</span>
                </div>
              </div>
            </div>

            <div class="integration-card">
              <div class="integration-header">
                <el-icon class="integration-icon"><Management /></el-icon>
                <h3>PMIS集成</h3>
              </div>
              <div class="integration-content">
                <p><strong>产品价值：</strong>与项目管理和人员管理系统联动</p>
                <p><strong>集成方式：</strong>同步任务和工作流，实现统一管理</p>
                <div class="integration-features">
                  <span class="feature-tag">任务同步</span>
                  <span class="feature-tag">工作流</span>
                  <span class="feature-tag">人员管理</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 知识库展示 -->
          <div class="knowledge-base">
            <h3>和达MCP水务行业知识库</h3>
            <div class="knowledge-grid">
              <div class="knowledge-item">
                <el-icon class="knowledge-icon"><Document /></el-icon>
                <h4>技术标准库</h4>
                <p>水务行业相关的技术标准和规范</p>
              </div>
              <div class="knowledge-item">
                <el-icon class="knowledge-icon"><Star /></el-icon>
                <h4>最佳实践库</h4>
                <p>行业成功案例和经验总结</p>
              </div>
              <div class="knowledge-item">
                <el-icon class="knowledge-icon"><User /></el-icon>
                <h4>专家知识库</h4>
                <p>专业技术知识和解决方案</p>
              </div>
              <div class="knowledge-item">
                <el-icon class="knowledge-icon"><Files /></el-icon>
                <h4>法规政策库</h4>
                <p>相关法律法规和政策解读</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 发展路线图 -->
      <section id="roadmap" class="section roadmap-section">
        <div class="container">
          <h2 class="section-title">发展路线图</h2>
          <div class="roadmap-timeline">
            <div class="timeline-item">
              <div class="timeline-marker">
                <span class="timeline-number">1</span>
              </div>
              <div class="timeline-content">
                <h3>第一阶段：MVP验证（0-3个月）</h3>
                <div class="timeline-details">
                  <p><strong>产品目标：</strong>验证WimTask+和达MCP双层架构的可行性</p>
                  <div class="timeline-features">
                    <div class="feature-group">
                      <h4>WimTask平台</h4>
                      <ul>
                        <li>基础任务编排引擎</li>
                        <li>简单AI智能体管理</li>
                        <li>MCP协议基础支持</li>
                        <li>基础数据集成能力</li>
                      </ul>
                    </div>
                    <div class="feature-group">
                      <h4>和达MCP服务包</h4>
                      <ul>
                        <li>值班机器人工具包</li>
                        <li>漏损卫士工具包</li>
                        <li>5个核心专业工具</li>
                      </ul>
                    </div>
                  </div>
                  <p><strong>成功指标：</strong>平台稳定性>99%，工具调用成功率>95%，用户满意度>4.0</p>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-marker">
                <span class="timeline-number">2</span>
              </div>
              <div class="timeline-content">
                <h3>第二阶段：产品完善（3-6个月）</h3>
                <div class="timeline-details">
                  <p><strong>产品目标：</strong>完善双平台功能，验证商业模式</p>
                  <div class="timeline-features">
                    <div class="feature-group">
                      <h4>功能完善</h4>
                      <ul>
                        <li>完整任务编排引擎</li>
                        <li>完善AI智能体管理</li>
                        <li>完整运营监控工具集</li>
                        <li>20个专业工具和模板</li>
                      </ul>
                    </div>
                  </div>
                  <p><strong>成功指标：</strong>月收入>20万元，NPS>50，客户推荐率>30%</p>
                </div>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-marker">
                <span class="timeline-number">3</span>
              </div>
              <div class="timeline-content">
                <h3>第三阶段：生态建设（6-12个月）</h3>
                <div class="timeline-details">
                  <p><strong>产品目标：</strong>建立MCP生态，扩展到其他行业</p>
                  <div class="timeline-features">
                    <div class="feature-group">
                      <h4>生态扩展</h4>
                      <ul>
                        <li>开放MCP工具市场</li>
                        <li>第三方智能体接入</li>
                        <li>制造业MCP服务包</li>
                        <li>能源行业MCP服务包</li>
                      </ul>
                    </div>
                  </div>
                  <p><strong>成功指标：</strong>年收入>500万元，第三方工具>50个，行业覆盖>3个</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 场景详情弹窗 -->
    <el-dialog
      v-model="scenarioDialogVisible"
      :title="currentScenario.title"
      width="800px"
      class="scenario-dialog"
    >
      <div class="scenario-content">
        <div class="scenario-description">
          <h4>应用场景</h4>
          <p>{{ currentScenario.description }}</p>
        </div>
        <div class="scenario-implementation">
          <h4>实现方式</h4>
          <div class="implementation-flow">
            <div class="flow-item">
              <div class="flow-title">WimTask平台提供</div>
              <ul>
                <li v-for="item in currentScenario.wimtaskProvides" :key="item">{{ item }}</li>
              </ul>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-item">
              <div class="flow-title">和达MCP提供</div>
              <ul>
                <li v-for="item in currentScenario.mcpProvides" :key="item">{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="scenario-value">
          <h4>预期价值</h4>
          <div class="value-metrics">
            <div v-for="metric in currentScenario.metrics" :key="metric.label" class="metric-item">
              <span class="metric-label">{{ metric.label }}</span>
              <span class="metric-value">{{ metric.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 技术详情弹窗 -->
    <el-dialog
      v-model="techDialogVisible"
      :title="currentTech.title"
      width="700px"
      class="tech-dialog"
    >
      <div class="tech-content">
        <div class="tech-description">
          <p>{{ currentTech.description }}</p>
        </div>
        <div class="tech-features">
          <h4>核心特性</h4>
          <ul>
            <li v-for="feature in currentTech.features" :key="feature">{{ feature }}</li>
          </ul>
        </div>
        <div class="tech-advantages">
          <h4>技术优势</h4>
          <ul>
            <li v-for="advantage in currentTech.advantages" :key="advantage">{{ advantage }}</li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  Setting,
  Tools,
  Connection,
  Share,
  Monitor,
  Warning,
  Management,
  TrendCharts,
  Box,
  Document,
  Files,
  Service,
  Operation,
  Avatar,
  Link,
  DataBoard,
  Cpu,
  User,
  Star
} from '@element-plus/icons-vue'

// 场景详情数据
const scenarioDialogVisible = ref(false)
const currentScenario = reactive({
  title: '',
  description: '',
  wimtaskProvides: [] as string[],
  mcpProvides: [] as string[],
  metrics: [] as Array<{label: string, value: string}>
})

// 技术详情数据
const techDialogVisible = ref(false)
const currentTech = reactive({
  title: '',
  description: '',
  features: [] as string[],
  advantages: [] as string[]
})

// 场景数据
const scenarios = {
  'duty-robot': {
    title: '值班机器人',
    description: '24小时智能值班监控系统，替代人工值班，提供全天候的设备监控和异常处理服务。',
    wimtaskProvides: [
      '任务编排引擎（定时监控任务调度）',
      'AI智能体管理（监控、分析、决策智能体协作）',
      '数据集成平台（SCADA系统数据接入）',
      'MCP工具集成框架（专业工具调用）'
    ],
    mcpProvides: [
      'SCADA监控工具（实时数据采集和展示）',
      '智能报警工具（AI异常检测和分级处理）',
      '自动记录工具（值班日志自动生成）',
      '应急响应模板（标准化应急处置流程）'
    ],
    metrics: [
      { label: '降低值班成本', value: '60%' },
      { label: '提高响应速度', value: '80%' },
      { label: '减少人为错误', value: '90%' },
      { label: '提升管理规范性', value: '显著' }
    ]
  },
  'leak-guard': {
    title: '漏损卫士',
    description: '智能管网漏损检测系统，通过AI算法分析压力流量数据，精确定位漏损点并优化巡检路线。',
    wimtaskProvides: [
      '任务编排引擎（定期检测任务调度）',
      'AI智能体管理（检测、定位、规划智能体协作）',
      '数据集成平台（压力流量数据接入）',
      'MCP工具集成框架（专业分析工具调用）'
    ],
    mcpProvides: [
      '漏损检测工具（压力流量分析算法）',
      '定位预测工具（AI定位算法和GIS集成）',
      '巡检优化工具（路线规划和任务调度）',
      '效果评估工具（维修前后对比分析）'
    ],
    metrics: [
      { label: '检测准确率提升', value: '70%' },
      { label: '定位精度提升', value: '50%' },
      { label: '巡检效率提升', value: '40%' },
      { label: '漏损率降低', value: '20%' }
    ]
  }
}

// 技术数据
const technologies = {
  mcp: {
    title: 'MCP协议先发优势',
    description: 'Model Context Protocol是Anthropic开发的模型上下文协议，用于AI模型与外部工具和数据源的标准化集成。',
    features: [
      '统一的工具接口规范',
      '插件生态建设支持',
      'AI模型能力无缝扩展',
      '第三方服务标准化集成'
    ],
    advantages: [
      '率先在水务行业应用MCP技术',
      '建立行业标准和技术壁垒',
      '支持开放生态建设',
      '降低第三方集成成本'
    ]
  },
  a2a: {
    title: 'A2A多智能体协作',
    description: '下一代智能体协作框架，支持多个AI智能体之间的自主协作和任务分配。',
    features: [
      '标准化的通信协议',
      '智能任务分解和调度',
      '负载均衡和优化调度',
      '知识共享和协同学习'
    ],
    advantages: [
      '实现不同专业领域智能体协作',
      '提高复杂任务处理能力',
      '支持动态扩展和配置',
      '提升整体系统效率'
    ]
  },
  rpa2: {
    title: 'RPA2.0智能自动化',
    description: '基于browser-use的新一代RPA框架，支持AI驱动的智能任务流程自动化。',
    features: [
      'AI原生设计架构',
      '自适应执行能力',
      '多模态交互支持',
      '云原生部署架构'
    ],
    advantages: [
      '根据环境变化自动调整策略',
      '支持分布式部署和扩展',
      '具备学习和优化能力',
      '提供更强的容错性'
    ]
  }
}

// 显示场景详情
const showScenario = (scenarioKey: string) => {
  const scenario = scenarios[scenarioKey as keyof typeof scenarios]
  if (scenario) {
    Object.assign(currentScenario, scenario)
    scenarioDialogVisible.value = true
  }
}

// 显示技术详情
const showTechDetail = (techKey: string) => {
  const tech = technologies[techKey as keyof typeof technologies]
  if (tech) {
    Object.assign(currentTech, tech)
    techDialogVisible.value = true
  }
}
</script>

<style scoped lang="scss">
.showcase-container {
  min-height: 100vh;
  height: 100vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 头部样式
.showcase-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;

    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo {
        width: 40px;
        height: 40px;
      }

      h1 {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
      }
    }

    .nav-menu {
      display: flex;
      gap: 30px;

      a {
        color: #2c3e50;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s;

        &:hover {
          color: #667eea;
        }
      }
    }
  }
}

// 主要内容样式
.showcase-main {
  padding-top: 70px;
}

.section {
  padding: 80px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 60px;
  }
}

// 英雄区域样式
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;

  .hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 60px;
    opacity: 0.9;
    line-height: 1.6;
  }

  .value-props {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;

    .prop-item {
      background: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .prop-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: #ffd700;
      }

      h3 {
        font-size: 20px;
        margin-bottom: 12px;
        font-weight: 600;
      }

      p {
        opacity: 0.9;
        line-height: 1.5;
      }
    }
  }
}

// 架构区域样式
.architecture-section {
  background: #f8fafc;

  .section-title {
    color: #2c3e50;
  }

  .architecture-diagram {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .architecture-layer {
      background: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border-left: 4px solid;

      &.application-layer {
        border-left-color: #e74c3c;
      }

      &.mcp-layer {
        border-left-color: #f39c12;
      }

      &.platform-layer {
        border-left-color: #3498db;
      }

      &.tech-layer {
        border-left-color: #9b59b6;
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #2c3e50;
      }

      .layer-items {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;

        .app-item,
        .service-item,
        .platform-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 15px;
          background: #f8fafc;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
          }

          .el-icon {
            font-size: 20px;
            color: #667eea;
          }

          span {
            font-weight: 500;
            color: #2c3e50;
          }
        }

        .tech-item {
          padding: 12px 16px;
          background: #f8fafc;
          border-radius: 8px;
          text-align: center;
          font-weight: 500;
          color: #2c3e50;
          border: 1px solid #e1e8ed;
        }
      }
    }
  }
}

// 特性区域样式
.features-section {
  background: white;

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;

    .feature-card {
      background: #f8fafc;
      padding: 30px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #e1e8ed;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;

        .el-icon {
          font-size: 28px;
          color: white;
        }
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2c3e50;
      }

      p {
        color: #5a6c7d;
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .feature-tags {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
  }
}

// 商业模式样式
.business-section {
  background: #f8fafc;

  .business-models {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;

    .model-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .model-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        display: flex;
        align-items: center;
        gap: 15px;

        .model-icon {
          font-size: 28px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin: 0;
        }
      }

      .model-content {
        padding: 25px;

        p {
          color: #5a6c7d;
          margin-bottom: 20px;
          line-height: 1.6;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            padding: 8px 0;
            color: #2c3e50;
            position: relative;
            padding-left: 20px;

            &::before {
              content: '•';
              color: #667eea;
              font-weight: bold;
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }
  }
}

// 弹窗样式
.scenario-dialog,
.tech-dialog {
  .scenario-content,
  .tech-content {
    .scenario-description,
    .tech-description {
      margin-bottom: 25px;

      h4 {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
      }

      p {
        color: #5a6c7d;
        line-height: 1.6;
      }
    }

    .scenario-implementation {
      margin-bottom: 25px;

      h4 {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 15px;
      }

      .implementation-flow {
        display: flex;
        align-items: center;
        gap: 20px;

        .flow-item {
          flex: 1;
          background: #f8fafc;
          padding: 20px;
          border-radius: 8px;

          .flow-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
          }

          ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              padding: 4px 0;
              color: #5a6c7d;
              font-size: 14px;
            }
          }
        }

        .flow-arrow {
          font-size: 24px;
          color: #667eea;
          font-weight: bold;
        }
      }
    }

    .scenario-value {
      h4 {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 15px;
      }

      .value-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;

        .metric-item {
          background: #f8fafc;
          padding: 15px;
          border-radius: 8px;
          text-align: center;

          .metric-label {
            display: block;
            font-size: 14px;
            color: #5a6c7d;
            margin-bottom: 5px;
          }

          .metric-value {
            display: block;
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
          }
        }
      }
    }

    .tech-features,
    .tech-advantages {
      margin-bottom: 20px;

      h4 {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 6px 0;
          color: #5a6c7d;
          position: relative;
          padding-left: 20px;

          &::before {
            content: '✓';
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
          }
        }
      }
    }
  }
}

// 市场分析样式
.market-section {
  background: white;

  .market-content {
    .market-challenges {
      margin-bottom: 60px;

      h3 {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 30px;
        text-align: center;
      }

      .challenge-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;

        .challenge-item {
          background: #f8fafc;
          padding: 30px;
          border-radius: 12px;
          text-align: center;
          border: 1px solid #e1e8ed;

          .challenge-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 20px;
          }

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
          }

          p {
            color: #5a6c7d;
            line-height: 1.6;
          }
        }
      }
    }

    .user-personas {
      h3 {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 30px;
        text-align: center;
      }

      .personas-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;

        .persona-card {
          background: #f8fafc;
          border-radius: 12px;
          overflow: hidden;
          border: 1px solid #e1e8ed;

          .persona-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;

            .persona-icon {
              font-size: 24px;
            }

            h4 {
              font-size: 18px;
              font-weight: 600;
              margin: 0;
            }
          }

          .persona-content {
            padding: 20px;

            p {
              margin-bottom: 10px;
              color: #5a6c7d;
              line-height: 1.5;

              strong {
                color: #2c3e50;
              }
            }
          }
        }
      }
    }
  }
}

// 竞品分析样式
.competitors-section {
  background: #f8fafc;

  .competitors-category {
    margin-bottom: 50px;

    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .competitors-table-wrapper {
      overflow-x: auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .competitors-table {
        width: 100%;
        border-collapse: collapse;
        min-width: 800px;

        th, td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #e1e8ed;
        }

        th {
          background: #f8fafc;
          font-weight: 600;
          color: #2c3e50;
          font-size: 14px;
        }

        td {
          color: #5a6c7d;
          font-size: 13px;
          line-height: 1.5;

          strong {
            color: #2c3e50;
            font-weight: 600;
          }
        }

        tr:hover {
          background: #f8fafc;
        }
      }
    }
  }

  .differentiation {
    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 30px;
      text-align: center;
    }

    .advantage-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 30px;

      .advantage-item {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

        .advantage-icon {
          font-size: 48px;
          color: #667eea;
          margin-bottom: 20px;
        }

        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 15px;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            padding: 6px 0;
            color: #5a6c7d;
            position: relative;
            padding-left: 20px;

            &::before {
              content: '•';
              color: #667eea;
              font-weight: bold;
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }
  }
}

// 集成优势样式
.integration-section {
  background: white;

  .integration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;

    .integration-card {
      background: #f8fafc;
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid #e1e8ed;

      .integration-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        display: flex;
        align-items: center;
        gap: 15px;

        .integration-icon {
          font-size: 28px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin: 0;
        }
      }

      .integration-content {
        padding: 25px;

        p {
          color: #5a6c7d;
          margin-bottom: 15px;
          line-height: 1.6;

          strong {
            color: #2c3e50;
          }
        }

        .integration-features {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .feature-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .knowledge-base {
    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 30px;
      text-align: center;
    }

    .knowledge-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;

      .knowledge-item {
        background: #f8fafc;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        border: 1px solid #e1e8ed;

        .knowledge-icon {
          font-size: 40px;
          color: #667eea;
          margin-bottom: 15px;
        }

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 10px;
        }

        p {
          color: #5a6c7d;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 发展路线图样式
.roadmap-section {
  background: #f8fafc;

  .roadmap-timeline {
    position: relative;
    padding-left: 30px;

    &::before {
      content: '';
      position: absolute;
      left: 20px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #667eea;
    }

    .timeline-item {
      position: relative;
      margin-bottom: 50px;

      .timeline-marker {
        position: absolute;
        left: -30px;
        top: 0;
        width: 40px;
        height: 40px;
        background: #667eea;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .timeline-number {
          color: white;
          font-weight: 600;
          font-size: 16px;
        }
      }

      .timeline-content {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-left: 30px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 20px;
        }

        .timeline-details {
          p {
            color: #5a6c7d;
            margin-bottom: 20px;
            line-height: 1.6;

            strong {
              color: #2c3e50;
            }
          }

          .timeline-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;

            .feature-group {
              background: #f8fafc;
              padding: 20px;
              border-radius: 8px;

              h4 {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 10px;
              }

              ul {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                  padding: 4px 0;
                  color: #5a6c7d;
                  font-size: 14px;
                  position: relative;
                  padding-left: 15px;

                  &::before {
                    content: '•';
                    color: #667eea;
                    position: absolute;
                    left: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .showcase-header .header-content {
    flex-direction: column;
    height: auto;
    padding: 15px 20px;

    .nav-menu {
      margin-top: 15px;
      gap: 20px;
    }
  }

  .showcase-main {
    padding-top: 120px;
  }

  .hero-section {
    .hero-title {
      font-size: 32px;
    }

    .hero-subtitle {
      font-size: 16px;
    }

    .value-props {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .architecture-diagram {
    .architecture-layer .layer-items {
      grid-template-columns: 1fr;
    }
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .business-models {
    grid-template-columns: 1fr;
  }

  .implementation-flow {
    flex-direction: column;

    .flow-arrow {
      transform: rotate(90deg);
    }
  }

  .challenge-grid,
  .personas-grid,
  .advantage-grid,
  .integration-grid,
  .knowledge-grid {
    grid-template-columns: 1fr;
  }

  .competitors-table-wrapper {
    font-size: 12px;
  }

  .timeline-features {
    grid-template-columns: 1fr;
  }

  .roadmap-timeline {
    padding-left: 20px;

    &::before {
      left: 15px;
    }

    .timeline-item .timeline-marker {
      left: -25px;
      width: 30px;
      height: 30px;

      .timeline-number {
        font-size: 14px;
      }
    }

    .timeline-item .timeline-content {
      margin-left: 20px;
    }
  }
}
</style>
