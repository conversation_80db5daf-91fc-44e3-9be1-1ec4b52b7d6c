"""
Workflow-Use 集成服务
提供统一的接口来使用workflow-use功能
"""

import asyncio
import json
import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from loguru import logger

try:
    from .adapter import WorkflowUseAdapter
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from adapter import WorkflowUseAdapter


class WorkflowUseService:
    """
    Workflow-Use 集成服务
    提供统一的接口来替换原有的浏览器录制功能
    """

    def __init__(self, enable_ai_fallback: bool = False):
        """
        初始化服务

        Args:
            enable_ai_fallback: 是否启用AI回退功能（默认关闭）
        """
        self.adapter = WorkflowUseAdapter(enable_ai_fallback=enable_ai_fallback)
        self.temp_dir = Path(tempfile.mkdtemp(prefix="workflow_use_"))
        self.temp_dir.mkdir(exist_ok=True)

        # 录制状态
        self.recording_status = "idle"  # idle, recording, completed, failed
        self.current_recording = None
        self.current_workflow_definition = None
        self.recording_task = None  # 后台录制任务

        logger.info(f"WorkflowUse服务初始化完成，临时目录: {self.temp_dir}")

    async def start_recording_session(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动录制会话（替换原有的browser_recording_service.start_recording_session）

        注意：workflow-use使用不同的录制模式，这里启动后台录制任务

        Args:
            config: 录制配置

        Returns:
            录制会话结果
        """
        try:
            if self.recording_status != "idle":
                return {"success": False, "error": "已有录制会话正在进行中"}

            if not self.adapter.is_available():
                return {"success": False, "error": "Workflow-Use服务不可用，请检查依赖"}

            # 更新状态
            self.recording_status = "recording"
            session_id = f"workflow_use_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            logger.info("启动workflow-use录制会话...")

            # 启动后台录制任务
            # 注意：这里我们不等待录制完成，而是立即返回
            self.recording_task = asyncio.create_task(
                self._background_recording(config)
            )

            return {
                "success": True,
                "session_id": session_id,
                "message": "Workflow-Use录制会话已启动，浏览器将打开进行录制",
                "features": {
                    "deterministic_execution": True,
                    "ai_fallback": self.adapter.enable_ai_fallback,
                    "fast_replay": True,
                    "self_healing": False,
                    "recording_type": "workflow-use",
                },
                "instructions": "请在打开的浏览器中进行操作，完成后关闭浏览器或调用停止录制API",
            }

        except Exception as e:
            logger.error(f"启动录制会话失败: {e}")
            self.recording_status = "idle"
            return {"success": False, "error": str(e)}

    async def _background_recording(self, config: Dict[str, Any]):
        """后台录制任务"""
        try:
            logger.info("开始后台录制任务...")

            # 使用workflow-use的录制功能
            recording_result = await self.adapter.start_recording(config)

            if recording_result["success"]:
                self.current_recording = recording_result.get("recording_data")
                self.recording_status = "completed"
                logger.info("后台录制任务完成")
            else:
                self.recording_status = "failed"
                logger.error(f"后台录制任务失败: {recording_result.get('error')}")

        except Exception as e:
            logger.error(f"后台录制任务异常: {e}")
            self.recording_status = "failed"

    def _convert_recording_to_nodes(self) -> List[Dict]:
        """将录制数据转换为WimTask工作流节点"""
        try:
            if not self.current_recording:
                return []

            # 如果录制数据是workflow-use格式，直接转换
            if hasattr(self.current_recording, "model_dump"):
                # Pydantic模型
                recording_dict = self.current_recording.model_dump()
            elif isinstance(self.current_recording, dict):
                recording_dict = self.current_recording
            else:
                # 尝试转换为字典
                recording_dict = json.loads(str(self.current_recording))

            # 使用适配器转换为WimTask格式
            workflow_nodes = self.adapter.convert_workflow_use_to_wimtask(
                recording_dict
            )

            return workflow_nodes

        except Exception as e:
            logger.error(f"转换录制数据失败: {e}")
            return []

    async def stop_recording_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        停止录制会话（替换原有的browser_recording_service.stop_recording_session）

        Args:
            session_id: 会话ID（可选）

        Returns:
            停止结果
        """
        try:
            if self.recording_status == "idle":
                return {"success": False, "error": "当前没有活动的录制会话"}

            # 如果还在录制中，等待后台任务完成
            if self.recording_status == "recording" and self.recording_task:
                logger.info("等待录制任务完成...")
                try:
                    # 等待录制任务完成，最多等待30秒
                    await asyncio.wait_for(self.recording_task, timeout=30.0)
                except asyncio.TimeoutError:
                    logger.warning("录制任务超时，强制停止")
                    self.recording_task.cancel()
                    self.recording_status = "failed"
                except Exception as e:
                    logger.error(f"等待录制任务时发生错误: {e}")
                    self.recording_status = "failed"

            # 生成工作流节点
            workflow_nodes = self._convert_recording_to_nodes()

            # 重置状态
            old_status = self.recording_status
            self.recording_status = "idle"
            self.current_recording = None
            self.recording_task = None

            if old_status == "completed":
                return {
                    "success": True,
                    "workflow_nodes": workflow_nodes,
                    "message": f"录制已完成，生成了{len(workflow_nodes)}个操作节点",
                    "stats": {
                        "total_operations": len(workflow_nodes),
                        "nodes_generated": len(workflow_nodes),
                        "recording_type": "workflow-use",
                    },
                }
            else:
                return {
                    "success": False,
                    "workflow_nodes": workflow_nodes,
                    "message": f"录制未完成（状态：{old_status}），生成了{len(workflow_nodes)}个操作节点",
                    "error": f"录制状态异常: {old_status}",
                }

        except Exception as e:
            logger.error(f"停止录制会话失败: {e}")
            return {"success": False, "error": str(e)}

    def get_recording_status(self) -> Dict[str, Any]:
        """
        获取录制状态（替换原有的browser_recording_service.get_recording_status）

        Returns:
            录制状态信息
        """
        return {
            "success": True,
            "status": self.recording_status,
            "adapter_status": self.adapter.get_status(),
            "message": f"当前状态: {self.recording_status}",
        }

    async def execute_workflow(
        self, workflow_definition: Dict[str, Any], inputs: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行workflow-use工作流

        Args:
            workflow_definition: 工作流定义
            inputs: 输入参数

        Returns:
            执行结果
        """
        try:
            if not self.adapter.is_available():
                return {"success": False, "error": "Workflow-Use服务不可用"}

            # 保存工作流定义到临时文件
            workflow_file = (
                self.temp_dir
                / f"temp_workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            with open(workflow_file, "w", encoding="utf-8") as f:
                json.dump(workflow_definition, f, indent=2, ensure_ascii=False)

            # 使用workflow-use执行
            from workflow_use import Workflow

            workflow = Workflow.load_from_file(
                str(workflow_file),
                controller=self.adapter.controller,
                fallback_to_agent=self.adapter.enable_ai_fallback,
            )

            # 执行工作流
            result = await workflow.run(inputs=inputs or {}, close_browser_at_end=True)

            logger.info("工作流执行完成")

            return {"success": True, "result": result, "message": "工作流执行完成"}

        except Exception as e:
            logger.error(f"执行工作流失败: {e}")
            return {"success": False, "error": str(e)}

    def convert_wimtask_workflow(self, wimtask_nodes: List[Dict]) -> Dict[str, Any]:
        """
        将WimTask工作流转换为workflow-use格式

        Args:
            wimtask_nodes: WimTask格式的工作流节点

        Returns:
            转换结果
        """
        try:
            workflow_definition = self.adapter.convert_wimtask_to_workflow_use(
                wimtask_nodes
            )

            if workflow_definition:
                return {
                    "success": True,
                    "workflow_definition": workflow_definition,
                    "message": f"成功转换{len(wimtask_nodes)}个节点",
                }
            else:
                return {"success": False, "error": "转换失败"}

        except Exception as e:
            logger.error(f"转换工作流失败: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup(self):
        """清理资源"""
        try:
            # 清理临时文件
            import shutil

            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir, ignore_errors=True)

            # 重置状态
            self.recording_status = "idle"
            self.current_recording = None
            self.current_workflow_definition = None

            logger.info("WorkflowUse服务已清理")

        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 全局服务实例
workflow_use_service = WorkflowUseService(enable_ai_fallback=False)
