<template>
  <div class="loginSms-page">
    <!-- 错误信息 -->
    <div class="error-info" v-if="model.err_info">
      <el-icon><Remove /></el-icon>{{ model.err_info }}
    </div>
    <el-form 
      ref="loginSmsRef" 
      label-position="top" 
      :model="model" 
      hide-required-asterisk 
      :show-message="false" 
      :validate-on-rule-change="false"
    >
      <el-form-item label="手机号" prop="phone">
        <el-input 
          v-model.trim="model.phone" 
          clearable 
          placeholder="请输入手机号" 
          @keyup.enter="sendCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="code" required>
        <div class="send-code">
          <el-input 
            placeholder="请输入验证码" 
            v-model.trim="model.code" 
            @keyup.enter="onLogin"
          ></el-input>
          <button 
            type="button" 
            class="send-btn" 
            @click="sendCode" 
            :disabled="disabled || isSended"
          >
            {{ text }}
          </button>
        </div>
      </el-form-item>
      <div class="action-button">
        <el-button 
          type="primary" 
          @click="onLogin" 
          class="login-btn"
        >
          登录
        </el-button>
      </div>
      <div class="register-button">
        <div class="register-item">
          <span>没有账号，</span>
          <span class="register-btn" @click="loginChange(-1)">注册</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, inject } from 'vue';
import { checkIsPhone, timer } from '@/utils/validate';
import { ElMessage } from 'element-plus';
import systemApi from "@/api/system";
import utils from '@/utils/utils';
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";

const userStore = useUserStore()
const loginStore = useLoginStore()

// 类型定义
interface Configs {
  [key: string]: any; // 可根据实际需求扩展具体配置项
}

interface Props {
  configs?: Configs;
  cid?: string | null;
  pwsIsShow?: boolean;
  loginPop?: boolean;
}

interface ModelType {
  phone: string | null;
  code: string | null;
  err_info: string | null;
  cid: string | null | undefined;
}

interface VerificationCodeResponse {
  Code: number;
  Message?: string;
}

interface LoginResponse {
  Code: number;
  Message?: string;
  Response?: {
    token: string;
    expire: string;
    refreshToken: string;
    tenantId: string;
  };
}

// 外部依赖类型定义
interface DlyApi {
  verificationCode: (params: { mobile: string; type: string }, options?: any) => Promise<VerificationCodeResponse>;
  verificationCodeLogin: (params: { mobile: string; code: string }, options?: any) => Promise<LoginResponse>;
}

interface Utils {
  setLocalStorageInfo: (token: string, expire: string, refreshToken: string, tenantId: string) => void;
}

interface Store {
  commit: (mutation: string, value: boolean) => void;
  dispatch: (action: string) => Promise<void>;
}

// 接收Props
const props = withDefaults(defineProps<Props>(), {
  configs: () => ({}),
  cid: null,
  pwsIsShow: false,
  loginPop: false
});

// 注入外部依赖

// 响应式状态
const loginSmsRef = ref<any>(null);
const loading = ref<boolean>(false);
const disabled = ref<boolean>(true);
const isSended = ref<boolean>(false);
const time = ref<number>(60);
const text = ref<string>('获取验证码');

const model = reactive<ModelType>({
  phone: null,
  code: null,
  err_info: null,
  cid: props.cid
});

// 监听手机号变化
watch(
  () => model.phone,
  (v) => {
    disabled.value = !v || v === '' || !checkIsPhone(v) || time.value < 60;
  },
  { immediate: true }
);

// 监听cid变化
watch(
  () => props.cid,
  (n) => {
    model.cid = n;
  }
);

// 组件挂载时恢复计时状态
onMounted(() => {
  const storedTime = localStorage.getItem('UniWimSmsTime');
  if (storedTime) {
    const parsedTime = Number(storedTime);
    if (!isNaN(parsedTime) && parsedTime > 0) {
      time.value = parsedTime - 1;
      text.value = `${time.value}秒后重发`;
      letTimer();
    }
  }
});

// 发送验证码
const handleSendCode = (): Promise<void> => {
  model.err_info = null;
  return new Promise((resolve, reject) => {
    if (!model.phone) {
      reject('请输入手机号');
      return;
    }

    systemApi
      .verificationCode(
        {
          mobile: model.phone,
          type: 'LOGIN_CODE'
        },
        {
          meta: {
            isData: false
          }
        }
      )
      .then((res) => {
        if (res.Code === 0) {
          resolve();
        } else {
          reject(res.Message || '发送验证码失败');
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

// 处理发送验证码逻辑
const sendCode = async () => {
  if (!model.phone) {
    model.err_info = '请输入手机号';
    return;
  }
  
  if (isSended.value) return;
  
  disabled.value = true;
  
  try {
    await handleSendCode();
    ElMessage({
      type: 'success',
      message: '验证码已发送，请注意查收'
    });
    isSended.value = true;
    letTimer();
  } catch (err) {
    model.err_info = err instanceof Error ? err.message : (err as string) || '操作失败，请联系管理员';
    disabled.value = false;
  }
};

// 倒计时逻辑
const letTimer = () => {
  let times = time.value;
  
  timer(
    times,
    (s) => {
      text.value = `${s}秒后重发`;
      time.value = s;
      localStorage.setItem('UniWimSmsTime', s.toString());
    },
    () => {
      const isRight = model.phone ? checkIsPhone(model.phone) : false;
      if (model.phone !== '' && isRight) {
        disabled.value = false;
      }
      text.value = '重新获取';
      isSended.value = false;
      time.value = 60;
      localStorage.removeItem('UniWimSmsTime');
    }
  );
};

// 登录逻辑
const onLogin = async () => {
  if (!model.phone) {
    model.err_info = '请输入手机号';
    return;
  }
  
  if (!model.code) {
    model.err_info = '请输入手机验证码';
    return;
  }
  
  try {
    const params = {
      mobile: model.phone,
      code: model.code
    };
    
    const res = await systemApi.verificationCodeLogin(params, {
      meta: {
        isData: false
      }
    });
    
    if (!res) {
      model.err_info = '登录失败，请重试！';
      return;
    }
    
    if (res.Code === 0 && res.Response) {
      utils.setLocalStorageInfo(
        res.Response.token,
        res.Response.expire,
        res.Response.refreshToken,
        res.Response.tenantId
      );
      
      // 度量云主题登录
      if (props.pwsIsShow) {
        // $store.commit('InitIsTenant', true);
      }
      
      if (props.loginPop) {
        // 更新相关信息
        // await $store.dispatch('UPDTAINFO');
        let userInfo = await systemApi.initUserInfo();
          if(userInfo){
            userStore.setUserInfo(userInfo)
            loginStore.LOGIN_POP_VISIBLE(false)
          }
      } else {
        location.replace(`${import.meta.env.BASE_URL}index.html`);
      }
    } else {
      model.err_info = res.Message || '登录失败';
    }
  } catch (err) {
    model.err_info = err instanceof Error 
      ? err.message 
      : (err as any)?.Message || JSON.stringify(err);
  }
};

// 切换到注册
const loginChange = (type: number) => {
  emit('onSwitchLoginType', type);
};

// 定义事件
const emit = defineEmits<{
  (e: 'onSwitchLoginType', type: number): void;
}>();
</script>

<style scoped lang="less">
::v-deep.loginSms-page {
  padding: 27px 28px 30px 32px;
  background: transparent;

  .error-info {
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 28px;
    background: #fcddd9;
    border: 1px solid #db3b35;
    border-radius: 2px;
    font-weight: 400;
    font-size: 14px;
    color: #db3b35;
    letter-spacing: 0;
    padding: 8px 15px;
    box-sizing: border-box;
    margin-bottom: 20px;

    i {
      margin-right: 13px;
    }
  }

  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-size: 14px;
      color: #333333;
      line-height: 20px;

      &::before {
        display: none;
      }
    }

    .el-form-item__content {
      .el-select {
        width: 100%;
      }
    }
  }

  .el-input__wrapper {
    height: 44px;
    line-height: 44px;
    // border: 1px solid #dadada;
    border-radius: 2px;
    background: #fff !important;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      color: #bfbfbf;
      letter-spacing: 0;
    }

    &:hover {
      border: 1px solid #0862ea !important;
    }
  }

  .send-code {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    border: 1px solid #dadada;
    border-radius: 2px;
    background: #fff;

    &:hover {
      border: 1px solid #0862ea;
    }

    .el-input__wrapper {
      border: 0 !important;
      box-shadow: none;
      &:hover {
        border: 0 !important;
      }
    }

    .el-input {
      flex: 1;
      height: 42px;
      overflow: hidden;
    }

    .send-btn {
      border: 0;
      height: 100%;
      padding: 0 16px;
      position: relative;
      cursor: pointer;
      line-height: 44px;
      font-weight: 400;
      font-size: 14px;
      color: #0054d2;
      letter-spacing: 0;
      background-color: transparent;

      &:after {
        content: '';
        position: absolute;
        left: 0;
        top: 12px;
        height: 20px;
        width: 1px;
        background: #dadada;
      }

      &:disabled {
        color: #999;
        cursor: not-allowed;
      }
    }
  }

  .action-button {
    width: 340px;
    height: 43px;
    margin-top: 151px;

    .el-button {
      font-size: 18px;
      font-weight: 400;
      width: 100%;
      height: 100%;
      border-radius: 2px;
      overflow: hidden;
      background: #0054d2;
      border-color: #0054d2;
    }
  }

  .register-button {
    margin-top: 10px;

    .register-item {
      text-align: right;

      span {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
        line-height: 17px;
      }

      .register-btn {
        cursor: pointer;
        color: #0054d2;
      }
    }
  }
}
</style>
