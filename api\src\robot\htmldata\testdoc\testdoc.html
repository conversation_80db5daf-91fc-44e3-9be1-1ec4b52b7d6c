<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="-1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="Generator" content="">
<link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKcAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAAqAAAAAAAAAAAAAAAAAAAALIAAAD/AAAA4AAAANwAAADcAAAA3AAAANwAAADcAAAA3AAAANwAAADcAAAA4AAAAP8AAACxAAAAAAAAAKYAAAD/AAAAuwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC/AAAA/wAAAKkAAAD6AAAAzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN8AAAD/AAAA+gAAAMMAAAAAAAAAAgAAAGsAAABrAAAAawAAAGsAAABrAAAAawAAAGsAAABrAAAADAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAIsAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAANEAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAAAAAAAAMgAAADIAAAAyAAAAMgAAADIAAAAyAAAAMgAAADIAAAAFAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAADwAAAB8AAAAAAAAAGAAAABcAAAAAAAAAH8AAABKAAAAAAAAAAAAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAADCAAAA/wAAACkAAADqAAAA4QAAAAAAAAD7AAAA/wAAALAAAAAGAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAIwAAAP4AAAD/AAAA/wAAAGAAAAAAAAAAAAAAAMkAAAD/AAAAigAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAAAAAAAIAAAAcAAAABkAAAAAAAAAAAAAAAAAAAAAAAAAEgAAAAAAAAAAAAAA2gAAAP8AAAD7AAAAywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN4AAAD/AAAAqwAAAP8AAACvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALIAAAD/AAAAsgAAAAAAAAC5AAAA/wAAAMoAAADAAAAAwAAAAMAAAADAAAAAwAAAAMAAAADAAAAAwAAAAMkAAAD/AAAAvAAAAAAAAAAAAAAAAAAAAKwAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAArQAAAAAAAAAAwAMAAIABAAAf+AAAP/wAAD/8AAAgBAAAP/wAAD/8AAA//AAAJIwAADHEAAA//AAAP/wAAB/4AACAAQAAwAMAAA==">
<link rel="stylesheet" type="text/css" href="../rebot/log.css" media="all">
<link rel="stylesheet" type="text/css" href="../rebot/common.css" media="all">
<link rel="stylesheet" type="text/css" href="../rebot/print.css" media="print">
<link rel="stylesheet" type="text/css" href="../rebot/doc_formatting.css" media="all">
<link rel="stylesheet" type="text/css" href="../common/js_disabled.css" media="all">
<link rel="stylesheet" type="text/css" href="testdoc.css" media="all">
<script type="text/javascript" src="../lib/jquery.min.js"></script>
<script type="text/javascript" src="../lib/jquery.tmpl.min.js"></script>
<script type="text/javascript" src="../rebot/util.js"></script>
<!-- JS MODEL --><script type="text/javascript" src="../testdata/testdoc.js"></script>
<title></title>
</head>
<body>
<div id="javascript-disabled">
  <h1>Opening test suite documentation failed</h1>
  <ul>
    <li>Verify that you have <b>JavaScript enabled</b> in your browser.</li>
    <li>Make sure you are using a <b>modern enough browser</b>. If using Internet Explorer, version 8 or newer is required.</li>
    <li>Check are there messages in your browser's <b>JavaScript error log</b>. Please report the problem if you suspect you have encountered a bug.</li>
  </ul>
</div>
<script type="text/javascript">
    // Not using jQuery here for maximum speed
    document.getElementById('javascript-disabled').style.display = 'none';
</script>

<div id="header"><h1></h1></div>

<script type="text/javascript">
$(document).ready(function(){
    parseTemplates();
    addTitle(window.testdoc.title);
    addGenerated(window.testdoc.generated);
    addSuite(window.testdoc.suite, 'body');
    if (window.location.hash) {
        makeElementVisible(window.location.hash.substring(1));
    } else {
        makeElementVisible(window.testdoc.suite.id, true);
    }
});

function parseTemplates() {
    $('script[type="text/x-jquery-tmpl"]').map(function (idx, elem) {
        $.template(elem.id, elem.text);
    });
}

function addTitle(title) {
    document.title = title;
    $('h1').text(title);
}

function addGenerated(millis) {
    $.tmpl('<div id="generated">' +
             '<span>Generated<br>${generated}</span><br>' +
             '<span id="generated-ago">${generatedAgo} ago</span>' +
           '</div>', {
        generated: util.createGeneratedString(millis),
        generatedAgo: util.createGeneratedAgoString(millis)
    }).appendTo($('#header'));
}

function addSuite(suite, parent_locator) {
    $.tmpl('suiteTemplate', suite).appendTo($(parent_locator));
    addKeywords(suite);
    addSuites(suite);
    addTests(suite);
}

function addSuites(suite) {
    $.map(suite.suites, function (s, _) {
        addSuite(s, '#' + suite.id + '> .children');
    });
}

function addTests(suite) {
    $.map(suite.tests, function (test, _) {
        $.tmpl('testTemplate', test).appendTo($('#' + suite.id + '> .children'));
        addKeywords(test);
    });
}

function addKeywords(parent) {
    $.map(parent.keywords, function (kw, _) {
        $.tmpl('keywordTemplate', kw).appendTo($('#' + parent.id + '> .children'));
    });
}

function makeElementVisible(id, noHighlight) {
    var ids = id.split('-');
    var current = [];
    while (ids.length) {
        current.push(ids.shift());
        openElement(current.join('-'));
    }
    if (!noHighlight) {
        highlight($('#' + id));
    }
}

function highlight(element, color) {
    if (color === undefined)
        color = 242;
    if (color < 255) {
        element.css({'background-color': 'rgb('+color+','+color+','+color+')'});
        setTimeout(function () { highlight(element, color+1); }, 300);
    } else {
        element.css({'background-color': ''});
    }
}

function openElement(id) {
    var element = $('#' + id);
    element.children('.children').show();
    element.children('.element-header').removeClass('closed');
}

function toggleElement(id) {
    var element = $('#' + id);
    element.children('.children').toggle(100, '', function () {
        element.children('.element-header').toggleClass('closed');
    });
}

function expandAll(id) {
    var element = $('#' + id);
    element.find('.children').show();
    element.find('.element-header').removeClass('closed');
}

function collapseAll(id) {
    var element = $('#' + id);
    element.find('.children').css({'display': 'none'});
    element.find('.element-header').addClass('closed');
}

// For complete cross-browser experience..
// http://www.quirksmode.org/js/events_order.html
function stopPropagation(event) {
    var event = event || window.event;
    event.cancelBubble = true;
    if (event.stopPropagation)
        event.stopPropagation();
}
</script>

<script type="text/x-jquery-tmpl" id="suiteTemplate">
  <div id="${id}" class="suite">
    <div class="element-header closed" onclick="toggleElement('${id}')">
      <div class="element-header-left" title="{{html fullName}}">
        <span class="not-run">TEST SUITE: </span>
        <span class="name">{{html name}}</span>
      </div>
      <div class="element-header-right" onclick="stopPropagation(event)" title="">
        <a class="expand" title="Expand all" href="javascript:expandAll('${id}')"></a>
        <a class="collapse" title="Collapse all" href="javascript:collapseAll('${id}')"></a>
        <a class="link" title="Link to this suite" href="#${id}" onclick="makeElementVisible('${id}')"></a>
      </div>
      <div class="element-header-toggle" title="Toggle visibility"></div>
    </div>
    <div class="children">
      <table class="metadata">
        <tr>
          <th>Full Name:</th>
          <td>{{html fullName}}</td>
        </tr>
        {{if doc}}
        <tr>
          <th>Documentation:</th>
          <td class="doc">{{html doc}}</td>
        </tr>
        {{/if}}
        {{each metadata}}
        <tr>
          <th>{{html $value[0]}}:</th>
          <td class="doc">{{html $value[1]}}</td>
        </tr>
        {{/each}}
        {{if source}}
        <tr>
          <th>Source:</th>
          {{if relativeSource}}
          <td><a href="${relativeSource}">${source}</a></td>
          {{else}}
          <td>${source}</td>
          {{/if}}
        </tr>
        {{/if}}
        <tr>
          <th>Number of Tests:</th>
          <td>${numberOfTests}</td>
        </tr>
      </table>
    </div>
  </div>
</script>

<script type="text/x-jquery-tmpl" id="testTemplate">
  <div id="${id}" class="test">
    <div class="element-header closed" onclick="toggleElement('${id}')">
      <div class="element-header-left" title="{{html fullName}}">
        <span class="not-run">TEST CASE: </span>
        <span class="name">{{html name}}</span>
      </div>
      <div class="element-header-right" onclick="stopPropagation(event)" title="">
        <a class="link" title="Link to this test" href="#${id}" onclick="makeElementVisible('${id}')"></a>
      </div>
      <div class="element-header-toggle" title="Toggle visibility"></div>
    </div>
    <div class="children">
      <table class="metadata">
        <tr>
          <th>Full Name:</th>
          <td>{{html fullName}}</td>
        </tr>
        {{if doc}}
        <tr>
          <th>Documentation:</th>
          <td class="doc">{{html doc}}</td>
        </tr>
        {{/if}}
        {{if tags.length}}
        <tr>
          <th>Tags:</th>
          <td>{{html tags.join(', ')}}</td>
        </tr>
        {{/if}}
        {{if timeout}}
        <tr>
          <th>Timeout:</th>
          <td>${timeout}</td>
        </tr>
        {{/if}}
      </table>
    </div>
  </div>
</script>

<script type="text/x-jquery-tmpl" id="keywordTemplate">
  <div class="keyword">
    <div class="element-header">
      <span class="not-run">${type}: </span>
      <span class="name">{{html name}}</span>
      <span class="arg">{{html arguments}}</span>
    </div>
  </div>
</script>

</body>
</html>
