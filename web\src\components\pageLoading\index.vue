<!--  -->
<template>
  <!-- 加载动画 -->
    <div id="route-loading">
        <div class="logo">
            <div class="name"><img
                    style="height: 48px;"
                    src="../../assets/images/admin/logoV2.png"
                    alt="WimTask logo"
                /><span class="version">{{ loadingStore.currentVersion?'V'+loadingStore.currentVersion:loadingStore.currentVersion }}</span></div>
        </div>
        <div class="content">
            <!-- 添加全屏视频背景 -->
            <video id="fullscreen-video" src="../../assets/login_4s.mp4" autoplay muted loop playsinline>
            </video>

            <div class="description">

                <div class="desc">
                    <img src="../../assets/images/homepage/quotation.png" class="quotation" alt="">
                    <div class="desc-content"><span>让重复工作</span><b>自动化 ，</b><span>让复杂工作</span><b>协同化 ，</b><span>让执行工作</span><b>智能化 。</b></div>
                </div>
                <div class="sub-desc">
                      人脑和AI结合 释放团队创造力 改变工作方式 提升工作效率和质量
                </div>
            </div>
            <div class="progress">
                <div class="progress-text" :class="[loadingStore.updateStatus==='failed'?'failed':'']">{{ progressText || "正在加载程序..."}}</div>
                <el-progress class="gradient-progress" color="#000" :percentage="loadingProgress"></el-progress>
            </div>
        </div>
        <div class="mask" v-if="!loadingStore.isLatest"></div>
        <div class="updater" v-if="!loadingStore.isLatest">
            <!-- <div class="updater-bg1"></div> -->
            <!-- <div class="updater-bg2"></div> -->
            <img src="../../assets/images/homepage/update-icon.png" class="updater-icon" width="163" height="163" alt="">
            <div class="updater-title">
                版本更新
            </div>
            <div class="updater-version">
                新版本 V{{loadingStore.publishResponse.versionNumber}} 可下载
            </div>
            <div class="updater-desc">
                为了让各位用户更好的编排任务，并且监控任务的执行情况，我们对WimTask的任务管理、任务监控页面进行了升级，为了更好的体验，建议您升级到最新版本。升级过程中带来的不便，敬请理解。
            </div>
            <div class="updater-content">
                【更新内容】
                <div class="content-inner">
                    <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                            <p :key="content" v-for="content in contents">- {{content}}</p>
                    </el-scrollbar>
                </div>
            </div>
            <div class="updater-footer">
                <div class="later-button" @click="updateLater" v-show="loadingStore.publishType==0">稍后更新</div>
                <div class="update-button" @click="updateSoon(loadingStore.publishResponse)">立即更新</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted,computed,onUnmounted,watch  } from 'vue'
import { useLoadingStore } from '@/stores/loading';
import systemApi from "@/api/system";
import { useRoute, useRouter } from 'vue-router'
import utils from "@/utils/utils";
const router = useRouter()
const route = useRoute()
const loadingStore = useLoadingStore();

// 定义响应式数据并指定类型
const loadingProgress = ref<number>(0)
const initpackage = ref(0)
const progressInterval = ref(null)
const checkTime = ref(0)
const checkStartTime = ref(0)
const checkStatusInterval = ref(null)
const checkStartStatusInterval = ref(null)
const progressText = ref<string>('正在检查程序版本....')

// 工具函数：精确保留小数位数
const roundFloat = (number: number, precision: number): number => {
  const factor = Math.pow(10, precision)
  return Math.round(number * factor) / factor
}

const contents = computed(() => {
    let history = loadingStore?.publishResponse.history;
    let result:any = []
    if(history){
        history.forEach((item:any)=>{
            if(item.publishContent){
                let contents = item.publishContent.trim().split('\n')
                contents.forEach(content=>{
                    result.push(content)
                })
            }

        })
    }

    ;
  return result
})

// 检查权限
const checkPermission = async () => {
  try {
    // 模拟检查权限请求
    const response = await fetch('http://localhost:39876/checkPermission?uniwater_utokn=' + utils.GetAuthorization(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': utils.GetAuthorization(),
      }
    })
    const result = await response.json()
    if (!response.ok || !result.hasPermission) {
      loadInitialData(50,100);
      jumpToNextPage()
      return false
    }
    loadInitialData(5,10);
    return true
  } catch (err) {
    console.error(err)
    loadInitialData(50,100);
    jumpToNextPage()
    return false
  }
}



// 生命周期钩子
onMounted(async() => {
  if (window.electron) {
    window.electron.removeAllListeners('app.onDownloadExe')
    //返回结果  code  【0:下载中, 1:替换文件成功, 2:替换文件异常, 3:下载中断, 4: WimTask.exe重新启动成功】
    window.electron.on('app.onDownloadExe', (result:any) => {
        clearInterval(progressInterval.value)
        console.log(result)
        let code = result.code;
        if(code==0){
            let percent = ((result.current/result.total)*70)
            // loadingProgress.value += Math.ceil(percent*0.7);
            loadingProgress.value = 20 + Math.ceil(percent);
        }
        if(code==2||code==3){
          loadingStore.setLatest(false)
        }
        if(code==4||code==1){
            progressText.value = '正在加载程序...'
            // loadingProgress.value = 0;
            loadInitialData(1,100);
            function checkRunstatus(){
                clearTimeout(checkStatusInterval.value)
                    checkStatusInterval.value = setTimeout(async() => {
                        try{
                            const versionResponse = await fetch('http://localhost:39876/version?uniwater_utokn='+utils.GetAuthorization(), {
                                method: 'GET',
                                headers: {
                                'Content-Type': 'application/json'
                                }
                            })
                            if(versionResponse.ok){
                                clearTimeout(checkStatusInterval.value)
                                // loadingStore.setLatest(true)
                                // loadingStore.finishLoading()
                                jumpToNextPage();
                            }else{
                                checkRunstatus()
                                ++checkTime.value
                                // checkRunstatus()
                                // ++checkTime.value
                            }
                        }catch(err){
                            checkRunstatus()
                            ++checkTime.value
                            console.error(err)
                        }

                    }, 2000)
                    if( checkTime.value > 30){//超过两分钟提示更新失败
                        clearTimeout(checkStatusInterval.value)
                        // loadingStore.setLatest(false)
                        loadingStore.setUpdateStatus('failed')
                        progressText.value = '更新失败...'
                    }

            }
            checkRunstatus()
        }
    })
  }
   const loading = document.getElementById('loading');
    if (loading) {
        loading.classList.add('loading-hidden');
        loading.style.display = 'none';
        loading.remove();
    }

    loadingStore.startLoading()
    //检查权限
    // const hasPermission = await checkPermission()
    // if (!hasPermission) return
    loadInitialData(5,10)
    //检查版本
    loadingStore.setUpdateStatus('success')
    function checkStartstatus(){
        clearTimeout(checkStartStatusInterval.value)
            checkStartStatusInterval.value = setTimeout(async() => {
                try{
                    const versionResponse = await fetch('http://localhost:39876/version?uniwater_utokn='+utils.GetAuthorization(), {
                        method: 'GET',
                        headers: {
                        'Content-Type': 'application/json'
                        }
                    })
                    if(versionResponse.ok){
                        clearTimeout(checkStartStatusInterval.value)
                        // loadingStore.setLatest(true)
                        // loadingStore.finishLoading()
                        //防止没拿到用户信息
                        systemApi.initUserInfo()
                        setTimeout(async()=>{
                            loadInitialData(5,20)
                            try{
                                const response = await fetch('http://localhost:39876/checkUpdate?uniwater_utokn='+utils.GetAuthorization(), {
                                    method: 'GET',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': utils.GetAuthorization(),
                                    }
                                })
                                const result = await response.json()
                                if(!response.ok){
                                    // loadInitialData(60,100)
                                    // setTimeout(()=>{
                                    //     jumpToNextPage()
                                    // },2000)
                                }
                                const versionResponse = await fetch('http://localhost:39876/version?uniwater_utokn='+utils.GetAuthorization(), {
                                    method: 'GET',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': utils.GetAuthorization(),
                                    }
                                })
                                let currentVersion = ''
                                if(versionResponse.ok){
                                    currentVersion = await versionResponse.json()
                                }else{
                                    if(!response.ok){
                                        loadInitialData(60,100)
                                        setTimeout(()=>{
                                            jumpToNextPage()
                                        },2000)
                                    }
                                }
                                // const loading = document.getElementById('loading');
                                // if (loading) {
                                //     loading.classList.add('loading-hidden');
                                //     loading.style.display = 'none';
                                //     loading.remove();
                                // }
                                loadingStore.setCurrentVersion(currentVersion)
                                if(result.Response) {
                                    loadingStore.setLatest(false)
                                    loadingStore.setPublishType(result.Response.publishType)
                                    loadingStore.setPublishResponse(result.Response)
                                }else{
                                    loadInitialData(30,90)
                                    loadingStore.setLatest(true)
                                    progressText.value = '正在加载程序...'
                                    // loadingProgress.value = 0;
                                    // loadInitialData();
                                    setTimeout(()=>{
                                        loadInitialData(1,100)
                                        jumpToNextPage()
                                        // loadingStore.finishLoading()

                                    },2000)
                                }
                            }catch(err){
                                console.error(err)
                                loadInitialData(60,100)
                                setTimeout(()=>{
                                    jumpToNextPage()
                                },2000)
                            }
                        },10)
                    }else{
                        checkStartstatus()
                        ++checkStartTime.value
                        // checkRunstatus()
                        // ++checkTime.value
                    }
                }catch(err){
                    checkStartstatus()
                    ++checkStartTime.value
                    console.error(err)
                }

            }, 2000)
            if( checkStartTime.value > 30){//超过两分钟提示更新失败
                clearTimeout(checkStartStatusInterval.value)
                // loadingStore.setLatest(false)
                loadingStore.setUpdateStatus('failed')
                progressText.value = '启动失败...'
            }

    }
    checkStartstatus()















































    // loadInitialData()
})

// 方法定义

const jumpToNextPage = () =>{
    if(route.path==='/loading'){
        router.push({
            path:'/homepage',
            query:route.query
        })
    }
}

const updateLater = () => {
    loadingStore.setLatest(true)
    progressText.value = '正在加载程序...'
    // loadingProgress.value = 0;
    loadInitialData(30,100);
    setTimeout(()=>{
        // loadingStore.finishLoading()
        jumpToNextPage()
    },2000)
}

const updateSoon = (response:any) => {
    if (window.electron) {
        const url = {
          url: response.packagePath,
        }
        window.electron.send('controller.robotMsg.downloadExe', url)
        clearTimeout(checkStatusInterval.value)
        checkTime.value = 0;
        initpackage.value=0;
        progressText.value = '正在更新程序...'
        // loadingProgress.value = 0;
        loadingStore.setLatest(true)
    }else{
        progressText.value = '正在更新程序...'
        // loadingProgress.value = 0;
        // loadInitialData();
        loadInitialData(50,90);
        setTimeout(()=>{
            loadingStore.setLatest(true)
            progressText.value = '正在加载程序...'
            loadInitialData(5,100);
            // loadingProgress.value = 0;
            // loadInitialData();
            setTimeout(()=>{
                // loadingStore.finishLoading()
                jumpToNextPage()
            },2000)
        },1000)

    }


}
const loadInitialData = (step:number,endValue:number) => {
  // 模拟加载过程
  clearInterval(progressInterval.value)
  progressInterval.value = setInterval(() => {
    loadingProgress.value += Math.ceil(Math.random()*step)
    // loadingProgress.value  = parseInt(loadingProgress.value)
    if (loadingProgress.value >= endValue) {
      loadingProgress.value = endValue
      clearInterval(progressInterval.value)
    }
  }, 200)
}
if(loadingStore.isLatest){
    // progressText.value = '正在加载程序...'
    // loadingProgress.value = 0;
    // loadInitialData();
    // setTimeout(()=>{
    //     loadingStore.finishLoading()
    // },2000)
}

onUnmounted(() => {
  // 清理监听器
  if (window.electron) {
    window.electron.removeAllListeners('app.onDownloadExe')
  }
})
</script>
<style lang='less' scoped>
#route-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    //background: linear-gradient(135deg, #778eec 0%, #895bba 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    background: white;
    width:100%;
    height:100%;
    background:url(../../assets/images/homepage/bg.png) no-repeat center center;
    background-size: 100% 100%;
    background-color:#fff;
    .gradient-progress /deep/.el-progress-bar__outer {
        background: #ebeef5;
    }
    .gradient-progress /deep/.el-progress-bar__inner {
        background: linear-gradient(to right, #C2BBF7, #0054D9); /* 渐变颜色 */
    }
    .logo{
        padding:20px 30px;
        box-sizing: border-box;
        width:100%;
        display:flex;
        justify-content: space-between;
        align-items: center;
        .name{
            // width: 180px;
            height: 41px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 28px;
            color: #424D62;
            display: flex;
            align-items: center;
        }
        .version{
            margin-left:20px;
            width: 79px;
            // height: 29px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #CDCDCD;
        }
    }
    .content{
        flex:1;
        // height:160px;
        width:100%;
        box-sizing: border-box;
        display:flex;
        justify-content: center;
        flex-direction: column;
        video{
            // position: absolute;
            top:250px;
            left: 50%;
            width: 200px;
            height: 200px;
            margin:0 auto;
            // object-fit: contain;
            z-index: -1;
            // transform: translate(-50%, -50%);
        }
        .description{
            width:920px;
            margin:0 auto;
            // margin-top:400px;
            margin-top:10%;
            display: flex;
            align-items: end;
            flex-direction: column;
        }
        .quotation{
            width:36px;
            height:36px;
            position: absolute;
            left:-50px;
            top:-20px;
        }
        .progress{
            width:920px;
            margin:0 auto;
            margin-top:110px;
            margin-top:5%;
            .progress-text{
                height: 24px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                color: #5C5F66;
                margin-bottom:10px;
                &.failed{
                    color:red;
                    font-weight:bold;
                }
            }
        }
        .desc{
            display:flex;
            align-items: end;
            position: relative;
            // height: 54px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 28px;
            color: #424D62;
            b{
                // height: 59px;
                font-family: SourceHanSansSC-Bold;
                font-weight: 700;
                font-size: 32px;
                color: #424D62;
                padding-left:20px;
            }
        }
        .sub-desc{
            margin-top:10px;
            width: 810px;
            height: 41px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 24px;
            color: #A5ABBC;
            text-align: right;
            padding-right:20px;
            box-sizing: border-box;
        }
    }
    .updater{
        padding:24px;
        box-sizing: border-box;
        width: 551px;
        min-height: 360px;
        background: #FFFFFF;
        border-radius: 4px;
        position: fixed;
        left:50%;
        top:50%;
        transform: translate(-50%,-50%);
        z-index:2000
    }
    .updater-icon{
        position:absolute;
        top:-30px;
        right:0;
    }
    .updater-bg1{
        position: absolute;
        top:0;
        left:0;
        width: 427px;
        height: 325.9px;
        opacity: 0.4;
        background: radial-gradient(circle at 50% 50%, #D0FDFE 0%, #ffffff1f 50%);
        filter: blur(10px);
    }
    .updater-bg2{
        position: absolute;
        top:0;
        right:0;
        width: 529.5px;
        height: 215.9px;
        opacity: 0.5;
        background: radial-gradient(circle at 50% 76%, #5fa2ff94 0%, #82a6fc14 29%);
        filter: blur(50px);
    }
    .updater-title{
        width: 80px;
        height: 29px;
        font-family: SourceHanSansSC-Bold;
        font-weight: 700;
        font-size: 20px;
        color: #222222;
        text-align: center;
    }
    .updater-version{
        margin-top:8px;
        height: 17px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
    }
    .updater-desc{
        margin-top:24px;
        width: 503px;
        height: 51px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
    }
    .updater-content{
        margin-top:24px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #5C5F66;
        letter-spacing: 0;
        line-height: 24px;
        padding-bottom:30px;
        .content-inner{
            // max-height:100px;
            overflow:auto;
            max-height: 350px;

        }
    }
    .mask{
        background:rgba(0, 0, 0, 0.5);
        position: fixed;
        left:0;
        right:0;
        top:0;
        bottom:0;
    }
    .updater-footer{
        display: flex;
        justify-content: end;
        margin-top: 20px;
        position: absolute;
        bottom: 30px;
        right: 30px;
    }
    .later-button{
        margin-right:16px;
        width: 80px;
        height: 32px;
        line-height:32px;
        background: #FFFFFF;
        border: 1px solid #E6E7E9;
        border-radius: 4px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        text-align: center;
        cursor:pointer;
    }
    .update-button{
        width: 80px;
        height: 32px;
        line-height:32px;
        background: #0054D9;
        border-radius: 4px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        cursor:pointer;
    }
}
</style>
