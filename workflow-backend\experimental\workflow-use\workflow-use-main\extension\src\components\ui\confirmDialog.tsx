import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from './dialog'; // 可使用 Radix UI 的 Dialog 组件
import { Button } from './button'; // 假设已有 Button 组件

interface ConfirmDialogProps {
  title: string;
  message: React.ReactNode;
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'default' | 'destructive';
  closeOnClickOutside?: boolean;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  title,
  message,
  visible,
  onConfirm,
  onCancel,
  confirmText = '确定',
  cancelText = '取消',
  confirmVariant = 'default',
  closeOnClickOutside = true,
}) => {
  const handleConfirm = () => {
    onConfirm();
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Dialog open={visible} onOpenChange={open => !open && onCancel()} modal={true}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {message}
        </div>
        
        <DialogFooter className="flex justify-end gap-3">
          <Button 
            variant="secondary" 
            onClick={handleCancel}
          >
            {cancelText}
          </Button>
          <Button 
            variant={confirmVariant} 
            onClick={handleConfirm}
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 提供函数式调用方式
export const confirm = (options: Omit<ConfirmDialogProps, 'visible' | 'onConfirm' | 'onCancel'>) => {
  return new Promise<boolean>((resolve) => {
    // 1. 创建真实DOM节点并添加到body
    const container = document.createElement('div');
    document.body.appendChild(container); // 关键：必须先添加到DOM树

    // 2. 使用React 18的createRoot渲染
    const root = createRoot(container);

    // 3. 清理函数：确保彻底卸载和移除DOM
    const cleanup = () => {
      root.unmount();
      document.body.removeChild(container); // 从DOM树移除
    };

    // 4. 渲染确认框组件
    root.render(
      <React.StrictMode>
        <ConfirmDialog
          {...options}
          visible={true}
          onConfirm={() => {
            resolve(true);
            cleanup();
          }}
          onCancel={() => {
            resolve(false);
            cleanup();
          }}
          closeOnClickOutside={true}
        />
      </React.StrictMode>
    );
  });
};

export default ConfirmDialog;