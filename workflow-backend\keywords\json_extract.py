# -*- coding: utf-8 -*-

#from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from robotlibcore import keyword
from typing import Dict, List, Optional, Any,Set
import json
import requests
import base64
import re
from loguru import logger

@keyword("Json Extract")
def json_extract(data, path:List[any] = None):
    # 处理JSON字符串输入
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            return None

    # 通过路径提取
    if path:
        #logger.info("结果" + str(_extract_by_path(data, path)))
        return _extract_by_path(data, path)

    return None


def _extract_by_path(data, path:List[any]):
    """通过路径提取值"""
    result = {}
    if path:
        for item in path:
            parts = _parse_path(item["realkey"])
            current = data

            for part in parts:
                if isinstance(current, dict):
                    if part not in current:
                        current = ""
                        continue
                    current = current[part]

                elif isinstance(current, list):
                    try:
                        index = int(part)
                        if index < 0 or index >= len(current):
                            current = ""
                            continue
                        current = current[index]
                    except (ValueError, IndexError):
                        str_list = [obj[part] for obj in current]
                        current = str_list
                        continue

                else:
                    current = ""
                    continue

            result_in = {
                     "variable":item["variable"],
                     "type":item["type"],
                     "desc":item["desc"],
                     "realkey":item["realkey"],
                     "result":current
            }

            result.setdefault(item["variable"],result_in)

        return result
    else:
        return None


# def _parse_path(path):
#     """解析路径字符串为组件列表"""
#     parts = []
#     current = ""
#     in_bracket = False
#
#     for char in path:
#         if char == '[':
#             if current:
#                 parts.append(current)
#                 current = ""
#             in_bracket = True
#         elif char == ']':
#             if in_bracket and current:
#                 parts.append(current)
#                 current = ""
#             in_bracket = False
#         elif char == '.' and not in_bracket:
#             if current:
#                 parts.append(current)
#                 current = ""
#         else:
#             current += char
#
#     if current:
#         parts.append(current)
#
#     return parts

def _parse_path(path):
    r_str2 = path.replace('[', '.').replace(']', '')
    parts = r_str2.split('.')
    return parts


# def _extract_recursive(data, field):
#     """递归查找所有匹配的字段"""
#     results = []
#
#     if isinstance(data, dict):
#         for key, value in data.items():
#             if key == field:
#                 results.append(value)
#             results.extend(_extract_recursive(value, field))
#
#     elif isinstance(data, list):
#         for item in data:
#             results.extend(_extract_recursive(item, field))
#
#     return results
#
#
# def _extract_single_level(data, field):
#     """在单层查找匹配的字段"""
#     results = []
#
#     if isinstance(data, dict):
#         if field in data:
#             results.append(data[field])
#
#     elif isinstance(data, list):
#         for item in data:
#             if isinstance(item, dict) and field in item:
#                 results.append(item[field])
#
#     return results


# 使用示例
if __name__ == "__main__":
    # 示例JSON数据
    json_data = '''
    {
        "users": [
            {
                "name": "Alice",
                "age": 30,
                "details": {
                    "city": "New York",
                    "hobbies": ["reading", "swimming"]
                }
            },
            {
                "name": "Bob",
                "age": 25,
                "details": {
                    "city": "Los Angeles",
                    "hobbies": ["running", "coding"]
                }
            }
        ]
    }
    '''

    path = [{
                "variable": "name",
                "type": "string",
                "desc": "小区",
                "realkey": "users.name"
              }]

    path1 = [{
        "variable": "name",
        "type": "string",
        "desc": "小区",
        "realkey": "users[0].name"
    }]

    path2 = [{
        "variable": "users",
        "type": "string",
        "desc": "小区",
        "realkey": "users"
    }]


    # 通过路径提取
    print(json_extract(json_data, path))  # 输出: Alice
    print(json_extract(json_data, path1))  # 输出: coding
    print(json_extract(json_data, path2))

    # # 处理JSON字符串
    # json_str = '{"a": {"b": [{"c": 1}, {"c": 2}]}}'
    # print(json_extract(json_str, ["a.b[1].c"]))  # 输出: 2