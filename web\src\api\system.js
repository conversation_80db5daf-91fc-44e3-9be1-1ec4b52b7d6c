import { request } from '@/utils/axios';
import { useUserStore } from "@/stores/user";
let retryNum = 0
const retryTime = 1500
const systemApi = {
    /*获取当前登录用户信息*/
    initUserInfo() {
        return new Promise((resolve, reject) => {
            request
                .get(`/uniwim/ump/currUserInfo`, {})
                .then((result) => {
                    window.WIMAI_CurrentUser = result;
                    if (result) {
                        const userStore = useUserStore()
                        // 登录信息存储
                        userStore.setUserInfo(result);
                        resolve(result);
                    } else {
                        resolve({ tenantId: '5d89917712441d7a5073058c' }); // 默认和达科技租户，如果没有获取到信息的话
                        if (retryNum < 10) {
                            retryNum++
                            setTimeout(() => {
                                systemApi.initUserInfo()
                            }, retryTime);
                        }
                    }
                })
                .catch((err) => {
                    resolve({ tenantId: '5d89917712441d7a5073058c' });
                    if (retryNum < 10) {
                        retryNum++
                        setTimeout(() => {
                            systemApi.initUserInfo()
                        }, retryTime);
                    }
                });
        });
    },
};
export default systemApi;
