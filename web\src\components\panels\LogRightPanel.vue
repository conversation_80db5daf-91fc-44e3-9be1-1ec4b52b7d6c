<template>
  <div class="log-property-panel view-box">
    <div class="panel-header">
      <h3 class="panel-title">
        <div class="node-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <span>执行记录</span>
      </h3>
      <div class="header-actions">
        <el-icon @click="handleClose"><Close /></el-icon>
      </div>
    </div>
    <div class="time-view-box">
      <div class="time-view-container">
        <el-scrollbar
          ref="panelScroll"
          style="height: 100%; width: 100%"
          wrap-style="overflow-x:hidden;"
        >
          <div class="monitor-box">
            <el-timeline>
              <el-timeline-item
                v-for="(it, index) in logData"
                :key="index"
                timestamp=""
                placement="top"
                :class="{
                  'error-tail': it.state === 'failed',
                  'loading-tail': it.state !== 'failed' && it.node_id !== 'end_node' && index === logData.length - 1,
                }"
              >
                <template #dot>
                  <div class="time-icon-box">
                    <div class="time-icon loading" v-if="it.state !== 'failed' && it.node_id !== 'end_node' && index === logData.length - 1 && isExecuting">
                      <el-icon class="time-icon-select is-loading"><Refresh /></el-icon>
                    </div>
                    <div class="time-icon error" v-else-if="it.state === 'failed'">
                      <el-icon class="time-icon-select"><Close /></el-icon>
                    </div>
                    <div class="time-icon" v-else>
                      <el-icon class="time-icon-select"><Select /></el-icon>
                    </div>
                  </div>
                  <div class="time-box">
                    <div class="time-left">
                      <div class="time-time">{{ it.time }}</div>
                      <!-- 显示重试次数 -->
                      <div class="retry-count" v-if="it.failedCount > 1">
                        重试 {{ it.failedCount - 1 }} 次
                      </div>
                    </div>
                  </div>
                </template>
                <div class="time-content">
                  <div class="time-title-box">
                    <div class="time-title-icon" :style="{ background: formatColor(it)}">
                      <el-icon>
                        <i :class="formatIcon(it)"></i>
                      </el-icon>
                    </div>
                    <div class="time-title" :title="it.node_name">{{ it.node_name }}</div>
                  </div>
                  <div class="content-item" v-if="handleContent(it)">
                    <div class="item-title">
                      <div class="complete-content" :title="handleContent(it)">
                        {{ handleContent(it) }}
                      </div>
                    </div>
                  </div>
                  <template v-if="it.node_id !== 'start_node' && it.node_id !== 'end_node'">
                    <div class="content-item flex-column code-content"
                         v-if="hasPuts(it.inputs)">
                      <div class="item-title font-bold" @click="toggleExpand(it, '_inputs')">
                        输入：
                        <div>
                          <el-icon v-if="expanedDict[it.history_id + it.node_id + '_inputs']" title="收起">
                            <CaretTop />
                          </el-icon>
                          <el-icon v-else title="展开">
                            <CaretBottom />
                          </el-icon>
                        </div>
                      </div>
                      <div v-show="expanedDict[it.history_id + it.node_id + '_inputs']" style="width: 100%;">
                        <json-viewer
                          :value="jsonViewParse(it.inputs)"
                          :copyable="{ copyText: '复制', copiedText: '已复制' }"
                          sort
                          boxed
                          show-double-quotes
                          class="my-awesome-json-theme"
                          theme="my-awesome-json-theme"
                        />
                      </div>
                    </div>
                    <div class="content-item flex-column code-content"
                      v-if="hasPuts(it.outputs) && it.state !== 'failed' && index < logData.length - 1">
                      <div class="item-title font-bold" @click="toggleExpand(it, '_outputs')">
                        输出：
                        <div>
                          <el-icon v-if="expanedDict[it.history_id + it.node_id + '_outputs']" title="收起">
                            <CaretTop />
                          </el-icon>
                          <el-icon v-else title="展开">
                            <CaretBottom />
                          </el-icon>
                        </div>
                      </div>
                      <div v-show="expanedDict[it.history_id + it.node_id + '_outputs']" style="width: 100%;">
                        <json-viewer
                          :value="jsonViewParse(it.outputs)"
                          :copyable="{ copyText: '复制', copiedText: '已复制' }"
                          sort
                          boxed
                          show-double-quotes
                          class="my-awesome-json-theme"
                          theme="my-awesome-json-theme"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-if="it.node_id !== 'end_node' && index === logData.length - 1 && isExecuting">
                    <div class="log-loading" v-if="it.state !== 'failed' && !it.failedCount">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>正在执行中...</span>
                    </div>
                    <div class="log-loading" v-if="it.state === 'failed' || it.failedCount">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>执行失败，重试中...</span>
                    </div>
                  </template>
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty
              v-if="!logData.length && !isExecuting"
              description="暂无执行日志"
              style="height: 80vh"
            />
            <el-empty
              v-if="!logData.length && isExecuting"
              description="任务执行中，请稍等"
              style="height: 80vh"
            />
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onUnmounted, nextTick, watch } from 'vue'
import { useLogNewStore } from '@/stores/logNew.ts'
import { generateComponentDescription } from '@/utils/componentDisplay.ts'
import { getCategoryColor } from '@/utils/componentCategories.ts'
import { useWorkflowStore } from '@/stores/workflow'

const workflowStore = useWorkflowStore()
import type { ElScrollbar } from 'element-plus'
import JsonViewer from 'vue-json-viewer'

const panelScroll = ref<InstanceType<typeof ElScrollbar>>()

const expanedDict = ref({})

// 日志
const logNewStore = useLogNewStore()

const logData = computed(() => {
  return logNewStore.logData.filter((it: any) => !!it.node_id)
})

watch(
  logData,
  (newVal, oldVal) => {
    if (newVal.length > oldVal.length) {
      nextTick(() => {
        if (panelScroll.value) {
          panelScroll.value.setScrollTop(panelScroll.value.wrapRef?.scrollHeight || 0)
        }
      })
    }
    // 清除下展开数据字典
    if (!newVal.length) {
      expanedDict.value = {}
    }
  },
  { deep: true },
)

// 加载状态
const isExecuting = computed(() => {
  return logNewStore.isExecuting
})

// 格式化获取node的颜色
const formatColor = (data: object) => {
  const nodes = workflowStore.nodes
  const node = nodes.find((it: object) => it.id === data.node_id)
  return getCategoryColor(node?.data?.category)
}

const formatIcon = (data: object) => {
  const nodes = workflowStore.nodes
  const node = nodes.find((it: object) => it.id === data.node_id)
  if(node?.type === 'start'){
    return 'action-iconfont icon-shouye1'
  }
  if(node?.type ==='end'){
    return 'action-iconfont icon-xunhuan'
  }
  return node ? node.data.icon : 'action-iconfont icon-zujian'
}

const handleContent = (data: object) => {
  const nodes = workflowStore.nodes
  const node = nodes.find((it: object) => it.id === data.node_id)
  let html = ''
  if (node?.data) {
    const config = node.data.config || {}
    config.type = 'taskMonitor'
    html = generateComponentDescription(node, 'taskMonitor')
  }
  if (html.includes('点击配置组件参数')) html = (data.describe || '')
  return html
}

// 格式化下output内容
const jsonViewParse = (data: string) => {
  try {
    return JSON.parse(data)
  } catch (e) {
    return data
  }
}

// 添加 toggleExpand 方法
const toggleExpand = (item: any, key: string) => {
  expanedDict.value[item?.history_id + item?.node_id + key] =
    !expanedDict.value[item?.history_id + item?.node_id + key]
}

// 判断是否要显示
const hasPuts = (inputs: any) => {
  if (typeof inputs === 'object') {
    return JSON.stringify(inputs) !== '{}'
  }
  return inputs && inputs !== '{}' && inputs !== 'null'
}

// 关闭日志面板
const handleClose = () => {
  // 隐藏日志面板
  logNewStore.setLogPanel(false)
}

onUnmounted(() => {
  // 设置执行状态
  logNewStore.setLogExecuting(false)
  // 清除日志信息
  // logNewStore.clearLogs()
  // 隐藏日志面板
  logNewStore.setLogPanel(false)
})
</script>

<style scoped lang="scss">
.flex-column {
  flex-direction: column;
}

.font-bold {
  font-weight: bold;
  color: #333333 !important;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 40px;
  z-index: 7;
  background: #F5F5F5;
}

.code-content {
  background: #f5f5f5;
  border: 1px solid #ebeef5;
  margin: 8px 0 0;
  padding-top: 0 !important;
}

.log-property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .node-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--el-color-primary);
    color: white;
    border-radius: 6px;
    margin-right: 8px;

    .action-iconfont {
      font-size: 14px;
      font-weight: normal;
    }
  }

  .time-view-box {
    flex: 1;
    overflow: hidden;

    .right-view-header {
      height: 40px;
      padding: 8px 16px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      border-bottom: 1px solid #e8ecf0;
      white-space: nowrap;

      .view-header-close {
        cursor: pointer;
      }
    }

    .time-view-container {
      height: 100%;

      .monitor-box {
        padding: 16px;

        .el-timeline {
          padding-left: 0;
        }

        .el-timeline-item {
          padding-bottom: 10px;
          transition: all 0.2s;

          &.error-tail {
            ::v-deep(.el-timeline-item__tail) {
              border-left: 2px solid #f6693e;
            }

            .time-content {
              border: 1px solid #f6693e;
            }
          }

          &.loading-tail {
            ::v-deep(.el-timeline-item__tail) {
              border-left: 2px solid #0099cb;
            }

            .time-content {
              border: 1px solid #0099cb;
            }
          }

          &.waiting-tail {
            ::v-deep(.el-timeline-item__tail) {
              border-left: 2px solid #9e9e9e;
            }

            .time-content {
              border: 1px solid #9e9e9e;
            }
          }
        }

        .time-content {
          // width: 100%;
          padding: 8px 8px 16px;
          box-sizing: border-box;
          // border: 1px solid #E6E7E9;
          border: 1px solid #17b26a;
          border-radius: 8px;
          margin-left: 16px;

          .time-title-box {
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            padding: 8px;
            background: #fff;
            z-index: 8;
            &+.content-item{
              margin-top: 0;
            }

            .time-title-icon {
              width: 24px;
              height: 24px;
              color: #ffffff;
              font-size: 12px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 8px;

              i {
                font-size: 14px;
              }
            }

            .time-title {
              font-size: 14px;
              color: #222222;
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .content-item {
            // margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 8px 8px 0;
            border-radius: 4px;
            // background: #F5F5F5;
            line-height: 18px;

            .item-title {
              width: 100%;
              display: flex;
              align-items: flex-start;
              color: #5c5f66;

              .complete-content {
                //font-size: 12px;
                color: #5c5f66;
                line-height: 1.5;
                word-wrap: break-word;
                word-break: break-word;
                white-space: pre-line;
                overflow-wrap: break-word;
                max-width: 100%;
                min-height: 18px;
              }
            }
          }
        }

        ::v-deep(.el-timeline-item__wrapper) {
          padding-left: 16px;

          .el-timeline-item__content {
            margin-top: 38px;
          }
        }

        ::v-deep(.el-timeline-item__tail) {
          top: 12px;
          left: 5px;
          border-left: 2px solid #17b26a;
        }

        ::v-deep(.el-timeline-item__dot) {
          width: 100%;
          justify-content: flex-start;
          z-index: 1;

          .time-icon-box {
            padding: 5px 0;
            background: #ffffff;
            margin-left: -3px;

            .time-icon {
              width: 18px;
              height: 18px;
              background: #17b26a;
              border: 3px solid #e8f7f0;
              border-radius: 50%;
              // flex-shrink: 0;
              display: inline-flex;
              justify-content: center;
              align-items: center;

              .time-icon-select {
                font-size: 10px;
                color: #ffffff;
              }

              &.error {
                background: #ef3f09;
                border: 3px solid #f8c6b8;
              }

              &.loading {
                background: #0099cb;
                border: 3px solid #e8f7f0;
              }

              &.waiting {
                background: #9e9e9e;
                border: 3px solid #dcfbec;
              }
            }
          }

          .time-box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 32px;

            //&:hover {
            //  background-color: #f2f2f2;
            //  border-radius: 2px;
            //}

            .time-left {
              width: 100%;
              display: flex;
              align-items: center;
              cursor: pointer;

              .time-time {
                font-size: 12px;
                color: #b0b3c0;
                margin-left: 16px;
                flex-shrink: 0;
              }

              .isover {
                color: #0099cb;
              }

              i {
                color: rgba(255, 255, 255, 0.9);
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}

// values are default one from jv-light template
// 自定义高亮json样式
.my-awesome-json-theme {
  border: none;
  white-space: nowrap;
  color: #333333;
  font-size: 14px;
  font-family: Consolas, Menlo, Courier, monospace;
  font-weight: 600;
  width: 100%;

  &.boxed {
    box-sizing: border-box;
    border: none;
    border-top: 1px solid #e5e5e5;
    border-radius: 0;

    &:hover {
      box-shadow: none;
      border: none;
      border-top: 1px solid #e5e5e5;
    }
  }

  :deep(.jv-tooltip.right) {
    right: 6px;
    font-weight: normal;
    font-size: 12px;
  }

  :deep(.jv-code) {
    padding: 15px 10px;
    overflow: auto;

    .jv-toggle {
      &:before {
        padding: 0px 2px;
        border-radius: 2px;
      }

      &:hover {
        &:before {
          background: #eee;
        }
      }
    }

    .jv-node {
      line-height: 20px;
    }

    .jv-ellipsis {
      color: #999;
      background-color: #eee;
      display: inline-block;
      line-height: 0.9;
      font-size: 0.9em;
      padding: 0px 4px 2px 4px;
      border-radius: 3px;
      vertical-align: 2px;
      cursor: pointer;
      user-select: none;
    }

    .jv-button {
      color: #49b3ff;
    }

    .jv-key {
      color: #92278f;
      margin-right: 8px;
    }

    .jv-item {
      &.jv-array {
        color: #111111;
      }

      &.jv-boolean {
        color: #fc1e70;
      }

      &.jv-function {
        color: #067bca;
      }

      &.jv-number {
        color: #067bca;
      }

      &.jv-number-float {
        color: #067bca;
      }

      &.jv-number-integer {
        color: #067bca;
      }

      &.jv-object {
        color: #111111;
      }

      &.jv-undefined {
        color: #e08331;
      }

      &.jv-string {
        color: #3ab54a;
        word-break: break-word;
        white-space: normal;
      }
    }
  }
}
.log-loading{
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  padding: 8px 8px 0;
  box-sizing: border-box;
}
.retry-count {
  font-size: 12px;
  color: #e6a23c;
  margin-left: 8px;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>
