<template>
  <div id="app" class="app-container">
    <pageLoading v-if="loadingStore.isLoading"/>
    <el-container>
      <el-header height="64px" class="app-header">
            <img
              style="height: 36px;margin-left: -4px;"
              src="./assets/images/admin/logoV2.png"
              alt="WimTask logo"
            />
            <!-- <img style="width: 72px;height: 18px;margin-left:10px" src="./assets/images/admin/logoText.png" alt=""> -->
            <div class="menus-box">
              <el-menu
                  :default-active="activeIndex"
                  mode="horizontal"
                  @select="handleSelect"
                >
                <template :key="menu.Path"  v-for="menu in menus">
                  <!-- <el-menu-item v-if="!!menu.num" :index="menu.Path" :key="menu.Path">{{ menu.Name }}</el-menu-item> -->
                  <el-badge :value="menu.num" :hidden="!menu.num">
                    <el-menu-item :index="menu.Path" :key="menu.Path">{{ menu.Name }}</el-menu-item>
                  </el-badge>
              </template>
                </el-menu>
            </div>
            <slot name="extend">
              <!-- <userset :user="user" :rid="rid"></userset> -->
            </slot>
        </el-header>
        <el-main class="app-page" >
            <div style="height: 100%; overflow-y: auto;">
                <transition name="fade" mode="out-in">
                    <keep-alive>
                        <RouterView />
                    </keep-alive>
                </transition>
            </div>
        </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import pageLoading from '@/components/pageLoading/index.vue'
import userset from '@/components/layout/userset.vue'
import { onMounted, onUnmounted,ref,watch} from 'vue'
import { RouterLink, RouterView } from 'vue-router'
import { useRoute, useRouter } from 'vue-router'
import { useLoadingStore } from '@/stores/loading';
import saasApi from '@/api/index';
const loadingStore = useLoadingStore();
const route = useRoute()
const router = useRouter()

const activeIndex = ref('/versionManager')
const menus = ref([
  {Name:"授权管理",Path:"/subscribe",num:0},
  {Name:"服务包管理",Path:"/servicePackage",num:0},
  {Name:"内容管理",Path:"/content",num:0},
  {Name:"客户申请",Path:"/customer",num:0},
  {Name:"指令管理",Path:"/instruction",num:0},
  {Name:"版本管理",Path:"/versionManager",num:0},
  {Name:"环境配置",Path:"/envConfig",num:0}
])

const user = ref({
            name: '管理员',
            Roles: [
                {
                    role: '1232sdf',
                    name: '和达管理员'
                },
                {
                    role: '1232ssdf',
                    name: '深水管理员'
                }
            ]
        })
const rid = ref("")


const handleSelect = (key: string, keyPath: string) => {
  router.push({path:key,query:route.query})
}


watch(
  () => route,
  (newRoute, oldRoute) => {
    activeIndex.value = newRoute.path;
    console.log('路由发生变化：', { newRoute, oldRoute });
  },
  { deep: true }
);
onMounted(() => {
  debugger;

  saasApi.AIAgentWimtaskCustomerApplicationQuery({
    conditions: [],
    data: {
      status:0
    },
    order:[
      {
        "Field":"created",
        "Value":-1
      }
    ],
    index: 1,
    size: 100,
  }).then((res:any) => {
    if (typeof res?.rows == 'object') {
      let obj = menus.value.find(menu=>menu.Name==='客户申请');
      debugger;
      if(obj){
        obj.num = res.total
      }
    }
  }).finally(() => {
  })
  activeIndex.value = route.path;
})

onUnmounted(() => {
  // 清理监听器
  saasApi.AIAgentWimtaskCustomerApplicationQuery({
    conditions: [],
    data: {
      status:0
    },
    order:[
      {
        "Field":"created",
        "Value":-1
      }
    ],
    index: 1,
    size: 100,
  }).then((res:any) => {
    if (typeof res?.rows == 'object') {
      let obj = menus.value.find(menu=>menu.Name==='客户申请');
      debugger;
      if(obj){
        obj.num = res.total
      }
    }
  }).finally(() => {
  })
})
</script>
<style scoped lang="less">
.app-header{
  display:flex;
  height:64px;
  align-items: center;
}
.menus-box{
  flex:1;
  margin-left:55px;
  height:64px;
  :deep(.el-badge__content.is-fixed){
    position: absolute;
    top: 18px;
    right: calc(1px + var(--el-badge-size))
  }
  :deep(.el-badge){
    // margin-top:12px;
  }
  :deep(.el-menu-item){
    // height:28px;
    // line-height:28px;
  }
}
:deep(.el-container){
  height:100%;
}
.el-menu--horizontal{
  height:100%;
}
.el-menu--horizontal.el-menu{
  border-bottom:0;
  position:relative;
}
.el-menu--horizontal.el-menu .el-menu-item{
  // border:none!important;
  // padding:0 16px;
  padding:0;
  margin: 0 16px;
  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 16px;
  height: 64px;
  line-height: 64px;
}
.el-menu--horizontal.el-menu .el-menu-item.is-active{
  font-weight:700;
  border-bottom: 2px solid var(--el-menu-active-color);
}
.el-menu--horizontal.el-menu .el-menu-item.is-active:after {
    // content: "";
    // position: absolute;
    // left: -14px;
    // margin-left: 50%;
    // bottom: 0;
    // width: 28px;
    // height: 2px;
    // background: #0054c9;
    // border-radius: 1px;
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal .el-menu-item:not(.is-disabled):focus{
  background:transparent
}
.el-main{
  padding:0;
}
</style>
<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 主题定制 */
:root {

}
</style>
