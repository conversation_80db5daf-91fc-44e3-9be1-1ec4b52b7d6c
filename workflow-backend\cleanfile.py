import sys
import os
import shutil
import ctypes
from typing import List

import win32api
import win32con
import win32process
import psutil
from logging import getLogger
import time
import subprocess
import re
import win32security
import enum
from ctypes import wintypes

logger = getLogger(__name__)


def find_processes_by_exe(exe_path: str) -> List[psutil.Process]:
    """查找所有运行指定exe的进程(增强版)"""
    abs_path = os.path.abspath(exe_path)
    processes = []

    for proc in psutil.process_iter(["pid", "name", "exe", "cmdline", "ppid"]):
        try:
            # 检查三种匹配情况：exe路径、命令行参数、父进程
            if (
                proc.info.get("exe") and os.path.samefile(proc.info["exe"], abs_path)
            ) or (
                proc.info.get("cmdline") and abs_path in " ".join(proc.info["cmdline"])
            ):
                processes.append(psutil.Process(proc.pid))
        except (psutil.NoSuchProcess, psutil.AccessDenied, FileNotFoundError):
            continue
    return processes


def kill_processes(processes: List[psutil.Process]) -> None:
    """终止进程列表(带异常处理)"""
    for proc in processes:
        try:
            proc.terminate()
            time.sleep(0.5)
            if proc.is_running():
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue


def scan_and_clean(folder_path: str, exe_name: str) -> None:
    """主流程：扫描文件夹并清理进程"""
    if not os.path.exists(folder_path):
        print(f"错误：文件夹 {folder_path} 不存在")
        return

    exe_path = os.path.join(folder_path, exe_name)
    if not os.path.exists(exe_path):
        print(f"错误：文件 {exe_path} 不存在")
        return

    processes = find_processes_by_exe(exe_path)
    if not processes:
        print(f"未找到运行 {exe_name} 的进程")
        return

    print(f"找到 {len(processes)} 个相关进程：")
    for p in processes:
        print(f"- PID: {p.pid}, 名称: {p.name()}")

    kill_processes(processes)
    try:
        os.remove(exe_path)
        print(f"成功删除 {exe_path}")
    except PermissionError:
        print(f"删除失败：文件仍被占用或无权限")


def is_admin():
    """检查是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def run_as_admin():
    """以管理员权限重新运行程序"""
    ctypes.windll.shell32.ShellExecuteW(
        None, "runas", sys.executable, " ".join(sys.argv), None, 1
    )


def is_folder_empty(folder_path):
    """检查文件夹是否为空（返回布尔值）"""
    return len(os.listdir(folder_path)) == 0


def take_ownership(path):
    """获取文件/目录所有权"""
    try:
        # 使用icacls命令授予管理员权限
        subprocess.run(
            f'icacls "{path}" /grant administrators:F /T /C',
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        return True
    except:
        return False


def terminate_processes(processes):
    """终止指定进程"""
    for proc in processes:
        try:
            p = psutil.Process(proc["pid"])
            p.terminate()
            print(f"已终止进程: {proc['name']} (PID: {proc['pid']})")
            time.sleep(1)
        except:
            try:
                p.kill()
                print(f"已强制终止进程: {proc['name']} (PID: {proc['pid']})")
            except:
                print(f"无法终止进程: {proc['name']} (PID: {proc['pid']})")


def force_remove(path):
    """强制删除文件/目录，处理权限和占用问题"""
    if not os.path.exists(path):
        return True

    try:
        # 先尝试常规删除
        if os.path.isfile(path):
            os.remove(path)
        else:
            shutil.rmtree(path, ignore_errors=True)

        if not is_folder_empty(path):
            for root, dirs, files in os.walk(path, topdown=False):
                print(f"处理目录: {root}")
                # 这里可以添加你的操作（如删除文件/文件夹）
                for file in files:
                    file_path = os.path.join(root, file)

                    # 1. 查找并终止占用进程
                    scan_and_clean(root, file)

                    try:
                        os.remove(file_path)
                    except PermissionError as e:
                        print("权限不足，尝试获取所有权...")
                        # 3. 获取所有权后再尝试删除
                        if take_ownership(file_path):
                            try:
                                if os.path.isfile(file_path) or os.path.islink(
                                    file_path
                                ):
                                    os.remove(file_path)
                                else:
                                    shutil.rmtree(file_path)
                                print(f"获取权限后成功删除: {file_path}")
                                return True
                            except Exception as e:
                                print(f"删除失败: {file_path}：{e.args[1]}")
                                pass

                # 如果目录已空，可以删除目录本身
                if not os.listdir(root):
                    print(f"✅ 目录已空，可删除: {root}")
                    # os.rmdir(root)  # 示例：删除空目录

        return True
    except Exception as e:
        print(f"删除失败: {file_path}：{e.args[1]}")
        return False


def clean_temp_dir(temp_dir):
    """清理PyInstaller生成的临时目录"""
    if getattr(sys, "frozen", False) and hasattr(sys, "_MEIPASS"):
        # temp_dir = sys._MEIPASS
        # logger.info(f"开始清理临时目录: {temp_dir}")

        # 检查是否以管理员权限运行
        if not is_admin():
            print("请以管理员权限运行，正在尝试提升权限...")
            run_as_admin()
            sys.exit(0)

        # 强制删除目录
        if force_remove(temp_dir):
            logger.info(f"成功删除临时目录: {temp_dir}")
            return True
        else:
            # 最后的补救：注册系统重启时删除（适用于顽固文件）
            try:
                win32api.MoveFileEx(
                    temp_dir, None, win32con.MOVEFILE_DELAY_UNTIL_REBOOT  # 重启后删除
                )
                logger.info(f"已安排系统重启时删除临时目录: {temp_dir}")
                return True
            except Exception as e:
                logger.error(f"安排重启删除失败: {e}")
        return True
    else:
        logger.warning("未检测到PyInstaller临时目录，跳过清理")
        return False


def main():
    pass


if __name__ == "__main__":
    clean_temp_dir()
