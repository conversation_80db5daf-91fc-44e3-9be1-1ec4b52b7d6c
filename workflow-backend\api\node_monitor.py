import json
import os

from fastapi import FastAP<PERSON>, Response,<PERSON><PERSON><PERSON><PERSON>,Header,Body
from fastapi.responses import StreamingResponse,JSONResponse
import asyncio
import time
from loguru import logger
app = APIRouter(prefix="/api",tags=["node_monitor"])
from .crud import forward_post
exc_result = {}

def step_call_back(data: dict):
    execution_id = data.get("execution_id","")
    result = exc_result.get(execution_id)
    if not result:
        result = []
        exc_result[execution_id] = result

    result.append(data)

@app.post("/node_monitor")
def event_stream(execution_id:str):

    async def generate():
        # 发送初始化消息
        result = exc_result.get(execution_id,None)
        yield "event: connected\ndata: OK\n\n"
        if not result:
            yield "event: disconnected\ndata: 没有找到执行的日志\n\n"
        else:
            stops1 = True
            start = int(time.time())
            while stops1:
                # 重新获取
                result = exc_result.get(execution_id,[])
                # 校验返回结果是否被删除
                stops1 = execution_id in exc_result
                while result:
                    item = result.pop(0)
                    end = item.get("end","0")
                    # 获取到结束标识
                    if end=="1":
                        yield f"data: End\n\n"
                        del exc_result[execution_id]
                        stops1 = False

                        break
                    else:
                        yield f"data: Event {json.dumps(item,ensure_ascii=False)}\n\n"

                    now = int(time.time())
                    if now > start+300:
                        yield f"data: End 超时已结束\n\n"
                        stops1 = False
                await asyncio.sleep(1)

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={"Cache-Control": "no-cache"}
    )

@app.post("/exe_files")
async def exe_files(items: dict = Body(None),authorization: str = Header(None)):
    resp = forward_post("/wimai/api/task/excLogs/query",authorization,items)
    result = resp["Response"]

    files = []
    rows = result.get("rows",[])
    for row in rows:
        node_type = row.get("nodeType", "")
        nodeId = row.get("nodeId", "")
        if node_type == "word_create" or node_type == "excel_create":
            file = {"nodeType":node_type,"nodeId":nodeId,"excTime":row["excTime"]}
            output = row.get("output","")
            if output:
                param = json.loads(output)
                file["dir"] = param['file_path']
                file["name"] = param['file_name']

                ext_name = "xlsx" if  node_type == "excel_create" else "docx"
                file["path"] = f"{param['file_path']}/{param['file_name']}.{ext_name}"
                file["size"] = os.path.getsize(file["path"]) if os.path.exists(file["path"]) else -1
            files.append(file)

    return JSONResponse(content={"Response": files,"code":200})
