<template>
  <div class="invalid-nodes-container" v-show="workflowStore.invalidNodes.length">
    <el-badge
      style="display: inline-flex;"
      :value="workflowStore.invalidNodes.length"
      :hidden="!workflowStore.invalidNodes.length"
    >
      <el-popover
        placement="bottom"
        trigger="click"
        :width="400"
        :offset="18"
        :hide-after="0"
        v-model:visible="popoverVisible"
      >
        <div class="popover-header">
          <span>问题</span>
          <span>({{ workflowStore.invalidNodes.length }})</span>
          <el-icon class="popover-header-close" @click="popoverVisible = false">
            <Close />
          </el-icon>
        </div>
        <div class="popover-header-desc">运行或保存前确保所有问题均已解决</div>
        <div class="invalid-nodes-list" v-if="workflowStore.invalidNodes.length">
          <div
            v-for="(item, index) in workflowStore.invalidNodes"
            :key="index"
            class="invalid-node-item"
            :style="{
              '--el-color-primary':
                item.node.id === 'start_node' || item.node.id === 'end_node'
                  ? '#67c23a'
                  : getCategoryColor(item.node.data.category),
            }"
            @click="selectNode(item.node.id)"
          >
            <div class="node-header">
              <div class="node-icon">
                <el-icon v-if="item.node.id === 'start_node'" :size="12">
                  <HomeFilled />
                </el-icon>
                <el-icon v-else-if="item.node.id === 'end_node'" :size="12">
                  <SuccessFilled />
                </el-icon>
                <i v-else-if="item.node.data.icon" :class="item.node.data.icon"></i>
                <el-icon v-else :size="12">
                  <Setting />
                </el-icon>
              </div>
              <div class="node-title">
                <span>{{ item.node.data?.label || '未命名节点' }}</span>
              </div>
              <!-- 系统节点标识 -->
              <div class="system-node-badge" v-if="item.node.data.isSystemNode">
                <el-icon :size="12">
                  <Lock />
                </el-icon>
              </div>
            </div>

            <div v-for="(error, i) in item.result.errors" :key="i" class="error-message">
              <el-icon color="#e6a23c" size="14">
                <Warning />
              </el-icon>
              <span>{{ error.message }}</span>
            </div>
          </div>
        </div>
        <el-empty v-else description="所有组件均正常" :image-size="100" />
        <template #reference>
          <el-button icon="WarningFilled">问题</el-button>
        </template>
      </el-popover>
    </el-badge>
    <el-divider direction="vertical" style="margin: 0 16px" />
  </div>
</template>

<script lang="ts" setup>
import { useWorkflowStore } from '@/stores/workflow'
import { computed } from 'vue'
import { getCategoryColor } from '@/utils/componentCategories.ts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const workflowStore = useWorkflowStore()
const popoverVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

/**
 * 选中节点
 * @param nodeId 节点ID
 */
const selectNode = (nodeId: string) => {
  workflowStore.selectNode(nodeId)
  popoverVisible.value = false
  emit('select', nodeId)
}

// // 当invalidNodes变化时自动弹出
// watch(() => workflowStore.invalidNodes.length, (newVal) => {
//   if (newVal > 0) {
//     popoverVisible.value = true
//   }
// })
</script>

<style scoped lang="scss">
.invalid-nodes-container {
  display: flex;
  align-items: center;
}
.popover-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  position: relative;
  margin-bottom: 8px;
}

.popover-header-close {
  position: absolute;
  right: -12px;
  top: -12px;
  cursor: pointer;
  padding: 16px;
}

.popover-header-desc {
  font-size: 12px;
  color: #666666;
}

.invalid-nodes-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.invalid-node-item {
  border: 1px solid #e7e7e7;
  border-radius: 8px;
  background: #fcfcfd;
  overflow: hidden;
  cursor: pointer;

  & ~ .invalid-node-item {
    margin-top: 4px;
  }
}

.error-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
  padding: 6px 12px;
  color: #667085;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  min-height: 40px;
  background: #ffffff;
}

.node-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary);
  color: white;
  border-radius: 6px;

  .action-iconfont {
    font-size: 12px;
    line-height: 12px;
  }
}

.node-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.system-node-badge {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6a23c;
  color: white;
  border-radius: 50%;
  font-size: 10px;
}
</style>
