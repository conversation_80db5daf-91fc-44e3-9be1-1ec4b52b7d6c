/**
 * HTTP/API组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const httpGetSchema: ComponentConfigSchema = {
  componentType: 'http_get',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'HTTP GET请求的基本参数',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'headers',
      label: '请求头',
      description: 'HTTP请求头的配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '请求的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    url: {
      type: 'url',
      label: 'API地址',
      description: '要请求的API端点URL',
      placeholder: 'https://api.example.com/data',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: 'API地址不能为空',
        },
        {
          type: 'pattern',
          value: '^https?://.+',
          message: 'URL必须以http://或https://开头',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '请求的最大等待时间',
      group: 'basic',
      order: 2,
      default: 30,
      min: 1,
      max: 300,
      unit: '秒',
    },

    params: {
      type: 'json',
      label: '查询参数',
      description: 'URL查询参数（JSON格式）',
      placeholder: `{
  "page": 1,
  "limit": 10
}`,
      group: 'basic',
      order: 3,
      rows: 6,
      variableSupport: false,
    },

    headers: {
      type: 'json',
      label: '请求头',
      description: 'HTTP请求头（JSON格式）',
      placeholder: `{
  "Authorization": "Bearer token",
  "Content-Type": "application/json"
}`,
      group: 'headers',
      order: 1,
      rows: 6,
      variableSupport: false,
    },

    auth_type: {
      type: 'radio',
      label: '认证方式',
      description: '选择API认证方式',
      group: 'headers',
      order: 2,
      default: 'none',
      options: [
        { label: '无', value: 'none', description: '不需要认证' },
        { label: 'API Key', value: 'api_key', description: 'API密钥认证' },
        { label: 'Bearer Token', value: 'bearer', description: 'JWT或OAuth2 Bearer令牌' },
        { label: 'Basic Auth', value: 'basic', description: '用户名密码认证' },
      ],
    },

    auth_token: {
      type: 'password',
      label: '认证令牌',
      description: 'Bearer Token或API Key',
      placeholder: '输入认证令牌',
      group: 'headers',
      order: 3,
      conditions: [
        {
          field: 'auth_type',
          operator: 'in',
          value: ['bearer', 'api_key'],
        },
      ],
    },

    auth_username: {
      type: 'string',
      label: '用户名',
      description: 'Basic认证的用户名',
      group: 'headers',
      order: 4,
      conditions: [
        {
          field: 'auth_type',
          operator: 'equals',
          value: 'basic',
        },
      ],
    },

    auth_password: {
      type: 'password',
      label: '密码',
      description: 'Basic认证的密码',
      group: 'headers',
      order: 5,
      conditions: [
        {
          field: 'auth_type',
          operator: 'equals',
          value: 'basic',
        },
      ],
    },

    verify_ssl: {
      type: 'boolean',
      label: '验证SSL证书',
      unit: '是否验证HTTPS证书',
      group: 'advanced',
      order: 1,
      default: true,
    },

    follow_redirects: {
      type: 'boolean',
      label: '跟随重定向',
      description: '是否自动跟随HTTP重定向',
      group: 'advanced',
      order: 2,
      default: true,
    },

    max_redirects: {
      type: 'number',
      label: '最大重定向次数',
      description: '允许的最大重定向次数',
      group: 'advanced',
      order: 3,
      default: 5,
      min: 0,
      max: 20,
      conditions: [
        {
          field: 'follow_redirects',
          operator: 'equals',
          value: true,
        },
      ],
    },

    retry_count: {
      type: 'number',
      label: '重试次数',
      description: '请求失败时的重试次数',
      group: 'advanced',
      order: 4,
      default: 0,
      min: 0,
      max: 5,
    },

    retry_delay: {
      type: 'number',
      label: '重试间隔',
      description: '重试之间的等待时间',
      group: 'advanced',
      order: 5,
      default: 1,
      min: 0.1,
      max: 60,
      step: 0.1,
      precision: 1,
      unit: '秒',
      conditions: [
        {
          field: 'retry_count',
          operator: 'greater',
          value: 0,
        },
      ],
    },
  },

  presets: {
    simple: {
      label: '简单请求',
      description: '基本的GET请求配置',
      config: {
        timeout: 30,
        verify_ssl: true,
        follow_redirects: true,
      },
    },

    api_with_auth: {
      label: 'API认证请求',
      description: '带认证的API请求配置',
      config: {
        auth_type: 'bearer',
        timeout: 30,
        verify_ssl: true,
        retry_count: 2,
        retry_delay: 1,
      },
    },

    robust: {
      label: '稳定请求',
      description: '具有重试机制的稳定请求配置',
      config: {
        timeout: 60,
        verify_ssl: true,
        follow_redirects: true,
        retry_count: 3,
        retry_delay: 2,
      },
    },
  },

  examples: [
    {
      title: '获取天气信息',
      description: '从天气API获取城市天气信息',
      config: {
        url: 'https://api.openweathermap.org/data/2.5/weather',
        params: { q: 'Beijing', appid: 'your_api_key', units: 'metric' },
        timeout: 30,
        auth_type: 'none',
      },
    },
    {
      title: '获取GitHub用户信息',
      description: '使用GitHub API获取用户信息',
      config: {
        url: 'https://api.github.com/users/octocat',
        timeout: 30,
        headers: { 'User-Agent': 'MyApp/1.0' },
        auth_type: 'none',
      },
    },
    {
      title: 'Bearer Token认证请求',
      description: '使用JWT Token访问受保护的API',
      config: {
        url: 'https://api.example.com/protected/data',
        auth_type: 'bearer',
        auth_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        timeout: 30,
        retry_count: 2,
      },
    },
    {
      title: '带查询参数的分页请求',
      description: '获取分页数据',
      config: {
        url: 'https://jsonplaceholder.typicode.com/posts',
        params: { _page: 1, _limit: 10, userId: 1 },
        timeout: 30,
        headers: { Accept: 'application/json' },
      },
    },
    {
      title: 'API Key认证请求',
      description: '使用API Key访问第三方服务',
      config: {
        url: 'https://api.example.com/data',
        auth_type: 'api_key',
        auth_token: 'sk-1234567890abcdef',
        headers: { 'X-API-Key': 'your-api-key' },
        timeout: 30,
      },
    },
  ],
}

export const httpPostSchema: ComponentConfigSchema = {
  componentType: 'http_post',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      // description: 'HTTP POST请求的基本参数',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      // description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      // description: 'HTTP请求头的配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
    // {
    //   id: 'response',
    //   label: '响应处理',
    //   description: '响应数据的处理和变量存储',
    //   icon: 'Download',
    //   order: 4,
    //   collapsible: true,
    //   collapsed: true,
    // },
    // {
    //   id: 'advanced',
    //   label: '高级选项',
    //   description: '请求的高级配置',
    //   icon: 'Setting',
    //   order: 5,
    //   collapsible: true,
    //   collapsed: true,
    //   advanced: true,
    // },
  ],

  fields: {
    url: {
      type: 'string',
      label: '请求URL',
      // description: '要请求的API端点URL，支持变量替换 ${variableName}',
      placeholder: 'https://api.example.com/data 或 ${api_base_url}/users',
      required: true,
      group: 'basic',
      order: 1,
      prefix:{
          options: [
            {value:"POST",label:"POST"}
          ]
      },
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'API地址不能为空',
        },
      ],
    },

    headers: {
      type: 'json',
      label: '请求头（JSON格式）',
      // description: 'HTTP请求头（JSON格式），支持变量替换 ${variableName}',
      placeholder: `{
  "Authorization": "Bearer \${api_token}",
  "Content-Type": "application/json"
}`,
      group: 'basic',
      order: 2,
      rows: 6,
      variableSupport: true,
    },

    // data_type: {
    //   type: 'radio',
    //   label: '数据类型',
    //   description: '',
    //   group: 'basic',
    //   order: 1,
    //   default: 'json',
    //   options: [
    //     { label: 'JSON', value: 'json', description: 'JSON格式数据' },
    //     { label: '表单数据', value: 'form', description: 'application/x-www-form-urlencoded' },
    //     { label: '原始数据', value: 'raw', description: '原始文本数据' },
    //     { label: '文件上传', value: 'multipart', description: 'multipart/form-data' },
    //   ],
    // },

    json_data: {
      type: 'json',
      label: '请求参数（JSON格式）',
      // description: '要发送的JSON数据，支持变量替换 ${variableName}',
      placeholder: `{
  "name": "\${user_name}",
  "age": \${user_age}
}`,
      group: 'basic',
      order: 2,
      rows: 6,
      variableSupport: true,
      // conditions: [
      //   {
      //     field: 'data_type',
      //     operator: 'equals',
      //     value: 'json',
      //   },
      // ],
    },

    form_data: {
      type: 'json',
      label: '表单数据',
      // description: '表单字段数据（JSON格式）',
      placeholder: `{
  "username": "john",
  "password": "secret",
}`,
      group: 'basic',
      order: 3,
      rows: 6,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'form',
        },
      ],
    },

    raw_data: {
      type: 'textarea',
      label: '原始数据',
      // description: '要发送的原始文本数据',
      placeholder: '输入原始数据内容',
      group: 'basic',
      order: 4,
      rows: 6,
      conditions: [
        {
          field: 'data_type',
          operator: 'equals',
          value: 'raw',
        },
      ],
    },

    // file_path: {
    //   type: 'file',
    //   label: '上传文件',
    //   // description: '要上传的文件路径',
    //   group: 'basic',
    //   order: 5,
    //   conditions: [
    //     {
    //       field: 'data_type',
    //       operator: 'equals',
    //       value: 'multipart',
    //     },
    //   ],
    // },



    // verify_ssl: {
    //   type: 'boolean',
    //   label: '验证SSL证书',
    //   description: '是否验证HTTPS证书',
    //   group: 'advanced',
    //   order: 1,
    //   default: true,
    // },

    // retry_count: {
    //   type: 'number',
    //   label: '重试次数',
    //   description: '请求失败时的重试次数',
    //   group: 'advanced',
    //   order: 2,
    //   default: 0,
    //   min: 0,
    //   max: 5,
    // },

    // response_variable: {
    //   type: 'string',
    //   label: '响应变量名',
    //   description: '存储完整响应对象的变量名（可选）',
    //   placeholder: 'api_response',
    //   group: 'response',
    //   order: 1,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },

    response_content_variable: {
      type: 'string',
      label: '响应内容变量名',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'response_content',
      group: 'response',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    response_status_variable: {
      type: 'string',
      label: '响应状态变量名',
      // description: '存储HTTP状态码的变量名（可选）',
      placeholder: 'status_code',
      group: 'response',
      order: 3,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    // response_headers_variable: {
    //   type: 'string',
    //   label: '响应头变量名',
    //   description: '存储响应头的变量名（可选）',
    //   placeholder: 'response_headers',
    //   group: 'response',
    //   order: 4,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },

    // json_path_extracts: {
    //   type: 'textarea',
    //   label: 'JSON字段提取',
    //   description: 'JSON路径和变量名映射，格式：path1:var1,path2:var2',
    //   placeholder: 'data.id:user_id,data.name:user_name',
    //   group: 'response',
    //   order: 5,
    //   rows: 3,
    // },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' }
      ]
    }
  },

  presets: {
    json_api: {
      label: 'JSON API',
      description: '发送JSON数据的API请求',
      config: {
        data_type: 'json',
        timeout: 30,
        verify_ssl: true,
      },
    },

    form_submit: {
      label: '表单提交',
      description: '提交表单数据的配置',
      config: {
        data_type: 'form',
        timeout: 30,
        retry_count: 1,
      },
    },

    file_upload: {
      label: '文件上传',
      description: '上传文件的配置',
      config: {
        data_type: 'multipart',
        timeout: 120,
        retry_count: 2,
      },
    },

    deepseek_api: {
      label: 'DeepSeek AI对话',
      description: '调用DeepSeek AI API进行对话',
      config: {
        data_type: 'json',
        timeout: 120,
        verify_ssl: true,
        retry_count: 2,
        retry_delay: 2,
      },
    },

    variable_demo: {
      label: '变量演示',
      description: '演示变量替换和响应捕获功能',
      config: {
        url: '${api_base_url}/users',
        data_type: 'json',
        json_data: {
          name: '${user_name}',
          email: '${user_email}',
          age: '${user_age}',
        },
        headers: {
          Authorization: 'Bearer ${api_token}',
          'Content-Type': 'application/json',
        },
        response_content_variable: 'api_response',
        response_status_variable: 'status_code',
        json_path_extracts: 'id:user_id,name:created_user_name',
        timeout: 30,
      },
    },
  },

  examples: [
    {
      title: '创建用户',
      description: '向API发送JSON数据创建新用户',
      config: {
        url: 'https://jsonplaceholder.typicode.com/users',
        data_type: 'json',
        json_data: {
          name: 'John Doe',
          username: 'johndoe',
          email: '<EMAIL>',
          phone: '**************',
        },
        timeout: 30,
      },
    },
    {
      title: '用户登录',
      description: '发送登录表单数据',
      config: {
        url: 'https://api.example.com/auth/login',
        data_type: 'form',
        form_data: {
          username: '<EMAIL>',
          password: 'password123',
          remember_me: true,
        },
        timeout: 30,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    },
    {
      title: '发送通知',
      description: '向Webhook发送通知数据',
      config: {
        url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
        data_type: 'json',
        json_data: {
          text: 'Hello from Phoenix RPA!',
          channel: '#general',
          username: 'Phoenix Bot',
        },
        timeout: 15,
      },
    },
    {
      title: '提交反馈',
      description: '提交用户反馈表单',
      config: {
        url: 'https://api.example.com/feedback',
        data_type: 'json',
        json_data: {
          subject: '产品建议',
          message: '希望能增加更多自动化功能',
          rating: 5,
          contact_email: '<EMAIL>',
        },
        headers: {
          Authorization: 'Bearer your-token-here',
          'Content-Type': 'application/json',
        },
        timeout: 30,
      },
    },
    {
      title: '上传文件',
      description: '上传文件到服务器',
      config: {
        url: 'https://api.example.com/upload',
        data_type: 'multipart',
        file_path: 'C:\\Users\\<USER>\\report.pdf',
        headers: {
          Authorization: 'Bearer your-token-here',
        },
        timeout: 120,
        retry_count: 2,
      },
    },
    {
      title: '发送原始数据',
      description: '发送XML或其他格式的原始数据',
      config: {
        url: 'https://api.example.com/xml-endpoint',
        data_type: 'raw',
        raw_data:
          '<?xml version="1.0"?><request><action>update</action><data>test</data></request>',
        headers: {
          'Content-Type': 'application/xml',
          Authorization: 'Basic dXNlcjpwYXNz',
        },
        timeout: 30,
      },
    },
    {
      title: 'DeepSeek AI 对话',
      description: '调用DeepSeek AI API进行智能对话',
      config: {
        url: 'https://api.deepseek.com/chat/completions',
        data_type: 'json',
        json_data: {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant.',
            },
            {
              role: 'user',
              content: '请帮我写一个Python函数来计算斐波那契数列',
            },
          ],
          stream: false,
          temperature: 0.7,
          max_tokens: 1000,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer sk-your-deepseek-api-key-here',
        },
        timeout: 60,
        retry_count: 2,
      },
    },
    {
      title: 'DeepSeek 推理模型',
      description: '使用DeepSeek推理模型进行复杂推理',
      config: {
        url: 'https://api.deepseek.com/chat/completions',
        data_type: 'json',
        json_data: {
          model: 'deepseek-reasoner',
          messages: [
            {
              role: 'user',
              content: '请分析一下人工智能在未来10年的发展趋势',
            },
          ],
          stream: false,
          temperature: 0.3,
          max_tokens: 2000,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer sk-your-deepseek-api-key-here',
        },
        timeout: 90,
        retry_count: 1,
      },
    },
  ],
}
export const httpRequestSchema: ComponentConfigSchema = {
  componentType: 'http_request',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      // description: 'HTTP POST请求的基本参数',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      // description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      // description: 'HTTP请求头的配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
    // {
    //   id: 'response',
    //   label: '响应处理',
    //   description: '响应数据的处理和变量存储',
    //   icon: 'Download',
    //   order: 4,
    //   collapsible: true,
    //   collapsed: true,
    // },
    // {
    //   id: 'advanced',
    //   label: '高级选项',
    //   description: '请求的高级配置',
    //   icon: 'Setting',
    //   order: 5,
    //   collapsible: true,
    //   collapsed: true,
    //   advanced: true,
    // },
  ],

  fields: {
    url: {
      type: 'string',
      label: '请求URL',
      // description: '要请求的API端点URL，支持变量替换 ${variableName}',
      placeholder: 'https://api.example.com/data 或 ${api_base_url}/users',
      required: true,
      group: 'basic',
      order: 1,
      prefix:{
          options: [
            {value:"POST",label:"POST"},
            {value:"GET",label:"GET"}
          ]
      },
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'API地址不能为空',
        },
      ],
    },

    headers: {
      type: 'json',
      label: '请求头（JSON格式）',
      // description: 'HTTP请求头（JSON格式），支持变量替换 ${variableName}',
      placeholder: `{
  "Authorization": "Bearer \${api_token}",
  "Content-Type": "application/json"
}`,
      group: 'basic',
      order: 2,
      rows: 6,
      variableSupport: true,
    },
    json_data: {
      type: 'json',
      label: '请求参数（JSON格式）',
      // description: '要发送的JSON数据，支持变量替换 ${variableName}',
      placeholder: `{
  "name": "\${user_name}",
  "age": \${user_age}
}`,
      group: 'basic',
      order: 2,
      rows: 6,
      variableSupport: true,
      // conditions: [
      //   {
      //     field: 'data_type',
      //     operator: 'equals',
      //     value: 'json',
      //   },
      // ],
    },
    // response_content_variable: {
    //   type: 'string',
    //   label: '响应内容变量名',
    //   // description: '存储响应内容的变量名（可选）',
    //   placeholder: 'response_content',
    //   group: 'response',
    //   order: 2,
    //   required: true,
    //   outputVariable: true,
    //   validation: [
    //     {
    //       type: 'pattern',
    //       value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
    //       message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
    //     },
    //   ],
    // },
    extract_variable: {
      type: 'variables_map',
      label: '响应内容变量名',
      description: '',
      placeholder: '',
      group: 'response',
      order: 4,
      outputVariable: true,
    },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' }
      ]
    }
  },

  presets: {
    json_api: {
      label: 'JSON API',
      description: '发送JSON数据的API请求',
      config: {
        data_type: 'json',
        timeout: 30,
        verify_ssl: true,
      },
    },

    form_submit: {
      label: '表单提交',
      description: '提交表单数据的配置',
      config: {
        data_type: 'form',
        timeout: 30,
        retry_count: 1,
      },
    },

    file_upload: {
      label: '文件上传',
      description: '上传文件的配置',
      config: {
        data_type: 'multipart',
        timeout: 120,
        retry_count: 2,
      },
    },

    deepseek_api: {
      label: 'DeepSeek AI对话',
      description: '调用DeepSeek AI API进行对话',
      config: {
        data_type: 'json',
        timeout: 120,
        verify_ssl: true,
        retry_count: 2,
        retry_delay: 2,
      },
    },

    variable_demo: {
      label: '变量演示',
      description: '演示变量替换和响应捕获功能',
      config: {
        url: '${api_base_url}/users',
        data_type: 'json',
        json_data: {
          name: '${user_name}',
          email: '${user_email}',
          age: '${user_age}',
        },
        headers: {
          Authorization: 'Bearer ${api_token}',
          'Content-Type': 'application/json',
        },
        response_content_variable: 'api_response',
        response_status_variable: 'status_code',
        json_path_extracts: 'id:user_id,name:created_user_name',
        timeout: 30,
      },
    },
  },

  examples: [
    {
      title: '创建用户',
      description: '向API发送JSON数据创建新用户',
      config: {
        url: 'https://jsonplaceholder.typicode.com/users',
        data_type: 'json',
        json_data: {
          name: 'John Doe',
          username: 'johndoe',
          email: '<EMAIL>',
          phone: '**************',
        },
        timeout: 30,
      },
    },
    {
      title: '用户登录',
      description: '发送登录表单数据',
      config: {
        url: 'https://api.example.com/auth/login',
        data_type: 'form',
        form_data: {
          username: '<EMAIL>',
          password: 'password123',
          remember_me: true,
        },
        timeout: 30,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    },
    {
      title: '发送通知',
      description: '向Webhook发送通知数据',
      config: {
        url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
        data_type: 'json',
        json_data: {
          text: 'Hello from Phoenix RPA!',
          channel: '#general',
          username: 'Phoenix Bot',
        },
        timeout: 15,
      },
    },
    {
      title: '提交反馈',
      description: '提交用户反馈表单',
      config: {
        url: 'https://api.example.com/feedback',
        data_type: 'json',
        json_data: {
          subject: '产品建议',
          message: '希望能增加更多自动化功能',
          rating: 5,
          contact_email: '<EMAIL>',
        },
        headers: {
          Authorization: 'Bearer your-token-here',
          'Content-Type': 'application/json',
        },
        timeout: 30,
      },
    },
    {
      title: '上传文件',
      description: '上传文件到服务器',
      config: {
        url: 'https://api.example.com/upload',
        data_type: 'multipart',
        file_path: 'C:\\Users\\<USER>\\report.pdf',
        headers: {
          Authorization: 'Bearer your-token-here',
        },
        timeout: 120,
        retry_count: 2,
      },
    },
    {
      title: '发送原始数据',
      description: '发送XML或其他格式的原始数据',
      config: {
        url: 'https://api.example.com/xml-endpoint',
        data_type: 'raw',
        raw_data:
          '<?xml version="1.0"?><request><action>update</action><data>test</data></request>',
        headers: {
          'Content-Type': 'application/xml',
          Authorization: 'Basic dXNlcjpwYXNz',
        },
        timeout: 30,
      },
    },
    {
      title: 'DeepSeek AI 对话',
      description: '调用DeepSeek AI API进行智能对话',
      config: {
        url: 'https://api.deepseek.com/chat/completions',
        data_type: 'json',
        json_data: {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant.',
            },
            {
              role: 'user',
              content: '请帮我写一个Python函数来计算斐波那契数列',
            },
          ],
          stream: false,
          temperature: 0.7,
          max_tokens: 1000,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer sk-your-deepseek-api-key-here',
        },
        timeout: 60,
        retry_count: 2,
      },
    },
    {
      title: 'DeepSeek 推理模型',
      description: '使用DeepSeek推理模型进行复杂推理',
      config: {
        url: 'https://api.deepseek.com/chat/completions',
        data_type: 'json',
        json_data: {
          model: 'deepseek-reasoner',
          messages: [
            {
              role: 'user',
              content: '请分析一下人工智能在未来10年的发展趋势',
            },
          ],
          stream: false,
          temperature: 0.3,
          max_tokens: 2000,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer sk-your-deepseek-api-key-here',
        },
        timeout: 90,
        retry_count: 1,
      },
    },
  ],
  test:true
}
