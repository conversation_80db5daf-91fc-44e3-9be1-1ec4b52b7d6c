/**
 *  判断是否为空
 * @param val  检测的数据
 * @returns {boolean}   不为空 返回 true
 */
export function isNull(val) {
    if (typeof val == 'boolean') {
        return false;
    }
    if (typeof val == 'number') {
        return false;
    }
    if (val instanceof Array) {
        if (val.length == 0) {
            return true;
        }
    } else if (val instanceof Object) {
        if (JSON.stringify(val) === '{}') {
            return true;
        }
    } else {
        return val == 'null' || val == null || val === undefined || val == 'undefined' || val == undefined || val == '';
    }
    return false;
}