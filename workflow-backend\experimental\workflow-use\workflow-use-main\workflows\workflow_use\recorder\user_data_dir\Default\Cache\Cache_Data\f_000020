(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-54f56a72"],{"1d5d":function(e,t,s){"use strict";s("a341")},3358:function(e,t,s){e.exports=s.p+"static/img/menu_4.05fcb8ca.png"},"441a":function(e,t,s){"use strict";var i=s("a27e");s("fa7d");const a={getGroupCount:(e={})=>i.e.post("/ump/cooperate/groupCount",e),queryCooperate:(e={})=>i.e.post("/ump/cooperate/query",e),getUnreadCount:(e={})=>i.e.get("/message/messageGroupChat/selUunread",e),getTodoCount:(e={})=>i.e.get("/ump/workSchedule/todayNum",e),getUserApplicationList:()=>i.e.get("/ump/menu/userApplicationList",{applicationType:"web"}),sendMessageToSelf:e=>fetch("/uniwim/message/chat/sendMessageToSelf",{method:"POST",headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:e.token,FROM_CHANNEL:"web"},body:JSON.stringify({sendMobile:e.sendMobile,content:e.content})}),getByPhoneApi:e=>i.e.get("/ump/user/getByPhone",e),getCasualUserApi:e=>i.e.get("/ump/user/getCasualUser",e),getDlDataApi:e=>i.e.post("/middle/request",e,{meta:{isMessage:!1}}),changeDlTokenApi:e=>i.e.get("https://saas.dlmeasure.com/platform/v3/dl/getTokenByUniwimToken",e,{meta:{isMessage:!1}})};t.a=a},5853:function(e,t){e.exports="data:image/png;base64,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"},"664f":function(e,t,s){},"6a89":function(e,t,s){e.exports=s.p+"static/img/menu_2.a879cb56.png"},7054:function(e,t,s){"use strict";s("664f")},7716:function(e,t,s){"use strict";s.d(t,"a",(function(){return a})),s.d(t,"c",(function(){return n})),s.d(t,"b",(function(){return o}));var i=s("a27e");const a=e=>i.e.get("/ump/notice/getNotice",e),n=(e,t={meta:{isMessage:!0}})=>i.e.post("/ump/workSchedule/saveAndUpdate",e,t),o=e=>i.e.get("/ump/workSchedule/list",e)},7938:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAAEnRFWHRleGlmOkV4aWZPZmZzZXQAMjZTG6JlAAAABGdBTUEAALGPC/xhBQAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAQqADAAQAAAABAAAAQgAAAADorYEXAAAArlBMVEUAAAAAWM4AVdEAVNIAVNIAVNIAVNIAVNIAVNIAVNL////+///+/v/8/f/8/f74+/74+v7t8/zq8fvo7/vn7/vm7vrg6vrf6fnZ5vjY5fjX5Pi/1PS3z/O3zvK1zfK0zfKzzfKyzPGxy/GGruqFremCq+mBq+mAquh/qel/qeh9qOhbkeJakOJZkOJPieA3edw2eNwZZNYXZNYWY9YEV9MDVtMDVtIBVdIBVNIAVNJW6jjCAAAACnRSTlMABhFFg4q+1ujx7V4PiQAAAAFiS0dECmjQ9FYAAADtSURBVHjazdXJDoIwEIDhIiBQqSCiogKuuIv70vd/MYFDEyGFTkKM//k7tGkzg5IkWdUMWpKhqbKEshqKTivTlUZmm1SoZqIlhQqmSEjWRbEuI5UKpyJNHGvIEMcGooD+Ee89/NXwWII9nGtUgnE+sz5szzasuZ3iy5OL+yfKOg9SPI65mPhbVkAS/MK9t9iZM9x6ADC+8zAJd6ywzbDIBSuwNVmxpnY5zlUD5rxgnRck/poVkPrObBZ0fOA+t1vATgc7nI8U2biQteB80VvkmrlDd5fXX8wN0GAEjVzQMAetCdACgqw22NIEreMPmExcONsOwzgAAAAASUVORK5CYII="},"7b6a":function(e,t,s){"use strict";(function(e){var i=s("441a");t.a={name:"User",data(){return{currentTime:this.formatTime(new Date),loading:!1,userInfo:null,unreadCount:0,todoCount:0,typeList:[]}},computed:{currentUser(){return this.$store.getters.currentUser}},beforeDestroy(){this.timer&&(clearInterval(this.timer),this.timer=null)},mounted(){this.initDict(),this.timer=setInterval(this.updateTime,1e3),i.a.getUnreadCount().then((e=>{this.unreadCount=e.total})),i.a.getTodoCount().then((e=>{this.todoCount=e}))},methods:{initDict(){this.$common.getDict("NAVTYPE").then((e=>{this.typeList=e.NAVTYPE||[]}))},formatTime:e=>e.toLocaleTimeString(),updateTime(){this.currentTime=this.formatTime(new Date)},typeClick(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?e.Value?this.$store.dispatch("OPEN_TAG",{Id:"category_"+(new Date).getTime(),Name:e.Name,Val:e.Value,link:e.Value,target:e.ExtendParams?e.ExtendParams:"_blankTop"}):this.$message({showClose:!0,message:"暂无链接地址",type:"warning"}):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},nyrzhou(){let t=new Date;switch(e(t).day()){case 1:return"星期一";case 2:return"星期二";case 3:return"星期三";case 4:return"星期四";case 5:return"星期五";case 6:return"星期六";case 0:return"星期日"}}}}}).call(this,s("c1df"))},8593:function(e,t,s){"use strict";(function(e){var i=s("a27e"),a=s("eec9"),n=s("4360"),o=s("fa7d"),r=s("140e");const l={getBpmList:(e,t={baseURL:""})=>i.e.post("/bpm/proc/task/query.json",e,t),tenantList:e=>i.e.get("/dmp/tenant/list",e,{meta:{isToken:!1}}),dictSelect(e){let t={codes:e};return i.e.post("/ump/dictionary/getValueByCode",t)},getEncryptKey:()=>i.e.get("/ump/key"),login:(e,t={})=>i.e.post("/ump/login",e,t),changePwd:(e,t={meta:{isMessage:!0}})=>i.e.post("/ump/user/changePwd",e,t),changeUserPwd:(e,t={meta:{isMessage:!0}})=>i.e.post("/dmp/user/changePwd",e,t),refreshToken:(e,t={})=>i.e.post("/ump/refreshToken",e,t),verificationCode:(e,t={})=>i.e.post("/dmp/verificationCode",e,t),verificationCodeLogin:(e,t={})=>i.e.post("/dmp/verificationCodeLogin",e,t),forgetPwd:(e,t={})=>i.e.post("/dmp/forgetPwd",e,t),verificationChangePwd:(e,t={meta:{isMessage:!0}})=>i.e.post("/dmp/verificationChangePwd",e,t),skipInit:(e,t={})=>i.e.get("/ump/skipInit",e,t),importProjectFiles:(e,t={})=>i.e.post("/dmp/tenant/importProjectFiles",e,t),initConfigs:t=>new Promise(((t,s)=>{let a=o.a.getUniwaterUtoken();i.e.get(a?o.a.getQueryString("menuId")?"/dmp/tenant/sysConfig/get":"/ump/cfg":"/ump/cfg-encrypt",{}).then((s=>{let i;if(i=a?s:JSON.parse(Object(r.a)(s)),i.customCss){let e=i.customCss,t=document.getElementsByTagName("head")[0],s=document.createElement("style");s.id="customStyle",s.type="text/css",s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e)),t.appendChild(s)}i.displayItems=i&&i.displayItems?JSON.parse(i.displayItems):[],i.yinuoEntrancedisplay=i&&i.yinuoEntrancedisplay?JSON.parse(i.yinuoEntrancedisplay):[],i.copyrightPosition=i&&i.copyrightPosition?JSON.parse(i.copyrightPosition):[],i.loginAccountType=i.loginAccountType?i.loginAccountType.split(","):[],i.forcePasswordChange=i&&i.forcePasswordChange?JSON.parse(i.forcePasswordChange):[],i.resourceType=i.resourceType?JSON.parse(i.resourceType):[],i.monitoringCenterIsShow=i&&i.monitoringCenterIsShow&&"0"===i.monitoringCenterIsShow?i.monitoringCenterIsShow:"1",i.tagNum=i&&i.tagNum&&i.tagNum?i.tagNum:"8",i.themeConfiguration=i&&i.themeConfiguration&&i.themeConfiguration?i.themeConfiguration:"default","1"==i.lockingIsOpen&&(i.loginTime=Number(i.loginTime),i.loginCountMax=Number(i.loginCountMax),i.loginTimeSpan=Number(i.loginTimeSpan)),i.globalSearch||(i.globalSearch="1"),i.appTemp=i.appTemp?JSON.parse(i.appTemp):[],i.appBannerList=i.appBannerList?JSON.stringify(i.appBannerList):"[]",n.a.dispatch("SET_CONFIGS",i);const l=i.favicon?i.favicon:null;if(l&&e("#ico").attr("href",l),"App"===o.a.getQueryString("source"))document.title="一诺AI";else if(o.a.getQueryString("menuId")){let e=i.title||i.name||i.tenantName;document.title=e}else!i.title||o.a.getQueryString("preview")||o.a.getQueryString("menuId")||(document.title=i.title);t(i)})).catch((e=>{s(e)}))})),initMenus:()=>new Promise((e=>{let t=o.a.getQueryString("menuId");-1!=window.location.href.indexOf("admin.html")&&n.a.state.user.isAdmin?i.e.get("/ump/adminMenu/adminMenuTree",{}).then((t=>{const s=t&&t.length?Object(a.b)(t):[];n.a.dispatch("init_menu",s),e(s)})):t&&"null"!=t?i.e.get("/ump/menu/getMenuTreeByPid?permission=0&menuId="+t).then((t=>{const s=t&&t.length?Object(a.b)(t):[];n.a.dispatch("init_menu",s),e(s)})):-1==window.location.href.indexOf("admin.html")||n.a.state.user.isAdmin?i.e.get("/ump/menu/userMenuTree",{}).then((t=>{const s=t&&t.length?Object(a.b)(t):[];n.a.dispatch("init_menu",s),e(s)})):window.location.href=window.location.href.replace(/\?admin=\d+.*$/,"")})),initUserInfo:()=>new Promise(((e,t)=>{i.e.get("/ump/currUserInfo",{}).then((s=>{window.UNIWIM_CurrentUser=s,s?(n.a.commit("INIT_USER",s),e(s)):(n.a.dispatch("LOGOUT"),t())})).catch((e=>{n.a.dispatch("LOGOUT"),t()}))})),getMergeUserMenuTree:e=>i.e.get("/ump/menu/mergeUserMenuTree",e),getUserMenuTree:e=>i.e.get("/ump/menu/userMenuTree",e),getUrlPrefix:()=>"/uniwim/ump/file",getPopTypeList:e=>i.e.post("/ump/dictionary/queryMsgPopType",e),getMsgPage:e=>i.e.post("/ump/dictionary/queryMsgPop",e),getAppLists:e=>i.e.post("/ump/dictionary/queryMsgGroup",e),getMessagePage:e=>i.e.post("/message/system/query",e),getMessagerRead:e=>i.e.post("/message/system/read",e),getMessagerClean:e=>i.e.post("/message/system/clean",e),getMessagerCleanAll:e=>i.e.post("/message/system/cleanAll",e),getMsgTotal:e=>i.e.get("/message/system/msgTotal",e),getMessageSearch:(e,t)=>i.e.post("/ump/search/"+e,t),getKnowledgeDetail:e=>i.e.get("/imb/Knowledge/detail?id="+e),getDetailByName:e=>i.e.get("/dmp/tenant/detailByName?name="+e),systemPreview:(e,t)=>new Promise((s=>{1==t?i.e.post("/dmp/productMenu/customPreview",{ids:e}).then((e=>{const t=Object(a.b)(e||[]);n.a.dispatch("init_menu",t),s(t)})):i.e.get("/dly/api/analysis/preview",{id:e}).then((e=>{const t=Object(a.b)(e.treeList||[]);n.a.dispatch("init_menu",t),n.a.dispatch("init_previewProjectInfo",{projectName:e.projectName}),s({menus:t,projectName:e.projectName})}))})),saasLogin:(e,t={})=>i.e.post("/dmp/login",e,t),getLoginCode:e=>"/uniwim/ump/loginCode.png?time="+e,getTenantList:e=>i.e.get("/dmp/getTenantUserOauth",e),changeTenant:e=>i.e.post("/dmp/changeTenant",e),getApplicationList:e=>i.e.get("/ump/oauthClient/applicationList",e),register:(e,t={})=>i.e.post("/dmp/register",e,t),uniLogin:(e,t,s)=>i.e.post("/ump/uniLogin?url="+e,t,s),userButtonPermission:e=>i.e.get("/ump/menu/userButtonPermissionList",e),getweather:(e,t)=>i.e.get("/weather/now",e,t),saveRegister:(e,t={})=>i.e.post("/ump/register",e,t),getOrgRoleOrgTree:e=>i.e.post("/ump/org/roleOrgTree",e),getUserRoleOrgTree:e=>i.e.post("/ump/user/roleOrgTree",e),analysisFunctionDemandUpdate:(e,t={meta:{isMessage:!0}})=>i.e.post("/dly/api/analysisFunction/demandUpdate",e,t),getAnalysisDetail:e=>i.e.get("/dly/api/analysisFunction/getDetail?analysisId="+e.analysisId+"&functionId="+e.functionId),exportTemplate:e=>i.e.downBlobFileGet("/ump/meeting/exportTemplate",e,"ces1"),saveMineTheme:(e,t={meta:{isMessage:!0}})=>i.e.post("/dmp/tenant/sysConfig/saveMineTheme",e,t),getSysConfig:e=>i.e.post("/dmp/tenant/sysConfig/getAppPic",e),getParameterConfig:e=>i.e.get("/ump/param/getObj?code="+e),logOutSaveLog:e=>i.e.get("/ump/loginLog/logOut",e),searchConfigSave:e=>i.e.post("/dmp/tenant/sysConfig/saveAdd",e),getNewsList:e=>i.e.post("/ump/press/queryMine",e)};t.a=l}).call(this,s("1157"))},"94af":function(e,t,s){e.exports=s.p+"static/img/menu_1.a6753097.png"},"96df":function(e,t,s){"use strict";s("adf0")},a341:function(e,t,s){},adf0:function(e,t,s){},af07:function(e,t,s){e.exports=s.p+"static/img/05.9f590b6e.png"},b96c:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAYAAAA5ZDbSAAAAEnRFWHRleGlmOkV4aWZPZmZzZXQAMjZTG6JlAAAABGdBTUEAALGPC/xhBQAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAtKADAAQAAAABAAAAtAAAAABW1ZZ5AAAABmJLR0QA/wD/AP+gvaeTAAAOtUlEQVR42uycS2yUVRSAvzvTUqRKHzOdaZkq4VEiIERCjAgYNZQCMVGMGhdgIimbGhfdacLaRHcujCzUoAESExTQmEARBLE8DAGxDQXK0ALzfnf6oK0z8197k39BjLYp0+ncGebb/Nub8+XMPefec0dQwAQCgXLDUu4AaiSorw14GliMZAGCSqAKePCrGAGSYH4lIwj8QB9wA4gJCAMJi5EKNzQ0pChQBAXG7XjcWpHKLEGIbUieAxYBNYDDlFhGdqSBJKZgoB/BJaQ8Nl5uvb2ktjZDAaG94Lv++Lwyq7FMwjoJ6wVsBpx5WLsEQhJ+EXBewMV0xtK7cEHtfTRGW8HeYOxxhNwEvAusBpZqtF4JuIEuYD9SnGqstw2jIboE7EGpLwFbgZ1ANYXBAHAAOI4Uv+kkWxvB3mB0C4JdQAtQQ2GSAE4g2ddYb+9gKopdsDccsyHlVqAVeIXi4jTwNUIcb3TYYvwXxSzYG4q+DLQDzUAlxckIcBL4rNFpP8O/KUbBvlD0WQm7gTbAwqOBAewV8JXLab8KJsUk2BeJPCYN8SawB2gCrDxaZIBbwMfCIn9w1dWNFoXgUChkTWNdIeELYINOhV2ekMA5Ae+XkelxOp0ZcoiFHDMhd7uEA8DGklwUAtioYqJiU7AZ7AlHXULyAfBhSeyk2fypFHz+pMPuKxjB98LhRou07ANeBCooMRnjwO+GMHY95XB4tf+Jnmh/Nk/I7QCaS3KnxoxRs4qZip3WGewLxdZJ5EFgMSUehj6B2OFy2i5qlcHXrknhDUV3S+SRktysWKxiqGKpYqpFBquFVNnibyHkXsBGiZkghhRtyVjt9ytXCpnXDK6yx1pLcmccm4qpim1eM9jcc48A9ZTIBUGBeCObPdmSTbWsCqqS3JxSr2KcTXUtsuhzO4AVlJgNegxhbFF9cs4FeyNRFwbfAM1oimFIApEBAuEkN/r8+EMJIvEhRsdTWIRgbkU59Y5qXI4ali9poL6uCodtPppzEgvvNdbZfbkVHIp+ArTreogRjCTpvNxL900vsYFh0ukMk1Exp4y62vmsfWYhL6xponr+PDRl3LxX/igngs1boe0SDul4thxNDHH+yi1OdF4zpU6feXPn8NqmNaxdtYgnKueiIVLA22VkjqpbqJkUrC7qV5m3QqvRDJW1+4+e444vSiZjkA1zystYvnQBO19fr6vkLgE7XU5794wJNi/rTwAb0YxLXX0c7rhMYnCEmcTlrOGdV59n2SItm4ROYZEt5tBA9m2SOYmxAc3o7Q9y6NglJTcHo0UJDv50AVWcacgG0wlZC1YzVMAe3fbd624/3x7uZHB4lFwRiib58rvTqCpcM4RyotxkLdgckGtCI9IZgx9PXkFVybnGE0xw7Gw3GtKk3GQl2BxtbdNpQC6VzvDzr1dRBdVsIKVE7fMX/nRjSIlGWIE25eghBJtD6dCu22hrnyfMxatuZptT53sIRZJohgVoV66mLRgpt+p2WmUYElUxDwzeZ7bxBuMcP9uFhjQrV9MXDK26vTi464vi8cfIF3/d8DA4PIZmVAKt0xKsHoLp9lbo71SaUxd68roPjo2nOPNHD4ah1V6McmU6m1Kw+YSTXWiGPzRAvydCvulx+0kkR9COCWfK3ZSCzfe5LWhG1817ZluUXzyBOO67ITSkRbmbWjBim47vc6+7A+iAOuvuvaOl4BrlblLBKsVB7kAzhobH6PdG0IXuXg9Sr57YRO5QDv9XMEJu0vFvE1SLohNDw6Mkh0bRkGrTIf+wd64/TZ1xHP+e0wsiaKUUqrRQS2ktIpcBQYeYbZnbYpbFvdmyF7pkf8H2p7hXe7PFxLlkMVkyk71Qx6LGaeZ0m+IFgYk3Bl4oBQbbFNqzfsMp4zYr5Zz2d5RPct43z6fPOc/t930WCWaaDYD9EMitgWFIgp33trDfNIf9dLlIsGpLRiTu9ZKRUTGZJrOMjMn7TToNdLlIsALsAFADYXDteWxC3utwOC5WcA1dLhLMkDFpW4JpwdPT8sLl/v7nCYSi0OU8wYwHZIIcBMJpCbcHpcGVNYnjaEKXdDormNmPALwQCKcjENiUQn9WGi+dzgpmsKfE13P6EJzDboc01hY6oYhsMRBFdwqVkbxgaqtQnE47igqdkEbphmKIJuWUblU9bzkIobASIeD3QBrVVeUQTpBuVQ1wg49gwptlDQ/WOB0I+uT96RbgplsVQBn4CCZQ4RFVUrJtix9Op7xxwTx0r6peuO2CcLbWVEACNpuK2pCM35IBF91ScBSA+L9jW2MIxWsLkG8CFaWoFfJny4Cdbik4BAvAUhI++aYu4keJoM9FBqopeBMsAAvBOlq3IJ+sK16DV7dHoQieAC+ggoKFT+j+ozFaiTL3OuQDSt3VGkFRociy6P+jmILFl7bPXfTY80oDHA4bck2wsgw7WyKwGOtVaWefM4+mfeC0Kdd0tIThdlmqqUiRaoUp0lw4H37n9SbYVBW5on5LJdqbw1b69qZxWa4Hk3BgY86mKiXri7C73bJhQkWiCsueFVVVsHd3M7weF8zmzV3bEAlaYqKxJBQs8Jh+Ziq8G7CzJQwzCfhK0d5cI3lbMBOTFCyuJvJZ4Df4jZ11pmVolLiK8N6eNhQ4HbAwY5btwem56Vu76uFaVwijaWuoxmb5O0bPbw9Ow4X/xtoAjISLGZxv2+1igg1ezB6sD7gM32kKVpVhTYGlX80zaDM9eBAWx2twzmRNlawDBlmjYJCC+2FxDC4Ek5pwlw39qn5n/TQszN2hGIwkPm7pr1aaabql4JiVB1r3h8dw9pc+g+MRb2F0XPTN7RnRncZ4W0o9gCMAorAIrHa4NxTD1b4BdJ69zuwMU5YoX3u5Fm2N1XAVr7XiYke3ArxvV4ARDZBVgPuUGKVL1+/gSu8Aem/fR3x00rRQFr6mj37/K672DKCxtoqrZlYbWcf5KDwcnVAdBwGIq+wniWQSDAT97dodMG3uYWwc+YBZ0tsifuxoCsG30Q0urgjnK1ty6iNlJtVu+GNoOABhxOITYHRST/8QBh/Gkf/UBIouANfBm+sCqWezqOO881Dwib/c8+mM4AePIoByQ0J90uRfj3FnMIbT57vRfXMIT6bkDvB5hPalrQHwFR4OeNmrpewZa4AW9XvLepV0+WjBVHIgn1fkUCSTdE6lxHLaQ9FWgQVylZvc4MZHR0sEpSXFyDP3HztUf8jtTijQufdg+JCS44wOTQNfvXwFM+xTRA7WSrHbbKiuKkNHa4RnqHmbS857tQZ8Wen1fIi5B94V4ByAfbl6TXOwxEETB08cRCWS8oq8s8uyToBJ9EzlK/esR12ND631QVSlZOcIjS6hM7cHNynAETPDv5Mpifyu/ny5Hxeu9EvMfDSNaKgCPLjH77XD3F2qPs5/fV7PpXk9OJlQe222ZBcFm5W1ceZCTyo9vQt/yktsNZ0bNwfBtNwHsXHsbq8D72syia7plEvoKAsS3vcC+NaMAdQ3xy6CF1YlBOZt5BJub25vCuGDt3eYJfldv9dzdOmkO035AcCo0QMpfmt/vLgql/CzdP7SzVR79JgRhziqO8SSgv0bSycAHIaBMGPy+Jkrz80gyijJneeuo//uIxjMYTrMFAh+DEAcBnGtbwDxMcvvzBgOd6uu/f4HDCROd5njhDXlNIATMIjL3VKTWfML24RXBBjICbrLKJhdHBoOGhf5J/LmMBHEDGwbOqO7TIJ1yZ7jAE4adc/BKqa3zUk6W+6tK19Y/cTlC8Kk7grLE6woxwB0rh5gm4fEw32ddLVswf7y0hiAAwCSyB4RuRpSMaBtknREV8sWTPxezykAnwFIIDtYV2v1+h5TYJuwbVZAgm7oaEW3jyrA5wD6kCVbwz40RCuxynzYJmybFdBHN0bcAM416n0ADgFQst1o+Pq7n9B149/2zickqigK47/70k1EhiOKmokYUUh/NpEEUVFhLqOlFkktcle7ImohRMtcBK3ERbpLaueiRQtbGK0ymFUqpItpmmdN5UJi3u0evNFjMJw3M43vPd8PPmY5w/m4Xu89556zxI/V7ZdoKN53jxzqkLvoSrJK2uiqWb0ToRnxLkcCyaa8fT/P4lJOkvuhLsepdsWHdKft6mjixNFuOtubpEKzJiPeVekjz3OHNUyEdXDHNmJOwaDJ936oeMS7nzoKaQUjoe5zHn+0eCBeUCKKYMh+/Ai4bRSpjmAxYM3osdl37xIAh4BoxRNghoRaM2NjHwhFGXzKZvc62pG7z8j2F4oYaU95ffuam5cJiEMZ2C+6FYe3xRFgQWL9x9warGD/f9Zur0a/QArmE/4HGYW61N6SmqVMHCrAfvF9I5eEauNKbH3m1t5gIZ9LjaHVcGJyVXElphJbDFtqcE+P0nm38TlwxyhDQqVkJJYSU4ktFaKoInZPnpRW8iSUw4JCDfj/LNdgBQfek28apUkISlpiV2xuqFZw0Tl5HDiV3HiVdEM14ylvyH8UCuUKxn9OdrgGjCZ315um/UYlVn5zQ7+Ci+6uLwMPkizUhlmhEZMVmsJHJFawn3oKLxUMAm+S1YygJRYSkzoTG3xEcgUXFQ3Iar5nn6hGvpVrQAq29OmhcvSUTdYTG4N9hQPHNNwAho0iOVKgDDyjp1JDtf4w2xJHg3178xmbsDgfxcEgJbJqa8tHN6x+jLPBwnLWTaH1ReC60VnixWujMSlK/2fdctwN9rOcyfWhGJJBJ0ZRrZb/uv7Kj3F5K8RmbCeD1012d6H0aVD9oAeAPUSDb6AmQU/LE07zyi80/aBCZfAGZp8Drtgz9P4Q/V5t9NFozuiZtE0Ik6mRMNhi2hqu7HR2eAcU9Go4qeAC0LIFv10bfdbwSvpQaZiVzkSdbY2hbl8QeoOLsG0XC90o1Y/mONBl1Ghn1jcY1VWhU3re6IvRitEiindoPb1Wv2Ne2gMSISJnsB9phew59c36r8Epo4M2XdlmZyPvtsewBvn0HV/y9vO70U87nGTBjjhwxWAFK473K9va2hrZV+y/AZ2HXN6SHBW/AAAAAElFTkSuQmCC"},b9a3:function(e,t,s){},befa:function(e,t,s){},c053:function(e,t,s){e.exports=s.p+"static/img/menu_5.3caa3777.png"},c621:function(e,t,s){"use strict";s.r(t);var i=[function(){var e=this._self._c;return e("div",{staticClass:"head"},[e("img",{attrs:{src:s("7938")}}),e("div",{staticClass:"lab"},[this._v("应用中心")]),e("div",{staticClass:"line"})])}],a=(s("14d9"),s("13d5"),s("e9f5"),s("9485"),s("441a")),n=s("8593"),o={name:"MyApps",data:()=>({appList:null,dlAppList:[],loading:!1,imgArr:[s("94af"),s("6a89"),s("d227"),s("3358"),s("c053")],dlToken:null}),computed:{getHeight(){return this.appList.length&&this.appList[0].length<=3?"170px":"300px"},getSrc(){return this.getRandomNumber(this.imgArr)},currentUser(){return this.$store.getters.currentUser}},methods:{getRandomNumber:e=>e[Math.floor(Math.random()*e.length)],changeDlToken(){let e={url:"https://saas.dlmeasure.com/platform/v3/dl/getTokenByUniwimToken",type:"GET",param:{},token:this.$utils.getUniwaterUtoken()};a.a.getDlDataApi(e).then((e=>{if(e&&e.data){this.dlToken=e.data.access_token;let t=e.data.userInfo,s={url:"https://saas.dlmeasure.com/platform/v2/user/getShowWebs",type:"GET",param:{},token:this.dlToken};a.a.getDlDataApi(s).then((e=>{if(e&&e.data){e.data||(e.data=[]);let s=[];for(let t of e.data)for(let e of t.apps)e.appHome&&(e.appHome.indexOf("?")>-1?e.url=e.appHome+`&token=${this.dlToken}&theme=agile`:e.url=e.appHome+`?token=${this.dlToken}&theme=agile`),e.target="_blank";t.tenantNo&&"default"!==t.tenantNo&&s.push({appName:"企业云学堂",url:"https://www.dlmeasure.com/mh/#/allorglist?token="+this.dlToken,thumbnail:"https://hdfs.dlmeasure.com:9001/hdfs/source/image/1630425600000/07054f90.png",workorderUnprocessed:0,alarmUnprocessed:0,groupComment:null}),3===t.userType&&s.push({appName:"包管理平台",url:Object({NODE_ENV:"production",BASE_URL:""}).CLOUD_ROOT+"/package-web/#/packageManager?token="+this.dlToken,thumbnail:"https://hdfs.dlmeasure.com:9001/hdfs/source/image/1630425600000/07054f90.png",workorderUnprocessed:0,alarmUnprocessed:0},{appName:"布署平台",url:Object({NODE_ENV:"production",BASE_URL:""}).CLOUD_ROOT+"/deployment-web/#/k8s/index?token="+this.dlToken,thumbnail:"https://hdfs.dlmeasure.com:9001/hdfs/source/image/1630425600000/07054f90.png",workorderUnprocessed:0,alarmUnprocessed:0},{appName:"文件上传管理",url:Object({NODE_ENV:"production",BASE_URL:""}).CLOUD_ROOT+"/hdfsManager/#/manager/authorizationAppManager?token="+this.dlToken,thumbnail:"https://hdfs.dlmeasure.com:9001/hdfs/source/image/1630425600000/07054f90.png",workorderUnprocessed:0,alarmUnprocessed:0}),this.dlAppList=[...e.data,{groupName:"其他应用",id:999,apps:s}]}}))}}))},arrChange(e,t){const s=[];for(;t.length>0;)s.push(t.splice(0,e));return s},init(){this.loading=!0,n.a.dictSelect("Scene").then((e=>{let t=e.Scene,s={"我的应用":[]};for(let e of t)s[e.Name]=[];a.a.getUserApplicationList().then((e=>{if(e&&e.length){this.loading=!1,e=this.sortByEmptyAttribute(e,"scene");for(let t of e)t.sceneName||(t.sceneName="我的应用");const t=e.reduce(((e,t)=>{const s=t.sceneName;return e[s]?e[s].push(t):e[s]=[t],e}),{});for(let e in t)s[e]=t[e];this.appList=s}})).catch((e=>{this.loading=!1}))}))},sortByEmptyAttribute:(e,t)=>e.sort(((e,s)=>{const i=e[t],a=s[t];return null==i&&null!=a?-1:null==a&&null!=i?1:0})),gotoApp(e){if(this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")){if(!e.firstChildLink)return void this.$message({showClose:!0,message:"暂无跳转地址！"});let t=e.firstChildLink;if(t=e.firstChildLink.replace("#!Index.htm",""),e.useDlmToken)return t=this.$utils.setUrlToken(t,e.dlmToken,"token"),void window.open(t,"_blank");let s={Id:e.firstChildId,App:e.rootId,Name:e.firstChildName,Val:t,link:t,target:e.target,menuStyle:e.menuStyle,applicationId:e.applicationId,applicationName:e.applicationName};window.top.postMessage("*#hd#*"+JSON.stringify({action:"OPEN_TAG",params:s}),"*")}else window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},gotoDlApp(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?e.url?window.open(e.url,e.target):this.$message({showClose:!0,message:"暂无跳转地址！"}):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")}},mounted(){this.init()}},r=o,l=(s("96df"),s("2877")),c=Object(l.a)(r,(function(){var e=this,t=e._self._c;return t("div",{ref:"appRef",staticClass:"my-apps"},[e._m(0),t("div",{staticClass:"app-con"},[e.appList?e._l(e.appList,(function(i,a,n){return t("div",{directives:[{name:"show",rawName:"v-show",value:i.length,expression:"val.length"}],key:n,staticClass:"group-item"},[t("h3",[e._v(e._s(a))]),t("div",{staticClass:"content"},e._l(i,(function(i){return t("div",{key:i.id,staticClass:"app-item",on:{click:function(t){return e.gotoApp(i)}}},[t("div",{staticClass:"img-con"},[t("img",{attrs:{src:i.showIcon||s("6a89")}}),i.tagName?t("span",[e._v(e._s(i.tagName))]):e._e()]),t("h1",{attrs:{title:i.name}},[e._v(e._s(i.name))])])})),0)])})):e._e(),e._l(e.dlAppList,(function(i){return t("div",{key:i.id,staticClass:"group-item"},[t("h3",[e._v(e._s(i.groupName))]),t("div",{staticClass:"content"},e._l(i.apps,(function(i){return t("div",{key:i.id,staticClass:"app-item",on:{click:function(t){return e.gotoDlApp(i)}}},[t("div",{staticClass:"img-con"},[t("img",{attrs:{src:i.thumbnail||s("5853")}})]),t("h1",[e._v(e._s(i.appName))])])})),0)])}))],2),e.appList||e.dlAppList.length?e._e():t("div",{staticClass:"no-data",staticStyle:{height:"340px"}},[t("cue-empty",{staticStyle:{"background-color":"transparent"},attrs:{text:"暂无数据"}})],1)])}),i,!1,null,"69d850f0",null),u=c.exports,d=(s("7d54"),{props:{height:{type:Number,default:784}},data:()=>({activeTab:"1",index:1,tabPane:[{name:"待处理",type:1,num:0,value:"1"},{name:"已处理",type:2,num:0,value:"3"},{name:"已发起",type:3,num:1,value:"2"}],userInfo:null,list:[],loading:!1}),computed:{heightStyle(){return 1084-this.height+"px"},currentUser(){return this.$store.getters.currentUser}},watch:{activeTab(e){this.index=1,this.list=[],this.query()}},methods:{gotoTask(e){if(this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")){if(e.webLink){let t=this.$utils.setUrlToken(e.webLink,this.Headers.Authorization);window.open(t,"_blank")}}else window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},scrollQuery(){++this.index,this.query()},query(){this.loading=!0;let e={conditions:[],data:{status:this.activeTab},index:this.index,size:11};a.a.queryCooperate(e).then((e=>{this.loading=!1,this.index<2?this.list=e.rows:this.list=this.list.concat(e.rows)}))},queryTabCount(){a.a.getGroupCount().then((e=>{this.tabPane.forEach((t=>{t.num=e[t.value]}))}))},seeMore(){if(this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")){let e={title:"待办中心",path:"/todoList",aid:""},t="*#hd#*"+JSON.stringify({action:"OPEN_TAG",params:e});window.top.postMessage(t,"*")}else window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")}},mounted(){this.$store.dispatch("GETUSERINFO").then((e=>{this.userInfo=e})),this.queryTabCount(),this.query()}}),m=d,p=(s("1d5d"),Object(l.a)(m,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"my-to-do"},[e._m(0),t("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},e._l(e.tabPane,(function(s){return t("el-tab-pane",{key:s.type,attrs:{name:s.value}},[t("template",{slot:"label"},[e._v(" "+e._s(s.name)+"("+e._s(s.num||0)+")")])],2)})),1),t("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:e.scrollQuery,expression:"scrollQuery"},{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"to-do-list-container"},[e._l(e.list,(function(s){return t("div",{key:s.id,staticClass:"to-do-list-item",on:{click:function(t){return e.gotoTask(s)}}},[t("div",{staticClass:"to-do-list-item__top"},[t("div",{staticClass:"task-name",attrs:{title:s.taskContent}},[e._v(e._s(s.taskContent))]),t("div",{staticClass:"creator-name"},[e._v(e._s(s.creatorName))]),t("div",{staticClass:"time"},[e._v(e._s(s.time))])]),t("div",{staticClass:"to-do-list-item__button"},[e._v(" "+e._s(s.source)+" ")])])})),e.list.length?e._e():t("cue-empty",{attrs:{text:"暂无数据"}})],2),e.list.length?t("div",{staticClass:"see-more",on:{click:e.seeMore}},[e._v("查看更多>")]):e._e()],1)}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"head"},[t("div",{staticClass:"lab"},[e._v("待办中心")]),t("div",{staticClass:"en"},[e._v("BACKLOG CENTER")])])}],!1,null,"74169f63",null)),g=p.exports,A=s("7b6a").a,h=(s("d4d9"),Object(l.a)(A,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"user-info"},[e.currentUser?t("div",{staticClass:"user-info__top"},[t("div",{staticClass:"user-info__top__left"},[t("img",{attrs:{src:e.currentUser.avatar||s("b96c"),alt:""}})]),t("div",{staticClass:"user-info__top__middle"},[t("div",{staticClass:"user-name"},[e._v(e._s(e.currentUser.name))]),t("div",{staticClass:"user-dept"},[e._v(e._s(e.currentUser.orgName||"/"))])]),t("div",{staticClass:"user-info__top__right"},[t("div",{staticClass:"time"},[e._v(e._s(e.currentTime))]),t("div",{staticClass:"xingqi",domProps:{innerHTML:e._s(e.nyrzhou())}})])]):e._e(),t("div",{staticClass:"user-info__bottom"},[e.typeList&&e.typeList.length?e._l(e.typeList,(function(s,i){return t("div",{key:i,staticClass:"bottom-item",on:{click:function(t){return e.typeClick(s)}}},[s.banner?t("img",{attrs:{src:s.banner}}):e._e(),t("div",[e._v(e._s(s.Name))])])})):e._e()],2)])}),[],!1,null,"261ec306",null)),f=h.exports,v=[function(){var e=this._self._c;return e("div",{staticClass:"head"},[e("img",{attrs:{src:s("7938")}}),e("div",{staticClass:"lab"},[this._v("我的日程")]),e("div",{staticClass:"line"})])},function(){var e=this._self._c;return e("div",{staticClass:"lab-name"},[e("div",{staticClass:"lab"},[this._v("日历安排")])])}],w=s("ea29").a,b=(s("ca3a"),Object(l.a)(w,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"pick-con"},[e._m(0),t("date-panel",{ref:"datePanel",on:{pick:e.pick}}),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus"},on:{click:e.addCalendar}},[e._v("添加提醒")]),e.list.length?t("div",{staticClass:"list-wrp",on:{click:function(t){return e.calendarClick(e.list[e.listIndex])}}},[e._m(1),t("div",{staticClass:"time"},[e._v(e._s(e.getTime(e.list[e.listIndex])))]),t("p",[e._v(e._s(e.list[e.listIndex].content))])]):e._e(),e.list.length?t("div",{staticClass:"page-num"},[t("div",{staticClass:"tot"},[e._v("共"+e._s(e.list.length)+"条")]),t("el-button",{attrs:{icon:"el-icon-arrow-left",disabled:0==e.listIndex},on:{click:function(t){return e.swithData("upper")}}}),t("el-button",{attrs:{icon:"el-icon-arrow-right",disabled:e.listIndex==e.list.length-1},on:{click:function(t){return e.swithData("next")}}})],1):e._e(),e.dialogVisible?t("div",{staticClass:"form-con"},[t("h1",[e._v("新建日程")]),t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入内容"},model:{value:e.formData.content,callback:function(t){e.$set(e.formData,"content",t)},expression:"formData.content"}}),t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("全天")]),t("el-switch",{model:{value:e.formData.isAllDay,callback:function(t){e.$set(e.formData,"isAllDay",t)},expression:"formData.isAllDay"}})],1),e.formData.isAllDay?e._e():t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("开始")]),t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm"},model:{value:e.formData.startTime,callback:function(t){e.$set(e.formData,"startTime",t)},expression:"formData.startTime"}})],1),e.formData.isAllDay?e._e():t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("结束")]),t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm"},model:{value:e.formData.endTime,callback:function(t){e.$set(e.formData,"endTime",t)},expression:"formData.endTime"}})],1),t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("优先级")]),t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.priority,callback:function(t){e.$set(e.formData,"priority",t)},expression:"formData.priority"}},e._l(e.levelData,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("提醒")]),t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.remind,callback:function(t){e.$set(e.formData,"remind",t)},expression:"formData.remind"}},e._l(e.remindData,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("div",{staticClass:"form-item"},[t("span",{staticClass:"lable"},[e._v("通知")]),t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.inform,callback:function(t){e.$set(e.formData,"inform",t)},expression:"formData.inform"}},e._l(e.noticeData,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("div",{staticClass:"btn-s"},[t("el-button",{on:{click:e.closeDia}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveScheduleAndUpdate}},[e._v("确认")])],1)],1):e._e()],1)}),v,!1,null,"ce68a934",null)),y=b.exports,C=s("7716"),k=s("c1df"),T=s.n(k),N=s("ff8f"),D=s("f61d"),M={components:{noticDetails:N.default,userCrud:D.a},props:{height:{type:Number,default:784}},data:()=>({noticeList:[],detailVisible:!1,currentRow:null,loading:!1}),computed:{currentUser(){return this.$store.getters.currentUser}},methods:{rowClick(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.currentRow=e,this.detailVisible=!0):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},showUser(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.$refs.userCrud.visible=!0,this.currentRow=e,this.$refs.userCrud.tabCode="receive",this.$nextTick((()=>{this.$refs.userCrud.$refs.unReceive.Query()}))):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},getNoticeList(){this.loading=!0;Object(C.a)({index:1,size:3}).then((e=>{for(let t of e)t.releasedTime=T()(1e3*t.releasedTime).format("YYYY-MM-DD HH:mm");this.noticeList=e||[],this.loading=!1}))},seeMore(){if(this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")){let e={Id:"GRZX"+(new Date).getTime(),Name:"通知公告",Val:"/umpNotice",link:"/umpNotice",target:"_blankTop"};window.top.postMessage("*#hd#*"+JSON.stringify({action:"OPEN_TAG",params:e}),"*")}else window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")}},mounted(){this.getNoticeList()}},U=M,S=(s("7054"),Object(l.a)(U,(function(){var e=this,t=e._self._c;return e.noticeList.length?t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"my-to-do"},[t("div",{staticClass:"head"},[t("img",{attrs:{src:s("7938")}}),t("div",{staticClass:"lab"},[e._v("通知公告")]),t("div",{staticClass:"line"}),e.noticeList.length>=8?t("div",{staticClass:"more"},[t("span",{on:{click:e.seeMore}},[e._v("更多")])]):e._e()]),t("div",{staticClass:"to-do-list-container"},[e._l(e.noticeList,(function(s){return t("div",{key:s.id,staticClass:"to-do-list-item",on:{click:function(t){return e.rowClick(s)}}},[t("div",{staticClass:"to-do-list-item__top"},[t("div",{staticClass:"task-name",domProps:{innerHTML:e._s(s.name)}}),t("div",{staticClass:"creator-name"},[e._v(e._s(s.releasedBy||"/"))]),t("div",{staticClass:"time"},[e._v(e._s(s.releasedTime))])]),t("div",{staticClass:"to-do-list-item__button"})])})),e.noticeList.length?e._e():t("cue-empty",{attrs:{text:"暂无数据"}}),t("cue-dialog",{attrs:{visible:e.detailVisible,fullscreen:"",title:"详情","custom-class":"notice-details-dialog","append-to-body":""},on:{"update:visible":function(t){e.detailVisible=t}}},[e.detailVisible?t("notic-details",{attrs:{info:e.currentRow},on:{showUser:e.showUser}}):e._e()],1),t("user-crud",{ref:"userCrud",attrs:{currentRow:e.currentRow}})],2)]):e._e()}),[],!1,null,"0e5fe0f4",null)),I={components:{myApps:u,toDo:g,User:f,Calendar:y,Notice:S.exports},data:()=>({bgUrl:`url(${s("af07")})`}),methods:{}},x=(s("e963"),Object(l.a)(I,(function(){var e=this._self._c;return e("div",{staticClass:"workbench"},[e("div",{staticClass:"workbench-wrap"},[e("div",{staticClass:"left"},[e("my-apps")],1),e("div",{staticClass:"right"},[e("user"),e("notice"),e("calendar")],1)])])}),[],!1,null,"43a3b780",null));t.default=x.exports},ca3a:function(e,t,s){"use strict";s("befa")},d227:function(e,t,s){e.exports=s.p+"static/img/menu_3.15b685d9.png"},d4d9:function(e,t,s){"use strict";s("b9a3")},e0bf:function(e,t,s){},e963:function(e,t,s){"use strict";s("e0bf")},ea29:function(e,t,s){"use strict";(function(e){s("e9f5"),s("910d"),s("f665");var i=s("f1c5"),a=s("7716");t.a={name:"Calendar",components:{DatePanel:i.a},data:()=>({currentDay:new Date,dialogVisible:!1,options:[],listIndex:0,levelData:[{label:"立刻",value:"立刻"},{label:"紧急",value:"紧急"},{label:"高",value:"高"},{label:"普通",value:"普通"},{label:"低",value:"低"}],remindData:[{label:"时间开始时",value:"0"},{label:"提前5分钟",value:"5"},{label:"提前15分钟",value:"15"},{label:"提前30分钟",value:"30"},{label:"提前1小时",value:"60"},{label:"提前2小时",value:"120"},{label:"提前3小时",value:"180"},{label:"提前1天",value:"1440"},{label:"提前2天",value:"2880"}],noticeData:[{label:"app",value:"app"},{label:"短信",value:"sms"}],formData:{content:"",isAllDay:!1,startTime:"",endTime:"",title:"",type:"工作日志",priority:"低",inform:"",remind:""},innerEvent:[]}),mounted(){this.$nextTick((()=>{this.$refs.datePanel.value=this.currentDay})),this.getScheduleList()},computed:{list(){return this.innerEvent.filter((e=>this.dayInEvent(this.currentDay,e)))},getTime:()=>t=>e(t.startTime).format("hh:mm")+"~"+e(t.endTime).format("hh:mm"),currentUser(){return this.$store.getters.currentUser}},methods:{getScheduleList(){Object(a.b)({}).then((e=>{this.innerEvent=e,this.$refs.datePanel.cellClassName=this.cellClassName}))},addCalendar(){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.formData.startTime=e(this.currentDay).format("YYYY-MM-DD hh:mm"),this.formData.endTime=e(this.currentDay).format("YYYY-MM-DD hh:mm"),this.dialogVisible=!0):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},calendarClick(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.formData=e,this.dialogVisible=!0):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},saveScheduleAndUpdate(){if(this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")){if(!this.formData.content)return void this.$message({message:"内容不能为空!",type:"warning"});this.formData.isAllDay=this.formData.isAllDay?1:0,Object(a.c)(this.formData).then((e=>{this.$message({message:"保存成功!",type:"success"}),this.closeDia(),this.getScheduleList()}))}else window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},closeDia(){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.dialogVisible=!1,this.formData={content:"",isAllDay:!1,startTime:"",endTime:"",title:"",type:"工作日志",priority:"低",inform:"",remind:""}):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},cellClassName(e){return this.innerEvent.find((t=>this.dayInEvent(e,t)))?"active-cell":""},dayInEvent(t,s){let i=e(t).format("YYYYMMDD"),a=Number(i+"0000"),n=Number(i+"2359"),o=Number(e(s.startTime).format("YYYYMMDDHHmm")),r=Number(e(s.endTime).format("YYYYMMDDHHmm"));return!(o<a&&r<=a||o>n)},pick(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?(this.$refs.datePanel.value=e,this.currentDay=e,this.$refs.datePanel.resetView&&this.$refs.datePanel.resetView()):window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")},swithData(e){this.currentUser&&this.currentUser.id&&window.localStorage.getItem("UniWimAuthorization")?"upper"==e?this.listIndex--:"next"==e&&this.listIndex++:window.top.postMessage("*#hd#*"+JSON.stringify({action:"LogoutSys",params:{}}),"*")}}}}).call(this,s("c1df"))},f61d:function(e,t,s){"use strict";var i=s("a27e"),a={props:{currentRow:{type:Object,default:()=>{}}},data:()=>({name:"",userCrud:Object(i.a)("/ump/noticeRecord"),tabCode:"receive",visible:!1,recordColumns:[{title:"姓名",data:"userName",minWidth:100},{title:"部门",data:"orgName",minWidth:100},{title:"查看时间",data:"consultTime",minWidth:120,dtype:"date",format:"yyyy-MM-dd HH:mm"}]}),watch:{tabCode:{handler(e,t){this.name="",this.$refs[e].Query()}},visible(e){e&&(this.name="",this.$nextTick((()=>{this.$refs[this.tabCode].Query()})))}},methods:{beforeUserQuery(e){return e.data={},e.data.noticeId=this.currentRow.id,this.name&&(e.data.userName=this.name),e.data.status=0,e},beforeUserQuerys(e){return e.data={},this.name&&(e.data.userName=this.name),e.data.noticeId=this.currentRow.id,e.data.status=1,e}}},n=a,o=s("2877"),r=Object(o.a)(n,(function(){var e=this,t=e._self._c;return t("cue-dialog",{attrs:{visible:e.visible,current:e.tabCode,width:"800px"},on:{"update:visible":function(t){e.visible=t},"update:current":function(t){e.tabCode=t}}},[t("cue-dialog-view",{attrs:{code:"receive",name:"已查阅("+(e.currentRow||{}).consultCount+")"}},[t("cue-crud",{ref:"receive",staticClass:"h-50 no-borer-crud",attrs:{columns:e.recordColumns,"before-query":e.beforeUserQuerys,version:"1",power:"",crud:e.userCrud}},[t("template",{slot:"condition"},[t("el-form",{attrs:{inline:""}},[t("el-form-item",[t("el-input",{attrs:{clearable:"",placeholder:"请输入姓名"},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}})],1)],1)],1)],2)],1),t("cue-dialog-view",{attrs:{code:"unReceive",name:"未查阅("+((e.currentRow||{}).receiveCount-(e.currentRow||{}).consultCount)+")"}},[t("cue-crud",{ref:"unReceive",staticClass:"h-50 no-borer-crud",attrs:{columns:e.recordColumns.slice(0,2),"before-query":e.beforeUserQuery,version:"1",power:"",crud:e.userCrud}},[t("template",{slot:"condition"},[t("el-form",{attrs:{inline:""}},[t("el-form-item",[t("el-input",{attrs:{clearable:"",placeholder:"请输入姓名"},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}})],1)],1)],1)],2)],1)],1)}),[],!1,null,null,null);t.a=r.exports}}]);