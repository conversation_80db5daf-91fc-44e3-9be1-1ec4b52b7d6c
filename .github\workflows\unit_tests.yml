name: Unit tests

on:
  push:
    branches:
      - main
      - master
      - v*-maintenance
    paths:
      - '.github/workflows/**'
      - 'src/**'
      - 'utest/**'
      - '!**/*.rst'


jobs:
  test_using_builtin_python:

    strategy:
      fail-fast: false
      matrix:
        os: [ 'ubuntu-latest', 'windows-latest' ]
        python-version: [ '3.8', '3.9', '3.10', '3.11', '3.12', '3.13', 'pypy-3.8' ]
        exclude:
          - os: windows-latest
            python-version: 'pypy-3.8'

    runs-on: ${{ matrix.os }}

    name: Python ${{ matrix.python-version }} on ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.6.0
        with:
          python-version: ${{ matrix.python-version }}
          architecture: 'x64'

      - name: Run unit tests
        run: |
          python -m pip install -r utest/requirements.txt
          python utest/run.py -v
