/**
 * 配置Schema管理器
 */

import type {
  ComponentConfigSchema,
  ConfigFieldDefinition,
  ConfigValues,
  ValidationResult,
  ConfigContext,
  ConfigCondition,
} from '@/types/config'

import {computed} from "vue";
import {getAvailableVariables} from "@/utils/availableVariables.ts";

const availableVariables = computed(() => getAvailableVariables())

/**
 * 为 schema 添加高级选项组，若 schema 的 fields 中不存在 timeout、error、error_handle 字段，则添加这些字段
 * @param schema 组件配置 schema 对象
 * @returns 添加高级选项组后的 schema 对象
 */
 const addCommonFieldsIfNeeded = (schema:ComponentConfigSchema)=>{
  const { fields, groups } = schema;
  const hasOtherGroup = groups?.some(group => group.id === 'other');

  // 检查是否存在 timeout、error、error_handle 字段
  const hasTimeout = fields.hasOwnProperty('timeout');
  const hasError = fields.hasOwnProperty('error');
  const hasErrorHandle = fields.hasOwnProperty('error_handle');

  // 开始、结束、条件判断、等待、连接数据库、变量赋值、增加/减少日期、消息通知之外，都需要添加显示监控字段
  if(!['workflow_start', 'workflow_end', 'wait', 'condition', 'db_connect','variable_assignment','add_time','notifier_send'].includes(schema.componentType)){
    const hasShowMonitor = fields.hasOwnProperty('show_monitor');
    if (!hasShowMonitor) {
      fields.show_monitor = {
        type: 'boolean',
        label: '运行监控',
        description: '',
        placeholder: '',
        default: true,
        group: 'other',
        order: 1,
        switchTrue: '显示',
        switchFalse: '隐藏'
      };
    }
  }

  // 若不存在则添加对应字段
  if (!hasTimeout) {
    fields.timeout = {
      type: 'number',
      label: '超时时间',
      description: '',
      group: 'other',
      order: 1,
      default: 30,
      min: 1,
      max: 3000,
      unit: '秒',
    };
  }

  if (!hasError) {
    fields.error = {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children: [
        {
          id: 'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id: 'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    };
  }

  if (!hasErrorHandle) {
    fields.error_handle = {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
      ]
    };
  }

  if (!hasOtherGroup) {
    const advancedGroup = {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    };

    schema?.groups?.push(advancedGroup);
  }

return schema;
}

export class ConfigSchemaManager {
  private schemas: Map<string, ComponentConfigSchema> = new Map()

  /**
   * 注册组件配置Schema
   */
  registerSchema(componentType: string, schema: ComponentConfigSchema) {
    let transSchema = schema
    if(!['workflow_start', 'workflow_end', 'wait', 'condition'].includes(componentType)){
      transSchema = addCommonFieldsIfNeeded(schema)
    }
    this.schemas.set(componentType, transSchema)
  }

  /**
   * 获取组件配置Schema
   */
  getSchema(componentType: string): ComponentConfigSchema | null {
    return this.schemas.get(componentType) || null
  }

  /**
   * 获取所有已注册的组件类型
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.schemas.keys())
  }

  /**
   * 验证配置值
   */
  validateConfig(
    componentType: string,
    values: ConfigValues,
    context?: ConfigContext,
    isChildren?: boolean
  ): ValidationResult {
    const schema = this.getSchema(componentType)
    if (!schema) {
      return {
        isValid: false,
        errors: [
          {
            field: '_root',
            message: `未找到指令类型 ${componentType} 的配置Schema`,
            type: 'schema',
          },
        ],
        warnings: [],
      }
    }

    const errors: ValidationResult['errors'] = []
    const warnings: ValidationResult['warnings'] = []
    // 验证每个字段
    for (const [fieldName, fieldDef] of Object.entries(schema.fields)) {
      const value = values[fieldName]
      const fieldErrors = this.validateField(fieldName, value, fieldDef, values, context)
      if(!isChildren){
        errors.push(...fieldErrors)
      }else{
        if(fieldDef.children){
          if(values[fieldName]){
            fieldDef.children.forEach(child=>{
              const childValue = values[child.id]
              const childFieldErrors = this.validateField(child.id, childValue, fieldDef, values, context)
              errors.push(...childFieldErrors)
            })
          }
        }


      }
    }

    // 检查必填字段
    for (const [fieldName, fieldDef] of Object.entries(schema.fields)) {
      if (fieldDef.required && this.shouldShowField(fieldDef, values)) {
        const value = values[fieldName]
        if(!isChildren){
          if (value === undefined || value === null || value === '') {
            errors.push({
              field: fieldName,
              message: `${fieldDef.label} 是必填项`,
              type: 'required',
            })
          }
        }
      }
      if(fieldDef.children){
        // if(values[fieldName]){
          fieldDef.children.forEach(child=>{
            if (child.required && this.shouldShowField(child, values)) {
              const childValue = values[child.id]
              if (childValue === undefined || childValue === null || childValue === '') {
                errors.push({
                  field: child.id,
                  message: `${child.label} 是必填项`,
                  type: 'required',
                })
              }
            }
          })
        // }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * 验证单个字段
   */
  private validateField(
    fieldName: string,
    value: any,
    fieldDef: ConfigFieldDefinition,
    allValues: ConfigValues,
    context?: ConfigContext,
  ): ValidationResult['errors'] {
    const errors: ValidationResult['errors'] = []

    // 如果字段不应该显示，跳过验证
    if (!this.shouldShowField(fieldDef, allValues)) {
      return errors
    }

    // 如果值为空且不是必填，跳过验证
    if ((value === undefined || value === null || value === '') && !fieldDef.required) {
      return errors
    }

    // 类型验证
    const typeError = this.validateFieldType(fieldName, value, fieldDef)
    if (typeError) {
      errors.push(typeError)
    }

    // 自定义验证规则
    if (fieldDef.validation) {
      for (const rule of fieldDef.validation) {
        const ruleError = this.validateRule(fieldName, value, rule, fieldDef)
        if (ruleError) {
          errors.push(ruleError)
        }
      }
    }

    // 新增：outputVariable字段的特殊校验
    if (fieldDef.outputVariable && value) {
      const variables = availableVariables.value
      // 如果存在超过1个相同变量则存在同名，1个的时候是当前变量自己
      if (variables.filter(v => v.name === value).length > 1) {
        errors.push({
          field: fieldName,
          message: `${fieldDef.label} 的值 "${value}" 已存在，请使用唯一变量名`,
          type: 'unique'
        })
      }
      // 提取变量名（支持多个自定义变量的验证）
      else if(fieldDef.type === 'variables_map'){
        const map_variables = value.map((value: string) => value.variable)
        // 使用Set去重后再检查重复变量
        const uniqueVariables = [...new Set(map_variables)]
        const duplicateVariables = uniqueVariables.filter(v =>
          v && variables.filter(t => t.name === v).length > 1
        )
        if (duplicateVariables.length > 0) {
          errors.push({
            field: fieldName,
            message: `${fieldDef.label} 的值 "${duplicateVariables.join('", "')}" 已存在，请使用唯一变量名`,
            variables: duplicateVariables,
            type: 'unique'
          })
        }
      }
    }

    return errors
  }

  /**
   * 验证字段类型
   */
  private validateFieldType(fieldName: string, value: any, fieldDef: ConfigFieldDefinition) {
    if (value === undefined || value === null || value === '') {
      return null
    }

    switch (fieldDef.type) {
      case 'number':
        if (isNaN(Number(value))) {
          return { field: fieldName, message: `${fieldDef.label} 必须是数字`, type: 'type' }
        }
        if (fieldDef.min !== undefined && Number(value) < fieldDef.min) {
          return {
            field: fieldName,
            message: `${fieldDef.label} 不能小于 ${fieldDef.min}`,
            type: 'min',
          }
        }
        if (fieldDef.max !== undefined && Number(value) > fieldDef.max) {
          return {
            field: fieldName,
            message: `${fieldDef.label} 不能大于 ${fieldDef.max}`,
            type: 'max',
          }
        }
        break

      case 'boolean':
        if (typeof value !== 'boolean') {
          return { field: fieldName, message: `${fieldDef.label} 必须是布尔值`, type: 'type' }
        }
        break

      case 'url':
        try {
          new URL(value)
        } catch {
          return { field: fieldName, message: `${fieldDef.label} 必须是有效的URL`, type: 'format' }
        }
        break

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          return {
            field: fieldName,
            message: `${fieldDef.label} 必须是有效的邮箱地址`,
            type: 'format',
          }
        }
        break

      case 'select':
        // 跳过异步选择字段的选项验证
        if (fieldDef.async && fieldDef.optionsLoader) {
          break
        }
        if (fieldDef.options && !fieldDef.options.some((opt) => opt.value === value)) {
          return {
            field: fieldName,
            message: `${fieldDef.label} 的值不在允许的选项中`,
            type: 'options',
          }
        }
        break
    }

    return null
  }

  /**
   * 验证自定义规则
   */
  private validateRule(fieldName: string, value: any, rule: any, fieldDef: ConfigFieldDefinition) {
    switch (rule.type) {
      case 'pattern':
        if (rule.value && !new RegExp(rule.value).test(value)) {
          return {
            field: fieldName,
            message: rule.message || `${fieldDef.label} 格式不正确`,
            type: 'pattern',
          }
        }
        break

      case 'custom':
        if (rule.validator && typeof rule.validator === 'function') {
          const result = rule.validator(value)
          if (result !== true) {
            return {
              field: fieldName,
              message:
                typeof result === 'string' ? result : rule.message || `${fieldDef.label} 验证失败`,
              type: 'custom',
            }
          }
        }
        break
    }

    return null
  }

  /**
   * 判断字段是否应该显示
   */
  shouldShowField(fieldDef: ConfigFieldDefinition, values: ConfigValues): boolean {
    if (fieldDef.hidden) {
      return false
    }

    if (!fieldDef.conditions || fieldDef.conditions.length === 0) {
      return true
    }

    return fieldDef.conditions.every((condition) => this.evaluateCondition(condition, values))
  }

  /**
   * 评估条件
   */
  private evaluateCondition(condition: ConfigCondition, values: ConfigValues): boolean {
    const fieldValue = values[condition.field]

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value
      case 'not_equals':
        return fieldValue !== condition.value
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue)
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue)
      case 'greater':
        return Number(fieldValue) > Number(condition.value)
      case 'less':
        return Number(fieldValue) < Number(condition.value)
      default:
        return true
    }
  }

  /**
   * 获取字段的默认值
   */
  getDefaultValues(componentType: string): ConfigValues {
    const schema = this.getSchema(componentType)
    if (!schema) {
      return {}
    }

    const defaults: ConfigValues = {}
    for (const [fieldName, fieldDef] of Object.entries(schema.fields)) {
      if (fieldDef.default !== undefined) {
        defaults[fieldName] = fieldDef.default
      }
    }

    return defaults
  }

  /**
   * 应用预设配置
   */
  applyPreset(
    componentType: string,
    presetName: string,
    currentValues: ConfigValues,
  ): ConfigValues {
    const schema = this.getSchema(componentType)
    if (!schema || !schema.presets || !schema.presets[presetName]) {
      return currentValues
    }

    return {
      ...currentValues,
      ...schema.presets[presetName].config,
    }
  }
}

// 全局实例
export const configSchemaManager = new ConfigSchemaManager()
