from typing import Dict

from actions.control import group
from core.executor import ExecutionContext, ActionContext
from actions.control import logger


@group.action(
    type="workflow_start",
    label="开始",
    description="工作流的开始节点，标识流程入口点",
    category="control",
    config_schema={
        "workflow_name": {"type": "string", "required": False, "default": ""},
        "description": {"type": "string", "required": False, "default": ""},
        "log_level": {
            "type": "string",
            "default": "INFO",
            "options": ["DEBUG", "INFO", "WARN", "ERROR"],
        },
        "confirm_start": {"type": "boolean", "default": False},
        "timeout": {"type": "number", "default": 300, "min": 1, "max": 3600},
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
    outputs=["workflow_context"],
)
def workflow_start(context: ActionContext, config: Dict):
    logger.info(
        f"开始执行{context.get_agent_context().task_id}-{context.get_agent_context().history_id}"
    )


@group.action(
    type="workflow_end",
    label="结束",
    description="工作流的结束节点，标识流程完成",
    category="control",
    icon="CircleClose",
    config_schema={
        "status": {
            "type": "string",
            "default": "success",
            "options": ["success", "failure", "cancelled", "timeout"],
        },
        "message": {"type": "string", "required": False, "default": ""},
        "return_data": {"type": "string", "required": False, "default": ""},
        "cleanup": {"type": "boolean", "default": True},
        "log_summary": {"type": "boolean", "default": True},
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
    inputs=["workflow_context"],
)
def workflow_end(context: ActionContext, config: Dict):
    logger.info(
        f"结束执行{context.get_agent_context().task_id}-{context.get_agent_context().history_id}"
    )
