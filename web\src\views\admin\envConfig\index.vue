<template>
  <div class="service-package" v-loading="loading">
    <div class="header">
      <div class="title-section">
        环境配置
        <div class="header-tools">
          <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
            <i class="action-iconfont icon-shuaxinzhongzhi"></i>
            刷新
          </el-button>
          <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
            <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
            新增
          </el-button>
          <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini" @click="onEdit">
            <i class="action-iconfont icon-bianji"></i>
            编辑
          </el-button>
          <el-button class="header-tools-item" type="circle" :disabled="currentRow == null || currentRow?.type == 'inner'" size="mini"
            @click="onDelete">
            <i class="action-iconfont icon-huishouzhanshanchu"></i>
            删除
          </el-button>
        </div>
      </div>
      <div class="condition-section">
        <el-form :inline="true" :model="params">
          <el-form-item label="编码">
            <el-input v-model.trim="params.code" type="text" style="width: 160px;" clearable @keyup.enter="onSubmit" />
          </el-form-item>
          <el-form-item label="名称">
            <el-input v-model.trim="params.name" type="text" style="width: 160px;" clearable @keyup.enter="onSubmit" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-section">
      <div class="table-content" v-show="tabType == 'table'">
        <el-table ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
          :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)" @rowClick="handleRowClick">
          <el-table-column type="index" label="序号" :index="1 + pagination.pageSize * (pagination.currentPage - 1)"
            align="center" width="60" />
          <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center"
            :width="it.width" :minWidth="100" :fixed="it.fixed">
            <template v-if="it.dtype == 'date'" #default="scope">
              {{ scope.row[it.data] ? formatTime(scope.row[it.data]) : '' }}
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 50vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery" />
        </div>
      </div>
    </div>
  </div>
  <el-dialog class="servicePackage-dialog envconfig-dialog" :append-to-body="false" v-model="dialogFormVisible" :title="isNew ? '新增' : '编辑'" width="650" style="border-radius: 12px;" :destroy-on-close="true"
    top="30vh">
    <el-form :model="form" ref="ruleFormRef" label-width="100px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="name" label="名称" :label-line="true" required>
            <el-input v-model.trim="form.name" :disabled="form.type == 'inner'" :maxlength="32" type="text" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="code" label="编码" :label-line="true" required>
            <el-input v-model.trim="form.code" :disabled="form.type == 'inner'" type="text" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="value" label="值" :label-line="true" required>           
            <el-input v-model="form.value" :rows="4" type="textarea"  />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="remark" label="备注" :label-line="true">           
            <el-input v-model="form.remark" :rows="4" type="textarea"  />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="save">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox, Action } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'

const route = useRoute()
const userStore = useUserStore()


const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
  currentPage: 1,
  pageSize: 30,
  total: 0
})
const isNew = ref(true)
const form = ref({
  name: '',
  code: '',
  value: '',
  remark: '',
  type: 'external'
})
const dialogFormVisible = ref(false)
const tabType = ref('table')
const params = ref({
  code: '',
  name: ''
})
const tableData = ref([])
const tableColumns = ref([
  { data: 'name', title: '名称' },
  { data: 'code', title: '编码' },
  { data: 'value', title: '值', showOverflowTooltip: true },
  { data: 'remark', title: '备注', scoped: 'remark', minWidth: 200 },
  { data: 'updater', title: '更新人', width: 120 },
  { data: 'updated', title: '更新时间', dtype: 'date', width: 200 },
])

const ruleFormRef = ref<FormInstance>()

//ref 
const tableRef = ref<HTMLElement>()

const codeList = ref([])


//computed
// isUniwimPc
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc'
})

const currentUser = computed(() => {
  return userStore.userInfo
})

// 验证code是否重复
const validateCode = (rule: any, value: any, callback: any) => {
  if (codeList.value.includes(value) && currentRow.value?.code !== value) {
    callback(new Error('编码重复'))
  } else {
    callback()
  }
};


const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'change' }
  ],
  code: [
    { required: true, message: '请输入编码', trigger: 'change' },
    { validator: validateCode, trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入值', trigger: 'change' }
  ]
})

const save = async() => {
  let isValidate = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
  if(!isValidate)return
  let update_params = {
    ...form.value
  };
  if (isNew.value) {
    tableInsert(update_params)
  } else {
    tableUpdate(update_params)
  }
}

const tableInsert = (insert_params:any)=> {
  saasApi.AIAgentConfigAdd(insert_params).then(res=>{
    if (res?.Success) {
      ElMessage({
        message: '新增成功!',
        type: 'success',
        showClose: true
      })
      // 新增成功,将code存入codeList
      if (!codeList.value.includes(insert_params.code)) codeList.value.push(insert_params.code)
      setTimeout(()=>{
        tableQuery()
      },200)
      dialogFormVisible.value = false
    } else {
      ElMessage({
        message: '新增失败!',
        type: 'error',
        showClose: true
      })
    }
  }).catch(()=>{
  }).finally(() => {
  })
}
const tableUpdate = (update_params:any)=>{
  let input_params = update_params
  saasApi.AIAgentConfigUpdate(input_params).then(res=>{
    if (res?.Code === 0) {
      ElMessage({
        message: '编辑成功!',
        type: 'success',
        showClose: true
      })
      // 更新codeList
      codeList.value = codeList.value.map((it: any) => {
        if (it === currentRow.value?.code) {
          return update_params.code
        }
        return it
      })
      setTimeout(()=>{
        tableQuery()
      },200)
      dialogFormVisible.value = false
    } else {
      ElMessage({
        message: '编辑失败!',
        type: 'error',
        showClose: true
      })
    }
  }).catch(()=>{
  }).finally(() => {
  })
}

const handleRowClick = (row:any, show:boolean) => {
  currentRow.value = row
}

const tableQuery = () => {
  loading.value = true
  currentRow.value = null
  const query_params: any = {
    conditions: [],
    data: {},
    index: pagination.value.currentPage,
    size: pagination.value.pageSize,
  }
  if (params.value.code) {
    query_params.data.code = params.value.code
  }
  if (params.value.name) {
    query_params.data.name = params.value.name
  }
  saasApi.AIAgentConfigQuery(query_params).then(res=>{
    if (typeof res?.rows == 'object') {
      pagination.value = {
        currentPage: res.current,
        pageSize: res.size,
        total: res.total
      }
      tableData.value = res.rows
    }
  }).catch(()=>{
    tableData.value = []
  }).finally(() => {
    loading.value = false
  })
}


const onSubmit = () => {
  tableQuery()
}

const formatTime = (data:any, format:string = 'YYYY-MM-DD HH:mm') => {
  if (!data) return ''
  return moment(data).format(format)
}

const onReset = () => {
  params.value = {
    code: '',
    name: ''
  }
  pagination.value = {
    currentPage: 1,
    pageSize: 30,
    total: 0
  }
  isNew.value = true
  form.value = {
    name: '',
    code: '',
    value: '',
    remark: ''
  }
  currentRow.value = null
  tableData.value = []
  tableQuery()
}
const formSet = async (model: any) => {
  let data = JSON.parse(JSON.stringify(model))
  // 返回解析后的cron表达式
  return data
}
const onAdd = async () => {
  isNew.value = true
  form.value = {
    name: '',
    code: '',
    value: '',
    remark: ''
  }
  dialogFormVisible.value = true
}
const onEdit = async () => {
  isNew.value = false
  form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
  dialogFormVisible.value = true
}


const onDelete = () => {
  ElMessageBox.confirm('确认删除配置吗？', '提示', {
    // autofocus: false,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    showClose: true,
    customClass: 'default-confirm-class',
    callback: (action: Action) => {
      if(action === 'confirm') {
        saasApi.AIAgentConfigDelete([currentRow.value.id]).then(res=>{
          ElMessage({
            message: '删除成功!',
            type: 'success',
            showClose: true
          })
          codeList.value = codeList.value.filter((it: any) => it !== currentRow.value.code)
          tableQuery();
        }).catch(()=>{
          ElMessage({
            message: '删除失败!',
            type: 'error',
            showClose: true
          })
        }).finally(() => {
        })
      }
    }
  })
}


const initInnerConfig = () => {
  return new Promise((resolve, reject) => {
    saasApi.AIAgentConfigInitInner().then(res=>{
      resolve(true)
    }).catch(err=>{
      reject(err)
    })
  })
}

const getAllCode = () => {
  saasApi.AIAgentConfigGetAllCode().then(res=>{
    codeList.value = res
  }).catch(err=>{
  })
}

onMounted(async() => {
  getAllCode()
  await initInnerConfig()
  tableQuery()
})
</script>
<style scoped lang="scss">
.el-dialog :deep(.el-dialog__title){
  font-size:14px;
  font-weight: bold;
}
.service-package {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 12px;
  box-sizing: border-box;
  background: #f7f7f9 !important;
  display: flex;
  flex-direction: column;

  .header {
    background: #fff;
    width: 100%;
    height: 112px;
    box-sizing: border-box;
    overflow: hidden;

    .title-section {
      height: 64px;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: SourceHanSansSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
      }
    }

    .header-tools {
      display: flex;
      align-items: center;

      .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
          margin-right: 4px;
          font-size: 14px;
        }

        span {
          margin-left: 6px;
          line-height: 17px;
        }

        &:hover {
          color: rgba(0, 84, 210, 0.8);
        }

        &:active {
          color: #0044A9;
        }

        &.is-disabled {
          color: #BCBFC3;
          cursor: not-allowed;
        }
      }
    }

    .condition-section {
      padding: 8px 16px;
      box-sizing: border-box;
      border-top: solid 1px #e8ecf0;
      display: flex;
      justify-content: space-between;

      .el-form-item {
        margin-bottom: 0;
      }

      .tab-list {
        .tab-list-item {
          width: 80px;
          height: 32px;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          border: 1px solid #E6E7E9;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.left {
            border-radius: 4px 0 0 4px;
          }

          &.right {
            border-radius: 0 4px 4px 0;
          }

          &.active {
            color: #FFFFFF;
            background: #0054D9;
            border-color: #0054D9;
          }
        }
      }
    }
  }

  .table-section {
    flex: 1;
    background: #fff;
  }

  .table-content {
    height: 100%;
    :deep(.el-table__cell){
      border-bottom: 1px solid #EEEEEE !important;
    }
    .el-link.task-link{
      font-size: 12px;
      &~.task-link{
        margin-left: 12px;
      }
    }
    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }

    ::v-deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .card-content {
    height: 100%;
    padding: 16px;
    box-sizing: border-box;

    .card-item-box {
      height: calc(100% - 48px);
      overflow-y: auto;
      margin-bottom: 16px;

      .card-item {
        width: calc(25% - 16px);
        min-width: 445px;
        height: 300px;
        display: inline-block;
        margin-right: 16px;
        padding: 16px;
        box-sizing: border-box;
        border: 1px solid #E6E7E9;
        border-radius: 4px;
        margin-bottom: 16px;
        cursor: pointer;

        &:hover {
          border-color: #0054D9;
        }

        &.active {
          border-color: #0054D9;
        }

        .card-item-title {
          height: 20px;
          line-height: 20px;
          font-weight: 500;
          font-size: 14px;
          color: rgba(34, 34, 34, 0.9);
          margin-bottom: 10px;
        }

        .card-item-tag-time {
          .el-tag {
            border-radius: 12px;
          }

          .card-item-tag-time-text {
            font-weight: 400;
            font-size: 12px;
            color: #BCBFC3;
            margin-left: 10px;
          }
        }

        .card-item-info {
          display: flex;
          margin-top: 12px;

          .card-item-info-item {
            font-weight: 400;
            font-size: 12px;
            color: #5C5F66;
            letter-spacing: 0;
            margin-right: 20px;
          }
        }

        .card-item-line {
          width: 100%;
          height: 0.5px;
          background: #EEEEEE;
          border-radius: 4px;
          margin: 16px 0;
        }

        .card-item-two-text {
          display: flex;
          flex-wrap: wrap;

          .card-item-two-text-item {
            width: 50%;
            display: inline-block;
            margin-bottom: 16px;

            .card-item-text1 {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-bottom: 10px;
            }

            .card-item-text2 {
              height: 18px;
              line-height: 18px;
              font-weight: 400;
              font-size: 12px;
              color: #222222;
            }
          }
        }

        .card-item-bottons {
          .card-item-botton {
            display: inline-flex;
            align-content: center;
            justify-content: center;
            padding: 8px 16px;
            box-sizing: border-box;
            background: #FFFFFF;
            border: 1px solid #E6E7E9;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 16px;
            font-size: 12px;

            &.disabled {
              color: #a8abb2;
              border-color: 1px solid #e4e7ed;
              cursor: not-allowed;
            }

            &:not(.disabled):hover {
              color: #0054D2;
              border: 1px solid #0054D2;
            }
          }
        }
      }

      // .card-item:nth-child(4n) {
      //   margin-right: 0;
      // }
    }

    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }
  }
}
</style>