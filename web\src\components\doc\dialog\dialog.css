
.canvas-content.dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000000;
  z-index: 2000;
}

.canvas-content.dialog-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 2000;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.canvas-content .dialog {
  position: absolute;
  padding: 0 30px 30px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
}

.canvas-content .dialog-title {
  position: relative;
  border-bottom: 1px solid #e2e6ed;
  margin-bottom: 30px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.canvas-content .dialog-title i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: inline-block;
  background: url(~@/assets/images/docx/close.svg);
}

.canvas-content .dialog-option__item {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.canvas-content .dialog-option__item span {
  margin-right: 12px;
  font-size: 14px;
  color: #3d4757;
  position: relative;
}

.canvas-content .dialog-option__item input,
.canvas-content .dialog-option__item textarea,
.canvas-content .dialog-option__item select {
  width: 276px;
  height: 30px;
  border-radius: 2px;
  border: 1px solid #d3d3d3;
  min-height: 30px;
  padding: 5px;
  box-sizing: border-box;
  outline: none;
  appearance: none;
  user-select: none;
  font-family: inherit;
}

.canvas-content .dialog-option__item input:focus,
.canvas-content .dialog-option__item textarea:focus {
  border-color: #4991f2;
}

.canvas-content .dialog-option__item--require::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
  position: absolute;
  left: -8px;
}

.canvas-content .dialog-menu {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.canvas-content .dialog-menu button {
  position: relative;
  display: inline-block;
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  background: #ffffff;
  line-height: 22px;
  padding: 0 16px;
  white-space: nowrap;
  cursor: pointer;
}

.canvas-content .dialog-menu button:hover {
  background: rgba(25, 55, 88, .04);
}

.canvas-content .dialog-menu__cancel {
  margin-right: 16px;
}

.canvas-content .dialog-menu button[type='submit'] {
  color: #ffffff;
  background: #4991f2;
  border-color: #4991f2;
}

.canvas-content .dialog-menu button[type='submit']:hover {
  background: #5b9cf3;
  border-color: #5b9cf3;
}