"""
Workflow-Use API 路由
提供workflow-use功能的REST API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import logging

# 导入集成模块
try:
    from experimental.workflow_use.integration import workflow_use_integration
    WORKFLOW_USE_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Workflow-Use集成不可用: {e}")
    WORKFLOW_USE_AVAILABLE = False
    workflow_use_integration = None

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/workflow-use", tags=["workflow-use"])


# 请求/响应模型
class RecordingConfig(BaseModel):
    browser: str = "chromium"
    start_url: str = "about:blank"
    enable_ai_fallback: bool = False


class RecordingResponse(BaseModel):
    success: bool
    session_id: Optional[str] = None
    workflow_nodes: Optional[List[Dict]] = None
    message: str
    error: Optional[str] = None
    features: Optional[Dict] = None
    stats: Optional[Dict] = None


class StatusResponse(BaseModel):
    success: bool
    status: str
    message: str
    adapter_status: Optional[Dict] = None


class WorkflowExecutionRequest(BaseModel):
    workflow_definition: Dict[str, Any]
    inputs: Optional[Dict[str, Any]] = None


class WorkflowExecutionResponse(BaseModel):
    success: bool
    result: Optional[Any] = None
    message: str
    error: Optional[str] = None


class ConversionRequest(BaseModel):
    wimtask_nodes: List[Dict]


class ConversionResponse(BaseModel):
    success: bool
    workflow_definition: Optional[Dict] = None
    message: str
    error: Optional[str] = None


@router.get("/status", response_model=StatusResponse)
async def get_status():
    """获取workflow-use集成状态"""
    if not WORKFLOW_USE_AVAILABLE:
        return StatusResponse(
            success=False,
            status="unavailable",
            message="Workflow-Use集成不可用"
        )
    
    try:
        status = workflow_use_integration.get_recording_status()
        return StatusResponse(
            success=status['success'],
            status=status.get('status', 'unknown'),
            message=status.get('message', ''),
            adapter_status=status.get('adapter_status')
        )
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capabilities")
async def get_capabilities():
    """获取workflow-use集成能力"""
    if not WORKFLOW_USE_AVAILABLE:
        return {
            "available": False,
            "error": "Workflow-Use集成不可用"
        }
    
    try:
        return workflow_use_integration.get_capabilities()
    except Exception as e:
        logger.error(f"获取能力信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recording/start", response_model=RecordingResponse)
async def start_recording(config: RecordingConfig):
    """启动workflow-use录制会话"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        # 转换配置
        recording_config = {
            "browser": config.browser,
            "start_url": config.start_url,
            "enable_ai_fallback": config.enable_ai_fallback
        }
        
        result = await workflow_use_integration.start_recording_session(recording_config)
        
        return RecordingResponse(
            success=result['success'],
            session_id=result.get('session_id'),
            workflow_nodes=result.get('workflow_nodes'),
            message=result.get('message', ''),
            error=result.get('error'),
            features=result.get('features'),
            stats=result.get('stats')
        )
        
    except Exception as e:
        logger.error(f"启动录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recording/stop", response_model=RecordingResponse)
async def stop_recording(session_id: Optional[str] = None):
    """停止workflow-use录制会话"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        result = await workflow_use_integration.stop_recording_session(session_id)
        
        return RecordingResponse(
            success=result['success'],
            workflow_nodes=result.get('workflow_nodes'),
            message=result.get('message', ''),
            error=result.get('error'),
            stats=result.get('stats')
        )
        
    except Exception as e:
        logger.error(f"停止录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflow/execute", response_model=WorkflowExecutionResponse)
async def execute_workflow(request: WorkflowExecutionRequest):
    """执行workflow-use工作流"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        result = await workflow_use_integration.execute_workflow(
            request.workflow_definition,
            request.inputs
        )
        
        return WorkflowExecutionResponse(
            success=result['success'],
            result=result.get('result'),
            message=result.get('message', ''),
            error=result.get('error')
        )
        
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/convert/wimtask-to-workflow-use", response_model=ConversionResponse)
async def convert_wimtask_to_workflow_use(request: ConversionRequest):
    """将WimTask格式转换为workflow-use格式"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        result = workflow_use_integration.convert_wimtask_workflow(request.wimtask_nodes)
        
        return ConversionResponse(
            success=result['success'],
            workflow_definition=result.get('workflow_definition'),
            message=result.get('message', ''),
            error=result.get('error')
        )
        
    except Exception as e:
        logger.error(f"格式转换失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recording/operations")
async def get_real_time_operations():
    """获取实时操作列表（兼容性接口）"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        return workflow_use_integration.get_real_time_operations()
    except Exception as e:
        logger.error(f"获取实时操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recording/pause")
async def pause_recording():
    """暂停录制（兼容性接口）"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        result = await workflow_use_integration.pause_recording()
        return result
    except Exception as e:
        logger.error(f"暂停录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recording/resume")
async def resume_recording():
    """恢复录制（兼容性接口）"""
    if not WORKFLOW_USE_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Workflow-Use集成不可用"
        )
    
    try:
        result = await workflow_use_integration.resume_recording()
        return result
    except Exception as e:
        logger.error(f"恢复录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cleanup")
async def cleanup_resources(background_tasks: BackgroundTasks):
    """清理workflow-use资源"""
    if not WORKFLOW_USE_AVAILABLE:
        return {"success": False, "message": "Workflow-Use集成不可用"}
    
    try:
        background_tasks.add_task(workflow_use_integration.cleanup)
        return {"success": True, "message": "清理任务已启动"}
    except Exception as e:
        logger.error(f"清理资源失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
