#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<AUTHOR>   zxf 
@Version :   1.0
@Time    :   2025/06/16 13:49:00
"""
import os
import json
import argparse
import requests
from typing import Dict
from typing import List
from typing import Any, Optional

from fastapi import Body
from fastapi import Query
from fastapi import Header
from fastapi import APIRouter
from fastapi import HTTPException
from fastapi import Response
from pydantic import BaseModel

from fastapi.responses import FileResponse
from executor.lifecycle import NodeStatus
from servers.websocket import ws_broadcast_proxy
from .utils.loggings import init_logger
from .utils.util import get_resource_path, parse_jwt
from models.workflow import WorkflowData, ExecutionResult, create_execution_id
from starlette.responses import StreamingResponse
from transpiler.workflow_transpiler import WorkflowTranspiler
from .req_robot import run_robot_code, take_task, upload_task_update
from .req_robot import TaskSchedulerManager
import time
import asyncio
from loguru import logger
from executor.task_executor import (
    TaskExecutor,
    ExecuteParam,
    task_manager as queued_task_manager,
)
from config import globals
from datetime import datetime, timedelta
import math

task_manager = TaskSchedulerManager()

from config.env_config import DLY_URL, get_config_item, AI_RUL, BPM_URL, YN_URL, config
from utils.file_util import get_excel_info_from_paths

router = APIRouter()

VERSION = "1.0.0"

# crud
TASK_UPDATE = "/wimai/api/task/update"
TASK_DETAIL_URI = "/wimai/api/task/detail"

# 修改基础信息
TASK_BASE_INFO = "/wimai/api/task/updateBaseInfo"
# 修改基础信息  务器没这个接口服
TASK_UPDATE_ROBOT = "/wimai/api/task/updateRobot"
# 启用/停用  务器没这个接口服
TASK_UPDATE_ACTIVE = "/wimai/api/task/updateActive"
# 提供给前端 模板配置的crud
TEMPLATE_CRUD = "/wimai/api/template/crud"

PROCESS_LIST = "/bpm/customize-api/definition/categorizedTask"

PROCESS_FORM = "/bpm/customize-api/definition/categorizedTask"


class TemplateCrud(BaseModel):
    query_param: dict = None
    body_param: object = None
    url: str
    method: str = "post"


class ExecutionResponse(BaseModel):
    success: bool
    execution_id: str
    message: str
    result: Optional[ExecutionResult] = None
    error: Optional[str] = None


class ExecuteWorkflowRequest(BaseModel):
    workflow: WorkflowData
    options: Optional[Dict[str, Any]] = None


def forward_get(url, authorization, params):
    headers = {"Authorization": authorization}
    try:
        response = requests.get(
            get_config_item(YN_URL) + url, params=params, headers=headers
        )
        if response.status_code != 200:
            logger.info(f"url:{url},response:{format(response.json())}")
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/detail fail")
        logger.info("message:{}".format(str(e)))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return Response(content={"message": str(e)})


def get_next_run_timestamp(
    type_code: int, value: int, start_dt: datetime, end_dt: datetime | None
):
    now = datetime.now()

    # 1. 如果当前时间已经超过end_dt，直接返回None
    if end_dt and now >= end_dt:
        return None

    # 2. 分钟级任务
    if type_code == 1:
        base = start_dt.replace(second=0, microsecond=0)
        if now <= base:
            next_dt = base
        else:
            delta_min = int((now - base).total_seconds() // 60)
            steps = delta_min // value + 1
            next_dt = base + timedelta(minutes=steps * value)
        if end_dt and next_dt > end_dt:
            return None
        return int(next_dt.timestamp() * 1000)

    elif type_code == 2:
        # 每 N 小时执行一次
        interval_hours = value
        # 总共过去的秒数
        delta_seconds = (now - start_dt).total_seconds()

        # 已过去几个完整周期
        periods_passed = math.floor(delta_seconds / (interval_hours * 3600))
        next_dt = start_dt + timedelta(hours=(periods_passed + 1) * interval_hours)

        if end_dt and next_dt > end_dt:
            return None
        return int(next_dt.timestamp() * 1000)

    # 4. 天级任务
    elif type_code == 3:
        base = start_dt.replace(second=0, microsecond=0)
        if now <= base:
            next_dt = base
        else:
            delta_day = (now.date() - base.date()).days
            steps = delta_day // value + 1
            next_dt = base + timedelta(days=steps * value)
        if end_dt and next_dt > end_dt:
            return None
        return int(next_dt.timestamp() * 1000)

    # 5. 星期任务
    elif type_code == 4:
        target_weekday = value
        today = now.isoweekday()  # 当前是星期几：1~7

        days_ahead = (target_weekday - today + 7) % 7
        next_dt = now.replace(
            hour=start_dt.hour, minute=start_dt.minute, second=0, microsecond=0
        )

        if days_ahead == 0:
            if next_dt <= now:
                next_dt += timedelta(days=7)
        else:
            next_dt += timedelta(days=days_ahead)

        if next_dt < start_dt:
            next_dt += timedelta(days=7)
        if end_dt and next_dt > end_dt:
            return None
        return int(next_dt.timestamp() * 1000)

    # 6. 单次任务
    elif type_code == 5:
        run_dt = datetime.fromtimestamp(value / 1000)
        return (
            int(run_dt.timestamp() * 1000) if (not end_dt or run_dt <= end_dt) else None
        )

    return None


def forward_post(url, authorization, params):
    headers = {"Content-Type": "application/json", "Authorization": authorization}

    try:
        response = requests.post(
            get_config_item(YN_URL) + url, json=params, headers=headers
        )
        if response.status_code != 200:
            logger.info(f"url:{url},response:{format(response.json())}")
        return response.json()
    except Exception as e:
        logger.info(f"url:{url} fail")
        logger.info("message:{}".format(str(e)))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return Response(content={"message": str(e)})


# app = FastAPI()


def load_config(config_file):
    with open(config_file, "r", encoding="utf-8") as f:
        config = json.load(f)
    return config


async def run_and_update(robot_code: str, authorization: str, task_id: str):
    now_ts = int(time.time() * 1000)
    # 执行前上传状态
    result = await run_robot_code(robot_code, authorization, task_id)


# @app.on_event("startup")
# async def startup_event():
#     load_config(args.config)


# @app.on_event("startup")
# async def startup_event():
#     load_config(args.config)


@router.get("/task/runner/start/immediately")
async def start_job_immediately(
    task_id: str = Query(...), authorization: str = Header(None)
):
    if globals.token is None:
        globals.token = authorization

    if task_id is None:
        return Response(
            status_code=404, content={"message": f"无效的task_id:{task_id}"}
        )

    await task_manager.run_task(task_id)

    return {"message": f"task_id:{task_id}"}


@router.get("/uniwim/ump/currUserInfo")
async def detail(authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(DLY_URL)}/uniwim/ump/currUserInfo"
    try:
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            user_info = parse_jwt(authorization)
            if user_info is not None:
                return {
                    "Code": 0,
                    "Message": "操作成功",
                    "Response": user_info,
                    "Success": True,
                }
            logger.info("java currUserInfo {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("currUserInfo fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/add")
async def add(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {
        "Content-Type": "application/json",
        "Authorization": authorization,  # "Bearer empty" # 如果需要授权，tokens 从params 获取
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/task/add"

    try:
        response = requests.post(url, json=items, headers=headers)
        # if response.status_code == 200:
        #     token = response.json().get("authorization")
        #     logger.info("令牌：{}".format(token))
        # else:
        #     logger.info("/wimai/api/task/config/add请求失败")
        if response.status_code != 200:
            logger.info("java /wimai/api/task/add {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/add  faile")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/delete")
async def delete(items: List[str], authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    print("delete items: ", items)
    ## 通知定时执行任务删除id
    for item in items:
        task_manager.remove_task(item)

    headers = {"Content-Type": "application/json", "Authorization": authorization}

    url = f"{get_config_item(YN_URL)}/wimai/api/task/delete"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/delete {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/delete fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


# @router.post("/wimai/api/task/delete")
# async def delete(items: List[str], authorization: str = Header(None)):
#     if globals.token is None:
#         globals.token = authorization
#     print("delete items: ", items)
#     ## 通知定时执行任务删除id
#     for item in items:
#         task_manager.remove_task(item)

#     headers = {
#         "Content-Type": "application/json",
#         "Authorization": authorization
#     }

#     url = f"{get_config_item(YN_URL)}/wimai/api/task/delete"
#     try:
#         response = requests.post(url,
#                                  json=items, headers=headers)
#         if response.status_code != 200:
#             logger.info("java /wimai/api/task/delete {}".format(response.json()))
#         return response.json()
#     except Exception as e:
#         logger.info("/wimai/api/task/delete fail")
#         logger.info("message:{}".format(str(e)))
#         return Response(content={"message": str(e)})


@router.get(TASK_DETAIL_URI)
async def detail(id: str = Query(...), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization,
    }
    # url = f"{get_config_item(YN_URL)}/wimai/api/task/detail?id={}".format(id)
    url = f"{get_config_item(YN_URL)}/wimai/api/task/detail"
    try:
        response = requests.get(url, params={"id": id}, headers=headers)

        if response.status_code != 200:
            logger.info("java /wimai/api/task/detail {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info(" /wimai/api/task/detail fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.get("/wimai/api/taskHistory/logs")
async def history_logs(id: str = Query(...), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {
        # "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/taskHistory/logs"
    try:
        response = requests.get(url, params={"id": id}, headers=headers)

        if response.status_code != 200:
            logger.info("java /wimai/api/taskHistory/logs {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/taskHistory/logs fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/query")
async def query(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/query"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/query {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/query 请求异常")
        logger.info("meassge:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/taskHistory/query")
async def query_history(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/taskHistory/query"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java wimai/api/taskHistory/query {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("wimai/api/taskHistory/query 请求异常")
        logger.info("meassge：{}".format(e))
        return Response(content={"message": e})


@router.post("/wimai/api/task/update")
async def update(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/update"
    if items.get("configContent", None):
        content = items.get("configContent").strip()
        if content:
            try:
                content = json.loads(content)
                workflowdata = WorkflowData(
                    nodes=content["nodes"],
                    edges=content["edges"],
                    viewport=content.get("viewport", None),
                    metadata=content.get("metadata", None),
                    variables=content.get("variables", None),
                )
                transpiler = WorkflowTranspiler()
                robot_code = await transpiler.transpile(workflowdata, items["id"])
                logger.info("api update 工作流转译完成")
                logger.info(f"api update 生成的Robot代码长度: {len(robot_code)} 字符")

                items["robotCode"] = robot_code
            except Exception as e:
                logger.error(f"api update <UNK>{e}")
                items["robotCode"] = None

    active = items.get("active", None)
    if active == 1:
        ### 通知任务执行器 修改相关 执行
        if items.get("robotCode", None):
            task_manager.add_or_update_task(
                task_id=items["id"],
                param=items.get("timeScheduled", None),
                workflow_json=items.get("configContent", None),
                authorization=authorization,
                start_time=items["missionStartTime"],
                end_time=items.get("missionEndTime", None),
            )
    elif active == 0:
        ## 停用通知robot
        task_manager.remove_task(items["id"])

    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/update {}".format(response.json()))
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.info("/wimai/api/task/update fail")
        logger.info("meassge：{}".format(e))
        return Response(content={"message": e})


@router.post(TASK_UPDATE_ACTIVE)
async def update_enable(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    mission_start_ts = items.get("missionStartTime")
    mission_end_ts = items.get("missionEndTime")
    start_dt = (
        datetime.fromtimestamp(mission_start_ts / 1000) if mission_start_ts else None
    )
    end_dt = datetime.fromtimestamp(mission_end_ts / 1000) if mission_end_ts else None
    next_start_time = None
    time_scheduled = items.get("timeScheduled")
    if time_scheduled and start_dt:
        try:
            type_str, value_str = time_scheduled.split(",")
            type_code = int(type_str)
            value = int(value_str)
            next_start_time = get_next_run_timestamp(type_code, value, start_dt, end_dt)
        except Exception as e:
            print(f"[计算下一次执行时间失败]: {e}")

    data = {
        "id": items["id"],
        "active": items["active"],
        "nextStartTime": next_start_time,
    }
    active = items.get("active", None)

    if active == 1:
        ### 通知任务执行器 修改相关 执行
        task = forward_get(TASK_DETAIL_URI, authorization, {"id": items["id"]}).get(
            "Response", {}
        )
        task_manager.add_or_update_task(
            task_id=items["id"],
            param=task.get("timeScheduled", None),
            workflow_json=task["configContent"],
            authorization=authorization,
            start_time=task["missionStartTime"],
            end_time=task.get("missionEndTime", None),
        )
    elif active == 0:
        ## 停用通知robot
        task_manager.remove_task(items["id"])

    return forward_post(TASK_UPDATE_ACTIVE, authorization, data)


@router.post(TASK_BASE_INFO)
async def update_base_info(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    task = forward_get(TASK_DETAIL_URI, authorization, {"id": items["id"]}).get(
        "Response", {}
    )

    active = task.get("active", None)

    mission_start_ts = items.get("missionStartTime")
    mission_end_ts = items.get("missionEndTime")
    start_dt = (
        datetime.fromtimestamp(mission_start_ts / 1000) if mission_start_ts else None
    )
    end_dt = datetime.fromtimestamp(mission_end_ts / 1000) if mission_end_ts else None

    next_start_time = None
    time_scheduled = items.get("timeScheduled")
    if time_scheduled and start_dt:
        try:
            type_str, value_str = time_scheduled.split(",")
            type_code = int(type_str)
            value = int(value_str)
            next_start_time = get_next_run_timestamp(type_code, value, start_dt, end_dt)
        except Exception as e:
            print(f"[计算下一次执行时间失败]: {e}")

    # 激活的任务，需求重新修改下定时信息
    if active == 1:
        task_manager.remove_task(task_id=items["id"])
        task_manager.add_or_update_task(
            task_id=items["id"],
            param=items.get("timeScheduled", None),
            workflow_json=items["configContent"],
            authorization=authorization,
            start_time=task["missionStartTime"],
            end_time=task.get("missionEndTime", None),
        )

    data = {
        "id": items["id"],
        "missionName": items["missionName"],
        "timeScheduled": items["timeScheduled"],
        "missionStartTime": items["missionStartTime"],
        "missionEndTime": items["missionEndTime"],
        "reportUser": items["reportUser"],
        "reportUserName": items["reportUserName"],
        "nextStartTime": next_start_time,
    }

    if items["execution"] == 5:
        data["missionStartTime"] = None
        data["missionEndTime"] = None
        data["nextStartTime"] = items["specify"]

    return forward_post(TASK_BASE_INFO, authorization, data)


@router.post(TASK_UPDATE_ROBOT)
async def update_robot_code(
    items: Dict = Body(None), authorization: str = Header(None)
):
    if globals.token is None:
        globals.token = authorization
    if items.get("configContent", None):
        content = items.get("configContent").strip()
        if content:
            try:
                content = json.loads(content)
                workflowdata = WorkflowData(
                    nodes=content["nodes"],
                    edges=content["edges"],
                    viewport=content.get("viewport", None),
                    metadata=content.get("metadata", None),
                    variables=content.get("variables", None),
                )
                transpiler = WorkflowTranspiler()
                robot_code = await transpiler.transpile(workflowdata, items["id"])
                logger.info("api update 工作流转译完成")
                logger.info(f"api update 生成的Robot代码长度: {len(robot_code)} 字符")

                items["robotCode"] = robot_code
            except Exception as e:
                logger.error(f"api update <UNK>{e}")
                items["robotCode"] = None

    task = forward_get(TASK_DETAIL_URI, authorization, {"id": items["id"]}).get(
        "Response", {}
    )
    active = task.get("active", None)
    if active == 1:
        if items.get("robotCode", None):
            task_manager.add_or_update_task(
                task_id=items["id"],
                param=task.get("timeScheduled", None),
                workflow_json=items["configContent"],
                authorization=authorization,
                start_time=task["missionStartTime"],
                end_time=task.get("missionEndTime", None),
            )

    content = json.loads(items.get("configContent").strip())
    data = {
        "id": items["id"],
        "missionName": items["missionName"],
        "robotCode": items["robotCode"],
        "configContent": items["configContent"],
        "nodeNum": len(content["nodes"]),
    }
    return forward_post(TASK_UPDATE_ROBOT, authorization, data)


@router.post("/wimai/api/task/config/add")
async def task_config_add(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    print("items: ", items)
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/add"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/add {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/add fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/config/batchSaveOrUpdate")
async def task_config_batchupdate(items: List[Dict], authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/batchSaveOrUpdate"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info(
                "java /wimai/api/task/config/batchSaveOrUpdate {}".format(
                    response.json()
                )
            )
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/batchSaveOrUpdate fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post(TEMPLATE_CRUD)
async def templae_crud(req: TemplateCrud, authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    if req.method == "get":
        resp = forward_get(req.url, authorization, req.query_param)
    else:
        resp = forward_post(req.url, authorization, req.body_param)

    if resp.get("Code", -1) == 0:
        if (
            req.url == "/wimai/api/task/config/add"
            or req.url == "/wimai/api/task/config/update"
        ):
            param = req.body_param
            config[param.get("code", "")] = config[param.get("value", "")]
        elif req.url == "/wimai/api/task/config/delete":
            items = resp.get("Response", [])
            codes = [d["name"] for d in items]
            for code in codes:
                config.pop(code, None)
    return resp


@router.post("/wimai/api/task/config/delete")
async def task_config_delete(items: List[str], authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}

    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/delete"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/delete {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/delete fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/config/query")
async def task_config_query(
    items: Dict = Body(None), authorization: str = Header(None)
):

    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/query"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/query {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/query fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/wimai/api/task/config/update")
async def task_config_update(
    items: Dict = Body(None), authorization: str = Header(None)
):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/update"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/update {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/update fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.get("/wimai/api/task/config/detail")
async def config_detail(id: str = Query(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    print("id: ", id)
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization,
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/detail"
    try:
        response = requests.get(url, params={"id": id}, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/detail {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/detail fail")
        logger.info("message:{}".format(str(e)))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return Response(content={"message": str(e)})


@router.get("/wimai/api/task/config/list")
async def config_list(authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization,
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/task/config/list"
    try:
        response = requests.get(url, headers=headers)
        # data=items["params"],  headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/list {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/list fail")
        logger.info("message:{}".format(str(e)))
        raise HTTPException(status_code=500, detail=f"task config list异常: {str(e)}")

@router.get("/wimai/api/task/config/getByCode")
async def config_list(code: str = Query(None),authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization,
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/task/getByCode?code={code}"
    try:
        response = requests.get(url, headers=headers)
        # data=items["params"],  headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/config/getByCode {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/config/getByCode fail")
        logger.info("message:{}".format(str(e)))
        raise HTTPException(status_code=500, detail=f"task config getByCode: {str(e)}")


@router.post("/wimai/api/task/excLogs/query")
async def exclogs_query(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    logger.info("request /wimai/api/task/excLogs/query")
    headers = {
        "Content-Type": "application/json",
        "Authorization": authorization,  # "Bearer empty" # 如果需要授权，tokens 从params 获取
    }
    url = f"{get_config_item(YN_URL)}/wimai/api/task/excLogs/query"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/excLogs/query {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/excLogs/query fail")
        logger.info("message:{}".format(str(e)))
        # return Response(content={"message": e})
        raise HTTPException(status_code=500, detail=f"节点执行日志异常: {str(e)}")


@router.get("/wimai/api/task/excLogs/file")
async def exclogs_query(id: str = Query(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    exc_log = forward_get("/wimai/api/task/excLogs/detail", authorization, {"id": id})[
        "Response"
    ]
    if not exc_log:
        raise HTTPException(status_code=404, detail="File not found")
    output_str = exc_log["output"]
    if exc_log["nodeId"] in ["excel_create", "excel_write"] and output_str:
        output = json.loads(output_str)
        directory_path = output["file_path"]
        directory_path = (
            directory_path
            if directory_path.endswith("/") or directory_path.endswith("\\")
            else f"{directory_path}/"
        )
        return FileResponse(
            path=f"{directory_path}{output['file_name']}.xlsx",
            filename=f"{output['file_name']}.xlsx",  # 自定义下载文件名
            media_type="application/octet-stream",
        )
    raise HTTPException(status_code=400, detail="File not Supported")


@router.get("/wimai/api/task/excLogs/detail")
async def exclogs_detail(id: str = Query(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    logger.info("调用/wimai/api/task/excLogs/detail")
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": authorization,
    }

    url = f"{get_config_item(YN_URL)}/wimai/api/task/excLogs/detail"
    try:
        response = requests.get(url, params={"id": id}, headers=headers)
        if response.status_code != 200:
            logger.info(
                "java /wimai/api/task/excLogs/detail {}".format(response.json())
            )
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/excLogs/detail fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/sys/user/query.json")
async def user_query(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(DLY_URL)}/sys/user/query.json"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /sys/user/query.json {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/sys/user/query.json fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.post("/sys/user/groupOfUser.json")
async def group_user(items: Dict = Body(None), authorization: str = Header(None)):
    if globals.token is None:
        globals.token = authorization
    headers = {"Content-Type": "application/json", "Authorization": authorization}
    url = f"{get_config_item(DLY_URL)}/uniwim/ump/user/orgTree"
    try:
        response = requests.post(url, json=items, headers=headers)
        if response.status_code != 200:
            logger.info("java /uniwim/ump/user/orgTree {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/uniwim/ump/user/orgTree fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


# @router.post("/wimai/api/task/execute", response_model=ExecutionResponse)
# async def execute_task_by_id(id: str = Query(..., description="任务ID"),
#                              authorization: str = Header(None)):
#     new_cache = take_task(authorization)
#     match = next((item for item in new_cache if item["id"] == id), None)
#     if not match:
#         raise HTTPException(status_code=404, detail=f"未找到ID为 {id} 的任务")
#
#     robot_code = match["robotCode"]
#
#     try:
#         response: ExecutionResponse = await run_robot_code(robot_code, authorization)
#         return response
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"任务执行异常: {str(e)}")


@router.post("/wimai/api/task/execute", response_model=ExecutionResponse)
async def execute_task_by_id(
    id: str = Query(..., description="任务ID"), authorization: str = Header(None)
):
    if globals.token is None:
        globals.token = authorization
    new_cache = take_task(authorization)
    match = next((item for item in new_cache if item["id"] == id), None)
    if not match:
        raise HTTPException(status_code=404, detail=f"未找到ID为 {id} 的任务")
    if match.get("configContent", None):
        content = match.get("configContent").strip()
        if content:
            # 记录最后一次执行的时间
            now_ts = int(datetime.now().timestamp() * 1000)
            upload_result = upload_task_update(
                authorization=authorization, task_id=id, last_execute_time=now_ts
            )
            logger.info(f"立即执行任务 {id} 执行前信息已上传，结果: {upload_result}")
            try:
                content = json.loads(content)
                workflowdata = WorkflowData(
                    nodes=content["nodes"],
                    edges=content["edges"],
                    viewport=content.get("viewport", None),
                    metadata=content.get("metadata", None),
                    variables=content.get("variables", None),
                )
                transpiler = WorkflowTranspiler()
                robot_code = await transpiler.transpile(workflowdata, match["id"])
                logger.info("api update 工作流转译完成")
                logger.info(f"api update 生成的Robot代码长度: {len(robot_code)} 字符")

                executor = TaskExecutor(False, True)

                execute_param = ExecuteParam(
                    robot_code=robot_code, task_id=id, token=authorization, options={}
                )
                execution_id = create_execution_id()
                execute_param.execution_id = execution_id

                def callback(data: dict):
                    ns = NodeStatus(
                        node_id=data.get("nodeId", ""),
                        node_type=data.get("nodeType", ""),
                        node_name=data.get("title", ""),
                        history_id=data.get("historyId", ""),
                        task_id=data.get("missionId", ""),
                        describe=data.get("describe", ""),
                    )

                    if data.get("end", "0") == "1":
                        ns.flow_end(
                            state=data.get("state", ""),
                            msg=data.get("exception", ""),
                            errors=data.get("errors", []),
                        )
                    else:
                        state = data.get("state", "unknown")
                        if state == "failed":
                            ns.failed(data.get("exception", ""))
                        elif state == "progress":
                            ns.progress(
                                inputs=data.get("input", {}),
                                outputs=data.get("output", {}),
                            )
                        elif state == "passed":
                            ns.done(
                                inputs=data.get("input", {}),
                                outputs=data.get("output", {}),
                            )

                    ws_broadcast_proxy.broadcast_data(ns.to_dict())

                execute_param.step_end_callback = callback
                asyncio.create_task(executor.execute_in_queue(execute_param))
                # 记录最后一次执行的时间
                now_ts = int(datetime.now().timestamp() * 1000)
                upload_result = upload_task_update(
                    authorization=authorization, task_id=id, last_execute_time=now_ts
                )
                logger.info(
                    f"立即执行任务 {id} 执行前信息已上传，结果: {upload_result}"
                )
                return ExecutionResponse(
                    success=True,
                    execution_id=execution_id,
                    message="任务已开始执行（后台运行）",
                    result=None,
                    error=None,
                )
            except Exception as e:
                logger.error("api update 工作流转译异常")
                logger.info("message:{}".format(str(e)))
                return ExecutionResponse(
                    success=False,
                    execution_id=id,
                    message="工作流转换异常,请确认工作流配置是否正确",
                    result=None,
                    error=str(e),
                )

    return ExecutionResponse(
        success=False,
        execution_id=id,
        message="执行失败，请保存任务编排后再执行",
        result=None,
        error=None,
    )


@router.post("/chat/completions")
async def task_config_query(items: Dict = Body(None)):
    headers = {"Content-Type": "application/json", "Authorization": "Bearer none"}
    url = f"{get_config_item(AI_RUL)}/v1/chat/completions"
    try:
        response = requests.post(url, json=items, headers=headers)
        content_after = ""
        if response.status_code == 200:
            content = response.json()["choices"][0]["message"]["content"]
            split_token = "</think>"
            if split_token in content:
                content_after = content.split(split_token, 1)[1].strip()
            else:
                content_after = content.strip()
        return content_after
    except Exception as e:
        logger.info("/wimai/api/task/config/query fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})


@router.get("/wimai/api/task/excFile/download")
async def proxy_file_download(
    execution_id: str = Query(..., alias="executionId"),  # 必传
    node_id: Optional[str] = Query(None, alias="nodeId"),  # 可选
    authorization: Optional[str] = Header(None),
):
    headers = {
        "Authorization": authorization,
        "Content-Type": "application/x-www-form-urlencoded",
    }

    # 只构造实际存在的参数
    params = {
        "executionId": execution_id,
    }
    if node_id:
        params["nodeId"] = node_id

    url = f"{get_config_item(YN_URL)}/wimai/api/task/excFile/download"

    try:
        resp = requests.get(url, headers=headers, params=params, stream=True)

        return StreamingResponse(
            resp.raw,
            media_type=resp.headers.get("Content-Type", "application/octet-stream"),
            headers={
                "Content-Disposition": resp.headers.get(
                    "Content-Disposition", "attachment; filename=file.xlsx"
                )
            },
        )
    except Exception as e:
        return {"error": str(e)}


@router.get(PROCESS_LIST)
async def process_list(authorization: Optional[str] = Header(None)):
    headers = {
        "Authorization": authorization,
        "Content-Type": "application/x-www-form-urlencoded",
    }
    user_info = parse_jwt(authorization)
    # 获取角色列表roles，用逗号拼接
    roles = ",".join(user_info["roles"])
    params = {"roles": roles, "state": 1}
    url = f"{get_config_item(BPM_URL)}/{PROCESS_LIST}"

    try:
        response = requests.post(url, json=params, headers=headers)
        if response.status_code != 200:
            logger.info(f"java {PROCESS_LIST}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info(f"{PROCESS_LIST} fail")
        logger.info("message:{}".format(str(e)))
        return Response(content={"message": str(e)})

class PathsRequest(BaseModel):
    paths: List[str]
@router.post("/wimai/api/excel_info")
async def excel_info(request: PathsRequest):
    if not request.paths:
        raise HTTPException(status_code=400, detail="paths不能为空")
    data = get_excel_info_from_paths(request.paths)
    return {"files": data}
