"""
Workflow-Use 配置管理
管理workflow-use集成的配置选项
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from loguru import logger


class WorkflowUseConfig:
    """Workflow-Use配置管理器"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径，默认为当前目录下的config.json
        """
        if config_file is None:
            config_file = Path(__file__).parent / "config.json"

        self.config_file = Path(config_file)
        self._config = self._load_default_config()
        self._load_config()

    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "enabled": False,  # 默认禁用，需要手动启用
            "ai_fallback": False,  # 默认禁用AI回退，符合用户偏好
            "browser": "chromium",  # 默认浏览器
            "recording": {
                "auto_screenshot": True,
                "smart_selectors": True,
                "element_highlighting": True,
                "timeout": 30000,
            },
            "execution": {
                "deterministic_only": True,  # 仅使用确定性执行
                "fast_replay": True,
                "self_healing": False,  # 暂不启用自愈功能
                "max_retries": 3,
            },
            "integration": {
                "replace_browser_recording": False,  # 是否完全替换原有录制
                "compatibility_mode": True,  # 兼容模式，保持原有API
                "fallback_to_original": True,  # 失败时回退到原有实现
            },
            "logging": {"level": "INFO", "enable_debug": False},
        }

    def _load_config(self):
        """从文件加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, "r", encoding="utf-8") as f:
                    file_config = json.load(f)

                # 合并配置（文件配置覆盖默认配置）
                self._merge_config(self._config, file_config)
                logger.info(f"配置已从文件加载: {self.config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self._save_config()  # 保存默认配置到文件

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")

    def _merge_config(self, base: Dict, override: Dict):
        """递归合并配置"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value

    def _save_config(self):
        """保存配置到文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split(".")
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set(self, key: str, value: Any, save: bool = True):
        """设置配置值"""
        keys = key.split(".")
        config = self._config

        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        # 设置值
        config[keys[-1]] = value

        if save:
            self._save_config()

        logger.info(f"配置已更新: {key} = {value}")

    def is_enabled(self) -> bool:
        """检查workflow-use是否启用"""
        return self.get("enabled", False)

    def enable(self, save: bool = True):
        """启用workflow-use"""
        self.set("enabled", True, save)
        logger.info("Workflow-Use已启用")

    def disable(self, save: bool = True):
        """禁用workflow-use"""
        self.set("enabled", False, save)
        logger.info("Workflow-Use已禁用")

    def is_ai_fallback_enabled(self) -> bool:
        """检查AI回退是否启用"""
        return self.get("ai_fallback", False)

    def enable_ai_fallback(self, save: bool = True):
        """启用AI回退"""
        self.set("ai_fallback", True, save)
        logger.info("AI回退已启用")

    def disable_ai_fallback(self, save: bool = True):
        """禁用AI回退"""
        self.set("ai_fallback", False, save)
        logger.info("AI回退已禁用")

    def get_recording_config(self) -> Dict[str, Any]:
        """获取录制配置"""
        return {
            "browser": self.get("browser", "chromium"),
            "auto_screenshot": self.get("recording.auto_screenshot", True),
            "smart_selectors": self.get("recording.smart_selectors", True),
            "element_highlighting": self.get("recording.element_highlighting", True),
            "timeout": self.get("recording.timeout", 30000),
        }

    def get_execution_config(self) -> Dict[str, Any]:
        """获取执行配置"""
        return {
            "deterministic_only": self.get("execution.deterministic_only", True),
            "fast_replay": self.get("execution.fast_replay", True),
            "self_healing": self.get("execution.self_healing", False),
            "max_retries": self.get("execution.max_retries", 3),
        }

    def get_integration_config(self) -> Dict[str, Any]:
        """获取集成配置"""
        return {
            "replace_browser_recording": self.get(
                "integration.replace_browser_recording", False
            ),
            "compatibility_mode": self.get("integration.compatibility_mode", True),
            "fallback_to_original": self.get("integration.fallback_to_original", True),
        }

    def should_replace_browser_recording(self) -> bool:
        """是否应该完全替换原有的浏览器录制"""
        return self.is_enabled() and self.get(
            "integration.replace_browser_recording", False
        )

    def should_fallback_to_original(self) -> bool:
        """是否应该在失败时回退到原有实现"""
        return self.get("integration.fallback_to_original", True)

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()

    def reset_to_default(self, save: bool = True):
        """重置为默认配置"""
        self._config = self._load_default_config()
        if save:
            self._save_config()
        logger.info("配置已重置为默认值")

    def update_from_env(self):
        """从环境变量更新配置"""
        env_mappings = {
            "WORKFLOW_USE_ENABLED": "enabled",
            "WORKFLOW_USE_AI_FALLBACK": "ai_fallback",
            "WORKFLOW_USE_BROWSER": "browser",
            "WORKFLOW_USE_REPLACE_RECORDING": "integration.replace_browser_recording",
        }

        for env_key, config_key in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 转换布尔值
                if env_value.lower() in ("true", "1", "yes", "on"):
                    value = True
                elif env_value.lower() in ("false", "0", "no", "off"):
                    value = False
                else:
                    value = env_value

                self.set(config_key, value, save=False)
                logger.info(f"从环境变量更新配置: {config_key} = {value}")


# 全局配置实例
workflow_use_config = WorkflowUseConfig()
