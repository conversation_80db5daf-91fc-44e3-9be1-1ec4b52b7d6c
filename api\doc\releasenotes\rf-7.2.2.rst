=====================
Robot Framework 7.2.2
=====================

.. default-role:: code

`Robot Framework`_ 7.2.2 is the second and the last planned bug fix release
in the Robot Framework 7.2.x series. It fixes a mistake made when releasing
`Robot Framework 7.2.1 <rf-7.2.1.rst>`_.

Questions and comments related to the release can be sent to the `#devel`
channel on `Robot Framework Slack`_ and possible bugs submitted to
the `issue tracker`_.

If you have pip_ installed, just run

::

   pip install --pre --upgrade robotframework

to install the latest available release or use

::

   pip install robotframework==7.2.2

to install exactly this version. Alternatively you can download the package
from PyPI_ and install it manually. For more details and other installation
approaches, see the `installation instructions`_.

Robot Framework 7.2.2 was released on Friday February 7, 2025.

.. _Robot Framework: http://robotframework.org
.. _Robot Framework Foundation: http://robotframework.org/foundation
.. _pip: http://pip-installer.org
.. _PyPI: https://pypi.python.org/pypi/robotframework
.. _issue tracker milestone: https://github.com/robotframework/robotframework/issues?q=milestone%3Av7.2.2
.. _issue tracker: https://github.com/robotframework/robotframework/issues
.. _robotframework-users: http://groups.google.com/group/robotframework-users
.. _Slack: http://slack.robotframework.org
.. _Robot Framework Slack: Slack_
.. _installation instructions: ../../INSTALL.rst

.. contents::
   :depth: 2
   :local:

Acknowledgements
================

Robot Framework development is sponsored by the `Robot Framework Foundation`_
and its over 70 member organizations. If your organization is using Robot Framework
and benefiting from it, consider joining the foundation to support its development
as well.

Big thanks to the Foundation and to everyone who has submitted bug reports, debugged
problems, or otherwise helped with Robot Framework development.

| `Pekka Klärck <https://github.com/pekkaklarck>`_
| Robot Framework lead developer

Full list of fixes and enhancements
===================================

.. list-table::
    :header-rows: 1

    * - ID
      - Type
      - Priority
      - Summary
    * - `#5329`_
      - bug
      - medium
      - New Libdoc language selection button does not work well on mobile

Altogether 1 issue. View on the `issue tracker <https://github.com/robotframework/robotframework/issues?q=milestone%3Av7.2.2>`__.

.. _#5329: https://github.com/robotframework/robotframework/issues/5329
