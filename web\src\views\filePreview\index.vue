<template>
  <div class="preview-wrapper">
    <div ref="univerContainer" class="univer-container">
      <span class="loading-text" v-if="loading">初始化中，请稍候</span>
      <span class="loading-text" v-else-if="!templateConfig">模板获取失败</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
// @ts-ignore
import saasApi from '@/api/index'
// @ts-ignore
import utils from '@/utils/utils'

// Props
interface Props {
  configId?: string
  config?: string
  missionId?: string
  id?: string
  historyId?: string
}

const props = withDefaults(defineProps<Props>(), {})

// Univer imports
import { createUniver, LocaleType, merge } from '@univerjs/presets'
import { defaultTheme } from '@univerjs/core'

import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsDrawingPreset } from '@univerjs/presets/preset-sheets-drawing'
import sheetsDrawingZhCN from '@univerjs/presets/preset-sheets-drawing/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-drawing.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsHyperLinkPreset } from '@univerjs/presets/preset-sheets-hyper-link'
import sheetsHyperLinkZhCN from '@univerjs/presets/preset-sheets-hyper-link/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-hyper-link.css'

// Univer容器引用
const univerContainer = ref<HTMLElement>()
// Univer 实例
let univerInstance: any = null
let univerAPIInstance: any = null

// 初始化univer配置
const initUniverConfig = (container, extendsConfig = {}) => {
  return createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {},
        UniverPresetSheetsCoreZhCN,
        sheetsConditionalFormattingZhCN,
        sheetsDataValidationZhCN,
        sheetsDrawingZhCN,
        sheetsFilterZhCN,
        sheetsHyperLinkZhCN,
      ),
    },
    theme: defaultTheme,
    presets: [
      UniverSheetsCorePreset({
        container: container,
        ...extendsConfig,
      }),
      UniverSheetsConditionalFormattingPreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsDrawingPreset(),
      UniverSheetsFilterPreset(),
      UniverSheetsHyperLinkPreset(),
    ],
  })
}

// 加载模板列表
const loadTemplateList = async () => {
  let templateList = []
  try {
    const response = await fetch('http://localhost:39876/api/report-templates/', {
      method: 'GET',
      headers: {
        Authorization: utils.GetAuthorization(),
      },
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const templates = await response.json()
    templateList = templates.map((template: any) => ({
      ...template,
      createTime: new Date(template.create_time).toLocaleString(),
    }))
  } catch (error) {
    templateList = []
  }
  return templateList
}

const loading = ref(false)
const templateConfig = ref(null)

// 初始化 Univer
const initializeDesigner = () => {
  return new Promise(async (resolve, reject) => {
    if (!univerContainer.value || univerInstance) return reject()
    let config = props.config ? JSON.parse(props.config) : null
    // 如果不存在config但有configId，则从接口获取config配置
    if (!config && props.configId) {
      const templateList = await loadTemplateList()
      const template = templateList.find((item: any) => item.id === props.configId)
      if (template) {
        config = template
      } else {
        return reject(new Error('Template not found'))
      }
    }
    try {
      // 存在则销毁掉
      if(univerInstance || univerAPIInstance) {
        const workbook = univerAPIInstance.getActiveWorkbook()
        // 销毁工作簿
        const unitId = workbook?.getId();
        if(unitId) {
          univerAPIInstance.disposeUnit(unitId)
        }
      }
      // 不存在则创建
      else {
        const { univer, univerAPI } = initUniverConfig(univerContainer.value, {
          header: false,
          toolbar: false,
          footer: false,
          contextMenu: false,
        })

        univerInstance = univer
        univerAPIInstance = univerAPI
      }

      // 步骤1 todo 需要替换成真实的模板数据获取接口
      const sheetData = config.data

      // 步骤2 特殊处理，将返回的模板数据中的sheets对象中每一个sheet中的cellData对象下的v值清空
      Object.keys(sheetData.sheets).forEach((sheetKey) => {
        const sheet = sheetData.sheets[sheetKey]
        if (sheet.cellData) {
          Object.keys(sheet.cellData).forEach((rowKey) => {
            const row = sheet.cellData[rowKey]
            Object.keys(row).forEach((cellKey) => {
              const cell = row[cellKey]
              if (cell && typeof cell === 'object' && 'v' in cell && typeof cell.v === 'string') {
                // 替换 ${xxx} 和 #{xxx} 格式的内容为空字符串
                cell.v = cell.v.replace(/\$\{[^}]+}/g, '').replace(/#\{[^}]+}/g, '')
              }
            })
          })
        }
      })
      // 步骤3 创建默认工作簿
      univerAPIInstance.createWorkbook(sheetData)

      // 步骤4 禁用当前工作表操作权限
      const previewUnitId = univerAPIInstance.getActiveWorkbook()?.getId()
      const permission = univerAPIInstance.getPermission()
      const workbookEditablePermission =
        permission.permissionPointsDefinition.WorkbookEditablePermission
      permission.setWorkbookPermissionPoint(previewUnitId, workbookEditablePermission, false)
      // 步骤5 移除权限确认框
      permission.setPermissionDialogVisible(false)

      resolve(config)
    } catch (error) {
      reject(error)
    }
  })
}

// 监听config配置，如果config配置发生变化，需要更新表格
watch(()=> props.config, ()=>{
  initializeDesigner()
    .then((config) => {
      templateConfig.value = config
      fetchExcelFile()
    })
    .catch(() => {
      templateConfig.value = null
    })
    .finally(() => {
      isFetching = false
    })
})

// 更新单元格值
const updateCellValue = (cell: string, value: string) => {
  if (!univerAPIInstance) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const worksheet = workbook.getActiveSheet()
    const range = worksheet.getRange(cell)
    // 允许当前工作表操作权限
    const previewUnitId = univerAPIInstance.getActiveWorkbook()?.getId()
    const permission = univerAPIInstance.getPermission()
    const workbookEditablePermission =
      permission.permissionPointsDefinition.WorkbookEditablePermission
    permission.setWorkbookPermissionPoint(previewUnitId, workbookEditablePermission, true)
    // 设置值
    range.setValue(value)
    // 禁用当前工作表操作权限
    permission.setWorkbookPermissionPoint(previewUnitId, workbookEditablePermission, false)
  } catch (error) {
    console.warn('更新单元格值失败:', error)
  }
}

watch(
  () => props.historyId,
  () => {
    errorTime.value = 0
    fetchExcelFile()
  },
)

// 定时器变量
let fetchTimer: ReturnType<typeof setTimeout> | null = null
let isFetching = false
const errorTime = ref(0)
const fetchExcelFile = async () => {
  if (!props.historyId || isFetching || errorTime.value >= 10) return
  isFetching = true

  try {
    // 步骤1 请求 AIAgentMissionLogFile 接口，设置 responseType 为 arraybuffer
    const responseData = await saasApi.AIAgentMissionExcFileFile(props.historyId)

    if (responseData instanceof ArrayBuffer) {
      const jsonString = new TextDecoder('utf-8').decode(responseData)
      try {
        const jsonData = JSON.parse(jsonString)
        if (jsonData && jsonData.Code) {
          errorTime.value++
          isFetching = false
          return setTimeout(()=>{
            fetchExcelFile()
          }, 3000)
        }else{
          errorTime.value = 0
        }
      } catch (error) {
        errorTime.value = 0
      }
    }

    // 步骤2 创建 Blob 对象
    const blob = new Blob([responseData], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    // 步骤3 读取 Blob 对象
    const reader = new FileReader()
    reader.onload = async (e) => {
      if (e.target?.result) {
        // 解析文件内容为 Excel
        const workbook = XLSX.read(e.target.result, { type: 'array' })
        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        console.warn({ worksheet })
        // 步骤4 将数据写入的到单元格中（不做XLSX转换数据）
        const keys = Object.keys(worksheet).filter((key) => !key.startsWith('!'))
        // 按列分组
        const columns: Record<string, string[]> = {}
        keys.forEach((key) => {
          // 修改正则表达式以匹配多字母列标识
          const col = key.match(/^[A-Z]{1,3}/)?.[0] || '' // 最多支持到ZZZ列
          if (!columns[col]) {
            columns[col] = []
          }
          columns[col].push(key)
        })

        // 按列循环处理
        const sortedColumns = Object.keys(columns).sort((a, b) => {
          // 比较列字母长度，短的在前
          if (a.length !== b.length) return a.length - b.length
          // 长度相同则按字母顺序比较
          return a.localeCompare(b)
        })

        for (const col of sortedColumns) {
          const colKeys = columns[col]
          for (let i = 0; i < colKeys.length; i++) {
            const key = colKeys[i]
            await new Promise((resolve) => setTimeout(resolve, 50))
            updateCellValue(key, worksheet[key]['v'])
          }
        }
      }
    }
    reader.onerror = () => {
      ElMessage.error('读取文件失败')
    }
    reader.readAsArrayBuffer(blob)
  } catch (error) {
    console.error('请求文件失败:', error)
    ElMessage.error('请求文件失败')
  } finally {
    isFetching = false
    // 请求完成后设置1分钟定时器
    // fetchTimer = setTimeout(() => {
    //   fetchExcelFile()
    // }, 60000) // 1分钟 = 60000毫秒
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (fetchTimer) {
    clearTimeout(fetchTimer)
    fetchTimer = null
  }
})

onMounted(() => {
  loading.value = true
  initializeDesigner()
    .then((config) => {
      templateConfig.value = config
      fetchExcelFile()
    })
    .catch(() => {
      templateConfig.value = null
    })
    .finally(() => {
      isFetching = false
    })
})
</script>

<style scoped lang="scss">
.preview-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .univer-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid #e4e7ed;
    border-radius: 0 0 4px 4px;
    min-height: 300px;
    flex: 1;
    position: relative;
    .loading-text{
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #909399;
      font-size: 14px;
    }
  }
}
</style>
