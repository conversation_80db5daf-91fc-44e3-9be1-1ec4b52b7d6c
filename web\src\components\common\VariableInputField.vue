<template>
  <div class="variable-input-field">
    <!-- 普通字符串输入 -->
    <div
      v-if="fieldConfig.type === 'string'"
      class="input-with-variable"
      :class="{
        'input-with-prefix':
          fieldConfig.prefix &&
          typeof fieldConfig.prefix === 'object' &&
          fieldConfig.prefix.options,
      }"
    >
      <el-input
        :model-value="modelValue"
        @update:model-value="handleInput"
        :placeholder="fieldConfig.placeholder"
        :disabled="disabled"
        ref="inputRef"
      >
        <template v-if="fieldConfig.prefix" #prefix>
          <el-select
            :style="{ width: `${fieldConfig.prefix.width || '90px'}` }"
            class="input-prefix-select"
            @change="handlePrefix"
            popper-class="input-prefix-select-popper"
            v-if="typeof fieldConfig.prefix === 'object' && fieldConfig.prefix.options"
            v-model="allValues[fieldConfig.prefix.key || `${fieldName}_method`]"
          >
            <el-option
              v-for="item in fieldConfig.prefix.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span v-else>{{ fieldConfig.prefix }}</span>
        </template>
        <template #suffix>
          {{ fieldConfig.suffix }}
        </template>
        <template #append v-if="fieldConfig.append || fieldConfig.variableSupport">
          <div class="variable-input-append">
            <el-select
              v-if="fieldConfig.append"
              :style="{ width: '100px' }"
              class="input-prefix-select"
              @change="handleAppend"
              :clearable="fieldConfig.append.clearable"
              :placeholder="fieldConfig.append.placeholder"
              popper-class="input-prefix-select-popper"
              v-model="allValues[fieldConfig.append.key || `extend_format`]"
            >
              <template #header v-if="fieldConfig.append.header">
                <div class="select-custom-header">{{ fieldConfig.append.header }}</div>
              </template>
              <el-option
                v-for="item in fieldConfig.append.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button
              v-if="fieldConfig.variableSupport"
              type="primary"
              @click="showVariablePicker"
              class="variable-button"
              link
              :icon="MagicStick"
              title="插入变量"
            ></el-button>
          </div>
        </template>
      </el-input>
    </div>

    <!-- 多行文本输入 -->
    <div v-else-if="fieldConfig.type === 'textarea'" class="textarea-with-variable">
      <div v-if="fieldConfig.variableSupport" class="variable-actions">
        <el-button
          @click="showVariablePicker"
          :icon="MagicStick"
          type="primary"
          link
          class="variable-button"
          size="small"
        >
          插入变量
        </el-button>
      </div>
      <el-input
        type="textarea"
        :model-value="modelValue"
        @update:model-value="handleInput"
        :placeholder="fieldConfig.placeholder"
        :rows="fieldConfig.rows || 3"
        :disabled="disabled"
        ref="textareaRef"
      />
    </div>

    <!-- JSON编辑器 -->
    <div v-else-if="fieldConfig.type === 'json'" class="json-with-variable">
      <div class="variable-actions">
        <el-button
          type="primary"
          v-if="fieldConfig.variableSupport"
          link
          @click="showVariablePicker"
          :icon="MagicStick"
          class="variable-button"
          size="small"
        >
          插入变量</el-button
        >
        <el-button @click="formatJson" :icon="Document" class="format-button" size="small">
          格式化
        </el-button>
      </div>
      <el-input
        type="textarea"
        :model-value="modelValue"
        @update:model-value="handleInput"
        :placeholder="fieldConfig.placeholder"
        :rows="fieldConfig.rows || 6"
        :disabled="disabled"
        ref="jsonRef"
      />
    </div>

    <!-- 文件夹选择 -->
    <div v-else-if="fieldConfig.type === 'folder'" class="folder-field">
      <div v-if="fieldConfig.variableSupport" class="variable-actions">
        <el-button
          @click="showVariablePicker"
          :icon="MagicStick"
          type="primary"
          link
          class="variable-button"
          size="small"
        >
          插入变量
        </el-button>
      </div>
      <el-input
        :model-value="modelValue"
        @update:model-value="handleInput"
        :placeholder="fieldConfig.placeholder || '选择文件夹'"
        :readonly="fieldConfig.readonly"
      >
        <template #append>
          <el-button @click="selectFolder">
            <el-icon>
              <FolderOpened />
            </el-icon>
            选择文件夹
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 可新增多个变量 -->
    <div
      class="add-variable-item"
      v-else-if="fieldConfig.type === 'stringArray'"
      v-for="(item, index) in addVariableDatas"
      :key="'addVariable' + index"
    >
      <el-input
        :model-value="addVariableDatas[index]"
        @update:model-value="handleVariableUpdate(index, $event)"
        :placeholder="fieldConfig.placeholder"
        :readonly="fieldConfig.readonly"
        ref="inputArrayRef"
        clearable
      >
        <template v-if="fieldConfig.prefix" #prefix>
          <span>{{ fieldConfig.prefix }}</span>
        </template>
        <template v-if="fieldConfig.suffix" #suffix>
          <span>{{ fieldConfig.suffix }}</span>
        </template>
        <template #append v-if="fieldConfig.variableSupport">
          <el-button
            type="primary"
            @click="showVariablePicker(index)"
            class="variable-button"
            link
            :icon="MagicStick"
            title="插入变量"
          ></el-button>
        </template>
      </el-input>
      <el-icon class="add-variable-item-delete" @click="deleteVariableFnc(index)"
        ><Delete
      /></el-icon>
    </div>

    <!-- 文件名预览 -->
    <div v-if="fieldConfig.type === 'string' && allValues && allValues[fieldConfig?.append?.key || `extend_format`]" class="file-name-preview">
      <span>文件名预览：</span>
      <span>{{ formatFileNamePreview() }}</span>
    </div>
    <!-- 变量选择对话框 -->
    <el-dialog v-model="showVariableDialog" title="选择变量" width="600px" :append-to-body="true">
      <div class="variable-selector">
        <div class="search-section">
          <el-input v-model="searchText" placeholder="搜索变量..." :prefix-icon="Search" clearable/>
        </div>

        <div class="variable-list">
          <div v-if="filteredVariables.length === 0" class="empty-state">
            <el-empty description="暂无可用变量" :image-size="80" />
          </div>

          <div
            v-for="variable in filteredVariables"
            :key="variable.name"
            class="variable-item"
            @click="insertVariable(variable)"
          >
            <div class="variable-header">
              <span class="variable-name">${{ variable.name }}</span>
              <div>
                <el-tag :type="getScopeTagType(variable.scope)" size="small">
                  {{ getScopeLabel(variable.scope) }}
                </el-tag>
                <el-tag
                  type="primary"
                  size="small"
                  v-if="variable.isSystem"
                  style="margin-left: 8px"
                >
                  内置
                </el-tag>
              </div>
            </div>

            <div class="variable-meta">
              <span class="variable-type">{{ getTypeLabel(variable.type) }}</span>
              <span class="variable-value" v-if="variable.value" >{{ formatValue(variable.value, variable.type) }}</span>
              <span v-if="variable.sourceNode" class="variable-source"
                >来自: {{ variable.sourceNode }}</span
              >
            </div>

            <div v-if="variable.description" class="variable-description" :title="variable.description">
              {{ variable.description }}
            </div>
          </div>
        </div>

        <div class="manual-input">
          <el-divider>或手动输入变量名</el-divider>
          <div class="manual-input-row">
            <el-input v-model="manualVariableName" placeholder="输入变量名" @keyup.enter="insertManualVariable"/>
            <el-button type="primary" @click="insertManualVariable" :disabled="!manualVariableName">插入</el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showVariableDialog = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { Search, Document, MagicStick } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getAvailableVariables } from "@/utils/availableVariables.ts";
import { useWorkflowStore } from "@/stores/workflow.ts";

// Props
interface Props {
  modelValue?: string
  fieldConfig: any
  disabled?: boolean
  noAdd?: boolean
  allValues: Record<string, any>
  fieldName: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 响应式数据
const showVariableDialog = ref(false)
const searchText = ref('')
const manualVariableName = ref('')
const inputRef = ref()
const textareaRef = ref()
const jsonRef = ref()
const stringArrayIndex = ref(-1)
const inputArrayRef = ref()


// 从工作流中收集可用变量
const availableVariables = computed(() => getAvailableVariables())

// 获取当前选中组件
const workflowStore = useWorkflowStore()
const selectedNode = computed(() => workflowStore.selectedNode)

// 计算属性
const filteredVariables = computed(() => {
  const notCurrentSelectNodeVariables = availableVariables.value.filter(
    (variable) => variable.sourceNodeId !== selectedNode.value?.id || variable.scope === 'local'
  )

  if (!searchText.value) {
    return notCurrentSelectNodeVariables
  }

  const search = searchText.value.toLowerCase()
  return notCurrentSelectNodeVariables.filter(
    (variable) =>
      variable.name.toLowerCase().includes(search) ||
      variable.description?.toLowerCase().includes(search) ||
      variable.type.toLowerCase().includes(search),
  )
})


//上传文件夹
const selectFolder = async () => {
  if (window.electronAPI) {
    // todo 一诺桌面端选择文件夹
  } else {
    // Web环境下的文件夹选择（HTML5 webkitdirectory）
    const input = document.createElement('input')
    input.type = 'file'
    input.webkitdirectory = true
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      if (files && files.length > 0) {
        // 获取第一个文件的路径，去掉文件名部分
        const firstFile = files[0]
        const path = firstFile.webkitRelativePath
        const folderPath = path.substring(0, path.indexOf('/'))
        handleInput(folderPath)
      }
    }
    input.click()
  }
}

// 方法
const handlePrefix = (value: string) => {
  emit('change-prefix', value, props.fieldConfig?.prefix?.key || `${props.fieldName}_method`)
}

const handleAppend = (value: string) => {
  emit('change-append', value, props.fieldConfig?.append?.key || `extend_format`)
}

// 文件名预览
const formatFileNamePreview = () => {
  const fileName = props.modelValue || ''
  const extendDict = {
    'YYYYMMDD': '20250101',
    'YYYYMMDDHHmmss': '20250101120000',
    'timestamp': '1690844645',
  }
  const suffix = props.fieldConfig.suffix ? `.${props.fieldConfig.suffix}` : ''
  const extendFormat = props.allValues[props.fieldConfig?.append?.key || `extend_format`] || ''
  return fileName + '_' + extendDict[extendFormat] + suffix
}

// 方法
const handleInput = (value: string) => {
  emit('update:modelValue', value)
}

const showVariablePicker = (saIndex = -1) => {
  if(saIndex !== -1 && !isNaN(saIndex)){
    stringArrayIndex.value = saIndex
  }
  showVariableDialog.value = true
  searchText.value = ''
  manualVariableName.value = ''
}

let clickTimer: NodeJS.Timeout | null = null;
const insertVariable = (variable: any) => {
  //解决双击触发两次
  if (clickTimer) {
    clearTimeout(clickTimer);
    clickTimer = null;
    return
  } else {
    clickTimer = setTimeout(() => {
      clickTimer = null;
    }, 300);
  }
  // 检查是否是Python代码字段
  const isPythonCode = props.language === 'python' || (props.type === 'textarea' && props.language === 'python')

  let variableRef
  if (isPythonCode) {
    // 对于Python代码，使用三引号包围变量以安全处理字符串
    variableRef = `"""\${${variable.name}}"""`
  } else {
    // 其他情况使用标准格式
    variableRef = `\${${variable.name}}`
  }
  if (props.noAdd) variableRef = variable.name
  insertTextAtCursor(variableRef)
  showVariableDialog.value = false
  ElMessage.success(`已插入变量: ${variable.name}`)
}

const insertManualVariable = () => {
  if (!manualVariableName.value) return

  // 检查是否是Python代码字段
  const isPythonCode =
    props.language === 'python' || (props.type === 'textarea' && props.language === 'python')

  let variableRef
  if (isPythonCode) {
    // 对于Python代码，使用三引号包围变量以安全处理字符串
    variableRef = `"""\${${manualVariableName.value}}"""`
  } else {
    // 其他情况使用标准格式
    variableRef = `\${${manualVariableName.value}}`
  }
  if (props.noAdd) variableRef = manualVariableName.value

  insertTextAtCursor(variableRef)
  showVariableDialog.value = false
  manualVariableName.value = ''
  ElMessage.success(`已插入变量: ${manualVariableName.value}`)
}

const insertTextAtCursor = (text: string) => {
  if(props.noAdd) {
    emit('update:modelValue', text)
    return
  }
  let currentValue = props.modelValue || ''
  // 获取当前活动的输入框
  let activeElement = inputRef.value?.input || textareaRef.value?.textarea || jsonRef.value?.textarea

  let activeArrayElement = stringArrayIndex.value > -1 ? inputArrayRef.value[stringArrayIndex.value]?.input : null

  if (activeElement) {
    const start = activeElement.selectionStart || 0
    const end = activeElement.selectionEnd || 0
    const newValue = currentValue.substring(0, start) + text + currentValue.substring(end)

    emit('update:modelValue', newValue)

    // 设置光标位置
    nextTick(() => {
      const newCursorPosition = start + text.length
      activeElement.setSelectionRange(newCursorPosition, newCursorPosition)
      activeElement.focus()
    })
  }
  // 多行文本变量插入需要调整为局部更新
  else if(activeArrayElement && props.fieldConfig.type ==='stringArray'){
    currentValue = addVariableDatas.value[stringArrayIndex.value] || ''

    const start = activeArrayElement.selectionStart || 0
    const end = activeArrayElement.selectionEnd || 0
    const newValue = currentValue.substring(0, start) + text + currentValue.substring(end)


    handleVariableUpdate(stringArrayIndex.value, newValue)

    // 设置光标位置
    nextTick(() => {
      const newCursorPosition = start + text.length
      activeArrayElement.setSelectionRange(newCursorPosition, newCursorPosition)
      activeArrayElement.focus()
    })
  }
  else {
    // 如果没有光标位置，就追加到末尾
    emit('update:modelValue', currentValue + text)
  }
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(props.modelValue || props.fieldConfig.formatJson || '{}')
    const formatted = JSON.stringify(parsed, null, 2)
    emit('update:modelValue', formatted)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

const getScopeTagType = (scope: string) => {
  switch (scope) {
    case 'global':
      return 'danger'
    case 'workflow':
      return 'warning'
    case 'local':
      return 'info'
    default:
      return 'info'
  }
}

const getScopeLabel = (scope: string) => {
  switch (scope) {
    case 'global':
      return '全局'
    case 'workflow':
      return '工作流'
    case 'local':
      return '局部'
    default:
      return scope
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'string':
      return '字符串'
    case 'number':
      return '数字'
    case 'boolean':
      return '布尔值'
    case 'json':
      return 'JSON'
    case 'list':
      return '列表'
    case 'currentTime':
      return '当前时间'
    default:
      return type
  }
}

const formatValue = (value: any, type?: string) => {
  if (!value) return ''
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  // 如果是当前时间类型且值是时间格式字符串
  if (type === 'currentTime' && typeof value === 'string') {
    // 将 % 格式转换为 YYYY-MM-DD HH:mm:ss 格式
    return value
      .replace(/%Y/g, 'YYYY')
      .replace(/%m/g, 'MM')
      .replace(/%d/g, 'DD')
      .replace(/%H/g, 'HH')
      .replace(/%M/g, 'mm')
      .replace(/%S/g, 'ss')
  }
  const str = String(value)
  return str.length > 30 ? str.substring(0, 30) + '...' : str
}



// 新增变量相关方法
const addVariableDatas = ref([] as string[])
// 新增变量回显
if (props.fieldConfig.addVariableDatas) {
  addVariableDatas.value = typeof props.modelValue === 'string' ? [props.modelValue] : (props.modelValue || [])
}
// 新增：监听value
watch(
  () =>  props.modelValue,
  () => {
    addVariableDatas.value = typeof props.modelValue === 'string' ? [props.modelValue] : (props.modelValue || [])
  },
  { deep: true }
)
// 新增变量输入框及字段
const addVariableFnc = async () => {
  addVariableDatas.value.push('')
}
// 删除新增变量
const deleteVariableFnc = async (index: any) => {
  addVariableDatas.value.splice(index, 1)
  emit('update:modelValue', addVariableDatas)
}
// 变更新增数据
const handleVariableUpdate = (index: number, newValue: any) => {
  addVariableDatas.value[index] = newValue
  emit('update:modelValue', addVariableDatas)
}

defineExpose({
  showVariablePicker,
  addVariableFnc,
})
</script>

<style>
.input-prefix-select-popper .el-select-dropdown__item {
  font-size: 12px;
}
</style>
<style scoped lang="scss">
.variable-input-field {
  width: 100%;
}
.input-with-prefix :deep(.el-input__wrapper) {
  padding: 0;
}

.input-prefix-select :deep(.el-select__selected-item) {
  font-size: 12px;
}

.input-with-variable,
.textarea-with-variable,
.json-with-variable {
  position: relative;
}

/*
.input-with-variable {
  :deep(.variable-button) {
    height: calc(var(--el-button-size) - 2px);
    line-height: calc(var(--el-button-size) - 2px);
  }
}
*/

.variable-button {
  padding: 0 4px;
  /* color: #409eff; */
  color: var(--el-button-text-color);
}

.variable-button:hover {
  /* color: #66b1ff; */
  color: var(--el-button-hover-link-text-color);
}

.variable-actions {
  margin-top: -32px;
  margin-bottom: 8px;
  display: flex;
  height: 24px;
  justify-content: flex-end;
}

.variable-selector {
  max-height: 550px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
}

.variable-list {
  flex: 1;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

.variable-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.variable-item:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.variable-name {
  font-weight: 600;
  color: #409eff;
  font-family: 'Courier New', monospace;
}

.variable-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
  flex-wrap: wrap;
}

.variable-type {
  font-weight: 500;
}

.variable-value {
  font-family: 'Courier New', monospace;
  color: #909399;
}

.variable-source {
  font-size: 11px;
  color: #c0c4cc;
}

.variable-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: -webkit-box !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.manual-input {
  margin-top: 16px;
}

.manual-input-row {
  display: flex;
  gap: 8px;
}

.manual-input-row .el-input {
  flex: 1;
}

.add-variable-item {
  width: 100%;
  margin-top: 8px;
  display: flex;
  align-items: center;
}
.add-variable-item .add-variable-item-delete {
  margin-left: 8px;
  cursor: pointer;
}
.add-variable-item .el-input {
  flex: 1;
}
.select-custom-header{
  color: #666666;
}
.variable-input-append{
  width: 100%;
  display: contents;
  .el-select{
    margin: 0 10px 0 -20px;
    :deep(.el-select__wrapper){
      box-shadow: none;
      box-sizing: border-box;
      border-radius: 0;
      border-right: 1px solid var(--el-input-border-color) !important;
    }

    & + .el-button{
      margin: 0 -10px 0 0;
    }
  }
}
.file-name-preview{
  color: #666666;
  font-size: 12px;
}
</style>
