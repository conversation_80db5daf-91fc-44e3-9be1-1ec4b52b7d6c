import time
import os
from datetime import datetime
from robot.libraries.BuiltIn import BuiltIn
from openpyxl import load_workbook



def generate_filename_with_extend_format(filename: str) -> str:
    extend_format = BuiltIn().get_variable_value("${extend_format}")
    name, ext = os.path.splitext(filename)

    # 处理格式拼接
    if extend_format == "timestamp":
        suffix = str(int(time.time()))
    elif extend_format == "YYYYMMDD":
        suffix = datetime.now().strftime("%Y%m%d")
    elif extend_format == "YYYYMMDDHHmmss":
        suffix = datetime.now().strftime("%Y%m%d%H%M%S")
    elif extend_format:
        suffix = extend_format  # 原样拼接
    else:
        suffix = ""

    if suffix:
        new_name = f"{name}_{suffix}{ext}"
    else:
        new_name = filename

    return new_name


def open_folder(path: str):
    # 获取是否打开目录开关
    is_open_folder = BuiltIn().get_variable_value("${is_open_folder}")
    if is_open_folder:
        if os.path.isdir(path):
            os.startfile(path)
        else:
            print(f"路径不存在或不是文件夹：{path}")


def get_excel_info_from_paths(paths):
    result = []

    def process_file(file_path):
        try:
            wb = load_workbook(filename=file_path, read_only=True, data_only=True)
            sheets = wb.sheetnames
            wb.close()
            result.append({
                "name": os.path.basename(file_path),
                "path": file_path,
                "sheets": sheets
            })
        except Exception as e:
            print(f"无法读取文件 {file_path}: {e}")

    for p in paths:
        if os.path.isfile(p):
            process_file(p)
        elif os.path.isdir(p):
            for root, dirs, files in os.walk(p):
                for file in files:
                    if file.lower().endswith(".xlsx"):
                        process_file(os.path.join(root, file))
        else:
            print(f"路径不存在或不是文件/目录: {p}")

    return result