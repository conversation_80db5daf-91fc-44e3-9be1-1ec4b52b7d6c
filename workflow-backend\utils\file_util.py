import time
import os
from datetime import datetime
from robot.libraries.BuiltIn import BuiltIn


def generate_filename_with_extend_format(filename: str) -> str:
    extend_format = BuiltIn().get_variable_value("${extend_format}")
    name, ext = os.path.splitext(filename)

    # 处理格式拼接
    if extend_format == "timestamp":
        suffix = str(int(time.time()))
    elif extend_format == "YYYYMMDD":
        suffix = datetime.now().strftime("%Y%m%d")
    elif extend_format == "YYYYMMDDHHmmss":
        suffix = datetime.now().strftime("%Y%m%d%H%M%S")
    elif extend_format:
        suffix = extend_format  # 原样拼接
    else:
        suffix = ""

    if suffix:
        new_name = f"{name}_{suffix}{ext}"
    else:
        new_name = filename

    return new_name


def open_folder(path: str):
    # 获取是否打开目录开关
    is_open_folder = BuiltIn().get_variable_value("${is_open_folder}")
    if is_open_folder:
        if os.path.isdir(path):
            os.startfile(path)
        else:
            print(f"路径不存在或不是文件夹：{path}")