import os
import uuid
import matplotlib.pyplot as plt

def draw_chart_from_echarts_config(config, output_dir="./charts"):
    """
    支持简单的 bar 和 line 类型图表，根据 echarts 结构画 matplotlib 图，
    返回生成的图片路径。
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    x_data = config.get("xAxis", {}).get("data", [])
    series_list = config.get("series", [])
    if not series_list or not x_data:
        raise ValueError("图表配置缺少 xAxis.data 或 series")

    series = series_list[0]  # 仅处理第一个series
    y_data_raw = series.get("data", [])
    chart_type = series.get("type", "bar")

    # 处理数据，y_data可能是数字或对象
    y_data = []
    colors = []
    for item in y_data_raw:
        if isinstance(item, dict):
            y_data.append(item.get("value", 0))
            c = item.get("itemStyle", {}).get("color", None)
            colors.append(c)
        else:
            y_data.append(item)
            colors.append(None)

    fig, ax = plt.subplots(figsize=(6, 4))

    if chart_type == "bar":
        bar_colors = [c if c else '#1f77b4' for c in colors]
        ax.bar(x_data, y_data, color=bar_colors)
    elif chart_type == "line":
        line_color = colors[0] if colors[0] else '#1f77b4'
        ax.plot(x_data, y_data, color=line_color, marker='o')
    else:
        raise ValueError(f"不支持的图表类型: {chart_type}")

    ax.set_xlabel(config.get("xAxis", {}).get("name", ""))
    ax.set_ylabel(config.get("yAxis", {}).get("name", ""))
    ax.set_title(config.get("title", {}).get("text", ""))

    plt.tight_layout()

    filename = f"chart_{uuid.uuid4().hex}.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath)
    plt.close()
    return filepath
