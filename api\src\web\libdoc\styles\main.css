:root {
  --background-color: white;
  --text-color: black;
  --border-color: #e0e0e2;
  --light-background-color: #f3f3f3;
  --robot-highlight: #00c0b5;
  --highlighted-color: var(--text-color);
  --highlighted-background-color: yellow;
  --less-important-text-color: gray;
  --link-color: #0000ee;
}

[data-theme="dark"] {
  --background-color: #1c2227;
  --text-color: #e2e1d7;
  --border-color: #4e4e4e;
  --light-background-color: #002b36;
  --robot-highlight: yellow;
  --highlighted-color: var(--background-color);
  --highlighted-background-color: yellow;
  --less-important-text-color: #5b6a6f;
  --link-color: #52adff;
  color-scheme: dark;
}

body {
  background: var(--background-color);
  color: var(--text-color);
  margin: 0;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

input,
button,
select {
  background: var(--background-color);
  color: var(--text-color);
}

a {
  color: var(--link-color);
}

.base-container {
  display: flex;
}

.libdoc-overview {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  background: var(--background-color);
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 0;
}

.libdoc-overview h4 {
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

.keyword-search-box {
  display: flex;
  justify-content: space-between;
  height: 30px;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  margin-top: 0.5rem;
}

#tags-shortcuts-container {
  margin-top: 0.5rem;
  height: 30px;
  border: 1px solid var(--border-color);
  border-radius: 3px;
}

.search-input {
  flex: 1;
  border: none;
  text-indent: 4px;
}

.clear-search {
  border: none;
}

#shortcuts-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.libdoc-details {
  margin-top: 60px;
  padding-left: 2%;
  padding-right: 2%;
  overflow: auto;
  max-width: 1000px;
}

.libdoc-title {
  position: fixed;
  left: 0;
  top: 0;
  width: 300px;
  height: 36px;
  padding: 0.5rem;
  margin: 0.5rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-color);
}

#language-container {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
}

#language-container button {
  border: none;
  padding-top: 15px;
  padding-right: 15px;
}

#language-container svg {
  width: 20px;
  height: 20px;
}

#language-container svg path {
  stroke: var(--text-color);
  fill: var(--background-color);
}

#language-container ul {
  list-style: none;
  margin: 0;
  padding: 10px;
  background-color: var(--background-color);
}

#language-container a {
  text-decoration: none;
  cursor: pointer;
  color: var(--less-important-text-color);
}

#language-container a.selected {
  color: var(--text-color);
}

.hamburger-menu {
  display: none;
  position: fixed;
  z-index: 100;
}

input.hamburger-menu {
  display: none;
  width: 67px;
  height: 46px;
  position: fixed;
  top: 0;
  right: 0;

  cursor: pointer;

  opacity: 0;
  z-index: 2;

  -webkit-touch-callout: none;
}

span.hamburger-menu {
  width: 31px;
  height: 2px;
  margin-bottom: 5px;
  position: fixed;
  right: 20px;

  background: black;
  background: var(--text-color);
  border-radius: 2px;

  z-index: 1;

  transform-origin: 4px 0;

  transition:
    transform 0.3s cubic-bezier(0.77, 0.2, 0.05, 1),
    opacity 0.35s ease;
}

span.hamburger-menu-1 {
  top: 14px;
  transform-origin: 0 0;
}

span.hamburger-menu-2 {
  top: 24px;
}

span.hamburger-menu-3 {
  top: 34px;
  transform-origin: 0 100%;
}

input.hamburger-menu:checked ~ span.hamburger-menu-1 {
  opacity: 1;
  transform: rotate(45deg) translate(2px, -3px);
  background: var(--text-color);
}

input.hamburger-menu:checked ~ span.hamburger-menu-2 {
  opacity: 0;
  transform: rotate(0deg) scale(0.2, 0.2);
}

input.hamburger-menu:checked ~ span.hamburger-menu-3 {
  transform: rotate(-45deg) translate(2px, 3px);
  background: var(--text-color);
}

.libdoc-title > svg {
  padding-top: 2px;
  height: 42px;
  width: 42px;
}

#robot-svg-path {
  fill: var(--text-color);
  stroke: none;
  fill-opacity: 1;
  fill-rule: nonzero;
}

.keywords-overview {
  display: flex;
  flex-direction: column;
  height: 0;
  max-height: calc(100vh - 60px - 0.5rem);
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  margin: 60px 0 0.5rem 0.5rem;
}

.keywords-overview-header-row {
  display: flex;
  justify-content: space-between;
}

.shortcuts {
  font-size: 0.9em;
  overflow: auto;
  list-style: none;
  padding-left: 0;
  margin: 0;
  flex: 1;
  max-width: 320px;
}

.shortcuts.keyword-wall {
  flex: unset;
}

.shortcuts a {
  display: block;
  text-decoration: none;
  white-space: nowrap;
  color: var(--text-color);
  padding: 0.5rem;
}

.shortcuts a:hover {
  background: var(--light-background-color);
}

.shortcuts a::first-letter {
  font-weight: bold;
  letter-spacing: 0.1em;
}

.shortcuts.keyword-wall a {
  padding: 0;
  padding-right: 0.5rem;
  padding-bottom: 0.5rem;
}

.shortcuts.keyword-wall a::after {
  content: "·";
  padding-left: 0.5rem;
}

.enum-type-members,
.dt-usages-list {
  list-style: none;
  padding-left: 1em;
}

.dt-usages-list > li {
  margin-bottom: 0.2em;
}

.dt-usages a {
  text-decoration: none;
  color: var(--text-color);
  display: inline-block;
  font-size: 0.9em;
}
.dt-usages a::first-letter {
  font-weight: bold;
  letter-spacing: 0.1em;
}

.arguments-list-container {
  overflow-y: auto;
  margin-bottom: 1.33rem;
}

.arguments-list {
  display: -ms-inline-grid;
  display: inline-grid;
  -ms-grid-columns: 1fr 1fr 1fr;
  grid-template-columns: auto auto auto;
  row-gap: 3px;
}

.typed-dict-annotation > span,
.enum-type-members span,
.arguments-list .arg-name {
  -ms-grid-column: 1;
  grid-column: 1;
  border-radius: 3px;
  white-space: nowrap;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  justify-self: start;
}

.arguments-list .arg-default-container {
  -ms-grid-column: 2;
  grid-column: 2;
  display: flex;
}

.optional-key {
  font-style: italic;
}

.arguments-list .arg-default-eq {
  margin-left: 2rem;
  margin-right: 0.5rem;
  background: var(--background-color);
}

.arguments-list .arg-default-value {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 3px;
}

.arguments-list .base-arg-data {
  display: flex;
  min-width: 150px;
}

.arguments-list .arg-type,
.return-type .arg-type {
  margin-left: 2rem;
  -ms-grid-column: 3;
  grid-column: 3;
  background: var(--background-color);
  white-space: nowrap;
  -webkit-text-size-adjust: none;
}

.tags .kw-tags {
  margin-left: 2rem;
  display: flex;
}

.tag-link {
  cursor: pointer;
}

.tag-link:hover {
  text-decoration: underline;
}

.arguments-list .arg-kind {
  color: transparent;
  text-shadow: 0 0 0 var(--less-important-text-color);
  padding: 0;
  font-size: 0.8em;
}

@media only screen and (min-width: 900px) {
  .libdoc-details {
    z-index: 1;
    background: var(--background-color);
  }

  #toggle-keyword-shortcuts {
    border: 1px solid var(--border-color);
    border-radius: 3px;
    margin-top: 3px;
    margin-bottom: 3px;
  }

  #toggle-keyword-shortcuts:hover {
    background: var(--light-background-color);
  }

  .shortcuts.keyword-wall {
    display: flex;
    flex-wrap: wrap;
    width: 320px;
    max-width: none;
  }
}

@media only screen and (min-width: 1200px) {
  .shortcuts.keyword-wall {
    width: 640px;
  }
}

@media only screen and (max-width: 899px) {
  .libdoc-overview {
    display: none;
  }

  #toggle-keyword-shortcuts {
    display: none;
  }

  .libdoc-title {
    width: 100%;
    padding: 0.5rem;
    margin: 0;
    border-bottom: 1px solid var(--border-color);
    background: white;
    background: var(--background-color);
  }

  .libdoc-title > svg {
    margin-right: 60px;
  }

  .libdoc-details {
    padding-left: 0.5rem;
  }

  input.hamburger-menu {
    display: block;
  }

  .hamburger-menu {
    display: block;
  }

  .hamburger-menu:checked ~ .libdoc-overview {
    display: block;
    position: fixed;
    height: 100vh;
    width: 100%;
  }

  .keywords-overview {
    border: none;
    margin: 60px 0 0;
  }

  .shortcuts {
    max-width: 100vw;
    overscroll-behavior: none;
  }

  #language-container {
    right: 50px;
    width: 100px;
  }

  #language-container button {
    cursor: pointer;
  }
}

.metadata {
  margin-top: 0.5rem;
}

.metadata th {
  text-align: left;
  padding-right: 1em;
}
a.name,
span.name {
  font-style: italic;
}
.libdoc-details a img {
  border: 1px solid #c30 !important;
}
a:hover,
a:active {
  text-decoration: underline;
  color: var(--text-color);
}
a:hover {
  text-decoration: underline !important;
}

.normal-first-letter::first-letter {
  font-weight: normal !important;
  letter-spacing: 0 !important;
}
.shortcut-list-toggle,
.tag-list-toggle {
  margin-bottom: 1em;
  font-size: 0.9em;
}
input.switch {
  display: none;
}
.slider {
  background-color: var(--border-color);
  display: inline-block;
  position: relative;
  top: 5px;
  height: 18px;
  width: 36px;
}
.slider:before {
  background-color: var(--background-color);
  content: "";
  position: absolute;
  top: 3px;
  left: 3px;
  height: 12px;
  width: 12px;
}
input.switch:checked + .slider::before {
  background-color: var(--background-color);
  left: 21px;
}

.keywords {
  display: flex;
  flex-direction: column;
}
.kw-overview {
  display: flex;
  flex-direction: column;
  justify-content: start;
}
@media only screen and (min-width: 899px) {
  .kw-overview {
    max-width: 850px;
    margin-right: 1.5rem;
  }
}
.kw-docs {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.dt-name:link,
.kw-name:link {
  text-decoration: none;
  color: var(--text-color);
}

.dt-name:visited,
.kw-name:visited {
  text-decoration: none;
  color: var(--text-color);
}
.kw {
  display: flex;
  align-items: baseline;
  min-width: 250px;
}
h4 {
  margin-right: 0.5rem;
}

.keyword-container {
  border: 1px solid var(--border-color);
  border-radius: 3px;
  padding: 0.5rem 1rem 0.5rem 1rem;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  scroll-margin-top: 60px;
}

.keyword-container:target {
  box-shadow: 0 0 4px var(--robot-highlight);
}

.data-type-content,
.keyword-content {
  display: flex;
  flex-direction: column;
}

.data-type-container {
  border-top: 1px solid var(--border-color);
  padding: 0.5rem 1rem 0.5rem 1rem;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  scroll-margin-top: 60px;
}

.kw-row {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  justify-content: start;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  padding: 0.5rem 1rem 0.5rem 1rem;
  margin-bottom: 0.5rem;
}
.kw a {
  color: inherit;
  text-decoration: none;
  font-weight: bold;
}
.args {
  min-width: 200px;
}

.enum-type-members span,
.args span,
.return-type span,
.args a {
  font-family: monospace;
  background: var(--light-background-color);
  padding: 0 0.1em;
  font-size: 1.1em;
}

.arg-type,
span.type,
a.type {
  font-size: 1em;
  background: none;
  padding: 0 0;
}

.typed-dict-item .td-type::after {
  content: ",";
}

.typed-dict-item .td-type:nth-last-child(2)::after {
  content: "";
}

.td-item::before {
  content: "  ";
  white-space: pre;
}

.typed-dict-item {
  display: block;
  padding: 0.4rem;
  font-family: monospace;
  background: var(--light-background-color);
  font-size: 1.1em;
}

.args span .highlight {
  background: var(--highlighted-background-color);
  color: var(--highlighted-color);
}

.tags,
.return-type {
  display: flex;
  align-items: baseline;
}
.tags a {
  color: inherit;
  text-decoration: none;
  padding: 0 0.1em;
}
.footer {
  font-size: 0.9em;
}

.doc div > *:last-child {
  margin-bottom: 0;
}
.highlight {
  background: var(--highlighted-background-color);
  color: var(--highlighted-color);
}

.data-type {
  font-style: italic;
}

.no-match {
  color: var(--less-important-text-color) !important;
}

.no-match .dt-name,
.no-match .kw-name {
  color: var(--less-important-text-color);
}

.modal-icon {
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  margin: 0 0.25rem;
  width: 1rem;
  height: 1rem;
  padding: 0;
  border: none;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" height="100%" width="100%"><path stroke="black" fill="none" stroke-width="2px" stroke-linecap="round" d="M1 8 L1 1 L8 1 M16 1 L23 1 L23 8 M23 16 L23 23 L16 23 M8 23 L1 23 L1 16"></path><path fill="black" stroke="none" stroke-width="1px" transform="scale(1.3) translate(-3 -2.5)" d="M19 7.97zm-8 9.2-4-2.3v-4.63l4 2.33v4.6zm1-6.33L8.04 8.53 12 6.25l3.96 2.28L12 10.84zm5 4.03-4 2.3v-4.6l4-2.33v4.63z"></path></svg>');
}
@media (prefers-color-scheme: dark) {
  .modal-icon {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" height="100%" width="100%"><path stroke="%23e2e1d7" fill="none" stroke-width="2px" stroke-linecap="round" d="M1 8 L1 1 L8 1 M16 1 L23 1 L23 8 M23 16 L23 23 L16 23 M8 23 L1 23 L1 16"></path><path fill="%23e2e1d7" stroke="none" stroke-width="1px" transform="scale(1.3) translate(-3 -2.5)" d="M19 7.97zm-8 9.2-4-2.3v-4.63l4 2.33v4.6zm1-6.33L8.04 8.53 12 6.25l3.96 2.28L12 10.84zm5 4.03-4 2.3v-4.6l4-2.33v4.63z"></path></svg>');
  }
}
.modal-background,
.modal {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
}
.modal-background {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1;
}
.modal {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  width: 720px;
  max-width: calc(100vw - 2rem);
  margin: 0 auto;
  height: calc(100vh - 6rem);
  overflow: auto;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  z-index: 2;
  transition-delay: 0.1s;
}
.modal-content {
  margin-bottom: 3rem;
}
.modal > .modal-content > .data-type-container {
  border-top: none;
}
.modal-close-button-wrapper {
  display: flex;
  justify-content: flex-end;
}

.modal-close-button-container {
  width: 720px;
  max-width: calc(100vw - 2rem);
  margin: 0 auto;
  overflow: auto;
}

.modal-close-button {
  margin: 0.5rem 0;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.modal-background.visible,
.modal.visible {
  opacity: 1;
  pointer-events: all;
}
#data-types-container {
  display: none;
}

.hidden {
  display: none;
}
