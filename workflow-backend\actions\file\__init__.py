from loguru import logger as main_logger

from core.executor import ActionGroup

import importlib
import inspect
from pathlib import Path

group = ActionGroup()

logger = main_logger

TARGET_DECORATORS = {"ActionGroup.action"}

current_dir = Path(__file__).parent
modules = [
    f.stem
    for f in current_dir.glob("*.py")
    if f.name != "__init__.py"  # 修正：原代码中"__init__.py"需要用!=判断
    and not f.name.startswith("_")
]

target_functions = []

for module_name in modules:
    try:
        # 动态导入模块
        module = importlib.import_module(f".{module_name}", package=__name__)
    except ImportError as e:
        print(f"警告：导入模块 {module_name} 失败: {e}")
        continue

    # 遍历模块中的所有成员
    for member_name, member in inspect.getmembers(module):
        # 筛选条件：
        # 1. 是函数或方法
        # 2. 带有装饰器标记（_decorated_by）
        # 3. 标记属于目标装饰器集合
        if (
            (inspect.isfunction(member) or inspect.ismethod(member))
            and hasattr(member, "_decorated_by")
            and member._decorated_by in TARGET_DECORATORS
        ):
            # 导入函数到当前命名空间
            globals()[member_name] = member
            target_functions.append(member_name)

# 定义__all__，控制from package import *时导入的内容
__all__ = target_functions
