#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
<AUTHOR>   zxf 
@Version :   1.0
@Time    :   2025/07/03 15:32:27
'''
import requests

from config import globals
from config.env_config import DLY_URL,get_config_item


def alarm_query(data, authorization):
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Authorization': authorization  if authorization else globals.token,
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': get_config_item(DLY_URL),
        'Referer': f'{get_config_item(DLY_URL)}/oneMap3DMaster/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'tenant-id': '0000000000',
        'Cookie': 'JSESSIONID=0376DA35FAE2455B2B77FA3C505B6750'
    }

    response = requests.post(f"{get_config_item(DLY_URL)}/uniwim/imb/hdAlarmRecord/alarm_record/query",
                            json=data, headers=headers).json()

    return response
