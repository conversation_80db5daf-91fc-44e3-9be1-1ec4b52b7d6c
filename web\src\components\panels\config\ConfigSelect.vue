<template>
  <el-form-item :label="configMeta.label || configKey">
    <el-select
      :model-value="configValue"
      @update:model-value="handleUpdate"
      :placeholder="configMeta.placeholder || `请选择${configMeta.label || configKey}`"
      clearable
      size="small"
      style="width: 100%"
    >
      <el-option
        v-for="option in options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      >
        <div class="option-content">
          <span>{{ option.label }}</span>
          <span v-if="option.description" class="option-description">
            {{ option.description }}
          </span>
        </div>
      </el-option>
    </el-select>

    <!-- 帮助文本 -->
    <div v-if="configMeta.help" class="config-help">
      <el-icon :size="12">
        <QuestionFilled />
      </el-icon>
      <span>{{ configMeta.help }}</span>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

interface SelectOption {
  label: string
  value: any
  description?: string
  disabled?: boolean
}

interface Props {
  configKey: string
  configValue: any
  nodeType?: string
}

interface Emits {
  (e: 'update', key: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置元数据
const configMeta = computed(() => {
  return getConfigMeta(props.nodeType, props.configKey)
})

// 选项列表
const options = computed(() => {
  return configMeta.value.options || []
})

// 更新处理
const handleUpdate = (value: any) => {
  emit('update', props.configKey, value)
}

// 获取配置元数据
function getConfigMeta(nodeType: string | undefined, key: string) {
  const configDefinitions: Record<string, Record<string, any>> = {
    new_browser: {
      browser: {
        label: '浏览器类型',
        placeholder: '选择要使用的浏览器',
        help: '选择用于自动化的浏览器类型',
        options: [
          { label: 'Chrome', value: 'chrome', description: '推荐使用，兼容性最好' },
          { label: 'Firefox', value: 'firefox', description: '开源浏览器' },
          { label: 'Safari', value: 'safari', description: '仅限macOS' },
          { label: 'Edge', value: 'edge', description: 'Microsoft浏览器' },
        ],
      },
    },
    click_element: {
      selector_type: {
        label: '选择器类型',
        placeholder: '选择元素定位方式',
        help: '选择用于定位页面元素的方法',
        options: [
          { label: 'XPath', value: 'xpath', description: '功能强大，支持复杂定位' },
          { label: 'CSS选择器', value: 'css', description: '简洁高效' },
          { label: 'ID', value: 'id', description: '通过元素ID定位' },
          { label: '名称', value: 'name', description: '通过元素name属性定位' },
          { label: '类名', value: 'class', description: '通过CSS类名定位' },
          { label: '标签名', value: 'tag', description: '通过HTML标签定位' },
        ],
      },
    },
    input_text: {
      selector_type: {
        label: '选择器类型',
        placeholder: '选择元素定位方式',
        help: '选择用于定位输入框的方法',
        options: [
          { label: 'XPath', value: 'xpath', description: '功能强大，支持复杂定位' },
          { label: 'CSS选择器', value: 'css', description: '简洁高效' },
          { label: 'ID', value: 'id', description: '通过元素ID定位' },
          { label: '名称', value: 'name', description: '通过元素name属性定位' },
          { label: '类名', value: 'class', description: '通过CSS类名定位' },
        ],
      },
    },
    get_text: {
      selector_type: {
        label: '选择器类型',
        placeholder: '选择元素定位方式',
        help: '选择用于定位目标元素的方法',
        options: [
          { label: 'XPath', value: 'xpath', description: '功能强大，支持复杂定位' },
          { label: 'CSS选择器', value: 'css', description: '简洁高效' },
          { label: 'ID', value: 'id', description: '通过元素ID定位' },
          { label: '类名', value: 'class', description: '通过CSS类名定位' },
          { label: '标签名', value: 'tag', description: '通过HTML标签定位' },
        ],
      },
    },
    wait: {
      unit: {
        label: '时间单位',
        placeholder: '选择时间单位',
        help: '等待时长的时间单位',
        options: [
          { label: '秒', value: 'seconds', description: '以秒为单位' },
          { label: '毫秒', value: 'milliseconds', description: '以毫秒为单位，更精确' },
        ],
      },
    },
    set_variable: {
      value_type: {
        label: '值类型',
        placeholder: '选择变量值的类型',
        help: '指定变量值的数据类型',
        options: [
          { label: '字符串', value: 'string', description: '文本类型' },
          { label: '数字', value: 'number', description: '数值类型' },
          { label: '布尔值', value: 'boolean', description: 'true/false' },
          { label: '列表', value: 'list', description: '数组类型' },
          { label: '字典', value: 'dict', description: '对象类型' },
        ],
      },
    },
    log_message: {
      level: {
        label: '日志级别',
        placeholder: '选择日志级别',
        help: '指定日志消息的重要程度',
        options: [
          { label: '信息', value: 'INFO', description: '一般信息' },
          { label: '警告', value: 'WARN', description: '警告信息' },
          { label: '错误', value: 'ERROR', description: '错误信息' },
          { label: '调试', value: 'DEBUG', description: '调试信息' },
          { label: '跟踪', value: 'TRACE', description: '详细跟踪信息' },
        ],
      },
    },
  }

  const componentConfig = configDefinitions[nodeType || ''] || {}
  return (
    componentConfig[key] || {
      label: key,
      placeholder: `请选择${key}`,
      help: '',
      options: [],
    }
  )
}
</script>

<style scoped>
.config-help {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 11px;
  color: #909399;
  line-height: 1.4;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-description {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
}

/* Element Plus 选择器样式调整 */
:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 12px;
  line-height: 1.4;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: #f5f7fa;
}
</style>
