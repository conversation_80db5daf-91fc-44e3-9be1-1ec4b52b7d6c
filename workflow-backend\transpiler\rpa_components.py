"""
RPA Framework 组件定义
包含所有基于RPA Framework的组件定义
"""

from models.workflow import ComponentDefinition

# from api.atest.testdata.keywords.type_conversion.CustomConverters import false


def get_rpa_browser_components():
    """获取基于RPA Framework Browser Playwright的浏览器组件"""
    return {
        "new_browser": ComponentDefinition(
            type="new_browser",
            label="打开浏览器网页",
            description="使用指定浏览器打开网页，以实现网页自动化",
            category="browser",
            icon="Monitor",
            config_schema={
                "url": {"type": "string", "required": True},
                "browser": {"type": "string", "default": "chromium"},
                "headless": {"type": "boolean", "default": False},
                "timeout": {"type": "number", "default": 30},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["browser_instance"],
            robot_template="""
    ${browser}    New Browser    browser=chromium    headless=False    executablePath=${BROWSER_PATH}
    New Context    viewport={'width': 1920, 'height': 1080}
    Set Browser Timeout    ${timeout}s
    Log    已创建浏览器实例（路径：${BROWSER_PATH}）
    # 保存实例到变量，供后续使用
    ${browser_instance}    Set Variable    ${browser}
    New Page    ${url}
            """,
        ),
        #     "new_browser": ComponentDefinition(
        #         type="new_browser",
        #         label="打开浏览器网页",
        #         description="使用指定浏览器打开网页，以实现网页自动化",
        #         category="browser",
        #         icon="Monitor",
        #         config_schema={
        #             "url": {"type": "string", "required": True},
        #             "browser": {"type": "string", "default": "chromium"},
        #             "headless": {"type": "boolean", "default": False},
        #             "timeout": {"type": "number", "default": 30},
        #             "retry_times": {"type": "number", "default": 0},
        #             "retry_delay": {"type": "number", "default": 2},
        #             "error_handle": {"type": "string", "default": "stop"},
        #         },
        #         outputs=["browser_instance"],
        #         robot_template="""
        # # 打开浏览器并导航到指定URL
        # Open Browser    ${url}    browser=${browser}    headless=False
        # Set Browser Timeout    ${timeout}s
        # ${browser_instance}=    Set Variable    browser_opened
        #         """,
        #     ),
        "navigate_to": ComponentDefinition(
            type="navigate_to",
            label="导航到页面",
            description="在当前浏览器中导航到指定URL",
            category="browser",
            icon="Navigation",
            config_schema={
                "url": {"type": "string", "required": True},
                "timeout": {"type": "number", "default": 30},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
            # 导航到指定URL

        IF    ${tabChanged}
            ${pages}    Get Page Ids
            Switch Page    ${pages[0]}
        END
        Go To    ${url}
        # Wait For Load State    networkidle    timeout=${timeout}s
        Sleep    5
            """,
        ),
        "click_element": ComponentDefinition(
            type="click_element",
            label="点击元素",
            description="点击网页中的按钮、链接或者其他任何元素",
            category="browser",
            icon="Mouse",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "timeout": {"type": "number", "default": 30},
                "wait_after": {"type": "number", "default": 1},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 智能点击元素 - 使用多种策略定位
    TRY
        # 首先等待页面稳定
        Sleep    1s

        # 等待元素可见
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s

        # 等待元素可点击（不被遮挡）
        Wait For Elements State    ${selector}    enabled    timeout=5s

        # 滚动到元素位置确保可见
        TRY
            Scroll To Element    ${selector}
            Sleep    0.5s
        EXCEPT
            Log    滚动到元素失败，继续尝试点击    DEBUG
        END

        # 检查是否有多个匹配元素
        ${element_count}=    Get Element Count    ${selector}

        IF    ${element_count} > 1
            Log    检测到多个匹配元素(${element_count}个)，尝试智能选择策略    WARN

            # 策略1: 尝试点击第一个可见元素
            TRY
                Click    ${selector} >> nth=0
                Log    成功点击第一个匹配元素: ${selector}    INFO
            EXCEPT    AS    ${click_error}
                Log    第一个元素点击失败: ${click_error}    WARN

                # 策略2: 尝试遍历所有匹配元素，找到可点击的
                ${success}=    Set Variable    False
                FOR    ${i}    IN RANGE    ${element_count}
                    TRY
                        Click    ${selector} >> nth=${i}
                        Log    成功点击第${i+1}个元素: ${selector}    INFO
                        ${success}=    Set Variable    True
                        BREAK
                    EXCEPT    AS    ${nth_error}
                        Log    第${i+1}个元素点击失败: ${nth_error}    DEBUG
                        Continue For Loop
                    END
                END

                IF    not ${success}
                    Fail    所有匹配元素都无法点击: ${selector}
                END
            END
        ELSE
            # 只有一个匹配元素，直接点击
            Click    ${selector}
            Log    成功点击元素: ${selector}    INFO
        END

        Sleep    ${wait_after}s

    EXCEPT    AS    ${error}
        Log    点击元素失败: ${selector} - ${error}    ERROR

        # 尝试备用策略
        TRY
            Log    尝试备用点击策略...    INFO

            # 策略1: 使用JavaScript点击
            Execute Javascript
            ...    var elements = document.querySelectorAll('${selector}');
            ...    if (elements.length > 0) {
            ...        for (var i = 0; i < elements.length; i++) {
            ...            var el = elements[i];
            ...            if (el.offsetParent !== null && el.style.display !== 'none') {
            ...                el.click();
            ...                console.log('JavaScript点击成功:', '${selector}', 'index:', i);
            ...                break;
            ...            }
            ...        }
            ...    }
            Log    使用JavaScript点击成功: ${selector}    INFO
            Sleep    ${wait_after}s

        EXCEPT    AS    ${js_error}
            Log    JavaScript点击也失败: ${js_error}    ERROR

            # 策略2: 尝试更宽松的等待条件
            TRY
                Wait For Elements State    ${selector}    attached    timeout=5s
                Click    ${selector}
                Log    使用宽松条件点击成功: ${selector}    INFO
                Sleep    ${wait_after}s
            EXCEPT    AS    ${loose_error}
                Log    宽松条件点击失败: ${loose_error}    ERROR
                Fail    所有点击策略都失败: 原始错误=${error}, JS错误=${js_error}, 宽松错误=${loose_error}
            END
        END
    END
            """,
        ),
        "input_text": ComponentDefinition(
            type="input_text",
            label="填写输入框",
            description="在网页的输入框中输入内容",
            category="browser",
            icon="EditPen",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "text": {"type": "string", "required": True},
                "clear_first": {"type": "boolean", "default": True},
                "timeout": {"type": "number", "default": 20},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance", "text_value"],
            robot_template="""
    # 智能输入文本 - 增强等待和重试策略
    TRY
        # 首先等待元素可见
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s

        # 等待元素可编辑
        Wait For Elements State    ${selector}    editable    timeout=5s

        # 清空并输入文本
        IF    ${clear_first}
            Clear Text    ${selector}
        END
        Fill Text    ${selector}    ${text}

        # 验证输入是否成功
        ${input_value}=    Get Property    ${selector}    value
        Log    输入验证 - 期望: ${text}, 实际: ${input_value}    INFO

        Log    成功输入文本到: ${selector}    INFO

    EXCEPT    AS    ${error}
        Log    输入文本失败: ${selector} - ${error}    ERROR

        # 备用策略：使用JavaScript输入
        TRY
            Log    尝试JavaScript输入策略...    INFO
            Execute Javascript
            ...    var element = document.querySelector('${selector}');
            ...    if (element) {
            ...        element.focus();
            ...        element.value = '${text}';
            ...        element.dispatchEvent(new Event('input', { bubbles: true }));
            ...        element.dispatchEvent(new Event('change', { bubbles: true }));
            ...    }
            Log    JavaScript输入成功: ${selector}    INFO

        EXCEPT    AS    ${js_error}
            Log    JavaScript输入也失败: ${js_error}    ERROR
            Fail    所有输入策略都失败: 原始错误=${error}, JS错误=${js_error}
        END
    END
            """,
        ),
        "hover_element": ComponentDefinition(
            type="hover_element",
            label="悬停元素",
            description="将鼠标悬停在指定元素上",
            category="browser",
            icon="Mouse",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 悬停在指定元素上
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        Hover    ${selector}
        Log    成功悬停在元素: ${selector}    INFO
    EXCEPT    AS    ${error}
        Log    悬停操作失败: ${selector} - ${error}    ERROR
        Fail    悬停操作失败: ${error}
    END
            """,
        ),
        "select_option": ComponentDefinition(
            type="select_option",
            label="选择下拉选项",
            description="在下拉框中选择指定选项",
            category="browser",
            icon="List",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "option_value": {"type": "string", "required": False},
                "option_text": {"type": "string", "required": False},
                "option_index": {"type": "number", "required": False},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 选择下拉框选项
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        IF    '${option_value}' != ''
            Select Options By    ${selector}    value    ${option_value}
        ELSE IF    '${option_text}' != ''
            Select Options By    ${selector}    text    ${option_text}
        ELSE IF    '${option_index}' != ''
            Select Options By    ${selector}    index    ${option_index}
        ELSE
            Fail    必须指定option_value、option_text或option_index中的一个
        END
        Log    成功选择下拉选项: ${selector}    INFO
    EXCEPT    AS    ${error}
        Log    选择下拉选项失败: ${selector} - ${error}    ERROR
        Fail    选择操作失败: ${error}
    END
            """,
        ),
        "get_text": ComponentDefinition(
            type="get_text",
            label="获取文本",
            description="获取指定元素的文本内容",
            category="browser",
            icon="Document",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            outputs=["text_content"],
            robot_template="""
    # 获取元素文本内容
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        ${text_content}=    Get Text    ${selector}
        Log    成功获取文本: ${text_content}    INFO
    EXCEPT    AS    ${error}
        Log    获取文本失败: ${selector} - ${error}    ERROR
        Fail    获取文本操作失败: ${error}
    END
            """,
        ),
        "get_attribute": ComponentDefinition(
            type="get_attribute",
            label="获取元素属性",
            description="获取指定元素的属性值",
            category="browser",
            icon="Info",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "attribute_name": {"type": "string", "required": True},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            outputs=["attribute_value"],
            robot_template="""
    # 获取元素属性值
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        ${attribute_value}=    Get Attribute    ${selector}    ${attribute_name}
        Log    成功获取属性 ${attribute_name}: ${attribute_value}    INFO
    EXCEPT    AS    ${error}
        Log    获取属性失败: ${selector} - ${error}    ERROR
        Fail    获取属性操作失败: ${error}
    END
            """,
        ),
        "wait_for_element": ComponentDefinition(
            type="wait_for_element",
            label="等待元素",
            description="等待指定元素出现或达到指定状态",
            category="browser",
            icon="Clock",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "state": {"type": "string", "default": "visible"},
                "timeout": {"type": "number", "default": 30},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 等待元素达到指定状态
    TRY
        Wait For Elements State    ${selector}    ${state}    timeout=${timeout}s
        Log    元素已达到状态 ${state}: ${selector}    INFO
    EXCEPT    AS    ${error}
        Log    等待元素失败: ${selector} - ${error}    ERROR
        Fail    等待元素操作失败: ${error}
    END
            """,
        ),
        "scroll_to_element": ComponentDefinition(
            type="scroll_to_element",
            label="滚动到元素",
            description="滚动页面直到指定元素可见",
            category="browser",
            icon="ArrowDown",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 滚动到指定元素
    TRY
        Scroll To Element    ${selector}
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        Log    成功滚动到元素: ${selector}    INFO
    EXCEPT    AS    ${error}
        Log    滚动到元素失败: ${selector} - ${error}    ERROR
        Fail    滚动操作失败: ${error}
    END
            """,
        ),
        "scroll_page": ComponentDefinition(
            type="scroll_page",
            label="页面滚动",
            description="按指定方向和距离滚动页面",
            category="browser",
            icon="ArrowDown",
            config_schema={
                "direction": {"type": "string", "default": "down"},
                "amount": {"type": "number", "default": 500},
                "behavior": {"type": "string", "default": "smooth"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
        # 页面滚动操作
        TRY
            Log    开始滚动页面    INFO
            Log    方向=${direction}    INFO
            Log    距离=${amount}px    INFO
            IF    '${direction}' == 'down'
                Scroll By    vertical=${amount}  # 向下滚动
            ELSE IF    '${direction}' == 'up'
                Scroll By    vertical=-${amount}  # 向下滚动
            ELSE IF    '${direction}' == 'left'
                Scroll By    horizontal=${amount}  # 向左滚动
            ELSE IF    '${direction}' == 'right'
                Scroll By    horizontal=-${amount}  # 向右滚动
            ELSE
                Log    不支持的滚动方向[${amount}]    INFO
            END
            Log    等待滚动页面    INFO
            Log    方向=${direction}, 距离=${amount}px    INFO
            Sleep    0.5s    # 等待滚动完成
            Log    成功滚动页面: 方向=${direction}, 距离=${amount}px    INFO
        EXCEPT    AS    ${error}
            Log    页面滚动失败: ${error}    ERROR
            Fail    页面滚动操作失败: ${error}
        END
                """,
        ),
        "send_keys": ComponentDefinition(
            type="send_keys",
            label="发送按键",
            description="向指定元素发送键盘按键",
            category="browser",
            icon="Keyboard",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "keys": {"type": "string", "required": True},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 发送按键到指定元素
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        Press Keys    ${selector}    ${keys}
        Log    成功发送按键到元素: ${selector}, 按键: ${keys}    INFO
    EXCEPT    AS    ${error}
        Log    发送按键失败: ${selector} - ${error}    ERROR
        Fail    按键操作失败: ${error}
    END
            """,
        ),
        "check_checkbox": ComponentDefinition(
            type="check_checkbox",
            label="复选框操作",
            description="选中或取消选中复选框",
            category="browser",
            icon="CheckSquare",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "checked": {"type": "boolean", "default": True},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 复选框操作
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        IF    ${checked}
            Check Checkbox    ${selector}
            Log    成功选中复选框: ${selector}    INFO
        ELSE
            Uncheck Checkbox    ${selector}
            Log    成功取消选中复选框: ${selector}    INFO
        END
    EXCEPT    AS    ${error}
        Log    复选框操作失败: ${selector} - ${error}    ERROR
        Fail    复选框操作失败: ${error}
    END
            """,
        ),
        "upload_file": ComponentDefinition(
            type="upload_file",
            label="文件上传",
            description="上传文件到指定的文件输入框",
            category="browser",
            icon="Upload",
            config_schema={
                "selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "file_path": {"type": "string", "required": True},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 文件上传
    TRY
        Wait For Elements State    ${selector}    visible    timeout=${timeout}s
        Upload File By Selector    ${selector}    ${file_path}
        Log    成功上传文件: ${file_path} 到 ${selector}    INFO
    EXCEPT    AS    ${error}
        Log    文件上传失败: ${selector} - ${error}    ERROR
        Fail    文件上传操作失败: ${error}
    END
            """,
        ),
        "type_text": ComponentDefinition(
            type="type_text",
            label="输入文本",
            description="模拟键盘输入文本（逐字符输入）",
            category="interaction",
            icon="Keyboard",
            config_schema={
                "selector": {"type": "string", "required": True},
                "text": {"type": "string", "required": True},
                "delay": {"type": "number", "default": 100},
                "timeout": {"type": "number", "default": 10000},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["page_id", "text"],
            robot_template="""
    Type Text    ${selector}    ${text}
            """,
        ),
        #     "get_text": ComponentDefinition(
        #         type="get_text",
        #         label="获取文本",
        #         description="获取指定元素的文本内容",
        #         category="data",
        #         icon="Document",
        #         config_schema={
        #             "selector": {"type": "string", "required": True},
        #             "timeout": {"type": "number", "default": 10000},
        #             "retry_times": {"type": "number", "default": 0},
        #             "retry_delay": {"type": "number", "default": 2},
        #             "error_handle": {"type": "string", "default": "stop"},
        #         },
        #         inputs=["page_id"],
        #         outputs=["text"],
        #         robot_template="""
        # ${text}=    Get Text    ${selector}
        #         """,
        #     ),
        "take_screenshot": ComponentDefinition(
            type="take_screenshot",
            label="截图",
            description="对当前页面或指定元素进行截图",
            category="browser",
            icon="Camera",
            config_schema={
                "filename": {"type": "string", "default": "screenshot.png"},
                "selector": {"type": "string", "required": False},
                "full_page": {"type": "boolean", "default": True},
                "quality": {"type": "number", "default": 80},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
                "save_path": {"type": "string", "default": "${OUTPUT_DIR}"},
            },
            inputs=["page_id"],
            outputs=["shot_path"],
            robot_template="""
    Re Path
    {% set is_jpeg = filename.endswith('.jpg') or filename.endswith('.jpeg') %}
	RPA.FileSystem.Create Directory    ${save_path}
	${filepath}=    Set Variable    ${save_path}${/}${filename}
	
	{% if selector %}
	    {% if is_jpeg %}
	${screenshot_path}=    RPA.Browser.Playwright.Take Element Screenshot    ${selector}    ${filepath}    quality=${quality}
	    {% else %}
	${screenshot_path}=    RPA.Browser.Playwright.Take Element Screenshot    ${selector}    ${filepath}
	    {% endif %}
	{% else %}
	    {% if is_jpeg %}
	${screenshot_path}=    RPA.Browser.Playwright.Take Screenshot    ${filepath}    fullPage=${full_page}    quality=${quality}
	    {% else %}
	${screenshot_path}=    RPA.Browser.Playwright.Take Screenshot    ${filepath}    fullPage=${full_page}
	    {% endif %}
	{% endif %}
	Log    截图保存路径为: ${screenshot_path}
            """,
        ),
        "switch_frame": ComponentDefinition(
            type="switch_frame",
            label="切换框架",
            description="切换到指定的iframe或frame",
            category="browser",
            icon="Layout",
            config_schema={
                "frame_selector": {"type": "string", "required": True},
                "selector_type": {"type": "string", "default": "xpath"},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 切换到指定框架
    TRY
        Wait For Elements State    ${frame_selector}    visible    timeout=${timeout}s
        Switch To Frame    ${frame_selector}
        Log    成功切换到框架: ${frame_selector}    INFO
    EXCEPT    AS    ${error}
        Log    切换框架失败: ${frame_selector} - ${error}    ERROR
        Fail    切换框架操作失败: ${error}
    END
            """,
        ),
        "handle_alert": ComponentDefinition(
            type="handle_alert",
            label="处理弹窗",
            description="处理浏览器弹窗（确认、取消或输入文本）",
            category="browser",
            icon="AlertTriangle",
            config_schema={
                "action": {"type": "string", "default": "accept"},
                "prompt_text": {"type": "string", "required": False},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            outputs=["alert_text"],
            robot_template="""
    # 处理浏览器弹窗
    TRY
        Wait For Alert    timeout=${timeout}s
        IF    '${action}' == 'accept'
            ${alert_text}=    Handle Future Dialogs    action=accept
        ELSE IF    '${action}' == 'dismiss'
            ${alert_text}=    Handle Future Dialogs    action=dismiss
        ELSE IF    '${action}' == 'prompt' and '${prompt_text}' != ''
            ${alert_text}=    Handle Future Dialogs    action=accept    prompt_text=${prompt_text}
        ELSE
            Fail    无效的弹窗操作: ${action}
        END
        Log    成功处理弹窗，操作: ${action}    INFO
    EXCEPT    AS    ${error}
        Log    处理弹窗失败: ${error}    ERROR
        Fail    弹窗处理操作失败: ${error}
    END
            """,
        ),
        "close_browser": ComponentDefinition(
            type="close_browser",
            label="关闭浏览器",
            description="关闭当前浏览器实例",
            category="browser",
            icon="X",
            config_schema={
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["browser_instance"],
            robot_template="""
    # 关闭浏览器
    TRY
        Close Browser
        Log    成功关闭浏览器    INFO
    EXCEPT    AS    ${error}
        Log    关闭浏览器失败: ${error}    ERROR
        Fail    关闭浏览器操作失败: ${error}
    END
            """,
        ),
        "comment": ComponentDefinition(
            type="comment",
            label="注释",
            description="添加注释说明",
            category="control",
            icon="ChatDotSquare",
            config_schema={"comment": {"type": "string", "required": True}},
            inputs=[],
            outputs=[],
            robot_template="""
    # ${comment}
            """,
        ),
    }


def get_rpa_desktop_components():
    """获取RPA Framework桌面组件"""
    return {
        "desktop_click": ComponentDefinition(
            type="desktop_click",
            label="桌面点击",
            description="在桌面应用中点击指定位置或元素",
            category="desktop",
            icon="Mouse",
            config_schema={
                "locator": {"type": "string", "required": True},
                "action": {"type": "string", "default": "click"},
                "timeout": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Click    ${locator}
            """,
        ),
        "desktop_type": ComponentDefinition(
            type="desktop_type",
            label="桌面输入",
            description="在桌面应用中输入文本",
            category="desktop",
            icon="EditPen",
            config_schema={
                "text": {"type": "string", "required": True},
                "clear_first": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Run Keyword If    ${clear_first}    Press Keys    ctrl+a
    Type Text    ${text}
            """,
        ),
        "desktop_screenshot": ComponentDefinition(
            type="desktop_screenshot",
            label="桌面截图",
            description="对桌面或指定区域进行截图",
            category="desktop",
            icon="Camera",
            config_schema={
                "filename": {"type": "string", "default": "desktop_screenshot.png"},
                "region": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["screenshot_path"],
            robot_template="""
    ${screenshot_path}=    Run Keyword If    '${region}' != ''
    ...    Take Screenshot    ${filename}    region=${region}
    ...    ELSE    Take Screenshot    ${filename}
            """,
        ),
        "open_application": ComponentDefinition(
            type="open_application",
            label="打开应用程序",
            description="启动指定的桌面应用程序",
            category="desktop",
            icon="Application",
            config_schema={
                "application": {"type": "string", "required": True},
                "arguments": {"type": "string", "default": ""},
                "timeout": {"type": "number", "default": 30},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["application_handle"],
            robot_template="""
    ${application_handle}=    Open Application    ${application}    ${arguments}
    Wait For Application    ${application_handle}    timeout=${timeout}
            """,
        ),
    }


def get_rpa_file_components():
    """获取RPA Framework文件处理组件"""
    return {
        "excel_open": ComponentDefinition(
            type="excel_open",
            label="打开Excel文件",
            description="打开Excel工作簿文件",
            category="file",
            icon="Document",
            config_schema={
                "path": {"type": "string", "required": True},
                "read_only": {"type": "boolean", "default": False},
                # 输入密码
                "password": {"type": "string", "default": "", "required": False},
                # 数据模式
                "data_only": {"type": "boolean", "default": False},
                # 保持VBA代码
                "keep_vba": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["workbook"],
            robot_template="""
                IF    '${password}' != ''
                    ${workbook}=    Load Password Excel    ${path}    ${password}    ${data_only}    ${keep_vba}
                    Set Suite Variable    ${workbook}
                ELSE
                    ${workbook}=    Load Excel    ${path}    ${read_only}
                    Set Suite Variable    ${workbook}
                END
                """,
        ),
        "excel_create": ComponentDefinition(
            type="excel_create",
            label="创建Excel文件",
            description="创建一个Excel文件，支持使用报表模板",
            category="file",
            icon="Document",
            config_schema={
                "file_path": {"type": "string", "required": True},  # 文件保存路径
                "file_name": {"type": "string", "required": True},  # 文件名称
                "is_open_folder": {
                    "type": "boolean",
                    "default": True,
                },  # 是否打开文件所在目录
                "extend_format": {"type": "string", "default": ""},  # 名称扩展格式
                "worksheet": {
                    "type": "string",
                    "default": "Sheet1",
                },  # 默认创建一个名为 "Sheet1" 的工作表
                "fmt": {"type": "string", "default": "xlsx"},
                "use_template": {
                    "type": "boolean",
                    "default": False,
                },  # 是否使用报表模板
                "template_id": {"type": "string", "required": False},  # 报表模板ID
                "response_variable": {"type": "string", "default": ""},  # 数据源变量
                "excel_response": {"type": "array", "default": []},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["workbook", "file_path", "file_name"],
            robot_template="""
                Set File Extension
                ${file_name}=    Sanitize Filename  ${file_name}
                ${full_file_name}=    Set Variable If    '.xlsx' in '${file_name}'    ${file_name}    ${file_name}.${fmt}
                {% if use_template %}
                Log    使用报表模板创建Excel: ${template_id}    INFO
                ${workbook}=     Create Excel With Report Template    ${file_path}${/}${full_file_name}    ${template_id}    ${response_variable}    ${worksheet}
                {% else %}
                Log    输出数据: ${excel_response}    INFO
                ${workbook}=     Create Excel    ${file_path}${/}${full_file_name}    ${excel_response}    ${worksheet}
                {% endif %}
                Set Suite Variable    ${workbook}
                """,
        ),
        "excel_read": ComponentDefinition(
            type="excel_read",
            label="读取Excel数据",
            description="从Excel工作表中读取数据",
            category="file",
            icon="Document",
            config_schema={
                "worksheet": {"type": "string", "default": "Sheet1"},
                "header": {"type": "boolean", "default": True},
                "range": {"type": "string", "default": None},
                "range_type": {"type": "string", "default": None},
                "start_row": {"type": "string", "default": None},
                "end_row": {"type": "string", "default": None},
                "start_col": {"type": "string", "default": None},
                "end_col": {"type": "string", "default": None},
                "rowdata": {"type": "array", "default": []},
                "columndata": {"type": "array", "default": []},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["workbook"],
            outputs=["data"],
            robot_template="""

                ${data}=    Read Excel    ${workbook}  sheetname=${worksheet}  rowdata=${rowdata}  columndata=${columndata}     range_type=${range_type}    range=${range}   start_row=${start_row}    end_row=${end_row}   start_col=${start_col}    end_col=${end_col}
                Set Suite Variable    ${data}
            """,
        ),
        "excel_write": ComponentDefinition(
            type="excel_write",
            label="写入Excel数据",
            description="向Excel工作表写入数据，支持报表模板",
            category="file",
            icon="EditPen",
            config_schema={
                "file_path": {"type": "string", "required": True},  # 文件保存路径
                "file_name": {"type": "string", "required": True},  # 文件名称
                "worksheet": {
                    "type": "string",
                    "default": "Sheet1",
                },  # 默认创建一个名为 "Sheet1" 的工作表
                "fmt": {"type": "string", "default": "xlsx"},
                "use_template": {"type": "boolean", "default": False},
                "template_id": {"type": "string", "required": False},
                "data": {"type": "string", "required": False},
                "start_cell": {"type": "string", "default": "A1"},
                "variable_mapping": {"type": "json", "default": {}},
                "overwrite": {"type": "boolean", "default": False},
                "auto_fit": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["workbook", "data"],
            robot_template="""
            
            ${file_name}=    Sanitize Filename  ${file_name}
            ${full_file_name}=    Set Variable If    '.xlsx' in '${file_name}'    ${file_name}    ${file_name}.${fmt}
            {% if use_template %}
            Write Excel With Template    ${file_path}${/}${full_file_name}    ${template_id}   ${worksheet}    ${variable_mapping}   overwrite=${overwrite}    auto_fit=${auto_fit}
            {% else %}
            Write To Worksheet    ${worksheet}    ${data}    start=${start_cell}
            {% endif %}
                    """,
        ),
        "pdf_read": ComponentDefinition(
            type="pdf_read",
            label="读取PDF文本",
            description="从PDF文件中提取文本内容",
            category="file",
            icon="Document",
            config_schema={
                "path": {"type": "string", "required": True},
                "pages": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["text"],
            robot_template="""
    {% if pages %}
    ${text}=    Get Text From PDF    ${path}    pages=${pages}
    {% else %}
    ${text}=    Get Text From PDF    ${path}
    {% endif %}
            """,
        ),
        "pdf_create": ComponentDefinition(
            type="pdf_create",
            label="创建PDF文档",
            description="从HTML内容创建PDF文档",
            category="file",
            icon="Document",
            config_schema={
                "html_content": {"type": "string", "required": True},
                "output_folder": {"type": "string", "required": True},
                "output_filename": {"type": "string", "required": True},
                "page_size": {"type": "string", "default": "A4"},
                "orientation": {"type": "string", "default": "Portrait"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["html_content"],
            outputs=["pdf_path"],
            robot_template="""
    ${output_path}=    Join Path    ${output_folder}    ${output_filename}
    HTML to PDF    ${html_content}    ${output_path}    page_size=${page_size}    orientation=${orientation}
    ${pdf_path}=    Set Variable    ${output_path}
            """,
        ),
        "word_create": ComponentDefinition(
            type="word_create",
            label="创建Word文档",
            description="创建新的Word文档并添加内容",
            category="file",
            icon="Document",
            config_schema={
                "content": {"type": "string", "default": ""},
                "use_template": {"type": "string", "default": False},
                "template_id": {"type": "string", "default": ""},
                "output_folder": {"type": "string", "required": True},
                "output_filename": {"type": "string", "required": True},
                "is_open_folder": {
                    "type": "boolean",
                    "default": True,
                },  # 是否打开文件所在目录
                "extend_format": {"type": "string", "default": ""},  # 名称扩展格式
                "title": {"type": "string", "default": ""},
                "font_size": {"type": "number", "default": 12},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["content"],
            outputs=["word_path", "file_path", "file_name"],
            robot_template="""
    Log    开始创建Word文档    INFO
    ${word_path}=    Set Variable    
    Log    输出路径: ${word_path}    INFO
    Create Smart Word    ${content}    ${use_template}    ${template_id}    ${output_folder}    ${output_filename}    ${title}    ${font_size}
            """,
        ),
        "markdown_save": ComponentDefinition(
            type="markdown_save",
            label="保存Markdown文件",
            description="将内容保存为Markdown格式文件",
            category="file",
            icon="Document",
            config_schema={
                "content": {"type": "string", "required": True},
                "output_folder": {"type": "string", "required": True},
                "output_filename": {"type": "string", "required": True},
                "encoding": {"type": "string", "default": "utf-8"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["content"],
            outputs=["markdown_path"],
            robot_template="""
    ${output_path}=    Join Path    ${output_folder}    ${output_filename}
    Create File    ${output_path}    ${content}    encoding=${encoding}
    ${markdown_path}=    Set Variable    ${output_path}
            """,
        ),
        "text_file_save": ComponentDefinition(
            type="text_file_save",
            label="保存文本文件",
            description="将内容保存为文本文件",
            category="file",
            icon="Document",
            config_schema={
                "content": {"type": "string", "required": True},
                "output_folder": {"type": "string", "required": True},
                "output_filename": {"type": "string", "required": True},
                "encoding": {"type": "string", "default": "utf-8"},
                "append": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["content"],
            outputs=["file_path"],
            robot_template="""
    ${output_path}=    Join Path    ${output_folder}    ${output_filename}
    {% if append %}
    Append To File    ${output_path}    ${content}    encoding=${encoding}
    {% else %}
    Create File    ${output_path}    ${content}    encoding=${encoding}
    {% endif %}
    ${file_path}=    Set Variable    ${output_path}
            """,
        ),
    }


def get_rpa_email_components():
    """获取RPA Framework邮件组件"""
    return {
        "email_send": ComponentDefinition(
            type="email_send",
            label="发送邮件",
            description="发送电子邮件",
            category="email",
            icon="Message",
            config_schema={
                "smtp_server": {"type": "string", "required": True},
                "smtp_port": {"type": "number", "default": 587},
                "username": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "to": {"type": "string", "required": True},
                "subject": {"type": "string", "required": True},
                "body": {"type": "string", "required": True},
                "attachments": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Authorize    ${smtp_server}    ${smtp_port}    ${username}    ${password}
    {% if attachments %}
    Send Message    sender=${username}    recipients=${to}    subject=${subject}    body=${body}    attachments=${attachments}
    {% else %}
    Send Message    sender=${username}    recipients=${to}    subject=${subject}    body=${body}
    {% endif %}
            """,
        ),
        "email_read": ComponentDefinition(
            type="email_read",
            label="读取邮件",
            description="从邮箱中读取邮件",
            category="email",
            icon="Message",
            config_schema={
                "imap_server": {"type": "string", "required": True},
                "username": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "folder": {"type": "string", "default": "INBOX"},
                "limit": {"type": "number", "default": 10},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["emails"],
            robot_template="""
    Authorize    ${imap_server}    port=993    account=${username}    password=${password}
    ${emails}=    List Messages    ${folder}    limit=${limit}
            """,
        ),
    }


def get_rpa_database_components():
    """获取RPA Framework数据库组件"""
    return {
        "db_connect": ComponentDefinition(
            type="db_connect",
            label="连接数据库",
            description="连接到数据库",
            category="database",
            icon="Database",
            config_schema={
                "driver": {"type": "string", "default": "sqlite"},
                "host": {"type": "string", "required": True},
                "port": {"type": "string", "required": True},
                "user": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["connection"],
            robot_template="""
${connection} =   Connect To Database    ${driver}    ${database}     ${user}    ${password}    ${host}    ${port}
${result}=    Query    SELECT 1
            """,
        ),
        "db_query": ComponentDefinition(
            type="db_query",
            label="查询数据库",
            description="执行数据库查询",
            category="database",
            icon="Search",
            config_schema={
                "query": {"type": "string", "required": True},
                "parameters": {"type": "string", "required": False},
                "result_variable": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["connection"],
            outputs=["result"],
            robot_template="""
    {% set result_var = result_variable.strip() if result_variable else "DB_QUERY_RESULT" %}

    Fix Query
    {% if parameters %}
    ${result}=    Query    ${query}    ${parameters}
    {% else %}
    ${result}=    Query    ${query}
    {% endif %}

    ${json_str}=    Table To Json    ${result}
    Set Suite Variable    ${{ '{' }}{{ result_var }}{{ '}' }}    ${json_str}
    Set Suite Variable    ${result}    ${json_str}
    Log    Query result stored in variable: {{ result_var }}    INFO
            """,
        ),
        "db_execute": ComponentDefinition(
            type="db_execute",
            label="执行数据库操作",
            description="执行数据库插入、更新、删除操作",
            category="database",
            icon="EditPen",
            config_schema={
                "statement": {"type": "string", "required": True},
                "parameters": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["connection"],
            robot_template="""
    {% if parameters %}
    Execute Sql Statement    ${statement}    ${parameters}
    {% else %}
    Execute Sql Statement    ${statement}
    {% endif %}
            """,
        ),
    }


def get_rpa_http_components():
    """获取RPA Framework HTTP组件"""
    return {
        "http_get": ComponentDefinition(
            type="http_get",
            label="HTTP GET请求",
            description="发送HTTP GET请求",
            category="api",
            icon="Connection",
            config_schema={
                "url": {"type": "string", "required": True},
                "headers": {"type": "string", "required": False},
                "params": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["response", "response_text"],
            robot_template="""
    {% if headers and params %}
    ${response}=    RequestsLibrary.GET    ${url}    headers=${headers}    params=${params}
    {% elif headers %}
    ${response}=    RequestsLibrary.GET    ${url}    headers=${headers}
    {% elif params %}
    ${response}=    RequestsLibrary.GET    ${url}    params=${params}
    {% else %}
    ${response}=    RequestsLibrary.GET    ${url}
    {% endif %}

    # translate reponse to markdown
    ${response_text} = Response To Markdown    ${json_response}
            """,
        ),
        "http_post": ComponentDefinition(
            type="http_post",
            label="HTTP POST请求",
            description="发送HTTP POST请求并支持变量替换和响应捕获",
            category="api",
            icon="Upload",
            config_schema={
                "url": {"type": "string", "required": True},
                "data": {"type": "string", "required": False},
                "json_data": {"type": "string", "required": False},
                "headers": {"type": "string", "required": False},
                "response_content_variable": {"type": "string", "required": False},
                "extract_variable": {"type": "string", "required": False},
                "timeout": {"type": "number", "default": 30},
                "verify_ssl": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["response", "response_text"],
            robot_template="""
    # HTTP POST Request with variable support
    Log    Sending POST request to: ${url}    INFO

    {% if json_data and headers %}
    ${response}=    RequestsLibrary.POST    ${url}    json=${json_data}    headers=${headers}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% elif data and headers %}
    ${response}=    RequestsLibrary.POST    ${url}    data=${data}    headers=${headers}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% elif json_data %}
    ${response}=    RequestsLibrary.POST    ${url}    json=${json_data}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% elif data %}
    ${response}=    RequestsLibrary.POST    ${url}    data=${data}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% elif headers %}
    ${response}=    RequestsLibrary.POST    ${url}    headers=${headers}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% else %}
    ${response}=    RequestsLibrary.POST    ${url}    timeout=${timeout}    verify={% if verify_ssl %}${True}{% else %}${False}{% endif %}
    {% endif %}

    # Log response details
    Log    Response Status: ${response.status_code}    INFO
    Log    Response Headers: ${response.headers}    INFO
    Log    Response Content: ${response.content}    INFO
    # translate reponse to markdown
     ${response_text} =   Response To Markdown    ${response.json()}
    # Store response content in variable if specified
    {% if response_content_variable %}
    # Get JSON response with proper Chinese character handling
    ${json_response}=    Set Variable    ${response.json()}
    ${json_text}=    Evaluate    json.dumps(${json_response}, ensure_ascii=False, indent=2)    json
    Set Suite Variable    ${{ '{' }}{{ response_content_variable }}{{ '}' }}    ${json_text}
    Log    Response content stored in variable: {{ response_content_variable }}    INFO
    
    {% endif %}
    
            """,
        ),
        "http_request": ComponentDefinition(
            type="http_request",
            label="HTTP 请求",
            description="发送HTTP请求并支持变量替换和响应捕获,可以选择POST和GET",
            category="api",
            icon="Upload",
            config_schema={
                "url_method": {"type": "string", "required": True},
                "url": {"type": "string", "required": True},
                "json_data": {"type": "string", "required": False},
                "headers": {"type": "string", "required": False},
                "error_handle": {"type": "string", "default": "stop"},
                "response_content_variable": {"type": "string", "required": False},
                "extract_variable": {"type": "array", "required": False, "default": []},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
                "verify_ssl": {"type": "boolean", "default": True},
            },
            outputs=["response", "response_text"],
            robot_template="""
            # Call custom HTTP function
            ${response_text}=    Set Variable    ""
            ${is_dict}=    Set Variable    True
            
            ${response}=    Send HTTP Request
            ...    url_method=${url_method}
            ...    url=${url}
            ...    json_data=${json_data}
            ...    headers=${headers}
            ...    error_handle=${error_handle}
            ...    timeout=${timeout}
            ...    verify_ssl=${verify_ssl}
            

            IF  ${is_dict} 
                ${response_output}=    Evaluate    json.dumps(${response}, ensure_ascii=False, indent=2)    json
                # Set Suite Variable    ${{ '{' }}{{ response_content_variable }}{{ '}' }}    ${response_output}
                # "response_content_variable": {"type": "string", "required": False},
                # ...    response_content_variable=${response_content_variable}
                # Log    Response content stored in variable: ${response_content_variable}    INFO
                
                ${json_value}=    Json Extract    ${response_output}    ${extract_variable}
                # 解析多格式响应
                ${result_dict}=    Set Variable    ${json_value}
                
                ${py_list}=    Evaluate    ast.literal_eval(${extract_variable}) if isinstance(${extract_variable}, str) else ${extract_variable}    modules=ast
    
                # 正确遍历列表中的每个元素
                FOR    ${value}    IN    @{py_list}
                    # 记录当前遍历的值
                    Log    当前遍历的值: ${value}    INFO
                    
                    ${variable}=    Get From Dictionary    ${value}    variable
        
                    # 使用更安全的方式从字典中获取值
                    ${var_value}=    Run Keyword If    "${result_dict}" == ""    Set Variable    ${None}    \
                    ...    ELSE    Get From Dictionary    ${result_dict}    ${variable}
                    
                    # 记录字典中的值
                    Log    当前遍历的值: ${var_value}    INFO
                    
                    # 解析多格式响应
                    ${var_value_dict}=    Set Variable    ${var_value}
                    
                    # 记录var_value_dict中的值
                    Log    当前遍历的值: ${var_value_dict}    INFO
                    
                    ${result_in}=    Get From Dictionary    ${var_value_dict}    result
        
                    # 动态构造变量名
                    ${var_name}=    Catenate   SEPARATOR=    ${variable}
        
                    # 设置全局变量
                    Set Suite Variable    ${${var_name}}    ${result_in}
                    
                END
                ${response_text}=    Set Variable    ${json_value}
            
            END
            """,
        ),
    }


def get_rpa_control_components():
    """获取RPA Framework控制流组件"""
    return {
        "workflow_start": ComponentDefinition(
            type="workflow_start",
            label="开始",
            description="工作流的开始节点，标识流程入口点",
            category="control",
            icon="VideoPlay",
            config_schema={
                "workflow_name": {"type": "string", "required": False, "default": ""},
                "description": {"type": "string", "required": False, "default": ""},
                "log_level": {
                    "type": "string",
                    "default": "INFO",
                    "options": ["DEBUG", "INFO", "WARN", "ERROR"],
                },
                "confirm_start": {"type": "boolean", "default": False},
                "timeout": {"type": "number", "default": 300, "min": 1, "max": 3600},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["workflow_context"],
            robot_template="""
    # === 工作流开始 ===
    {% if workflow_name %}
    Log    开始执行工作流: ${workflow_name}    ${log_level}
    {% else %}
    Log    开始执行工作流    ${log_level}
    {% endif %}

    {% if description %}
    Log    工作流描述: ${description}    ${log_level}
    {% endif %}

    # 记录开始时间
    ${start_time}=    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    Set Suite Variable    ${WORKFLOW_START_TIME}    ${start_time}
    Log    工作流开始时间: ${start_time}    ${log_level}

    # 初始化工作流上下文
    &{workflow_context}=    Create Dictionary
    ...    start_time=${start_time}
    ...    status=running
    ...    name=${workflow_name}
    ...    description=${description}
    Set Suite Variable    ${WORKFLOW_CONTEXT}    ${workflow_context}

    {% if confirm_start %}
    Log    等待用户确认开始执行...    ${log_level}
    # 在实际实现中，这里可以添加用户确认逻辑
    {% endif %}

    Log    工作流初始化完成，开始执行任务    ${log_level}
            """,
        ),
        "workflow_end": ComponentDefinition(
            type="workflow_end",
            label="结束",
            description="工作流的结束节点，标识流程完成",
            category="control",
            icon="CircleClose",
            config_schema={
                "status": {
                    "type": "string",
                    "default": "success",
                    "options": ["success", "failure", "cancelled", "timeout"],
                },
                "message": {"type": "string", "required": False, "default": ""},
                "return_data": {"type": "string", "required": False, "default": ""},
                "cleanup": {"type": "boolean", "default": True},
                "log_summary": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["workflow_context"],
            robot_template="""
    # === 工作流结束 ===
    ${end_time}=    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    Set Suite Variable    ${WORKFLOW_END_TIME}    ${end_time}

    # 计算执行时间
    ${duration}=    Subtract Date From Date    ${end_time}    ${WORKFLOW_START_TIME}
    Set Suite Variable    ${WORKFLOW_DURATION}    ${duration}

    # 更新工作流上下文
    Set To Dictionary    ${WORKFLOW_CONTEXT}
    ...    end_time=${end_time}
    ...    duration=${duration}
    ...    status=${status}
    ...    message=${message}

    {% if log_summary %}
    Log    ========== 工作流执行摘要 ==========    INFO
    Log    开始时间: ${WORKFLOW_START_TIME}    INFO
    Log    结束时间: ${end_time}    INFO
    Log    执行时长: ${duration} 秒    INFO
    Log    执行状态: ${status}    INFO
    {% if message %}
    Log    结束消息: ${message}    INFO
    {% endif %}
    Log    =====================================    INFO
    {% endif %}

    {% if status == 'success' %}
    Log    ✅ 工作流执行成功完成    INFO
    {% elif status == 'failure' %}
    Log    ❌ 工作流执行失败    ERROR
    {% elif status == 'cancelled' %}
    Log    ⚠️ 工作流执行被取消    WARN
    {% elif status == 'timeout' %}
    Log    ⏰ 工作流执行超时    WARN
    {% endif %}

    {% if return_data %}
    Log    返回数据: ${return_data}    INFO
    Set Suite Variable    ${WORKFLOW_RETURN_DATA}    ${return_data}
    {% endif %}

    {% if cleanup %}
    # 执行清理操作
    Log    执行工作流清理操作...    INFO
    # 这里可以添加具体的清理逻辑，如关闭浏览器、清理临时文件等
    {% endif %}

    # 根据状态设置最终结果
    {% if status == 'failure' %}
    Fail    工作流执行失败: ${message}
    {% elif status == 'timeout' %}
    Fail    工作流执行超时: ${message}
    {% endif %}
            """,
        ),
        "wait": ComponentDefinition(
            type="wait",
            label="等待",
            description="暂停执行指定的时间",
            category="control",
            icon="Timer",
            config_schema={
                "duration": {"type": "number", "required": True},
                "unit": {"type": "string", "default": "seconds"},
            },
            robot_template="    Sleep    ${duration}${unit}",
        ),
        "set_variable": ComponentDefinition(
            type="set_variable",
            label="设置变量",
            description="设置一个变量的值",
            category="control",
            icon="Setting",
            config_schema={
                "variable_name": {"type": "string", "required": True},
                "value": {"type": "string", "required": True},
                "value_type": {"type": "string", "default": "string"},
                "scope": {"type": "string", "default": "local"},
                "description": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["variable"],
            robot_template="""
    # Set variable: ${variable_name}
    {% if value_type == 'json' %}
    ${temp_value}=    Evaluate    ${value}
    Set Suite Variable    ${${variable_name}}    ${temp_value}
    {% elif value_type == 'number' %}
    ${temp_value}=    Convert To Number    ${value}
    Set Suite Variable    ${${variable_name}}    ${temp_value}
    {% elif value_type == 'boolean' %}
    ${temp_value}=    Convert To Boolean    ${value}
    Set Suite Variable    ${${variable_name}}    ${temp_value}
    {% else %}
    Set Suite Variable    ${${variable_name}}    ${value}
    {% endif %}
    Log    Variable '${variable_name}' set to: ${${variable_name}}    INFO
            """,
        ),
        "user_input": ComponentDefinition(
            type="user_input",
            label="用户输入",
            description="运行时弹出对话框让用户输入文本，可选择存储为变量",
            category="control",
            icon="User",
            config_schema={
                "prompt_message": {"type": "string", "required": True},
                "default_value": {"type": "string", "required": False},
                "input_type": {"type": "string", "default": "text"},
                "multiline": {"type": "boolean", "default": False},
                "required": {"type": "boolean", "default": True},
                "hidden": {"type": "boolean", "default": False},
                "variable_name": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["user_input"],
            robot_template="""
    # Process user input
    Log    Processing user input for: ${prompt_message}    INFO

    {% if variable_name %}
    # Store input in variable: ${variable_name}
    {% if user_provided_value is defined %}
    Set Suite Variable    ${${variable_name}}    ${user_provided_value}
    Log    User input stored in variable '${variable_name}': ${user_provided_value}    INFO
    {% elif default_value %}
    Set Suite Variable    ${${variable_name}}    ${default_value}
    Log    Default value stored in variable '${variable_name}': ${default_value}    INFO
    {% else %}
    Set Suite Variable    ${${variable_name}}
    Log    Empty value stored in variable '${variable_name}'    WARN
    {% endif %}

    {% if required %}
    Should Not Be Empty    ${${variable_name}}    msg=User input is required
    {% endif %}

    Log    Variable '${variable_name}' is ready for use    INFO
    {% else %}
    # No variable storage, just log the input
    {% if user_provided_value is defined %}
    Log    User provided input: ${user_provided_value}    INFO
    {% elif default_value %}
    Log    Using default input: ${default_value}    INFO
    {% else %}
    Log    No input provided    INFO
    {% endif %}
    {% endif %}
            """,
        ),
        "user_choice": ComponentDefinition(
            type="user_choice",
            label="用户选择",
            description="运行时弹出选择对话框让用户选择选项",
            category="control",
            icon="Select",
            config_schema={
                "variable_name": {"type": "string", "required": True},
                "prompt_message": {"type": "string", "required": True},
                "choices": {"type": "string", "required": True},
                "default_choice": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["user_choice"],
            robot_template="""
    # Get user choice: ${variable_name}
    Log    Processing user choice for: ${prompt_message}    INFO

    @{choice_list}=    Split String    ${choices}    ,

    # Use user-provided choice if available
    {% if user_provided_value is defined %}
    Set Suite Variable    ${${variable_name}}    ${user_provided_value}
    Log    Using user-selected value: ${user_provided_value}    INFO
    {% elif default_choice %}
    Set Suite Variable    ${${variable_name}}    ${default_choice}
    Log    Using default choice: ${default_choice}    INFO
    {% else %}
    Set Suite Variable    ${${variable_name}}    @{choice_list}[0]
    Log    Using first available choice: @{choice_list}[0]    INFO
    {% endif %}

    Log    Variable '${variable_name}' set to: ${${variable_name}}    INFO
            """,
        ),
        "user_confirm": ComponentDefinition(
            type="user_confirm",
            label="用户确认",
            description="运行时弹出确认对话框让用户确认操作",
            category="control",
            icon="QuestionFilled",
            config_schema={
                "variable_name": {"type": "string", "required": True},
                "prompt_message": {"type": "string", "required": True},
                "default_answer": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["user_confirm"],
            robot_template="""
    # Get user confirmation: ${variable_name}
    Log    Processing user confirmation for: ${prompt_message}    INFO

    # Use user-provided confirmation if available
    {% if user_provided_value is defined %}
    Set Suite Variable    ${${variable_name}}    ${user_provided_value}
    Log    Using user confirmation: ${user_provided_value}    INFO
    {% else %}
    Set Suite Variable    ${${variable_name}}    ${default_answer}
    Log    Using default confirmation: ${default_answer}    INFO
    {% endif %}

    Log    Variable '${variable_name}' set to: ${${variable_name}}    INFO
            """,
        ),
        "log_message": ComponentDefinition(
            type="log_message",
            label="记录日志",
            description="输出一条日志消息",
            category="control",
            icon="Document",
            config_schema={
                "message": {"type": "string", "required": True},
                "level": {"type": "string", "default": "INFO"},
            },
            inputs=["message"],
            robot_template="    Log    ${message}    ${level}",
        ),
        "condition": ComponentDefinition(
            type="condition",
            label="条件判断",
            description="根据条件执行不同的分支",
            category="control",
            icon="Branch",
            config_schema={
                # "condition": {"type": "string", "required": True},
                # "true_action": {"type": "string", "required": False},
                # "false_action": {"type": "string", "required": False}
            },
            robot_template="""
    # IF    ${condition}
    #     {% if true_action %}${true_action}{% else %}Log    Condition is True{% endif %}
    # ELSE
    #     {% if false_action %}${false_action}{% else %}Log    Condition is False{% endif %}
    # END
    #         """,
        ),
    }


def get_rpa_advanced_components():
    """获取RPA Framework高级功能组件"""
    return {
        "csv_read": ComponentDefinition(
            type="csv_read",
            label="读取CSV文件",
            description="从CSV文件中读取数据",
            category="advanced",
            icon="Document",
            config_schema={
                "path": {"type": "string", "required": True},
                "delimiter": {"type": "string", "default": ","},
                "encoding": {"type": "string", "default": "utf-8"},
                "header": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["csv_data"],
            robot_template="""
    ${csv_data}=    Read Table From Csv    ${path}    delimiter=${delimiter}    encoding=${encoding}    header=${header}
            """,
        ),
        "csv_write": ComponentDefinition(
            type="csv_write",
            label="写入CSV文件",
            description="将数据写入CSV文件",
            category="advanced",
            icon="EditPen",
            config_schema={
                "path": {"type": "string", "required": True},
                "data": {"type": "string", "required": True},
                "delimiter": {"type": "string", "default": ","},
                "encoding": {"type": "string", "default": "utf-8"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["csv_data"],
            robot_template="""
    Write Table To Csv    ${data}    ${path}    delimiter=${delimiter}    encoding=${encoding}
            """,
        ),
        "json_parse": ComponentDefinition(
            type="json_parse",
            label="解析JSON",
            description="解析JSON字符串为对象",
            category="advanced",
            icon="Code",
            config_schema={
                "json_string": {"type": "string", "required": True},
                "path": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["json_data"],
            robot_template="""
    {% if path %}
    ${json_data}=    Load Json From File    ${path}
    {% else %}
    ${json_data}=    Convert String To Json    ${json_string}
    {% endif %}
            """,
        ),
        "json_create": ComponentDefinition(
            type="json_create",
            label="创建JSON",
            description="将数据转换为JSON字符串",
            category="advanced",
            icon="Code",
            config_schema={
                "data": {"type": "string", "required": True},
                "indent": {"type": "number", "default": 2},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["data"],
            outputs=["json_string"],
            robot_template="""
    ${json_string}=    Convert Json To String    ${data}    indent=${indent}
            """,
        ),
        "file_copy": ComponentDefinition(
            type="file_copy",
            label="复制文件",
            description="复制文件到指定位置",
            category="advanced",
            icon="CopyDocument",
            config_schema={
                "source": {"type": "string", "required": True},
                "destination": {"type": "string", "required": True},
                "overwrite": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Copy File    ${source}    ${destination}    overwrite=${overwrite}
            """,
        ),
        "file_move": ComponentDefinition(
            type="file_move",
            label="移动文件",
            description="移动文件到指定位置",
            category="advanced",
            icon="FolderOpened",
            config_schema={
                "source": {"type": "string", "required": True},
                "destination": {"type": "string", "required": True},
                "overwrite": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Move File    ${source}    ${destination}    overwrite=${overwrite}
            """,
        ),
        "file_delete": ComponentDefinition(
            type="file_delete",
            label="删除文件",
            description="删除指定文件",
            category="advanced",
            icon="Delete",
            config_schema={
                "path": {"type": "string", "required": True},
                "confirm": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    {% if confirm %}
    File Should Exist    ${path}
    {% endif %}
    Remove File    ${path}
            """,
        ),
        "folder_create": ComponentDefinition(
            type="folder_create",
            label="创建文件夹",
            description="创建新文件夹",
            category="advanced",
            icon="FolderAdd",
            config_schema={
                "path": {"type": "string", "required": True},
                "parents": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Create Directory    ${path}    parents=${parents}
            """,
        ),
        "zip_create": ComponentDefinition(
            type="zip_create",
            label="创建压缩包",
            description="将文件或文件夹压缩为ZIP",
            category="advanced",
            icon="Files",
            config_schema={
                "source": {"type": "string", "required": True},
                "archive_name": {"type": "string", "default": "archive.zip"},
                "compression_level": {"type": "number", "default": 6},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["archive_path"],
            robot_template="""
    ${archive_path}=    Archive Folder With Zip    ${source}    ${archive_name}    compression=${compression_level}
            """,
        ),
        "zip_extract": ComponentDefinition(
            type="zip_extract",
            label="解压缩包",
            description="解压ZIP文件",
            category="advanced",
            icon="FolderOpened",
            config_schema={
                "archive_path": {"type": "string", "required": True},
                "extract_to": {"type": "string", "required": True},
                "overwrite": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Extract Archive    ${archive_path}    ${extract_to}    overwrite=${overwrite}
            """,
        ),
    }


def get_rpa_system_components():
    """获取RPA Framework系统操作组件"""
    return {
        "run_command": ComponentDefinition(
            type="run_command",
            label="执行命令",
            description="执行系统命令",
            category="system",
            icon="Terminal",
            config_schema={
                "command": {"type": "string", "required": True},
                "shell": {"type": "boolean", "default": True},
                "timeout": {"type": "number", "default": 30},
                "capture_output": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["command_output", "return_code"],
            robot_template="""
    ${result}=    Run Process    ${command}    shell=${shell}    timeout=${timeout}
    ${command_output}=    Set Variable    ${result.stdout}
    ${return_code}=    Set Variable    ${result.rc}
            """,
        ),
        "python_execute": ComponentDefinition(
            type="python_execute",
            label="执行Python代码",
            description="执行Python代码片段或脚本文件",
            category="system",
            icon="Code",
            config_schema={
                "code": {"type": "string", "required": False},
                "script_file": {"type": "string", "required": False},
                "python_path": {"type": "string", "default": "python"},
                "timeout": {"type": "number", "default": 60},
                "capture_output": {"type": "boolean", "default": True},
                "working_directory": {"type": "string", "required": False},
                "environment_vars": {"type": "string", "required": False},
                "arguments": {"type": "string", "required": False},
                "output_variable": {"type": "string", "required": False},
                "error_variable": {"type": "string", "required": False},
                "return_code_variable": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["python_output", "return_code", "error_output"],
            robot_template="""
    # Python代码执行器
    Log    开始执行Python代码    INFO

    IF    $code != "" and $script_file != ""
        Fail    不能同时指定代码和脚本文件，请选择其中一种方式
    ELSE IF    $code == "" and $script_file == ""
        Fail    必须指定要执行的Python代码或脚本文件
    END

    # 执行Python代码片段
    Log    执行Python代码片段    INFO
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S_%f
    # 设置环境变量强制UTF-8输出
    Set Environment Variable    PYTHONIOENCODING    utf-8
    ${result}=    Execute Python Script    ${code}    &{GLOBAL_VARIABLES}    &{WORKFLOW_VARIABLES}

        
    log  ${result}
    # 处理执行结果
    ${python_output}=    Set Variable    ${result.stdout}
    ${error_output}=    Set Variable    ${result.stderr}
    ${return_code}=    Set Variable    ${result.rc}

   

    # 记录执行结果
    Log    Python执行返回码: ${return_code}    INFO
    ${output_length}=    Get Length    ${python_output}
    Log    Python输出长度: ${output_length}    INFO
    Log    Python原始输出: ${python_output}    INFO
    Log    Python输出repr: ${python_output.__repr__()}    INFO
    IF    ${capture_output}
        Log    Python输出:\\n${python_output}    INFO
        IF    $error_output != ""
            Log    Python错误输出:\\n${error_output}    WARN
        END
    END

    # 如果结果是字典类型，则结果拆成多个变量并赋值
    IF    $python_output.__class__.__name__ == 'dict'
        ${keys}=    Get Dictionary Keys    ${python_output}
        ${values}=    Get Dictionary Values    ${python_output}
        ${key_count}=    Get Length    ${keys}
        FOR    ${index}    IN RANGE    ${key_count}
            ${key}=    Get From List    ${keys}    ${index}
            ${value}=    Get From List    ${values}    ${index}
            Set Suite Variable    ${${key}}    ${value}
            Log    Python输出已存储到变量: ${key}    INFO
        END
    END

    IF    $error_variable != ""
        Set Suite Variable    ${${error_variable}}    ${error_output}
        Log    Python错误信息已存储到变量: ${error_variable}    INFO
    END

    IF    $return_code_variable != ""
        Set Suite Variable    ${${return_code_variable}}    ${return_code}
        Log    Python返回码已存储到变量: ${return_code_variable}    INFO
    END

    # 检查执行是否成功
    IF    ${return_code} != 0
        Log    Python代码执行失败，返回码: ${return_code}    ERROR
        IF    $error_output != ""
            Log    错误信息: ${error_output}    ERROR
        END
    ELSE
        Log    Python代码执行成功    INFO
    END
            """,
        ),
        "python_evaluate": ComponentDefinition(
            type="python_evaluate",
            label="Python表达式求值",
            description="执行Python表达式并返回结果",
            category="system",
            icon="Calculator",
            config_schema={
                "expression": {"type": "string", "required": True},
                "variables": {"type": "string", "required": False},
                "modules": {"type": "string", "required": False},
                "result_variable": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["eval_result"],
            robot_template="""
    # Python表达式求值
    Log    执行Python表达式: ${expression}    INFO

    IF    $modules != ""
        # 导入指定模块
        @{module_list}=    Split String    ${modules}    ,
        FOR    ${module}    IN    @{module_list}
            ${module_clean}=    Strip String    ${module}
            Log    导入模块: ${module_clean}    DEBUG
            Import Library    ${module_clean}
        END
    END

    IF    $variables != ""
        # 设置变量上下文
        ${eval_result}=    Evaluate    ${expression}    ${variables}
    ELSE
        ${eval_result}=    Evaluate    ${expression}
    END

    Log    表达式求值结果: ${eval_result}    INFO

    # 将结果存储到指定变量
    Set Suite Variable    ${${result_variable}}    ${eval_result}
    Log    结果已存储到变量: ${result_variable}    INFO
            """,
        ),
        "python_import": ComponentDefinition(
            type="python_import",
            label="导入Python模块",
            description="动态导入Python模块或库",
            category="system",
            icon="Package",
            config_schema={
                "module_name": {"type": "string", "required": True},
                "alias": {"type": "string", "required": False},
                "from_module": {"type": "string", "required": False},
                "install_if_missing": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["import_success"],
            robot_template="""
    # Python模块导入
    IF    $from_module != ""
        Log    从模块 ${from_module} 导入 ${module_name}    INFO
    ELSE
        Log    导入模块: ${module_name}    INFO
    END

    ${import_success}=    Set Variable    ${False}

    TRY
        IF    $from_module != ""
            IF    $alias != ""
                Import Library    ${from_module}.${module_name}    WITH NAME    ${alias}
            ELSE
                Import Library    ${from_module}.${module_name}
            END
        ELSE
            IF    $alias != ""
                Import Library    ${module_name}    WITH NAME    ${alias}
            ELSE
                Import Library    ${module_name}
            END
        END
        ${import_success}=    Set Variable    ${True}
        Log    模块导入成功    INFO
    EXCEPT    AS    ${error}
        Log    模块导入失败: ${error}    WARN
        IF    ${install_if_missing}
            Log    尝试安装缺失的模块...    INFO
            ${install_result}=    Run Process    pip    install    ${module_name}    timeout=120
            IF    ${install_result.rc} == 0
                Log    模块安装成功，重新尝试导入    INFO
                TRY
                    IF    $from_module != ""
                        IF    $alias != ""
                            Import Library    ${from_module}.${module_name}    WITH NAME    ${alias}
                        ELSE
                            Import Library    ${from_module}.${module_name}
                        END
                    ELSE
                        IF    $alias != ""
                            Import Library    ${module_name}    WITH NAME    ${alias}
                        ELSE
                            Import Library    ${module_name}
                        END
                    END
                    ${import_success}=    Set Variable    ${True}
                    Log    模块重新导入成功    INFO
                EXCEPT    AS    ${retry_error}
                    Log    模块重新导入仍然失败: ${retry_error}    ERROR
                END
            ELSE
                Log    模块安装失败: ${install_result.stderr}    ERROR
            END
        END
    END
            """,
        ),
        "javascript_execute": ComponentDefinition(
            type="javascript_execute",
            label="执行JavaScript代码",
            description="通过 Node.js 执行 JavaScript 代码片段或脚本文件",
            category="system",
            icon="Code",
            config_schema={
                "code": {"type": "string", "required": False},  # JS 代码片段
                "script_file": {"type": "string", "required": False},  # JS 脚本路径
                "node_path": {"type": "string", "default": "node"},  # Node.js 执行路径
                "timeout": {"type": "number", "default": 60},  # 超时时间（秒）
                "capture_output": {
                    "type": "boolean",
                    "default": True,
                },  # 是否记录控制台输出
                "working_directory": {"type": "string", "required": False},  # 执行目录
                "arguments": {"type": "string", "required": False},  # 传递给脚本的参数
                "output_variable": {
                    "type": "string",
                    "required": False,
                },  # 保存标准输出变量
                "error_variable": {
                    "type": "string",
                    "required": False,
                },  # 保存错误输出变量
                "return_code_variable": {
                    "type": "string",
                    "required": False,
                },  # 保存返回码变量
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["js_output", "return_code", "error_output"],
            robot_template="""
                # 启动 JS 执行任务
                Log    开始执行 JavaScript 代码    level=INFO

                # 检查不能同时传入 code 和 script_file
                IF    ($code != None and $code != "" and $code != "None") and ($script_file != None and $script_file != "None" and $script_file != "")
                    Fail    不能同时指定 JavaScript 代码和脚本文件，请选择其一
                ELSE IF    ($code == None or $code == "None" or $code == "") and ($script_file == None or $script_file == "None" or $script_file == "")
                    Fail    必须提供 JavaScript 代码或脚本文件
                END

                # 调用 Python 自定义关键字执行 JavaScript
                ${js_output}    ${error_output}    ${return_code} =    Run Javascript
                ...    code=${code}
                ...    script_file=${script_file}
                ...    node_path=${node_path}
                ...    timeout=${timeout}
                ...    capture_output=${capture_output}
                ...    working_directory=${working_directory}
                ...    arguments=${arguments}

                # 输出日志记录
                Log    Node.js 返回码: ${return_code}    level=INFO

                IF    ${capture_output}
                    Log    JavaScript 输出: ${js_output}    level=INFO
                    IF    ($error_output != None and $error_output != "")
                        Log    JavaScript 错误输出: ${error_output}    level=WARN
                    END
                END

                # ========= 变量赋值（输出内容保存到流程变量中） ==========
                IF    $output_variable != None and $output_variable != ""
                    ${cleaned_output}=    Strip String    ${js_output}
                    Set Suite Variable    ${${output_variable}}    ${cleaned_output}
                    Log    JavaScript 输出结果已存储到变量: ${output_variable}    level=INFO
                END

                IF    $error_variable != None and $error_variable != ""
                    Set Suite Variable    ${${error_variable}}    ${error_output}
                    Log    错误信息已存储到变量: ${error_variable}    level=INFO
                END

                IF    $return_code_variable != None and $return_code_variable != ""
                    Set Suite Variable    ${${return_code_variable}}    ${return_code}
                    Log    返回码已存储到变量: ${return_code_variable}    level=INFO
                END

                # ========= 判断执行成功与否 ==========
                IF    ${return_code} != 0
                    Log    JavaScript 执行失败，返回码: ${return_code}    level=ERROR
                    IF    $error_output != None and $error_output != ""
                        Log    错误信息: ${error_output}    level=ERROR
                    END
                ELSE
                    Log    JavaScript 执行成功    level=INFO
                END

            """,
        ),
        "environment_get": ComponentDefinition(
            type="environment_get",
            label="获取环境变量",
            description="获取系统环境变量的值",
            category="system",
            icon="Setting",
            config_schema={
                "variable_name": {"type": "string", "required": True},
                "default_value": {"type": "string", "default": ""},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["env_value"],
            robot_template="""
    ${env_value}=    Get Environment Variable    ${variable_name}    ${default_value}
            """,
        ),
        "environment_set": ComponentDefinition(
            type="environment_set",
            label="设置环境变量",
            description="设置系统环境变量",
            category="system",
            icon="Setting",
            config_schema={
                "variable_name": {"type": "string", "required": True},
                "value": {"type": "string", "required": True},
                "permanent": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Set Environment Variable    ${variable_name}    ${value}
            """,
        ),
        "process_list": ComponentDefinition(
            type="process_list",
            label="列出进程",
            description="获取系统运行进程列表",
            category="system",
            icon="List",
            config_schema={
                "filter_name": {"type": "string", "required": False},
                "include_children": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["process_list"],
            robot_template="""
    ${process_list}=    Get Running Processes    ${filter_name}
            """,
        ),
        "process_kill": ComponentDefinition(
            type="process_kill",
            label="终止进程",
            description="终止指定进程",
            category="system",
            icon="Close",
            config_schema={
                "process_name": {"type": "string", "required": False},
                "process_id": {"type": "string", "required": False},
                "force": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    {% if process_id %}
    Terminate Process    ${process_id}    kill=${force}
    {% else %}
    Terminate Process    ${process_name}    kill=${force}
    {% endif %}
            """,
        ),
    }


def get_rpa_image_components():
    """获取RPA Framework图像处理组件"""
    return {
        "image_find": ComponentDefinition(
            type="image_find",
            label="查找图像",
            description="在屏幕或图像中查找指定图像",
            category="image",
            icon="Search",
            config_schema={
                "template": {"type": "string", "required": True},
                "source": {"type": "string", "required": False},
                "confidence": {"type": "number", "default": 0.8},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["location"],
            robot_template="""
    ${location}=    Find Template    ${template}    source=${source}    confidence=${confidence}
            """,
        ),
        "image_click": ComponentDefinition(
            type="image_click",
            label="点击图像",
            description="点击屏幕上找到的图像",
            category="image",
            icon="Mouse",
            config_schema={
                "template": {"type": "string", "required": True},
                "confidence": {"type": "number", "default": 0.8},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Click Template    ${template}    confidence=${confidence}
            """,
        ),
        "image_crop": ComponentDefinition(
            type="image_crop",
            label="裁剪图像",
            description="裁剪图像的指定区域",
            category="image",
            icon="Crop",
            config_schema={
                "source": {"type": "string", "required": True},
                "left": {"type": "number", "required": True},
                "top": {"type": "number", "required": True},
                "right": {"type": "number", "required": True},
                "bottom": {"type": "number", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["cropped_image"],
            robot_template="""
    ${cropped_image}=    Crop Image    ${source}    ${left}    ${top}    ${right}    ${bottom}
            """,
        ),
    }


def get_rpa_office_components():
    """获取RPA Framework Office应用组件"""
    return {
        "excel_app_open": ComponentDefinition(
            type="excel_app_open",
            label="打开Excel应用",
            description="启动Excel桌面应用程序",
            category="office",
            icon="Application",
            config_schema={
                "visible": {"type": "boolean", "default": True},
                "display_alerts": {"type": "boolean", "default": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["excel_app"],
            robot_template="""
    Open Application    visible=${visible}    display_alerts=${display_alerts}
            """,
        ),
        "word_app_open": ComponentDefinition(
            type="word_app_open",
            label="打开Word应用",
            description="启动Word桌面应用程序",
            category="office",
            icon="Application",
            config_schema={
                "visible": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["word_app"],
            robot_template="""
    Open Application    visible=${visible}
            """,
        ),
        "outlook_app_open": ComponentDefinition(
            type="outlook_app_open",
            label="打开Outlook应用",
            description="启动Outlook桌面应用程序",
            category="office",
            icon="Message",
            config_schema={
                "visible": {"type": "boolean", "default": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["outlook_app"],
            robot_template="""
    Open Application    visible=${visible}
            """,
        ),
    }


def get_rpa_cloud_components():
    """获取RPA Framework云服务组件"""
    return {
        "aws_s3_upload": ComponentDefinition(
            type="aws_s3_upload",
            label="上传到S3",
            description="上传文件到AWS S3存储桶",
            category="cloud",
            icon="Upload",
            config_schema={
                "bucket": {"type": "string", "required": True},
                "key": {"type": "string", "required": True},
                "filename": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Upload File    ${bucket}    ${key}    ${filename}
            """,
        ),
        "aws_s3_download": ComponentDefinition(
            type="aws_s3_download",
            label="从S3下载",
            description="从AWS S3存储桶下载文件",
            category="cloud",
            icon="Download",
            config_schema={
                "bucket": {"type": "string", "required": True},
                "key": {"type": "string", "required": True},
                "filename": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Download File    ${bucket}    ${key}    ${filename}
            """,
        ),
        "azure_blob_upload": ComponentDefinition(
            type="azure_blob_upload",
            label="上传到Azure Blob",
            description="上传文件到Azure Blob存储",
            category="cloud",
            icon="Upload",
            config_schema={
                "container": {"type": "string", "required": True},
                "blob_name": {"type": "string", "required": True},
                "file_path": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Upload Blob    ${container}    ${blob_name}    ${file_path}
            """,
        ),
    }


def get_rpa_security_components():
    """获取RPA Framework安全组件"""
    return {
        "crypto_hash": ComponentDefinition(
            type="crypto_hash",
            label="计算哈希",
            description="计算文件或字符串的哈希值",
            category="security",
            icon="Lock",
            config_schema={
                "data": {"type": "string", "required": True},
                "algorithm": {"type": "string", "default": "sha256"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["hash_value"],
            robot_template="""
    ${hash_value}=    Hash String    ${data}    ${algorithm}
            """,
        ),
        "crypto_encrypt": ComponentDefinition(
            type="crypto_encrypt",
            label="加密数据",
            description="使用指定算法加密数据",
            category="security",
            icon="Lock",
            config_schema={
                "data": {"type": "string", "required": True},
                "key": {"type": "string", "required": True},
                "algorithm": {"type": "string", "default": "AES"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["encrypted_data"],
            robot_template="""
    ${encrypted_data}=    Encrypt String    ${data}    ${key}    ${algorithm}
            """,
        ),
        "mfa_generate": ComponentDefinition(
            type="mfa_generate",
            label="生成OTP",
            description="生成一次性密码",
            category="security",
            icon="Key",
            config_schema={
                "secret": {"type": "string", "required": True},
                "algorithm": {"type": "string", "default": "SHA1"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["otp_code"],
            robot_template="""
    ${otp_code}=    Get Time Based OTP    ${secret}    ${algorithm}
            """,
        ),
    }


def get_all_rpa_components():
    """获取所有RPA Framework组件"""
    components = {}
    components.update(get_rpa_browser_components())
    components.update(get_rpa_desktop_components())
    components.update(get_rpa_file_components())
    components.update(get_rpa_email_components())
    components.update(get_rpa_database_components())
    components.update(get_rpa_http_components())
    components.update(get_rpa_control_components())
    components.update(get_rpa_advanced_components())
    components.update(get_rpa_system_components())
    components.update(get_rpa_image_components())
    components.update(get_rpa_office_components())
    components.update(get_rpa_cloud_components())
    components.update(get_rpa_security_components())
    components.update(get_rpa_network_components())
    components.update(get_rpa_enterprise_components())
    components.update(get_rpa_ai_components())
    components.update(get_rpa_calendar_components())
    components.update(get_data_handler_components())
    components.update(get_rpa_other_components())
    return components


#  components.update(get_rpa_recognition_components())


def get_rpa_network_components():
    """获取RPA Framework网络组件"""
    return {
        "notifier_send": ComponentDefinition(
            type="notifier_send",
            label="消息通知",
            description="发送消息通知",
            category="network",
            icon="Message",
            config_schema={
                "title": {"type": "string", "required": True},
                "content": {"type": "string", "required": True},
                "link": {"type": "string", "required": False, "default": ""},
                "users": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
                "methods": {"type": "array", "required": True},
            },
            outputs=["push_result"],
            robot_template="""
    ${send_result}=    Notifier Send    ${title}    ${content}    ${users}     ${methods}    ${link}
            """,
        ),
        "ftp_connect": ComponentDefinition(
            type="ftp_connect",
            label="连接FTP",
            description="连接到FTP服务器",
            category="network",
            icon="Connection",
            config_schema={
                "host": {"type": "string", "required": True},
                "username": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "port": {"type": "number", "default": 21},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["ftp_connection"],
            robot_template="""
    ${ftp_connection}=    FTP Connect    ${host}    ${username}    ${password}    ${port}
            """,
        ),
        "ftp_upload": ComponentDefinition(
            type="ftp_upload",
            label="FTP上传",
            description="上传文件到FTP服务器",
            category="network",
            icon="Upload",
            config_schema={
                "local_file": {"type": "string", "required": True},
                "remote_file": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            inputs=["ftp_connection"],
            robot_template="""
    Upload File    ${local_file}    ${remote_file}
            """,
        ),
        "slack_send": ComponentDefinition(
            type="slack_send",
            label="发送Slack消息",
            description="发送消息到Slack频道",
            category="network",
            icon="Message",
            config_schema={
                "token": {"type": "string", "required": True},
                "channel": {"type": "string", "required": True},
                "message": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
    Send Message    ${channel}    ${message}    token=${token}
            """,
        ),
        #     "notifier_send": ComponentDefinition(
        #         type="notifier_send",
        #         label="发送通知",
        #         description="通过多种服务发送通知",
        #         category="network",
        #         icon="Bell",
        #         config_schema={
        #             "service": {"type": "string", "required": True},
        #             "message": {"type": "string", "required": True},
        #             "recipient": {"type": "string", "required": True}
        #         },
        #         robot_template="""
        # Notify    ${service}    ${message}    ${recipient}
        #         """
        #     )
    }


def get_rpa_enterprise_components():
    """获取RPA Framework企业系统组件"""
    return {
        "salesforce_query": ComponentDefinition(
            type="salesforce_query",
            label="Salesforce查询",
            description="查询Salesforce数据",
            category="enterprise",
            icon="Search",
            config_schema={
                "query": {"type": "string", "required": True},
                "username": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "security_token": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["query_result"],
            robot_template="""
    Auth With Token    ${username}    ${password}    ${security_token}
    ${query_result}=    Salesforce Query    ${query}
            """,
        ),
        "sap_connect": ComponentDefinition(
            type="sap_connect",
            label="连接SAP",
            description="连接到SAP GUI客户端",
            category="enterprise",
            icon="Connection",
            config_schema={
                "connection": {"type": "string", "required": True},
                "client": {"type": "string", "required": True},
                "username": {"type": "string", "required": True},
                "password": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["sap_session"],
            robot_template="""
    ${sap_session}=    Connect To Server    ${connection}    ${client}    ${username}    ${password}
            """,
        ),
        "hubspot_get": ComponentDefinition(
            type="hubspot_get",
            label="获取HubSpot数据",
            description="从HubSpot获取数据",
            category="enterprise",
            icon="Download",
            config_schema={
                "api_key": {"type": "string", "required": True},
                "object_type": {"type": "string", "required": True},
                "object_id": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["hubspot_data"],
            robot_template="""
    Set Api Key    ${api_key}
    ${hubspot_data}=    Get Object    ${object_type}    ${object_id}
            """,
        ),
    }


def get_rpa_ai_components():
    """获取RPA Framework AI组件"""
    return {
        "text_to_speech": ComponentDefinition(
            type="text_to_speech",
            label="语音播报",
            description="朗读文本",
            category="ai",
            icon="Speech",
            config_schema={
                "input": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
            TEXT TO SPEECH    ${input}
            """,
        ),
        "ai_analyze": ComponentDefinition(
            type="ai_analyze",
            label="AI分析",
            description="使用AI进行分析",
            category="ai",
            icon="Robot",
            config_schema={
                "model": {"type": "string", "default": "qwen3-32b"},
                "question": {"type": "string", "required": True},
                "prompt": {"type": "string", "default": "回答问题", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["ai_analyze_response"],
            robot_template="""
           ${result}=    Ai Analyze Exec    ${question}    ${prompt}    ${NODE_ID}    ${execution_id}    ${TASK_ID}
           # 将结果存储到指定变量
           IF    $ai_analyze_response != ""
               Set Suite Variable    ${${ai_analyze_response}}    ${result}
               Log    AI分析结果内容: ${result}    INFO
           END
           # 结果存储到原始变量

          ${ai_analyze_response}=    Set Variable    ${result}
           """,
        ),
        "openai_chat": ComponentDefinition(
            type="openai_chat",
            label="OpenAI对话",
            description="使用OpenAI进行对话",
            category="ai",
            icon="Robot",
            config_schema={
                "api_key": {"type": "string", "required": True},
                "model": {"type": "string", "default": "gpt-3.5-turbo"},
                "prompt": {"type": "string", "required": True},
                "max_tokens": {"type": "number", "default": 150},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["ai_response"],
            robot_template="""
    Set Api Key    ${api_key}
    ${ai_response}=    Chat Completion Create    ${model}    ${prompt}    max_tokens=${max_tokens}
            """,
        ),
        "document_ai_extract": ComponentDefinition(
            type="document_ai_extract",
            label="AI文档提取",
            description="使用AI从文档中提取信息",
            category="ai",
            icon="Document",
            config_schema={
                "file_path": {"type": "string", "required": True},
                "extraction_type": {"type": "string", "default": "text"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["extracted_data"],
            robot_template="""
    ${extracted_data}=    Extract From Document    ${file_path}    ${extraction_type}
            """,
        ),
        "assistant_display": ComponentDefinition(
            type="assistant_display",
            label="显示助手界面",
            description="显示用户交互界面",
            category="ai",
            icon="User",
            config_schema={
                "title": {"type": "string", "required": True},
                "message": {"type": "string", "required": True},
                "buttons": {"type": "string", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["user_response"],
            robot_template="""
    ${user_response}=    Display Form    title=${title}    message=${message}    buttons=${buttons}
            """,
        ),
        "img_recognition": ComponentDefinition(
            type="img_recognition",
            label="图片识别",
            description="'根据提示词识别图片",
            category="img",
            icon="img",
            config_schema={
                "img": {"type": "array", "items": {"type": "string"}, "required": True},
                "prompt": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
            },
            outputs=["recognition_result"],
            robot_template="""
               ${respone}=    Image Recognition    ${img}    ${prompt}
                # 将结果存储到指定变量
               IF    $recognition_result != ""
                   Set Suite Variable    ${${recognition_result}}    ${respone}
                   Log    图片识别结果: ${respone}    INFO
               END
               ${recognition_result}=    Set Variable    ${respone}
               """,
        ),
        "invoice_recognition": ComponentDefinition(
            type="invoice_recognition",
            label="发票识别",
            description="'根据发票地址识别图片",
            category="img",
            icon="img",
            config_schema={
                "img": {"type": "string", "required": True},
                "ticket_type": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
            },
            outputs=["recognition_results"],
            robot_template="""
${response}=    Invoice Recognitions    ${img}    ${ticket_type}    ${NODE_ID}    ${execution_id}    ${TASK_ID}

IF    len($response) > 0

    Set Suite Variable    ${${recognition_results}}    ${response}
    Set Suite Variable    ${recognition_results}    ${response}
    Log    图片识别结果: ${response}    INFO
END

${recognition_results}=    Set Variable    ${response}
               """,
        ),
    }


def get_rpa_calendar_components():
    """获取RPA Framework日历组件"""
    return {
        "date_add": ComponentDefinition(
            type="date_add",
            label="日期加法",
            description="在日期上添加时间",
            category="calendar",
            icon="Calendar",
            config_schema={
                "date": {"type": "string", "required": True},
                "days": {"type": "number", "default": 0},
                "hours": {"type": "number", "default": 0},
                "minutes": {"type": "number", "default": 0},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["new_date"],
            robot_template="""
    ${new_date}=    Add Time To Date    ${date}    days=${days}    hours=${hours}    minutes=${minutes}
            """,
        ),
        "date_format": ComponentDefinition(
            type="date_format",
            label="格式化日期",
            description="格式化日期字符串",
            category="calendar",
            icon="Calendar",
            config_schema={
                "date": {"type": "string", "required": True},
                "format": {"type": "string", "default": "%Y-%m-%d"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["formatted_date"],
            robot_template="""
    ${formatted_date}=    Convert Date    ${date}    result_format=${format}
            """,
        ),
        "date_diff": ComponentDefinition(
            type="date_diff",
            label="日期差值",
            description="计算两个日期之间的差值",
            category="calendar",
            icon="Calculator",
            config_schema={
                "date1": {"type": "string", "required": True},
                "date2": {"type": "string", "required": True},
                "unit": {"type": "string", "default": "days"},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["date_difference"],
            robot_template="""
    ${date_difference}=    Subtract Date From Date    ${date1}    ${date2}    result_format=${unit}
            """,
        ),
    }


def get_data_handler_components():
    """获数据处理"""
    return {
        "variable_assignment": ComponentDefinition(
            type="variable_assignment",
            label="变量赋值",
            description="变量赋值",
            category="data_pocess",
            icon="data_pocess",
            config_schema={
                "variables": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            robot_template="""
            # 设置值到指定变量
            Assign Variables  ${variables}
        """,
        ),
        "text_template": ComponentDefinition(
            type="text_template",
            label="文本模板转化",
            description="支持变量替换的文本模板转化节点，可以将模板中的变量占位符替换为实际变量值",
            category="data_process",
            icon="data_pocess",
            config_schema={
                "text": {"type": "string", "required": True},
                "output_variable": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["output_variable"],
            robot_template="""
            # 文本模板转化生成
            Set Suite Variable    ${{ '{' }}{{ output_variable }}{{ '}' }}    ${text}
            ${output_variable}=    Set Variable    ${text}
            
        """,
        ),
        "add_time": ComponentDefinition(
            type="add_time",
            label="日期计算",
            description="日期计算，增加时间或者减少时间",
            category="data_process",
            icon="data_pocess",
            config_schema={
                "date": {"type": "string", "required": True},
                "method": {"type": "string", "required": True},
                "duration": {"type": "int", "required": True},
                "duration_unit": {"type": "int", "required": True},
                "response_date_variable": {"type": "string", "required": True},
                "response_week_variable": {"type": "string", "required": True},
                "response_timestamp_variable": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "error_handle": {"type": "string", "default": "stop"},
            },
            outputs=["newtime"],
            robot_template="""
            ${newtimestr}=     Time Calculation   ${date}    ${method}   ${duration}   ${duration_unit}

            ${weekday}=       Get Weekday    ${newtimestr}
            ${timestamp}=      Get Timestamp    ${newtimestr}
            IF    $response_date_variable != ''
                Set Suite Variable    ${{ '{' }}{{ response_date_variable }}{{ '}' }}    ${newtimestr}
                Log    获取时间: ${newtimestr}    INFO
            END

            IF    $response_week_variable != ''
                Set Suite Variable    ${{ '{' }}{{ response_week_variable }}{{ '}' }}    ${weekday}
                Log    获取星期: ${weekday}    INFO
            END

            IF    $response_timestamp_variable != ''
                Set Suite Variable    ${{ '{' }}{{ response_timestamp_variable }}{{ '}' }}    ${timestamp}
                Log    获取时间戳: ${timestamp}    INFO
            END

            ${newtime}=    Set Variable   ${newtimestr}
        """,
        ),
        "data_forecast": ComponentDefinition(
            type="data_forecast",
            label="数据预测",
            description="根据不同的数据类型，预测未来一天的数据",
            category="business",
            icon="action-iconfont icon-caozuoxitong",
            config_schema={
                "his_win": {"type": "array", "required": True},
                "day_count": {"type": "number", "required": True},
                "forecast_type": {"type": "string", "required": True},
                "forecast_format": {"type": "string", "required": True},
                "set_step": {"type": "number", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
            },
            outputs=["forecast_data"],
            robot_template="""
               ${response}=    Data Forecast    ${his_win}    ${day_count}    ${forecast_type}    ${forecast_format}    ${set_step}
                # 将结果存储到指定变量
               IF    $forecast_data != ""
                   Set Suite Variable    ${${forecast_data}}    ${response}
                   Log    数据预测结果: ${response}    INFO
               END
               ${forecast_data}=    Set Variable    ${response}
               """,
        ),
        "data_wash": ComponentDefinition(
            type="data_wash",
            label="数据清洗",
            description="对监测数据进行数据清洗，获得清洗后的结果，以及有效数据步长、数据评分等信息",
            category="business",
            icon="action-iconfont icon-caozuoxitong",
            config_schema={
                "wash_type": {"type": "string", "required": True},
                "wash_format": {"type": "string", "required": True},
                "his_win": {"type": "array", "required": True},
                "day_count": {"type": "number", "required": True},
                "fill_in_data": {"type": "string", "required": True},
                "set_step": {"type": "number", "required": True},
                "allow_max_data": {"type": "number", "required": False},
                "allow_min_data": {"type": "number", "required": False},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
            },
            outputs=[
                "wash_win",
                "wash_time",
                "miss_count",
                "invalid_count",
                "valid_count",
                "grade",
            ],
            robot_template="""
            
               {% if allow_max_data and allow_min_data %}
               ${response}=    Data Wash    ${wash_type}    ${wash_format}    ${his_win}    ${day_count}    ${fill_in_data}    ${set_step}    ${allow_max_data}    ${allow_min_data}
               {% else %}
               ${response}=    Data Wash    ${wash_type}    ${wash_format}    ${his_win}    ${day_count}    ${fill_in_data}    ${set_step}
               {% endif %}
               
               
               # 解析多格式响应
               ${result_dict}=    Set Variable    ${response}
        
               # 设置清洗后数据
               IF    $wash_win != ''
                   Set Suite Variable    ${${wash_win}}    ${result_dict.get('wash_win', [])}
                   Log    清洗后数据: ${result_dict.get('wash_win', [])}    INFO
               END
               ${$wash_win}=    Set Variable    ${result_dict.get('$wash_win', [])}
               
               # 设置清洗后数据时间
               IF    $wash_time != ''
                   Set Suite Variable    ${${wash_time}}    ${result_dict.get('wash_time', [])}
                   Log    清洗后数据时间: ${result_dict.get('wash_time', [])}    INFO
               END
               ${$wash_time}=    Set Variable    ${result_dict.get('wash_time', [])}
               
               # 设置缺失数据数量
               IF    $miss_count != ''
                   Set Suite Variable    ${${miss_count}}    ${result_dict.get('miss_count')}
                   Log    缺失数据数量: ${result_dict.get('miss_count')}    INFO
               END
               ${$miss_count}=    Set Variable    ${result_dict.get('miss_count')}
               
               # 设置无效数据数量
               IF    $invalid_count != ''
                   Set Suite Variable    ${${invalid_count}}    ${result_dict.get('invalid_count')}
                   Log    无效数据数量: ${result_dict.get('invalid_count')}    INFO
               END
               ${$invalid_count}=    Set Variable    ${result_dict.get('invalid_count')}
               
               # 设置有效数据数量
               IF    $valid_count != ''
                   Set Suite Variable    ${${valid_count}}    ${result_dict.get('valid_count')}
                   Log    有效数据数量: ${result_dict.get('valid_count')}    INFO
               END
               ${$valid_count}=    Set Variable    ${result_dict.get('valid_count')}
               
               # 设置历史数据评分
               IF    $grade != ''
                   Set Suite Variable    ${${grade}}    ${result_dict.get('grade')}
                   Log    历史数据评分: ${result_dict.get('grade')}    INFO
               END
               ${$grade}=    Set Variable    ${result_dict.get('grade')}
               """,
        ),
        "water_shutoff_valve": ComponentDefinition(
            type="water_shutoff_valve",
            label="停水关阀",
            description="停水关阀",
            category="business",
            icon="action-iconfont icon-caozuoxitong",
            config_schema={
                "input_type": {"type": "string", "required": True},
                "input_data": {"type": "string", "required": True},
                "response_content_variable": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
                "timeout": {"type": "number", "default": 30},
            },
            outputs=["gis_input", "response_content_variable"],
            robot_template="""
               ${result}=    Water Shutoff Valve    ${input_type}    ${input_data}
               
                # 解析多格式响应
               ${result_dict}=    Set Variable    ${result}
                # 将结果存储到指定变量
                IF    $gis_input != ''
                    Set Suite Variable    ${${gis_input}}    ${input_data}
                    Log    获取结果: ${input_data}  INFO
                END
                ${$gis_input}=    Set Variable    ${input_data}
                
                IF    $response_content_variable == 'water_outage_volume'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('water_outage_volume')}
                    Log    获取结果: ${result_dict.get('water_outage_volume')}   INFO
                END
                
                IF    $response_content_variable == 'closed_valves'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('closed_valves',[])}
                    Log    获取结果: ${result_dict.get('closed_valves',[])}   INFO
                END
                
                IF    $response_content_variable == 'water_outage_users'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('water_outage_users',[])}
                    Log    获取结果: ${result_dict.get('water_outage_users',[])}   INFO
                END
                
                IF    $response_content_variable == 'water_outage_pipelines'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('water_outage_pipelines',[])}
                    Log    获取结果: ${result_dict.get('water_outage_pipelines',[])}   INFO
                END
                
                IF    $response_content_variable == 'water_outage_hydrants'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('water_outage_hydrants',[])}
                    Log    获取结果: ${result_dict.get('water_outage_hydrants',[])}   INFO
                END
                
                IF    $response_content_variable == 'suggested_opened_valves'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('suggested_opened_valves',[])}
                    Log    获取结果: ${result_dict.get('suggested_opened_valves',[])}   INFO
                END
                
                IF    $response_content_variable == 'water_flow_pipelines_after_valve_opening'
                    Set Suite Variable    ${${response_content_variable}}    ${result_dict.get('water_flow_pipelines_after_valve_opening',[])}
                    Log    获取结果: ${result_dict.get('water_flow_pipelines_after_valve_opening',[])}   INFO
                END             
                ${response_content_variable}=    Set Variable    ${result}
                
               """,
        ),
    }


def get_rpa_other_components():
    return {
        "weather_query": ComponentDefinition(
            type="weather_query",
            label="获取天气",
            description="'获取指定城市的天气信息",
            category="other",
            icon="weather",
            config_schema={
                "city": {"type": "string", "required": True},
                "days": {"type": "int", "required": True},
                "timeout": {"type": "int", "required": False},
                "error_handle": {"type": "string", "default": "stop"},
                "response_content_variable": {"type": "string", "required": True},
                "retry_times": {"type": "number", "default": 0},
                "retry_delay": {"type": "number", "default": 2},
            },
            outputs=["reponse"],
            robot_template="""
           ${result}=    Get Weather    ${city}    ${days}
           # 将结果存储到指定变量
           IF    $response_content_variable != ''
               Set Suite Variable    ${${response_content_variable}}    ${result}
               Log    获取天气的结果: ${result}    INFO
           END
           # 结果存储到原始变量

          ${reponse}=    Set Variable    ${result}
           """,
        )
    }


#  流程操作
def get_rpa_process_components():
    """发起流程"""
    return {
        "start_workflow": ComponentDefinition(
            type="start_workflow",
            label="发起工单流程",
            description="发起工单流程",
            category="process",
            icon="action-iconfont icon-gongdanliucheng",
            config_schema={
                "params": {"type": "object", "required": True},
                "procCode": {"type": "string", "required": True},
                "procName": {"type": "string", "required": True},
            },
            outputs=["process_result"],
            robot_template="""
               ${result}=    start workflow    ${daparamsa}    ${procCode}    ${procName}
                       # 将结果存储到指定变量
               IF    $process_result != ''
                   Set Suite Variable    ${${process_result}}    ${result}
                   Log    发起工单: ${result}    INFO
               END
               # 结果存储到原始变量
    
              ${reponse}=    Set Variable    ${result}  
               """,
        )
    }


def get_rpa_informationmodel_components():
    """信息模型节点"""
    return {
        "alarm_query": ComponentDefinition(
            type="alarm_query",
            label="报警查询",
            description="报警查询",
            category="informationmodel",
            icon="informationmodel",
            config_schema={
                "data": {"type": "string", "required": True},
                "authorization": {"type": "string", "required": False},
            },
            outputs=["reponse"],
            robot_template="""
           ${reponse}=    Alarm Query    ${data}    ${authorization}
           # translate reponse to markdown
           ${response_text} = Response To Markdownv2    ${response}

          ${reponse}=    Set Variable    ${result}
           """,
        )
    }
