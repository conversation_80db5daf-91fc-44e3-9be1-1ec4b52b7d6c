<template>
  <div class="service-package" v-loading="loading">
    <div class="header">
      <div class="title-section">
        版本管理
        <!-- <el-dropdown @command="handleCommand" trigger="click">
          <span class="el-dropdown-link">
            {{ page_map[type] }}<el-icon class="table-header-icon-right el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="page in pages" :key="page.code" :class="{ selected: type === page.code }"
                :command="page.code">
                <span class="el-dropdown-item-title">{{ page.title }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
        <div class="header-tools">
          <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
            <i class="action-iconfont icon-shuaxinzhongzhi"></i>
            刷新
          </el-button>
          <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
            <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
            新增
          </el-button>
          <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini" @click="onEdit">
            <i class="action-iconfont icon-bianji"></i>
            编辑
          </el-button>
          <!-- <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
            @click="onDelete">
            <i class="action-iconfont icon-huishouzhanshanchu"></i>
            删除
          </el-button> -->
          <!-- <el-divider direction="vertical" style="margin: 0 16px" />
          <el-button class="header-tools-item" type="circle" size="mini" @click="openDetail('guihua')">
            <i class="action-iconfont icon-guihua"></i>
            规划
          </el-button>
          <el-button class="header-tools-item" type="circle" size="mini" @click="openDetail('yuanxing')">
            <i class="action-iconfont icon-yuanxing"></i>
            原型
          </el-button>
          <el-button class="header-tools-item" type="circle" size="mini" @click="openDetail('xiangshe')">
            <i class="action-iconfont icon-xiangshe"></i>
            详设
          </el-button> -->
        </div>
      </div>
      <div class="condition-section">
        <el-form :inline="true" :model="params">
          <el-form-item label="状态">
            <el-select v-model="params.status" placeholder="请选择状态" style="width: 100px">
              <el-option v-for="it in status" :key="it.Value" :label="it.Name" :value="it.Value" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="更新内容">
            <el-input v-model="params.publishContent" placeholder="请输入更新内容" clearable style="width: 160px"
              @keyup.enter="onSubmit" />
          </el-form-item>
          <el-form-item label="更新时间" style="margin-right: 10px;">
            <el-date-picker v-model="params.updateStartTime" type="date" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 150px" @change="changeTimeStart" />
          </el-form-item>
          <span style="fontSize: 12px;">至</span>
          <el-form-item label="" style="margin-left: 10px;">
            <el-date-picker v-model="params.updateEndTime" type="date" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 150px" :disabled-date="disabledDateEnd" />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <!-- <div class="tab-list">
          <div class="tab-list-item left" :class="{ 'active': tabType == 'table' }" @click="changeTabType('table')">列表视图
          </div>
          <div class="tab-list-item right" :class="{ 'active': tabType == 'card' }" @click="changeTabType('card')">卡片视图
          </div>
        </div> -->
      </div>
    </div>
    <div class="table-section">
      <div class="table-content" v-show="tabType == 'table'">
        <el-table ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
          :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)" @rowClick="handleRowClick">
          <el-table-column type="index" label="序号" :index="1 + pagination.pageSize * (pagination.currentPage - 1)"
            align="center" width="60" />
          <el-table-column v-for="it in tableColumns" :key="it.data" :header-align="it.headerAlign||'center'" :prop="it.data" :label="it.title" :align="it.align||'center'"
            :width="it.width" :minWidth="100" :fixed="it.fixed">
            <template v-if="it.data == 'status'" #default="scope">
              <el-tag type="info" round v-if="scope.row.status === 0">
                撤回
              </el-tag>
              <el-tag type="success" round v-else-if="scope.row.status === 1">
                发布
              </el-tag>
            </template>
            <template v-else-if="it.dtype == 'date'" #default="scope">
              {{scope.row[it.data]}}
            </template>
            <template v-else-if="it.data == 'handle'" #default="scope">
              <el-link type="primary" class="task-link"  v-if="scope.row.status === 1" @click.stop="onRevoke(scope.row)">撤回</el-link>
              <el-link type="primary" class="task-link"  v-else @click.stop="onPublish(scope.row)">发布</el-link>
            </template>
            <template v-else-if="it.data == 'publishType'" #default="scope">
              <el-switch v-model="scope.row.publishType" @change="onPublishTypeChange(scope.row)"  :active-value="1" :inactive-value="0"></el-switch>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 50vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery" />
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogFormVisible" class="servicePackage-dialog" :append-to-body="false" :close-on-click-modal="false" :title="isNew ? '新增' : '编辑'" width="650" style="border-radius: 12px;"
    top="30vh">
    <el-form :model="form" ref="ruleFormRef" label-width="100px" :rules="rules" v-if="dialogFormVisible">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="versionNumber" label="版本号" :label-line="true">
            <el-input v-model.trim="form.versionNumber" :maxlength="32" type="text" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="publishContent" label="更新内容" :label-line="true">
            <el-input v-model="form.publishContent" :rows="3" type="textarea" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="files" label="程序包文件" :label-line="true">
            <el-upload
              v-model:file-list="form.files"
              :headers="headers"
              style="width:100%;"
              @success="onUploaded"
              drag
              :on-change="handleChange"
              accept=".exe"
              action="/wimai/api/task/upload"
              :multiple="false"
              :limit="1"
            >
              <div class="upload-item" style="width:64px;height:64px;font-size:12px;margin-bottom:0;">
                  <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
              </div>
              <!-- <el-icon style="width:64px;height:64px;font-size:12px;margin-bottom:0;" class="el-icon--upload"><Plus /></el-icon> -->
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="publishType" label="强制更新" :label-line="true">
            <el-switch v-model="form.publishType" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :disabled="!form.file_url" @click="save">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules,UploadProps } from 'element-plus'

const route = useRoute()
const userStore = useUserStore()

//
const pages = ref([
  { "title": "服务包管理", "code": "service-package" }
])
const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
  "Authorization":Authorization.value,
  "FROM_CHANNEL":"web"
})
const type = ref('service-package')
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
  currentPage: 1,
  pageSize: 30,
  total: 0
})
const isNew = ref(true)
const form = ref({
  versionNumber:"",
  files: "",
  file_url:"",
  publishType:0,
  publishContent:""
})
const dialogFormVisible = ref(false)
const status = ref([
  { Name: '全部', Value: 'all' },
  { Name: '发布', Value: 1 },
  { Name: '撤回', Value: 0 }
])
const tabType = ref('table')
const params = ref({
  status: 'all',
  publishContent: "",
  updateStartTime: null,
  updateEndTime: null
})
const tableData = ref([])
const tableColumns = ref([
  { data: 'versionNumber', title: '版本号', width: 120, orderable: true, filterable: true },
  { data: 'publishContent', title: '更新内容',align:'left', orderable: true, filterable: true },
  { data: 'updater', title: '更新人', width: 150, showOverflowTooltip: true, orderable: true, filterable: true },
  { data: 'updated', title: '更新时间', width: 260, orderable: true, filterable: true },
  { data: 'publishType', title: '强制更新', scoped: 'publishType', width: 120, orderable: true, filterable: true },
  { data: 'status', title: '发布状态', scoped: 'status', width: 85, orderable: true, filterable: true },
  { data: 'handle', title: '操作', scoped: 'handle', width: 200, fixed: 'right' },
])

const ruleFormRef = ref<FormInstance>()



let page_map = ref({
})
pages.value.forEach(page => {
  page_map.value[page.code] = page.title
})



//ref
const tableRef = ref<HTMLElement>()


//computed
// isUniwimPc
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc'
})

const currentUser = computed(() => {
  return userStore.userInfo
})



//方法

const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  function extractVersionFromFileName(fileName: string): string | null {
    const reg = /^WimTask-(\d+\.\d+\.\d+)\.exe$/;
    const matchResult = fileName.match(reg);
    return matchResult && matchResult.length > 1 ? matchResult[1] : null;
  }
  let versionNumber = extractVersionFromFileName(uploadFile.name)
  if(!!versionNumber)form.value.versionNumber = versionNumber;
  // fileList.value = fileList.value.slice(-3)
}

const onUploaded = (response:any, file:string, fileList:any) => {
  ;
  form.value.file_url = response?.Response
}
// 验证版本号格式 xx.xx.xx 的正则表达式
const validateVersion = (rule: any, value: any, callback: any) => {
  const regex = /^\d{1,2}\.\d{1,2}\.\d{1,2}$/;
  if(!regex.test(value)){
    callback(new Error('版本号格式不正确'))
  }else {
    callback()
  }
};

// 自定义唯一性校验规则
const validateVersionUnique = (rule, value, callback) => {
  // if (!value) {
  //   return callback(new Error('请输入版本号'));
  // }
  if(currentRow&&currentRow.value&&currentRow.value.versionNumber == value){
    callback()
    return
  }
  // 模拟异步请求检查用户名唯一性
  saasApi.AIAgentPackagePublishingQuery({
      data:{
        versionNumber:value
      }
    }).then(response => {
    if (response&&response.rows&&response.rows.length) {
      callback(new Error('该版本号已存在，请重新输入'));
    } else {
      callback();
    }
  }).catch(error => {
    callback();
  });
};

const rules = reactive<FormRules>({
  files: [
    { required: true, message: '请选择程序包文件', trigger: 'change' }
  ],
  publishContent: [
    { required: true, message: '请输入更新内容', trigger: 'change' }
  ],
  versionNumber: [
    { validator: validateVersionUnique, trigger: 'blur' },
    { required: true, message: '请输入版本号', trigger: 'change' },
    { validator: validateVersion, trigger: 'change' }
  ]
})

const save = async() => {
  let isValidate = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
  if(!isValidate)return
  let update_params = {
    versionNumber: form.value.versionNumber,
    packagePath: form.value.file_url,
    publishType: form.value.publishType,
    publishContent: form.value.publishContent
  }
  ;
  if (isNew.value) {
    tableInsert(update_params)
  } else {
    tableUpdate(update_params)
  }
}

const onPublishTypeChange = (row:any) => {
  let update_params = {
    publishType: row.publishType
  }
  currentRow.value = row
  tableUpdate(update_params)
}

const tableInsert = (insert_params:any)=> {
  saasApi.AIAgentPackagePublishingInsert(insert_params).then((res:any) => {
    if (res?.Success) {
      ElMessage({
        message: '新增成功!',
        type: 'success',
        showClose: true
      })

      setTimeout(()=>{
        // this.onRunArrange(params)
        tableQuery()
      },200)
      dialogFormVisible.value = false
    } else {
      ElMessage({
        message: '新增失败!',
        type: 'error',
        showClose: true
      })
    }
  }).finally(() => {
  })
}
const tableUpdate = (update_params:any)=>{
  let input_params = {
    ...currentRow.value,
    ...update_params
  }
  saasApi.AIAgentPackagePublishingUpdate(input_params).then((res:any) => {
    if (res?.Code === 0) {
      ElMessage({
        message: '编辑成功!',
        type: 'success',
        showClose: true
      })
      // 编辑不再跳转编排页面
      setTimeout(()=>{
        // this.onRunArrange(params)
        tableQuery()
      },200)
      dialogFormVisible.value = false
    } else {
      ElMessage({
        message: '编辑失败!',
        type: 'error',
        showClose: true
      })
    }
  }).finally(() => {

  })
}

const handleCommand = (command: string) => {
  type.value = command
}

const handleRowClick = (row:any, show:boolean) => {
  currentRow.value = row
  ruleFormRef.value?.clearValidate()
}

const changeTimeStart = (val: number) => {
  if (params.value.updateEndTime && moment(val).valueOf() > moment(params.value.updateEndTime).valueOf()) {
    params.value.updateEndTime = null
  }
}
const disabledDateEnd = (date: number) => {
  let disable = false
  if (params.value.updateStartTime && date <= moment(params.value.updateStartTime).valueOf()) {
    disable = true
  }
  return disable
}

const tableQuery = (noloading: boolean = false) => {
  if (!noloading) {
    loading.value = true

  }
  ;
  currentRow.value = null
  const query_params: any = {
    conditions: [],
    data: {},
    index: pagination.value.currentPage,
    size: pagination.value.pageSize,
  }
  if (params.value.publishContent) {
    query_params.conditions.push({
      Field: "publishContent",
      Group: 1,
      Operate: "like",
      Relation: "and",
      Value: params.value.publishContent
    })
  }
  if (params.value.status !== 'all') {
    query_params.data.status = params.value.status
  }
  if (params.value.updateStartTime) {
    query_params.conditions.push({
      Field: "created",
      Group: 1,
      Operate: ">=",
      Relation: "and",
      Value: moment(params.value.updateStartTime).unix()
    })
  }
  if (params.value.updateEndTime) {
    query_params.conditions.push({
      Field: "created",
      Group: 1,
      Operate: "<=",
      Relation: "and",
      Value: moment(params.value.updateEndTime).unix()
    })
  }
  saasApi.AIAgentPackagePublishingQuery(query_params).then((res:any) => {
    if (typeof res?.rows == 'object') {
      pagination.value = {
        currentPage: res.current,
        pageSize: res.size,
        total: res.total
      }
      tableData.value = res.rows
    }
  }).finally(() => {
    if (!noloading) loading.value = false
  })
}


const onSubmit = () => {
  tableQuery()
}

const formatTime = (data:any, format:string = 'YYYY-MM-DD HH:mm') => {
  if (!data) return ''
  return moment(data).format(format)
}

//发布
const onPublish = async(row:any) => {
  await ElMessageBox.confirm('是否发布当前版本？', '确认发布', {
    type: 'warning',
  })
  currentRow.value = row;
  let update_params = {
    id:row.id,
    status: 1
  }
  tableUpdate(update_params)
}

//撤回
const onRevoke = async(row:any) => {
  await ElMessageBox.confirm('是否撤回当前版本？', '提示', {
    type: 'warning',
    customClass: 'default-confirm-class',
  })
  currentRow.value = row;
  let update_params = {
    id:row.id,
    status: 0
  }
  tableUpdate(update_params)
}

const onReset = () => {
  params.value = {
    status: 'all',
    publishContent: "",
    missionStartTime: null,
    missionEndTime: null
  }
  pagination.value = {
    currentPage: 1,
    pageSize: 30,
    total: 0
  }
  isNew.value = true
  form.value = {}
  currentRow.value = null
  tableData.value = []
  tableQuery()
}
const formSet = async (model: any) => {
  const form_params: any = {
    versionNumber:'',
    files:[],
    file_url:"",
    publishContent:"",
    publishType:0
  }
  let data = JSON.parse(JSON.stringify(model))
  // 设置默认值
  Object.keys(form_params).forEach((key) => {
    if (!data[key]) {
      data[key] = form_params[key]
    }
  })
  ;
  if(data.packagePath){
    let path = data.packagePath.split('/')
    data.files = [{
      name:path[path.length-1],
      url:data.packagePath
    }]
  }

  // 新增数据处理
  if (isNew.value) {
    // 设置模式为通用
    data.missionType = 'normal'

    // 设置当前默认开始时间
    data.missionStartTime = moment(moment().add(10, 'm').format("YYYY-MM-DD HH:mm:00")).valueOf()

    // 设置默认通知人为自己
    data.reportUser = currentUser.value?.id || ''
    data.reportUserName = currentUser.value?.name || ''

    // todo 后续删除，不需要前端传
    data.appKey = 'app-0hXu1dnesxZwflRouhJdHnJr'
  }
  // 编辑数据处理
  else {
    let res = await saasApi.AIAgentPackagePublishingDetail({ id: currentRow.value.id })
    if (res) {
      data = { ...data, ...res }
      data.file_url = res.packagePath||''
    }
  }
  // 返回解析后的cron表达式
  return data
}
// cron 格式反向解析
const onCronToModel = (model: any) => {
  const parts = (model.timeScheduled || '').split(',')
  switch (parts[0]) {
    // 按分钟
    case '1':
      model.execution = 1;
      model.minutes = Number(parts[1])
      break;
    case '2':
      model.execution = 2;
      model.hours = Number(parts[1])
      break;
    case '3':
      model.execution = 3;
      model.days = Number(parts[1])
      break;
    case '4':
      model.execution = 4;
      model.weeks = parts.slice(1).map((week: string) => week)
      break;
    case '5':
      model.execution = 5;
      model.specify = Number(parts[1])
      break;
    default:
      model.execution = 1;
      model.minutes = 1
  }
  console.warn({ model })

  return model
}
const onAdd = async () => {
  isNew.value = true
  form.value = JSON.parse(JSON.stringify(await formSet({})))
  dialogFormVisible.value = true
}
const onEdit = async () => {
  isNew.value = false
  form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
  ;
  dialogFormVisible.value = true
}


const changeTabType = (type: string) => {
  currentRow.value = null
  tableRef?.value.setCurrentRow()
  tabType.value = type
  localStorage.setItem('managerTabType' + (currentUser.value?.sn || ''), type)
}

const onDelete = async() => {
  await ElMessageBox.confirm('是否删除当前版本？', '确认删除', {
    type: 'warning',
  })
  saasApi.AIAgentPackagePublishingDelete([currentRow.value.id]).then((res: any) => {
    if (res.Code === 0) {
      ElMessage({
        message: '删除成功!',
        type: 'success',
        showClose: true
      })
      tableQuery();
    } else {
      ElMessage({
        message: res.Message || '删除失败!',
        type: 'error',
        showClose: true
      })
    }
  }).finally(() => {

  })
}

const openDetail = (type: string) => {
  const urlDict = {
    yuanxing: 'http://192.168.100.205:25427/Products/金聪_WimTask/',
    xiangshe: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2MyYjhiYWRjOWVjMDRmMGViNWQxNDljODQ1ZTRmYzg4OzA7MQ==&showAi=true&hideNativeTitle=true',
    guihua: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2JjMWE0NWNmMGI5YTQ2ZjU4YjY5ZTJlNTY4YjJmNjgyOzE7Mg==&showAi=true&hideNativeTitle=true'
  }
  const urlToken = utils.GetAuthorization();
  let urlOrigin:string = `${urlDict[type]}`
  if (type !== 'yuanxing') urlOrigin += `&uniwater_utoken=${urlToken}`
  if (isUniwimPc) {
    window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
  } else {
    window.open(urlOrigin)
  }
}

onMounted(() => {
  tableQuery()
})
</script>
<style scoped lang="scss">
:deep(.el-upload--text){
  width:64px;
}
:deep(.el-upload-dragger){
  padding:0;
  border:none;
}
:deep(.el-dialog__title){
  font-size:14px;
  font-weight: bold;
}
.service-package {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 12px;
  box-sizing: border-box;
  background: #f7f7f9 !important;
  display: flex;
  flex-direction: column;

  .header {
    background: #fff;
    width: 100%;
    height: 112px;
    box-sizing: border-box;
    overflow: hidden;

    .title-section {
      height: 64px;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: SourceHanSansSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
      }
    }

    .header-tools {
      display: flex;
      align-items: center;

      .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
          margin-right: 4px;
          font-size: 14px;
        }

        span {
          margin-left: 6px;
          line-height: 17px;
        }

        &:hover {
          color: rgba(0, 84, 210, 0.8);
        }

        &:active {
          color: #0044A9;
        }

        &.is-disabled {
          color: #BCBFC3;
          cursor: not-allowed;
        }
      }
    }

    .condition-section {
      padding: 8px 16px;
      box-sizing: border-box;
      border-top: solid 1px #e8ecf0;
      // display: flex;
      justify-content: space-between;

      .el-form-item {
        margin-bottom: 0;
      }

      .tab-list {
        .tab-list-item {
          width: 80px;
          height: 32px;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          border: 1px solid #E6E7E9;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.left {
            border-radius: 4px 0 0 4px;
          }

          &.right {
            border-radius: 0 4px 4px 0;
          }

          &.active {
            color: #FFFFFF;
            background: #0054D9;
            border-color: #0054D9;
          }
        }
      }
    }
  }

  .table-section {
    // flex: 1;
    height:calc(100% - 112px);
    background: #fff;
  }

  .table-content {
    height: 100%;
    :deep(.el-table__cell){
      border-bottom: 1px solid #EEEEEE !important;
    }
    .el-link.task-link{
      font-size: 12px;
      &~.task-link{
        margin-left: 12px;
      }
    }
    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }

    ::v-deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .card-content {
    height: 100%;
    padding: 16px;
    box-sizing: border-box;

    .card-item-box {
      height: calc(100% - 48px);
      overflow-y: auto;
      margin-bottom: 16px;

      .card-item {
        width: calc(25% - 16px);
        min-width: 445px;
        height: 300px;
        display: inline-block;
        margin-right: 16px;
        padding: 16px;
        box-sizing: border-box;
        border: 1px solid #E6E7E9;
        border-radius: 4px;
        margin-bottom: 16px;
        cursor: pointer;

        &:hover {
          border-color: #0054D9;
        }

        &.active {
          border-color: #0054D9;
        }

        .card-item-title {
          height: 20px;
          line-height: 20px;
          font-weight: 500;
          font-size: 14px;
          color: rgba(34, 34, 34, 0.9);
          margin-bottom: 10px;
        }

        .card-item-tag-time {
          .el-tag {
            border-radius: 12px;
          }

          .card-item-tag-time-text {
            font-weight: 400;
            font-size: 12px;
            color: #BCBFC3;
            margin-left: 10px;
          }
        }

        .card-item-info {
          display: flex;
          margin-top: 12px;

          .card-item-info-item {
            font-weight: 400;
            font-size: 12px;
            color: #5C5F66;
            letter-spacing: 0;
            margin-right: 20px;
          }
        }

        .card-item-line {
          width: 100%;
          height: 0.5px;
          background: #EEEEEE;
          border-radius: 4px;
          margin: 16px 0;
        }

        .card-item-two-text {
          display: flex;
          flex-wrap: wrap;

          .card-item-two-text-item {
            width: 50%;
            display: inline-block;
            margin-bottom: 16px;

            .card-item-text1 {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-bottom: 10px;
            }

            .card-item-text2 {
              height: 18px;
              line-height: 18px;
              font-weight: 400;
              font-size: 12px;
              color: #222222;
            }
          }
        }

        .card-item-bottons {
          .card-item-botton {
            display: inline-flex;
            align-content: center;
            justify-content: center;
            padding: 8px 16px;
            box-sizing: border-box;
            background: #FFFFFF;
            border: 1px solid #E6E7E9;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 16px;
            font-size: 12px;

            &.disabled {
              color: #a8abb2;
              border-color: 1px solid #e4e7ed;
              cursor: not-allowed;
            }

            &:not(.disabled):hover {
              color: #0054D2;
              border: 1px solid #0054D2;
            }
          }
        }
      }

      // .card-item:nth-child(4n) {
      //   margin-right: 0;
      // }
    }

    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }
  }
}
</style>
