Robot Framework contributors
============================

This file lists contributors to Robot Framework during the time it was
developed at Nokia Networks. There are both `core team members`_ as well
as `other contributors`_.

Nowadays contributors are acknowledged separately in the release notes
and this file is not anymore updated. Contributors can also be found easily
via GitHub__.

__ https://github.com/robotframework/robotframework/graphs/contributors

Core team members
-----------------

The members of the Robot Framework core team during the Nokia Networks years
2005 - 2015.

===========================    ===========
         Name                     Years
===========================    ===========
Pekka Klärck (né Laukkanen)    2005 - 2015
Petri Haapio                   2005 - 2008
<PERSON><PERSON>                  2005 - 2011
Lasse Koskela                  2005 - 2006
Janne Härkönen                 2006 - 2012
Sami <PERSON>                  2006 - 2007
<PERSON><PERSON>                 2006
Ra<PERSON>                      2007 - 2010
<PERSON><PERSON><PERSON>                  2007 - 2009
<PERSON>                 2008
<PERSON><PERSON>                      2009 - 2011
<PERSON><PERSON>                  2010 - 2015
<PERSON><PERSON><PERSON>                  2010 - 2014
Ismo <PERSON>ro                       2010 - 2012
<PERSON><PERSON><PERSON>             2010 - 2013
Tommi Asiala                   2012
<PERSON><PERSON>                  2012 - 2013
<PERSON><PERSON>                     2012 - 2014
Anssi Syrjäsalo                2013
Janne Piironen                 2013 - 2014
===========================    ===========

Other contributors
------------------

External contributors after Robot Framework was open sourced in 2008.
As already mentioned above, this list is not anymore updated.

===========================    ===============================================
         Name                                    Contribution
===========================    ===============================================
Elisabeth Hendrickson          | Quick Start Guide (2.0)
Marcin Michalak                | String library (2.1)
Chris Prinos                   | reST (HTML) support (2.1)
                               | How-to debug execution with `pdb` (2.7.6)
Régis Desgroppes               | Fixing installation paths (2.1.3)
                               | xUnit compatible outputs (2.5.5)
Robert Spielmann               | Report background colors (2.5)
Xie Yanbo                      | Alignment of east asian characters (2.5.3)
JSXGraph Developers            | JSXGraph tool and license changes for it (2.6)
Imran                          | Template names to listener API (2.6)
                               | Suite source to listener API (2.7)
Tatu Aalto                     | Get Time keyword enhancement (2.7.5)
Eemeli Kantola                 | Fix for non-breaking spaces (2.7.5)
Martti Haukijärvi              | IronPython support to Screenshot library (2.7.5)
Guy Kisel                      | How-to use decorators when creating libraries (2.7.7)
                               | BuiltIn.Log pprint support (2.8.6)
                               | New pattern matching keywords in Collections (2.8.6)
                               | Keyword/variable not found recommendations (2.8.6)
                               | Tidy ELSE and ELSE IF onto separate lines (2.8.7)
                               | Initial contribution guidelines (2.9, #1805)
Mike Terzo                     | Better connection errors to Remote library (2.7.7)
Asko Soukka                    | reST (plain text) support (2.8.2)
Vivek Kumar Verma              | reST (plain text) support (2.8.2)
Stefan Zimmermann              | `**kwargs` support for dynamic libraries (2.8.2)
                               | `**kwargs` support for Java libraries (2.8.3)
                               | `*varargs` support using java.util.List (2.8.3)
Mirel Pehadzic                 | Terminal emulation for Telnet library (2.8.2)
Diogo Sa-Chaves De Oliveira    | Terminal emulation for Telnet library (2.8.2)
Lionel Perrin                  | Giving custom seed to --randomize (2.8.5)
Michael Walle                  | Telnet.Write Control Character keyword (2.8.5)
                               | Telnet.Read Until Prompt strip_prompt option (2.8.7)
                               | String.Strip String (3.0)
Tero Kinnunen                  | BDD 'But' prefix ignored (2.8.7)
Heiko Thiery                   | Enable log level config option for TelnetLibrary (2.8.7)
Nicolae Chedea                 | Float parameters in FOR IN RANGE (2.8.7)
Jared Hellman                  | Custom keyword names (2.9)
                               | Embedded arguments for library keywords (2.9)
Vinicius K. Ruoso              | Support multiple listeners per library (2.9)
                               | Allowing control over connection timeout in Telnet library (2.9.2)
                               | Suppress docutils errors/warnings with reST format (2.9.2)
Joseph Lorenzini               | Exposed ERROR log level for keywords (2.9)
Guillaume Grossetie            | Less flashy report and log styles (2.9, #1943)
Ed Brannin                     | `FOR ... IN ZIP`, `FOR ... IN ENUMERATE` (2.9, #1989)
Moon SungHoon                  | String.Get Regexp Matches keyword (2.9, #1985)
Hélio Guilherme                | Support partial match with String.Get Lines Matching Regexp (2.9, #1836)
Jean-Charles Deville           | Make variable errors not exit `Runner keywords` (2.9, #1869)
Laurent Bristiel               | Convert examples in User Guide to plain text format (2.9, #1972)
Tim Orling                     | IronPython support for `Dialogs` library (2.9.2, #1235)
Jozef Behran                   | Fix `${TEST_MESSAGE}` to reflect current test message (3.0, #2188)
Joong-Hee Lee                  | Extend 'Repeat Keyword' to support timeout (3.0, #2245)
Anton Nikitin                  | Should (Not) Contain Any (3.0.1, #2120)
Yang Qian                      | Support to copy/deepcopy model objects (3.0.1, #2483)
Chris Callan                   | Case-insensitivity support to various comparison keywords (3.0.1, #2439)
Benjamin Einaudi               | Add `--rerunfailedsuites` option (3.0.1, #2117)
===========================    ===============================================
