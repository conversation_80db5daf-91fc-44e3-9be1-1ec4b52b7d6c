#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
<AUTHOR>   zxf 
@Version :   1.0
@Time    :   2025/06/17 15:51:00
'''
import os
import sys
from pathlib import Path
import base64
import json
from config import globals
#分辨项目路径还是实际出包路径
def get_resource_path(relative_path):
    #"""获取打包后的资源文件绝对路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包后的临时目录
        base_path = sys._MEIPASS
    else:
        # 开发环境下的当前目录
        base_path = os.path.abspath(".")
    return Path(base_path) / relative_path

def parse_jwt(token):
    """
       解析JWT格式的token
       :param token: JWT字符串
       :return: 头部(header), 载荷(payload), 签名(signature)
       """
    try:
        # 1. 分割token为三部分
        parts = token.split('.')
        if len(parts) != 3:
            raise ValueError("Invalid JWT format - expected 3 parts")

        # 2. Base64解码payload部分
        payload = json.loads(base64.urlsafe_b64decode(parts[1] + '==').decode('utf-8'))
        try:
            args = {
                "user_info": payload
            }
            globals.set_globals(args)
        except:
            pass
        return  payload

    except Exception as e:
        raise ValueError(f"JWT parsing failed: {str(e)}")

if __name__ == '__main__':
    print(parse_jwt('eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTAyMDY5NDIsImlkIjoiNjMxNmM2ZWE1NmE3YjMxNmUwNTZmYWMyIiwiand0SWQiOiI2MWYzOGFiMzBmYjQ0ZjU5OWM3MGI3MjViMTM1MmQ1YyIsInVpZCI6IjYzMTZjNmVhNTZhN2IzMTZlMDU2ZmFjMiIsInRlbmFudElkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwiY2lkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwibWFpbklkIjoiMTU5MSIsImF2YXRhciI6Imh0dHBzOi8vaGRrai5kbG1lYXN1cmUuY29tL3VuaXdpbS91cGxvYWRzLzIwMjUvMi90aHVtYm5haWxkODEzZDU5MjdlZTg0M2FmYjBkYmEwYTU4ZWQ1ZDMzZS5qcGciLCJuYW1lIjoi5YiY5oiQ6L6JIiwiYWNjb3VudCI6Ijk1NiIsIm1vYmlsZSI6IjE1NjcwNDY1Nzg4Iiwic24iOiI5NTYiLCJncm91cCI6IjYyNzRiM2MyNTZhN2IzMzhjNDNmYjMxNSIsImdyb3VwTmFtZSI6IjAxLuWfuuehgOW5s-WPsOS6p-e6vyIsInlobG9OdW0iOiI2MDEyNyIsImlzQWRtaW4iOmZhbHNlLCJjaGFubmVsIjoiZGVzayIsInJvbGVzIjpbIjE4MjY3ODgwMjkxNTM0NTYxMjkiLCI2NDc5YWRiOTU2YTdiMzNkYmNjZTYxMGMiLCIxNzYwMTkzOTAxNDgxNzMwMDUwIiwiMTc3NTQzMzg5MjA2NzY1NTY4MiIsIjYxZGY4OGZjNTZhN2IzMTM4MGVkZGY1MiIsIjYzMTA3ZDgxNTZhN2IzMDdlY2Y5ODk4ZCIsIjE3NDk2MDAxNjQzNTk3NTc4MjUiLCIxODI0MDE0NDc5NjkwNjAwNDQ5IiwiMTkxNjc5MDc1OTY2NTc1NDExMyJdLCJjb21wYW55Ijp7ImlkIjoiNjI3NGIzYzI1NmE3YjMzOGM0M2ZiMzE1IiwibmFtZSI6IjAxLuWfuuehgOW5s-WPsOS6p-e6vyIsImNvZGUiOiI2NDY1OTk2OTIifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzgxNzQzMjQyfQ.PROKZbzj9VHiKfynMaF8rzSnx9cfbO1rGoXANf1XVAc'))