<template>
  <iframe
    ref="mapIframe"
    :src="iframeUrl"
    frameborder="0"
    style="width: 100%; height: 800px"
  ></iframe>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
// @ts-ignore
import utils from '@/utils/utils'
// 新增props定义
const props = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
  config: {
    type: Object,
    default: () => ({}),
  },
})

const token = utils.GetAuthorization()
const prefix = location.origin === 'https://www.dlmeasure.com' ? 'https://www.dlmeasure.com/extends' : location.origin

const iframeUrl =
  prefix +
  `/oneMap3D/#/imb/OneMap?id=zcxx&mapmodel=2d&headerToolBar=true&hideSideToolbar=all&hidePlayBar=true&hideCompass=true&hideLegend=true&hideRightPanel=true&uniwim_tenant_id=0000000000&uniwater_utoken=${token}`


const mapIframe = ref<HTMLIFrameElement | null>(null)

onMounted(() => {
  window.addEventListener('message', handleMapMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMapMessage)
})

// 处理地图消息
const handleMapMessage = (e: MessageEvent) => {
  if (e.data?.action && e.data.params && e.data.action === 'mapEvent') {
    const { method } = e.data.params
    if (method === 'mapCompleted') {
      console.clear()
      console.warn('孪生图加载完成!!')
      console.warn('孪生图加载完成!!')
      console.warn('孪生图加载完成!!')
      // 这里执行地图加载完成后的操作
      setTimeout(() => {
        const matchedNode = findNodeById(props.config.nodeId)
        if (matchedNode) {
          console.log('找到匹配的节点:', matchedNode)
          console.log('执行输出数据:', props.config?.output)
          // 如果存在关阀分析返回结果
          if(props.config?.output?.response_content_variable){
            const {
              closed_valves: ListValve,
              water_outage_users: ListUser,
              water_outage_pipelines: ListPipe,
              water_outage_hydrants: HydrantList,
              suggested_opened_valves: OpenValvesList,
              water_flow_pipelines_after_valve_opening: ListReleasedPipes
            } = props.config?.output?.response_content_variable
            callMapMethod('aiScenePipeBurstAnalysis', {
              result: {
                ListValve,
                ListUser,
                ListPipe,
                HydrantList,
                OpenValvesList,
                ListReleasedPipes,
              },
            })
            return
          }
          // 如果是管道编号，则传入管道编号数据
          if (
            matchedNode?.data?.config?.input_type === '管道GIS-编号' &&
            props.config?.output?.gis_input
          ) {
            callMapMethod('aiScenePipeBurstAnalysis', {
              globalid: [props.config.output.gis_input],
            })
          }
          // 如果是管道中心坐标
          if (
            matchedNode?.data?.config?.input_type === '管道中心坐标' &&
            props.config?.output?.gis_input
          ) {
            let center = props.config.output.gis_input
            if (typeof center === 'string') {
              try {
                center = JSON.parse(center)
              } catch (error) {
                center = []
              }
            }
            callMapMethod('aiScenePipeBurstAnalysis', {
              radius: 50,
              center: center,
            })
          }
          // 如果是模型ID
          if (matchedNode?.data?.config?.input_type === '管道模型ID') {
            // callMapMethod('aiScenePipeBurstAnalysis', {
            //   globalid: [matchedNode.data.config.input_data],
            // })
          }
        }
      }, 500)
    }
  }
}

// 调用地图方法
const callMapMethod = (methodName: string, args: any) => {
  if (mapIframe.value?.contentWindow) {
    console.warn('调用地图方法', methodName, args)
    mapIframe.value.contentWindow.postMessage({
      action: 'mapMethod',
      params: {
        method: methodName,
        args,
      },
    })
  }
}

// 新增查找节点函数
const findNodeById = (id: string) => {
  return props.nodes.find((node: any) => node.id === id)
}
</script>

<style scoped lang="scss"></style>
