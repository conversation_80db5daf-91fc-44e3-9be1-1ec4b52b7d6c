import re
from typing import Any, Dict, Union

pattern = r'\${.*?}'
# 记录变量，用于展示输入，不支持数组中添加变量
def record_vars( node_info, wid: str, node_name: str) -> list:
    node = node_info
    re_list = []
    if not isinstance(node_info, dict):
        node = node_info.__dict__
    if "data" in node and not type(node["data"]) is str:
        node_name = node_info.data.label
        node = node_info.data.__dict__

    for key, value in node.items():
        if isinstance(value, dict):
            re_list.extend(record_vars(value, wid, node_name))
        elif isinstance(value, str):
            match = re.findall(pattern, value)
            if match:
                re_list.extend(match)

    re_list = [t.replace("${","").replace("}","")  for t in re_list]
    return re_list