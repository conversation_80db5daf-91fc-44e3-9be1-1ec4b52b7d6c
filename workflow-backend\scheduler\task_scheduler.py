"""
任务调度器 - 负责定时执行工作流
"""

import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from loguru import logger

from models.workflow import ScheduleConfig, WorkflowData


class TaskScheduler:
    """任务调度器"""

    def __init__(self):
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': True,
            'max_instances': 1,
            'misfire_grace_time': 300
        }

        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )

        self.scheduled_workflows: Dict[str, Dict[str, Any]] = {}
        self.execution_callback: Optional[Callable] = None

        logger.info("任务调度器初始化完成")

    async def start(self):
        """启动调度器"""
        try:
            self.scheduler.start()
            logger.info("任务调度器已启动")
        except Exception as e:
            logger.error(f"启动任务调度器失败: {e}")
            raise

    async def shutdown(self):
        """关闭调度器"""
        try:
            self.scheduler.shutdown(wait=True)
            logger.info("任务调度器已关闭")
        except Exception as e:
            logger.error(f"关闭任务调度器失败: {e}")

    def set_execution_callback(self, callback: Callable):
        """设置执行回调函数"""
        self.execution_callback = callback

    async def schedule_workflow(
            self,
            workflow_id: str,
            workflow: WorkflowData,
            schedule_config: ScheduleConfig
    ) -> str:
        """调度工作流"""
        if not schedule_config.enabled:
            raise ValueError("调度配置未启用")

        # 创建触发器
        trigger = self._create_trigger(schedule_config)
        if not trigger:
            raise ValueError("无效的调度配置")

        # 添加任务
        job_id = f"workflow_{workflow_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            job = self.scheduler.add_job(
                func=self._execute_scheduled_workflow,
                trigger=trigger,
                args=[workflow_id, workflow],
                id=job_id,
                name=f"Workflow: {workflow.metadata.name}",
                max_instances=schedule_config.max_instances,
                coalesce=schedule_config.coalesce,
                misfire_grace_time=schedule_config.misfire_grace_time
            )

            # 保存调度信息
            self.scheduled_workflows[job_id] = {
                'workflow_id': workflow_id,
                'workflow': workflow,
                'schedule_config': schedule_config,
                'job': job,
                'created_at': datetime.now(),
                'last_run': None,
                'next_run': job.next_run_time,
                'run_count': 0
            }

            logger.info(f"工作流 {workflow_id} 已添加到调度，任务ID: {job_id}")
            logger.info(f"下次执行时间: {job.next_run_time}")

            return job_id

        except Exception as e:
            logger.error(f"调度工作流失败: {e}")
            raise

    def _create_trigger(self, schedule_config: ScheduleConfig):
        """创建调度触发器"""
        if schedule_config.cron_expression:
            # Cron表达式调度
            try:
                return CronTrigger.from_crontab(
                    schedule_config.cron_expression,
                    start_date=schedule_config.start_date,
                    end_date=schedule_config.end_date
                )
            except Exception as e:
                logger.error(f"无效的Cron表达式: {schedule_config.cron_expression}, {e}")
                return None

        elif schedule_config.interval_seconds:
            # 间隔调度
            return IntervalTrigger(
                seconds=schedule_config.interval_seconds,
                start_date=schedule_config.start_date,
                end_date=schedule_config.end_date
            )

        elif schedule_config.start_date:
            # 一次性调度
            return DateTrigger(run_date=schedule_config.start_date)

        return None

    async def _execute_scheduled_workflow(self, workflow_id: str, workflow: WorkflowData):
        """执行调度的工作流"""
        logger.info(f"开始执行调度的工作流: {workflow_id}")

        # 更新执行统计
        for job_id, info in self.scheduled_workflows.items():
            if info['workflow_id'] == workflow_id:
                info['last_run'] = datetime.now()
                info['run_count'] += 1
                if info['job'].next_run_time:
                    info['next_run'] = info['job'].next_run_time
                break

        try:
            if self.execution_callback:
                # 调用执行回调
                await self.execution_callback(workflow)
            else:
                logger.warning("未设置执行回调函数")

        except Exception as e:
            logger.error(f"执行调度工作流失败: {e}")

    async def unschedule_workflow(self, job_id: str) -> bool:
        """取消工作流调度"""
        try:
            if job_id in self.scheduled_workflows:
                # 移除调度任务
                self.scheduler.remove_job(job_id)

                # 移除记录
                del self.scheduled_workflows[job_id]

                logger.info(f"已取消工作流调度: {job_id}")
                return True
            else:
                logger.warning(f"调度任务不存在: {job_id}")
                return False

        except Exception as e:
            logger.error(f"取消工作流调度失败: {e}")
            return False

    async def pause_workflow(self, job_id: str) -> bool:
        """暂停工作流调度"""
        try:
            if job_id in self.scheduled_workflows:
                self.scheduler.pause_job(job_id)
                logger.info(f"已暂停工作流调度: {job_id}")
                return True
            else:
                logger.warning(f"调度任务不存在: {job_id}")
                return False

        except Exception as e:
            logger.error(f"暂停工作流调度失败: {e}")
            return False

    async def resume_workflow(self, job_id: str) -> bool:
        """恢复工作流调度"""
        try:
            if job_id in self.scheduled_workflows:
                self.scheduler.resume_job(job_id)
                logger.info(f"已恢复工作流调度: {job_id}")
                return True
            else:
                logger.warning(f"调度任务不存在: {job_id}")
                return False

        except Exception as e:
            logger.error(f"恢复工作流调度失败: {e}")
            return False

    async def get_scheduled_workflows(self) -> List[Dict[str, Any]]:
        """获取所有调度的工作流"""
        result = []

        for job_id, info in self.scheduled_workflows.items():
            job = info['job']
            result.append({
                'job_id': job_id,
                'workflow_id': info['workflow_id'],
                'workflow_name': info['workflow'].metadata.name,
                'schedule_config': info['schedule_config'],
                'created_at': info['created_at'],
                'last_run': info['last_run'],
                'next_run': info['next_run'],
                'run_count': info['run_count'],
                'is_paused': job.next_run_time is None and not job.pending
            })

        return result

    async def get_workflow_schedule(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取特定工作流的调度信息"""
        if job_id in self.scheduled_workflows:
            info = self.scheduled_workflows[job_id]
            job = info['job']

            return {
                'job_id': job_id,
                'workflow_id': info['workflow_id'],
                'workflow_name': info['workflow'].metadata.name,
                'schedule_config': info['schedule_config'],
                'created_at': info['created_at'],
                'last_run': info['last_run'],
                'next_run': info['next_run'],
                'run_count': info['run_count'],
                'is_paused': job.next_run_time is None and not job.pending
            }

        return None

    async def update_workflow_schedule(
            self,
            job_id: str,
            schedule_config: ScheduleConfig
    ) -> bool:
        """更新工作流调度配置"""
        try:
            if job_id not in self.scheduled_workflows:
                logger.warning(f"调度任务不存在: {job_id}")
                return False

            info = self.scheduled_workflows[job_id]

            # 创建新的触发器
            trigger = self._create_trigger(schedule_config)
            if not trigger:
                raise ValueError("无效的调度配置")

            # 修改任务
            self.scheduler.modify_job(
                job_id,
                trigger=trigger,
                max_instances=schedule_config.max_instances,
                coalesce=schedule_config.coalesce,
                misfire_grace_time=schedule_config.misfire_grace_time
            )

            # 更新配置
            info['schedule_config'] = schedule_config
            info['next_run'] = self.scheduler.get_job(job_id).next_run_time

            logger.info(f"已更新工作流调度配置: {job_id}")
            return True

        except Exception as e:
            logger.error(f"更新工作流调度配置失败: {e}")
            return False

    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'running': self.scheduler.running,
            'state': self.scheduler.state,
            'scheduled_jobs': len(self.scheduled_workflows),
            'pending_jobs': len([job for job in self.scheduler.get_jobs() if job.pending]),
            'next_run_time': min([
                job.next_run_time for job in self.scheduler.get_jobs()
                if job.next_run_time
            ], default=None)
        }
