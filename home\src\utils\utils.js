'use strict';
import moment from "moment";
import wtf from "@/assets/images/wtf.png"
import docx from "@/assets/images/docx.png"
import excel from "@/assets/images/excel.png"
import pdf from "@/assets/images/pdf.png"
import png from "@/assets/images/png.png"
import {ElMessage} from 'element-plus'
// src/utils/version.js
import { version } from '../../../package.json';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

export const getAppVersion = () => {
  return version;
};


const utils = {
    getFromChannel(){
        // 是否桌面端
        const isUniwimPc = (utils.GetQueryString('uniwim') || utils.GetQueryString('uniwim', 'hash')) === 'pc'
                || location.hash.indexOf('/ai/uniwimpc') > -1;
        // FROM_CHANNEL 来源渠道
        let FROM_CHANNEL = 'web';
        if (utils.isMobile()) {
            FROM_CHANNEL = 'app'
        }
        else if(isUniwimPc){
            FROM_CHANNEL = 'desk'
        }
        return FROM_CHANNEL
    },
    // 获取token
    getUniwaterUtoken() {
        let UniWimAuthorization = '';
        try {
            let tenantInfo = utils.GetQueryString('tenantInfo') || utils.GetQueryString('tenantInfo', window.top);
            if (tenantInfo && decodeURIComponent(tenantInfo)) {
                let acItem = decodeURIComponent(HD.base64.decode(tenantInfo));
                if (JSON.parse(acItem)) {
                    tenantInfo = JSON.parse(acItem);
                    UniWimAuthorization = tenantInfo && tenantInfo.uniwater_utoken ? tenantInfo.uniwater_utoken : '';
                }
            }
        } catch (e) {
            console.log(e);
        }
        let token = UniWimAuthorization || utils.GetQueryString('uniwater_utoken') || utils.GetQueryString('uniwater_utoken','hash') || utils.GetQueryString('token') || window.localStorage.getItem('UniWimAuthorization') || '';
        return token && token != 'null' ? token : '';
    },
    // 获取刷新token
    getUniWimRefreshToken() {
        let tenantInfo = utils.GetQueryString('tenantInfo');
        let UniWimRefreshToken = '';
        if (tenantInfo && decodeURIComponent(tenantInfo)) {
            let acItem = decodeURIComponent(HD.base64.decode(tenantInfo));
            if (JSON.parse(acItem)) {
                tenantInfo = JSON.parse(acItem);
                UniWimRefreshToken = tenantInfo && tenantInfo.UniWimRefreshToken ? tenantInfo.UniWimRefreshToken : '';
            }
        }
        let token = UniWimRefreshToken || utils.GetQueryString('UniWimRefreshToken') || localStorage.getItem('UniWimRefreshToken') || '';
        return token && token != 'null' ? token : '';
    },
    // 租户id
    getUniWimTenantId() {
        let tenantInfo = utils.GetQueryString('tenantInfo');
        let uniwim_tenant_id = '';

        if (tenantInfo && decodeURIComponent(tenantInfo)) {
            let acItem = decodeURIComponent(HD.base64.decode(tenantInfo));
            if (JSON.parse(acItem)) {
                tenantInfo = JSON.parse(acItem);
                uniwim_tenant_id = tenantInfo && tenantInfo.uniwim_tenant_id ? tenantInfo.uniwim_tenant_id : '';
            }
        }
        return uniwim_tenant_id || utils.GetQueryString('uniwim_tenant_id') || window.localStorage.getItem('UniWimTenantId') || '';
    },
    /**
     * 根据基础URL和查询参数对象生成完整地址
     * @param {string} baseUrl - 基础URL（如 'https://example.com/api' 或 '/page'）
     * @param {Object} queryParams - 查询参数对象（如 { id: 1, name: 'test' }）
     * @returns {string} 拼接后的完整地址
     */
    generateUrlWithQuery(baseUrl, queryParams) {
        // 处理空参数情况
        if (!queryParams || Object.keys(queryParams).length === 0) {
            return baseUrl;
        }

        // 将查询参数对象转为键值对数组并编码
        const queryString = Object.entries(queryParams)
            .map(([key, value]) => {
                // 对键和值进行URL编码，处理特殊字符
                const encodedKey = encodeURIComponent(key);
                const encodedValue = encodeURIComponent(value);
                return `${encodedKey}=${encodedValue}`;
            })
            .join('&');

        // 判断基础URL是否已有查询参数（含?）
        const hasQuery = baseUrl.includes('?');
        // 拼接基础URL和查询字符串
        return `${baseUrl}${hasQuery ? '&' : '?'}${queryString}`;
    },  
    require(imgPath) {
        try {
            const handlePath = imgPath.replace('@', '/src');
            return new URL(handlePath, import.meta.url).href;
        } catch (error) {
            console.warn(error);
        }
    },
    getAppVersion(){
        return version;
    },
    isHdkj() {
        const u = navigator.userAgent.toLowerCase();
        return u.indexOf("hdkj") > -1
    },
    // 字符串缩短
    shortenText(text, startLength = 10, endLength = 10) {
        if (text.length <= startLength + endLength) {
            return text;
        }
        const start = text.slice(0, startLength);
        const end = text.slice(-endLength);
        return `${start}...${end}`;
    },
    // 对查询关键字中的特殊字符进行编码
    encodeSearchKey(key) {
        const encodeArr = [
            { code: '%', encode: '%25' },
            // { code: '?', encode: '%3F' },
            { code: '#', encode: '%23' },
            // { code: '&', encode: '%26' },
            // { code: '=', encode: '%3D' }
        ];
        return key.replace(/[%#]/g, ($, index, str) => {
            for (const k of encodeArr) {
                if (k.code === $) {
                    return k.encode;
                }
            }
        });
    },
    /**
     * 根据文件后缀或Base64前缀，判断一个字符串是否代表一张图片。
     *
     * @param {string | null | undefined} inputString - 要检查的字符串 (URL, 文件名, 或 Base64 数据)。
     * @returns {boolean} - 如果是图片，则返回 true，否则返回 false。
     */
    isImage(inputString) {
        // 1. 安全检查：处理 null、undefined 或非字符串输入
        if (!inputString || typeof inputString !== 'string') {
            return false;
        }

        // 2. Base64 图片检查：检查是否以 "data:image/" 开头
        // 这是最可靠、最高效的Base64图片判断方式。
        if (inputString.startsWith('data:image/')) {
            return true;
        }

        // 3. 文件后缀检查
        // 创建一个包含常见图片后缀的 Set，以便快速查找 (比数组的 .includes() 更快)
        const imageExtensions = new Set([
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico', 'tiff'
        ]);

        // 提取后缀
        // 使用 lastIndexOf('.') 来正确处理包含多个点的文件名 (e.g., "archive.v1.2.jpg")
        const lastDotIndex = inputString.lastIndexOf('.');
        if (lastDotIndex === -1) {
            // 如果没有点，就没有后缀
            return false;
        }

        // 获取点之后的所有内容，并转换为小写以进行不区分大小写的比较
        const suffix = inputString.substring(lastDotIndex + 1).toLowerCase();

        // 检查提取出的后缀是否存在于我们的 Set 中
        return imageExtensions.has(suffix);
    },
    /**
     * 获取 Base64 编码的图片的原始尺寸。
     *
     * @param {string} base64String - 完整的 Base64 Data URL (e.g., "data:image/png;base64,...").
     * @returns {Promise<{ width: number; height: number; }>} - 返回一个包含宽度和高度的 Promise 对象。
     * @throws {Error} - 如果输入的字符串无效或图片数据损坏，则抛出异常。
     */
    getBase64ImageDimensions(base64String) {
        return new Promise((resolve, reject) => {
            // 1. 输入验证：确保输入是有效的 Base64 图片 Data URL
            if (!base64String || typeof base64String !== 'string' || !base64String.startsWith('data:image/')) {
            // 如果格式不正确，立即拒绝 Promise
                return reject(new Error('无效的输入：提供的字符串不是一个有效的 Base64 图片。'));
            }

            // 2. 创建一个内存中的 Image 对象
            const img = new Image();

            // 3. 设置 onload 事件处理器
            // 这个事件会在图片成功解码并加载后触发
            img.onload = () => {
                // 成功时，解析 Promise 并返回图片的原始尺寸
                resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                });
            };

            // 4. 设置 onerror 事件处理器
            // 如果 Base64 数据损坏或格式不被支持，会触发这个事件
            img.onerror = () => {
                // 失败时，拒绝 Promise 并提供错误信息
                reject(new Error('无法加载 Base64 图片。数据可能已损坏或格式不受支持。'));
            };

            // 5. 将 Base64 字符串设置为图片的 src
            // 这一步会启动浏览器的解码和加载过程
            img.src = base64String;
        });
        },
    /**
     * 将在线图片的URL转换为Base64字符串 (Data URL)。
     * @param {string} url - 要转换的在线图片的URL。
     * @returns {Promise<string>} - 返回一个包含Base64格式字符串的Promise。
     * @throws {Error} - 如果网络请求失败或发生其他错误，则抛出异常。
     */
    async imageUrlToBase64(url) {
        // 1. 使用 fetch API 异步请求图片资源
        const response = await fetch(url);

        // 2. 检查网络请求是否成功 (例如，处理 404 Not Found)
        if (!response.ok) {
            throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
        }

        // 3. 将响应体转换为二进制大对象 (Blob)
        const imageBlob = await response.blob();

        // 4. 使用 FileReader 将 Blob 转换为 Base64
        // 这是一个回调式的API，我们将其包装成Promise以便使用async/await
        return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        // 读取成功完成时，Promise成功并返回结果
        reader.onloadend = () => {
            resolve(reader.result);
            };

            // 读取发生错误时，Promise失败
            reader.onerror = (error) => {
            reject(error);
            };

            // 开始读取Blob，并将其编码为Base64 (Data URL)
            reader.readAsDataURL(imageBlob);
        });
    },
    incrementVersion(version, type = 'patch') {
            // 验证版本号格式
            const versionRegex = /^\d+\.\d+\.\d+$/;
            if (!versionRegex.test(version)) {
                throw new Error('版本号格式不正确，请使用 x.y.z 格式（如 1.0.0）');
            }

            // 将版本号拆分为数组
            const parts = version.split('.').map(Number);
            
            // 根据类型自增相应的版本号部分
            switch (type.toLowerCase()) {
                case 'major': // 主版本号自增，次版本号和修订号重置为0
                    parts[0]++;
                    parts[1] = 0;
                    parts[2] = 0;
                    break;
                case 'minor': // 次版本号自增，修订号重置为0
                    parts[1]++;
                    parts[2] = 0;
                    break;
                case 'patch': // 修订号自增（默认）
                    parts[2]++;
                    break;
                default:
                    throw new Error('自增类型不正确，请使用 major、minor 或 patch');
            }

            // 拼接成新的版本号字符串
            return parts.join('.');
        },
    //获取路由参数
    GetQueryString(name, type) {
        let target;
        if (type === "hash") {
            target = window.location.hash.split("?")[1];
        } else {
            target = window.location.search.substr(1);
        }
        if (!target) {
            return null;
        }
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        let r = target.match(reg);
        if (r != null) {
            return decodeURIComponent(window.HD ? window.HD.EncodeSearchKey(r[2]) : utils.encodeSearchKey(r[2]));
        }
        return null;
    },
    /**
     * 给URL拼接参数
     * @param {string} url - 原始URL地址
     * @param {Object} params - 要拼接的参数对象（键值对）
     * @returns {string} 拼接后的完整URL
     */
    appendUrlParams(url, params) {
    // 如果没有参数，直接返回原URL
    if (!params || Object.keys(params).length === 0) {
        return url;
    }

    // 转换参数对象为key=value格式的数组
    const paramArr = [];
    for (const key in params) {
        if (params.hasOwnProperty(key)) {
        // 对参数值进行URL编码，避免特殊字符问题
        paramArr.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
        }
    }
    const paramStr = paramArr.join('&');

    // 判断URL中是否已包含问号
    if (url.includes('?')) {
        // 已有问号，用&拼接新参数
        return `${url}&${paramStr}`;
    } else {
        // 没有问号，用?拼接新参数
        return `${url}?${paramStr}`;
    }
    },
    setLocalStorageInfo(token, expire, refreshToken, tenantId) {
        if (expire) {
            let secondsVal = moment().add(Number(expire), 'seconds').format('x');
            window.localStorage.setItem('tokenTime', secondsVal);
        }
        window.localStorage.setItem('UniWimAuthorization', token);
        window.localStorage.setItem('UniWimExpire', expire);
        window.localStorage.setItem('UniWimRefreshToken', refreshToken);
        window.localStorage.setItem('UniWimTenantId', tenantId);
    },
    isMobile() {
        return utils.isIos() || utils.isAndroid();
    },
    isMobileSys() {
        const userAgent = navigator.userAgent.toLowerCase();
        const mobileKeywords = ['iphone', 'ipod', 'android', 'blackberry', 'windows phone', 'ipad', 'opera mini', 'mobile'];
        return mobileKeywords.some((keyword) => userAgent.includes(keyword));
    },
    removeStorage() {
        window.sessionStorage.clear();
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            keys.push(localStorage.key(i));
        }
        keys.forEach((key) => {
            if (key !== 'phoneShowYinuo') {
                localStorage.removeItem(key);
            }
        });
    },
    // 下载一诺
    async downloadYN(type, applicationName, functionName) {
        const FROM_CHANNEL = utils.getFromChannel();
        const token = utils.GetAuthorization()
            let response = await fetch('/uniwim/package-api/appPackage/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: token,
                    FROM_CHANNEL
                },
                contentType: 'application/json',
                dataType: 'json',
                body: JSON.stringify({
                    appKey: type
                })
            })
            const result = await response.json()
            debugger;
            if(response.ok){
                let res = result.Response
                // loadInitialData(60,100)
                // setTimeout(()=>{
                //     jumpToNextPage()
                // },2000)
                if (res && res.zip) {
                    // 获取文件名
                    window.open(res.zip);
                    let param = {
                        source: 'web',
                        type: 2,
                        applicationId: 'download',
                        applicationName: applicationName, //应用名称
                        functionId: 'download',
                        functionName: functionName, //功能名称
                        path: res.zip, //路径
                        result: 1 //访问结果
                    };
                    utils.saveLogInfo(param);
                } else {
                    ElMessage({
                        showClose: true,
                        message: '暂无下载包',
                        type: 'warning'
                    });
                }
            }else{
                ElMessage({
                    showClose: true,
                    message: '暂无下载包',
                    type: 'warning'
                });
            }
    },
    async getVisitorId() {
        const fp = await FingerprintJS.load();
        const result = await fp.get();
        window.visitorId = result.visitorId;
        return result.visitorId;
    },
    // 埋点
    saveLogInfo(params) {
        const FROM_CHANNEL = utils.getFromChannel();
        const token = utils.GetAuthorization()
        if (utils.getUniwaterUtoken()) {
            const source = utils.GetQueryString('uniwim') == 'pc' ? '桌面端' : 'web';
            // 登录状态下
            fetch('/uniwim/ump/accessLog/saveLog', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: token,
                    FROM_CHANNEL
                },
                contentType: 'application/json',
                dataType: 'json',
                body: JSON.stringify({
                    result: 1,
                    type: 1,
                    source,
                    ...params
                })
            }).then(() => {});
            // systemApi.saveLog({result: 1, type: 1, source, ...params}).then(() => {});
        } else {
            if (window.visitorId) {
                params.userName = '游客-' + window.visitorId;
                // systemApi.saveAccessTouristLog(params).then(() => {});
                fetch('/uniwim/ump/accessTouristLog/saveLog', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        FROM_CHANNEL
                    },
                    contentType: 'application/json',
                    dataType: 'json',
                    body: JSON.stringify({
                        ...params
                    })
                }).then(() => {});
            } else {
                utils.getVisitorId().then((res) => {
                    params.userName = '游客-' + res;
                    // systemApi.saveAccessTouristLog(params).then(() => {});
                    fetch('/uniwim/ump/accessTouristLog/saveLog', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            FROM_CHANNEL
                        },
                        contentType: 'application/json',
                        dataType: 'json',
                        contentType: 'application/json',
                        dataType: 'json',
                        body: JSON.stringify({
                            ...params
                        })
                    }).then(() => {});
                });
            }
        }
    },
    sysLogOut(token) {
        const FROM_CHANNEL = utils.getFromChannel();
        if (token) {
            fetch('/uniwim/ump/loginLog/logOut',{
                method: 'GET',
                contentType: 'application/json',
                dataType: 'json',
                headers: {
                    Authorization: token,
                    FROM_CHANNEL
                }
            });
        }
    },
    containsElement(tree, value){
        for (let i = 0; i < tree.length; i++) {
            if (tree[i].Id === value) {
                return true;
            }
            if (tree[i].children && containsElement(tree[i].children, value)) {
                return true;
            }
        }
        return false;
    },
    FormatAllMenu(apps, AppId = null, menuStyle, version){
        ALL_APP_MENUS.menus = [];
        if (!apps && !apps.length) {
            return;
        }
        apps.forEach((m) => {
            m.Id = m.id;
            m.Name = m.name;
            let fullLink = m.fullLink || m.link;
            if (m.effectDrawing && !fullLink) {
                m.Val = '/preview?imgUrl=' + m.effectDrawing;
            } else {
                m.Val = fullLink;
            }
    
            if (!m.Val && (!m.children || !m.children.length)) {
                // 预览无地址时显示开发中
                m.Val = '/developingPage';
                m.fullLink = '/developingPage';
                m.link = '/developingPage';
            }
    
            m.Value = m.id;
            m.version = m.version ? m.version : version;
            m.reload = m.reload || '';
            m.monitor = m.monitor ? 1 : 0; //监控中心是否开启
            m.monitorUrl = m.monitorUrl ? m.monitorUrl : ''; //监控中心开启则显示地址
    
            if (fullLink && fullLink.indexOf('runCommand:') != -1) {
                const uniwim_utoken = window.localStorage.getItem('UniWimAuthorization') || '';
                m.Val = fullLink.split('runCommand:')[1] + '://' + uniwim_utoken;
            }
    
            m.ValBuffer = m.Val;
            m.App = AppId || m.id;
            m.Children = m.children;
    
            if (menuStyle && (!m.menuStyle || ['4', '5', '12'].indexOf(m.menuStyle) == -1)) {
                m.menuStyle = menuStyle;
            }
            if (m.children) {
                m.children = FormatAllMenu(m.children, m.App, m.menuStyle, m.version);
            }
    
            if (window.visitorId) {
                m.VisitorId = window.visitorId;
            } else {
                utils.getVisitorId().then((res) => {
                    m.VisitorId = res;
                });
            }
            if (m.Val) {
                ALL_APP_MENUS.menus.push(m);
            }
    
            m.VisitorId = window.visitorId;
        });
        return apps;
    },
    // 获取第一个菜单
    GetFirstMenu(menus, type = '', target){
        for (let i = 0; i < menus.length; i++) {
            let m = menus[i];
            // 如果type是scene 场景模式
            // 满足非新窗口打开，且是菜单类型为面板，或菜单类型不是面板且配置了链接地址的
            if (type === 'scene' && m.target !== '_blank') {
                if (m.type === 'panel' || (m.type !== 'panel' && m.Val)) return m;
            } else if (m.Val && m.Val !== 'disabled' && ((!target && m.target !== '_blank') || target) && (!m.Children || !m.Children.length)) {
                return m;
            }
    
            let cm = GetFirstMenu(m.Children || [], type, target);
            if (cm) {
                return cm;
            }
        }
        return null;
    },
    // 递归查找指定的菜单
    GetMenuForId(menus, id){
        for (let o of menus || []) {
            if (o.Id === id) return o;
            const o_ = GetMenuForId(o.Children, id);
            if (o_) return o_;
        }
        return null;
    },
    // 防抖
    debounce(delay, callback) {
        let timeoutID;
        function wrapper() {
            const self = this;
            const args = arguments;

            function exec() {
                callback.apply(self, args);
            }

            clearTimeout(timeoutID);

            timeoutID = setTimeout(exec, delay);
        }

        return wrapper;
    },
    setLocalStorageInfo(token, expire, refreshToken, tenantId) {
        if (expire) {
            let secondsVal = moment().add(Number(expire), 'seconds').format('x');
            window.localStorage.setItem('tokenTime', secondsVal);
        }
        window.localStorage.setItem('UniWimAuthorization', token);
        window.localStorage.setItem('UniWimExpire', expire);
        window.localStorage.setItem('UniWimRefreshToken', refreshToken);
        window.localStorage.setItem('UniWimTenantId', tenantId);
    },
    // 获取token
    GetAuthorization() {
        ;
        let urlToken = utils.GetQueryString('uniwater_utoken', 'hash')
            || utils.GetQueryString('uniwater_utoken')
            || utils.GetQueryString('token', 'hash')
            || utils.GetQueryString('token',)
            || utils.GetQueryString('utoken', 'hash')
            || utils.GetQueryString('utoken',);
        if (urlToken && urlToken !== 'null'&& urlToken !== 'undefined') {

        }
        else if (sessionStorage.getItem('UniWimAuthorization')) {
            let token = sessionStorage.getItem('UniWimAuthorization');
            urlToken = (token && token !== 'null'&& token !== 'undefined') ? token : '';
        }
        else if (localStorage.getItem('UniWimAuthorization')) {
            let token = localStorage.getItem('UniWimAuthorization');
            urlToken = (token && token !== 'null'&& token !== 'undefined') ? token : '';
        }
        sessionStorage.removeItem('UniWimAuthorization');
        urlToken && sessionStorage.setItem('UniWimAuthorization', urlToken);
        return urlToken
    },
    /**
     * 判断是否是ios
     */
    isIosAndMac() {
        const u = navigator.userAgent;
        return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) || !!u.match(/\(M[^;]+; Intel Mac OS X/);
    },
    isIos() {
        const u = navigator.userAgent;
        return /iPad|iPhone|iPod/.test(u) && !/Macintosh/.test(u);
    },
    isSafari() {
        const u = navigator.userAgent;
        return /Safari/.test(u) && !/Chrome/.test(u);
    },
    isAndroid() {
        const u = navigator.userAgent;
        return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
    },
    isMobile() {
        return utils.isIos() || utils.isAndroid();
    },
    // 动态修改var变量
    setRootVars(obj) {
        Object.keys(obj).forEach((key) => {
            document.documentElement.style.setProperty(key, obj[key]);
        })
    },
    isJson(str) {
        if (typeof str == 'string') {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == 'object' && obj) {
                    return true;
                } else {
                    return false;
                }
            } catch (e) {
                return false;
            }
        }
    },
    extractLandlineNumbers(text) {
        const regex = /(?:\(?0\d{2,3}\)?[- ][1-9]\d{6,7}|\b0\d{9,10}\b)(?:[-\u8f6c#][1-9]\d{0,3})?/g;
        let phoneArray = text.match(regex) || []
        //特殊处理
        const regexPhone = /\d{4}-\d{6}/g;
        let phoneArray2 = text.match(regexPhone) || []
        return [...new Set(phoneArray.concat(phoneArray2))];
    },
    removeHTMLTags(str) {
        return str.replace(/<[^>]*>/g, '');
    },
    cosineSimilarity(str1, str2) {
        // 将字符串转换为词频向量
        const getVector = (str) => {
            const words = str.split("");
            const vector = {};
            for (const word of words) {
                vector[word] = (vector[word] || 0) + 1;
            }
            return vector;
        };

        const vector1 = getVector(str1);
        const vector2 = getVector(str2);

        // 计算点积
        let dotProduct = 0;
        for (const key in vector1) {
            if (vector2[key]) {
                dotProduct += vector1[key] * vector2[key];
            }
        }

        // 计算向量模长
        const magnitude = (vector) => {
            let sum = 0;
            for (const key in vector) {
                sum += vector[key] ** 2;
            }
            return Math.sqrt(sum);
        };

        const magnitude1 = magnitude(vector1);
        const magnitude2 = magnitude(vector2);

        // 计算余弦相似度
        return dotProduct / (magnitude1 * magnitude2);
    },
    getHost(){
        let location = window.location;
        let host = utils.GetQueryString("host") || utils.GetQueryString("host", "hash");
        let load = utils.GetQueryString("load") || utils.GetQueryString("load", "hash");

        // 非离线环境不处理host逻辑
        if(!load || load !== 'disk'){
            return ""
        }
        if(host){
            host = decodeURIComponent(host);
            sessionStorage.setItem("host", host);
        }
        else{
            host = sessionStorage.getItem("host") || "";
        }
        host = decodeURIComponent(host)

        //匹配安卓host
        if(location.origin.toLowerCase() === "file://"){
            return host;
        }

        //匹配ios host
        let code = location.port.substring(1);
        let regex = new RegExp(`^${location.origin}/packages/${code}/`, "i");
        if(regex.test(location.href)){
            return host;
        }

        //匹配在线http模式
        if(location.href.startsWith('http') && !location.href.startsWith(`${location.origin}/packages/${code}/`)){
            return host || location.origin
        }

        return "";
    },
    // 替换字符串中xxx=xxx内容
    removeParameterFromUrl(url, key) {
        // 先移除指定 key 的参数
        let newUrl = url.replace(new RegExp(`${key}=[^&]*(?=&|$)`), '');
        // 移除多余的 & 符号
        newUrl = newUrl.replace(/&+/g, '&');
        // 移除开头多余的 &
        newUrl = newUrl.replace(/^(&|\?)/, '');
        // 移除结尾多余的 &
        newUrl = newUrl.replace(/(&|\?)$/, '');
        // 如果只剩下一个 ?，则移除它
        if (newUrl.endsWith('?')) {
            newUrl = newUrl.slice(0, -1);
        }
        // 处理只剩 ? 或 & 分隔符的情况
        newUrl = newUrl.replace(/\?&/, '?');
        return newUrl;
    },
    // 获取指定参数值
    getUrlParameter(url, paramName) {
        const regex = new RegExp(`[?&]${paramName}=([^&#]*)`);
        const results = url.match(regex);
        return results === null ? null : decodeURIComponent(results[1].replace(/\+/g, ' '));
    },
    /**
     * 从配置对象中提取所有 ${variable} 格式的变量引用
     * @param {Object|Array|string} config - 配置对象、数组或字符串
     * @returns {Set<string>} 包含所有变量名的Set集合
     */
    extractTemplateVariables(config) {
        const variables = new Set();
        
        // 递归函数处理不同类型的值
        function processValue(value) {
            if (typeof value === 'string') {
                // 正则表达式匹配 ${variable} 格式
                const regex = /\$\{([^}]+)\}/g;
                let match;
                
                while ((match = regex.exec(value)) !== null) {
                    variables.add(match[1]); // 添加变量名（不包含${}）
                }
            } else if (typeof value === 'object' && value !== null) {
                // 处理对象或数组
                if (Array.isArray(value)) {
                    value.forEach(processValue);
                } else {
                    Object.values(value).forEach(processValue);
                }
            }
        }
        
        // 开始处理配置
        processValue(config);
        
        return variables;
    },

    formatTime(time) {
        const now = moment();
        const targetTime = moment(time);

        // 判断是否为今天
        if (now.isSame(targetTime, 'day')) {
            return targetTime.format('HH:mm:ss');
        }

        // 判断是否为今年
        if (now.isSame(targetTime, 'year')) {
            return targetTime.format('MM-DD HH:mm:ss');
        }

        // 其他情况，显示完整年份
        return targetTime.format('YYYY-MM-DD HH:mm:ss');
    },

    // 文件大小换算
    formatSize(num) {
        if (!num) return '0KB';
        const kb = num / 1024;
        let size = 0;
        let unit = 'KB';
        if (kb >= 1024) {
            size = kb / 1024;
            unit = 'M';
        } else {
            size = kb;
        }
        return size.toFixed(2) + unit;
    },

    // 根据文件名称判断文件类型
    getFileIcon(name) {
        if (!name) {
            return wtf
        }
        const nameArr = name.split('.')
        if(nameArr[nameArr.length - 1]){
            const fileType = nameArr[nameArr.length - 1]
            if (/(doc|docx)$/.test(fileType.toLowerCase())) {
                return docx
            } else if (/(xls|xlsx)$/.test(fileType.toLowerCase())) {
                return excel
            } else if (/(pdf)$/.test(fileType.toLowerCase())) {
                return pdf
            } else if (/(png|jpg|jpeg|gif)$/.test(fileType.toLowerCase())) {
                return png
            }else {
                return wtf
            }
        }
        return wtf
    },

};

export default utils;
