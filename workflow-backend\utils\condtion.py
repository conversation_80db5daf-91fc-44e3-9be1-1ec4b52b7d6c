from typing import Dict, List, Any
import ast
from datetime import datetime, time

from utils.types import cast_float, cast_list, cast_datetime, cast_time, cast_bool, cast_str

_mapping: Dict[str, List[str]] = {
    # 比较操作（数字、日期通用）
    "gt": [">", "gt", "greater", "greaterthan", "大于"],
    "lt": ["<", "lt", "less", "lessthan", "小于"],
    "ge": [">=", "ge", "greaterequal", "greaterthanorequal", "大于等于"],
    "le": ["<=", "le", "lessthanorequal", "小于等于"],
    "eq": ["=", "eq", "equals", "等于", "相等"],
    "ne": ["!=", "<>", "ne", "notequals", "不等于", "不相等"],

    # 集合/字符串操作
    "contains": ["contains", "in"],
    "not_contains": ["not_contains", "not_in", "notcontains", "notin"],

    # 字符串特有操作
    "startswith": ["startswith", "prefix"],
    "endswith": ["endswith", "suffix"],
}

mapper: Dict[str, str] = {alias.lower(): op_key for op_key, aliases in _mapping.items() for alias in aliases}

# 定义各数据类型支持的操作（用于校验）
_type_supported_ops: Dict[str, List[str]] = {
    "float": ["gt", "lt", "ge", "le", "eq", "ne"],
    "bool": ["eq", "ne"],
    "datetime": ["gt", "lt", "ge", "le", "eq", "ne"],
    "list": ["contains", "not_contains", "eq", "ne"],
    "str": ["eq", "ne", "contains", "not_contains", "startswith", "endswith"],
}




def test_float(first: float, second: float, opt: str) -> bool:

    op_type = mapper.get(opt,"")

    if op_type not in _type_supported_ops["float"]:
        raise ValueError(f"数字类型不支持操作：{opt}")

    # 执行对应操作
    if op_type == "gt":
        return first > second
    elif op_type == "lt":
        return first < second
    elif op_type == "ge":
        return first >= second
    elif op_type == "le":
        return first <= second
    elif op_type == "eq":
        return first == second
    elif op_type == "ne":
        return first != second
    return False


def test_bool(first: bool, second: bool, opt: str) -> bool:
    op_type = mapper.get(opt,"")

    if op_type not in _type_supported_ops["bool"]:
        raise ValueError(f"布尔类型不支持操作：{opt}")

    if op_type == "eq":
        return first == second
    elif op_type == "ne":
        return first != second
    return False


# def test_datetime(first: Any, value: Any, opt: str) -> bool:
#     # 假设str_to_datetime和hms_str_to_time已实现
#     def str_to_datetime(s: str) -> datetime:
#         return datetime.fromisoformat(s)
#
#     def hms_str_to_time(s: str) -> time:
#         h, m, s = map(int, s.split(':'))
#         return time(hour=h, minute=m, second=s)
#
#     try:
#         ft = str_to_datetime(field)
#         st = str_to_datetime(value)
#     except ValueError:
#         ft = hms_str_to_time(field)
#         st = hms_str_to_time(value)
#
#     return test_datetime(ft, st, opt)


def test_datetime(first: datetime | time, second: datetime | time, opt: str) -> bool:
    if not (isinstance(first, (datetime, time)) and isinstance(second, (datetime, time))):
        raise TypeError("参数必须是datetime或time对象")
    if type(first) != type(second):
        raise TypeError(f"参数类型必须一致（{type(first).__name__} vs {type(second).__name__}）")

    op_type = mapper.get(opt,"")
    if op_type not in _type_supported_ops["datetime"]:
        raise ValueError(f"日期时间类型不支持操作：{opt}")

    if op_type == "gt":
        return first > second
    elif op_type == "lt":
        return first < second
    elif op_type == "ge":
        return first >= second
    elif op_type == "le":
        return first <= second
    elif op_type == "eq":
        return first == second
    elif op_type == "ne":
        return first != second
    return False


def test_list(first: List, second: List, opt: str) -> bool:

    op_type = mapper.get(opt,"")
    if op_type not in _type_supported_ops["list"]:
        raise ValueError(f"列表类型不支持操作：{opt}")

    if op_type == "contains":
        return all(item in first for item in second)
    elif op_type == "not_contains":
        return any(item not in first for item in second)
    elif op_type == "eq":
        if len(first) != len(second):
            return False
        return all(a == b for a, b in zip(first, second))
    elif op_type == "ne":
        if len(first) != len(second):
            return True
        return not all(a == b for a, b in zip(first, second))
    return False


def test_str(first: str, second: str, opt: str) -> bool:
    op_type = mapper.get(opt,"")

    if op_type not in _type_supported_ops["str"]:
        raise ValueError(f"字符串类型不支持操作：{opt}")

    if op_type == "eq":
        return first == second
    elif op_type == "ne":
        return first != second
    elif op_type == "contains":
        return second in first
    elif op_type == "not_contains":
        return second not in first
    elif op_type == "startswith":
        return first.startswith(second)
    elif op_type == "endswith":
        return first.endswith(second)
    return False

def test_one(field, value, opt):

    data_type,first,second = determine_data_type(field, value, opt)
    match data_type.lower():
        case "bool":
            return test_bool(first, second, opt)
        case "float":
            return test_float(first, second, opt)
        case "datetime" | "time":
            return test_datetime(first, second, opt)
        case "list":
            return test_list(first, second, opt)
        case "str":
            return test_str(first, second, opt)
        case _:
            raise Exception(f"不能确定数据类型{data_type}")


def determine_data_type(field: Any, value: Any, opt: str) -> (str,Any,Any):
    type_checkers = [
        ("list", cast_list),
        ("datetime", cast_datetime),
        ("time", cast_time),
        ("bool", cast_bool),
        ("float", cast_float),
        ("str", cast_str),
    ]

    for type_name, caster in type_checkers:
        first = caster(field)
        second = caster(value)
        if first is not None and second is not None:
            return type_name, first, second
    #正常不会运行到这
    return "error","",""