*** Settings ***
Suite Setup      Run Tests    ${EMPTY}    standard_libraries/process/terminate_process.robot
Resource         atest_resource.robot

*** Test Cases ***
Terminate process
    ${tc} =    Check Test Case    ${TESTNAME}
    Check Log Message    ${tc[1, 0]}    Gracefully terminating process.
    Check Log Message    ${tc[1, 1]}    Process completed.

Kill process
    ${tc} =    Check Test Case    ${TESTNAME}
    Check Log Message    ${tc[1, 0]}    Forcefully killing process.
    Check Log Message    ${tc[1, 1]}    Process completed.

Terminate process running on shell
    Check Test Case    ${TESTNAME}

Kill process running on shell
    [Tags]    no-windows
    Check Test Case    ${TESTNAME}

Also child processes are terminated
    Check Test Case    ${TESTNAME}

Also child processes are killed
    [Tags]    no-windows
    Check Test Case    ${TESTNAME}

Kill process when terminate fails
    ${tc} =    Check Test Case    ${TESTNAME}
    Check Log Message    ${tc[5, 0]}    Gracefully terminating process.
    Check Log Message    ${tc[5, 1]}    Graceful termination failed.
    Check Log Message    ${tc[5, 2]}    Forcefully killing process.
    Elapsed Time Should Be Valid    ${tc.elapsed_time}    minimum=2

Terminating already terminated process is ok
    Check Test Case    ${TESTNAME}

Waiting for terminated process is ok
    Check Test Case    ${TESTNAME}

Terminate all processes
    ${tc} =    Check Test Case    ${TESTNAME}
    Check Log Message   ${tc[14, 0]}    Gracefully terminating process.

Terminating all empties cache
    Check Test Case    ${TESTNAME}
