<template>
  <div class="box" v-loading="loading">
    <div class="table-box">
      <div class="table-header">
        <div class="table-header-title">
          <!-- <i class="table-header-icon iconfont icon-yingyong01"></i> -->
          <el-dropdown @command="handleCommand" trigger="click">
            <span class="el-dropdown-link">
              {{ type == 'manager' ? '工作台' : '任务记录' }}<el-icon class="table-header-icon-right el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :class="{ selected: type === 'manager' }" command="manager">
                  <span class="el-dropdown-item-title">工作台</span>
                </el-dropdown-item>
                <el-dropdown-item :class="{ selected: type === 'record' }" command="record">
                  <span class="el-dropdown-item-title">任务记录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="table-header-tools">
          <!-- <div class="table-header-tools-item" @click="openExcelTemplate">
            <el-icon :size="16" title="excel模板">
              <Grid />
            </el-icon>
            <span>excel模板</span>
          </div> -->
          <el-button class="table-header-tools-item" @click="onReset">
            <i class="action-iconfont icon-shuaxinzhongzhi"></i>
            <span>刷新</span>
          </el-button>
          <el-button class="table-header-tools-item" @click="onAdd">
            <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
            <span>新增</span>
          </el-button>
           <!-- && currentUser?.id !== current?.creatorId -->
          <el-button class="table-header-tools-item" :disabled="currentRow == null" @click="onEdit">
            <i class="action-iconfont icon-bianji"></i>
            <span>编辑</span>
          </el-button>
          <el-button class="table-header-tools-item" :disabled="currentRow == null || (currentRow && currentRow.taskState === 'running')" @click="onDelete">
            <i class="action-iconfont icon-huishouzhanshanchu"></i>
            <span>删除</span>
          </el-button>
          <el-divider direction="vertical" style="margin: 0 16px" />
          <el-button class="table-header-tools-item" style="margin-right: 12px;" @click="goHome">
            <i class="action-iconfont icon-shouye1"></i>
            <span>介绍</span>
          </el-button>
          <el-dropdown placement="bottom-end" size="small">
            <el-button>
              <span class="header-tools-drop-text">
                <span style="margin-right: 5px;font-size: 12px;">更多</span>
                <el-icon><CaretBottom /></el-icon>
              </span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu class="header-tools-drop-menu">
                <el-dropdown-item @click="openDetail('guihua')" title="规划">
                  <i class="action-iconfont icon-guihua header-tools-drop-iconfont"></i>
                  <span>规划</span>
                </el-dropdown-item>
                <el-dropdown-item @click="openDetail('yuanxing')" title="原型">
                  <i class="action-iconfont icon-yuanxing header-tools-drop-iconfont"></i>
                  <span>原型</span>
                </el-dropdown-item>
                <el-dropdown-item @click="openDetail('xiangshe')" title="详设">
                  <i class="action-iconfont icon-xiangshe header-tools-drop-iconfont"></i>
                  <span>详设</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="table-condition">
        <el-form :inline="true" :model="params">
          <el-form-item label="状态">
            <el-select v-model="params.state" placeholder="请选择状态" style="width: 100px">
              <el-option v-for="it in state" :key="it.Value" :label="it.Name" :value="it.Value" />
            </el-select>
          </el-form-item>
          <el-form-item label="任务名称">
            <el-input v-model="params.missionName" placeholder="请输入任务名称" clearable style="width: 160px" @keyup.enter="onSubmit" />
          </el-form-item>
          <el-form-item label="创建时间" style="margin-right: 8px;">
            <el-date-picker v-model="params.missionStartTime" type="date" value-format="YYYY-MM-DD HH:mm" style="width: 150px" @change="changeTimeStart" />
          </el-form-item>
          <span style="fontSize: 12px;">至</span>
          <el-form-item label="" style="margin-left: 8px;">
            <el-date-picker v-model="params.missionEndTime" type="date" value-format="YYYY-MM-DD HH:mm" style="width: 150px" :disabled-date="disabledDateEnd" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">
              <template #icon>
                <i class="action-iconfont icon-sousuofangdajing" style="font-size: 12px;"></i>
              </template>
              查询
            </el-button>
          </el-form-item>
        </el-form>
        <div class="tab-list">
          <div class="tab-list-item left" :class="{'active': tabType == 'table'}" @click="changeTabType('table')">列表视图</div>
          <div class="tab-list-item right" :class="{'active': tabType == 'card'}" @click="changeTabType('card')">卡片视图</div>
        </div>
      </div>
      <div class="table-content crud-tag-style" v-show="tabType == 'table'">
        <el-table class="crud-table" ref="tableRef" :data="tableData" stripe border :show-overflow-tooltip="true" :highlight-current-row="true"
          style="width: 100%;height: calc(100% - 48px)" @rowClick="handleRowClick" @row-dblclick="handleRowDblClick">
          <el-table-column type="index" label="序号" :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
          <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center"
            :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed" :show-overflow-tooltip="it.showOverflowTooltip">
            <template v-if="it.data == 'taskState'" #default="scope">
              <el-tag type="primary" round v-if="scope.row.taskState == 'running'">
                <el-tooltip content="任务已到开始时间未超过结束时间" placement="top">执行中</el-tooltip>
              </el-tag>
              <el-tag type="info" round v-else-if="scope.row.taskState == 'unable'">
                <el-tooltip content="任务停用" placement="top">已停用</el-tooltip>
              </el-tag>
              <el-tag type="info" round v-else-if="scope.row.taskState == 'end'">
                <el-tooltip content="任务超过结束时间" placement="top">已结束</el-tooltip>
              </el-tag>
              <el-tag type="warning" round v-else-if="scope.row.taskState == 'wait'">
                <el-tooltip content="任务未到开始时间" placement="top">待执行</el-tooltip>
              </el-tag>
            </template>
            <!-- <template v-else-if="it.data == 'missionName'" #default="{ row }">
              <div class="mission-name">
                <el-tooltip :content="row.missionName" placement="top">
                  <span>{{ row.missionName }}</span>
                </el-tooltip>
                <el-tooltip content="任务介绍" placement="top" v-if="row.reportUrl">
                  <div class="report-describe" @click="openReportUrl(row)">
                    <i class="action-iconfont icon-zhihangrenwu"></i>
                  </div>
                </el-tooltip>
              </div>
            </template> -->
            <template v-else-if="it.data == 'timeScheduled'" #default="scope">
              <span v-if="scope.row.timeScheduled">{{ formatCronToText(scope.row) }}</span>
            </template>
            <template v-else-if="it.data == 'nodeNum'" #default="scope">
              {{scope.row.nodeNum || '-'}}
            </template>
            <template v-else-if="it.data == 'sRate'" #default="scope">
              {{(Number(scope.row.executeCount) + Number(scope.row.failCount) === 0 ? 0 : Math.round((Number(scope.row.executeCount) / (Number(scope.row.executeCount) + Number(scope.row.failCount)) * 100)))}}%
            </template>
            <template v-else-if="it.data == 'reportUser'" #default="scope">
              {{scope.row.reportUserName}}
            </template>
            <template v-else-if="it.data == 'count'" #default="scope">
              <el-tooltip v-if="Number(scope.row.executeCount) +
                Number(scope.row.failCount) > 0" :content="`成功次数：${scope.row.executeCount || 0}；失败次数：${scope.row.failCount || 0}`" placement="top">
                <el-link type="primary" @click="onTaskRecord(scope.row)">
                  <span>{{ Number(scope.row.executeCount) || 0 }}</span>
                  <span style="margin: 0 4px;"> / </span>
                  <span style="color: red;">{{ Number(scope.row.failCount) || 0 }}</span>
                </el-link>
              </el-tooltip>
              <span v-else>{{ Number(scope.row.executeCount) +
                Number(scope.row.failCount) }}</span>
            </template>
            <template v-else-if="it.dtype == 'date' && it.format" #default="scope">
              {{ (it.data === 'nextStartTime' && (scope.row.taskState === 'end' || scope.row.taskState === 'unable')) || !scope.row[it.data] ? '/' : formatTime(scope.row[it.data], it.format) }}
            </template>
            <template v-else-if="it.data == 'handle'" #default="scope">
              <div style="white-space: normal;" v-if="scope.row.creatorId === currentUser?.id">
                <el-button size="small" class="task-button primary"
                  @click.stop="onRunTask(scope.row)" :disabled="!!executeId" >
                  <el-tooltip v-if="executeId && executeId !== scope.row.id" content="请等待当前任务执行完成" placement="top">
                    <span>{{ executeId === scope.row.id ? '执行中' : '手动执行' }}</span>
                  </el-tooltip>
                  <span v-else>{{ executeId === scope.row.id ? '执行中' : '手动执行' }}</span>
                </el-button>
                <el-button size="small" class="task-button" @click.stop="onRunLastView(scope.row)">监控</el-button>
                <!-- 点击立即执行的任务不能进行编排 -->
                <el-button size="small" class="task-button" :disabled="executeId === scope.row.id"
                  @click.stop="onRunArrange(scope.row)">编排</el-button>
                <el-button size="small" class="task-button" @click.stop="onRowActiveChange(scope.row)">{{ scope.row.active ?
                  '停用' : '启用'}}</el-button>
                <el-tooltip v-if="!scope.row.reportUrl" :content="'暂无说明'" placement="top">
                  <el-button size="small" class="task-button" :disabled="true" @click.stop="openReportUrl(scope.row)">说明</el-button>
                </el-tooltip>
                <el-button v-else size="small" class="task-button" :disabled="false" @click.stop="openReportUrl(scope.row)">说明</el-button>
              </div>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 50vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery(false)" />
        </div>
      </div>
      <div class="card-content crud-tag-style" v-show="tabType == 'card'">
        <div class="card-item-box" v-if="tableData.length > 0">
          <div class="card-item" :class="{'active': item.id == currentRow?.id, 'running': item.taskState === 'running', 'wait': item.taskState === 'wait', 'end': item.taskState === 'end' || item.taskState === 'unable'}" v-for="item in tableData" :key="item.id" @click="handleRowClick(item, true)">
            <div class="card-item-title-box">
              <!-- <div class="card-item-title-bg"></div> -->
              <div class="card-item-title" :title="item.missionName">{{ item.missionName }}</div>
              <div class="card-item-tag-time">
                <el-tag type="primary" round v-if="item.taskState == 'running'">
                  <el-tooltip content="任务已到开始时间未超过结束时间" placement="top">执行中</el-tooltip>
                </el-tag>
                <el-tag type="info" round v-else-if="item.taskState == 'unable'">
                  <el-tooltip content="任务停用" placement="top">已停用</el-tooltip>
                </el-tag>
                <el-tag type="info" round v-else-if="item.taskState == 'end'">
                  <el-tooltip content="任务超过结束时间" placement="top">已结束</el-tooltip>
                </el-tag>
                <el-tag type="warning" round v-else-if="item.taskState == 'wait'">
                  <el-tooltip content="任务未到开始时间" placement="top">待执行</el-tooltip>
                </el-tag>
              </div>
            </div>
            <div class="card-item-info-box">
              <div class="card-item-info">
                <div class="card-item-info-item">
                  <span>节点数：</span>
                  <span>{{item.nodeNum || '-'}}</span>
                </div>
                <div class="card-item-info-item" v-if="Number(item.executeCount) + Number(item.failCount) > 0">
                  <span>运行：</span>
                  <span>{{Number(item.executeCount) + Number(item.failCount)}}次</span>
                </div>
                <div class="card-item-info-item" v-if="Number(item.executeCount) + Number(item.failCount) > 0">
                  <span>成功率：</span>
                  <span>{{ Math.round((Number(item.executeCount) / (Number(item.executeCount) + Number(item.failCount)) * 100)) }}%</span>
                </div>
              </div>
              <span class="card-item-tag-time-text" v-if="item.timeScheduled">{{ formatCronToText(item) }}</span>
            </div>
            <!-- <div class="card-item-line"></div> -->
            <div class="card-item-two-text">
              <div class="card-item-two-text-item">
                <div class="card-item-text1">下次运行</div>
                <div class="card-item-text2">{{ (item.taskState === 'end' || item.taskState === 'unable' || !item.nextStartTime) ? '/' : formatTime(item.nextStartTime, 'yyyy-MM-DD HH:mm') }}</div>
              </div>
              <div class="card-item-two-text-item">
                <div class="card-item-text1">最后运行</div>
                <div class="card-item-text2">{{ item.lastExecuteTime ? formatTime(item.lastExecuteTime, 'yyyy-MM-DD HH:mm') : '/' }}</div>
              </div>
              <div class="card-item-two-text-item">
                <div class="card-item-text1">通知对象</div>
                <div class="card-item-text2" :title="item.reportUserName">{{ item.reportUserName || '' }}</div>
              </div>
              <div class="card-item-two-text-item">
                <div class="card-item-text1">最后编辑</div>
                <div class="card-item-text2">{{ item.updater || '' }}</div>
              </div>
            </div>
            <div class="card-item-buttons">
              <div class="card-item-button primary" @click.stop="onRunTask(item)" :class="{'disabled': !!executeId}">{{ executeId === item.id ? '执行中' : '手动执行' }}</div>
              <div class="card-item-button" @click.stop="onRunLastView(item)">监控</div>
              <div class="card-item-button" :class="{'disabled': executeId === item.id}" @click.stop="onRunArrange(item)">编排</div>
              <div class="card-item-button" @click.stop="onRowActiveChange(item)">{{ item.active ? '停用' : '启用'}}</div>
            </div>
          </div>
        </div>
        <div class="card-item-box" style="grid-template-columns: repeat(1, 1fr);grid-template-rows: auto;justify-content: center;" v-else>
          <div class="empty-box">
            <el-empty description="暂无数据" />
          </div>
        </div>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery(false)" />
        </div>
      </div>
    </div>
    <div class="copyright">Copyright ©浙江和达科技股份有限公司</div>
    <el-dialog v-model="dialogFormVisible" :title="isNew ? '新增' : '编辑'" width="650" style="border-radius: 12px;" top="30vh">
      <el-form :model="form" :show-message="false">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="missionName" label="任务名称" required :label-line="true">
              <el-input v-model.trim="form.missionName" :maxlength="32" type="text" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="execution" label="执行时间" required :label-line="true">
              <el-radio-group v-model="form.execution" @change="onExecutionChange($event, form)">
                <el-radio :value="1">按分钟</el-radio>
                <el-radio :value="2">按小时</el-radio>
                <el-radio :value="3">按天</el-radio>
                <el-radio :value="4">按周</el-radio>
                <el-radio :value="5">指定时间</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="form.execution === 5 ? 12 : 24">
            <el-form-item v-if="form.execution === 1" class="flex-inline-form" prop="minutes" required>
              <span>执行间隔</span>
              <el-input-number v-model="form.minutes" :min="5" :max="59" clearable />
              <span>分钟</span>
            </el-form-item>
            <el-form-item v-if="form.execution === 2" class="flex-inline-form" prop="hours" required>
              <span>执行间隔</span>
              <el-input-number v-model="form.hours" :min="1" :max="23" clearable />
              <span>小时</span>
            </el-form-item>
            <el-form-item v-if="form.execution === 3" class="flex-inline-form" prop="days" required>
              <span>执行间隔</span>
              <el-input-number v-model="form.days" :min="1" :max="31" clearable />
              <span>天</span>
            </el-form-item>
            <el-form-item v-if="form.execution === 4" class="flex-inline-form" prop="weeks" required>
              <el-checkbox-group v-model="form.weeks">
                <el-checkbox v-for="(name, index) in weeks" :key="index" :value="String(index + 1)">{{ name }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item v-if="form.execution === 5" prop="specify" label="选择时间" required
              :label-line="true">
              <el-date-picker v-model="form.specify" type="datetime" format="YYYY-MM-DD HH:mm" placeholder=""
                :disabled-date="specifyTimeOptions" :disabled-hours="specifyDisabledHours" :disabled-minutes="specifyDisabledMinutes" />
            </el-form-item>
          </el-col>
          <template v-if="[1, 2, 3, 4].includes(form.execution)">
            <el-col :span="12">
              <el-form-item prop="missionStartTime" label="开始时间" required
                :label-line="true">
                <el-date-picker v-model="form.missionStartTime" format="YYYY-MM-DD HH:mm" type="datetime" placeholder=""
                  :disabled-date="startTimeOptions" :disabled-hours="startDisabledHours" :disabled-minutes="startDisabledMinutes" @change="onStartTimeChange" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="flex-inline-form" prop="missionEndTime" label="结束时间" :label-line="true">
                <el-date-picker v-model="form.missionEndTime" format="YYYY-MM-DD HH:mm" type="datetime" placeholder=""
                  :disabled-date="endTimeOptions" :disabled-hours="endDisabledHours" :disabled-minutes="endDisabledMinutes" />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item class="flex-inline-form" prop="reportUser" label="通知对象" :label-line="true">
              <tree-picker-user v-model='form.reportUser' title="人员选择" @change="handleReportUserChange"></tree-picker-user>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="flex-inline-form" prop="reportUrl" label="说明文档" :label-line="true">
              <el-input v-model.trim="form.reportUrl" type="text" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.missionType === 'dify'">
            <el-form-item prop="appKey" label="密钥" :label-line="true">
              <el-input v-model="form.appKey" type="text" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="save">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 底部抽屉 -->
    <el-drawer v-model="drawerVisible" :show-close="false" :close-on-click-modal="true" :modal="false" :append-to-body="false" modal-class="mask-layer" direction="btt" size="100%">
      <template #header="{ close, titleId, titleClass }">
        <h4 :id="titleId" :class="titleClass">
          运行记录
          <span class="two-text">{{ maskTableTitle }}</span>
        </h4>
        <el-button type="default" @click="close">
          关闭
        </el-button>
      </template>
      <div class="mask-table-box crud-tag-style" v-loading="maskLoading">
        <el-table class="mask-table" :data="maskTableData" border :show-overflow-tooltip="true" :highlight-current-row="true"
          style="width: 100%;height: calc(100% - 48px)">
          <el-table-column type="index" label="序号" :index="1 + maskPagination.pageSize * (maskPagination.currentPage - 1)" align="center" width="100" />
          <el-table-column v-for="it in maskTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center"
            :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
            <template v-if="it.data == 'executeState'" #default="scope">
              <el-tag type="info" round v-if="scope.row.executeState == -1">失败</el-tag>
              <el-tag type="danger" round v-else-if="scope.row.executeState == 0">执行中</el-tag>
              <el-tag type="success" round v-else-if="scope.row.executeState == 1">成功</el-tag>
            </template>
            <template v-else-if="it.dtype == 'date' && it.format" #default="scope">
              {{ formatTime(scope.row[it.data], it.format) }}
            </template>
            <template v-else-if="it.data == 'handle'" #default="scope">
              <el-link type="primary" @click="openMonitorDialog(scope.row)">
                <span style="font-size: 12px;">运行详情</span>
              </el-link>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 30vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="maskPagination.currentPage" v-model:page-size="maskPagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="maskPagination.total" @change="maskTableQuery" />
        </div>
      </div>
    </el-drawer>

    <!-- 模板设计器组件 -->
    <!-- <report-designer v-model="isReportDesignerVisible" from="crud" @template-saved="onTemplateSaved" /> -->
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import moment from "moment";
import saasApi from '@/api/index';
import { TaskActions, FormatTaskActionName } from "@/utils/taskActions";
import { useUserStore } from "@/stores/user";
import { ElMessage, ElMessageBox } from 'element-plus'
import TreePickerUser from '@/components/treePickerUser.vue'
// import ReportDesigner from '@/components/panels/ReportDesigner.vue'
// @ts-ignore
import utils from '@/utils/utils'

const userStore = useUserStore()

export default defineComponent({
  name: 'manager',
  components: { TreePickerUser },
  data() {
    return {
      loading: false,
      type: 'manager',
      params: {
        state: 'all',
        missionName: "",
        missionStartTime: null,
        missionEndTime: null
      },
      pagination: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      },
      state: [
        {Name: '全部', Value: 'all'},
        {Name: '执行中', Value: 'running'},
        {Name: '已停用', Value: 'unable'},
        // {Name: '未开始', Value: 'notStarted'},
        {Name: '已结束', Value: 'end'},
        {Name: '待执行', Value: 'wait'}
      ],
      currentTime: moment(moment().add(-1, 'd').format("YYYY-MM-DD 00:00:00")).valueOf(),
      taskActions: TaskActions,
      weeks: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      currentRow: null,
      tableData: [],
      tableColumns: [
        { data: 'taskState', title: '状态', scoped: 'taskState', width: 85, orderable: true, filterable: true },
        { data: 'missionName', title: '任务名称', scoped: 'missionName', minWidth: 200, showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'timeScheduled', title: '计划周期', width: 170, scoped: 'timeScheduled', showOverflowTooltip: true },
        { data: 'reportUser', title: '通知对象', width: 100, scoped: 'reportUser', showOverflowTooltip: true, filterable: true },
        { data: 'nodeNum', title: '节点数', width: 70, scoped: 'nodeNum', showOverflowTooltip: true, orderable: true },
        { data: 'count', title: '运行次数', width: 90, scoped: 'count', showOverflowTooltip: true, orderable: true },
        { data: 'sRate', title: '成功率', width: 90, scoped: 'sRate', showOverflowTooltip: true, orderable: true },
        { data: 'nextStartTime', title: '下次运行', width: 150, dtype: 'date', format: 'yyyy-MM-DD HH:mm', showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'lastExecuteTime', title: '最后运行', width: 150, dtype: 'date', format: 'yyyy-MM-DD HH:mm', showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'missionStartTime', title: '任务开始时间', width: 150, dtype: 'date', format: 'yyyy-MM-DD HH:mm', showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'missionEndTime', title: '任务结束时间', width: 150, dtype: 'date', format: 'yyyy-MM-DD HH:mm', showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'updater', title: '最后编辑人', width: 95, showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'updated', title: '最后编辑时间', width: 160, dtype: 'date', format: 'yyyy-MM-DD HH:mm', showOverflowTooltip: true, orderable: true, filterable: true },
        { data: 'handle', title: '操作', scoped: 'handle', width: 290, fixed: 'right' },
      ],


      dialogFormVisible: false,
      isNew: true,
      form: {},

      executeId: null, // 任务-立即执行任务id
      taskTimers: {}, // 任务-立即执行定时函数集合

      isReportDesignerVisible: false,

      drawerVisible: false,
      maskLoading: false,
      maskTableTitle: '',
      maskTableData: [],
      maskCurrentRow: null,
      maskPagination: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      },
      maskTableColumns: [
        {data: 'missionName', title: '任务名称', orderable: true, filterable: true},
        // {data: 'missionType', title: '任务类型', orderable: true, filterable: true},
        {data: 'executeUser', title: '运行人', width: 160},
        {data: 'executeTime', title: '运行时间', dtype: 'date', format: 'yyyy-MM-DD HH:mm'},
        // {data: 'executeTime', title: '结束时间', dtype: 'date', format: 'yyyy-MM-DD HH:mm'},
        {data: 'executeState', title: '状态', scoped: 'executeState', width: 160},
        { data: 'handle', title: '操作', scoped: 'handle', width: 220, fixed: 'right' },
      ],

      tabType: 'table',
      timer: null
    }
  },
  computed: {
    headers() {
      return {
        Authorization: utils.GetAuthorization()
      }
    },
    // 是否是集成到一诺桌面端
    isUniwimPc() {
      return this.$route.query.uniwim === 'pc'
    },
    // 用户信息
    currentUser() {
      return userStore.userInfo
    },
    // 按钮权限
    buttons() {
      return {
        insert: { disabled: false },
        update: { disabled: this.currentUser?.id !== this.current?.creatorId },
        delete: { disabled: this.currentUser?.id !== this.current?.creatorId },
      }
    },
  },
  mounted() {
    // 从缓存取默认tabType
    const tabType = localStorage.getItem('managerTabType'+(this.currentUser?.sn || '')) || ''
    if (tabType) {
      this.tabType = tabType
    }
    this.tableQuery()
    // 定时1分钟刷新当前数据
    this.timer = setInterval(() => {
      this.tableQuery(true)
    }, 1000 * 60 * 1)
  },
  methods: {
    changeTimeStart(val) {
      if (this.params.missionEndTime && moment(val).valueOf() > moment(this.params.missionEndTime).valueOf()) {
        this.params.missionEndTime = null
      }
    },
    disabledDateEnd(date) {
      let disable = false
      if (this.params.missionStartTime && date <= moment(this.params.missionStartTime).valueOf()) {
        disable = true
      }
      return disable
    },
    handleCommand(command) {
      this.type = command;
      this.$router.push({ name: this.type, query: {
        uniwim: this.$route.query.uniwim
      }});
    },
    tableQuery(noloading) {
      if (!noloading) {
        this.loading = true
        this.currentRow = null
      }
      const params = {
        conditions: [],
        data: {},
        index: this.pagination.currentPage,
        size: this.pagination.pageSize,
        order: [
          {
            Field: "created",
            Type: -1
          }
        ]
      }
      if (this.params.missionName) {
        params.conditions.push({
          Field: "missionName",
          Group: 1,
          Operate: "like",
          Relation: "and",
          Value: this.params.missionName
        })
      }
      if (this.params.state !== 'all') {
        params.data.taskState = this.params.state
      }
      if (this.params.missionStartTime) {
        params.conditions.push({
          Field: "created",
          Group: 1,
          Operate: ">=",
          Relation: "and",
          Value: moment(this.params.missionStartTime + ' 00:00:00').unix()
        })
      }
      if (this.params.missionEndTime) {
        params.conditions.push({
          Field: "created",
          Group: 1,
          Operate: "<=",
          Relation: "and",
          Value: moment(this.params.missionEndTime + ' 23:59:59').unix()
        })
      }
      saasApi.AIAgentMissionQuery(params).then(res => {
        if (typeof res?.rows == 'object') {
          this.pagination = {
            currentPage: res.current,
            pageSize: res.size,
            total: res.total
          }
          this.tableData = res.rows
          // 刷新当前数据后，重新设置当前选中行数据
          if (noloading && this.currentRow) {
            this.currentRow = this.tableData.find(item => item.id === this.currentRow.id) || null
            this.$nextTick(() => {
              this.$refs.tableRef.setCurrentRow(this.currentRow)
              if (!this.currentRow) {
                // 关闭弹窗
                this.dialogFormVisible = false
                this.drawerVisible = false
              }
            })
          }
        }
      }).finally(() => {
        if (!noloading) this.loading = false
      })
    },
    tableInsert(params) {
      saasApi.AIAgentMissionAdd(params).then(res => {
        if (res?.id) {
          ElMessage({
            message: '新增成功!',
            type: 'success',
            showClose: true
          })
          setTimeout(()=>{
            this.onRunArrange(res)
          },200)
        } else {
          ElMessage({
            message: '新增失败!',
            type: 'error',
            showClose: true
          })
        }
      }).finally(() => {

      })
    },
    tableUpdate(params) {
      saasApi.AIAgentMissionUpdate(params).then(res => {
        if (res?.Code === 0) {
          ElMessage({
            message: '编辑成功!',
            type: 'success',
            showClose: true
          })
          // 编辑不再跳转编排页面
          setTimeout(()=>{
            // this.onRunArrange(params)
            this.tableQuery()
          },200)
        } else {
          ElMessage({
            message: '编辑失败!',
            type: 'error',
            showClose: true
          })
        }
      }).finally(() => {

      })
    },
    tableDelete(params) {
      saasApi.AIAgentMissionDelete(params).then(res => {
        if (res.Code === 0) {
          ElMessage({
            message: '删除成功!',
            type: 'success',
            showClose: true
          })
          this.tableQuery();
        } else {
          ElMessage({
            message: res.Message || '删除失败!',
            type: 'error',
            showClose: true
          })
        }
      }).finally(() => {

      })
    },
    maskTableQuery() {
      if (!this.maskCurrentRow) return
      this.maskLoading = true
      const params = {
        conditions: [],
        data: {},
        index: this.maskPagination.currentPage,
        size: this.maskPagination.pageSize,
      }
      if (this.maskCurrentRow.id){
        params.conditions.push({
          Field: "missionId",
          Group: 1,
          Operate: "=",
          Relation: "and",
          Value: this.maskCurrentRow.id
        })
      }
      saasApi.AIAgentMissionHistoryQuery(params).then(res => {
        console.log(res)
        if (typeof res?.rows == 'object') {
          this.maskPagination = {
            currentPage: res.current,
            pageSize: res.size,
            total: res.total
          }
          this.maskTableData = res.rows
        }
      }).finally(() => {
        this.maskLoading = false
      })
    },
    // 运行记录弹窗
    openMonitorDialog(row){
      let urlOrigin = location.origin + location.pathname + `#/taskMonitor?historyId=${row.id}&missionId=${row.missionId}`
      if (this.headers?.Authorization) urlOrigin += `&uniwater_utoken=${this.headers.Authorization}`
      this.isUniwimPc
        ? window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
        : window.open(urlOrigin)
    },

    handleRowClick(row, show) {
      this.currentRow = row
      if (typeof show === 'boolean') {
        this.onTaskRecord(row)
      }
    },
    handleRowDblClick(row) {
      if (this.currentRow) this.onEdit()
    },
    openExcelTemplate() {
      console.log('打开excel模板');
      this.isReportDesignerVisible = true
    },
    // 处理模板保存
    onTemplateSaved(template) {
      ElMessage.success(`模板 "${template.name}" 保存成功`)
      console.log('保存的模板:', template)

      // 可以在这里刷新Excel组件的模板选项
      // 或者触发其他相关的更新操作
    },
    onSubmit() {
      this.tableQuery()
    },
    onReset(){
      this.params = {
        state: 'all',
        missionName: "",
        missionStartTime: null,
        missionEndTime: null
      }
      this.pagination = {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
      this.isNew = true
      this.form = {}
      this.currentRow = null
      this.tableData = []
      this.tableQuery()
    },
    async onAdd() {
      this.isNew = true
      this.dialogFormVisible = true
      this.form = JSON.parse(JSON.stringify(await this.formSet({})))
    },
    async onEdit() {
      this.isNew = false
      this.dialogFormVisible = true
      this.form = JSON.parse(JSON.stringify(await this.formSet(this.currentRow)))
    },
    onDelete() {
      if (this.currentRow?.id && this.currentRow.taskState !== 'running') {
        ElMessageBox.confirm('确定要删除选中的记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          showCancelButton: true,
          customClass: 'default-confirm-class',
          callback: (action) => {
            if (action === 'confirm') {
              this.tableDelete({
                id: this.currentRow.id
              })
            }
          },
        })
      }
    },
    goHome() {
      // this.$router.push({
      //   name: 'homepage',
      //   query: this.$route.query
      // })
      const url = `https://wimtask.dlmeasure.com/#/homepage?uniwater_utoken=${utils.GetAuthorization()}`
      window.location.href = url
    },
    formatTime(data, format) {
      if (!data) return ''
      return moment(data).format(format)
    },
    // 类型切换
    onExecutionChange(e, model) {
      if (e === 5 && !model.specify && model.missionStartTime) {
        model.specify = moment().add(10, 'm').valueOf()
      }
    },
    // 数据展示样式切换(列表还是卡片)
    changeTabType(type) {
      this.currentRow = null
      this.$refs.tableRef?.setCurrentRow()
      this.tabType = type
      localStorage.setItem('managerTabType'+(this.currentUser?.sn || ''), type)
      this.tableQuery(true)
    },
    // model转成cron 格式转换
    onModelToCron(model) {
      switch (model.execution) {
        // 按分钟
        case 1:
          return `1,${model.minutes}`
        // 按小时
        case 2:
          return `2,${model.hours}`
        // 按天
        case 3:
          return `3,${model.days}`
        // 按周
        case 4:
          return `4,${model.weeks.join(',')}`
        // 指定时间
        case 5:
          return `5,${model.specify}`
      }
    },
    // cron 格式反向解析
    onCronToModel(model) {
      const parts = (model.timeScheduled || '').split(',')
      switch (parts[0]) {
        // 按分钟
        case '1':
          model.execution = 1;
          model.minutes = Number(parts[1])
          break;
        case '2':
          model.execution = 2;
          model.hours = Number(parts[1])
          break;
        case '3':
          model.execution = 3;
          model.days = Number(parts[1])
          break;
        case '4':
          model.execution = 4;
          model.weeks = parts.slice(1).map(week => week)
          break;
        case '5':
          model.execution = 5;
          model.specify = Number(parts[1])
          break;
        default:
          model.execution = 1;
          model.minutes = 5
      }
      console.warn({ model })

      return model
    },
    // 打开任务介绍
    openReportUrl(row) {
      if (row.reportUrl) {
        this.isUniwimPc
          ? window.open(row.reportUrl, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
          : window.open(row.reportUrl)
      }
    },
    // cron 格式化显示
    formatCronToText(model) {
      const parts = (model.timeScheduled || '').split(',')
      switch (parts[0]) {
        // 按分钟
        // case '1':
        //   return `按分钟 间隔${parts[1]}分钟`;
        // case '2':
        //   return `按小时 间隔${parts[1]}小时`;
        // case '3':
        //   return `按天 间隔${parts[1]}天`;
        // case '4':
        //   let weeks = parts.slice(1).sort((a, b) => a - b).map(week => this.weeks[week - 1]).join(',');
        //   return `按周 ${weeks}`;
        // case '5':
        //   return `指定日期 ${moment(Number(parts[1])).format('YYYY-MM-DD HH:mm:ss')}`;
        case '1':
          return `间隔${parts[1]}分钟`;
        case '2':
          return `间隔${parts[1]}小时`;
        case '3':
          return `间隔${parts[1]}天`;
        case '4':
          let weeks = parts.slice(1).sort((a, b) => a - b).map(week => this.weeks[week - 1]).join(',');
          return `${weeks}`;
        case '5':
          return `${utils.formatTime(Number(parts[1]))}`;
      }
      return model.timeScheduled
    },
    async formSet(model) {
      const params = {
        countdown_hours: 0,
        countdown_minutes: 0,
        countdown_seconds: 0,
        days: 1,
        hours: 1,
        minutes: 5,
        weeks: [],
        specify: null,
        execution: 1,
        reportUser: '',
        reportUrl: '',
        reportUserName: '',
      }
      let data = JSON.parse(JSON.stringify(model))
      // 设置默认值
      Object.keys(params).forEach((key) => {
        if (!data[key]) {
          data[key] = params[key]
        }
      })
      // 新增数据处理
      if (this.isNew) {
        // 设置模式为通用
        data.missionType = 'normal'

        // 设置当前默认开始时间
        data.missionStartTime = moment(moment().add(10, 'm').format("YYYY-MM-DD HH:mm:00")).valueOf()

        // 设置默认通知人为自己
        data.reportUser = this.currentUser?.id || ''
        data.reportUserName = this.currentUser?.name || ''

        // todo 后续删除，不需要前端传
        data.appKey = 'app-0hXu1dnesxZwflRouhJdHnJr'
      }
      // 编辑数据处理
      else {
        let res = await saasApi.AIAgentMissionDetail({ id: this.currentRow.id })
        if (res) {
          data = { ...data, ...res }
        }
        if (data.missionEndTime === 0) {
          data.missionEndTime = null
        }

        // if (data.missionNodes?.length) {
        //   data.missionNodes.forEach(node => {
        //     try {
        //       node.taskParam = JSON.parse(node.taskParam)
        //     } catch (e) {
        //       node.taskParam = {}
        //     }
        //   })
        // }
      }
      // 返回解析后的cron表达式
      return this.onCronToModel(data)
    },
    // 运行次数
    // onRunCount(row){
    //   const urlOrigin = location.origin + location.pathname + `#/record?id=${row.id}&missionName=${row.missionName}&showBack=1`
    //   location.replace(urlOrigin)
    // },
    // 查询最后一次执行记录
    onRunLastView(row, isExc, execution_id) {
      // 在任何需要检查WebSocket连接的地方调用
      window.checkWebSocketConnection();
      let urlOrigin = location.origin + location.pathname + `#/taskMonitor?missionId=${row.id}&currentPage=${this.pagination.currentPage}&pageSize=${this.pagination.pageSize}`
      if (isExc) urlOrigin += `&isExc=true`
      if (execution_id) urlOrigin += `&historyId=${execution_id}`
      if (this.headers?.Authorization) urlOrigin += `&uniwater_utoken=${this.headers.Authorization}`
      this.isUniwimPc
        ? window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
        : window.open(urlOrigin)
    },
    // 任务运行
    onRunTask(row) {
      if (!!this.executeId) return
      this.executeId = row.id
      saasApi.AIAgentMissionExecute(row).then(res => {
        if (res.success) {
          ElMessage({
            message: '发起手动执行成功，任务正在执行中...',
            type: 'success',
            showClose: true
          })
          this.createTaskExecuteTimer(row)
          setTimeout(() => {
            this.onRunLastView(row, true, res.execution_id)
          }, 1000)
        } else {
          this.executeId = null
          ElMessage({
            message: '手动执行失败',
            type: 'error',
            showClose: true
          })
        }
      }).catch(() => {
        this.executeId = null
        ElMessage({
          message: '手动执行失败',
          type: 'error',
          showClose: true
        })
      }).finally(() => {

      })
    },
    // 立即执行创建定时函数
    createTaskExecuteTimer(row) {
      if (this.executeId) {
        if (this.taskTimers[this.executeId]) {
          clearInterval(this.taskTimers[this.executeId])
        }
        this.taskExecuteDetail(row)
        this.taskTimers[this.executeId] = setInterval(() => {
          console.log('定时调用detail接口')
          this.taskExecuteDetail(row)
        }, 5000)
      }
    },
    // 手动执行-查询任务详情
    taskExecuteDetail(row) {
      saasApi.AIAgentMissionDetail({ id: row.id })
        .then(res => {
          if (res) {
            const oldRow = this.tableData.find(it => it.id === row.id)
            // 0-待执行1-执行中2-执行完毕3-已结束
            if (oldRow?.state !== res.state) this.tableQuery(true)
            // 运行结束后清除定时函数
            if (res.state !== 1) this.clearTaskExecuteTimer()
          }
        }).catch(() => {

        }).finally(() => {

        })
    },
    // 清理立即执行定时函数
    clearTaskExecuteTimer(type) {
      if (type === 'all') {
        this.executeId = null
        Object.keys(this.taskTimers).forEach(key => {
          if (this.taskTimers[key]) {
            clearInterval(this.taskTimers[key])
            this.taskTimers[key] = null
            delete this.taskTimers[key]
          }
        })
      } else {
        clearInterval(this.taskTimers[this.executeId])
        this.taskTimers[this.executeId] = null
        delete this.taskTimers[this.executeId]
        this.executeId = null
      }
    },
    // 任务编排
    onRunArrange(row) {
      if (this.executeId === row.id) return
      this.loading = true
      const urlToken = utils.GetAuthorization();
      // if (this.isUniwimPc) {
      //   const urlOrigin = location.origin + location.pathname + `#/designer?id=${row.id}&uniwater_utoken=${urlToken}`
      //   window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
      //   this.tableQuery(true)
      // } else {
        this.$router.push({
          path: '/designer',
          query: {
            id: row.id,
            uniwim: this.$route.query.uniwim,
            uniwater_utoken: urlToken
          }
        })
      // }
      // // 顺序执行模式，进入编辑页面
      // if (row.missionType === 'normal') {
      //   this.$refs.crud.Update()
      //   return
      // }
      // const token = this.$utils.GetAuthorization();
      // // todo 后续调整编排地址
      // const url = `http://10.80.20.22/app/2c7955d4-8380-474f-966d-e9d44e128552/workflow?hideNavBar=1&hideAppDetail=1&uniwater_utoken=${token}`
      // this.isUniwimPc
      //   ? window.open(url, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
      //   : window.open(url)
    },
    onTaskRecord(row) {
      this.maskCurrentRow = row
      this.maskTableTitle = this.maskCurrentRow?.missionName || ''
      this.drawerVisible = true
      this.maskPagination = {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
      this.maskTableQuery()
    },
    async onRowActiveChange(row) {
      // this.loading = true
      row.active = row.active === 0 ? 1 : 0
      const params = {
        ...row,
        missionStartTime: moment(row.missionStartTime).valueOf(),
        missionEndTime: row.missionEndTime ? moment(row.missionEndTime).valueOf() : null,
      }
      if (row.active) {
        let res = await saasApi.AIAgentMissionDetail({ id: row.id })
        params.configContent = res?.configContent || ''
      } else {
        delete params.configContent
      }
      saasApi.AIAgentMissionUpdateActive(params).then(res => {
        if (res?.Code === 0) {
          if (this.executeId === row.id && !row.active) this.clearTaskExecuteTimer()
          ElMessage({
            message: row.active === 0 ? '任务已关闭' : '任务已开启',
            type: 'success',
            showClose: true
          })
          this.tableQuery()
        } else {
          ElMessage({
            message: row.active === 0 ? '任务关闭失败' : '任务开启失败',
            type: 'error',
            showClose: true
          })
          row.active = row.active === 0 ? 1 : 0
        }
      }).catch(() => {
        ElMessage({
          message: row.active === 0 ? '任务关闭失败' : '任务开启失败',
          type: 'error',
          showClose: true
        })
        row.active = row.active === 0 ? 1 : 0
      }).finally(() => {
        // this.loading = false
      })
    },
    // 表格新增保存方法
    save() {
      console.log('this.form', this.form);
      if (!this.form.missionName) {
        ElMessage({
          message: '请输入任务名称!',
          type: 'error',
          showClose: true
        })
        return
      } else if (this.form.execution === 1 && !this.form.minutes) {
        ElMessage({
          message: '请输入分钟数!',
          type: 'error',
          showClose: true
        })
        return
      } else if (this.form.execution === 2 && !this.form.hours) {
        ElMessage({
          message: '请输入小时数!',
          type: 'error',
          showClose: true
        })
        return
      } else if (this.form.execution === 3 && !this.form.days) {
        ElMessage({
          message: '请输入天数!',
          type: 'error',
          showClose: true
        })
        return
      } else if (this.form.execution === 4 && !(this.form.weeks.length > 0)) {
        ElMessage({
          message: '请选择星期!',
          type: 'error',
          showClose: true
        })
        return
      } else if ([1,2,3,4].includes(this.form.execution) && !this.form.missionStartTime) {
         ElMessage({
          message: '请选择开始时间!',
          type: 'error',
          showClose: true
        })
        return
      } else if (this.form.execution === 5 && !this.form.specify) {
        ElMessage({
          message: '请选择任务时间!',
          type: 'error',
          showClose: true
        })
        return
      }
      // 处理周期数据
      if (this.form.specify) {
        this.form.specify = moment(this.form.specify).valueOf()
      }
      this.form.timeScheduled = this.onModelToCron(this.form)
      this.dialogFormVisible = false
      const params = {
        ...this.form,
        missionStartTime: moment(this.form.missionStartTime).valueOf(),
        missionEndTime: this.form.missionEndTime ? moment(this.form.missionEndTime).valueOf() : null,
      }
      if (this.isNew) {
        this.tableInsert(params)
      } else {
        this.tableUpdate(params)
      }
    },

    handleReportUserChange(val) {
      this.form.reportUserName = val;
    },

    specifyTimeOptions(time) {
      return time.getTime() <= this.currentTime;
    },
    specifyDisabledHours() {
      const now = moment();
      const selectedDate = moment(this.form.specify);
      if (selectedDate.isSame(now, 'day')) {
        return Array.from({ length: now.hour() }, (_, i) => i);
      }
      return [];
    },
    specifyDisabledMinutes(selectedHour) {
      const now = moment();
      const selectedDate = moment(this.form.specify);
      if (selectedDate.isSame(now, 'day') && selectedHour === now.hour()) {
        return Array.from({ length: now.minute() }, (_, i) => i);
      }
      return [];
    },
    startTimeOptions(time) {
      if (this.form.missionEndTime) {
        return time.getTime() > this.form.missionEndTime || time.getTime() <= this.currentTime;
      }
      return time.getTime() <= this.currentTime;
    },
    endTimeOptions(time) {
      if (this.form.missionStartTime) {
        return time.getTime() <= this.form.missionStartTime || time.getTime() <= this.currentTime;
      }
      return time.getTime() <= this.currentTime;
    },
    startDisabledHours() {
      const now = moment();
      const selectedDate = moment(this.form.missionStartTime);
      if (selectedDate.isSame(now, 'day')) {
        return Array.from({ length: now.hour() }, (_, i) => i);
      }
      return [];
    },
    endDisabledHours() {
      const now = moment();
      const selectedDate = moment(this.form.missionEndTime);
      if (selectedDate.isSame(now, 'day')) {
        return Array.from({ length: now.hour() }, (_, i) => i);
      }
      return [];
    },
    startDisabledMinutes(selectedHour) {
      const now = moment();
      const selectedDate = moment(this.form.missionStartTime);
      if (selectedDate.isSame(now, 'day') && selectedHour === now.hour()) {
        return Array.from({ length: now.minute() }, (_, i) => i);
      }
      return [];
    },
    endDisabledMinutes(selectedHour) {
      const now = moment();
      const selectedDate = moment(this.form.missionEndTime);
      if (selectedDate.isSame(now, 'day') && selectedHour === now.hour()) {
        return Array.from({ length: now.minute() }, (_, i) => i);
      }
      return [];
    },

    onStartTimeChange(time) {
      if (this.form.missionEndTime && time.getTime() > this.form.missionEndTime) {
        this.form.missionStartTime = this.form.missionEndTime
      }
    },

    // 编排详设原型
    openDetail(type) {
      const urlDict = {
        yuanxing: 'http://192.168.100.205:25427/Products/金聪_WimTask/',
        xiangshe: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2MyYjhiYWRjOWVjMDRmMGViNWQxNDljODQ1ZTRmYzg4OzA7MQ==&showAi=true&hideNativeTitle=true',
        guihua: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2JjMWE0NWNmMGI5YTQ2ZjU4YjY5ZTJlNTY4YjJmNjgyOzE7Mg==&showAi=true&hideNativeTitle=true'
      }
      const urlToken = utils.GetAuthorization();
      let urlOrigin = `${urlDict[type]}`
      if (type !== 'yuanxing') urlOrigin += `&uniwater_utoken=${urlToken}`
      if (this.isUniwimPc) {
        window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
      } else {
        window.open(urlOrigin)
      }
    }
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    this.clearTaskExecuteTimer('all')
  }
})
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-x: auto;
  padding: 16px;
  box-sizing: border-box;
  background: #f7f7f9 !important;
  .table-box {
    width: 100%;
    min-width: 1200px;
    height: 100%;
    background: white !important;
    display: flex;
    flex-flow: row nowrap;
    flex-direction: column;
    .table-header {
      padding: 8px 16px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      .table-header-title {
        display: flex;
        align-items: center;
        .table-header-icon {
          font-size: 18px;
          width: 24px;
          height: 24px;
          background: #ff884a;
          color: #fff;
          display: -webkit-inline-box;
          display: -ms-inline-flexbox;
          display: inline-flex;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack: center;
          -ms-flex-pack: center;
          justify-content: center;
          border-radius: 2px;
          margin-right: 8px;
        }
        .table-header-icon-right {
          vertical-align: bottom;
        }
        ::v-deep(.el-dropdown) {
          font-size: 16px;
          cursor: pointer;
        }
      }
      .table-header-tools {
        display: flex;
        align-items: center;
        .table-header-tools-item {
          padding: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          // color: #222222;
          font-weight: 400;
          // cursor: pointer;
          &+.table-header-tools-item {
            margin-left: 8px;
          }
          .action-iconfont{
            font-size: 12px;
          }
          span {
            margin-left: 6px;
            line-height: 17px;
          }
          // &:hover {
          //   color: rgba(0, 84, 210, 0.8);
          // }
          // &:active {
          //   color: #0044A9;
          // }
          // &.disabled {
          //   color: #BCBFC3;
          //   cursor: not-allowed;
          // }
        }
        .header-tools-drop-text {
          font-size: 12px;
        }
      }
    }
    .table-condition {
      padding: 16px;
      box-sizing: border-box;
      // border-top: solid 1px #e8ecf0;
      display: flex;
      justify-content: space-between;
      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }
      .tab-list {
        .tab-list-item {
          width: 80px;
          height: 32px;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          border: 1px solid #E6E7E9;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &.left {
            border-radius: 4px 0 0 4px;
          }
          &.right {
            border-radius: 0 4px 4px 0;
          }
          &.active {
            color: #FFFFFF;
            background: #0054D9;
            border-color: #0054D9;
          }
        }
      }
    }
    .table-content {
      height: calc(100% - 112px);
      padding: 0 16px;
      box-sizing: border-box;
      .table-content-pagination {
        height: 48px;
        padding: 0 16px;
        display: flex;
        justify-content: right;
        align-items: center;
      }
      ::v-deep(.el-scrollbar__view) {
        height: 100%;
      }
    }
    .card-content {
      height: calc(100% - 112px);
      padding: 0 16px;
      box-sizing: border-box;
      .card-item-box {
        height: calc(100% - 48px);
        overflow-y: auto;
        // margin-bottom: 16px;
        // display: block;
        .card-item {
          width: calc(25% - 16px);
          // min-width: 445px;
          height: 270px;
          position: relative;
          display: inline-block;
          margin-right: 16px;
          padding: 16px;
          box-sizing: border-box;
          border: 1px solid #E6E7E9;
          box-shadow: 0 2px 10px 0 #00000014;
          border-radius: 4px;
          margin-bottom: 16px;
          cursor: pointer;
          &:hover {
            border-color: #3B94E6;
          }
          &.active {
            border-color: #3B94E6;
          }
          &.running {
            &::after {
              content: '';
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 6px;
              // background-image: linear-gradient(270deg, #0054D9 0%, #8FDDE1 100%);
              background: #3B94E6;
              border-radius: 3px 3px 0 0;
            }
            .card-item-title-box {
              .card-item-title-bg {
                background: rgba(59, 148, 230, 0.2);
              }
            }
            &:hover {
              border-color: #3B94E6;
            }
            &.active {
              border-color: #3B94E6;
            }
          }
          &.wait {
            &::after {
              content: '';
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 6px;
              background: #FFA125;
              border-radius: 3px 3px 0 0;
            }
            .card-item-title-box {
              .card-item-title-bg {
                background: rgba(255, 161, 37, 0.2);
              }
            }
            &:hover {
              border-color: #FFA125;
            }
            &.active {
              border-color: #FFA125;
            }
          }
          &.end {
            &::after {
              content: '';
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 6px;
              background: #C1C5CE;
              border-radius: 3px 3px 0 0;
            }
            .card-item-title-box {
              .card-item-title-bg {
                background: rgba(145, 147, 152, 0.2);
              }
            }
            &:hover {
              border-color: #C1C5CE;
            }
            &.active {
              border-color: #C1C5CE;
            }
          }
          .card-item-title-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            .card-item-title-bg {
              width: 100px;
              height: 10px;
              border-radius: 100px 0 0 0;
              position: absolute;
              top: 12px;
            }
            .card-item-title {
              height: 20px;
              line-height: 20px;
              font-weight: bold;
              font-size: 14px;
              color: rgba(34, 34, 34, 0.9);
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .card-item-info-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 16px;
            .card-item-info {
              display: flex;
              .card-item-info-item {
                font-weight: 400;
                font-size: 12px;
                color: #5C5F66;
                letter-spacing: 0;
                margin-right: 20px;
              }
            }
            .card-item-tag-time-text {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-left: 10px;
            }
          }
          .card-item-line {
            width: 100%;
            height: 0.5px;
            background: #EEEEEE;
            border-radius: 4px;
            margin: 16px 0;
          }
          .card-item-two-text {
            display: flex;
            flex-wrap: wrap;
            padding: 8px 16px;
            background: #FAFAFA;
            margin: 16px 0;
            .card-item-two-text-item {
              width: calc(50% - 8px);
              display: inline-block;
              margin-bottom: 16px;
              margin-right: 16px;
              .card-item-text1 {
                font-weight: 400;
                font-size: 12px;
                color: #BCBFC3;
                margin-bottom: 10px;
              }
              .card-item-text2 {
                height: 18px;
                line-height: 18px;
                font-weight: 400;
                font-size: 12px;
                color: #222222;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
            .card-item-two-text-item:nth-child(2n) {
              margin-right: 0;
            }
            .card-item-two-text-item:nth-child(n+2) {
              margin-bottom: 0;
            }
          }
          .card-item-buttons {
            .card-item-button {
              height: 32px;
              display: inline-flex;
              align-content: center;
              justify-content: center;
              padding: 8px 16px;
              box-sizing: border-box;
              background: #FFFFFF;
              border: 1px solid #E6E7E9;
              border-radius: 4px;
              cursor: pointer;
              margin-right: 16px;
              font-size: 12px;
              &.disabled {
                color: #a8abb2;
                border-color: 1px solid #e4e7ed;
                cursor: not-allowed;
              }
              &:not(.disabled):hover {
                color: #0054D2;
                background: rgba(0, 84, 217, 0.08);
                border: 1px solid #0054D2;
              }
              &:not(.disabled).primary {
                color: #0054D2;
                border: 1px solid #0054D2;
              }
            }
          }
        }
        // .card-item:nth-child(4n) {
        //   margin-right: 0;
        // }
        .empty-box {
          display: flex;
          justify-content: center;
          :deep(.el-empty) {
            .el-empty__description {
              height: 60px;
              line-height: 60px;
            }
          }
        }
      }
      .table-content-pagination {
        height: 48px;
        padding: 0;
        display: flex;
        justify-content: right;
        align-items: center;
      }
    }

    /* card自适应宽 */
    /* 3 列 */
    @media (max-width: calc(3 * 445px + 2 * 16px)) {
      .card-content .card-item-box .card-item {
        width: calc(33% - 13px);
      }
    }
    @media (min-width: calc(3 * 445px + 2 * 16px)) {
      .card-content .card-item-box .card-item {
        width: calc(33% - 13px);
      }
    }
    /* 4 列 */
    @media (min-width: calc(4 * 445px + 3 * 16px)) {
      .card-content .card-item-box .card-item {
        width: calc(25% - 16px);
      }
    }
  }
}
:deep(.mask-layer) {
  inset: auto !important;
  width: 100%;
  height: 50%;
  left: 0 !important;
  bottom: 0 !important;
  .el-drawer__header {
    background: #FAFAFA;
    padding-top: 8px;
    padding-bottom: 8px;
    margin-bottom: 0;
    border-bottom: 1px solid #E4E7ED;
    .two-text {
      font-size: 14px;
      font-weight: 500;
      color: #999999;
      margin-left: 8px;
    }
  }
  .el-drawer__body {
    padding: 16px;
    .mask-table-box {
      height: 100%;
      .table-content-pagination {
        height: 48px;
        // padding: 0 16px;
        display: flex;
        justify-content: right;
        align-items: center;
      }
      ::v-deep(.el-scrollbar__view) {
        height: 100%;
      }
    }
  }
}
.mission-name {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    // flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .report-describe {
    width: 24px;
    height: 24px;
    color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-left: 8px;
    cursor: pointer;
    &:hover {
      color: #0054D2;
      border-color: rgb(178.5, 203.7, 241.5);
      background-color: rgb(229.5, 237.9, 250.5);
    }
    i {
      font-size: 12px;
    }
  }
}
.el-button.task-button{
  font-size: 12px;
  padding: 5px 7px;
  &~.task-button{
    margin-left: 8px;
  }
  &:not(:disabled).primary {
    color: #0054D2;
    border: 1px solid #0054D2;
  }
}
.flex-inline-form{
  padding-left: 10px;
  .el-form-item__content{
    display: flex;
    align-items: center;
    span{
      flex-shrink: 0;
      &~.el-input,
      &~.el-input-number{
        margin-left: 12px;
        max-width: 110px;
        .el-input{
            margin-left: 0;
        }
        &.no-margin{
            margin-left: 0;
        }
        &~span{
            margin-left: 12px;
        }
      }
      &~.el-date-editor{
        max-width: 180px;
      }
    }
  }
}
.copyright{
  margin-left: 24px;
}
.header-filter {
  display: inline-block;
  padding: 0 1px;
  color: #999;
  cursor: pointer;
  &:hover {
    background: #e2e2e2;
  }
}
</style>
