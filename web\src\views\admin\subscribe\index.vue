<!--  -->
<template>
    <div class="subscribe-page">
        <div class="header">
            <div class="title-section">
                授权管理
                <div class="header-tools">
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
                        <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                        刷新
                    </el-button>
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                        新增
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onEdit(null)">
                        <i class="action-iconfont icon-bianji"></i>
                        编辑
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onDelete(null)">
                        <i class="action-iconfont icon-huishouzhan<PERSON>chu"></i>
                        删除
                    </el-button>
                </div>
            </div>
            <div class="condition-section">
                <el-form :inline="true" :model="params">
                    <el-form-item label="版本类型">
                        <el-select v-model="params.targetType" placeholder="" style="width: 160px" @change="changeType" clearable>
                            <el-option label="全部类型" value="all" />
                            <el-option v-for="it in types" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.authStatus" placeholder="请选择状态" @change="changeAuthStatus" style="width: 160px" clearable>
                            <el-option v-for="it in status" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="params.targetName" placeholder="用户/企业名称" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="table-section">
            <div class="table-content">
                <!-- stripe -->
                <el-table class="service-table" ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)"
                    @rowClick="handleRowClick">
                    <el-table-column type="index" label="序号"
                        :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                    <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'status'" #default="{ row }">
                            <el-tag type="danger" round v-if="row.authStatus === -1">
                                已过期
                            </el-tag>
                            <el-tag type="info" round v-if="row.authStatus === 0">
                                未生效
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.authStatus === 1">
                                生效中
                            </el-tag>
                        </template>
                        <template v-if="it.scoped == 'type'" #default="{ row }">
                            <el-tag type="info" class="personal-tag"  v-if="row.targetType === 'user'" round>
                                个人版
                            </el-tag>
                            <el-tag type="success" class="enterprise-tag" round v-else-if="row.targetType === 'tenant'">
                                企业版
                            </el-tag>
                        </template>
                        <template v-if="it.scoped == 'startTime'||it.scoped == 'endTime'" #default="{ row }">
                            {{ moment(row[it.scoped]).format('YYYY-MM-DD') }}
                        </template>
                       <template v-if="it.scoped == 'targetId'" #default="{ row }">
                            {{ row.targetType==='user'?row.targetId:'' }}
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <!-- <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link> -->
                            <el-link type="primary" class="task-link" @click="handleAuthorize(row)">授权</el-link>
                            <el-link type="primary" class="task-link" @click="handleAuthorize(row,true)" v-if="row.sceneNum||row.mcpNum||row.templateNum||row.commandNum">授权明细</el-link>
                            <!-- <el-link type="danger" class="task-link" @click="onDelete(row)" :disabled="row.status === 1">删除</el-link> -->
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 50vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                        :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total" @change="tableQuery(false)" />
                </div>
            </div>
        </div>
        <!-- 新增授权弹窗 -->
        <el-dialog class="servicePackage-dialog" v-model="dialogFormVisible" :title="isNew?'新增':'编辑'" width="500" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
            <el-form class="subscribe-form subscribe-template-form" :model="form" ref="ruleFormRef" label-width="110px" :rules="rules">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item prop="targetType" label="版本类型" required :label-line="true">
                            <el-select v-model="form.targetType" placeholder="请选择授权分类" :disabled="!isNew" @change="changeTargetType">
                                <el-option v-for="it in types" :key="it.Value" :label="it.Name" :value="it.Value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="targetId" label="手机号" required :label-line="true" v-show="!form.targetType||form.targetType==='user'">
                            <!-- <el-input v-model.trim="form.name" :maxlength="20" type="text" clearable /> -->
                             <el-autocomplete
                                :disabled="!isNew"
                                v-model="form.targetId"
                                :fetch-suggestions="querySearchAsync"
                                @select="handleSelect"
                                placeholder="手机号"
                            />
                        </el-form-item>
                        <el-form-item prop="targetId" label="租户" required :label-line="true" v-show="form.targetType==='tenant'">
                            <el-select v-model="form.targetId" @change="changeTargetId" :disabled="!isNew" placeholder="请选择租户" filterable>
                                <el-option v-for="it in tenantList" :key="it.Value" :label="it.Name" :value="it.Value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="startTime" label="授权开始时间" required :label-line="true">
                            <el-date-picker
                                v-model="form.startTime"
                                type="date"
                                placeholder="授权开始时间"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="endTime" label="授权截止时间" required :label-line="true">
                            <el-date-picker
                                v-model="form.endTime"
                                type="date"
                                placeholder="授权截止时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="save()">
                        确定
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!--授权弹框-->
        <el-dialog class="authorize-dialog" :appaned-to-body="false" v-model="dialogAuthorizeVisible" :title="dialogAuthorizeIsDetail?'授权明细':'授权'" width="1100" style="border-radius: 4px;" @close="authorizeDialogClosed" :destroy-on-close="true" top="10vh">
            <div class="authorize-box">
                <div class="authorize-tab">
                    <el-tabs v-model="currentAuthTab" class="demo-tabs" @tab-click="handleAuthTabClick">
                        <el-tab-pane label="服务包授权" name="service"></el-tab-pane>
                        <el-tab-pane label="指令授权" name="command"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="authorize-table" v-show="currentAuthTab==='service'">
                    <div class="authorize-table-conditions">
                        <el-form :inline="true" :model="servicePackageParams">
                            <el-form-item label="类型">
                                <el-select v-model="servicePackageParams.type" placeholder="" @change="servicePackageTableQuery" style="width: 160px" clearable>
                                    <el-option label="全部类型" value="all" />
                                    <el-option v-for="it in servicePackageTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="">
                                <el-select v-model="servicePackageParams.classify" @change="servicePackageTableQuery" placeholder="请选择分类" style="width: 160px" clearable>
                                    <el-option label="全部分类" value="all" />
                                    <el-option v-for="it in classify" :key="it.Value" :label="it.Name" :value="it.Value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="">
                                <el-select v-model="servicePackageParams.formatType" @change="servicePackageTableQuery" placeholder="请选择模板分类" style="width: 160px" clearable>
                                    <el-option label="全部分类" value="all" />
                                    <el-option v-for="it in servicePackageTemplateTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="">
                                <el-input v-model="servicePackageParams.name" placeholder="场景/MCP/模板名称" clearable style="width: 160px" @keyup.enter="servicePackageTableQuery" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="servicePackageTableQuery">
                                    <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                                    <span>查询</span>
                                </el-button>
                            </el-form-item>
                        </el-form>
                        <el-button class="header-tools-item" type="circle" :disabled="!selection[currentAuthTab]||selection[currentAuthTab].length===0" v-if="!dialogAuthorizeIsDetail" size="mini" @click="onPatchAuthorize()">
                            批量授权
                        </el-button>
                        <el-button class="header-tools-item" type="circle" :disabled="!selection[currentAuthTab]||selection[currentAuthTab].length===0" v-else size="mini" @click="onPatchAuthorize('cancel')">
                            取消授权
                        </el-button>
                    </div>
                    <div class="authorize-table-content">
                        <el-table selectable @selection-change="onSelectionChange" class="service-table" ref="tableRef" :data="servicePackageData" border :show-overflow-tooltip="true"
                            :highlight-current-row="true" style="width: 100%;height: 350px"
                            @rowClick="handleAuthorizeRowClick">
                            <el-table-column type="selection" width="55" :selectable="selectable"/>
                            <el-table-column type="index" label="序号"
                                :index="1 + servicePackagePagination.pageSize * (servicePackagePagination.currentPage - 1)" align="center" width="60" />
                                <el-table-column v-for="it in servicePackageColumns.filter(col=>!col.hide)" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                                    <template v-if="it.scoped == 'image'" #default="{ row }">
                                        <img v-if="row.image" class="table-icon" :src="utils.require(row.image)" alt="">
                                    </template>
                                    <template v-else-if="it.scoped == 'iconPath'" #default="{ row }">
                                        <img v-if="row.iconPath" class="table-icon" style="width:24px;height:24px;" :src="utils.require(row.iconPath)" alt="">
                                    </template>
                                    <template v-else-if="it.scoped == 'categoryMain'" #default="{ row }">
                                        {{ servicePackageTypes.find(it => it.Value === row.categoryMain)?.Name || '' }}
                                    </template>
                                    <template v-else-if="it.scoped == 'categoryIds'" #default="{ row }">
                                        {{ formatCategoryIds(row.categoryIds, '') }}
                                    </template>
                                    <template v-else-if="it.scoped == 'formatType'" #default="{ row }">
                                        {{ row.categoryMain==='template'?(row.formatType==='word'?'Word':(row.formatType==='excel'?'Excel':'')):'' }}
                                    </template>
                                    <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                                        <el-link type="primary" class="task-link" :disabled="row.status===0"  v-if="!dialogAuthorizeIsDetail"  @click="onAuthorize('service',row)">授权</el-link>
                                        <el-link type="primary" class="task-link" :disabled="row.status===0" v-else @click="onAuthorize('service',row,'cancel')">取消授权</el-link>
                                    </template>
                                </el-table-column>
                        </el-table>
                    </div>
                    <div class="authroize-table-footer">
                        <el-pagination v-model:current-page="servicePackagePagination.currentPage" v-model:page-size="servicePackagePagination.pageSize"
                            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                            :total="servicePackagePagination.total" @change="servicePackageTableQuery(false)" />
                    </div>
                </div>
                <div class="authorize-table" v-show="currentAuthTab==='command'">
                    <div class="authorize-table-conditions">
                        <el-form :inline="true" :model="instructionParams">
                            <el-form-item label="指令分类">
                                <el-select v-model="instructionParams.commandType" @change="instructionTableQuery" placeholder="" style="width: 160px" clearable>
                                    <el-option label="全部类型" value="all" />
                                    <el-option v-for="it in instructionTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="">
                                <el-input v-model="instructionParams.name" placeholder="指令名称" clearable style="width: 160px" @keyup.enter="instructionTableQuery" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="instructionTableQuery">
                                    <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                                    <span>查询</span>
                                </el-button>
                            </el-form-item>
                        </el-form>
                        <el-button class="header-tools-item" type="circle" v-if="!dialogAuthorizeIsDetail"  size="mini" @click="onPatchAuthorize()">
                            批量授权
                        </el-button>
                        <el-button class="header-tools-item" type="circle" v-else size="mini" @click="onPatchAuthorize('cancel')">
                            取消授权
                        </el-button>
                    </div>
                    <div class="authorize-table-content">
                        <el-table selectable @selection-change="onSelectionChange" class="service-table" ref="tableRef" :data="instructionData" border :show-overflow-tooltip="true"
                            :highlight-current-row="true" style="width: 100%;height: 350px"
                            @rowClick="handleAuthorizeRowClick">
                            <el-table-column type="selection" width="55"  :selectable="selectable"/>
                            <el-table-column type="index" label="序号"
                                :index="1 + instructionPagination.pageSize * (instructionPagination.currentPage - 1)" align="center" width="60" />
                                <el-table-column v-for="it in instructionColumns.filter(col=>!col.hide)" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                                    <template v-if="it.scoped == 'iconPath'" #default="{ row }">
                                        <i class="icon action-iconfont" :class="row.iconPath"></i>
                                    </template>
                                    <template v-else-if="it.scoped == 'commandType'" #default="{ row }">
                                        {{ instructionTypes.find(it => it.Value === row.commandType)?.Name || '' }}
                                    </template>
                                    <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                                        <el-link type="primary" class="task-link" :disabled="row.status===0"  v-if="!dialogAuthorizeIsDetail"  @click="onAuthorize('command',row)">授权</el-link>
                                        <el-link type="primary" class="task-link" :disabled="row.status===0" v-else @click="onAuthorize('command',row,'cancel')">取消授权</el-link>
                                    </template>
                                </el-table-column>
                        </el-table>
                    </div>
                    <div class="authroize-table-footer">
                        <el-pagination v-model:current-page="instructionPagination.currentPage" v-model:page-size="instructionPagination.pageSize"
                            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                            :total="instructionPagination.total" @change="servicePackageTableQuery(false)" />
                    </div>
                </div>
            </div>

        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import { componentCategories } from "@/utils/componentCategories"
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { colProps, ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
const route = useRoute()
const userStore = useUserStore()

// 定义 tableData 中元素的类型
interface TableDataItem {
    id: string;
    iconPath: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscriptionCount?: string;
    downloadCount?: number;
    formatType?: string;
    status: any;
}


const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
    "Authorization": Authorization.value,
    "FROM_CHANNEL": "web"
})
const currentAuthTab = ref('service')
const selection = ref({})
const currentRow = ref(null)
const authorizeCurrenRow = ref({})
const loading = ref(false)
const pagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const isNew = ref(true)
const form = ref({
    targetType: "",
    targetId: "",
    targetName: "",
    startTime:moment().format("YYYY-MM-DD HH:mm"),
    endTime:moment().add(1,'year').format("YYYY-MM-DD HH:mm")
})
const dialogFormVisible = ref(false)
const dialogAuthorizeVisible = ref(false);
const dialogAuthorizeIsDetail = ref(false);
const types = ref([
    {'Value':'user','Name':'个人版'},
    {'Value':'tenant','Name':'企业版'}
])
const status = ref([
    { Name: '全部状态', Value: 'all' },
    { Name: '生效中', Value: 1 },
    { Name: '未生效', Value: 0 },
    { Name: '已过期', Value: -1 }
])
const params = ref({
    targetType: 'all',
    authStatus: 'all',
    targetName: ""
})
const servicePackageParams = ref({
    type: 'all',
    classify:'all',
    status:'all',
    name: "",
    formatType:'all'
})

const instructionParams = ref({
    commandType: 'all',
    name: ""
})
const servicePackageTypes = ref([
    { Name: '场景', Value: 'scene' },
    { Name: 'MCP', Value: 'mcp' },
    { Name: '模板', Value: 'template' },
])
const servicePackageTemplateTypes = ref([
    { Name: 'Word', Value: 'word' },
    { Name: 'Excel', Value: 'excel' }
])
const classify = ref([])
const tenantList = ref([])
const serVicePackageTypes = ref([
    { Name: '场景', Value: 'scene' },
    { Name: 'MCP', Value: 'mcp' },
    { Name: '模板', Value: 'template' },
])
const tableData = ref<TableDataItem[]>([])
const tableColumns = ref([
    { data: 'targetType', title: '版本类型', scoped: 'type', width: 120, orderable: true, filterable: true },
    { data: 'targetName', title: '用户/企业', minWidth: 200, orderable: true, filterable: true },
    { data: 'targetId', title: '手机号', minWidth: 160,scoped:"targetId", orderable: true, filterable: true },
    { data: 'startTime', title: '授权开始日期',scoped:"startTime",minWidth: 160, orderable: true, filterable: true },
    { data: 'endTime', title: '授权截止日期',scoped:"endTime", minWidth: 160, orderable: true, filterable: true },
    { data: 'sceneNum', title: '场景数', minWidth: 80, orderable: true, filterable: true },
    { data: 'mcpNum', title: 'MCP数', minWidth: 80, orderable: true, filterable: true },
    { data: 'templateNum', title: '模板数', minWidth: 80, orderable: true, filterable: true },
    { data: 'commandNum', title: '指令数', minWidth: 80, orderable: true, filterable: true },
    { data: 'updater', title: '最后操作人', minWidth: 120, orderable: true, filterable: true },
    { data: 'updated', title: '最后编辑时间', minWidth: 180, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 120, fixed: 'right' },
])

const authorizeCurrentRow = ref({})
const servicePackageData = ref<TableDataItem[]>([])
const servicePackageColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true,hide:false },
    { data: 'name', title: '名称', minWidth: 200, orderable: true, filterable: true,hide:false },
    { data: 'categoryMain', title: '类型', scoped: 'categoryMain', minWidth: 160, orderable: true, filterable: true,hide:false },
    { data: 'categoryIds', title: '分类', scoped: 'categoryIds', minWidth: 160, orderable: true, filterable: true,hide:false },
    { data: 'formatType', title: '模板类型', scoped: 'formatType', minWidth: 160, orderable: true, filterable: true,hide:false },
    { data: 'summary', title: '简介', minWidth: 200, orderable: true, filterable: true,hide:false },
    { data: 'updated', title: '授权时间', minWidth: 180, orderable: true, filterable: true,hide:false },
    { data: 'handle', title: '操作', scoped: 'handle', width: 120, fixed: 'right',hide:false }
])
const servicePackagePagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})


const instructionData = ref<TableDataItem[]>([])
const instructionColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true,hide:false},
    { data: 'name', title: '指令名称', minWidth: 200, orderable: true, filterable: true,hide:false },
    { data: 'commandType', title: '指令分类', scoped: 'commandType', minWidth: 160, orderable: true, filterable: true,hide:false },
    { data: 'description', title: '描述', minWidth: 200, orderable: true, filterable: true,hide:false },
    { data: 'updated', title: '授权时间', minWidth: 180, orderable: true, filterable: true,hide:false },
    { data: 'handle', title: '操作', scoped: 'handle', width: 120, fixed: 'right',hide:false }
])
const instructionPagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})

const ruleFormRef = ref<FormInstance>()
const validateTargetId = (rule, value, callback) => {
  if (!value) {
    return callback(new Error(form.value.targetType==='user'?'请输入手机号':'请选择租户'));
  }
  return callback([])
};
const validateTargetIdUnique = (rule, value, callback) => {
    debugger;
    if(!value){
        return callback(new Error(form.value.targetType==='user'?'请输入手机号':'请选择租户'));
    }
  if(currentRow&&currentRow.value&&currentRow.value.targetId == value){
    callback()
    return
  }
  // 模拟异步请求检查用户名唯一性
  saasApi.AIAgentWimtaskAuthTargetQuery({
      data:{
        targetId:value
      }
    }).then(response => {
    if (response&&response.rows&&response.rows.length) {
        if(form.value.targetType==='user'){
            callback(new Error('该手机号已存在，请重新输入'));
        }else{
            callback(new Error('该租户已存在，请重新选择'));
        }
    } else {
      callback();
    }
  }).catch(error => {
    callback();
  });
};
const rules = reactive<FormRules>({
    targetId: [
        { validator: validateTargetId, trigger: 'change' },
        { validator: validateTargetIdUnique, trigger: 'change' },
    ],
    targetType: [
        { required: true, message: '请选择版本类型', trigger: 'change' }
    ],
    startTime: [
        { required: true, message: '请选择授权开始日期', trigger: 'change' }
    ],
    endTime: [
        { required: true, message: '请选择授权结束日期', trigger: 'change' }
    ]
})




//computed
const instructionTypes = computed(()=>{
    let types:any = []
    componentCategories.forEach(category=>{
        types.push({
            Name:category.label,
            Value:category.name
        })
    })
    console.log('componentCategories',types)
    return types
})


//方法

const selectable = (row, index)=>{
    if(row.status===0){
        return false
    }
    return true
}
const changeType = (val:any) => {
    if (!val) {
        params.value.targetType = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

const changeAuthStatus = (val:any) => {
    if (!val&&val!==0) {
        params.value.authStatus = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}
const authorizeDialogClosed = ()=>{
    selection.value[currentAuthTab.value] = []
    servicePackageParams.value = {
        type: 'all',
        classify:'all',
        status:'all',
        name: "",
        formatType:'all'
    }
    currentAuthTab.value = 'service'
}
const changeTargetType = (val: any) => {
    form.value.targetId='';
    form.value.targetName='';
    ruleFormRef.value.clearValidate();
}
//服务包授权
const querySearchAsync = (queryString: string, cb: (arg: any) => void) => {
    saasApi.DmpUserByMobile({mobile:queryString}).then((res: any) => {
        if (typeof res == 'object') {
            let result = res.map(it=>{
                return {
                    value:it.mobile,
                    link:it.name
                }
            })
            cb(result)
        } else {
            cb([])
        }
    })
    .catch((err: any) => {
         cb([])
    })
    .finally(() => {
    })

}
const changeTargetId = (val: any) => {
    let obj = tenantList.value.find(it=>it.Value===val)
    if(obj)form.value.targetName = obj.Name
}
const handleSelect = (item: Record<string, any>) => {
    ;
    form.value.targetName = item.link
}
const handleAuthorizeRowClick = (row:any)=>{
    let type = currentAuthTab.value;
    authorizeCurrentRow.value[type]  = row
}
const formatCategoryIds = (data: any, text: any) => {
    (data || []).forEach((item, index) => {
        const name = classify.value.find(it => it.Value === item)?.Name || ''
        if (name) {
            if (index === 0) {
                text += `${name}`
            } else {
                text += `,${name}`
            }
        }
    })
    return text
}
const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {},
        size:Infinity,
        index:1
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            classify.value = res.rows.map(it => {
                return {
                    ...it,
                    Name: it.name,
                    Value: it.id
                }
            })
        } else {
            classify.value = []
        }
    })
    .catch((err: any) => {
        classify.value = []
    })
    .finally(() => {
    })

}
const getTenantList  = ()=>{
    const query_params: any = {}
    saasApi.DmpTenantDetailByName(query_params).then((res: any) => {
        if (typeof res == 'object') {
            tenantList.value = res.map(it => {
                return {
                    ...it,
                    Name: it.tenantName,
                    Value: it.id
                }
            })
        } else {
            tenantList.value = []
        }
    })
    .catch((err: any) => {
        tenantList.value = []
    })
    .finally(() => {
    })

}
const instructionTableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    const query_params: any = {
        conditions: [],
        data: {
            status:1,
            isFree:0
        },
        "authTargetIds": [currentRow.value.id],
        "isAuthFilter": dialogAuthorizeIsDetail.value?"1":"0",
        index: instructionPagination.value.currentPage,
        size: instructionPagination.value.pageSize,
    }
    if (instructionParams.value.commandType !== 'all') {
        query_params.data.commandType = instructionParams.value.commandType
    }
    if (instructionParams.value.name) {
        query_params.data.name = instructionParams.value.name
    }
    saasApi.AIAgentWimtaskCommandQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            instructionPagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            instructionData.value = res.rows
        } else {
            instructionData.value = []
        }
    })
    .catch((err: any) => {
        instructionData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}
const servicePackageTableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    const query_params: any = {
        conditions: [],
        data: {
            status:1,
            "authTargetIds": [currentRow.value.id],
            "isAuthFilter": dialogAuthorizeIsDetail.value?"1":"0",
        },
        order:[
            {
                Field:"sort",
                Type:1
            },
            {
                Field:"updated",
                Type:-1
            }
        ],
        index: servicePackagePagination.value.currentPage,
        size: servicePackagePagination.value.pageSize,
    }
    if (servicePackageParams.value.type !== 'all') {
        query_params.data.categoryMain = servicePackageParams.value.type
    }
    if (servicePackageParams.value.formatType !== 'all') {
        query_params.data.formatType = servicePackageParams.value.formatType
    }
    if (servicePackageParams.value.classify !== 'all') {
        query_params.data.categoryIds = servicePackageParams.value.classify?[servicePackageParams.value.classify]:[]
    }
    if (servicePackageParams.value.name) {
        query_params.data.name = servicePackageParams.value.name
    }
    saasApi.AIAgentTaskTemplateQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            servicePackagePagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            servicePackageData.value = res.rows
        } else {
            servicePackageData.value = []
        }
    })
    .catch((err: any) => {
        servicePackageData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}



const handleAuthTabClick = (item:any)=>{
    let type = item.paneName
    if(type=='service'){
        servicePackageTableQuery();
    }else{
        instructionTableQuery();
    }
}

const handleAuthorize = (row:any,isDetail:boolean)=>{
    currentRow.value = row;
    dialogAuthorizeVisible.value=true;
    dialogAuthorizeIsDetail.value = isDetail
    let service_updated_obj = servicePackageColumns.value.find((it:any)=>it.data=='updated');
    if(service_updated_obj)service_updated_obj.hide = !isDetail;
    let updated_obj = instructionColumns.value.find(it=>it.data=='updated');
    if(updated_obj)updated_obj.hide = !isDetail;
    if(currentAuthTab.value==='service'){
        servicePackageTableQuery();
        
    }else{
        instructionTableQuery();
        
    }
}
const getServicePackage = ()=>{
    saasApi.AIAgentTaskTemplateQuery({
        data:{
        }
    }).then((res: any) => {
        if (typeof res?.rows == 'object') {
            servicePackageData.value = res.rows
        } else {
            servicePackageData.value = []
        }
    })
    .catch((err: any) => {
        servicePackageData.value = []
    })
    .finally(() => {
    })
}
const onAuthorize = (resourceType:string,row:any,action:string)=>{
    let update_params = {
        type:resourceType,
        resources:[{resourceId:row.id,resourceType:resourceType=='command'?'command':row.categoryMain}],
        authTargetId:currentRow.value.id
    }
    if(action!=='cancel'){
        saasApi.AIAgentAddAuthResource(update_params).then((res: any) => {
            if(res?.Code==0){
                ElMessage.success("授权成功")
                if(currentAuthTab.value==='service'){
                    servicePackageTableQuery();
                }else{
                    instructionTableQuery();
                }
                tableQuery(false,true)
            }else{
                ElMessage.error("授权失败")
            }
        })
        .catch((err: any) => {
            ElMessage.error("授权失败")
        })
        .finally(() => {
        })
    }else{
        saasApi.AIAgentRemoveAuthResource(update_params).then((res: any) => {
            if(res?.Code==0){
                ElMessage.success("取消授权成功")
                if(currentAuthTab.value==='service'){
                    servicePackageTableQuery();
                }else{
                    instructionTableQuery();
                }
                tableQuery(false,true)
            }else{
                ElMessage.error("取消授权失败")
            }
        })
        .catch((err: any) => {
            ElMessage.error("取消授权失败")
        })
        .finally(() => {
        })
    }
    // dialogAuthorizeVisible.value=true;

}
const onPatchAuthorize = (action:string)=>{
    ;
    let type = currentAuthTab.value;
    let update_params = {
        type,
        authTargetId:currentRow.value.id,
        resources:selection.value[type]
    }
    if(action!=='cancel'){
        saasApi.AIAgentAddAuthResource(update_params).then((res: any) => {
            if(res?.Code==0){
                ElMessage.success("授权成功")
                if(currentAuthTab.value==='service'){
                    servicePackageTableQuery();
                }else{
                    instructionTableQuery();
                }
                tableQuery(false,true)
            }else{
                ElMessage.error("授权失败")
            }

        })
        .catch((err: any) => {
            ElMessage.error("授权失败")
        })
        .finally(() => {
        })
    }else{
        saasApi.AIAgentRemoveAuthResource(update_params).then((res: any) => {
            if(res?.Code==0){
                ElMessage.success("取消授权成功")
                if(currentAuthTab.value==='service'){
                    servicePackageTableQuery();
                }else{
                    instructionTableQuery();
                }
                tableQuery()
            }else{
                ElMessage.error("取消授权失败")
            }
        })
        .catch((err: any) => {
            ElMessage.error("取消授权失败")
        })
        .finally(() => {
        })
    }

}
const onSelectionChange = (val: any[]) => {
    ;
    let type = currentAuthTab.value
    selection.value[type] = val.map(it=>{
        return {
            resourceId:it.id,
            resourceType:type=='command'?'command':it.categoryMain
        }
    })
}

const handleRowClick = (row: any, show: boolean) => {
    currentRow.value = row
}
const onReset = () => {
    params.value.targetType = 'all'
    params.value.authStatus = 'all'
    params.value.targetName = ''

    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isNew.value = true
    form.value = {
        targetType:'user',
        targetId:"",
        targetName:"",
        startTime:moment().format("YYYY-MM-DD HH:mm"),
        endTime:moment().add(1,'year').format("YYYY-MM-DD HH:mm")
    }
    currentRow.value = null
    tableData.value = []
    tableQuery()
}

const onSubmit = () => {
    tableQuery()
}
const selectStatusChange = (val: any) => {
    if (!val && val !== 0) {
        params.value.status = 'all'
    }
    tableQuery()
}
const tableQuery = (noloading: boolean = false,notReset:boolean) => {
    if (!noloading) {
        loading.value = true
    }
    if(!notReset)currentRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"updated",
                Type:-1
            }
        ],
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    if (params.value.targetType !== 'all') {
        query_params.data.targetType = params.value.targetType
    }
    if (params.value.authStatus !== 'all') {
        query_params.data.authStatus = params.value.authStatus
    }
    if (params.value.targetName) {
        query_params.data.targetName = params.value.targetName
    }
    saasApi.AIAgentWimtaskAuthTargetQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach(row=>{
                if(row.startTime){
                    row.startTime = row.startTime*1000
                }
                if(row.endTime){
                    row.endTime = row.endTime*1000
                }
            })
            pagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            tableData.value = res.rows
        } else {
            tableData.value = []
        }
    })
    .catch((err: any) => {
        tableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}
const save = async () => {
    ;
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return

    let update_params = {
        ...form.value,
        startTime:moment(form.value.startTime).startOf('day').unix(),
        endTime:moment(form.value.endTime).endOf('day').unix()
    }
    if (isNew.value) {
        tableInsert(update_params)
    } else {
        tableUpdate(update_params)
    }
}
const tableInsert = (insert_params: any) => {
    saasApi.AIAgentWimtaskAuthTargetInsert(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                tableQuery(false,true)
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const tableUpdate = (update_params: any, noQuery?: boolean) => {
    let input_params = {
        ...currentRow.value,
        ...update_params
    }
    saasApi.AIAgentWimtaskAuthTargetUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                if (!noQuery) tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}
const formSet = async (model: any) => {
    let data = JSON.parse(JSON.stringify(model))
    return data
}
const onAdd = async () => {
    isNew.value = true
    form.value = JSON.parse(JSON.stringify(await formSet({
        targetType:'user',
        targetId:"",
        targetName:"",
        startTime:moment().format("YYYY-MM-DD HH:mm"),
        endTime:moment().add(1,'year').format("YYYY-MM-DD HH:mm")
    })))
    // tableQuery()
    dialogFormVisible.value = true
}
const onEdit = async (row: any) => {
    if (row) currentRow.value = row
    isNew.value = false
    form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
    // tableQuery()
    dialogFormVisible.value = true
}
const onDelete = async(row: any) => {
    if (row) currentRow.value = row
    await ElMessageBox.confirm(`确认删除授权？`, `确认删除`, {
        type: 'warning',
    })
    saasApi.AIAgentWimtaskAuthTargetDelete([currentRow.value.id]).then((res: any) => {
        if (res.Code === 0 || res === true) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            tableQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}
onMounted(() => {
    getTenantList();
    getClassifyData()
    tableQuery()
})
</script>
<style lang='less' scoped>
:deep(.service-table) {
    .el-table__cell {
        border-bottom: 1px solid #EEEEEE !important;
    }
}
.authorize-table-content{
    .table-icon{
        width:24px;
        height:24px;
    }
}
.subscribe-page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px;
    box-sizing: border-box;
    background: #f7f7f9 !important;
    // display: flex;
    flex-direction: column;

    .header {
        background: #fff;
        width: 100%;
        height: 112px;
        box-sizing: border-box;
        overflow: hidden;



        .condition-section {
            padding: 8px 16px;
            box-sizing: border-box;
            // border-top: solid 1px #e8ecf0;
            // display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 16px;
            }

            .tab-list {
                .tab-list-item {
                    width: 80px;
                    height: 32px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    border: 1px solid #E6E7E9;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &.left {
                        border-radius: 4px 0 0 4px;
                    }

                    &.right {
                        border-radius: 0 4px 4px 0;
                    }

                    &.active {
                        color: #FFFFFF;
                        background: #0054D9;
                        border-color: #0054D9;
                    }
                }
            }
        }
    }

    .table-section {
        // flex: 1;
        height:calc(100% - 112px);
        background: #fff;
    }

    .table-content {
        height: 100%;

        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }

        .table-icon {
            width: 24px;
            height: 24px;
        }

        .table-content-pagination {
            height: 48px;
            padding: 0 12px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        ::v-deep(.el-scrollbar__view) {
            height: 100%;
        }
    }

    .subscribe-template-form{
        :deep(.el-date-editor){
            width:100%;
        }
    }

}
.authorize-dialog{
    .authorize-box{
        .authorize-tab{}
        .authorize-table{
            .authorize-table-conditions{
                display:flex;
                justify-content: space-between;
            }
            .authroize-table-footer{
                width:100%;
                display: flex;
                justify-content: flex-end;
            }
        }
    }
}
.title-section {
    height: 64px;
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSansSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #222222;

    .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
    }
    .header-tools {
        display: flex;
        align-items: center;

        .header-tools-item {
            padding: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #222222;
            font-weight: 400;
            cursor: pointer;

            .action-iconfont {
                margin-right: 4px;
                font-size: 14px;
            }

            span {
                margin-left: 6px;
                line-height: 17px;
            }

            &:hover {
                color: rgba(0, 84, 210, 0.8);
            }

            &:active {
                color: #0044A9;
            }

            &.is-disabled {
                color: #BCBFC3;
                cursor: not-allowed;
            }
        }
    }
}
.personal-tag{
    background: #F0F5FF;
    border: 1px solid #BED2FF;
    height: 22px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #1E39C3;
    line-height: 22px;
}
.enterprise-tag{
    height: 22px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #2B9196;
    line-height: 22px;
    background: #F0FFFB;
    border: 1px solid #C0E7DF;
}
</style>
