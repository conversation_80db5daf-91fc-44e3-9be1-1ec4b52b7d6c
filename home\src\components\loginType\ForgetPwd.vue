<template>
    <div class="forget-pwd">
        <span class="forget-pwd-btn dly-forget" @click="pwdVisiable = true">忘记密码</span>
        <el-dialog
            v-model="pwdVisiable"
            :close-on-click-modal="false"
            :append-to-body="false"
            width="380px"
            title="重置密码"
            class="pwd-content forget-pwd-dialog"
            :show-close="false"
            @closed="onClosed"
        >
            <button type="button" aria-label="Close" class="el-dialog__headerbtn" @click="pwdVisiable = false">
                <el-icon><Close /></el-icon>
            </button>
            <form id="loginFm2" autocomplete="off" @keyup.enter.prevent>
                <div class="input-box">
                    <div class="send-code">
                        <input v-model.trim="model.mobile" type="text" placeholder="请输入手机号" />
                        <button type="button" class="send-btn" @click="sendCode" :disabled="disabled || isSended">{{ text }}</button>
                    </div>
                </div>
                <div class="input-box no-icon">
                    <input v-model="model.code" type="text" placeholder="请输入验证码" />
                </div>
                <div class="input-box no-icon">
                    <el-tooltip class="item" effect="dark" placement="right-end">
                        <template #content><div v-html="pwdRuleTips"></div></template>
                        <el-input v-model="model.npwd" placeholder="请输入新密码" show-password></el-input>
                    </el-tooltip>
                </div>
                <!-- 错误信息 -->
                <p class="error" style="height: 30px">{{ model.err_info }}</p>
            </form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button size="large" type="primary" @click="onConfirm">重置密码</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, getCurrentInstance, ComponentInternalInstance } from 'vue';
import JSEncrypt from 'jsencrypt';
import { checkIsPhone, timer } from '@/utils/validate'; // 假设这些工具函数已适配或无需更改
import { Close } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import systemApi from "@/api/system";
import utils from '@/utils/utils'

// --- 类型定义 ---
interface IConfigs {
    pwdRuleType?: '1' | '2' | '3' | '4' | '5' | string;
    pwdRule?: string;
    pwdMinLength?: number;
    weakPasswordSetting?: string;
}

interface IModel {
    mobile: string | null;
    code: string | null;
    npwd: string | null;
    err_info: string | null;
}

// --- Props 定义 ---
const props = withDefaults(
    defineProps<{
        configs?: IConfigs;
        cid?: string | null;
    }>(),
    {
        configs: () => ({
            pwdRuleType: '',
            pwdMinLength: 0,
            weakPasswordSetting: '',
            pwdRule: ''
        }),
        cid: null
    }
);



// --- 响应式状态 ---
const pwdVisiable = ref(false);
const publicKey = ref<string | null>(null);
const disabled = ref(true);
const isSended = ref(false);
const time = ref(60);
const text = ref('获取验证码');

const model = reactive<IModel>({
    mobile: null,
    code: null,
    npwd: null,
    err_info: null
});

// --- Computed ---
const pwdRuleTips = computed(() => {
    let str = `新密码需要符合以下规则：<br/>1.`;
    switch (props.configs.pwdRuleType) {
        case '1':
            str += '字母大写+字母小写+数字组合';
            break;
        case '2':
            str += '字母+数字+特殊字符组合';
            break;
        case '3':
            str += '字母+数字组合';
            break;
        case '4':
            if (props.configs.pwdRule) {
                str += `满足正则策略：${props.configs.pwdRule}`;
            } else {
                str += '新密码不能为空';
            }
            break;
        case '5':
            str += '字母大写+字母小写+特殊字符+数字';
            break;
        default:
            str += '新密码不能为空';
            break;
    }
    str += `<br/>2.密码长度不能小于${props.configs.pwdMinLength}`;
    if (props.configs.weakPasswordSetting) {
        str += `<br/>3.不能设置的密码名单有：${props.configs.weakPasswordSetting}`;
    }
    return str;
});

// --- Watch ---
watch(
    () => model.mobile,
    (v) => {
        disabled.value = v === null || v === '' || !checkIsPhone(v);
    },
    { immediate: true }
);

// --- 方法 ---
const onClosed = () => {
    Object.assign(model, {
        mobile: null,
        code: null,
        npwd: null,
        err_info: null
    });
    disabled.value = true;
    isSended.value = false;
    time.value = 60;
    text.value = '获取验证码';
};

const letTimer = () => {
    let times = time.value;
    timer(
        times,
        (s: number) => {
            text.value = `已发送，${s}秒`;
        },
        () => {
            const isRight = model.mobile ? checkIsPhone(model.mobile) : false;
            if (model.mobile !== '' && isRight) {
                disabled.value = false;
            }
            text.value = '重新获取';
            isSended.value = false;
        }
    );
};

const getEncryptKey = (): Promise<void> => {
    return new Promise((resolve, reject) => {
        systemApi.getEncryptKey().then((res: { publicKey?: string }) => {
            publicKey.value = res.publicKey || '';
            if (res.publicKey) {
                sessionStorage.setItem('publicKey', res.publicKey);
                resolve();
            } else {
                reject(new Error('Failed to get public key'));
            }
        });
    });
};

const handleSendCode = (): Promise<void> => {
    model.err_info = null;
    return new Promise((resolve, reject) => {
        systemApi
            .verificationCode(
                {
                    mobile: model.mobile,
                    type: 'FORGET_PWD_CODE'
                },
                { meta: { isData: false } }
            )
            .then((res: { Code: number; Message: string }) => {
                if (res.Code === 0) {
                    resolve();
                } else {
                    reject(new Error(res.Message));
                }
            })
            .catch((err: any) => {
                reject(err);
            });
    });
};

const sendCode = () => {
    ;
    if (isSended.value) return;
    disabled.value = true;

    handleSendCode()
        .then(() => {
            ElMessage({
                type: 'success',
                message: '验证码已发送，请注意查收'
            });
            isSended.value = true;
            letTimer();
        })
        .catch((err) => {
            ElMessage.error(err.message || '操作失败，请联系管理员');
            disabled.value = false;
        });
};

const onConfirm = async () => {
    model.err_info = null;
    if (!model.mobile) {
        model.err_info = '请输入手机号';
        return;
    }
    if (!checkIsPhone(model.mobile)) {
        model.err_info = '手机号有误';
        return;
    }
    if (!model.code) {
        model.err_info = '请输入手机验证码';
        return;
    }
    if (!model.npwd) {
        model.err_info = '请输入新密码';
        return;
    }

    try {
        await getEncryptKey();
        const encryptStr = new JSEncrypt();
        encryptStr.setPublicKey(publicKey.value as string);
        const encrypted = encryptStr.encrypt(model.npwd);
        if (!encrypted) {
            throw new Error('密码加密失败');
        }
        
        // 假设 HD.base64.encode 存在
        const pwd = HD.base64.encode(encrypted);
        const params = {
            mobile: model.mobile,
            code: model.code,
            npwd: pwd
        };

        const res = await dlyApi.forgetPwd(params, { meta: { isData: false } });
        
        if (!res) {
            ElMessage.error('密码重置失败，请联系管理员');
            return;
        }
        if (res.Code === 0) {
            pwdVisiable.value = false;
            ElMessage.success('密码重置成功');
        } else {
            ElMessage.error(res.message);
        }
    } catch (err: any) {
        ElMessage.error(err.message || err.Message || '操作失败，请联系管理员');
    }
};
</script>

<style scoped lang="less">
.forget-pwd {
    :deep(.forget-pwd-dialog) {
        border-radius: 6px;
        box-shadow: 0 10px 30px 0 rgba(35, 49, 103, 0.18);
        overflow: hidden;
        user-select: none;
        .el-dialog__header {
            text-align: center;
            display: flex;
            justify-content: center;
            padding: 30px 0 0;
            height: auto;
            border-bottom: 0;
            background: #fff;
            .el-dialog__title {
                padding: 0 10px;
                font-size: 20px;
                font-weight: 700;
                user-select: none;
            }
        }
        .el-dialog__headerbtn {
            right: 12px;
            top: 10px;
            height: auto; // 覆盖el-icon的默认大小
            width: auto;
        }
        .el-dialog__body {
            padding: 12px 24px 5px;
        }
        .el-dialog__footer {
            text-align: center;
            padding-bottom: 24px;
            .el-button {
                height: 38px;
                border-radius: 38px;
                width: 80%;
                &.el-button--primary {
                    background: #008ddf;
                    border-color: #008ddf;
                }
            }
        }
    }
    .send-code {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        font-size: 14px;

        input {
            flex: 1;
            width: 0 !important;
            border: 0;
            padding: 0 10px;
            background: none;
            outline: none;
            position: relative !important;
            &::placeholder {
                color: #bfbfbf;
            }
        }

        .send-btn {
            border: 0;
            height: 100%;
            flex: 0 1 auto;
            padding: 0 10px;
            background-color: #f4f6f8;
            color: #333333;
            position: relative;
            cursor: pointer;
            line-height: 44px;
            &:after {
                content: '';
                position: absolute;
                left: 0;
                top: 12px;
                height: 21px;
                width: 1px;
                background: #bfbfbf;
            }
            &:disabled {
                opacity: 0.3;
                cursor: not-allowed;
            }
        }
    }
    :deep(.pwd-content) {
        margin-top: 30px;
        .input-box {
            position: relative;
            height: 44px;
            border: 1px solid #f4f6f8;
            margin-top: 15px;
            overflow: hidden;
            background-color: #f4f6f8;
            border-radius: 4px;
            .login-bg-icon {
                background-image: url('../../assets/images/login/login-input-bg.png');
                &.nameicon {
                    background-position: 8px -34px;
                }
                &.customericon {
                    background-position: 8px 4px;
                }
                &.passwordicon {
                    background-position: 8px -73px;
                }
            }
            i:not(.el-select__caret.el-input__icon) {
                background-repeat: no-repeat;
                height: 44px;
                width: 44px;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 2;
                font-size: 22px;
                color: #97989a;
                text-align: center;
                line-height: 36px;
            }
            input,
            select {
                outline: none;
                line-height: 44px;
                height: 44px;
                border: 0;
                background: none;
                width: calc(100% - 44px);
                font-size: 14px;
                position: absolute;
                top: 0;
                right: 0;
                display: block;
                &::placeholder {
                    color: #bfbfbf;
                }
            }
            .el-input {
                height: 44px;
                line-height: 44px;
                .el-input__wrapper {
                    padding: 0;
                    box-shadow: none;
                    background: none;
                }
                input {
                    padding: 0 26px 0 10px;
                    outline: none;
                    height: 42px;
                    line-height: 42px;
                    border: 0;
                    background: none;
                    width: 100%;
                    font-size: 14px;
                    position: static;
                    display: block;
                }
                .el-input__suffix {
                    right: 10px;
                }
            }
            &.no-icon {
                .el-input,
                > input,
                select {
                    width: 100%;
                    padding: 0 10px;
                    box-sizing: border-box;
                }
                 .el-input__inner {
                    padding: 0 10px;
                 }
            }
        }
        .select-ck {
            position: absolute;
            transform: translateY(6px);
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            align-items: center;
            left: 34px;
            right: 34px;
            > .forget-pwd,
            .el-checkbox {
                display: flex;
                cursor: pointer;
                span {
                    padding-left: 4px;
                    color: #888888;
                    font-size: 12px;
                }
                .el-checkbox__inner {
                    margin-top: 3px;
                }
            }
        }
        .submit {
            cursor: pointer;
            background: rgb(0, 141, 223);
            color: #fff;
            position: relative;
            width: 75%;
            margin: 30px 12.5% 0 12.5%;
            height: 38px;
            border: none;
            outline: 0;
            border-radius: 38px;
        }
        .error {
            color: Red;
            text-align: center;
            font-size: 12px;
            line-height: 30px;
            transition: all 0.2s ease;
        }
    }
    .dly-forget {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
        cursor: pointer;
    }
}
</style>