from datetime import datetime
import uuid
from typing import Optional, Dict, Any, List


class Message:
    id: str
    time: str
    type: str

    def __init__(self, t: str):
        self.id = str(uuid.uuid4())
        self.time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.type = t

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典用于JSON序列化"""
        return {"id": self.id, "time": self.time, "type": self.type}


class Except:
    """异常信息数据类"""

    msg: str
    detail: str  # 详细异常信息

    def __init__(self, msg: str, detail: str = ""):
        self.msg = msg
        self.detail = detail

    def to_dict(self) -> Dict[str, Any]:
        return {"msg": self.msg, "detail": self.detail}


class NodeStatus(Message):
    """执行消息数据类，对应JSON结构"""

    node_id: str  # 节点id
    node_type: str  # 指令类型
    node_name: str  # 指令名称
    history_id: str  # 执行id
    task_id: str  # 任务id
    describe: str
    state: str  # 节点状态，可选值：pass/fail/progress/new
    inputs: Dict[str, Any]  # 输入变量列表
    outputs: Dict[str, Any]  # 输出变量列表
    exception: Optional[Except] = None  # 异常信息
    errors: List[Dict[str, Any]] = []

    def __init__(
        self,
        node_id: str,
        node_type: str,
        node_name: str,
        history_id: str,
        task_id: str,
        describe: str = "",
    ):
        super().__init__("exec")
        self.state = "new"
        self.node_id = node_id
        self.node_type = node_type
        self.node_name = node_name
        self.history_id = history_id
        self.task_id = task_id
        self.describe = describe
        # 初始化输入输出字典避免属性未定义
        self.inputs = {}
        self.outputs = {}

    def failed(self, msg: str, detail: str = ""):
        # 确保异常对象存在
        if not self.exception:
            self.exception = Except(msg, detail)
        self.state = "failed"

    def progress(self, inputs: Dict[str, Any], outputs: Dict[str, Any]):
        self.inputs = inputs
        self.outputs = outputs
        self.state = "progress"

    def done(self, inputs: Dict[str, Any], outputs: Dict[str, Any]):
        self.inputs = inputs
        self.outputs = outputs
        self.state = "pass"

    def flow_end(self, state: str, msg: str = "", errors=None):
        if errors is not None:
            self.errors = errors
        self.state = state
        if msg != "":
            self.exception = Except(msg=msg)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典用于JSON序列化"""
        base_dict = super().to_dict()
        base_dict.update(
            {
                "node_id": self.node_id,
                "node_type": self.node_type,
                "node_name": self.node_name,
                "history_id": self.history_id,
                "task_id": self.task_id,
                "state": self.state,
                "describe": self.describe,
                "inputs": self.inputs,
                "outputs": self.outputs,
                "exception": self.exception.to_dict() if self.exception else None,
                "errors": self.errors,
            }
        )
        return base_dict


class FlowStatus(Message):
    history_id: str  # 执行id
    task_id: str  # 任务id
    state: str
    msg: str
    exception: Optional[Except] = None  # 异常信息

    def __init__(
        self,
        history_id: str,
        task_id: str,
    ):
        super().__init__("flow")
        self.history_id = history_id
        self.task_id = task_id
        self.state = "new"

    def progress(self):
        self.state = "progress"

    def canceled(self, msg: str = ""):
        self.state = "canceled"
        self.msg = msg

    def failed(self, msg: str):
        self.state = "failed"
        if len(msg) > 0:
            self.exception = Except(msg=msg)

    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update(
            {
                "history_id": self.history_id,
                "task_id": self.task_id,
                "state": self.state,
                "msg": self.msg,
                "exception": self.exception.to_dict() if self.exception else None,
            }
        )
        return base_dict
