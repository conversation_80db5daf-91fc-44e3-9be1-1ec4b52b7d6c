<template>
    <el-dialog v-model="dlgVisible" :close-on-click-modal="false" append-to-body header-class="user-picker-dialog-header" body-class="user-picker-dialog-body" :width="'800px'" :title="title">
        <div class="setting-content">
            <div class="setting-tab">
                <el-tabs v-model="tab" tab-position="left" class="demo-tabs">
                    <el-tab-pane name="system" label="系统设置"></el-tab-pane>
                </el-tabs>
            </div>
            <div class="setting-form">
                <div class="setting-description"></div>
                <el-form :model="form" ref="formRef"
                     label-width="120px">
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item prop="output_path" label="总输出文件夹" :label-line="true">
                                <el-input
                                    v-model="form.output_path"
                                    :placeholder="'选择文件夹'"
                                >
                                    <template #append>
                                    <el-button @click="selectFolder('output_path')">
                                        <el-icon>
                                        <FolderOpened />
                                        </el-icon>
                                        选择文件夹
                                    </el-button>
                                    </template>
                                </el-input>
                                <span class="form-tip">所有输出文件的根目录</span>
                            </el-form-item>
                        </el-col>
                        <el-divider content-position="left">节点默认路径</el-divider>
                        <el-col :span="24">
                            <el-form-item prop="excel_output_path" label="创建Excel文件" :label-line="true">
                                <el-input
                                    v-model="form.excel_output_path"
                                    :placeholder="'选择文件夹'"
                                >
                                    <template #append>
                                    <el-button @click="selectFolder('excel_output_path')">
                                        <el-icon>
                                        <FolderOpened />
                                        </el-icon>
                                        选择文件夹
                                    </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="word_output_path" label="创建Word文件" :label-line="true">
                                <el-input
                                    v-model="form.word_output_path"
                                    :placeholder="'选择文件夹'"
                                >
                                    <template #append>
                                    <el-button @click="selectFolder('word_output_path')">
                                        <el-icon>
                                        <FolderOpened />
                                        </el-icon>
                                        选择文件夹
                                    </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="file_output_path" label="文件上传" :label-line="true">
                                <el-input
                                    v-model="form.file_output_path"
                                    :placeholder="'选择文件夹'"
                                >
                                    <template #append>
                                    <el-button @click="selectFolder('file_output_path')">
                                        <el-icon>
                                        <FolderOpened />
                                        </el-icon>
                                        选择文件夹
                                    </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="ticket_output_path" label="发票识别" :label-line="true">
                                <el-input
                                    v-model="form.ticket_output_path"
                                    :placeholder="'选择文件夹'"
                                >
                                    <template #append>
                                    <el-button @click="selectFolder('ticket_output_path')">
                                        <el-icon>
                                        <FolderOpened />
                                        </el-icon>
                                        选择文件夹
                                    </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <template #footer>
            <el-button size="default" @click="reset()">重置</el-button>
            <el-button size="default" @click="close()">取消</el-button>
            <el-button size="default" type="primary" @click="onConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';


interface Emits {
  (e: 'confim'): void
  (e: 'close'): void
}

// 定义响应式数据
const title = ref('系统设置');
const dlgVisible = ref(true)
const tab = ref('system')
const form = ref({
    output_path:'',
    word_output_path:'',
    excel_output_path:'',
    file_output_path:'',
    ticket_output_path:'',
})



// Emits
const emit = defineEmits<Emits>()


const close = ()=>{
    emit('close')
}
const reset = ()=>{
    form.value = {
        output_path:'',
        word_output_path:'',
        excel_output_path:'',
        file_output_path:'',
        ticket_output_path:'',
    }
}
const onConfirm = ()=>{
    emit('close')
}


// 方法
const handleInput = (value: string) => {
  emit('update:modelValue', value)
}
//上传文件夹
const selectFolder = async (type:string) => {
  if (window.electronAPI) {
    // todo 一诺桌面端选择文件夹
  } else {
    // Web环境下的文件夹选择（HTML5 webkitdirectory）
    const input = document.createElement('input')
    input.type = 'file'
    input.webkitdirectory = true
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      if (files && files.length > 0) {
        // 获取第一个文件的路径，去掉文件名部分
        const firstFile = files[0]
        const path = firstFile.webkitRelativePath
        const folderPath = path.substring(0, path.indexOf('/'))
        // handleInput(folderPath)
        form.value[type] = folderPath
      }
    }
    input.click()
  }
}
</script>

<style scoped lang="scss">
.setting-content{
    display:flex;
    gap:20px;
    .form-tip {
      font-weight: 400;
      font-size: 12px;
      color: #BCBFC3;
      font-size: 12px;
      line-height: 1;
      padding-top: 2px;
      position: absolute;
      top: 100%;
      left: 0;
    }
}
</style>
