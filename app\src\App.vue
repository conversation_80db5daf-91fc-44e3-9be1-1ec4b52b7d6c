<template>
  <van-config-provider id="app" class="app-container">
    <RouterView v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" v-if="route.meta.keepAlive"/>
      </keep-alive>
      <component :is="Component" v-if="!route.meta.keepAlive"/>
    </RouterView>
  </van-config-provider>
</template>

<script setup lang="ts">
import {RouterView} from "vue-router";
import {useRoute} from "vue-router";

const route = useRoute();

</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
  color: #333333;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
