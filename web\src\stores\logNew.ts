import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useWebsocketStore } from '@/stores/websocket.ts'

export const useLogNewStore = defineStore('logNew', () => {
  // 是否显示日志
  const logPanelVisible = ref(false)

  // 临时存储日志数据
  const logs = ref([])

  // 当前执行编排任务信息
  const logHistoryInfo = ref<{
    historyId: string
    taskId: string
  }>({
    historyId: '',
    taskId: '',
  })

  // 日志是否在执行中
  const isExecuting = ref(false)
  // 日志是否终止
  const isStopped = ref(false)

  // 日志执行记录
  const logData = computed(() => {
    if (isExecuting.value) {
      const data = useWebsocketStore().getMessagesByType(logHistoryInfo.value.historyId, 'exec')
      logs.value = [...data]
    }
    return logs.value
  })

  // 监听日志变化，判断是否执行完成
  watch(
    () => logData.value,
    (newVal) => {
      const lastLog = newVal[newVal.length - 1]
      const suiteLog = newVal.find((f) => f?.node_id === '' && f?.state?.startsWith('suite.'))
      if (lastLog?.node_id === 'end_node') {
        isExecuting.value = false
      }
      if (suiteLog) {
        isExecuting.value = false
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  // 显示隐藏日志面板
  const setLogPanel = (visible: boolean) => {
    logPanelVisible.value = visible
  }

  // 设置日志是否在请求中
  const setLogExecuting = (executing: boolean) => {
    isExecuting.value = executing
  }

  const setLogStoped = (stoped: boolean) => {
    isStopped.value = stoped
  }

  // 执行前清除日志相关信息
  const clearLogs = () => {
    logHistoryInfo.value.historyId = ''
  }

  // 设置日志执行后生产信息
  const setLogHistoryInfo = (historyId: string, taskId: string) => {
    logHistoryInfo.value = {
      historyId,
      taskId,
    }
  }

  return {
    // 变量
    logPanelVisible,
    isExecuting,
    isStopped,
    logData,
    logHistoryInfo,

    // 方法
    setLogPanel,
    setLogExecuting,
    setLogStoped,
    clearLogs,
    setLogHistoryInfo,
  }
})
