import logging
import random
import string
import time
import ast

import requests

from config import globals
from config.env_config import get_config_item, DLY_URL


def make_message_request(title, content, users, method, link=""):
    headers = {
        "Authorization": globals.token,
        "content-type": "application/json",
    }

    base_url = f"{get_config_item(DLY_URL)}/uniwater/event/push.json"
    data = make_message_data(title, content, users, method, link)

    if method == "chat":
        base_url = f"{get_config_item(DLY_URL)}/uniwim/message/chat/send-many"
        data = make_to_user_data(content, users)

    return {"url": base_url, "headers": headers, "data": data}


def generate_id(length=8):
    timestamp = int(time.time() * 1000)  # 毫秒级时间戳
    random_part = "".join(
        random.choices(string.ascii_letters + string.digits, k=length)
    )
    return f"{timestamp}_{random_part}"


def make_to_user_data(content, users):
    data = {"userIds": users.split(","), "content": content}
    return data


def make_message_data(title, content, users, method, link=""):
    timestamp = time.time()
    data = {
        "sourceid": "WimAI_" + generate_id(),
        "type": "WimAI_Task",
        "push": method,
        "title": title,
        "content": content,
        "begin": int(timestamp),
        "popupType": "pop",
        "users": users.split(","),
        "link": link,
        "clientLink": link,
    }
    return data


def notifier_send(title, content, users, methods, link):
    methods = ast.literal_eval(methods)
    if len(methods) == 2:
        method = ",".join(methods)
    elif len(methods) == 1:
        method = methods[0]
    else:
        method = "sys"
    request = make_message_request(title, content, users, method, link)
    response = requests.post(
        url=request["url"], headers=request["headers"], json=request["data"]
    )
    if response.status_code == 200:
        logging.info(f"发送消息成功")
        return "ok"
    else:
        logging.info(f"发送消息失败{response.status_code}")
        return response.status_code
