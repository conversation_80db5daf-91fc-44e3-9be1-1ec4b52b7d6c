<template>
  <el-form-item :label="configMeta.label || configKey">
    <el-input
      :model-value="configValue"
      @update:model-value="handleUpdate"
      :placeholder="configMeta.placeholder || `请输入${configMeta.label || configKey}`"
      :type="inputType"
      :rows="inputType === 'textarea' ? 3 : undefined"
      clearable
      size="small"
    >
      <template v-if="configMeta.prefix" #prefix>
        <span>{{ configMeta.prefix }}</span>
      </template>
      <template v-if="configMeta.suffix" #suffix>
        <span>{{ configMeta.suffix }}</span>
      </template>
    </el-input>

    <!-- 帮助文本 -->
    <div v-if="configMeta.help" class="config-help">
      <el-icon :size="12">
        <QuestionFilled />
      </el-icon>
      <span>{{ configMeta.help }}</span>
    </div>

    <!-- 示例文本 -->
    <div v-if="configMeta.example" class="config-example">
      <span class="example-label">示例:</span>
      <code>{{ configMeta.example }}</code>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

interface Props {
  configKey: string
  configValue: any
  nodeType?: string
}

interface Emits {
  (e: 'update', key: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置元数据
const configMeta = computed(() => {
  return getConfigMeta(props.nodeType, props.configKey)
})

// 输入框类型
const inputType = computed(() => {
  if (configMeta.value.multiline) return 'textarea'
  if (configMeta.value.password) return 'password'
  return 'text'
})

// 更新处理
const handleUpdate = (value: string) => {
  emit('update', props.configKey, value)
}

// 获取配置元数据
function getConfigMeta(nodeType: string | undefined, key: string) {
  const configDefinitions: Record<string, Record<string, any>> = {
    new_browser: {
      url: {
        label: 'URL地址',
        placeholder: 'https://www.example.com',
        help: '要打开的网页地址',
        example: 'https://www.baidu.com',
      },
    },
    click_element: {
      selector: {
        label: '元素选择器',
        placeholder: '//button[@id="submit"]',
        help: '用于定位页面元素的选择器表达式',
        example: '//button[text()="登录"]',
        multiline: true,
      },
    },
    input_text: {
      selector: {
        label: '元素选择器',
        placeholder: '//input[@name="username"]',
        help: '用于定位输入框的选择器表达式',
        example: '//input[@id="username"]',
        multiline: true,
      },
      text: {
        label: '输入文本',
        placeholder: '要输入的文本内容',
        help: '将要输入到元素中的文本',
        example: '<EMAIL>',
      },
    },
    get_text: {
      selector: {
        label: '元素选择器',
        placeholder: '//div[@class="result"]',
        help: '用于定位要获取文本的元素',
        example: '//span[@class="price"]',
        multiline: true,
      },
    },
    set_variable: {
      variable_name: {
        label: '变量名称',
        placeholder: 'my_variable',
        help: '变量的名称，用于在后续步骤中引用',
        example: 'user_name',
      },
      value: {
        label: '变量值',
        placeholder: '变量的值',
        help: '分配给变量的值',
        example: 'John Doe',
      },
    },
    log_message: {
      message: {
        label: '日志消息',
        placeholder: '要记录的消息内容',
        help: '将要输出到日志中的消息',
        example: '开始执行登录流程',
        multiline: true,
      },
    },
    navigate_to: {
      url: {
        label: 'URL地址',
        placeholder: 'https://www.example.com',
        help: '要导航到的网页地址',
        example: 'https://www.google.com',
      },
    },
  }

  const componentConfig = configDefinitions[nodeType || ''] || {}
  return (
    componentConfig[key] || {
      label: key,
      placeholder: `请输入${key}`,
      help: '',
    }
  )
}
</script>

<style scoped>
.config-help {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 11px;
  color: #909399;
  line-height: 1.4;
}

.config-example {
  margin-top: 4px;
  font-size: 11px;
  color: #606266;
}

.example-label {
  color: #909399;
  margin-right: 4px;
}

code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 10px;
  color: #e6a23c;
}
</style>
