import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 用户信息状态
  const userInfo = ref<any>(null)
  // 全局配置
  const configs = ref<any>(null)

  // 方法
  const setUserInfo = (info: any) => {
    userInfo.value = info
  }
  // 方法
  const setConfigs = (info: any) => {
    configs.value = info
  }

  const clearUser = () => {
    userInfo.value = null
  }
  const clearConfigs = () => {
    configs.value = null
  }

  return {
    userInfo,
    setUserInfo,
    clearUser,

    configs,
    setConfigs,
    clearConfigs
  }
})
