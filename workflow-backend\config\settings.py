from typing import Optional
from .env_config import get_config_item,DLY_URL,AI_RUL,BPM_URL

# 度量云地址
DlY_URl: Optional[str] = None
# 一诺地址
YN_URL: Optional[str] = None
# AI模型地址
AIMODEL_URL: Optional[str] = None
# BPM服务地址
BPM_URL: Optional[str] = None


def set_settings(
        dly_url: str,
        yn_url: str,
        aimodel_url: str,
        bpm_url: str
) -> None:

    global DlY_URl, YN_URL, AIMODEL_URL, BPM_URL

    DlY_URl = get_config_item(DLY_URL)
    YN_URL = get_config_item(YN_URL)
    AIMODEL_URL = get_config_item(AI_RUL)
    BPM_URL = get_config_item(BPM_URL)