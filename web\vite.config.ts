import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import ElementPlus from 'unplugin-element-plus/vite'
import { resolve } from 'path';
 // 我们约定环境变量名为 VITE_BUILD_PAGE
const pageName = process.env.VITE_BUILD_PAGE;
const allPages = {
  index: resolve(__dirname, 'index.html'),
  admin: resolve(__dirname, 'admin.html')
}

// 2. 根据环境变量决定最终的入口
function getBuildInput() {
  // 从环境变量中获取要打包的页面名称



  // 如果没有指定页面，或者指定为 'all'，则打包所有页面
  if (!pageName || pageName.toLowerCase() === 'all') {
    console.log('Building all pages...');
    return allPages;
  }
  console.log('allPages',allPages)
  // 如果指定了页面，则只打包该页面
  const singlePageEntry = allPages[pageName];
  if (singlePageEntry) {
    console.log(`Building single page: ${pageName}... ${singlePageEntry}`);
    return {
      [pageName]: singlePageEntry,
    };
  } else {
    // 如果找不到指定的页面，抛出错误
    console.error(`Error: Page "${pageName}" not found.`);
    console.error('Available pages are:', Object.keys(allPages).join(', '));
    process.exit(1); // 退出构建
  }
}

// https://vite.dev/config/
export default defineConfig({
  build: {
    outDir: 'WimTask', // 指定输出目录为WimTask
    emptyOutDir: true,//每次打包清空输出目录
    assetsDir: 'assets', // 静态资源目录
    minify: 'terser', // 使用terser进行代码压缩
    rollupOptions: {
      input: getBuildInput(),
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js', // 代码分割后的文件名格式
        entryFileNames: 'assets/js/[name]-[hash].js', // 入口文件名格式
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]' // 静态资源文件名格式
      }
    },
    // 设置图片转 Base64 的大小阈值
    assetsInlineLimit: pageName==="admin"?2048:4096, // 单位为字节，这里设置为 4KB，小于 4KB 的图片会转为 Base64
  },
  base: './',
  plugins: [
    vue(),
    vueDevTools(),
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
    ElementPlus({ useSource: true }),
  ],
  server: {
    proxy: {
      '^/uniwim': {
        // target: 'https://www.dlmeasure.com',
        target: 'http://192.168.100.4:31830',
        // target: 'https://122.224.132.37:38081/',
        changeOrigin: true
      },
      '^/wimai': {
        // target: 'https://www.dlmeasure.com',
        target: 'http://192.168.100.4:31830',
        // target: 'https://122.224.132.37:38081/',
        changeOrigin: true
      },
      '^/oneMap3D': {
        // target: 'https://www.dlmeasure.com',
        target: 'https://www.dlmeasure.com/extends',
        // target: 'https://122.224.132.37:38081/',
        changeOrigin: true
      },
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler',
        additionalData: `@use "@/styles/element/index.scss" as *;`,
      },
    },
  },
  optimizeDeps: {
    exclude: ['electron'],
  },
})
