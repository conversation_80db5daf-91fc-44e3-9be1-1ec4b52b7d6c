{"en": {"code": "en", "intro": "Introduction", "libVersion": "Library version", "libScope": "Library scope", "importing": "Importing", "arguments": "Arguments", "doc": "Documentation", "keywords": "Keywords", "tags": "Tags", "returnType": "Return Type", "kwLink": "Link to this keyword", "argName": "Argument name", "varArgs": "Variable number of arguments", "varNamedArgs": "Variable number of named arguments", "namedOnlyArg": "Named only argument", "posOnlyArg": "Positional only argument", "defaultTitle": "Default value that is used if no value is given", "typeInfoDialog": "Click to show type information", "search": "Search", "dataTypes": "Data types", "allowedValues": "Allowed Values", "dictStructure": "Dictionary Structure", "convertedTypes": "Converted Types", "usages": "Usages", "generatedBy": "Generated by", "on": "on", "chooseLanguage": "Choose language"}, "fi": {"code": "fi", "intro": "<PERSON><PERSON><PERSON><PERSON>", "libVersion": "<PERSON><PERSON><PERSON><PERSON> versio", "libScope": "<PERSON><PERSON><PERSON><PERSON>", "importing": "K<PERSON><PERSON>tö<PERSON>not<PERSON>", "arguments": "Argumentit", "doc": "Dokumentaatio", "keywords": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Tagit", "returnType": "<PERSON><PERSON>uar<PERSON>", "kwLink": "<PERSON><PERSON> t<PERSON>", "argName": "<PERSON><PERSON><PERSON><PERSON> nimi", "varArgs": "Vaihteleva määrä argumentteja", "varNamedArgs": "Vaihteleva määrä nimett<PERSON> argument<PERSON>ja", "namedOnlyArg": "<PERSON><PERSON>", "posOnlyArg": "Vain positionaalisia argument<PERSON>ja", "defaultTitle": "<PERSON><PERSON><PERSON><PERSON>, jota käytetään jos arvoa ei anneta", "typeInfoDialog": "Näytä tyyppitieto", "search": "Etsi", "dataTypes": "Datatyypit", "allowedValues": "Sallitut arvot", "dictStructure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "convertedTypes": "<PERSON><PERSON><PERSON><PERSON> tyypit", "usages": "Käyttöpaikat", "generatedBy": "<PERSON><PERSON><PERSON>", "on": "", "chooseLanguage": "Valitse kieli"}, "fr": {"code": "fr", "intro": "Introduction", "libVersion": "Version de la bibliothèque", "libScope": "Portée de la bibliothèque", "importing": "Importation", "arguments": "Arguments", "doc": "Documentation", "keywords": "Mots-clés", "tags": "Tags", "returnType": "Type de retour", "kwLink": "Lien vers ce mot-clé", "argName": "Nom de l'argument", "varArgs": "Nombre variable d'arguments", "varNamedArgs": "Nombre variable d'arguments nommés", "namedOnlyArg": "Argument nommé uniquement", "posOnlyArg": "Argument positionnel uniquement", "defaultTitle": "Valeur par défaut utilisée si aucune valeur n'est donnée", "typeInfoDialog": "Cliquez pour afficher les informations de type", "search": "<PERSON><PERSON><PERSON>", "dataTypes": "Types de données", "allowedValues": "Valeurs autorisées", "dictStructure": "Structure du dictionnaire", "convertedTypes": "Types convertis", "usages": "Utilisations", "generatedBy": "Généré par", "on": "le", "chooseLanguage": "Choisir la langue"}, "it": {"code": "it", "intro": "Introduzione", "libVersion": "Versione della libreria", "libScope": "Ambito della libreria", "importing": "Importazione", "arguments": "Argomenti", "doc": "Documentazione", "keywords": "<PERSON><PERSON><PERSON> chiave", "tags": "Tag", "returnType": "Tipo di ritorno", "kwLink": "<PERSON> a questa parola chiave", "argName": "Nome dell'argomento", "varArgs": "Numero variabile di argomenti", "varNamedArgs": "Numero variabile di argomenti nominati", "namedOnlyArg": "Argomento solo nominato", "posOnlyArg": "Argomento solo posizionale", "defaultTitle": "<PERSON>ore predefinito utilizzato se non viene fornito un valore", "typeInfoDialog": "Clicca per mostrare le informazioni sul tipo", "search": "Cerca", "dataTypes": "Tipi di dati", "allowedValues": "Valori consentiti", "dictStructure": "Struttura del dizionario", "convertedTypes": "Tipi convertiti", "usages": "<PERSON><PERSON><PERSON><PERSON>", "generatedBy": "<PERSON><PERSON> da", "on": "su", "chooseLanguage": "Scegli la lingua"}, "nl": {"code": "nl", "intro": "Introductie", "libVersion": "Bibliotheekversie", "libScope": "Bibliotheekbereik", "importing": "Importeren", "arguments": "Parameters", "doc": "Documentatie", "keywords": "Actiewoorden", "tags": "Labels", "returnType": "Andwoord type", "kwLink": "<PERSON> naar actiewo<PERSON>", "argName": "Benoemde parameters", "varArgs": "Variabel aantal parameters", "varNamedArgs": "Variable aantal benoemde parameters", "namedOnlyArg": "<PERSON><PERSON> ben<PERSON>", "posOnlyArg": "Aleen positionele parameters", "defaultTitle": "Standaard waarde welke wordt gebruikt als geen waarde is gegeven", "typeInfoDialog": "Klik om informatie over dit type te zien", "search": "<PERSON><PERSON>", "dataTypes": "Datatypen", "allowedValues": "Geldige waarden", "dictStructure": "Woordenboek Structuur", "convertedTypes": "Geconverteerde typen", "usages": "<PERSON><PERSON><PERSON><PERSON><PERSON> in", "generatedBy": "Gegenereerd door", "on": "op", "chooseLanguage": "<PERSON><PERSON> taal"}, "pt-br": {"code": "pt-BR", "intro": "Introdução", "libVersion": "Versão da Biblioteca", "libScope": "Escopo da Biblioteca", "importing": "Importação", "arguments": "Argumentos", "doc": "Documentação", "keywords": "Palavras-Chave", "tags": "Etiquetas", "returnType": "Tipo de Retorno", "kwLink": "Ligação para a palavra-chave", "argName": "Nome do Argumento", "varArgs": "Argumentos em quantidade variável", "varNamedArgs": "Argumentos nomeados em quantidade variável", "namedOnlyArg": "Apenas argumentos nomeados", "posOnlyArg": "Apenas argumentos posicionais", "defaultTitle": "Valor padrão que é usado se nenhum tiver sido informado", "typeInfoDialog": "Clicar para mostrar informação de tipo", "search": "<PERSON><PERSON><PERSON><PERSON>", "dataTypes": "Tipos de dados", "allowedValues": "Valores permitidos", "dictStructure": "Estrutura de Dicionário", "convertedTypes": "Tipos Convertidos", "usages": "Usos", "generatedBy": "Gerado por", "on": "ligado", "chooseLanguage": "Escolher idioma"}, "pt-pt": {"code": "pt-PT", "intro": "Introdução", "libVersion": "Versão da Biblioteca", "libScope": "Âmbito da Biblioteca", "importing": "Importação", "arguments": "Argumentos", "doc": "Documentação", "keywords": "Palavras-Chave", "tags": "Etiquetas", "returnType": "Tipo de Retorno", "kwLink": "Ligação para a palavra-chave", "argName": "Nome do Argumento", "varArgs": "Argumentos em quantidade variável", "varNamedArgs": "Argumentos nomeados em quantidade variável", "namedOnlyArg": "Apenas argumentos nomeados", "posOnlyArg": "Apenas argumentos posicionais", "defaultTitle": "Valor por omissão que é usado se nenhum tiver sido dado", "typeInfoDialog": "Clicar para mostrar informação de tipo", "search": "Procurar", "dataTypes": "Tipos de dados", "allowedValues": "Valores permitidos", "dictStructure": "Estrutura de Dicionário", "convertedTypes": "Tipos Convertidos", "usages": "Utilização", "generatedBy": "Gerado por", "on": "ligado", "chooseLanguage": "Escolher <PERSON>"}}