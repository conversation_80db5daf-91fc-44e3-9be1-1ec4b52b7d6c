<template>
  <div id="app" class="app-container">
    <!-- <pageLoading v-if="loadingStore.isLoading"/> -->
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import pageLoading from '@/components/pageLoading/index.vue'
import { RouterView } from 'vue-router'
import { useLoadingStore } from '@/stores/loading';
const loadingStore = useLoadingStore();

const loading = document.getElementById('loading');
if (loading) {
    loading.classList.add('loading-hidden');
    loading.style.display = 'none';
    loading.remove();
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 主题定制 */
:root {

}
</style>
