import requests
import sounddevice as sd
import numpy as np
from loguru import logger

from config import globals
from typing import Optional

from config.env_config import get_config_item
from config.env_config import YN_URL


class PCMStreamPlayer:

    def make_request(self):
        self.url = f"{get_config_item(YN_URL)}/wimai/api/tool/ttsStream"
        self.headers = {
            "Authorization": globals.token,
            "Content-Type": "application/json",
        }
        # 音频参数（需与服务端保持一致）
        self.sample_rate = 22050
        self.channels = 1
        self.leftover = b""  # 用于存储未对齐的剩余字节

    def play_pcm_stream(self, text: str) -> None:
        """
        流式获取 PCM 音频并播放

        参数:
            text: 需要转换为语音的文本
        """
        try:
            # 发送 POST 请求，获取流式响应
            response = requests.post(
                self.url,
                headers=self.headers,
                json={"text": text},
                stream=True,  # 启用流式响应
            )

            if not response.ok:
                logger.info(f"请求失败: {response.status_code}")
                return

            # 初始化音频输出流
            with sd.OutputStream(
                samplerate=self.sample_rate, channels=self.channels, dtype=np.float32
            ) as stream:
                logger.info("开始播放...")

                # 逐块读取响应数据
                for chunk in response.iter_content(chunk_size=1024):
                    if not chunk:
                        break

                    # 处理数据对齐（与 JavaScript 逻辑对应）
                    pcm_bytes = self._process_pcm_bytes(chunk)
                    if not pcm_bytes:
                        continue

                    # 转换为 Float32 并归一化（-1.0 到 1.0）
                    pcm_float = self._convert_to_float32(pcm_bytes)

                    # 播放音频块
                    stream.write(pcm_float)

            logger.info("播放结束")

        except Exception as e:
            logger.info(f"播放出错: {str(e)}")
        finally:
            # 确保响应被关闭
            if "response" in locals():
                response.close()

    def _process_pcm_bytes(self, new_bytes: bytes) -> Optional[bytes]:
        """处理 PCM 字节数据，确保 16 位对齐"""
        # 拼接上次剩余的数据
        combined = self.leftover + new_bytes
        self.leftover = b""

        # 确保数据长度为偶数（16位PCM每个样本占2字节）
        if len(combined) % 2 != 0:
            self.leftover = combined[-1:]  # 保留最后一个字节
            combined = combined[:-1]  # 截取到偶数长度

        return combined if len(combined) > 0 else None

    def _convert_to_float32(self, pcm_bytes: bytes) -> np.ndarray:
        """将 16 位 PCM 字节转换为归一化的 32 位浮点数数组"""
        # 转换为 16 位整数数组
        pcm_int16 = np.frombuffer(pcm_bytes, dtype=np.int16)
        # 归一化到 -1.0 到 1.0 范围
        return pcm_int16.astype(np.float32) / 32768.0


def text_to_speech(text=""):
    player = PCMStreamPlayer()
    player.make_request()
    player.play_pcm_stream(text)
