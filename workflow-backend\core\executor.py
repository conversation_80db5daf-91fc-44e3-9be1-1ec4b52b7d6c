import asyncio
import inspect
import json
import os
import re
from dataclasses import field
from datetime import datetime, timedelta
from queue import Queue, Empty
from typing import Dict, Any, List, Optional, Callable, Annotated, Awaitable, Type

from loguru import logger
from nanoid import generate
from pydantic import BaseModel
from selenium.webdriver.common.options import BaseOptions
from starlette.concurrency import run_in_threadpool

from core.model import (
    ActionNode,
    ActionParam,
    AgentMetadata,
    ExecutionState,
    ActionResult,
    Agent,
    BaseActionParams,
    ActionOptions,
)
from utils.types import is_dict_str_any


# agent执行上下文
class ExecutionContext:

    history_id: str
    task_id: str
    state: str = ExecutionState.New

    start_time: datetime
    end_time: datetime
    elapsed: timedelta

    # 执行历史
    history: [ActionResult]
    # 变量，初始化传入页面变量管理的全局变量，每次执行一个action都可以设置入对应的变量
    variables: Dict[str, Any]
    # 执行错误
    errors: List[str]
    messages: List[str]

    def __init__(
        self, task_id: str, history_id: str = None, variables: Dict[str, Any] = None
    ):
        if variables is None:
            variables = {}
        if history_id is None:
            history_id = generate()
        self.task_id = task_id
        self.variables = variables
        self.history_id = history_id
        self.errors = []
        self.messages = []

    def set_variable(self, key, value):
        self.variables[key] = value

    def start(self):
        self.start_time = datetime.now()
        self.state = ExecutionState.RUNNING

    def success(self):
        self.end_time = datetime.now()
        self.elapsed = self.end_time - self.start_time
        self.state = ExecutionState.SUCCESS

    def failed(self, msg: str):
        self.errors.append(msg)
        self.state = ExecutionState.FAILED

    def cancel(self, msg: str):
        self.messages.append(msg)
        self.state = ExecutionState.CANCELLED


class ActionContext:
    _variables: Dict[str, str] = {}
    _agent_context: ExecutionContext
    _result: ActionResult
    node: ActionNode
    _send_data: Callable[[Dict[str, Any]], Awaitable[None]]
    _retried: int

    def __init__(
        self,
        node: ActionNode,
        agent_context: ExecutionContext,
        send_data: Callable[[Dict[str, Any]], Awaitable[None]] = None,
    ):
        self._agent_context = agent_context
        self._variables = agent_context.variables
        self._result = ActionResult(node.id)
        self._send_data = send_data

    async def send_data(self, data: Dict[str, Any]):
        if self._send_data is None:
            logger.info(f"发送数据{data}")
        else:
            await self._send_data(data)

    def set_input(self, key: str, val: Any):
        self._result.inputs[key] = val

    def set_output(self, key: str, val: Any):
        self._result.outputs[key] = val

    def get_inputs(self):
        return self._result.inputs

    def get_output(self):
        return self._result.outputs

    def get_agent_context(self) -> ExecutionContext:
        return self._agent_context

    def is_running(self) -> bool:
        return self._result.state == ExecutionState.RUNNING

    def clear_for_retry(self):
        self._retried = self._retried + 1
        self._result = ActionResult(node_id=self._result.node_id)

    def get_variable(self, key: str, scope: str = "all", default=None):
        match scope:
            case "local" | "action":
                return self._variables.get(key, default)
            case "suite" | "agent":
                return self._agent_context.variables.get(key, default)
            case "all":
                if key in self._variables:
                    return self._variables[key]
                return self._agent_context.variables.get(key, default)
            case _:
                raise ValueError(f"非法的参数作用域[{scope}]")

    def set_variable(self, key, value, scope: str = "all"):
        match scope:
            case "local" | "action":
                # 设置本地/动作级变量
                self._variables[key] = value
            case "suite" | "agent":
                # 设置代理/套件级变量
                self._agent_context.variables[key] = value
            case "all":
                # 同时设置所有作用域
                self._variables[key] = value
                self._agent_context.variables[key] = value
            case _:
                raise ValueError(f"非法的参数作用域[{scope}]")

    def success(self, condition_key="", msg=""):
        if self._result is not None:
            self._result.messages.append(msg)
            self._result.condition_key = condition_key
            self._result.state = ExecutionState.SUCCESS

    def failed(self, err=""):
        if self._result is not None:
            self._result.errors.append(err)
            self._result.state = ExecutionState.FAILED

    def cancel(self, msg=""):
        if self._result is not None:
            if msg != "":
                self._result.messages.append(msg)
            self._result.state = ExecutionState.CANCELLED

    def message(self, msg: str):
        if self._result is not None and msg != "":
            self._result.messages.append(msg)

    def error(self, msg: str):
        if self._result is not None and msg != "":
            self._result.errors.append(msg)

    def is_end(self):
        match self._result.state:
            case ExecutionState.RUNNING | ExecutionState.PENDING:
                return False
            case (
                ExecutionState.FAILED
                | ExecutionState.SUCCESS
                | ExecutionState.CANCELLED
            ):
                return True
            case _:
                logger.warning(f"未知的运行状态{self._result.state}")
        return False

    def return_result(self) -> ActionResult:
        return self._result


class Action:

    _callable: Callable[..., Awaitable[ActionResult]]

    def __init__(
        self,
        type: Annotated[str, "动作类型"],
        label: str,
        description: str = "",
        icon: str = None,
        category: str = "other",
        template: str = "",
        params: Optional[Type[BaseActionParams]] = None,
        config_schema: Dict[str, Dict[str, Any]] = None,
        inputs: List[str] = None,
        outputs: List[str] = None,
        require: List[str] = None,
    ):
        self.type = type
        self.label = label
        self.description = description
        self.icon = icon
        self.category = category
        self.template = template
        self.params = params
        self.config_schema = config_schema
        self.inputs = inputs
        self.outputs = outputs
        self.require = require

    async def run(self, *args) -> ActionResult:
        return await self._callable(*args)

    def make(self, func) -> Optional[str]:
        err = self.validate_params(func)
        if err is not None:
            return err

        async def wrapper(*args) -> ActionResult:
            if inspect.iscoroutinefunction(func):
                await func(*args)
            else:
                await run_in_threadpool(func, *args)
            c = args[0]
            if c.is_running():
                c.success()
            return c.return_result()

        self._callable = wrapper
        return None

    def parse_params(self, config: Dict[str, Any]) -> Dict[str, Any] | BaseActionParams:
        if self.params is None:
            return config
        else:
            return self.params(**config)

    def validate_params(self, func) -> Optional[str]:
        sig = inspect.signature(func)
        params = list(sig.parameters.values())

        if len(params) < 2:
            return f"可调用对象至少需要两个参数,动作函数只有{len(params)}个参数"

        first_param_anno = params[0].annotation
        second_param_anno = params[1].annotation

        if first_param_anno is not ActionContext:
            return f"动作函数第一个参数类型应为ActionContext，实际为{first_param_anno}"

        if self.params is None:
            is_dict_str_any(params[1])
        elif second_param_anno is not self.params:
            return f"动作函数第二个参数需为{self.params}，实际为{second_param_anno}"
        return None


class ActionGroup:
    actions: Dict[str, Action]

    def __init__(self):
        self.actions = {}

    def action(
        self,
        type: Annotated[str, "动作类型"],
        label: str,
        description: str = "",
        icon: str = None,
        category: str = "other",
        template: str = "",
        params: Optional[Type[BaseActionParams]] = None,
        config_schema: Dict[str, Dict[str, Any]] = None,
        inputs: List[str] = None,
        outputs: List[str] = None,
        require: List[str] = None,
    ):

        action = Action(
            type=type,
            label=label,
            description=description,
            icon=icon,
            category=category,
            template=template,
            params=params,
            config_schema=config_schema,
            inputs=inputs,
            outputs=outputs,
            require=require,
        )

        def decorator(func):
            err = action.make(func)
            if err is not None:
                logger.info(f"注册动作失败{action.type}{err}")
            else:
                self.actions[type] = action
                logger.info(f"注册动作{action.type}成功")
            return func

        return decorator


class ActionManager:
    _actions: Dict[str, Action]

    def __init__(self):
        self._actions = {}

    def _add_action(self, typ: str, action: Action):
        if typ in self._actions:
            raise ValueError(f"严重错误,动作类型重复{typ}")
        self._actions[typ] = action

    def include(self, group: ActionGroup):
        for action in group.actions.values():
            self._add_action(action.type, action)

    def get_requires(self, typ: str) -> List[str]:
        action = self._actions[typ]
        return action.require

    def has(self, typ: str) -> bool:
        return typ in self._actions

    def get(self, typ: str, must: bool = False) -> Optional[Action]:
        if typ in self._actions:
            return self._actions[typ]
        if must:
            raise ValueError(f"未知的动作类型[{typ}]")
        return None


class NodeRouter:
    node_map: Dict[str, ActionNode]
    current_node_id: str
    passed: set[str]
    waiting: List[str]
    current_node: Optional[ActionNode] = None
    is_last_node = False
    is_end = False

    def __init__(self, node_map: Dict[str, ActionNode]):
        self.node_map = node_map
        self.passed = set()
        self.waiting = []

    @staticmethod
    def geather_next_node_ids(node: ActionNode) -> List[str]:
        if not node.is_condition_node:
            return node.next_nodes

        return [
            item
            for value_list in node.next_condition_nodes.values()
            for item in value_list
        ]

    def start(self) -> Optional[str]:
        node = self.node_map.get("start_node", None)
        if node is None:
            return f"找不到开始节点，或者开始节点的id不是start_node"
        self.set_current_node(node)
        return None

    def next(self, condition_key: str = None) -> Optional[str]:

        if self.is_last_node:
            self.done()
            return None

        if len(self.passed) > 1000:
            return f"超过1000个节点，保护机制终止执行"

        if self.current_node is not None:
            err = self.pass_one(condition_key)
            if err is not None:
                return err

        try:
            err = self.try_next()
            if err is not None:
                return err
        except IndexError:
            return f"没有最后结束节点"

        return None

    def try_next(self) -> Optional[str]:
        has = False
        for node_id in self.waiting:
            node = self.node_map.get(node_id, None)
            if node is None:
                return f"下一个node的id无效{node_id}"
            if self.can_next(node):
                self.set_current_node(node)
                self.waiting.remove(node_id)
                has = True
                break

        if not has:
            return f"等待队列里面所有的节点都不能运行{self.waiting}"

        return None

    def set_current_node(self, node: ActionNode):
        self.current_node = node
        if (
            len(node.next_nodes) == 0 and len(node.next_condition_nodes) == 0
        ) or node.id == "workflow_end":
            self.is_last_node = True

    def mark_unfit_condition_path(self, condition_path: str) -> Optional[str]:
        stack: List[str] = []
        for key, node_ids in self.current_node.next_condition_nodes.items():
            for node_id in node_ids:
                if condition_path == key:
                    continue
                stack.append(node_id)

        passed = set()
        passed.add(self.current_node.id)

        while len(stack) > 0:
            node_id = stack.pop()
            node = self.node_map.get(node_id, None)
            if node is None:
                return f"node不能对应node{node_id}"

            diffs = set(node.previous_nodes).difference(passed)
            if len(diffs) < 1:
                passed.add(node_id)
                next_node_ids = NodeRouter.geather_next_node_ids(node)
                stack.extend(next_node_ids)
            elif diffs.issubset(self.passed):
                self.add_to_waiting(node_id)

        self.passed.update(passed)

        return None

    def can_next(self, node: ActionNode):
        return set(node.previous_nodes).issubset(self.passed)

    def pass_one(self, condition_key: str) -> Optional[str]:
        self.passed.add(self.current_node.id)
        if condition_key != "":
            node_ids = self.current_node.next_condition_nodes.get(condition_key, None)
            if node_ids is None:
                return f"有多个下一个节点候选，但是传递的状态key确定不了下一个节点，condition_key={condition_key},node={self.current_node.id},next={self.current_node.next_nodes}"
            self.mark_unfit_condition_path(condition_key)
            for node_id in node_ids:
                self.add_to_waiting(node_id)
            return None
        for node_id in self.current_node.next_nodes:
            self.add_to_waiting(node_id)

        return None

    def add_to_waiting(self, node_id: str):
        if not node_id in self.waiting:
            self.waiting.append(node_id)

    def done(self):
        self.current_node = None
        self.is_end = True


_variable_regex = re.compile(r"\$\{(\w+)\}")


def _replace_placeholders(v: Dict[str, Any], p: Dict[str, Any]) -> None:
    def replace_value(value: Any) -> Any:
        """递归处理单个值"""
        if isinstance(value, str):
            # 对字符串进行替换，使用lambda函数处理每个匹配
            return _variable_regex.sub(
                lambda match: str(p.get(match.group(1), match.group(0))), value
            )
        elif isinstance(value, dict):
            # 对子字典递归处理
            for k, v_val in value.items():
                value[k] = replace_value(v_val)
            return value
        elif isinstance(value, list):
            # 对列表中的每个元素递归处理
            return [replace_value(item) for item in value]
        # 其他类型保持不变
        return value

    # 处理顶层字典
    for key in v:
        v[key] = replace_value(v[key])


class AgentExecutor:
    pid: int
    name: str
    get_action: Callable[[str], Action]

    def __init__(self, get_action: Callable[[str], Optional[Action]], name: str = None):
        self.pid = os.getpid()
        if name is None:
            name = f"agent{self.pid}"
        self.name = name
        self.get_action = get_action

    def action_start(
        self,
        node: ActionNode,
        action: Action,
        context: ActionContext,
        params: Dict[str, Any] | BaseActionParams,
        options: ActionOptions,
    ):
        if node.inputs is not None:
            for key in node.inputs:
                variable = context.get_variable(key)
                if variable is not None:
                    context.set_input(key, variable)
        logger.info(
            f"agent={self.pid} action {node.label}-{node.id} start {context.get_inputs()}"
        )

    def action_finish(
        self,
        node: ActionNode,
        action: Action,
        context: ActionContext,
        params: Dict[str, Any] | BaseActionParams,
        options: ActionOptions,
    ):

        if node.outputs is not None:
            for key in node.outputs:
                variable = context.get_variable(key)
                if variable is not None:
                    context.set_output(key, variable)
        logger.info(
            f"agent={self.pid} action {node.label}-{node.id} finish {context.get_output()}"
        )

    async def retry_run(
        self,
        node: ActionNode,
        action: Action,
        context: ActionContext,
        params: Dict[str, Any] | BaseActionParams,
        options: ActionOptions,
    ):
        self.action_start(node, action, context, params, options)
        result = await action.run(context, params)
        self.action_finish(node, action, context, params, options)
        if result.state == ExecutionState.FAILED:
            i = 0
            while i < options.retry_times:
                self.action_start(node, action, context, params, options)
                context.clear_for_retry()
                result = await action.run(context, params)
                self.action_finish(node, action, context, params, options)
                if result.state != ExecutionState.FAILED:
                    break

        return result

    async def execute(
        self,
        agent: Agent,
        execution_id: str = "",
        send_data: Callable[[Dict[str, Any]], Awaitable[None]] = None,
    ):
        if execution_id == "":
            execution_id = generate()

        ctx = ExecutionContext(agent.task_id, execution_id, agent.variables)
        ctx.start()
        router = NodeRouter(agent.node_map)
        err = router.start()
        if err is not None:
            ctx.failed(err)
        while not router.is_end:
            if ctx.state != ExecutionState.RUNNING:
                break

            action_node = router.current_node
            action = self.get_action(action_node.componentType)
            if action is None:
                ctx.failed(f"无效的动作类型{action_node.type}")
                return

            # XXX 这里要注意下，是否需要把上次执行的action的本地变量纳入到替换中来
            _replace_placeholders(action_node.config, ctx.variables)
            try:
                params = action.parse_params(action_node.config)
            except Exception as e:
                ctx.failed(f"异常的action参数{action_node.type} {e}")
                break

            action_context = ActionContext(action_node, ctx, send_data)

            result = await self.retry_run(
                action_node, action, action_context, params, action_node.options
            )

            await asyncio.sleep(action_node.options.run_interval / 1000)

            if result.state == ExecutionState.FAILED:
                if action_node.options.error_handle == "stop":
                    ctx.failed(f"{action_node.type}")
                    break

            condition_key = result.condition_key
            err = router.next(condition_key)
            if err is not None:
                ctx.failed(f"流程错误 {err}")
                break

        match ctx.state:
            case ExecutionState.RUNNING:
                ctx.success()
            case (
                ExecutionState.SUCCESS
                | ExecutionState.FAILED
                | ExecutionState.CANCELLED
            ):
                pass
            case _:
                ctx.failed(f"agent执行状态为未知{ctx.state}")
