import json
import os
import re
from dataclasses import field
from datetime import datetime, timedelta
from queue import Queue, Empty
from typing import Dict, Any, List, Optional, Callable, Annotated

from loguru import logger
from nanoid import generate

from core.model import (
    ActionNode,
    ActionParam,
    AgentMetadata,
    ExecutionState,
    ActionResult,
    Agent,
)


# agent执行上下文
class ExecutionContext:
    current: ActionResult

    current_node: ActionNode

    history_id: str
    task_id: str
    state: str = ExecutionState.New

    start_time: datetime
    end_time: datetime
    elapsed: timedelta

    # 执行历史
    history: [ActionResult]
    # 变量，初始化传入页面变量管理的全局变量，每次执行一个action都可以设置入对应的变量
    variables: Dict[str, Any]
    # 执行错误
    errors: List[str]
    messages: List[str]

    send_data: Callable[[Dict[str, Any]], None]

    def __init__(
        self,
        task_id: str,
        history_id: str = None,
        variables: Dict[str, Any] = None,
        send_data: Callable[[Dict[str, Any]], None] = lambda data: logger.info(
            f"动作中发送数据:{json.dumps(data, ensure_ascii=False)}"
        ),
    ):
        if variables is None:
            variables = {}
        if history_id is None:
            history_id = generate()
        self.task_id = task_id
        self.send_data = send_data
        self.variables = variables
        self.history_id = history_id
        self.errors = []
        self.messages = []

    def action_success(self, condition_key="", msg=""):
        if self.current is not None:
            self.current.messages.append(msg)
            self.current.condition_key = condition_key
            self.current.state = ExecutionState.SUCCESS

    def action_failed(self, err=""):
        if self.current is not None:
            self.current.errors.append(err)
            self.current.state = ExecutionState.FAILED

    def action_cancel(self, msg=""):
        if self.current is not None:
            if msg != "":
                self.current.messages.append(msg)
            self.state = ExecutionState.CANCELLED

    def action_message(self, msg: str):
        if self.current is not None and msg != "":
            self.current.messages.append(msg)

    def action_error(self, msg: str):
        if self.current is not None and msg != "":
            self.current.errors.append(msg)

    def set_variable(self, key, value):
        self.variables[key] = value

    def next(self, node: ActionNode):
        self.current_node = node
        self.current = ActionResult(node_id=self.current_node.id)

    def start(self):
        self.start_time = datetime.now()
        self.state = ExecutionState.RUNNING

    def success(self):
        self.end_time = datetime.now()
        self.elapsed = self.end_time - self.start_time
        self.state = ExecutionState.SUCCESS

    def failed(self, msg: str):
        self.errors.append(msg)
        self.state = ExecutionState.FAILED

    def cancel(self, msg: str):
        self.messages.append(msg)
        self.state = ExecutionState.CANCELLED


class Action:
    type = ""
    label = ""
    description = ""
    icon: str = ""
    category: str = ""
    template: str = ""
    config_schema: Dict[str, ActionParam]
    outputs: List[str]
    require: List[str]
    run: Callable[[ExecutionContext, Dict, Dict], None] = None

    def __init__(self):
        self.params = {}
        self.output = []
        self.require = []


class ActionGroup:
    actions: Dict[str, Action]

    def __init__(self):
        self.actions = {}

    def action(
        self,
        type: Annotated[str, "动作类型"],
        label: str,
        description: str = "",
        category: str = "other",
        config_schema: Dict[str, Dict[str, Any]] = None,
        inputs: List[str] = None,
        outputs: List[str] = None,
        require: List[str] = None,
        icon: str = None,
        template: str = "",
    ):
        action = Action()
        action.type = type
        action.label = label
        action.description = description
        action.category = category
        action.require = require
        action.config_schema = config_schema or {}
        action.inputs = inputs or []
        action.outputs = outputs or []
        action.template = template
        action.icon = icon

        def decorator(func):
            logger.info(f"注册动作{action.type}")
            action.run = func
            self.actions[type] = action
            return func

        return decorator


class ActionManager:
    _actions: Dict[str, Action]

    def __init__(self):
        self._actions = {}

    def _add_action(self, typ: str, action: Action):
        if typ in self._actions:
            raise ValueError(f"严重错误,动作类型重复{typ}")
        self._actions[typ] = action

    def include(self, group: ActionGroup):
        for action in group.actions.values():
            self._add_action(action.type, action)

    def get_requires(self, typ: str) -> List[str]:
        action = self._actions[typ]
        return action.require

    def has(self, typ: str) -> bool:
        return typ in self._actions

    def get(self, typ: str, must: bool = False) -> Optional[Action]:
        if typ in self._actions:
            return self._actions[typ]
        if must:
            raise ValueError(f"未知的动作类型[{typ}]")
        return None


class NodeRouter:
    node_map: Dict[str, ActionNode]
    current_node_id: str
    passed: set[str]
    waiting: List[str]
    current_node: Optional[ActionNode] = None
    is_last_node = False
    is_end = False

    def __init__(self, node_map: Dict[str, ActionNode]):
        self.node_map = node_map
        self.passed = set()
        self.waiting = []

    @staticmethod
    def geather_next_node_ids(node: ActionNode) -> List[str]:
        if not node.is_condition_node:
            return node.next_nodes

        return [
            item
            for value_list in node.next_condition_nodes.values()
            for item in value_list
        ]

    def start(self) -> Optional[str]:
        node = self.node_map.get("start_node", None)
        if node is None:
            return f"找不到开始节点，或者开始节点的id不是start_node"
        self.set_current_node(node)
        return None

    def next(self, condition_key: str = None) -> Optional[str]:

        if self.is_last_node:
            self.done()
            return None

        if len(self.passed) > 1000:
            return f"超过1000个节点，保护机制终止执行"

        if self.current_node is not None:
            err = self.pass_one(condition_key)
            if err is not None:
                return err

        try:
            err = self.try_next()
            if err is not None:
                return err
        except IndexError:
            return f"没有最后结束节点"

        return None

    def try_next(self) -> Optional[str]:
        has = False
        for node_id in self.waiting:
            node = self.node_map.get(node_id, None)
            if node is None:
                return f"下一个node的id无效{node_id}"
            if self.can_next(node):
                self.set_current_node(node)
                self.waiting.remove(node_id)
                has = True
                break

        if not has:
            return f"等待队列里面所有的节点都不能运行{self.waiting}"

        return None

    def set_current_node(self, node: ActionNode):
        self.current_node = node
        if (
            len(node.next_nodes) == 0 and len(node.next_condition_nodes) == 0
        ) or node.id == "workflow_end":
            self.is_last_node = True

    def mark_unfit_condition_path(self, condition_path: str) -> Optional[str]:
        stack: List[str] = []
        for key, node_ids in self.current_node.next_condition_nodes.items():
            for node_id in node_ids:
                if condition_path == key:
                    continue
                stack.append(node_id)

        passed = set()
        passed.add(self.current_node.id)

        while len(stack) > 0:
            node_id = stack.pop()
            node = self.node_map.get(node_id, None)
            if node is None:
                return f"node不能对应node{node_id}"

            diffs = set(node.previous_nodes).difference(passed)
            if len(diffs) < 1:
                passed.add(node_id)
                next_node_ids = NodeRouter.geather_next_node_ids(node)
                stack.extend(next_node_ids)
            elif diffs.issubset(self.passed):
                self.add_to_waiting(node_id)

        self.passed.update(passed)

        return None

    def can_next(self, node: ActionNode):
        return set(node.previous_nodes).issubset(self.passed)

    def pass_one(self, condition_key: str) -> Optional[str]:
        self.passed.add(self.current_node.id)
        if condition_key != "":
            node_ids = self.current_node.next_condition_nodes.get(condition_key, None)
            if node_ids is None:
                return f"有多个下一个节点候选，但是传递的状态key确定不了下一个节点，condition_key={condition_key},node={self.current_node.id},next={self.current_node.next_nodes}"
            self.mark_unfit_condition_path(condition_key)
            for node_id in node_ids:
                self.add_to_waiting(node_id)
            return None
        for node_id in self.current_node.next_nodes:
            self.add_to_waiting(node_id)

        return None

    def add_to_waiting(self, node_id: str):
        if not node_id in self.waiting:
            self.waiting.append(node_id)

    def done(self):
        self.current_node = None
        self.is_end = True


_variable_regex = re.compile(r"\$\{(\w+)\}")


def _replace_placeholders(v: Dict[str, Any], p: Dict[str, Any]) -> None:
    def replace_value(value: Any) -> Any:
        """递归处理单个值"""
        if isinstance(value, str):
            # 对字符串进行替换，使用lambda函数处理每个匹配
            return _variable_regex.sub(
                lambda match: str(p.get(match.group(1), match.group(0))), value
            )
        elif isinstance(value, dict):
            # 对子字典递归处理
            for k, v_val in value.items():
                value[k] = replace_value(v_val)
            return value
        elif isinstance(value, list):
            # 对列表中的每个元素递归处理
            return [replace_value(item) for item in value]
        # 其他类型保持不变
        return value

    # 处理顶层字典
    for key in v:
        v[key] = replace_value(v[key])


class AgentExecutor:
    pid: int
    name: str
    get_action: Callable[[str], Action]

    def __init__(self, get_action: Callable[[str], Optional[Action]], name: str = None):
        self.pid = os.getpid()
        if name is None:
            name = f"agent{self.pid}"
        self.name = name
        self.get_action = get_action

    async def action_start(self, action: Action, action_node: ActionNode):
        logger.info(
            f"agent={self.pid} action {action_node.label}-{action_node.id} start "
        )

    async def action_finish(self, action: Action, action_node: ActionNode):
        logger.info(
            f"agent={self.pid} action {action_node.label}-{action_node.id} finish"
        )

    async def execute(self, agent: Agent, execution_id: str = ""):
        if execution_id == "":
            execution_id = generate()

        ctx = ExecutionContext(agent.task_id, execution_id, agent.variables)
        ctx.start()
        router = NodeRouter(agent.node_map)
        err = router.start()
        if err is not None:
            ctx.failed(err)
        while not router.is_end:
            if ctx.state != ExecutionState.RUNNING:
                break

            current = router.current_node
            action = self.get_action(current.componentType)
            if action is None:
                ctx.failed(f"无效的动作类型{current.type}")
                return
            ctx.next(current)
            _replace_placeholders(current.config, ctx.variables)
            await self.action_start(action, current)
            # XXX 这里需要asyncio.to_thread
            action.run(ctx, current.config, current.options)
            await self.action_finish(action, current)
            condition_key = ctx.current.condition_key
            err = router.next(condition_key)
            if err is not None:
                ctx.failed(f"流程错误 {err}")
                break

        match ctx.state:
            case ExecutionState.RUNNING:
                ctx.success()
            case (
                ExecutionState.SUCCESS
                | ExecutionState.FAILED
                | ExecutionState.CANCELLED
            ):
                pass
            case _:
                ctx.failed(f"agent执行状态为未知{ctx.state}")
