<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon2.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微任务-让重复工作自动化 ，让复杂工作协同化 ，让执行工作智能化</title>
    <style>
        /* 加载动画 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            //background: linear-gradient(135deg, #778eec 0%, #895bba 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            background: white;
        }

        /* 全屏视频样式 */
        #loading #fullscreen-video {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            object-fit: contain;
            z-index: -1;
            transform: translate(-50%, -50%);
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            margin-bottom: 24px;
            //background: rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;

        }
        .loading-logo img {
            animation: pulse 2s infinite;
        }

        .loading-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            position: absolute;
            top: calc(50% + 120px);
            transform: translateY(-50%)
        }

        .loading-subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 32px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 0, 0, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 隐藏加载动画 */
        .loading-hidden {
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
        }

        .copyright{
            position: absolute;
            bottom: 32px;
            font-size: 12px;
            color: #333;
        }
    </style>
</head>
<body>
    
    
<!-- 加载动画 -->
<div id="loading">
    <!-- 添加全屏视频背景 -->
    <video id="fullscreen-video" src="./src/assets/login_4s.mp4" autoplay muted loop playsinline>
    </video>

    <div class="loading-title"><img
                    style="width: 100px;height: 20px;"
                    src="./src/assets/images/admin/logo.png"
                    alt="WimTask logo"
                /></div>
<!--    <div class="loading-subtitle"></div>-->
<!--    <div class="loading-spinner"></div>-->

    <div class="copyright">Copyright ©浙江和达科技股份有限公司</div>
</div>
<div id="app">
</div>
<script type="module" src="/src/main.ts"></script>

<script>
    let hash = window.location.hash;
    if(hash===''||hash==='#/loading'){
        const loading = document.getElementById('loading');
        loading.classList.add('loading-hidden');
        loading.style.display = 'none';
        loading.remove();

    }
    // 应用加载完成后隐藏加载动画
    window.addEventListener('load', () => {
        // const loading = document.getElementById('loading');
        // if (loading) {
        //     setTimeout(() => {
        //         loading.classList.add('loading-hidden');
        //     },10)
        //     setTimeout(() => {
        //         loading.style.display = 'none';
        //         loading.remove();
        //     // }, 3000);
        //     }, 30);
        // }
    });

    // 错误处理
    window.addEventListener('error', (event) => {
        console.error('应用加载错误:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
        console.error('未处理的Promise拒绝:', event.reason);
    });
</script>
</body>
</html>
