/**
 * 配置Schema注册器
 * 统一管理所有组件的配置Schema
 */

import { configSchemaManager } from '@/utils/configSchema'
import {
  conditionSchema,
  forLoopSchema,
} from './schemas/judgecycleSchemas'
import{
  waitSchema,
} from './schemas/waitSchemas'
import {
  // 新的Playwright组件
  newBrowserSchema,
  newPageSchema,
  clickSchema,
  fillTextSchema,
  // 保留的旧组件（兼容性）
  openBrowserSchema,
  clickElementSchema,
  inputTextSchema,
  getTextSchema,
  // 新增的完整网页自动化组件
  navigateToSchema,
  hoverElementSchema,
  selectOptionSchema,
  getAttributeSchema,
  waitForElementSchema,
  scrollToElementSchema,
  checkboxSchema,
  uploadFileSchema,
  switchFrameSchema,
  handleAlertSchema,
  closeBrowserSchema,
  screenshotSchema,
  scrollPageSchema,
  sendKeysSchema,
} from './schemas/browserSchemas'
import {
  setVariableSchema,
  logMessageSchema,
  userInputSchema,
  userChoiceSchema,
  userConfirmSchema,
  notifierSendSchema
} from './schemas/controlSchemas'
import {
  excelCreateSchema,
  excelOpenSchema,
  excelReadSchema,
  excelWriteSchema,
  pdfCreateSchema,
  wordCreateSchema,
  markdownSaveSchema,
  textFileSaveSchema,
} from './schemas/fileSchemas'
import { desktopClickSchema, desktopTypeSchema } from './schemas/desktopSchemas'
import {
  // 保留的旧组件（兼容性）
  httpGetSchema,
  httpPostSchema,
  httpRequestSchema
} from './schemas/httpSchemas'
import { weatherQuerySchema } from './schemas/otherSchemas'
import {
  runCommandSchema,
  processListSchema,
  pythonExecuteSchema,
  pythonEvaluateSchema,
  pythonImportSchema,
  javascriptExecuteSchema,
} from './schemas/systemSchemas'
import {
  dbConnectSchema,
  dbQuerySchema,
  dbExecuteSchema,
} from './schemas/databaseSchemas'

import {
  workflowStartSchema,
  workflowEndSchema,
} from './schemas/workflowSchemas'

import {
  aiAnalysisSchema,
  imgReconitionSchema,
  invoiceReconitionSchema,
  textToSpeechSchema
} from './schemas/agentAiSchemas'
import {
  monitorDataSchema,
  dataForecastSchema,
  limitIntervalSchema,
  dataWashSchema,
  waterShutoffValveSchema,
} from './schemas/businessSchemas.ts'
import {
  variableAssignmentSchema,
  textTemplateSchema,
  addTimeSchema
} from './schemas/dataProcessSchemas.ts'
import {
  startWorkflowSchema,
} from "./schemas/processSchemas.ts"

/**
 * 注册所有组件的配置Schema
 */
export function registerAllSchemas() {
  // 判断/循环组件
  configSchemaManager.registerSchema('condition', conditionSchema)
  configSchemaManager.registerSchema('for_loop', forLoopSchema)

  // 等待组件
  configSchemaManager.registerSchema('wait', waitSchema)

  // Playwright 浏览器组件（新）
  configSchemaManager.registerSchema('new_browser', newBrowserSchema)
  configSchemaManager.registerSchema('new_page', newPageSchema)
  configSchemaManager.registerSchema('click', clickSchema)
  configSchemaManager.registerSchema('fill_text', fillTextSchema)

  // 浏览器相关组件（兼容性保留）
  configSchemaManager.registerSchema('open_browser', openBrowserSchema)
  configSchemaManager.registerSchema('click_element', clickElementSchema)
  configSchemaManager.registerSchema('input_text', inputTextSchema)
  configSchemaManager.registerSchema('get_text', getTextSchema)

  // 完整的网页自动化组件
  configSchemaManager.registerSchema('navigate_to', navigateToSchema)
  configSchemaManager.registerSchema('hover_element', hoverElementSchema)
  configSchemaManager.registerSchema('select_option', selectOptionSchema)
  configSchemaManager.registerSchema('get_attribute', getAttributeSchema)
  configSchemaManager.registerSchema('wait_for_element', waitForElementSchema)
  configSchemaManager.registerSchema('scroll_to_element', scrollToElementSchema)
  configSchemaManager.registerSchema('check_checkbox', checkboxSchema)
  configSchemaManager.registerSchema('upload_file', uploadFileSchema)
  configSchemaManager.registerSchema('switch_frame', switchFrameSchema)
  configSchemaManager.registerSchema('handle_alert', handleAlertSchema)
  configSchemaManager.registerSchema('close_browser', closeBrowserSchema)
  configSchemaManager.registerSchema('take_screenshot', screenshotSchema)
  configSchemaManager.registerSchema('scroll_page', scrollPageSchema)
  configSchemaManager.registerSchema('send_keys', sendKeysSchema)

  // 控制流组件
  configSchemaManager.registerSchema('workflow_start', workflowStartSchema)
  configSchemaManager.registerSchema('workflow_end', workflowEndSchema)
  configSchemaManager.registerSchema('set_variable', setVariableSchema)
  configSchemaManager.registerSchema('log_message', logMessageSchema)
  configSchemaManager.registerSchema('user_input', userInputSchema)
  configSchemaManager.registerSchema('user_choice', userChoiceSchema)
  configSchemaManager.registerSchema('user_confirm', userConfirmSchema)
  configSchemaManager.registerSchema('notifier_send', notifierSendSchema)

  // 文件操作组件
  configSchemaManager.registerSchema('excel_create', excelCreateSchema)
  configSchemaManager.registerSchema('excel_open', excelOpenSchema)
  configSchemaManager.registerSchema('excel_read', excelReadSchema)
  configSchemaManager.registerSchema('excel_write', excelWriteSchema)
  configSchemaManager.registerSchema('pdf_create', pdfCreateSchema)
  configSchemaManager.registerSchema('word_create', wordCreateSchema)
  configSchemaManager.registerSchema('markdown_save', markdownSaveSchema)
  configSchemaManager.registerSchema('text_file_save', textFileSaveSchema)

  // 桌面操作组件
  configSchemaManager.registerSchema('desktop_click', desktopClickSchema)
  configSchemaManager.registerSchema('desktop_type', desktopTypeSchema)

  // HTTP/API组件
  configSchemaManager.registerSchema('http_get', httpGetSchema)
  configSchemaManager.registerSchema('http_post', httpPostSchema)
  configSchemaManager.registerSchema('http_request', httpRequestSchema)

  // 系统操作组件
  configSchemaManager.registerSchema('run_command', runCommandSchema)
  configSchemaManager.registerSchema('process_list', processListSchema)
  configSchemaManager.registerSchema('python_execute', pythonExecuteSchema)
  configSchemaManager.registerSchema('python_evaluate', pythonEvaluateSchema)
  configSchemaManager.registerSchema('python_import', pythonImportSchema)
  configSchemaManager.registerSchema('javascript_execute', javascriptExecuteSchema)

  // 数据库操作组件
  configSchemaManager.registerSchema('db_connect', dbConnectSchema)
  configSchemaManager.registerSchema('db_query', dbQuerySchema)
  configSchemaManager.registerSchema('db_execute', dbExecuteSchema)

  // 人工智能AI
  configSchemaManager.registerSchema('ai_analyze', aiAnalysisSchema)
  configSchemaManager.registerSchema('text_to_speech', textToSpeechSchema)
  configSchemaManager.registerSchema('img_recognition', imgReconitionSchema)
  configSchemaManager.registerSchema('invoice_recognition', invoiceReconitionSchema)

  // 变量赋值
  configSchemaManager.registerSchema('variable_assignment', variableAssignmentSchema)
  // 文本模板
  configSchemaManager.registerSchema('text_template', textTemplateSchema)
  //增加、减少时间
  configSchemaManager.registerSchema('add_time', addTimeSchema)


  // 信息模型相关
  configSchemaManager.registerSchema('monitor_data', monitorDataSchema)
  configSchemaManager.registerSchema('data_forecast', dataForecastSchema)
  configSchemaManager.registerSchema('limit_interval', limitIntervalSchema)
  configSchemaManager.registerSchema('data_wash', dataWashSchema)
  configSchemaManager.registerSchema('water_shutoff_valve', waterShutoffValveSchema)

  // 流程相关
  configSchemaManager.registerSchema('start_workflow', startWorkflowSchema)

  //其他
  configSchemaManager.registerSchema('weather_query', weatherQuerySchema)


  console.log('已注册配置Schema:', configSchemaManager.getRegisteredTypes())
}

/**
 * 获取组件的配置Schema
 */
export function getComponentSchema(componentType: string) {
  return configSchemaManager.getSchema(componentType)
}

/**
 * 验证组件配置
 */
export function validateComponentConfig(componentType: string, config: any, context?: any) {
  return configSchemaManager.validateConfig(componentType, config, context)
}

/**
 * 获取组件的默认配置
 */
export function getDefaultConfig(componentType: string) {
  return configSchemaManager.getDefaultValues(componentType)
}

/**
 * 应用预设配置
 */
export function applyPresetConfig(componentType: string, presetName: string, currentConfig: any) {
  return configSchemaManager.applyPreset(componentType, presetName, currentConfig)
}

/**
 * 检查字段是否应该显示
 */
export function shouldShowField(componentType: string, fieldName: string, currentConfig: any) {
  const schema = configSchemaManager.getSchema(componentType)
  if (!schema || !schema.fields[fieldName]) {
    return true
  }

  return configSchemaManager.shouldShowField(schema.fields[fieldName], currentConfig)
}

/**
 * 获取字段的验证错误
 */
export function getFieldValidationErrors(
  componentType: string,
  fieldName: string,
  value: object,
  allValues: object,
  isChildren: boolean
) {
  const result = configSchemaManager.validateConfig(componentType, {
    ...allValues,
    [fieldName]: value,
  }, null, isChildren)
  return result.errors.filter((error) => error.field === fieldName)
}

/**
 * 获取组件的所有预设
 */
export function getComponentPresets(componentType: string) {
  const schema = configSchemaManager.getSchema(componentType)
  return schema?.presets || {}
}



/**
 * 获取组件的配置组
 */
export function getComponentGroups(componentType: string) {
  const schema = configSchemaManager.getSchema(componentType)
  return schema?.groups || []
}

/**
 * 按组分类字段
 */
export function getFieldsByGroup(componentType: string) {
  const schema = configSchemaManager.getSchema(componentType)
  if (!schema) {
    return {}
  }

  const groups: Record<string, any[]> = {}

  // 初始化组
  if (schema.groups) {
    schema.groups.forEach((group) => {
      groups[group.id] = []
    })
  }

  // 添加默认组
  if (!groups.basic) {
    groups.basic = []
  }

  // 分类字段
  Object.entries(schema.fields).forEach(([fieldName, fieldDef]) => {
    const groupId = fieldDef.group || 'basic'
    if (!groups[groupId]) {
      groups[groupId] = []
    }
    groups[groupId].push({
      name: fieldName,
      definition: fieldDef,
    })
  })

  // 按order排序
  Object.keys(groups).forEach((groupId) => {
    groups[groupId].sort((a, b) => (a.definition.order || 999) - (b.definition.order || 999))
  })

  return groups
}

/**
 * 获取字段的完整信息
 */
export function getFieldInfo(componentType: string, fieldName: string) {
  const schema = configSchemaManager.getSchema(componentType)
  if (!schema || !schema.fields[fieldName]) {
    return null
  }

  return {
    name: fieldName,
    definition: schema.fields[fieldName],
    componentType,
  }
}

/**
 * 检查组件类型是否已注册
 */
export function isComponentRegistered(componentType: string) {
  return configSchemaManager.getRegisteredTypes().includes(componentType)
}

/**
 * 获取所有已注册的组件类型
 */
export function getAllRegisteredComponents() {
  return configSchemaManager.getRegisteredTypes()
}
