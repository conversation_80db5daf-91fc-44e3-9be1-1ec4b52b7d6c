import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface LogEntry {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success' | 'debug'
  message: string
  details?: string
  source?: string
}

export const useLogStore = defineStore('log', () => {
  // 状态
  const logs = ref<LogEntry[]>([])
  const maxLogs = ref(1000) // 最大日志条数
  const autoScroll = ref(true) // 自动滚动到底部
  const filterLevel = ref<string>('all') // 日志过滤级别

  // 计算属性
  const filteredLogs = computed(() => {
    if (filterLevel.value === 'all') {
      return logs.value
    }
    return logs.value.filter((log) => log.level === filterLevel.value)
  })

  const logCounts = computed(() => {
    const counts = {
      total: logs.value.length,
      info: 0,
      warning: 0,
      error: 0,
      success: 0,
      debug: 0,
    }

    logs.value.forEach((log) => {
      counts[log.level]++
    })

    return counts
  })

  const hasErrors = computed(() => logCounts.value.error > 0)
  const hasWarnings = computed(() => logCounts.value.warning > 0)

  // 方法
  const addLog = (level: LogEntry['level'], message: string, details?: string, source?: string) => {
    const logEntry: LogEntry = {
      id: generateLogId(),
      timestamp: new Date().toISOString(),
      level,
      message,
      details,
      source,
    }

    logs.value.push(logEntry)

    // 限制日志数量
    if (logs.value.length > maxLogs.value) {
      logs.value.splice(0, logs.value.length - maxLogs.value)
    }

    return logEntry
  }

  const addInfo = (message: string, details?: string, source?: string) => {
    return addLog('info', message, details, source)
  }

  const addWarning = (message: string, details?: string, source?: string) => {
    return addLog('warning', message, details, source)
  }

  const addError = (message: string, details?: string, source?: string) => {
    return addLog('error', message, details, source)
  }

  const addSuccess = (message: string, details?: string, source?: string) => {
    return addLog('success', message, details, source)
  }

  const addDebug = (message: string, details?: string, source?: string) => {
    return addLog('debug', message, details, source)
  }

  const removeLog = (logId: string) => {
    const index = logs.value.findIndex((log) => log.id === logId)
    if (index > -1) {
      logs.value.splice(index, 1)
    }
  }

  const clearLogs = () => {
    logs.value = []
  }

  const clearLogsByLevel = (level: LogEntry['level']) => {
    logs.value = logs.value.filter((log) => log.level !== level)
  }

  const setFilterLevel = (level: string) => {
    filterLevel.value = level
  }

  const setAutoScroll = (enabled: boolean) => {
    autoScroll.value = enabled
  }

  const setMaxLogs = (max: number) => {
    maxLogs.value = Math.max(100, Math.min(10000, max))

    // 如果当前日志数超过新的限制，删除旧的日志
    if (logs.value.length > maxLogs.value) {
      logs.value.splice(0, logs.value.length - maxLogs.value)
    }
  }

  const exportLogs = (format: 'json' | 'txt' = 'json') => {
    if (format === 'json') {
      return JSON.stringify(logs.value, null, 2)
    } else {
      return logs.value
        .map(
          (log) =>
            `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}${
              log.details ? `\n  Details: ${log.details}` : ''
            }${log.source ? `\n  Source: ${log.source}` : ''}`,
        )
        .join('\n\n')
    }
  }

  const importLogs = (data: string, format: 'json' | 'txt' = 'json') => {
    try {
      if (format === 'json') {
        const importedLogs = JSON.parse(data) as LogEntry[]
        if (Array.isArray(importedLogs)) {
          logs.value = [...logs.value, ...importedLogs]

          // 应用日志数量限制
          if (logs.value.length > maxLogs.value) {
            logs.value.splice(0, logs.value.length - maxLogs.value)
          }

          return true
        }
      }
      // TODO: 实现txt格式的导入
      return false
    } catch (error) {
      console.error('Failed to import logs:', error)
      return false
    }
  }

  const searchLogs = (query: string) => {
    if (!query.trim()) {
      return filteredLogs.value
    }

    const searchTerm = query.toLowerCase()
    return filteredLogs.value.filter(
      (log) =>
        log.message.toLowerCase().includes(searchTerm) ||
        log.details?.toLowerCase().includes(searchTerm) ||
        log.source?.toLowerCase().includes(searchTerm),
    )
  }

  // 工具函数
  const generateLogId = () => {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3,
    })
  }

  const getLevelColor = (level: LogEntry['level']) => {
    const colors = {
      info: '#409eff',
      warning: '#e6a23c',
      error: '#f56c6c',
      success: '#67c23a',
      debug: '#909399',
    }
    return colors[level] || colors.info
  }

  const getLevelIcon = (level: LogEntry['level']) => {
    const icons = {
      info: 'InfoFilled',
      warning: 'WarningFilled',
      error: 'CircleCloseFilled',
      success: 'CircleCheckFilled',
      debug: 'Tools',
    }
    return icons[level] || icons.info
  }

  return {
    // 状态
    logs,
    maxLogs,
    autoScroll,
    filterLevel,

    // 计算属性
    filteredLogs,
    logCounts,
    hasErrors,
    hasWarnings,

    // 方法
    addLog,
    addInfo,
    addWarning,
    addError,
    addSuccess,
    addDebug,
    removeLog,
    clearLogs,
    clearLogsByLevel,
    setFilterLevel,
    setAutoScroll,
    setMaxLogs,
    exportLogs,
    importLogs,
    searchLogs,

    // 工具函数
    formatTimestamp,
    getLevelColor,
    getLevelIcon,
  }
})
