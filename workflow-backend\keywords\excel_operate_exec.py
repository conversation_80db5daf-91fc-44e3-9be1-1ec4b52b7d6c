
import json
import logging
import os
import io
import re
import ast
import msoffcrypto
from markdown import markdown
from bs4 import BeautifulSoup
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from pygame.examples.eventlist import last_key
from robot.libraries.BuiltIn import BuiltIn
from openpyxl import Workbook
from utils.wimtask_server_api import get_online_template
from utils.file_util import generate_filename_with_extend_format
from utils.file_util import open_folder
import copy

import traceback

# 设置文件扩展名称
def set_file_extension():
    # 获取file_name
    file_name = BuiltIn().get_variable_value("${file_name}")
    new_file_name = generate_filename_with_extend_format(file_name)
    # 替换掉file_name
    BuiltIn().set_suite_variable("${file_name}", new_file_name)





def markdown_to_text(md_text: str) -> str:
	"""
	将 Markdown 文本转换为结构清晰但紧凑的纯文本。
	"""
	# 将 Markdown 转为 HTML
	html = markdown(md_text, output_format="html")
	# 解析 HTML
	soup = BeautifulSoup(html, "html.parser")

	# 控制哪些标签换行
	lines = []
	for element in soup.descendants:
		if element.name in ["h1", "h2", "h3", "h4", "h5", "h6", "p", "li", "pre", "blockquote"]:
			text = element.get_text(strip=True)
			if text:
				lines.append(text)

	return "\n".join(lines).strip()



#  todo 这个文件不要删除 没写完的代码，还要继续处理
def create_excel_with_report_template(file_path, template_id, response_variable="", sheet_name="Sheet1"):
    """
    使用报表模板创建Excel文件

    Args:
        file_path: Excel文件保存路径
        template_id: 报表模板ID
        response_variable: 响应数据变量名
        sheet_name: 工作表名称
    """
    import json
    import os
    from pathlib import Path

    try:
        # 使用Robot Framework的Log函数
        from robot.libraries.BuiltIn import BuiltIn
        builtin = BuiltIn()

        builtin.log("=" * 80, "INFO")
        builtin.log("🚀 CREATE_EXCEL_WITH_REPORT_TEMPLATE 函数被调用!", "INFO")
        builtin.log(f"📁 原始文件路径: {file_path}", "INFO")

        # 确保文件路径有正确的扩展名
        if not file_path.lower().endswith(('.xlsx', '.xls')):
            file_path = file_path + '.xlsx'
            builtin.log(f"📁 添加扩展名后: {file_path}", "INFO")

        builtin.log(f"📋 模板ID: {template_id}", "INFO")
        builtin.log(f"📊 响应变量: {response_variable}", "INFO")
        builtin.log(f"📄 工作表名: {sheet_name}", "INFO")
        builtin.log("=" * 80, "INFO")
        # 如果文件已存在，加载一个原表的originWorkbook
        originWorkbook =  None
        originWorksheet =  None
        if os.path.exists(file_path):
            originWorkbook = load_workbook(file_path)
            # 读取对应的工作表
            originWorksheet = originWorkbook[sheet_name]
        # 创建新的工作簿
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = sheet_name

        template_data = get_online_template(template_id)

        if template_data and template_data["Response"]:
            template_data = json.loads(template_data["Response"]["config"])
        else:
            # 加载报表模板 - 尝试多个可能的路径
            possible_paths = [
                Path("workflow-backend/data/reports/templates") / f"{template_id}.json",
                Path("data/reports/templates") / f"{template_id}.json",
                Path("../data/reports/templates") / f"{template_id}.json",
                Path("./workflow-backend/data/reports/templates") / f"{template_id}.json"
            ]

            template_file = None
            for path in possible_paths:
                if path.exists():
                    template_file = path
                    break

            if not template_file:
                # 打印调试信息
                current_dir = Path.cwd()
                print(f"当前工作目录: {current_dir}")
                print(f"尝试的路径:")
                for path in possible_paths:
                    print(f"  - {path.absolute()} (存在: {path.exists()})")

                print(f"无法查询到线下模板，从线上模板获取数据")

                raise Exception(f"报表模板不存在: {template_id}")

            with open(template_file, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
        # 应用模板样式
        apply_template_to_worksheet(worksheet, template_data)
        # 从用户选择的工作流变量中获取数据
        builtin.log("🚀 从用户选择的工作流变量中获取数据...", "INFO")
        builtin.log(f"📊 用户选择的变量: {response_variable}", "INFO")

        http_response_data = None

        # 如果用户选择了工作流变量
        if response_variable and response_variable.strip():
            # 确保变量名格式正确
            if not response_variable.startswith('${'):
                response_variable = f"${{{response_variable}}}"

            builtin.log(f"🎯 从工作流变量 {response_variable} 获取数据", "INFO")
            http_response_data = get_response_data_from_variable(builtin, response_variable)


            # 2. 如果未获取到数据，尝试常见变量名
            if not http_response_data:
                common_vars = ["response_content", "api_response", "http_response", "response"]
                for var_name in common_vars:
                    http_response_data = get_response_data_from_variable(builtin, f"${{{var_name}}}")
                    if http_response_data:
                        break

        if not http_response_data:
            builtin.log("❌ 无法获取HTTP响应数据", "ERROR")
            return workbook

        # 构建变量映射，直接使用Robot Framework已经设置的变量值
        variable_mapping = {}
        try:
            # 获取所有工作流变量
            all_variables = builtin.get_variables()

            # 处理所有工作流变量
            for var_name, var_value in all_variables.items():
                # 移除变量名的装饰符（${} 等）
                clean_name = var_name.replace('${', '').replace('}', '')

                # 跳过系统变量和内部变量
                if clean_name.startswith('_'):
                    continue

                # 所有变量直接使用其值
                variable_mapping[clean_name] = var_value

        except Exception as e:
            builtin.log(f"获取工作流变量失败: {e}", "ERROR")

        # 首先处理模板的固定文本，现在支持变量替换
        template_workbook_data = template_data.get('data', {})
        if template_workbook_data:
            builtin.log("📝 处理模板固定文本（支持变量替换）...", "INFO")
            apply_template_fixed_text(worksheet, template_workbook_data, variable_mapping)

        # 获取模板的绑定信息
        bindings = template_data.get('bindings', {})
        builtin.log(f"📋 处理 {len(bindings)} 个绑定", "INFO")
        # 定义一个列表，存储已经赋值过的单元格用行-列号的形式存储，行列拼接方式： 行-列号
        applied_cells = []
        # 直接处理绑定，不使用变量映射
        for cell_address, binding in bindings.items():
            try:
                # 解析单元格地址 (例如: A1 -> (1, 1), B2 -> (2, 2))
                import re
                binding['cell_address'] = cell_address
                match = re.match(r'([A-Z]+)(\d+)', cell_address)
                if match:
                    col_letters = match.group(1)
                    row_num = int(match.group(2))

                    # 将列字母转换为数字 (A=1, B=2, ...)
                    col_num = 0
                    for char in col_letters:
                        col_num = col_num * 26 + (ord(char) - ord('A') + 1)

                    row, col = row_num, col_num
                else:
                    builtin.log(f"❌ 无法解析单元格地址: {cell_address}", "ERROR")
                    continue

                if binding.get('type') == 'loop':
                    # 处理循环绑定
                    process_loop_binding_direct(
                        worksheet=worksheet,
                        originWorksheet=originWorksheet,
                        binding=binding,
                        http_response_data=http_response_data,
                        start_row=row,
                        start_col=col,
                        applied_cells = applied_cells
                    )
                else:
                    # 处理普通绑定
                    value = get_field_value_direct(binding.get('variable', ''), http_response_data)

                    # 应用条件表达式（如果有）
                    if binding.get('conditionalExpression'):
                        value = apply_conditional_expression(value, binding.get('conditionalExpression'))

                    # 检查显示条件
                    if binding.get('displayConditions'):
                        if not display_condition_expression(None, http_response_data, binding.get('displayConditions')):
                            value = ""
                    # 判断行列是否已经赋值过，如果已经赋值过，则跳过
                    position_val = str(row)+'-'+str(col)
                    if value and position_val not in applied_cells:
                        worksheet.cell(row=row, column=col, value=value)
                        applied_cells.append(position_val)
                        builtin.log(f"📝 设置单元格 {cell_address}: {value}", "INFO")


            except Exception as e:
                builtin.log(f"❌ 处理绑定 {cell_address} 失败: {e}", "ERROR")



        # 保存文件
        workbook.save(file_path)
        # 提取目录路径并打开资源管理器
        try:
            directory = os.path.dirname(file_path)
            open_folder(directory)
        except:
            pass

        return workbook

    except Exception as e:
        raise Exception(f"使用报表模板创建Excel失败: {str(e)}")



def get_response_data_from_variable(builtin, response_variable):
    """
        从工作流变量获取响应数据的通用方法
    """
    try:
        response_var = builtin.get_variable_value(response_variable)

        if not response_var:
            return None

        # 处理各种可能的变量格式
        if isinstance(response_var, dict):
            # 已经是字典格式
            return response_var

        elif isinstance(response_var, str):
            # 尝试解析JSON字符串
            try:
                return json.loads(response_var)
            except json.JSONDecodeError:
                # 可能是其他格式字符串，直接返回
                return {'data': response_var}

        elif hasattr(response_var, '__dict__'):
            # 处理对象类型，转换为字典
            return vars(response_var)

        else:
            # 其他类型(列表、数字等)包装成字典
            return {'value': response_var}

    except Exception as e:
        builtin.log(f"获取变量{response_variable}失败: {str(e)}", "ERROR")
        return None

def find_cell_by_address(ws, cell_address):
    """
    根据单元格地址（例如 'B2'），直接返回对应单元格对象
    如果地址不合法或单元格不存在，返回 None
    """
    try:
        cell = ws[cell_address]
        return cell
    except KeyError:
        # 地址错误或超出范围
        return None

def process_loop_binding_direct(worksheet, originWorksheet, binding, http_response_data, start_row, start_col,
                                applied_cells):
    """统一处理循环绑定的主入口，支持单层和多层嵌套数据"""
    from robot.libraries.BuiltIn import BuiltIn
    builtin = BuiltIn()

    try:
        # 1. 参数校验
        if not binding or not http_response_data:
            builtin.log("❌ 无效参数: binding或http_response_data为空", "ERROR")
            return

        variable_name = binding.get('variable', '')
        # 模板单元格位置
        cell_address = binding.get('cell_address', '')
        loop_config = binding.get('loopConfig', {})
        array_field = loop_config.get('arrayField', '')
        direction = loop_config.get('direction', 'vertical')
        actual_field = loop_config.get('actualField', '')

        # 2. 增强嵌套路径检测逻辑
        is_nested = False
        nested_parts = []

        # 最多支持两层嵌套，即最多有两个[].出现，检查标准嵌套格式 (如 Response[].vals[].strValue)
        # 判断[].出现的次数，最多两次
        if '[].' in variable_name and variable_name.count('[].') < 3:
            is_nested = True
            nested_parts = array_field.split('.')
        else:
            builtin.log("❌ 无效参数: variable_name不是数组类型，或数组层数超过2层", "ERROR")
            return
        if is_nested and len(nested_parts) > 1:
            if isinstance(http_response_data, dict):
                builtin.log(f"  🎯 嵌套字段: {array_field}", "INFO")
                builtin.log(f"  📊 首层数据数量: {len(http_response_data)}", "INFO")
                http_response_data = get_nested_data_direct(nested_parts[0], http_response_data)
                if isinstance(http_response_data, list):
                    return process_multi_loop_binding_direct(worksheet, originWorksheet, nested_parts, actual_field,
                                                             applied_cells, direction, binding, http_response_data,
                                                             start_row, start_col, builtin)
                else:
                    # 下层数据是字典类型，字典中某属性是数组的处理 nested_parts截取索引1到最后的数据
                    nested_parts = nested_parts[1:]
                    http_response_data = get_nested_data_direct(nested_parts[0], http_response_data)
                    if isinstance(http_response_data, list):
                        return process_multi_loop_binding_direct(worksheet, originWorksheet, nested_parts, actual_field,
                                                                 applied_cells, direction, binding, http_response_data,
                                                                 start_row, start_col, builtin)
                    else:
                        builtin.log("❌ 无效参数: variable_name不是数组类型", "ERROR")
                        return
            else:
                # todo 返回结果不标准，只有数组，没有外层code、respopnse之类的情况,还没想好怎么做
                builtin.log("❌ 无效参数: 不是标准的响应类型，暂不支持", "ERROR")
                return


        else:
            # 处理单层数据
            builtin.log("🔄 处理单层循环数据", "INFO")
            array_data = get_nested_data_direct(array_field, http_response_data)

            if not array_data:
                builtin.log(f"⚠️ 获取数组数据为空: {array_field}", "WARN")
                return

            # 原有单层数据处理逻辑...
            for i, item in enumerate(array_data):
                # 计算目标位置
                if direction == 'horizontal':
                    current_row, current_col = start_row, start_col + i
                else:
                    current_row, current_col = start_row + i, start_col

                # 检查合并单元格
                if is_merged_cell(worksheet, worksheet.cell(current_row, current_col)):
                    continue

                # 获取值并处理
                if actual_field == "__index__":
                    value = i + 1
                else:
                    value = str(item.get(actual_field, "")) if isinstance(item, dict) else str(item)

                # 应用条件表达式
                if binding.get('conditionalExpression'):
                    value = apply_conditional_expression(value, binding.get('conditionalExpression'))

                # 检查显示条件
                if binding.get('displayConditions'):
                    if not display_condition_expression(None, item, binding.get('displayConditions')):
                        value = ""

                # 保留原值
                if originWorksheet and originWorksheet.cell(current_row, current_col).value:
                    value = originWorksheet.cell(current_row, current_col).value

                # 写入单元格
                position_key = f"{current_row}-{current_col}"
                if value and position_key not in applied_cells:
                    target_cell = worksheet.cell(current_row, current_col, value=value)

                    try:
                        template_cell = find_cell_by_address(worksheet, cell_address)
                        if template_cell.has_style:
                            target_cell.font = copy.copy(template_cell.font)
                            target_cell.border = copy.copy(template_cell.border)
                            target_cell.fill = copy.copy(template_cell.fill)
                            target_cell.number_format = template_cell.number_format
                            target_cell.protection = copy.copy(template_cell.protection)
                            target_cell.alignment = copy.copy(template_cell.alignment)
                    except Exception as e:
                        builtin.log(f"样式复制失败: {e}", "WARN")


                    applied_cells.append(position_key)

    except Exception as e:
        builtin.log(f"❌ 循环绑定处理失败: {str(e)}\n{traceback.format_exc()}", "ERROR")

# 处理多层嵌套数据渲染
def process_multi_loop_binding_direct(worksheet, originWorksheet, nested_parts,actual_field,applied_cells,direction,binding, http_response_data, start_row, start_col,builtin):
    # 获取第二层数组的字段
    last_field = nested_parts[1]
    # 开始循环处理值
    for first_index, first_data in enumerate(http_response_data):
        if not isinstance(first_data, dict) or last_field not in first_data:
            builtin.log(f"  ⚠️ 首层数据[{first_index}]没有{last_field}字段", "INFO")
            continue
        # 二层数组处理
        last_array = first_data[last_field]
        if not isinstance(last_array, list):
            builtin.log(f"  ⚠️ 首层数据[{first_index}]的{last_field}不是数组", "INFO")
            continue

        builtin.log(f"  📊 首层数据[{first_index}]有{len(last_array)}个{last_field}项", "INFO")

        # 为当前设备的嵌套数组生成数据
        for last_index, last_data in enumerate(last_array):
            if direction == 'horizontal':
                # 水平方向：每个数据一行，时间点展开为列
                current_row = start_row
                current_col = start_col + last_index
            else:
                # 垂直方向：每个时间点一行，设备展开为列
                current_row = start_row + last_index
                current_col = start_col + first_index
            # 检查目标单元格是否在合并区域内
            target_cell = worksheet.cell(row=current_row, column=current_col)
            if is_merged_cell(worksheet, target_cell):
                builtin.log(f"⚠️ 跳过合并单元格 ({current_row}, {current_col})", "ERROR")
                continue
            # 获取字段值
            if isinstance(last_data, dict) and actual_field in last_data:
                value = str(last_data[actual_field])
            else:
                value = ""

            # 应用条件表达式（如果有）
            if binding.get('conditionalExpression'):
                original_value = value
                value = apply_conditional_expression(value, binding.get('conditionalExpression'))
                builtin.log(
                    f"🔄 条件表达式处理: {original_value} -> {value} (表达式: {binding.get('conditionalExpression')})",
                    "INFO")

            # 显示条件表达式（如果有）
            if binding.get('displayConditions'):
                validResult = display_condition_expression(first_data, last_data,
                                                           binding.get('displayConditions'))
                if not validResult:
                    value = ""  # 没有对应字段时留空
                builtin.log(
                    f"🔄 显示条件表达式处理:  (表达式: {binding.get('conditionalExpression')}),校验结果{validResult}",
                    "INFO")
            # originWorksheet相同单元格是否有值，有就用原单元格的值
            if originWorksheet and originWorksheet.cell(row=current_row, column=current_col).value:
                value = originWorksheet.cell(row=current_row, column=current_col).value
            position_val = str(current_row) + '-' + str(current_col)
            if value and position_val not in applied_cells:
                # 写入单元格
                worksheet.cell(row=current_row, column=current_col, value=value)
                applied_cells.append(position_val)
                builtin.log(
                    f"  📝 设备[{first_index}]项[{last_index}] ({current_row}, {current_col}): {value}",
                    "Error")

def get_nested_data_direct(array_field, response_data):
    """
    优化后的通用数组数据获取方法
    支持任意深度嵌套路径，如: a.b.c[], x.y[].z[]
    """
    if not array_field or not response_data:
        return []

    try:
        # 拆分路径
        parts = array_field.split('.')
        current_data = response_data

        for part in parts:
            if not current_data:
                return []

            # 处理数组部分 (如 vals[])
            if part.endswith('[]'):
                array_name = part[:-2]

                if isinstance(current_data, list):
                    # 如果是数组，取第一个元素继续解析
                    if len(current_data) > 0:
                        current_data = current_data[0].get(array_name, [])
                    else:
                        return []
                elif isinstance(current_data, dict):
                    current_data = current_data.get(array_name, [])
                else:
                    return []

            else:
                # 处理普通字段
                if isinstance(current_data, list):
                    if len(current_data) > 0:
                        current_data = current_data[0].get(part, [])
                    else:
                        return []
                elif isinstance(current_data, dict):
                    current_data = current_data.get(part, [])
                else:
                    return []

        if not current_data and 'value' in response_data:
            current_data = response_data['value']
        return current_data

    except Exception as e:
        logging.error(f"获取嵌套数据失败: {e}")
        return []
# 检查单元格是否属于合并区域
def is_merged_cell(worksheet, cell):
    """
    检查单元格是否属于合并区域
    """
    for merged_range in worksheet.merged_cells.ranges:
        if cell.coordinate in merged_range:
            return True
    return False


def apply_conditional_expression(value, expression):
    """
    应用条件表达式

    Args:
        value: 原始值
        expression: 条件表达式，支持JSON格式的多条件或旧的三元运算符格式

    Returns:
        转换后的值
    """
    try:
        import json
        import re

        # 尝试解析JSON格式的多条件表达式
        try:
            parsed = json.loads(expression)
            if parsed.get('type') in ['multi_condition', 'case_when']:
                return apply_multi_condition(value, parsed)
        except (json.JSONDecodeError, TypeError):
            pass

        # 解析旧的三元运算符格式: condition ? true_value : false_value
        ternary_match = re.match(r'(.+?)\s*\?\s*(.+?)\s*:\s*(.+)', expression.strip())

        if ternary_match:
            condition, true_value, false_value = ternary_match.groups()

            # 替换条件中的 value 为实际值
            condition = condition.replace('value', str(value)).strip()

            # 处理返回值，去除引号
            def process_value(val):
                val = val.strip()
                if (val.startswith('"') and val.endswith('"')) or (val.startswith("'") and val.endswith("'")):
                    return val[1:-1]
                return val

            # 简单的条件评估
            result = evaluate_simple_condition(condition)
            return process_value(true_value if result else false_value)

        return value

    except Exception as e:
        from robot.libraries.BuiltIn import BuiltIn
        builtin = BuiltIn()
        builtin.log(f"应用条件表达式失败: {e}", "WARN")
        return value


def compare_values_equal(value1, value2):
    """比较两个值是否相等"""
    try:
        # 尝试数值比较
        return float(value1) == float(value2)
    except (ValueError, TypeError):
        # 字符串比较
        return str(value1) == str(value2)



def evaluate_simple_condition(condition):
    """评估简单条件表达式（用于向后兼容）"""
    try:
        # 支持常见的比较操作
        if '==' in condition:
            left, right = condition.split('==', 1)
            left_val = left.strip()
            right_val = right.strip().strip('"\'')
            return compare_values_equal(left_val, right_val)

        elif '!=' in condition:
            left, right = condition.split('!=', 1)
            left_val = left.strip()
            right_val = right.strip().strip('"\'')
            return not compare_values_equal(left_val, right_val)

        elif '>=' in condition:
            left, right = condition.split('>=', 1)
            return compare_values_numeric(left.strip(), right.strip(), '>=')

        elif '<=' in condition:
            left, right = condition.split('<=', 1)
            return compare_values_numeric(left.strip(), right.strip(), '<=')

        elif '>' in condition:
            left, right = condition.split('>', 1)
            return compare_values_numeric(left.strip(), right.strip(), '>')

        elif '<' in condition:
            left, right = condition.split('<', 1)
            return compare_values_numeric(left.strip(), right.strip(), '<')

        else:
            # 默认为真值检查
            return bool(condition)

    except Exception:
        return False

def apply_multi_condition(value, parsed_expression):
    """
    应用多条件表达式

    Args:
        value: 原始值
        parsed_expression: 解析后的条件表达式对象

    Returns:
        转换后的值
    """
    try:
        conditions = parsed_expression.get('conditions', [])
        else_value = parsed_expression.get('else')

        # 遍历条件，找到第一个匹配的
        for condition in conditions:
            operator = condition.get('operator')
            compare_value = condition.get('value')
            result_value = condition.get('result')
            min_value = condition.get('minValue')
            max_value = condition.get('maxValue')

            if evaluate_condition(value, operator, compare_value, min_value, max_value):
                return result_value

        # 如果没有条件匹配，返回else值，如果else值为空则返回原始值
        if else_value is not None and else_value != '':
            return else_value
        else:
            return value

    except Exception as e:
        from robot.libraries.BuiltIn import BuiltIn
        builtin = BuiltIn()
        builtin.log(f"应用多条件表达式失败: {e}", "WARN")
        return value



def evaluate_condition(value, operator, compare_value=None, min_value=None, max_value=None):
    """
    评估单个条件

    Args:
        value: 要比较的值
        operator: 操作符
        compare_value: 比较值
        min_value: 区间最小值
        max_value: 区间最大值

    Returns:
        bool: 条件是否满足
    """
    try:
        # 处理空值检查
        if operator == 'is_empty':
            return value is None or str(value).strip() == ''
        elif operator == 'not_empty':
            return value is not None and str(value).strip() != ''

        # 处理区间条件
        if operator in ['between', 'not_between']:
            if min_value is None or max_value is None:
                print(f"❌ 区间条件缺少参数: min_value={min_value}, max_value={max_value}")
                return False
            try:
                val_num = float(value)
                min_num = float(min_value)
                max_num = float(max_value)
                in_range = min_num <= val_num <= max_num
                result = in_range if operator == 'between' else not in_range
                print(f"🔍 区间判断: {value}({val_num}) 在 [{min_value}({min_num}), {max_value}({max_num})] 内? {in_range}, 操作符: {operator}, 结果: {result}")
                return result
            except (ValueError, TypeError) as e:
                print(f"❌ 区间条件数值转换失败: value={value}, min_value={min_value}, max_value={max_value}, error={e}")
                return False

        # 处理字符串包含
        if operator == 'contains':
            return str(compare_value) in str(value)
        elif operator == 'not_contains':
            return str(compare_value) not in str(value)

        # 处理数值和字符串比较
        if operator == '==':
            return compare_values_equal(value, compare_value)
        elif operator == '!=':
            return not compare_values_equal(value, compare_value)
        elif operator in ['>', '<', '>=', '<=']:
            return compare_values_numeric(value, compare_value, operator)

        return False

    except Exception as e:
        from robot.libraries.BuiltIn import BuiltIn
        builtin = BuiltIn()
        builtin.log(f"条件评估失败: {e}", "WARN")
        return False



def display_condition_expression(device_data,item, expression):
    """
    显示条件表达式

    Args:
        value: 原始值
        expression: 条件表达式，支持JSON格式的多条件或旧的三元运算符格式

    Returns:
        转换后的值
    """
    try:
        import json
        import re

        # 尝试解析JSON格式的多条件表达式
        try:
            conditions = json.loads(expression)
        except (json.JSONDecodeError, TypeError):
            return True
        """
        conditions结构如下:
        [{
            "field":"#{Response[].code}",
            "relation": "==或>或<或!="
            "value":"比较值"
        }]
        1. 取field，去掉#{}，取其中的值，并去掉第一个点前面的值
        2. 从device_data或item中取得对应field的值，
        3. 根据relation关系，判断结果
        """

        validResult = True
        for condition in conditions:
            field = condition.get('field')
            field = field.split('.')[1]
            value = get_variable_value(field, device_data)
            item_value = get_variable_value(field, item)
            if value == None or value.startswith('${') or value.startswith('#{'):
                value = item_value
            result = evaluate_condition(value, condition.get('relation'), condition.get('value'))
            if not result:
                validResult = False
                break


        return validResult


    except Exception as e:
        from robot.libraries.BuiltIn import BuiltIn
        builtin = BuiltIn()
        builtin.log(f"显示条件表达式失败: {e}", "WARN")
        return True

def get_field_value_direct(variable_name, http_response_data):
    """
    直接从HTTP响应数据获取字段值，支持多种路径格式：
    1. 数组路径：Response[].fieldName
    2. 字典路径：Response.data.fieldName
    3. 直接字段名：fieldName
    """
    try:
        if not variable_name or not http_response_data:
            return ''

        # 1. 处理数组路径 (如 "Response[].fieldName")
        if '[].' in variable_name:
            field_name = variable_name.split('[].')[-1]
            if isinstance(http_response_data, list) and len(http_response_data) > 0:
                first_item = http_response_data[0]
                if isinstance(first_item, dict) and field_name in first_item:
                    return first_item[field_name]
            return ''

        # 2. 处理点分隔路径 (如 "Response.data.fieldName")
        elif '.' in variable_name:
            parts = variable_name.split('.')
            current_data = http_response_data

            for part in parts:
                if isinstance(current_data, dict):
                    current_data = current_data.get(part, None)
                elif isinstance(current_data, list) and len(current_data) > 0:
                    current_data = current_data[0].get(part, None)
                else:
                    return ''

                if current_data is None:
                    return ''

            return current_data

        # 3. 处理直接字段名
        else:
            if isinstance(http_response_data, dict):
                return http_response_data.get(variable_name, '')
            return ''

    except Exception as e:
        logging.error(f"获取字段值失败: {e}")
        return ''


def apply_template_fixed_text(worksheet, template_workbook_data, variable_mapping=None):
    """
    应用模板中的固定文本到工作表，支持变量替换
    """
    from robot.libraries.BuiltIn import BuiltIn
    builtin = BuiltIn()

    try:
        sheets = template_workbook_data.get('sheets', {})
        if not sheets:
            return

        # 获取第一个工作表
        first_sheet = next(iter(sheets.values()))
        cell_data = first_sheet.get('cellData', {})



        for row_str, row_data in cell_data.items():
            row_num = int(row_str) + 1  # Univer使用0基索引，Excel使用1基索引

            for col_str, cell_info in row_data.items():
                col_num = int(col_str) + 1  # Univer使用0基索引，Excel使用1基索引
                # 检查是否是合并单元格
                cell = worksheet.cell(row=row_num, column=col_num)
                if is_merged_cell(worksheet, cell) and not is_merge_origin_cell(worksheet, cell):
                    continue  # 跳过非主合并单元格
                cell_value = cell_info.get('v', '')

                # 只处理固定文本（不包含绑定语法的单元格）
                if cell_value and not (isinstance(cell_value,str) and cell_value.startswith('#{') and cell_value.endswith('}')):
                    # 进行变量替换
                    processed_value = simple_variable_replacement(cell_value, variable_mapping)
                    worksheet.cell(row=row_num, column=col_num, value=processed_value)

    except Exception as e:

        error_msg = f"""
        ❌ 处理固定文本失败: {str(e)}
        完整堆栈信息:
        {traceback.format_exc()}
        """
        builtin.log(error_msg, "ERROR")
def is_merge_origin_cell(worksheet, cell):
    """
    检查单元格是否是合并区域的主单元格（左上角单元格）
    """
    for merged_range in worksheet.merged_cells.ranges:
        if cell.coordinate == merged_range.coord.split(':')[0]:
            return True
    return

def parse_excel_range(range_str):
    """
    解析Excel范围字符串，如'A1:D10'
    返回: (start_col, start_row, end_col, end_row)
    """
    pattern = r'([A-Z]+)(\d+):([A-Z]+)(\d+)'
    match = re.match(pattern, range_str)

    if not match:
        raise ValueError(f"无效的范围格式: {range_str}")

    start_col_letter, start_row, end_col_letter, end_row = match.groups()

    start_col = col_letter_to_num(start_col_letter)
    end_col = col_letter_to_num(end_col_letter)

    return start_col, int(start_row), end_col, int(end_row)


# 将列字母转换为数字
def col_letter_to_num(letter):
    num = 0
    for char in letter:
        num = num * 26 + (ord(char) - ord('A') + 1)
    return num

# 构建实际范围字符串用于显示
def num_to_col_letter(num):
    result = ""
    while num > 0:
        num -= 1
        result = chr(num % 26 + ord('A')) + result
        num //= 26
    return result



def read_excel(workbook, sheetname, rowdata, columndata,
               range_type, range=None, start_row=None, end_row=None, start_col=None, end_col=None):

    if sheetname in workbook.sheetnames:
        worksheet = workbook[sheetname]
    else:
        print(f"工作表 '{sheetname}' 不存在")
        return

    # 追加数据到工作表末尾
    rowdata = ast.literal_eval(rowdata)
    if rowdata:
        header = list(rowdata[0].keys())
        rows = [[str(item[key]) for key in header] for item in rowdata]
        for row in rows:
            worksheet.append(row)

    # columndata
    column_position = worksheet.max_column + 1

    columndata = ast.literal_eval(columndata)
    if columndata:
        # header = list(columndata[0].keys())
        # columns = columndata[0][header[0]]
        # worksheet.cell(row=1, column=column_position, value=header[0])
        # for i, data in enumerate(columns, start=2):
        #     worksheet.cell(row=i, column=column_position, value=data)

        for coluitem in columndata:
            for column_name, column_data in coluitem.items():
                # 添加列标题
                worksheet.cell(row=1, column=column_position, value=column_name)
                for i, itemdata in enumerate(column_data, start=2):
                    worksheet.cell(row=i, column=column_position, value=itemdata)
                column_position += 1

    data = []
    if range_type == "all":

        for row in worksheet.iter_rows(values_only=True):
            data.append(list(row))  # 将每一行的数据转换为列表并添加到列表中

    elif range_type == "range":
        start_col, start_row, end_col, end_row = parse_excel_range(range)
        print(start_col, start_row, end_col, end_row)
        # 获取工作表实际范围
        actual_max_row = worksheet.max_row
        actual_max_col = worksheet.max_column

        # 调整范围
        effective_end_row = min(end_row, actual_max_row)
        effective_end_col = min(end_col, actual_max_col)
        actual_range = f"{num_to_col_letter(start_col)}{start_row}:{num_to_col_letter(effective_end_col)}{effective_end_row}"

        print(f"请求范围: {range}")
        print(f"工作表实际最大范围: {num_to_col_letter(actual_max_col)}{actual_max_row}")
        print(f"实际读取范围: {actual_range}")

        data_range = worksheet[actual_range]
        for row in data_range:
            row_data = []
            for cell in row:
                row_data.append(cell.value)
            data.append(row_data)

    elif range_type == "rows_cols":
        start_col = col_letter_to_num(start_col)
        end_col = col_letter_to_num(end_col)

        # 获取工作表实际范围
        actual_max_row = worksheet.max_row
        actual_max_col = worksheet.max_column

        # 调整范围
        effective_end_row = min(int(end_row), actual_max_row)
        effective_end_col = min(int(end_col), actual_max_col)
        actual_range = f"{num_to_col_letter(start_col)}{start_row}:{num_to_col_letter(effective_end_col)}{effective_end_row}"
        data_range = worksheet[actual_range]
        for row in data_range:
            row_data = []
            for cell in row:
                row_data.append(cell.value)
            data.append(row_data)

    workbook.close()
    return data


def apply_cell_style(cell, style_id, template_data):
    """
    应用单元格样式到Excel单元格
    Args:
        cell: openpyxl的单元格对象
        style_id: 样式ID (如 'nio9qR')
        template_data: 整个模板数据
    """
    if not style_id or not template_data:
        return

    builtin = BuiltIn()
    styles = template_data.get('data', {}).get('styles', {})
    style_info = styles.get(style_id, {})

    if not style_info:
        return

    # 1. 处理边框样式
    if 'bd' in style_info:
        border_info = style_info['bd']

        border = Border(
            left=Side(style='thin', color=convert_color(border_info.get('l', {}).get('cl'))),
            right=Side(style='thin', color=convert_color(border_info.get('r', {}).get('cl'))),
            top=Side(style='thin', color=convert_color(border_info.get('t', {}).get('cl'))),
            bottom=Side(style='thin', color=convert_color(border_info.get('b', {}).get('cl')))
        )
        cell.border = border

    # 2. 处理背景色
    if 'bg' in style_info and 'rgb' in style_info['bg']:
        bg_color = convert_color(style_info['bg'])

        fill = PatternFill(start_color=bg_color,
                           end_color=bg_color,
                           fill_type='solid')
        cell.fill = fill

    # 3. 处理字体颜色
    font = Font()
    if 'cl' in style_info and 'rgb' in style_info['cl']:
        font_color = convert_color(style_info['cl'])
        cell.font = Font(color=font_color)
    # 3. 处理字体名称、加粗、斜体等
    if 'ff' in style_info:
        font.name = style_info['ff']  # 字体名称
    if 'bl' in style_info:
        font.bold = style_info['bl'] == 1  # 加粗
    if 'it' in style_info:
        font.italic = style_info['it'] == 1  # 斜体
    if 'fs' in style_info:
        font.size = style_info['fs']  # 字号
    cell.font = font
    # 4. 处理对齐方式
    alignment = Alignment(horizontal='center', vertical='center')
    # 根据模板数据调整对齐方式
    if 'ht' in style_info:  # 水平对齐 1左 2中 3右
        if style_info['ht'] == 1:
            alignment.horizontal = 'left'
        elif style_info['ht'] == 2:
            alignment.horizontal = 'center'
        elif style_info['ht'] == 3:
            alignment.horizontal = 'right'

    if 'vt' in style_info:  # 垂直对齐
        if style_info['vt'] == 1:
            alignment.vertical = 'top'
        elif style_info['vt'] == 2:
            alignment.vertical = 'center'
        elif style_info['vt'] == 3:
            alignment.vertical = 'bottom'
        # 5. 换行方式 tb 1 不换行 2 换行 3 截断
    if 'tb' in style_info:
        if style_info['tb'] == 1:
            alignment.wrap_text = False
        elif style_info['tb'] == 2:
            alignment.wrap_text = True
        elif style_info['tb'] == 3:
            alignment.wrap_text = True
            alignment.shrink_to_fit = True
    cell.alignment = alignment

 # 颜色转换函数
def convert_color(color_dict):

    builtin = BuiltIn()
    color = 'FF000000'  # 默认黑色
    try:
        if not color_dict or 'rgb' not in color_dict:
            return '000000'  # 默认黑色
        color = color_dict['rgb']
        # 处理rgb(r,g,b)格式
        if color.startswith('rgb('):
            import re
            match = re.match(r'rgb\((\d+),\s*(\d+),\s*(\d+)\)', color)
            if match:
                r, g, b = match.groups()
                # 将RGB转换为6位HEX
                color = f"{int(r):02X}{int(g):02X}{int(b):02X}"
        if color.startswith('#'):
            color = color[1:]
        # 处理3位缩写(如 #FFF -> FFFFFF)
        if len(color) == 3:
            color = ''.join([c * 2 for c in color])

        # 处理6位RGB -> 8位aRGB(添加不透明值FF)
        if len(color) == 6:
            color = 'FF' + color

        # 确保最终是8位aRGB
        if len(color) != 8:
            return 'FF000000'  # 格式不正确时返回默认黑色
        return color
    except Exception as e:
        builtin.log(f"❌ 处理文本颜色失败: {e},使用默认的黑色", "ERROR")
        return color

def apply_template_to_worksheet(worksheet, template_data):
    """
    将模板数据和样式应用到工作表
    """
    try:
        builtin = BuiltIn()
        builtin.log("🔄 开始应用模板样式...", "ERROR")
        sheet_name = worksheet.title.lower()
        cell_data = template_data.get('data', {}).get('sheets', {}).get(sheet_name, {}).get('cellData', {})

        # # 1. 首先处理合并单元格
        merge_data = template_data.get('data', {}).get('sheets', {}).get(sheet_name, {}).get('mergeData', {})
        for merge_info in merge_data:
            try:
                start_row = merge_info['startRow'] + 1  # 转换为1基
                end_row = merge_info['endRow'] + 1
                start_col = merge_info['startColumn'] + 1
                end_col = merge_info['endColumn'] + 1

                # 转换为Excel列字母
                start_col_letter = get_column_letter(start_col)
                end_col_letter = get_column_letter(end_col)

                merge_range = f"{start_col_letter}{start_row}:{end_col_letter}{end_row}"
                worksheet.merge_cells(merge_range)
                builtin.log(f"✅ 合并单元格: {merge_range}", "ERROR")
            except Exception as e:
                builtin.log(f"❌ 合并单元格失败: {e}", "ERROR")
        # 2. 处理单元格数据和样式
        for row_str, row_data in cell_data.items():
            row_num = int(row_str) + 1  # Univer使用0基索引，Excel使用1基索引

            for col_str, cell_info in row_data.items():
                col_num = int(col_str) + 1  # Univer使用0基索引，Excel使用1基索引
                cell = worksheet.cell(row=row_num, column=col_num)
                # 应用样式
                if 's' in cell_info:
                    apply_cell_style(cell, cell_info['s'], template_data)
                    builtin.log(f"✅ 应用样式: {cell_info['s']}", "ERROR")
                # 获取当前单元格
                if is_merged_cell(worksheet, cell) and not is_merge_origin_cell(worksheet, cell):
                    continue  # 跳过非主合并单元格
                # 设置单元格值
                if 'v' in cell_info:
                    # 获取值，如果值是用${} 或#{}类型的变量，不赋值
                    if not isinstance(cell_info['v'], str) or not (cell_info['v'].startswith('${') or cell_info['v'].startswith('#{')):
                        cell.value = cell_info['v']



        # 3.设置列宽
        column_data = template_data.get('data', {}).get('sheets', {}).get('sheet1', {}).get('columnData', {})
        for col_str, col_info in column_data.items():
            col_num = int(col_str) + 1
            if 'w' in col_info:
                # 将模板中的宽度转换为Excel列宽单位
                excel_width = col_info['w'] / 7  # 经验值调整
                worksheet.column_dimensions[get_column_letter(col_num)].width = excel_width
                builtin.log(f"✅ 设置列宽: {col_num}", "ERROR")

        # 行数据 行高
        row_data = template_data.get('data', {}).get('sheets', {}).get(sheet_name, {}).get('rowData', {})
        for row_str, row_info in row_data.items():
            row_num = int(row_str) + 1
            if 'h' in row_info:
                # 将模板中的高度转换为Excel行高单位
                excel_height = row_info['h']
                worksheet.row_dimensions[row_num].height = excel_height
                builtin.log(f"✅ 设置行高: {row_num}", "ERROR")

        builtin.log("✅ 模板样式应用完成", "ERROR")

    except Exception as e:
        # 打印异常堆栈信息
        import traceback
        # 打印完整异常堆栈信息
        error_msg = f"""
        ❌ 应用模板样式失败: {str(e)}
        完整堆栈信息:
        {traceback.format_exc()}
        """
        builtin.log(error_msg, "ERROR")
        # builtin.log(f"❌ 应用模板样式失败: {e}", "ERROR")


def get_column_letter(col_num):
    """
    将列数字转换为字母 (1 => 'A', 2 => 'B', ...)
    """
    letter = ''
    while col_num > 0:
        col_num, remainder = divmod(col_num - 1, 26)
        letter = chr(65 + remainder) + letter
    return letter


def write_excel_with_template(file_path, template_id, worksheet_name,variable_mapping="",
                              overwrite=True, auto_fit=False):
    """
    使用报表模板写入Excel数据

    Args:
        worksheet_name: 工作表名称
        template_id: 报表模板ID
        variable_mapping: 变量映射字典
        workbook: Excel工作簿对象
        overwrite: 是否覆盖现有数据
        auto_fit: 是否自动调整列宽
    """
    import json
    import os
    from pathlib import Path

    try:
        # 确保文件路径有正确的扩展名
        if not file_path.lower().endswith(('.xlsx', '.xls')):
            file_path = file_path + '.xlsx'
        # 文件是否存在，不存在就创建
        if not os.path.exists(file_path):
            return create_excel_with_report_template(file_path, template_id, worksheet_name)

        workbook = load_workbook(file_path)

        # 加载报表模板
        template_data = get_online_template(template_id)
        if template_data and template_data["Response"]:
            template_data = json.loads(template_data["Response"]["config"])
        else:
            template_file = Path("data/reports/templates") / f"{template_id}.json"
            if not template_file.exists():
                raise Exception(f"报表模板不存在: {template_id}")

            with open(template_file, 'r', encoding='utf-8') as f:
                template_data = json.load(f)

        # 解析变量映射
        if isinstance(variable_mapping, str):
            try:
                variable_mapping = json.loads(variable_mapping)
            except:
                variable_mapping = {}

        # 获取模板的单元格绑定信息
        bindings = template_data.get('bindings', {})
        template_workbook_data = template_data.get('data', {})

        # 如果没有提供workbook，创建新的工作簿
        if workbook is None:
            from openpyxl import Workbook
            workbook = Workbook()
            if worksheet_name in workbook.sheetnames:
                worksheet = workbook[worksheet_name]
            else:
                worksheet = workbook.create_sheet(worksheet_name)
        else:
            # 获取或创建工作表
            if worksheet_name in workbook.sheetnames:
                worksheet = workbook[worksheet_name]
            else:
                worksheet = workbook.create_sheet(worksheet_name)

        # 清空工作表（如果需要覆盖）
        if overwrite:
            worksheet.delete_rows(1, worksheet.max_row)
            worksheet.delete_cols(1, worksheet.max_column)

        # 应用模板数据到工作表
        # apply_template_to_worksheet(worksheet, template_workbook_data, bindings, variable_mapping)

        # 自动调整列宽
        if auto_fit:
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return workbook

    except Exception as e:
        raise Exception(f"使用报表模板写入Excel失败: {str(e)}")



def get_array_data(array_field, variable_mapping):
    """
    从变量映射中获取数组数据

    Args:
        array_field: 数组字段路径，如 "Response.vals" 或 "Response"
        variable_mapping: 变量映射

    Returns:
        数组数据
    """
    try:
        print(f"  🔍 解析数组字段: {array_field}")

        # 解析路径
        if '.' in array_field:
            parts = array_field.split('.')
            print(f"  📂 路径部分: {parts}")

            # 通用路径解析（移除硬编码的vals特殊处理）
            data = variable_mapping
            for i, part in enumerate(parts):
                print(f"  📍 处理路径部分[{i}]: {part}, 当前数据类型: {type(data)}")

                if isinstance(data, dict) and part in data:
                    data = data[part]
                    print(f"    ✅ 找到字段 {part}")
                elif isinstance(data, list) and len(data) > 0:
                    # 如果当前是数组，需要特殊处理
                    print(f"    🔄 当前是数组，长度: {len(data)}")
                    # 对于嵌套路径，取第一个元素继续解析
                    data = data[0]
                    if isinstance(data, dict) and part in data:
                        data = data[part]
                        print(f"    ✅ 在数组第一项中找到字段 {part}")
                    else:
                        print(f"    ❌ 在数组第一项中未找到字段 {part}")
                        return []
                else:
                    print(f"    ❌ 未找到字段 {part}")
                    return []

            result = data if isinstance(data, list) else []
            print(f"  🎯 最终结果: {len(result)}项")
            return result
        else:
            # 简单字段名
            data = variable_mapping.get(array_field, [])
            result = data if isinstance(data, list) else []
            print(f"  🎯 简单字段结果: {len(result)}项")
            return result

    except Exception as e:
        print(f"  ❌ 获取数组数据失败: {e}")
        return []




def process_cell_binding(binding, variable_mapping, original_value):
    """
    处理单元格绑定，生成实际值

    Args:
        binding: 绑定信息
        variable_mapping: 变量映射
        original_value: 原始单元格值

    Returns:
        处理后的值
    """
    try:
        variable_name = binding.get('variable', '')
        binding_type = binding.get('type', 'string')

        print(f"  🔍 处理绑定: {variable_name} (类型: {binding_type})")

        # 根据绑定类型处理
        if binding_type == 'loop':
            # 循环绑定：尝试获取真实数据
            value = get_variable_value(variable_name, variable_mapping)
        elif binding_type == 'string':
            # 字符串绑定：直接替换
            value = get_variable_value(variable_name, variable_mapping)
        else:
            # 其他类型：尝试获取变量值
            value = get_variable_value(variable_name, variable_mapping)

        # 应用条件表达式（如果有）
        if binding.get('conditionalExpression'):
            value = apply_conditional_expression(value, binding.get('conditionalExpression'))
            print(f"  🔄 应用条件表达式后: {value}")

        return value

    except Exception as e:
        print(f"  ❌ 处理绑定失败: {e}")
        return original_value





def get_variable_value(variable_name, variable_mapping):
    """
    从变量映射中获取变量值

    Args:
        variable_name: 变量名
        variable_mapping: 变量映射

    Returns:
        变量值
    """
    # 直接匹配
    if variable_name in variable_mapping:
        return str(variable_mapping[variable_name])

    # 尝试部分匹配（处理复杂路径）
    for key, value in variable_mapping.items():
        if key in variable_name or variable_name in key:
            return str(value)

    # 没有找到，返回原始变量名
    return f"${{{variable_name}}}"


def simple_variable_replacement(text, variable_mapping):
    """
    简单的变量替换

    Args:
        text: 原始文本
        variable_mapping: 变量映射

    Returns:
        替换后的文本
    """
    if not isinstance(text, str) or not variable_mapping:
        return text

    result = text

    # 替换 ${...} 格式
    import re
    for var_name, var_value in variable_mapping.items():
        if isinstance(var_value, str) and '</think>' in var_value:
            var_value = markdown_to_text(var_value)

        result = result.replace(f"${{{var_name}}}", str(var_value))
        result = result.replace(f"#{{{var_name}}}", str(var_value))

    return result




def compare_values_equal(value1, value2):
    """比较两个值是否相等"""
    try:
        # 尝试数值比较
        return float(value1) == float(value2)
    except (ValueError, TypeError):
        # 字符串比较
        return str(value1) == str(value2)


def compare_values_numeric(value1, value2, operator):
    """数值比较"""
    try:
        val1 = float(value1)
        val2 = float(value2)

        if operator == '>':
            return val1 > val2
        elif operator == '<':
            return val1 < val2
        elif operator == '>=':
            return val1 >= val2
        elif operator == '<=':
            return val1 <= val2

        return False
    except (ValueError, TypeError):
        return False



def load_password_excel(file, password, data_only, keep_vba):


    with open(file, "rb") as f:
        office_file = msoffcrypto.OfficeFile(f)
        office_file.load_key(password=password)
        decrypted = io.BytesIO()
        office_file.decrypt(decrypted)
    string_to_bool = {'true': True, 'false': False}
    data_only = string_to_bool[data_only.lower()]
    keep_vba = string_to_bool[keep_vba.lower()]
    workbook = load_workbook(decrypted, data_only=data_only, keep_vba=keep_vba)
    return workbook


def load_excel(file, read_only):
    string_to_bool = {'true': True, 'false': False}
    flag = string_to_bool[read_only.lower()]
    workbook = load_workbook(file, read_only=flag)

    return workbook


def create_excel(file_path, data, sheet_name):
    workbook = Workbook()
    sheet = workbook.active
    sheet.title = sheet_name
    data = ast.literal_eval(data)
    if data:
        result = []
        header = list(data[0].keys())
        result.append(header)
        rows = [[str(item[key]) for key in header] for item in data]
        result.extend(rows)
        for row in result:
            sheet.append(row)
    try:
        directory = os.path.dirname(file_path)
        open_folder(directory)
    except:
        pass
    workbook.save(file_path)
    return workbook
