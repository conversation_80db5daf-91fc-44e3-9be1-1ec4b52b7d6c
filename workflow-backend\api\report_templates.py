"""
报表模板管理API
提供报表模板的CRUD操作
"""

import os
import json
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Header
from pydantic import BaseModel, Field
from pathlib import Path
from config import globals



from .crud import forward_post,forward_get

# 报表模板存储目录
TEMPLATES_DIR = Path("data/reports/templates")
TEMPLATES_DIR.mkdir(parents=True, exist_ok=True)

router = APIRouter(prefix="/api/report-templates", tags=["报表模板"])


class LoopConfig(BaseModel):
    """循环配置"""
    arrayField: str = Field(..., description="数组字段路径")
    direction: str = Field("vertical", description="扩展方向: vertical/horizontal")
    actualField: Optional[str] = Field(None, description="实际字段名")


class CellBinding(BaseModel):
    """单元格变量绑定"""
    variable: str = Field(..., description="变量名")
    type: str = Field(..., description="变量类型")
    format: Optional[str] = Field(None, description="格式化字符串")
    loopConfig: Optional[LoopConfig] = Field(None, description="循环配置")
    conditionalExpression: Optional[str] = Field(None, description="条件表达式，如 'value == 0 ? \"否\" : \"是\"'")
    displayConditions: Optional[str] = Field(None, description="显示条件表达式")


class ReportTemplate(BaseModel):
    """报表模板模型"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    templateType: str = Field("", description="模板分类")
    description: str = Field("", description="模板描述")
    data: Dict[str, Any] = Field(..., description="Univer工作簿数据")
    bindings: Dict[str, CellBinding] = Field(default_factory=dict, description="单元格变量绑定")
    create_time: str = Field(..., description="创建时间")
    update_time: str = Field(..., description="更新时间")
    creator: str = Field("system", description="创建者")


class CreateTemplateRequest(BaseModel):
    """创建模板请求"""
    name: str = Field(..., description="模板名称")
    templateType: str = Field("", description="模板分类")
    description: str = Field("", description="模板描述")
    data: Dict[str, Any] = Field(..., description="Univer工作簿数据")
    bindings: Dict[str, CellBinding] = Field(default_factory=dict, description="单元格变量绑定")


class UpdateTemplateRequest(BaseModel):
    """更新模板请求"""
    name: Optional[str] = Field(None, description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    data: Optional[Dict[str, Any]] = Field(None, description="Univer工作簿数据")
    bindings: Optional[Dict[str, CellBinding]] = Field(None, description="单元格变量绑定")


def get_template_file_path(template_id: str) -> Path:
    """获取模板文件路径"""
    return TEMPLATES_DIR / f"{template_id}.json"


def load_template(template_id: str) -> Optional[ReportTemplate]:
    """加载模板"""
    file_path = get_template_file_path(template_id)
    if not file_path.exists():
        return None

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return ReportTemplate(**data)
    except Exception as e:
        print(f"加载模板失败: {e}")
        return None


def save_template(template: ReportTemplate,token:str=None) -> bool:
    """保存模板"""
    file_path = get_template_file_path(template.id)
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template.dict(), f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"保存模板失败: {e}")
        return False

    try:
        config = json.dumps(template.dict(), ensure_ascii=False, indent=2)
        body = {"id":template.id,"name":template.name,"templateType":template.templateType,"config":config}
        forward_post("/wimai/api/office/template/add",authorization = token,params=body)
    except Exception as e:
        print(f"更新模板到服务器失败: {e}")

    return True

def delete_template_file(template_id: str) -> bool:
    """删除模板文件"""
    file_path = get_template_file_path(template_id)
    try:
        if file_path.exists():
            file_path.unlink()
        return True
    except Exception as e:
        print(f"删除模板文件失败: {e}")
        return False


@router.get("/", response_model=List[ReportTemplate])
async def get_templates(templateType: str = None):
    authorization = globals.token
    json_data = {"index": -1}  # 基础字段
    if templateType:
        json_data["data"] = {"templateType": templateType}

    resp = forward_post("/wimai/api/office/template/query",
                        authorization=authorization,
                        params=json_data)

    templates = []

    if  resp and resp["Response"]:
        rows = resp["Response"]["rows"]
        for row in rows:
            templates.append(ReportTemplate(**json.loads(row["config"])))
    else:
        try:
            for file_path in TEMPLATES_DIR.glob("*.json"):
                template_id = file_path.stem
                template = load_template(template_id)
                if template:
                    templates.append(template)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

    # 按创建时间倒序排列
    templates.sort(key=lambda x: x.create_time, reverse=True)
    return templates


@router.get("/{template_id}", response_model=ReportTemplate)
async def get_template(template_id: str,authorization: str = Header(None)):
    """获取指定报表模板"""
    resp = forward_post("/wimai/api/office/template/detail", authorization=authorization, params={"id": template_id})
    if resp and resp["Response"] :
        return resp["Response"]["config"]
    else:
        template = load_template(template_id)
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        return template


@router.post("/", response_model=ReportTemplate)
async def create_template(request: CreateTemplateRequest,authorization: str = Header(None)):
    """创建报表模板"""
    template_id = str(uuid.uuid4())
    current_time = datetime.now().isoformat()

    template = ReportTemplate(
        id=template_id,
        name=request.name,
        templateType=request.templateType,
        description=request.description,
        data=request.data,
        bindings=request.bindings,
        create_time=current_time,
        update_time=current_time,
    )

    if not save_template(template,authorization):
        raise HTTPException(status_code=500, detail="保存模板失败")

    return template


@router.put("/{template_id}", response_model=ReportTemplate)
async def update_template(template_id: str, request: UpdateTemplateRequest,authorization: str = Header(None)):
    """更新报表模板"""
    template = load_template(template_id)
    if template:
        # 更新字段
        if request.name is not None:
            template.name = request.name
        if request.description is not None:
            template.description = request.description
        if request.data is not None:
            template.data = request.data
        if request.bindings is not None:
            template.bindings = request.bindings

        template.update_time = datetime.now().isoformat()
    resp = forward_get("/wimai/api/office/template/detail", authorization=authorization, params={"id": template_id})
    if resp and resp["Response"]:
        config =  json.loads(resp["Response"]["config"])
        template = ReportTemplate(**config)
        # 更新字段
        if request.name is not None:
            template.name = request.name
        if request.description is not None:
            template.description = request.description
        if request.data is not None:
            template.data = request.data
        if request.bindings is not None:
            template.bindings = request.bindings

    if not save_template(template,authorization):
        raise HTTPException(status_code=500, detail="更新模板失败")

    return template


@router.delete("/{template_id}")
async def delete_template(template_id: str,authorization: str = Header(None)):
    """删除报表模板"""
    forward_post("/wimai/api/office/template/delete",authorization,[template_id])
    
    template = load_template(template_id)
    # if not template:
        # raise HTTPException(status_code=404, detail="模板不存在")

    # if not delete_template_file(template_id):
        # raise HTTPException(status_code=500, detail="删除模板失败")

    return {"message": "模板删除成功"}


@router.get("/{template_id}/export")
async def export_template(template_id: str):
    """导出报表模板"""
    template = load_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 返回模板数据用于下载
    export_data = {
        "name": template.name,
        "description": template.description,
        "data": template.data,
        "bindings": template.bindings,
        "export_time": datetime.now().isoformat(),
        "version": "1.0"
    }

    return export_data


@router.post("/import", response_model=ReportTemplate)
async def import_template(template_data: Dict[str, Any],authorization: str = Header(None)):
    """导入报表模板"""
    try:
        # 验证导入数据格式
        if "name" not in template_data or "data" not in template_data:
            raise HTTPException(status_code=400, detail="导入数据格式不正确")

        template_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()

        template = ReportTemplate(
            id=template_id,
            name=template_data["name"],
            description=template_data.get("description", ""),
            data=template_data["data"],
            bindings=template_data.get("bindings", {}),
            create_time=current_time,
            update_time=current_time,
        )

        if not save_template(template,authorization):
            raise HTTPException(status_code=500, detail="导入模板失败")

        return template

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入模板失败: {str(e)}")


@router.get("/{template_id}/preview")
async def preview_template(template_id: str, variables: Optional[Dict[str, Any]] = None):
    """预览报表模板（使用提供的变量值）"""
    template = load_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 创建预览数据
    preview_data = template.data.copy()

    if variables and template.bindings:
        # 应用变量值到模板
        try:
            # 这里需要实现变量替换逻辑
            # 遍历绑定关系，将变量值应用到对应的单元格
            for cell, binding in template.bindings.items():
                if binding.variable in variables:
                    value = variables[binding.variable]
                    # 根据单元格位置更新预览数据
                    # 这里需要根据实际的Univer数据结构来实现
                    pass
        except Exception as e:
            print(f"应用变量值失败: {e}")

    return {
        "template": template,
        "preview_data": preview_data,
        "applied_variables": variables or {}
    }


# 获取可用的模板选项（用于前端下拉框）
@router.get("/options/list")
async def get_template_options(templateType: str = None):
    """获取模板选项列表（用于前端下拉框）"""
    templates = await get_templates(templateType)
    return [
        {
            "label": template.name,
            "value": template.id,
            "description": template.description
        }
        for template in templates
    ]
