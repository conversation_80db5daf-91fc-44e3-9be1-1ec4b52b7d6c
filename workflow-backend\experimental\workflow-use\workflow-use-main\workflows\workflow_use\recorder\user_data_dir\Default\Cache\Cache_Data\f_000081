!function(e){function t(t){for(var a,i,s=t[0],c=t[1],u=t[2],l=0,p=[];l<s.length;l++)i=s[l],Object.prototype.hasOwnProperty.call(o,i)&&o[i]&&p.push(o[i][0]),o[i]=0;for(a in c)Object.prototype.hasOwnProperty.call(c,a)&&(e[a]=c[a]);for(d&&d(t);p.length;)p.shift()();return r.push.apply(r,u||[]),n()}function n(){for(var e,t=0;t<r.length;t++){for(var n=r[t],a=!0,i=1;i<n.length;i++){var c=n[i];0!==o[c]&&(a=!1)}a&&(r.splice(t--,1),e=s(s.s=n[0]))}return e}var a={},i={index:0},o={index:0},r=[];function s(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.e=function(e){var t=[];i[e]?t.push(i[e]):0!==i[e]&&{"chunk-555f4130":1,"chunk-618b4ecc":1,"chunk-620caac4":1,"chunk-6f2acad0":1,"chunk-0df06a5a":1,"chunk-435e3d73":1,"chunk-6db3fc7c":1,"chunk-3c4a3fdb":1,"chunk-7146eae5":1,"chunk-1c33c228":1,"chunk-6ad94c94":1,"chunk-e8b89456":1,"chunk-c35539f8":1,"chunk-0399d562":1,"chunk-093d85e6":1,"chunk-1f7c93c6":1,"chunk-24f3f763":1,"chunk-2a6aa068":1,"chunk-2d88fc4a":1,"chunk-2df4f603":1,"chunk-33fa93ec":1,"chunk-4c34b870":1,"chunk-615c639c":1,"chunk-6464ad6a":1,"chunk-734bb9b9":1,"chunk-ede49ca0":1}[e]&&t.push(i[e]=new Promise((function(t,n){for(var a="static/css/"+({}[e]||e)+"."+{"chunk-555f4130":"80a87c75","chunk-618b4ecc":"bd8962a7","chunk-620caac4":"2c8f2090","chunk-6f2acad0":"c4687e92","chunk-0df06a5a":"e62f96bf","chunk-b85d6058":"31d6cfe0","chunk-435e3d73":"b4762fed","chunk-6db3fc7c":"c84397c7","chunk-3c4a3fdb":"c6d13e2c","chunk-7146eae5":"dcef0245","chunk-f9b9e012":"31d6cfe0","chunk-1c33c228":"4ca7dd95","chunk-6ad94c94":"7850154f","chunk-e8b89456":"04f67af5","chunk-c35539f8":"92384112","chunk-0399d562":"c5ff4be9","chunk-093d85e6":"b679fe35","chunk-1f7c93c6":"c588e872","chunk-24f3f763":"30a32975","chunk-2a6aa068":"802e0a5f","chunk-2d88fc4a":"ed2ae24a","chunk-2df4f603":"53bedbc0","chunk-33fa93ec":"5f5ff593","chunk-4c34b870":"9a4f9ce9","chunk-615c639c":"94f0277c","chunk-6464ad6a":"47b273bd","chunk-734bb9b9":"3b91399a","chunk-ede49ca0":"b5fee13e"}[e]+".css",o=s.p+a,r=document.getElementsByTagName("link"),c=0;c<r.length;c++){var u=r[c],l=u.getAttribute("data-href")||u.getAttribute("href");if("stylesheet"===u.rel&&(l===a||l===o))return t()}var d=document.getElementsByTagName("style");for(c=0;c<d.length;c++)if((l=(u=d[c]).getAttribute("data-href"))===a||l===o)return t();var p=document.createElement("link");p.rel="stylesheet",p.type="text/css",p.onload=t,p.onerror=function(t){var a=t&&t.target&&t.target.src||o,r=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");r.code="CSS_CHUNK_LOAD_FAILED",r.request=a,delete i[e],p.parentNode.removeChild(p),n(r)},p.href=o,document.getElementsByTagName("head")[0].appendChild(p)})).then((function(){i[e]=0})));var n=o[e];if(0!==n)if(n)t.push(n[2]);else{var a=new Promise((function(t,a){n=o[e]=[t,a]}));t.push(n[2]=a);var r,c=document.createElement("script");c.charset="utf-8",c.timeout=120,s.nc&&c.setAttribute("nonce",s.nc),c.src=function(e){return s.p+"static/js/"+({}[e]||e)+"."+{"chunk-555f4130":"f93d30ac","chunk-618b4ecc":"033615a4","chunk-620caac4":"da315de8","chunk-6f2acad0":"fc2041ec","chunk-0df06a5a":"14481b9c","chunk-b85d6058":"c6b59512","chunk-435e3d73":"e434a176","chunk-6db3fc7c":"b71cfbb8","chunk-3c4a3fdb":"71e38d35","chunk-7146eae5":"0d1d1e70","chunk-f9b9e012":"28fd7ace","chunk-1c33c228":"f5d2edbe","chunk-6ad94c94":"5f0a2e88","chunk-e8b89456":"7816ecf5","chunk-c35539f8":"c033e841","chunk-0399d562":"42295aaf","chunk-093d85e6":"3360d15c","chunk-1f7c93c6":"84047289","chunk-24f3f763":"2e408b30","chunk-2a6aa068":"fce0bcc8","chunk-2d88fc4a":"1f5e1b1d","chunk-2df4f603":"8e868df1","chunk-33fa93ec":"6cbc68bd","chunk-4c34b870":"1230c5c5","chunk-615c639c":"40f47d58","chunk-6464ad6a":"13fac688","chunk-734bb9b9":"2faf6cac","chunk-ede49ca0":"36300bdf"}[e]+".js"}(e);var u=new Error;r=function(t){c.onerror=c.onload=null,clearTimeout(l);var n=o[e];if(0!==n){if(n){var a=t&&("load"===t.type?"missing":t.type),i=t&&t.target&&t.target.src;u.message="Loading chunk "+e+" failed.\n("+a+": "+i+")",u.name="ChunkLoadError",u.type=a,u.request=i,n[1](u)}o[e]=void 0}};var l=setTimeout((function(){r({type:"timeout",target:c})}),12e4);c.onerror=c.onload=r,document.head.appendChild(c)}return Promise.all(t)},s.m=e,s.c=a,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)s.d(n,a,function(t){return e[t]}.bind(null,a));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s.oe=function(e){throw console.error(e),e};var c=window.webpackJsonp=window.webpackJsonp||[],u=c.push.bind(c);c.push=t,c=c.slice();for(var l=0;l<c.length;l++)t(c[l]);var d=u;r.push([1,"chunk-cue","chunk-charts","chunk-common","chunk-plugins","chunk-commons"]),n()}({0:function(e,t){},1:function(e,t,n){e.exports=n("56d7")},2:function(e,t){},"25ff":function(e,t,n){},3:function(e,t){},"365c":function(e,t,n){"use strict";n("d9e2"),n("e9f5"),n("7d54"),n("ab43");var a=n("a27e"),i=n("fa7d");const o={accessLogSaveLog({...e},t="web"){const n={...e};return delete n.uuid,a.a.post("/uniwim/ump/accessLog/saveLog",n)},AIConfigList:()=>a.a.get("/wimai/api/sysConfig/list",{}),AIUpdateConfig:e=>a.a.post("/wimai/api/sysConfig/batchSaveOrUpdate",e),WimAIMenuQuery:e=>a.a.post("/wimai/api/menuConfig/query",e),WimAIMenuUpdate:e=>a.a.post("/wimai/api/menuConfig/saveBatch",e),AIShareToWimPic:({name:e,content:t,images:n=[]},i="")=>a.a.post(i+"/uniwim/wimpic/document/insert/markdown",{type:"1",name:e?e.replace(/\//g,"|"):"",content:t,images:n}).then((e=>{let t=window.frames?window.frames.length:0,n="*#hd#*"+JSON.stringify({action:"REFRESH_DOC_PAGE",params:{}});for(let e=0;e<t;e++)window.frames[e].postMessage(n,"*"),console.warn({message:n});return e})),AISend({...e},t=null){"debug"===e.content&&("1"===localStorage.getItem("debug123456")?localStorage.removeItem("debug123456"):localStorage.setItem("debug123456","1"));let n=new FormData;for(let t in e)e[t]&&n.append(t,"file"===t?e[t]:e[t].trim());let a=!1;const o=setTimeout((()=>{a=!0,null==t||t.abort()}),6e4);return fetch(i.a.getHost()+"/wimai/api/sendStream",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},body:n,signal:null==t?void 0:t.signal}).catch((e=>{if(a&&"AbortError"===e.name)throw new Error("当前访问人数过多，请稍候再试!");throw e})).finally((()=>{clearTimeout(o)}))},AISendLottery({content:e,sessionId:t,file:n,meetingId:i}){let o=new FormData;return o.append("content",e||""),o.append("meetingId",i||""),t&&o.append("sessionId",t),n&&o.append("file",n),a.a.post("/wimai/api/send",o,{headers:{"content-type":"application/x-www-form-urlencoded"}})},AIRegeneratorMessage({...e},t){let n=!1;const a=setTimeout((()=>{n=!0,null==t||t.abort()}),6e4);let o=i.a.getHost()+"/wimai/api/regeneratorMessageStream";return Object.keys(e).forEach(((t,n)=>{o+=`${0===n?"?":"&"}${t}=${e[t].trim()}`})),this.AISessionRecordUpdate({id:e.recordId,regenerated:1}),fetch(o,{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},signal:null==t?void 0:t.signal}).catch((e=>{if(n&&"AbortError"===e.name)throw new Error("当前访问人数过多，请稍候再试!");throw e})).finally((()=>{clearTimeout(a)}))},AIRecommend:e=>a.a.post("/wimai/api/recommend",{}),AIReportTemplate:e=>a.a.get("/wimai/api/config/template",{}),AIListSession:e=>a.a.post("/wimai/api/listSession",e),AIPageSessionRecord:e=>a.a.post("/wimai/api/pageSessionRecord",e),AIUpdateSession:e=>a.a.post("/wimai/api/updateSession",e),AIDeleteSession:e=>a.a.post("/wimai/api/deleteSession?id="+e,{}),AIIsLikeRecord:(e,t=-1)=>a.a.post(`/wimai/api/isLikeRecord?id=${e}&isLike=${t}`,{}),AIExamination({questionType:e,num:t,prompt:n,modelType:a="",file:o},r){let s=new FormData;return s.append("questionType",e),s.append("prompt",n),s.append("modelType",a),o&&s.append("file",o),fetch(i.a.getHost()+"/wimai/api/examination",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},body:s,signal:r})},AIRagSearchTitle(e){let t=new FormData;return t.append("topic",e),a.a.post("/wimai/api/ragSearchTitle",t,{headers:{"content-type":"application/x-www-form-urlencoded"}})},AIRagExamination:({questionType:e,num:t=1,topic:n="根据文档出题",titleJson:a="{}",modelType:o=""},r)=>fetch(i.a.getHost()+"/wimai/api/ragExamination",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),"content-type":"application/json; charset=UTF-8",FROM_CHANNEL:"web"},body:JSON.stringify({questionType:e,topic:n,titleJson:a,modelType:o}),signal:r}),AIKcExamTopicLibrary:(e="")=>a.a.get("/uniwim/knowledge/api/kcExamTopicLibrary/selectAll?tenantId="+e,{}),AIKcExamTopicGenerateByAi:e=>a.a.post("/uniwim/knowledge/api/kcExamTopic/generateByAi",e),AILotterySign:e=>a.a.post("/wimai/api/sign?phone="+e,{}),AILotterySignNum:e=>e?a.a.get("/uniwim/ump/meeting/getTotalAndMobile?id="+e):a.a.post("/wimai/api/signNum",{}),AIRagRecommend:e=>a.a.post("/wimai/api/ragRecommend",{}),AIKnowledgeSuggest:({datasetId:e,index:t,size:n=6})=>a.a.post("/wimai/api/KnowledgeSuggest?datasetId="+e,{index:t,size:n}),AIKnowledgeType:()=>a.a.get("/wimai/api/knowledgeType",{}),AISendRag({content:e,sessionId:t,modelType:n,knowledgeType:a},o=null){let r=new FormData;return r.append("content",e||""),r.append("modelType",n||""),a&&r.append("knowledgeType",a||""),t&&r.append("sessionId",t),fetch(i.a.getHost()+"/wimai/api/sendRagStream",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},body:r,signal:o})},AISendInfoModel:({prompt:e})=>a.a.post("/wimai/api/infoModel",{prompt:e},{meta:{isMessage:!1}}),AIRegeneratorRagMessage(e,t=null,n=null){let a=i.a.getHost()+"/wimai/api/regeneratorRagMessageStream?recordId="+e;return t&&(a+="&modelType="+t),fetch(a,{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},signal:n})},AIBriefing({metric:e,type:t,prompt:n,excel:a=null,modelType:o="",agentParams:r=null,sessionId:s},c=null){let u=new FormData;u.append("prompt",n.trim()),u.append("modelType",o),r&&u.append("agentParams",r),s&&u.append("sessionId",s),a?u.append("excel",a):(e&&u.append("metric",e),u.append("type",t));let l=!1;const d=setTimeout((()=>{l=!0,null==c||c.abort()}),6e4);return fetch(i.a.getHost()+"/wimai/api/briefingStream",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web"},body:u,signal:null==c?void 0:c.signal}).catch((e=>{if(l&&"AbortError"===e.name)throw new Error("当前访问人数过多，请稍候再试!");throw e})).finally((()=>{clearTimeout(d)}))},AIAllMetric(e={}){let t=new FormData;return Object.keys(e).forEach((n=>{t.append(n,e[n])})),a.a.post("/wimai/api/allMetric",t,{headers:{"content-type":"application/x-www-form-urlencoded"}})},AIClassification(e="",t="",n){let i=new FormData;return i.append("prompt",e),t&&i.append("modelType",t),a.a.post("/wimai/api/classification",i,{headers:{"content-type":"application/x-www-form-urlencoded"},signal:n})},AIBriefingTemplateSave:e=>a.a.post("/wimai/api/messageBriefingTemplate/save",e),AIBriefingTemplateDelete:e=>a.a.post("/wimai/api/messageBriefingTemplate/delete",e),AIBriefingTemplateQuery:()=>a.a.get("/wimai/api/messageBriefingTemplate/query",{}),AIDevice:(e,t=null)=>fetch(i.a.getHost()+"/wimai/api/deviceStream",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web","Content-Type":"application/json"},body:JSON.stringify(e),signal:t}),AIMenuTemplate:e=>a.a.post("/wimai/api/template/query",e),AIMenuTemplateSaveBatch:e=>a.a.post("/wimai/api/template/saveBatch",e),AIMenuTemplateDelete:e=>a.a.post("/wimai/api/template/delete",e),AIAgentParam:e=>a.a.post("/wimai/api/agentParam/query",e),AIAgentParamSaveBatch:e=>a.a.post("/wimai/api/agentParam/saveBatch",e),AIAgentParamDelete:e=>a.a.post("/wimai/api/agentParam/delete",e),AIMessageFriend:()=>a.a.get("/uniwim/message/messageFriend/query",{keyword:"19820001157"}),AIVoiceToText(e){let t=new FormData;return t.append("file",e),a.a.post("/wimai/api/tool/asr",t,{headers:{"content-type":"application/x-www-form-urlencoded"}})},AITextToSoundStream:(e,t=null)=>fetch(i.a.getHost()+"/wimai/api/tool/ttsStream",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),"Content-Type":"application/json"},body:JSON.stringify({text:function(e){var t;const n=[".","*","+","?","|","(",")","[","]","{","}","^","$","-","#"],a={"°C":"摄氏度","m³":"立方米","m²":"平方米","km²":"平方千米",kg:"千克",mm:"毫米","+":"加","全屏预览":""};if(null!==(t=window.uniwimAICustomReplaceWords)&&void 0!==t&&t.length&&window.uniwimAICustomReplaceWords.forEach((e=>{e.source&&e.value&&(a[e.source]=e.value)})),!e)return e;const i=Object.keys(a).map((e=>n.includes(e)?"\\"+e:e)),o=new RegExp(i.join("|"),"g");return e.replace(o,(e=>{const t=e.replace("\\","");return a[t]}))}(e)}),signal:t}),AITextToSound:(e,t)=>a.a.post("/wimai/api/tool/xfTtsUrl",{text:function(e){var t;const n=[".","*","+","?","|","(",")","[","]","{","}","^","$","-","#"],a={"°C":"摄氏度","m³":"立方米","m²":"平方米","km²":"平方千米",kg:"千克",mm:"毫米","+":"加","全屏预览":""};if(null!==(t=window.uniwimAICustomReplaceWords)&&void 0!==t&&t.length&&window.uniwimAICustomReplaceWords.forEach((e=>{e.source&&e.value&&(a[e.source]=e.value)})),!e)return e;const i=Object.keys(a).map((e=>n.includes(e)?"\\"+e:e)),o=new RegExp(i.join("|"),"g");return e.replace(o,(e=>{const t=e.replace("\\","");return a[t]}))}(e)},{signal:t}).catch((e=>"canceled"===(null==e?void 0:e.message)?"canceled":e)),TwinsMapData:e=>a.a.post("/uniwim/imb/toolbar/label/list",e,{headers:{Authorization:i.a.GetAuthorization(),"tenant-id":i.a.GetQueryString("uniwim_tenant_id","hash")||i.a.GetQueryString("uniwim_tenant_id")}}),AIMapReport:(e,t=null)=>fetch(i.a.getHost()+"/wimai/api/twinGraph/MapReport",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),FROM_CHANNEL:"web","Content-Type":"application/json"},body:JSON.stringify(e),signal:t}),AIInsertMarkdown:e=>fetch(i.a.getHost()+"/uniwim/wimpic/document/insert/markdown",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),"tenant-id":localStorage.getItem("UniWimTenantId"),"Content-Type":"application/json"},body:JSON.stringify(e)}),AISendUser:e=>fetch(i.a.getHost()+"/uniwim/wimpic/document/share/sendUser",{method:"POST",headers:{Authorization:i.a.GetAuthorization(),"tenant-id":localStorage.getItem("UniWimTenantId"),"Content-Type":"application/json"},body:JSON.stringify(e)}),AISuggestedMessage:(e,t=null)=>a.a.get("/wimai/api/suggestedMessage",e,{signal:t}),fileUpload:e=>a.a.post("/uniwim/upload",e),AICreateAgent:e=>a.a.post("/wimai/api/agentConfig/create",e),AIUpdateAgent:e=>a.a.post("/wimai/api/agentConfig/update",e),AIGetAllUsableAgent:(e,t="")=>t?a.a.post("/wimai/api/agentConfigShare/acceptShare?configId="+t,e):a.a.post("/wimai/api/agentConfig/getAllUsable",e),AIDeleteAgent:e=>a.a.post("/wimai/api/agentConfig/delete?configId="+e,{}),AIKnowledgeList:e=>a.a.get("/wimai/api/dataset/getDatasetsVisible",e),AIAgentPolish:(e,t)=>a.a.post("/wimai/api/agentConfig/polish?text="+t,e),AICorrect:e=>a.a.post("/wimai/api/dataset/correction",e),AISessionRecordUpdate(e){a.a.post("/wimai/api/sessionRecord/update",e).finally((e=>{}))},AIAgentMissionQuery:e=>a.a.post("/wimai/api/task/query",e),AIAgentMissionAdd:e=>a.a.post("/wimai/api/task/add",e),AIAgentMissionUpdate:e=>a.a.post("/wimai/api/task/update",e),AIAgentMissionDelete(e){const t=null!=e&&e.id?[null==e?void 0:e.id]:[];return a.a.post("/wimai/api/task/delete",t)},AIAgentMissionDetail:({id:e})=>a.a.get("/wimai/api/task/detail?id="+e),AIAgentMissionExecute:({id:e})=>a.a.post("/wimai/api/task/execute?id="+e),AIAgentMissionHistoryQuery:e=>a.a.post("/wimai/api/taskHistory/query",e),AIAgentMissionLogQuery:({id:e})=>a.a.get("/wimai/api/taskHistory/logs?id="+e),AIAgentSessionRecordPageQuery:e=>a.a.post("/wimai/api/sessionRecord/page",e),AIAgentConfigGetFAQs:({id:e})=>a.a.get("/wimai/api/agentConfig/getFAQs?agentId="+e),AIAgentCorrentAuth:()=>a.a.get("/uniwim/ump/menu/userButtonPermissionList?applicationType=web").then((e=>e)).catch((e=>null)),chatSendToMine:e=>a.a.get("/uniwim/message/chat/sendToMine?type="+e)};t.a=o},"3dfd":function(e,t,n){"use strict";var a=n("fa7d"),i={name:"App",data:()=>({theme:["light","dark","bigscreen1"]}),computed:{aiConfigs(){return this.$store.getters.configs},currentUser(){return this.$store.getters.currentUser},isMobile:()=>a.a.isIos()||a.a.isAndroid()},mounted(){if(this.isMobile)return;const e=a.a.GetQueryString("theme","hash")||a.a.GetQueryString("theme");let t="";if(e&&this.theme.includes(e))t=e,this.$store.commit("INIT_CONFIGS",{...this.aiConfigs,theme:t});else{let e=localStorage.getItem("wimAiTheme"+this.currentUser.tenantId);e=e&&"null"!==e?e:"",e&&localStorage.setItem("wimAiTheme"+this.currentUser.tenantId,e),t=e||this.aiConfigs.theme||"light"}this.$store.commit("INIT_THEME",t)},methods:{}},o=i,r=(n("a2fe"),n("2877")),s=Object(r.a)(o,(function(){var e=this._self._c;return e("div",{attrs:{id:"app"}},[e("keep-alive",{attrs:{exclude:["agentCreate","agentStore","taskManager","taskRecord","monitorTask"]}},[e("router-view")],1)],1)}),[],!1,null,null,null);t.a=s.exports},4:function(e,t){},4360:function(e,t,n){"use strict";var a=n("a026"),i=n("2f62");n("8593"),n("fa7d"),n("14d9");class o{constructor(){this.queue=[],this.isRunning=!1}enqueue(e){this.queue.push(e),this.run()}async run(){if(this.isRunning||0===this.queue.length)return;this.isRunning=!0;const e=this.queue.shift();try{await e()}catch(e){console.error("方法执行出错:",e)}this.isRunning=!1,this.run()}}const r="ws://10.80.20.9:31104/shoufeizjj3/ws/Chat2WithKnowledgeAsync",s={state:{showAi:!1,socketTask:null,isConnected:!1,messages:[],error:null,reconnectAttempts:0,isManualClose:!1,heartbeatTimer:null,reconnectTimer:null,chatLoding:!1,chatPro:"",chatList:[],timesCount:0,audioStatus:"paused",audioCurrentTime:0,menuList:[{name:"交水费",path:"/web/50gerenzhongxin/pay?routername=我要交费",requireBind:!1},{name:"立即交费",path:"/web/50gerenzhongxin/pay?routername=我要交费",requireBind:!1},{name:"水费账单",path:"/web/50gerenzhongxin/sfqd?routername=水费清单",requireBind:!1},{name:"开具发票",path:"/web/50gerenzhongxin/dzfp?routername=开具发票",requireBind:!1},{name:"用水详情",path:"/web/50gerenzhongxin/sxgz?routername=用水详情",requireBind:!1},{name:"我的给水号",path:"/web/50gerenzhongxin/03yhgl?routername=用户管理",requireBind:!1},{name:"我的水号",path:"/web/50gerenzhongxin/03yhgl?routername=用户管理",requireBind:!1},{name:"个人中心",path:"/web/50gerenzhongxin/03yhgl?routername=用户管理",requireBind:!1}]},mutations:{SET_SHOWAI(e,t){e.showAi=t},SET_SOCKET_TASK(e,t){e.socketTask=t},SET_CONNECTION_STATUS(e,t){e.isConnected=t},SET_TIMECOUNT(e,t){e.timesCount=t},ADD_CHATLIST(e,t){e.chatList.push(t)},SET_CHATLIST(e,t){e.chatList=t},SET_CHATLODING(e,t){e.chatLoding=t},SET_MESSAGE(e,t){e.messages=t},SET_CHATPRO(e,t){e.chatPro=t},ADD_CHATPRO(e,t){e.chatPro+=t,e.chatPro=e.chatPro.replace(/努力思考中(?=\S)/g,"")},SET_ERROR(e,t){e.error=t},CLEAR_MESSAGES(e){e.messages=[]},SET_RECONNECT_ATTEMPTS(e,t){e.reconnectAttempts=t},SET_MANUAL_CLOSE(e,t){e.isManualClose=t},SET_HEARTBEAT_TIMER(e,t){e.heartbeatTimer=t},SET_RECONNECT_TIMER(e,t){e.reconnectTimer=t},UPDATE_AUDIO_STATUS(e,t){e.audioStatus=t},UPDATE_CURRENT_TIME(e,t){e.currentTime=t},SET_MENULIST(e,t){e.menuList=e.menuList.concat(t)}},actions:{connectWebSocket:({commit:e,state:t,dispatch:n},a)=>new Promise(((a,i)=>{if(t.socketTask&&t.socketTask.onopen)return a("已有存在的连接");e("SET_MANUAL_CLOSE",!1),e("SET_ERROR",null);const s=new WebSocket(r);e("SET_SOCKET_TASK",s),s.onopen=()=>{e("SET_CONNECTION_STATUS",!0),e("SET_RECONNECT_ATTEMPTS",0),console.log("WebSocket 连接已打开"),n("loginSocket"),n("startHeartbeat")},s.onclose=()=>{e("SET_CONNECTION_STATUS",!1),e("SET_SOCKET_TASK",null),console.log("WebSocket 连接已关闭"),e("SET_CHATPRO",""),e("SET_CHATLODING",!1),t.isManualClose||n("handleReconnect",r)},s.onerror=()=>{e("SET_ERROR",err),console.error("WebSocket 发生错误:",err)};const c=new o;s.onmessage=t=>{c.enqueue((()=>{console.log("进栈消息",t.data),e("SET_MESSAGE",t.data)}))}})),startHeartbeat({commit:e,state:t,dispatch:n}){t.heartbeatTimer&&clearInterval(t.heartbeatTimer);let a={OpenID:localStorage.getItem("OpenId"),Phonenumber:localStorage.getItem("phone"),MessageID:localStorage.getItem("MessageID"),TimeStamp:(new Date).getTime(),BangDCount:4,Message:"Hello!",Voice:""};e("SET_HEARTBEAT_TIMER",setInterval((()=>{t.isConnected&&t.socketTask.send(JSON.stringify(a))}),6e4))},resetHeartbeat({dispatch:e}){e("startHeartbeat")},handleReconnect({commit:e,state:t,dispatch:n},a){if(t.reconnectAttempts>=5)return void console.log("已达最大重连次数，停止重连");t.reconnectTimer&&clearTimeout(t.reconnectTimer);const i=1e3*Math.pow(2,t.reconnectAttempts);console.log(`将在 ${i}ms 后尝试第 ${t.reconnectAttempts+1} 次重连`);const o=setTimeout((()=>{n("connectWebSocket",r).then((()=>e("SET_RECONNECT_ATTEMPTS",0))).catch((n=>{console.error("重连失败:",n),e("SET_RECONNECT_ATTEMPTS",t.reconnectAttempts+1)}))}),i);e("SET_RECONNECT_TIMER",o)},loginSocket({dispatch:e}){let t={OpenID:localStorage.getItem("OpenId"),Phonenumber:localStorage.getItem("phone"),MessageID:localStorage.getItem("MessageID"),TimeStamp:"",BangDCount:4,Message:"",Voice:""},n=localStorage.getItem("userList");n&&n.length>0&&(t.BangDCount=n.length),t.Message="登录",t.TimeStamp=(new Date).getTime(),e("sendMessage",t)},sendMessage:({state:e},t)=>new Promise(((n,a)=>{e.isConnected?(console.log(JSON.stringify(t)),e.socketTask.send(JSON.stringify(t)+"\n")):a("WebSocket 未连接")})),closeWebSocket:({commit:e,state:t})=>new Promise((n=>{e("SET_MANUAL_CLOSE",!0),t.socketTask&&(t.heartbeatTimer&&clearInterval(t.heartbeatTimer),t.reconnectTimer&&clearTimeout(t.reconnectTimer),t.socketTask.close({success:()=>{e("SET_CONNECTION_STATUS",!1),e("SET_SOCKET_TASK",null),n()}}))}))},getters:{}};var c={namespaced:!0,state:{mapData:null},mutations:{SET_MAP_DATA(e,t){e.mapData=t}}};a.default.use(i.a);const u={chat:s,twinmap:c},l={INIT_USER(e,t){e.user=t},INIT_CONFIGS(e,t){e.configs=t},INIT_THEME(e,t){document.documentElement.setAttribute("data-ai-theme",t),e.theme=t},INIT_TEMPLATE(e,t){e.template=t},INIT_AGENT_HISTORY_LIST(e,t){e.agentHistoryList=t},INIT_AGENT_SESSION_ID(e,t){e.agentHistorySessionId=t},WEB_SEARCH_RESULT(e,t){e.websearchResult=t}},d=new i.a.Store({state:{user:{},configs:{},template:[],theme:"light",agentHistoryList:[],agentHistorySessionId:null,websearchResult:[]},actions:{},modules:u,mutations:l,getters:{currentUser:e=>e.user,configs:e=>e.configs,theme:e=>e.theme,template:e=>e.template,agentHistoryList:e=>e.agentHistoryList,agentHistorySessionId:e=>e.agentHistorySessionId,websearchResult:e=>e.websearchResult}});t.a=d},5:function(e,t){},"522b":function(e,t,n){},"56d7":function(e,t,n){"use strict";n.r(t),function(e){n("e9f5"),n("f665"),n("7d54");var t=n("a026"),a=n("4302"),i=(n("cc04"),n("3dfd")),o=n("a18c"),r=n("aa11"),s=n("4360"),c=(n("afe7"),n("522b"),n("8593")),u=n("c1df"),l=n.n(u),d=n("fa7d"),p=n("698a"),m=n("365c"),h=n("f3bd"),g=n("3a34"),f=n.n(g);t.default.use(a.a,{}),t.default.use(r.a),window.WIMAI_VERSION=n("9224").version,window.$=e,window.moment=l.a,t.default.prototype.$utils=d.a,t.default.prototype.$native=p.a,t.default.prototype.$saasApi=m.a;const w=new h.a({offset:1292976e6});t.default.prototype.$snowflake=w,t.default.config.productionTip=!1;localStorage.getItem("debug123456")&&new f.a;(()=>{let t=[],n={theme:"light"};return new Promise((a=>{Promise.all([c.a.initUserInfo(),m.a.AIConfigList(),m.a.AIMenuTemplate()]).then((([e,n,a])=>{var i;s.a.commit("INIT_USER",e),n&&n.length&&(t=n),null!=a&&null!==(i=a.rows)&&void 0!==i&&i.length&&s.a.commit("INIT_TEMPLATE",a.rows)})).finally((async()=>{if(t.forEach((e=>{if("template"===e.code)if(e.value)try{e.value=JSON.parse(e.value)}catch(t){e.value=[]}else e.value=[];if("modelDesc"===e.code)if(e.value)try{e.value=JSON.parse(e.value)}catch(t){e.value={}}else e.value={};if("sceneType"===e.code)if(e.value)try{e.value=JSON.parse(e.value)}catch(t){e.value={}}else e.value={};if("applicationScene"===e.code)if(e.value)try{e.value=JSON.parse(e.value)}catch(t){e.value={}}else e.value={};if("customReplaceWords"===e.code){if(e.value)try{e.value=JSON.parse(e.value)}catch(t){e.value=[]}else e.value=[];window.uniwimAICustomReplaceWords=e.value}n[e.code]=e.value})),n.title&&(document.title=n.title),n.favicon&&e("#ico").attr("href",n.favicon),"1"===n.correctAuth){let e="1";try{(await m.a.AIAgentCorrentAuth()).find((e=>"wimai_correct"===e.link))&&(e="0")}catch(e){}n.hideCorrect=e}s.a.commit("INIT_CONFIGS",n),a()}))}))})().then((()=>{new t.default({router:o.a,store:s.a,render:e=>e(i.a),data:()=>({platform:""})}).$mount("#app")}))}.call(this,n("1157"))},6:function(e,t){},"61f7":function(e,t,n){"use strict";function a(e){if("boolean"==typeof e)return!1;if("number"==typeof e)return!1;if(e instanceof Array){if(0==e.length)return!0}else{if(!(e instanceof Object))return"null"==e||null==e||void 0===e||"undefined"==e||null==e||""==e;if("{}"===JSON.stringify(e))return!0}return!1}n.d(t,"a",(function(){return a}))},"698a":function(e,t,n){"use strict";n("e9f5"),n("ab43");var a=n("fa7d");const i={},o={},r=["search","collect","btDisConnected","btReceiver","btGetParams","manualPostRes","handPostResp","gpsnPostResp","btSound","btScan","handCollectResp","bluetooth","onBtState","interceptBack","bgNavi","getLocation","btUpdate","screenoff","screenon","bgNaviClose","btRawData","onSingleDownload","sqlite_close","unzip","btNoiseLog","get_blan","rpc_blan","onfmkz_sscx","connectUHFTag","disconnectUHFTag","readUHFTag","nfcData","tlv_onDataReport","qx_rtk","sound_record_stop","onAppEnterBackground","onAppEnterForeground","tlv_dataQuery","onNfcWriteData","onBtScanClassic","onShortVideoEvents","onShortVideoRecord","analRelated","onReceiveMsg","wechat_login","multi_file","im_onEventListener","im_unreadnum","speech_recognizer","onLinkRestore","onClickMessage","onReceiveMessage","onRMQUserState","onRMQUserAllState","onRMQMessage","onReconnect","onShutdown","sangfor_change","onUnzip","request_permission"];window.response=({msgid:e,method:t,params:n})=>{r.some((e=>{if(e===t)return o[t]=n,!0}));const a=i[e];if(a){!0!==a(n)&&delete i[e]}else(function(e){if(null!=window.frames&&0!=window.frames.length)for(let t=0;t<window.frames.length;t++)window.frames[t].postMessage(e,"*")})({msgid:e,method:t,params:n}),delete i[e]},window.response2=({method:e,cb:t})=>{e&&t&&Object.defineProperty(o,e,{configurable:!0,enumerable:!0,set(n){n&&(t(n),delete i[e])}})},window.changeTheme=e=>{let t=location.hash.split("?"),n=t[1]||"";if(!n)return t[1]="theme="+e,void(location.hash=t.join("?"));let a={};n=n.split("&");for(let e=0;e<n.length;e++){let[t,i]=n[e].split("=");a[t]=i}a.theme=e,t[1]=Object.entries(a).map((([e,t])=>`${e}=${t}`)).join("&"),location.replace(t.join("?"))},window.addEventListener("message",(function(e){let t=e.data;if(null==t)return;let n=t.msgid,a=t.method,i=t.params;null!=a&&null!=i&&window.response({msgid:n||"",method:a,params:i})}),!1);let s={};["save","delete","query","rotate","startNavi","getLocation","isCollected","sysInfo","hideHeader","scanCode","copy","share","userInfo","userBehaviorRecord","search","openWebview","closeWebview","collect","nfcData","goback","refreshBadge","signature","saveLocalData","queryLocalData","deleteLocalData","bluetooth","btDisConnected","bluetoothState","manualPost","btGetParams","manualPostRes","btSound","soundPlay","vibratorSound","dictList","btScan","shareFile","btUpdate","spectrum","trace","delFile","interceptBack","bgNavi","keepScreenOn","video","showVideo","sound","screenshot","bgNaviClose","regeocode","watermarkCamera","download","sqlite_execsql","sqlite_query","multi_file","v88s_zdsjcx","v88s_params","v88s_tc","logger","singleDownload","clearCache","resSave","sqlite_close","unzip","queryCustomer","get_blan","rpc_blan","rfm_getDevices","rfm_openDoor","fmkz_params","fmkz_sscx","fmkz_tc","analRelated","ocr_watermeter","eranntex_params","connectUHFTag","disconnectUHFTag","readUHFTag","openBluetooth","tlv_params","tlv_dataQuery","tlv_tc","tlv_noiseAudio","r800c_printer","qx_rtk","ycbDatasUpload","ycbDatasDebug","spon_audio","get_local_file","sound_record_start","sound_record_stop","sound_record_cancel","getGpsState","openScheme","wenzhen_get_data","intent_open_url","nfcWriteData","b32_printer_connect","b32_printer_disconnect","b32_printer","bt_scan_classic","change_sn","deviceList","appConfig","wechat_login","appleSignIn","im_init","im_login","im_openPage","im_getInfo","screenoff","screenon","shortVideo","open_miniprogram","im_unreadnum","speech_recognizer","get_imei","waterMarkBg","onLinkRestore","appBadges","shortVideoRecord","faceVerify","header","loginStatusChange","local_res_zip_version","sangfor_login","sangfor_status","resetApp","finish_app","reloadWebView","changeClient","chat_room_join","check_permission","openFileBySystem","niimbot_m2_printer_connect","niimbot_m2_printer","niimbot_m2_printer_disconnect","request_permission","kill_app"].forEach((e=>{s[e]=({params:t,cb:n})=>{(({cb:e,method:t,params:n})=>{let o=parseInt(Math.random()*Math.pow(10,17))+(new Date).getTime();"getLocation"===t&&(o="mue_getLocation",n&&n.msgid&&(o=n.msgid)),i[o]=e;try{let e=JSON.stringify({msgid:o,method:t,params:n});a.a.isIosAndMac()?window.webkit.messageHandlers.postMessage.postMessage(e):window.native.postMessage(e)}catch(e){console.log(e)}})({method:e,params:t,cb:n})}})),t.a=s},8593:function(e,t,n){"use strict";var a=n("a27e"),i=n("4360");const o={initUserInfo:()=>new Promise(((e,t)=>{a.a.get("/uniwim/ump/currUserInfo",{}).then((t=>{window.WIMAI_CurrentUser=t,t?(i.a.commit("INIT_USER",t),e(t)):e({tenantId:"5d89917712441d7a5073058c"})})).catch((t=>{e({tenantId:"5d89917712441d7a5073058c"})}))}))};t.a=o},9224:function(e){e.exports=JSON.parse('{"name":"hdkj-uniwim-ai-web","version":"0.0.96","private":true,"scripts":{"clean":"rm -rf node_modules","serve":"vue-cli-service serve","build":"vue-cli-service build","lint":"vue-cli-service lint","hdlint":"eslint --fix --ext .js,.vue src"},"dependencies":{"axios":"^0.22.0","core-js":"^3.8.3","cue":"git+https://hub.hddznet.com/uniplatform/cue.git","file-saver":"^2.0.5","html-docx-js":"^0.3.1","jquery":"3.6.1","lodash":"^4.17.21","moment":"^2.29.4","pinyin-pro":"^3.26.0","snowflake-id":"^1.1.0","speak-tts":"^2.0.8","vconsole":"^3.15.1","vue":"^2.6.11","vue-codemirror":"4.0.6","vue-markdown":"^2.2.4","vue-qr":"^4.0.9","vue-router":"^3.5.1","vue2-leaflet":"2.5.2","vuex":"^3.6.2"},"devDependencies":{"@babel/polyfill":"^7.12.1","@vue/cli-plugin-babel":"~4.5.0","@vue/cli-plugin-eslint":"~4.5.13","@vue/cli-service":"~4.5.0","@vue/eslint-config-prettier":"^6.0.0","babel-eslint":"^10.1.0","compression-webpack-plugin":"^3.1.0","eslint":"^6.7.2","eslint-plugin-prettier":"^3.3.1","eslint-plugin-vue":"^6.2.2","less":"^3.9.0","less-loader":"^5.0.0","postcss":"^8.0.0","postcss-preset-env":"^6.0.0","prettier":"^2.2.1","style-resources-loader":"^1.5.0","terser-webpack-plugin":"^4.2.3","vue-cli-plugin-style-resources-loader":"^0.1.5","vue-template-compiler":"^2.6.11","webpack-bundle-analyzer":"^4.9.0"},"main":"index.js","repository":"https://hub.hddznet.com/uniwim/uniwim-ai-web.git","author":"“hechentao” <“<EMAIL>”>","license":"MIT"}')},a18c:function(e,t,n){"use strict";var a=n("a026"),i=n("8c4f");const o=[{path:"/",name:"",redirect:"/ai"},{path:"/ai",name:"ai",component:()=>n.e("chunk-620caac4").then(n.bind(null,"aa58")),props:{isRouter:!0}},{path:"/ai/:mode",name:"ai-iframe",component:()=>n.e("chunk-620caac4").then(n.bind(null,"aa58")),props:{isRouter:!0}},{path:"/config",name:"config",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-6ad94c94")]).then(n.bind(null,"eb43"))},{path:"/ai/web/popup",name:"web-popup",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-0df06a5a"),n.e("chunk-f9b9e012"),n.e("chunk-1c33c228")]).then(n.bind(null,"cc03"))},{path:"/agent",name:"agent",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-0df06a5a"),n.e("chunk-b85d6058"),n.e("chunk-6db3fc7c"),n.e("chunk-3c4a3fdb")]).then(n.bind(null,"4921")),props:{isRouter:!0}},{path:"/agentCreate",name:"agentCreate",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-0df06a5a"),n.e("chunk-b85d6058"),n.e("chunk-6db3fc7c")]).then(n.bind(null,"1afb")),props:{isRouter:!0}},{path:"/agentStore",name:"agentStore",component:()=>n.e("chunk-6f2acad0").then(n.bind(null,"8206")),props:{isRouter:!0}},{path:"/monitorTask",name:"monitorTask",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-0df06a5a"),n.e("chunk-b85d6058"),n.e("chunk-7146eae5")]).then(n.bind(null,"b040")),props:{isRouter:!0}},{path:"/taskManager",name:"taskManager",component:()=>n.e("chunk-555f4130").then(n.bind(null,"8345")),props:{isRouter:!0}},{path:"/taskRecord",name:"taskRecord",component:()=>n.e("chunk-618b4ecc").then(n.bind(null,"55b1")),props:{isRouter:!0}},{path:"/questionList",name:"questionList",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-0df06a5a"),n.e("chunk-b85d6058"),n.e("chunk-435e3d73")]).then(n.bind(null,"d5d1b")),props:{isRouter:!0}},{path:"/postmessage-demo",name:"postmessage-demo",component:()=>Promise.all([n.e("chunk-commons"),n.e("chunk-e8b89456")]).then(n.bind(null,"7660"))}];a.default.use(i.a);const r=new i.a({routes:o});t.a=r},a2fe:function(e,t,n){"use strict";n("25ff")},aa11:function(e,t,n){"use strict";n("14d9"),n("e9f5"),n("7d54");var a=n("037d"),i=n("365c"),o=n("fa7d");function r(){const e=o.a.getHost();return!(-1===location.origin.indexOf("dlmeasure")&&-1===e.indexOf("dlmeasure"))}function s(){let e=[];try{let t=sessionStorage.getItem("uniwimAiTrackingQueue");t=JSON.parse(t),t&&Array.isArray(t)&&t.forEach((t=>{e.push(t)}))}catch(e){}return e}function c(e){sessionStorage.setItem("uniwimAiTrackingQueue",JSON.stringify(e))}async function u(){if(!r())return;const e=s();if(e.length>0){const t=e[0],n=await async function(e){try{const t=await i.a.accessLogSaveLog(e);if(t&&0===t.Code){const t=s(),n=t.findIndex((t=>t.uuid===e.uuid));return-1!==n&&(t.splice(n,1),c(t)),!0}return console.error("埋点数据发送失败:",t.statusText),!1}catch(e){return console.error("埋点数据发送出错:",e),!1}}(t);n&&(e.shift(),c(e))}setTimeout(u,3e3)}t.a=function(e){u(),e.prototype.$track=function(e={}){if(!r())return;const t={...e,applicationId:e.functionId,applicationName:"一诺AI",path:e.path||"",result:1,type:1,uuid:Object(a.g)()},n=s();n.push(t),c(n)}}},fa7d:function(e,t,n){"use strict";n("e9f5"),n("7d54"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3");var a=n("c1df"),i=n.n(a);const o={isHdkj:()=>navigator.userAgent.toLowerCase().indexOf("hdkj")>-1,shortenText(e,t=10,n=10){if(e.length<=t+n)return e;return`${e.slice(0,t)}...${e.slice(-n)}`},GetQueryString(e,t){let n;if(n="hash"===t?window.location.hash.split("?")[1]:window.location.search.substr(1),!n)return null;let a=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),i=n.match(a);return null!=i?decodeURIComponent(window.HD.EncodeSearchKey(i[2])):null},GetAuthorization(){let e=o.GetQueryString("uniwater_utoken","hash")||o.GetQueryString("uniwater_utoken")||o.GetQueryString("token","hash")||o.GetQueryString("token")||o.GetQueryString("utoken","hash")||o.GetQueryString("utoken");if(e&&"null"!==e);else if(sessionStorage.getItem("UniWimAuthorization")){let t=sessionStorage.getItem("UniWimAuthorization");e=t&&"null"!==t?t:""}else if(localStorage.getItem("UniWimAuthorization")){let t=localStorage.getItem("UniWimAuthorization");e=t&&"null"!==t?t:""}return sessionStorage.removeItem("UniWimAuthorization"),e&&sessionStorage.setItem("UniWimAuthorization",e),e},isIosAndMac(){const e=navigator.userAgent;return!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)||!!e.match(/\(M[^;]+; Intel Mac OS X/)},isIos(){const e=navigator.userAgent;return/iPad|iPhone|iPod/.test(e)&&!/Macintosh/.test(e)},isSafari(){const e=navigator.userAgent;return/Safari/.test(e)&&!/Chrome/.test(e)},isAndroid(){const e=navigator.userAgent;return e.indexOf("Android")>-1||e.indexOf("Adr")>-1},isMobile:()=>o.isIos()||o.isAndroid(),setRootVars(e){Object.keys(e).forEach((t=>{document.documentElement.style.setProperty(t,e[t])}))},isJson(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(e){return!1}},extractLandlineNumbers(e){let t=e.match(/(?:\(?0\d{2,3}\)?[- ][1-9]\d{6,7}|\b0\d{9,10}\b)(?:[-\u8f6c#][1-9]\d{0,3})?/g)||[];let n=e.match(/\d{4}-\d{6}/g)||[];return[...new Set(t.concat(n))]},removeHTMLTags:e=>e.replace(/<[^>]*>/g,""),cosineSimilarity(e,t){const n=e=>{const t=e.split(""),n={};for(const e of t)n[e]=(n[e]||0)+1;return n},a=n(e),i=n(t);let o=0;for(const e in a)i[e]&&(o+=a[e]*i[e]);const r=e=>{let t=0;for(const n in e)t+=e[n]**2;return Math.sqrt(t)};return o/(r(a)*r(i))},getHost(){let e=window.location,t=o.GetQueryString("host")||o.GetQueryString("host","hash"),n=o.GetQueryString("load")||o.GetQueryString("load","hash");if(!n||"disk"!==n)return"";if(t?(t=decodeURIComponent(t),sessionStorage.setItem("host",t)):t=sessionStorage.getItem("host")||"",t=decodeURIComponent(t),"file://"===e.origin.toLowerCase())return t;let a=e.port.substring(1);return new RegExp(`^${e.origin}/packages/${a}/`,"i").test(e.href)?t:e.href.startsWith("http")&&!e.href.startsWith(`${e.origin}/packages/${a}/`)?t||e.origin:""},removeParameterFromUrl(e,t){let n=e.replace(new RegExp(t+"=[^&]*(?=&|$)"),"");return n=n.replace(/&+/g,"&"),n=n.replace(/^(&|\?)/,""),n=n.replace(/(&|\?)$/,""),n.endsWith("?")&&(n=n.slice(0,-1)),n=n.replace(/\?&/,"?"),n},getUrlParameter(e,t){const n=new RegExp(`[?&]${t}=([^&#]*)`),a=e.match(n);return null===a?null:decodeURIComponent(a[1].replace(/\+/g," "))},formatTime(e){const t=i()(),n=i()(e);return t.isSame(n,"day")?n.format("HH:mm:ss"):t.isSame(n,"year")?n.format("MM-DD HH:mm:ss"):n.format("YYYY-MM-DD HH:mm:ss")}};t.a=o}});