import sysCryptoJS from 'crypto-js';
const key = 'hdkj1234567890yh'
const iv = '1234567812345678'
// 加密模式：CBC
export const aesEncrypt = (text) => {
    return sysCryptoJS.AES.encrypt(text, sysCryptoJS.enc.Utf8.parse(key), { iv: sysCryptoJS.enc.Utf8.parse(iv) }).toString()
}

export const aesDecrypt = (text) => {
    return sysCryptoJS.AES.decrypt(text, sysCryptoJS.enc.Utf8.parse(key), { iv: sysCryptoJS.enc.Utf8.parse(iv) }).toString(sysCryptoJS.enc.Utf8)
}
