'use strict';
import moment from "moment";
import wtf from "@/assets/images/wtf.png"
import docx from "@/assets/images/docx.png"
import excel from "@/assets/images/excel.png"
import pdf from "@/assets/images/pdf.png"
import png from "@/assets/images/png.png"
// src/utils/version.js
import { version } from '../../../package.json';

export const getAppVersion = () => {
  return version;
};

const utils = {
    require(imgPath) {
        try {
            const handlePath = imgPath.replace('@', '/src');
            return new URL(handlePath, import.meta.url).href;
        } catch (error) {
            console.warn(error);
        }
    },
    getAppVersion(){
        return version;
    },
    isHdkj() {
        const u = navigator.userAgent.toLowerCase();
        return u.indexOf("hdkj") > -1
    },
    // 字符串缩短
    shortenText(text, startLength = 10, endLength = 10) {
        if (text.length <= startLength + endLength) {
            return text;
        }
        const start = text.slice(0, startLength);
        const end = text.slice(-endLength);
        return `${start}...${end}`;
    },
    // 对查询关键字中的特殊字符进行编码
    encodeSearchKey(key) {
        const encodeArr = [
            { code: '%', encode: '%25' },
            // { code: '?', encode: '%3F' },
            { code: '#', encode: '%23' },
            // { code: '&', encode: '%26' },
            // { code: '=', encode: '%3D' }
        ];
        return key.replace(/[%#]/g, ($, index, str) => {
            for (const k of encodeArr) {
                if (k.code === $) {
                    return k.encode;
                }
            }
        });
    },
    /**
     * 根据文件后缀或Base64前缀，判断一个字符串是否代表一张图片。
     *
     * @param {string | null | undefined} inputString - 要检查的字符串 (URL, 文件名, 或 Base64 数据)。
     * @returns {boolean} - 如果是图片，则返回 true，否则返回 false。
     */
    isImage(inputString) {
        // 1. 安全检查：处理 null、undefined 或非字符串输入
        if (!inputString || typeof inputString !== 'string') {
            return false;
        }

        // 2. Base64 图片检查：检查是否以 "data:image/" 开头
        // 这是最可靠、最高效的Base64图片判断方式。
        if (inputString.startsWith('data:image/')) {
            return true;
        }

        // 3. 文件后缀检查
        // 创建一个包含常见图片后缀的 Set，以便快速查找 (比数组的 .includes() 更快)
        const imageExtensions = new Set([
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico', 'tiff'
        ]);

        // 提取后缀
        // 使用 lastIndexOf('.') 来正确处理包含多个点的文件名 (e.g., "archive.v1.2.jpg")
        const lastDotIndex = inputString.lastIndexOf('.');
        if (lastDotIndex === -1) {
            // 如果没有点，就没有后缀
            return false;
        }

        // 获取点之后的所有内容，并转换为小写以进行不区分大小写的比较
        const suffix = inputString.substring(lastDotIndex + 1).toLowerCase();

        // 检查提取出的后缀是否存在于我们的 Set 中
        return imageExtensions.has(suffix);
    },
    /**
     * 获取 Base64 编码的图片的原始尺寸。
     *
     * @param {string} base64String - 完整的 Base64 Data URL (e.g., "data:image/png;base64,...").
     * @returns {Promise<{ width: number; height: number; }>} - 返回一个包含宽度和高度的 Promise 对象。
     * @throws {Error} - 如果输入的字符串无效或图片数据损坏，则抛出异常。
     */
    getBase64ImageDimensions(base64String) {
        return new Promise((resolve, reject) => {
            // 1. 输入验证：确保输入是有效的 Base64 图片 Data URL
            if (!base64String || typeof base64String !== 'string' || !base64String.startsWith('data:image/')) {
            // 如果格式不正确，立即拒绝 Promise
                return reject(new Error('无效的输入：提供的字符串不是一个有效的 Base64 图片。'));
            }

            // 2. 创建一个内存中的 Image 对象
            const img = new Image();

            // 3. 设置 onload 事件处理器
            // 这个事件会在图片成功解码并加载后触发
            img.onload = () => {
                // 成功时，解析 Promise 并返回图片的原始尺寸
                resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                });
            };

            // 4. 设置 onerror 事件处理器
            // 如果 Base64 数据损坏或格式不被支持，会触发这个事件
            img.onerror = () => {
                // 失败时，拒绝 Promise 并提供错误信息
                reject(new Error('无法加载 Base64 图片。数据可能已损坏或格式不受支持。'));
            };

            // 5. 将 Base64 字符串设置为图片的 src
            // 这一步会启动浏览器的解码和加载过程
            img.src = base64String;
        });
        },
    /**
     * 将在线图片的URL转换为Base64字符串 (Data URL)。
     * @param {string} url - 要转换的在线图片的URL。
     * @returns {Promise<string>} - 返回一个包含Base64格式字符串的Promise。
     * @throws {Error} - 如果网络请求失败或发生其他错误，则抛出异常。
     */
    async imageUrlToBase64(url) {
        // 1. 使用 fetch API 异步请求图片资源
        const response = await fetch(url);

        // 2. 检查网络请求是否成功 (例如，处理 404 Not Found)
        if (!response.ok) {
            throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
        }

        // 3. 将响应体转换为二进制大对象 (Blob)
        const imageBlob = await response.blob();

        // 4. 使用 FileReader 将 Blob 转换为 Base64
        // 这是一个回调式的API，我们将其包装成Promise以便使用async/await
        return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        // 读取成功完成时，Promise成功并返回结果
        reader.onloadend = () => {
            resolve(reader.result);
            };

            // 读取发生错误时，Promise失败
            reader.onerror = (error) => {
            reject(error);
            };

            // 开始读取Blob，并将其编码为Base64 (Data URL)
            reader.readAsDataURL(imageBlob);
        });
    },
    incrementVersion(version, type = 'patch') {
            // 验证版本号格式
            const versionRegex = /^\d+\.\d+\.\d+$/;
            if (!versionRegex.test(version)) {
                throw new Error('版本号格式不正确，请使用 x.y.z 格式（如 1.0.0）');
            }

            // 将版本号拆分为数组
            const parts = version.split('.').map(Number);
            
            // 根据类型自增相应的版本号部分
            switch (type.toLowerCase()) {
                case 'major': // 主版本号自增，次版本号和修订号重置为0
                    parts[0]++;
                    parts[1] = 0;
                    parts[2] = 0;
                    break;
                case 'minor': // 次版本号自增，修订号重置为0
                    parts[1]++;
                    parts[2] = 0;
                    break;
                case 'patch': // 修订号自增（默认）
                    parts[2]++;
                    break;
                default:
                    throw new Error('自增类型不正确，请使用 major、minor 或 patch');
            }

            // 拼接成新的版本号字符串
            return parts.join('.');
        },
    //获取路由参数
    GetQueryString(name, type) {
        let target;
        if (type === "hash") {
            target = window.location.hash.split("?")[1];
        } else {
            target = window.location.search.substr(1);
        }
        if (!target) {
            return null;
        }
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        let r = target.match(reg);
        if (r != null) {
            return decodeURIComponent(window.HD ? window.HD.EncodeSearchKey(r[2]) : utils.encodeSearchKey(r[2]));
        }
        return null;
    },
    // 获取token
    GetAuthorization() {
        let urlToken = utils.GetQueryString('uniwater_utoken', 'hash')
            || utils.GetQueryString('uniwater_utoken')
            || utils.GetQueryString('token', 'hash')
            || utils.GetQueryString('token',)
            || utils.GetQueryString('utoken', 'hash')
            || utils.GetQueryString('utoken',);
        if (urlToken && urlToken !== 'null') {

        }
        else if (sessionStorage.getItem('UniWimAuthorization')) {
            let token = sessionStorage.getItem('UniWimAuthorization');
            urlToken = token && token !== 'null' ? token : '';
        }
        else if (localStorage.getItem('UniWimAuthorization')) {
            let token = localStorage.getItem('UniWimAuthorization');
            urlToken = token && token !== 'null' ? token : '';
        }
        sessionStorage.removeItem('UniWimAuthorization');
        urlToken && sessionStorage.setItem('UniWimAuthorization', urlToken);
        return urlToken
    },
    /**
     * 判断是否是ios
     */
    isIosAndMac() {
        const u = navigator.userAgent;
        return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) || !!u.match(/\(M[^;]+; Intel Mac OS X/);
    },
    isIos() {
        const u = navigator.userAgent;
        return /iPad|iPhone|iPod/.test(u) && !/Macintosh/.test(u);
    },
    isSafari() {
        const u = navigator.userAgent;
        return /Safari/.test(u) && !/Chrome/.test(u);
    },
    isAndroid() {
        const u = navigator.userAgent;
        return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
    },
    isMobile() {
        return utils.isIos() || utils.isAndroid();
    },
    // 动态修改var变量
    setRootVars(obj) {
        Object.keys(obj).forEach((key) => {
            document.documentElement.style.setProperty(key, obj[key]);
        })
    },
    isJson(str) {
        if (typeof str == 'string') {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == 'object' && obj) {
                    return true;
                } else {
                    return false;
                }
            } catch (e) {
                return false;
            }
        }
    },
    extractLandlineNumbers(text) {
        const regex = /(?:\(?0\d{2,3}\)?[- ][1-9]\d{6,7}|\b0\d{9,10}\b)(?:[-\u8f6c#][1-9]\d{0,3})?/g;
        let phoneArray = text.match(regex) || []
        //特殊处理
        const regexPhone = /\d{4}-\d{6}/g;
        let phoneArray2 = text.match(regexPhone) || []
        return [...new Set(phoneArray.concat(phoneArray2))];
    },
    removeHTMLTags(str) {
        return str.replace(/<[^>]*>/g, '');
    },
    cosineSimilarity(str1, str2) {
        // 将字符串转换为词频向量
        const getVector = (str) => {
            const words = str.split("");
            const vector = {};
            for (const word of words) {
                vector[word] = (vector[word] || 0) + 1;
            }
            return vector;
        };

        const vector1 = getVector(str1);
        const vector2 = getVector(str2);

        // 计算点积
        let dotProduct = 0;
        for (const key in vector1) {
            if (vector2[key]) {
                dotProduct += vector1[key] * vector2[key];
            }
        }

        // 计算向量模长
        const magnitude = (vector) => {
            let sum = 0;
            for (const key in vector) {
                sum += vector[key] ** 2;
            }
            return Math.sqrt(sum);
        };

        const magnitude1 = magnitude(vector1);
        const magnitude2 = magnitude(vector2);

        // 计算余弦相似度
        return dotProduct / (magnitude1 * magnitude2);
    },
    getHost(){
        let location = window.location;
        let host = utils.GetQueryString("host") || utils.GetQueryString("host", "hash");
        let load = utils.GetQueryString("load") || utils.GetQueryString("load", "hash");

        // 非离线环境不处理host逻辑
        if(!load || load !== 'disk'){
            return ""
        }
        if(host){
            host = decodeURIComponent(host);
            sessionStorage.setItem("host", host);
        }
        else{
            host = sessionStorage.getItem("host") || "";
        }
        host = decodeURIComponent(host)

        //匹配安卓host
        if(location.origin.toLowerCase() === "file://"){
            return host;
        }

        //匹配ios host
        let code = location.port.substring(1);
        let regex = new RegExp(`^${location.origin}/packages/${code}/`, "i");
        if(regex.test(location.href)){
            return host;
        }

        //匹配在线http模式
        if(location.href.startsWith('http') && !location.href.startsWith(`${location.origin}/packages/${code}/`)){
            return host || location.origin
        }

        return "";
    },
    // 替换字符串中xxx=xxx内容
    removeParameterFromUrl(url, key) {
        // 先移除指定 key 的参数
        let newUrl = url.replace(new RegExp(`${key}=[^&]*(?=&|$)`), '');
        // 移除多余的 & 符号
        newUrl = newUrl.replace(/&+/g, '&');
        // 移除开头多余的 &
        newUrl = newUrl.replace(/^(&|\?)/, '');
        // 移除结尾多余的 &
        newUrl = newUrl.replace(/(&|\?)$/, '');
        // 如果只剩下一个 ?，则移除它
        if (newUrl.endsWith('?')) {
            newUrl = newUrl.slice(0, -1);
        }
        // 处理只剩 ? 或 & 分隔符的情况
        newUrl = newUrl.replace(/\?&/, '?');
        return newUrl;
    },
    // 获取指定参数值
    getUrlParameter(url, paramName) {
        const regex = new RegExp(`[?&]${paramName}=([^&#]*)`);
        const results = url.match(regex);
        return results === null ? null : decodeURIComponent(results[1].replace(/\+/g, ' '));
    },
    /**
     * 从配置对象中提取所有 ${variable} 格式的变量引用
     * @param {Object|Array|string} config - 配置对象、数组或字符串
     * @returns {Set<string>} 包含所有变量名的Set集合
     */
    extractTemplateVariables(config) {
        const variables = new Set();
        
        // 递归函数处理不同类型的值
        function processValue(value) {
            if (typeof value === 'string') {
                // 正则表达式匹配 ${variable} 格式
                const regex = /\$\{([^}]+)\}/g;
                let match;
                
                while ((match = regex.exec(value)) !== null) {
                    variables.add(match[1]); // 添加变量名（不包含${}）
                }
            } else if (typeof value === 'object' && value !== null) {
                // 处理对象或数组
                if (Array.isArray(value)) {
                    value.forEach(processValue);
                } else {
                    Object.values(value).forEach(processValue);
                }
            }
        }
        
        // 开始处理配置
        processValue(config);
        
        return variables;
    },

    formatTime(time) {
        const now = moment();
        const targetTime = moment(time);

        // 判断是否为今天
        if (now.isSame(targetTime, 'day')) {
            return targetTime.format('HH:mm:ss');
        }

        // 判断是否为今年
        if (now.isSame(targetTime, 'year')) {
            return targetTime.format('MM-DD HH:mm:ss');
        }

        // 其他情况，显示完整年份
        return targetTime.format('YYYY-MM-DD HH:mm:ss');
    },

    // 文件大小换算
    formatSize(num) {
        if (!num) return '0KB';
        const kb = num / 1024;
        let size = 0;
        let unit = 'KB';
        if (kb >= 1024) {
            size = kb / 1024;
            unit = 'M';
        } else {
            size = kb;
        }
        return size.toFixed(2) + unit;
    },

    // 根据文件名称判断文件类型
    getFileIcon(name) {
        if (!name) {
            return wtf
        }
        const nameArr = name.split('.')
        if(nameArr[nameArr.length - 1]){
            const fileType = nameArr[nameArr.length - 1]
            if (/(doc|docx)$/.test(fileType.toLowerCase())) {
                return docx
            } else if (/(xls|xlsx)$/.test(fileType.toLowerCase())) {
                return excel
            } else if (/(pdf)$/.test(fileType.toLowerCase())) {
                return pdf
            } else if (/(png|jpg|jpeg|gif)$/.test(fileType.toLowerCase())) {
                return png
            }else {
                return wtf
            }
        }
        return wtf
    },

};

export default utils;
