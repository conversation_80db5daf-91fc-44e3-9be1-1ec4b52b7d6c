============================
Robot Framework 4.0.2 beta 1
============================

.. default-role:: code

`Robot Framework`_ 4.0.2 is the second and the last planned bug fix release
in the Robot Framework 4.0.x series. This beta release contains fixes to all
issues that have been reported so far, but if more problems are encountered
they can still be fixed before the final Robot Framework 4.0.2 release.

Questions and comments related to the release can be sent to the
`robotframework-users`_ mailing list or to `Robot Framework Slack`_,
and possible bugs submitted to the `issue tracker`_.

If you have pip_ installed, just run

::

   pip install --pre --upgrade robotframework

to install the latest available release or use

::

   pip install robotframework==4.0.2b1

to install exactly this version. Alternatively you can download the source
distribution from PyPI_ and install it manually. For more details and other
installation approaches, see the `installation instructions`_.

Robot Framework 4.0.2 beta 1 was released on Thursday May 6, 2021.
The final Robot Framework 4.0.1 release is planned for Tuesday May 11, 2021.
That is exactly two months after the original `Robot Framework 4.0`__ release.

__ https://github.com/robotframework/robotframework/blob/master/doc/releasenotes/rf-4.0.rst
.. _Robot Framework: http://robotframework.org
.. _Robot Framework Foundation: http://robotframework.org/foundation
.. _pip: http://pip-installer.org
.. _PyPI: https://pypi.python.org/pypi/robotframework
.. _issue tracker milestone: https://github.com/robotframework/robotframework/issues?q=milestone%3Av4.0.2
.. _issue tracker: https://github.com/robotframework/robotframework/issues
.. _robotframework-users: http://groups.google.com/group/robotframework-users
.. _Robot Framework Slack: https://robotframework-slack-invite.herokuapp.com
.. _installation instructions: ../../INSTALL.rst

.. contents::
   :depth: 2
   :local:

Most important enhancements
===========================

Fix using using `Union` containing generics as type hint
--------------------------------------------------------

`Robot Framework 4.0.1`__ fine-tuned how using Union__ as type hint used in
automatic argument conversion works. Changes themselves were fine, but they
introduced a regression (`#3931`_) making it impossible to use `Union` containing
`subscribed generics`__ such as:

.. code:: python

    def example(arg: Union[List[int], Dict[str, int]]):
        # ...

__ https://github.com/robotframework/robotframework/blob/master/doc/releasenotes/rf-4.0.1.rst#avoid-argument-conversion-if-given-argument-has-one-of-the-accepted-types
__ https://docs.python.org/3/library/typing.html#typing.Union
__ https://docs.python.org/3/library/typing.html#generics

Acknowledgements
================

Robot Framework 4.0.2 development has been sponsored by the `Robot Framework Foundation`_
and its `close to 50 member organizations <https://robotframework.org/foundation/#members>`_.
In addition to that we got one nice contribution by the open source community:

- `miktuy <https://github.com/miktuy>`__ fixed including `sourcename` attribute in
  output.xml generated by Rebot (`#3941`_)

Big thanks to sponsors, contributors and to everyone else who has reported problems or
otherwise helped to make Robot Framework better!

| `Pekka Klärck <https://github.com/pekkaklarck>`__
| Robot Framework Lead Developer

Full list of fixes and enhancements
===================================

.. list-table::
    :header-rows: 1

    * - ID
      - Type
      - Priority
      - Summary
      - Added
    * - `#3931`_
      - bug
      - critical
      - Using `Union` containing generics as type hint causes an error
      - beta 1
    * - `#3929`_
      - bug
      - medium
      - Libdoc does not anymore work with resource files in PYTHONPATH
      - beta 1
    * - `#3941`_
      - bug
      - medium
      - Rebot removes `sourcename` attribute from `<kw>` in output.xml
      - beta 1
    * - `#3951`_
      - bug
      - medium
      - "Run keyword if test failed" executes keywords if test was skipped
      - beta 1
    * - `#3958`_
      - bug
      - medium
      - Argument conversion problems when type hint is ABC
      - beta 1

Altogether 5 issues. View on the `issue tracker <https://github.com/robotframework/robotframework/issues?q=milestone%3Av4.0.2>`__.

.. _#3931: https://github.com/robotframework/robotframework/issues/3931
.. _#3929: https://github.com/robotframework/robotframework/issues/3929
.. _#3941: https://github.com/robotframework/robotframework/issues/3941
.. _#3951: https://github.com/robotframework/robotframework/issues/3951
.. _#3958: https://github.com/robotframework/robotframework/issues/3958
