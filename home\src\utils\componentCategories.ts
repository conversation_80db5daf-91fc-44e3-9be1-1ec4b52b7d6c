export interface ComponentDefinition {
  type: string
  label: string
  description: string
  icon: string
  category: string
  config: Record<string, any>
  inputs?: string[]
  outputs?: string[]
  disabled?: boolean
}

export interface ComponentCategory {
  name: string
  label: string
  icon: string
  color: string
  components: ComponentDefinition[]
}

// 开发环境为 true
const isDevelopment = import.meta.env.DEV

export const categoryColors: Record<string, string> = {
  control: '#8cd169',
  judgecycle: '#2FB36B',
  wait: '#6172F3',
  browser: '#875AF7',
  ai: '#6172F3',
  database: '#FF8E31',
  data_pocess: '#E76F47',
  message: '#37AED4',
  process: '#E7BA47',
  system: '#5D5EBC',
  file: '#5DBC92',
  folder: '#CA6790',
  zipunzip: '#80C940',
  mousekeyboard: '#6172F3',
  code_exceute: '#13C0C9',
  business: '#be35e4',
  other: '#823cff',
}

export function getCategoryColor(categoryName: string): string {
  return categoryColors[categoryName] || '#0054D2'
}

// 指令定义 - 基于RPA Framework
export const componentCategories: ComponentCategory[] = [
  {
    name: 'judgecycle',
    label: '判断/循环',
    icon: 'judgecycle',
    color: categoryColors.judgecycle,
    components: [
      {
        type: 'condition',
        label: '条件判断',
        description: '根据条件执行不同的分支',
        icon: 'action-iconfont icon-IFtiaojianpanduan',
        category: 'judgecycle',
        disabled: false,
        config: {
          conditions: [
            {
              relation: 'and',
              condition_id: 'node_start',
              conditions: [],
            },
          ],
        },
      },
      {
        // todo 仅前端添加了
        type: 'for_loop',
        label: 'For循环',
        description: '对一组指令进行特定次数的循环操作',
        icon: 'action-iconfont icon-Forxunhuan',
        category: 'judgecycle',
        disabled: true,
        config: {
          loop_type: 'count',
          loop_count: 5,
          loop_variable: 'i',
        },
        outputs: ['loop_index'],
      },
    ],
  },
  {
    name: 'wait',
    label: '等待',
    icon: 'wait',
    color: categoryColors.wait,
    components: [
      {
        type: 'wait',
        label: '等待',
        description: '让流程等待一段时间',
        icon: 'action-iconfont icon-dengdai',
        category: 'wait',
        disabled: false,
        config: {
          duration: 1,
          unit: 'seconds',
        },
      },
      {
        // todo 仅前端添加了
        type: 'wait_element',
        label: '等待元素',
        description: '等待元素出现或消失，再执行接下来的流程',
        icon: 'action-iconfont icon-dengdaiyuansu',
        category: 'wait',
        disabled: true,
        config: {},
      },
      {
        // todo 仅前端添加了
        type: 'wait_window',
        label: '等待窗口',
        description: '等待窗口状态变化',
        icon: 'action-iconfont icon-dengdaichuangkou',
        category: 'wait',
        disabled: true,
        config: {},
      },
    ],
  },
  {
    name: 'browser',
    label: '网页自动化',
    icon: 'browser',
    color: categoryColors.browser,
    components: [
      {
        type: 'new_browser',
        label: '打开浏览器网页',
        description: '使用指定浏览器打开网页，以实现网页自动化',
        icon: 'action-iconfont icon-dakaiwangye',
        category: 'browser',
        disabled: false,
        config: {
          url: 'https://www.dlmeasure.com',
          browser: 'chromium',
          headless: false,
          timeout: 30,
        },
        outputs: ['browser_instance'],
      },
      // {
      //   // todo 仅前端写了
      //   type: 'navigate_to',
      //   label: '打开页面',
      //   description: '使用指定浏览器打开网页，以实现网页自动化',
      //   icon: 'action-iconfont icon-dakaiwangye',
      //   category: 'browser',
      //   disabled: true,
      //   config: {
      //     url: 'https://www.example.com',
      //     timeout: 30,
      //   },
      //   inputs: ['browser_instance'],
      // },
      {
        type: 'click_element',
        label: '点击元素',
        description: '点击网页中的按钮、链接或者其他任何元素',
        icon: 'action-iconfont icon-dianjiyuansu',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          timeout: 10,
          wait_after: 1,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'navigate_to',
        label: '导航到页面',
        description: '在当前浏览器中导航到指定URL',
        icon: 'action-iconfont icon-daohang',
        category: 'browser',
        disabled: false,
        config: {
          url: 'https://www.example.com',
          timeout: 30,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'hover_element',
        label: '悬停元素',
        description: '将鼠标悬停在指定元素上',
        icon: 'action-iconfont icon-shubiaoxuanting',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'input_text',
        label: '填写输入框',
        description: '在网页的输入框中输入内容',
        icon: 'action-iconfont icon-tianxieshurukuang',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          text: '',
          clear_first: true,
          timeout: 10,
        },
        inputs: ['browser_instance', 'text_value'],
      },
      {
        type: 'select_option',
        label: '选择下拉选项',
        description: '在下拉框中选择指定选项',
        icon: 'action-iconfont icon-xialakuang',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          option_value: '',
          option_text: '',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'get_attribute',
        label: '获取元素属性',
        description: '获取指定元素的属性值',
        icon: 'action-iconfont icon-huoquyuansushuxing',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          attribute_name: 'value',
          timeout: 10,
        },
        inputs: ['browser_instance'],
        outputs: ['attribute_value'],
      },
      {
        type: 'wait_for_element',
        label: '等待元素',
        description: '等待指定元素出现或达到指定状态',
        icon: 'action-iconfont icon-dengdaiyuansu',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          state: 'visible',
          timeout: 30,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'scroll_to_element',
        label: '滚动到元素',
        description: '滚动页面直到指定元素可见',
        icon: 'action-iconfont icon-shubiaogundong',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'check_checkbox',
        label: '复选框操作',
        description: '选中或取消选中复选框',
        icon: 'action-iconfont icon-fuxuankuang',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          checked: true,
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'upload_file',
        label: '文件上传',
        description: '上传文件到指定的文件输入框',
        icon: 'action-iconfont icon-wenjianshangchuan',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          file_path: '',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'switch_frame',
        label: '切换框架',
        description: '切换到指定的iframe或frame',
        icon: 'action-iconfont icon-kuangjia',
        category: 'browser',
        disabled: false,
        config: {
          frame_selector: '',
          selector_type: 'xpath',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
      {
        type: 'handle_alert',
        label: '处理弹窗',
        description: '处理浏览器弹窗（确认、取消或输入文本）',
        icon: 'action-iconfont icon-tanchuang',
        category: 'browser',
        disabled: false,
        config: {
          action: 'accept',
          prompt_text: '',
          timeout: 10,
        },
        inputs: ['browser_instance'],
        outputs: ['alert_text'],
      },
      {
        type: 'close_browser',
        label: '关闭浏览器',
        description: '关闭已打开的某个或所有网页',
        icon: 'action-iconfont icon-guanbiwangye',
        category: 'browser',
        disabled: false,
        config: {},
        inputs: ['browser_instance'],
      },
      {
        // todo 仅前端写了
        type: 'refresh_page',
        label: '刷新页面',
        description: '重新加载（刷新）当前页面',
        icon: 'action-iconfont icon-shuaxinwangye',
        category: 'browser',
        disabled: true,
        config: {},
        inputs: ['browser_instance'],
      },
      {
        type: 'scroll_page',
        label: '鼠标滚动页面',
        description: '在指定的网页中滚动鼠标，可以设置为滚动到顶部、底部或者指定位置',
        icon: 'action-iconfont icon-shubiaogundongwangye',
        category: 'browser',
        disabled: false,
        config: {
          direction: 'down',
          amount: 500,
          behavior: 'smooth',
        },
        inputs: ['browser_instance'],
      },
      {
        // todo 仅前端写了
        type: 'get_element_text',
        label: '网页数据抓取',
        description:
          '在网页中抓取批量数据，常用于抓取列表页、详情页，同时可通过设置分页按钮抓取多页数据',
        icon: 'action-iconfont icon-wangyeshujuzhuaqu',
        category: 'browser',
        disabled: true,
        config: {},
        inputs: ['browser_instance'],
        outputs: ['text_value'],
      },
      {
        type: 'take_screenshot',
        label: '网页截图',
        description: '将网页截图，并保存为图片',
        icon: 'action-iconfont icon-wangyejietu',
        category: 'browser',
        disabled: false,
        config: {
          filename: 'screenshot.png',
          full_page: true,
        },
        inputs: ['browser_instance'],
        outputs: ['screenshot_path'],
      },
      {
        type: 'get_text',
        label: '获取文本',
        description: '获取指定元素的文本内容',
        icon: 'action-iconfont icon-congwenbenzhongtiquneirong',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          timeout: 10,
        },
        inputs: ['browser_instance'],
        outputs: ['text_content'],
      },
      {
        type: 'send_keys',
        label: '发送按键',
        description: '向指定元素发送键盘按键（如回车、Tab、Ctrl+A等）',
        icon: 'action-iconfont icon-jianpanshuru',
        category: 'browser',
        disabled: false,
        config: {
          selector: '',
          selector_type: 'xpath',
          keys: 'Enter',
          timeout: 10,
        },
        inputs: ['browser_instance'],
      },
    ],
  },
  {
    name: 'ai',
    label: '人工智能AI',
    icon: 'ai',
    color: categoryColors.ai,
    components: [
      {
        type: 'ai_analyze',
        label: 'AI分析',
        description: 'AI大模型对输入的问题进行总结分析',
        icon: 'action-iconfont icon-AIfenxi',
        category: 'ai',
        disabled: false,
        config: {
          model: 'qwen3-32b',
          ai_analyze_response: 'AI_Text',
          retry_times: 1,
          retry_delay: 1,
          timeout: 60,
          error_handle: 'stop',
        },
        outputs: ['ai_analyze_response'],
      },
      {
        type: 'img_recognition',
        label: '图片识别',
        description: '识别并理解图片中的内容',
        icon: 'action-iconfont icon-tupianshibie',
        category: 'ai',
        disabled: false,
        config: {
          img: '',
          prompt: '',
          recognition_result: 'recognition_result',
        },
        outputs: ['recognition_result'],
      },
      {
        type: 'invoice_recognition',
        label: '发票识别',
        description: '识别并理解发票中的内容',
        icon: 'action-iconfont icon-tupianshibie',
        category: 'ai',
        disabled: false,
        config: {
          img: '',
          ticket_type:'',
          prompt: '',
          recognition_results: 'recognition_results',
        },
        outputs: ['recognition_results'],
      },
      {
        type: 'document_ai_extract',
        label: '文档内容提取',
        description: '识别并提取文档中的内容',
        icon: 'action-iconfont icon-OCRshibie',
        category: 'ai',
        disabled: true,
        config: {
          file_path: '',
          extraction_type: 'text',
        },
        outputs: ['extracted_data'],
      },
      {
        // todo 仅前端写了
        type: 'speech_to_text',
        label: '语音识别',
        description: '将输入的语音转换成文本内容',
        icon: 'action-iconfont icon-yuyinshibie',
        category: 'ai',
        disabled: true,
        config: {
          audio_path: '',
          language: 'en',
        },
        outputs: ['transcribed_text'],
      },
      // {
      //   type: 'openai_chat',
      //   label: 'OpenAI对话',
      //   description: '使用OpenAI进行对话',
      //   icon: 'action-iconfont',
      //   category: 'ai',
      //   disabled: true,
      //   config: {
      //     api_key: '',
      //     model: 'gpt-3.5-turbo',
      //     prompt: '',
      //     max_tokens: 150,
      //   },
      //   outputs: ['ai_response'],
      // },
      // {
      //   type: 'assistant_display',
      //   label: '显示助手界面',
      //   description: '显示用户交互界面',
      //   icon: 'action-iconfont',
      //   category: 'ai',
      //   disabled: true,
      //   config: {
      //     title: '',
      //     message: '',
      //     buttons: '',
      //   },
      //   outputs: ['user_response'],
      // },
    ],
  },
  {
    name: 'database',
    label: '接口/数据库',
    icon: 'database',
    color: categoryColors.database,
    components: [
      // {
      //   type: 'http_get',
      //   label: 'HTTP GET请求',
      //   description: '发送HTTP GET请求',
      //   icon: 'action-iconfont icon-HTTPGETqingqiu',
      //   category: 'database',
      //   disabled: false,
      //   config: {
      //     url: 'https://api.example.com/data',
      //     headers: '',
      //     params: '',
      //   },
      //   outputs: ['response','response_text'],
      // },
      // {
      //   type: 'http_post',
      //   label: 'HTTP POST请求',
      //   description: '发送HTTP POST请求',
      //   icon: 'action-iconfont icon-HTTPPOSTqingqiu',
      //   category: 'database',
      //   disabled: false,
      //   config: {
      //     url: 'https://api.example.com/data',
      //     data: '',
      //     json: '',
      //     headers: '',
      //     url_method:'POST',
      //     error:true,
      //     response_content_variable: "http_response",
      //     response_status_variable: "http_code",
      //     retry_times:1,
      //     time_out:1
      //   },
      //   outputs: ['response','response_text'],
      // },
      {
        type: 'http_request',
        label: 'HTTP请求',
        description: '发送HTTP请求',
        icon: 'action-iconfont icon-HTTPPOSTqingqiu',
        category: 'database',
        disabled: false,
        config: {
          url: 'https://api.example.com/data',
          json_data: '',
          headers: '',
          timeout: 15,
          // response_content_variable: 'response_content',
          url_method: 'POST',
          extract_variable: [
            {
              variable: 'response_content',
              type: 'json',
              realkey: 'Response',
              desc: '响应内容变量名'
            }
          ],
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        outputs: ['response', 'response_text'],
      },
      {
        type: 'db_connect',
        label: '连接数据库',
        description: '连接到数据库',
        icon: 'action-iconfont icon-lianjieshujuku',
        category: 'database',
        disabled: false,
        config: {
          host: '',
          database: '',
          port: '',
          user: '',
          password: '',
          driver: 'pymysql',
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        outputs: ['connection'],
      },
      {
        type: 'db_query',
        label: '执行SQL语句',
        description: '连接并通过SQL或者数据库表的方式查询数据',
        icon: 'action-iconfont icon-bianzu',
        category: 'database',
        disabled: false,
        config: {
          query: 'SELECT * FROM table',
          response_content_variable: 'sql_result',
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        inputs: ['connection'],
        outputs: ['result'],
      },
      {
        type: 'db_execute',
        label: '执行数据库操作',
        description: '执行数据库插入、更新、删除操作',
        icon: 'action-iconfont icon-bianzu',
        category: 'database',
        disabled: true,
        config: {
          statement: 'INSERT INTO table VALUES (?)',
          parameters: '',
        },
        inputs: ['connection'],
      },
      // {
      //   // todo 仅前端写了
      //   type: 'db_rag_query',
      //   label: '信息模型数据查询',
      //   description: '查询设备实时上发的数据',
      //   icon: 'action-iconfont icon-xinximoxingshujuchaxun',
      //   category: 'database',
      //   disabled: true,
      //   config: {
      //     query: 'SELECT * FROM table',
      //     parameters: '',
      //   },
      //   inputs: ['connection'],
      //   outputs: ['result'],
      // },
      // {
      //   // todo 仅前端写了
      //   type: 'db_rag_query',
      //   label: '设备上发数据查询',
      //   description: '查询设备实时上发的数据',
      //   icon: 'action-iconfont icon-shebeishangfashujuchaxun',
      //   category: 'database',
      //   disabled: true,
      //   config: {
      //     query: 'SELECT * FROM table',
      //     parameters: '',
      //   },
      //   inputs: ['connection'],
      //   outputs: ['result'],
      // },
    ],
  },
  {
    name: 'data_pocess',
    label: '数据处理',
    icon: 'data_pocess',
    color: categoryColors.data_pocess,
    components: [
      {
        type: 'variable_assignment',
        label: '变量赋值',
        description: '变量赋值节点用于向可写入变量进行变量赋值',
        icon: 'action-iconfont icon-chanshengsuijishu',
        category: 'data_pocess',
        disabled: false,
        config: {
          variables: [],
        },
        outputs: [],
      },
      {
        // todo 仅前端写了
        type: 'random_number',
        label: '生成随机数',
        description: '生成指定范围内的随机数',
        icon: 'action-iconfont icon-chanshengsuijishu',
        category: 'data_pocess',
        disabled: true,
        config: {
          min_value: 1,
          max_value: 100,
        },
        outputs: ['random_number'],
      },
      {
        // todo 仅前端写了
        type: 'extract_text',
        label: '从文本中提取内容',
        description: '从文本中提取指定的内容',
        icon: 'action-iconfont icon-congwenbenzhongtiquneirong',
        category: 'data_pocess',
        disabled: true,
        config: {
          text: '',
          pattern: '',
        },
        outputs: ['extracted_text'],
      },
      {
        type: 'text_template',
        label: '文本模板',
        description: '组合变量和文本内容，输入根据变量动态变化的文本内容',
        icon: 'action-iconfont icon-xiaoxitongzhi',
        category: 'data_pocess',
        disabled: false,
        config: {
          text: '',
          output_variable: 'formatted_text',
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        outputs: ['output_variable'],
      },
      {
        // todo 仅前端写了
        type: 'replace_text',
        label: '文本替换',
        description: '替换文本中的指定内容',
        icon: 'action-iconfont icon-wenbentihuan',
        category: 'data_pocess',
        disabled: true,
        config: {
          text: '',
          pattern: '',
          replacement: '',
        },
        outputs: ['replaced_text'],
      },
      {
        // todo 仅前端写了
        type: 'convert_case',
        label: '字母大小写转换',
        description: '用于改变文本的大小写（全部大写、全部小写、词首字母大写）',
        icon: 'action-iconfont icon-zimudaxiaoxiezhuanhuan',
        category: 'data_pocess',
        disabled: true,
        config: {
          text: '',
          case: 'upper',
        },
        outputs: ['converted_text'],
      },
      {
        // todo 仅前端写了
        type: 'get_current_time',
        label: '获取当前时间',
        description: '获取当前日期时间',
        icon: 'action-iconfont icon-huoqudangqianshijian',
        category: 'data_pocess',
        disabled: true,
        config: {
          format: 'YYYY-MM-DD HH:mm:ss',
        },
        outputs: ['current_time'],
      },
      {
        // todo 仅前端写了
        type: 'get_current_time',
        label: '获取时间详细信息',
        description:
          '获取日期时间的年、月、日、时、分、秒、星期、当月最后一天、当年第几周、当年第几天',
        icon: 'action-iconfont icon-huoqushijianxiangxixinxi',
        category: 'data_pocess',
        disabled: true,
        config: {
          format: 'YYYY-MM-DD HH:mm:ss',
        },
        outputs: ['current_time'],
      },
      {
        // todo 仅前端写了
        type: 'add_time',
        label: '增加/减少时间',
        description: '在指定日期时间上进行时间增加或者减少',
        icon: 'action-iconfont icon-jianshaoshijian',
        category: 'data_pocess',
        disabled: false,
        config: {
          date: '', //原日期时间
          method: 'add', //调整方式
          duration: 1, //调整时长
          duration_unit: 'seconds', //调整时长单位
          response_date_variable: 'response_date', //响应日期变量名
          response_week_variable: 'response_week', //响应星期变量名
          response_timestamp_variable: 'response_timestamp', //响应时间戳变量名
          timeout: 15, //超时秒数
          retry_times: 1, //重试次数
          retry_delay: 1, //重试间隔
          error_handle: 'stop', //错误处理方式
        },
        inputs: ['current_time'],
        outputs: ['new_time'],
      },
      {
        // todo 仅前端写了
        type: 'get_time_difference',
        label: '获取时间间隔',
        description: '获取时间间隔',
        icon: 'action-iconfont icon-huoqushijianjiange',
        category: 'data_pocess',
        disabled: true,
        config: {
          start_time: '',
          end_time: '',
          unit: 'day',
        },
        outputs: ['time_difference'],
      },
      {
        type: 'date_format',
        label: '文本转日期',
        description: '将文本转换成日期时间，默认采用 %Y-%m-%d 的时间格式',
        icon: 'action-iconfont icon-wenbenzhuanriqi',
        category: 'data_pocess',
        disabled: true,
        config: {
          date: '',
          format: '%Y-%m-%d',
        },
        outputs: ['formatted_date'],
      },
      {
        // todo 仅前端写了
        type: 'date_to_text',
        label: '日期转文本',
        description: '日期时间转换为文本',
        icon: 'action-iconfont icon-riqizhuanwenben',
        category: 'data_pocess',
        disabled: true,
        config: {
          date: '',
          format: '%Y-%m-%d',
        },
        outputs: ['formatted_date'],
      },
      {
        // todo 仅前端写了
        type: 'date_to_timestamp',
        label: '日期转时间戳',
        description: '日期时间转换为[时间戳]',
        icon: 'action-iconfont icon-riqizhuanshijianchuo',
        category: 'data_pocess',
        disabled: true,
        config: {
          date: '',
          format: '%Y-%m-%d',
        },
        outputs: ['timestamp'],
      },
      {
        // todo 仅前端写了
        type: 'timestamp_to_date',
        label: '时间戳转日期',
        description: '时间戳转换为日期时间',
        icon: 'action-iconfont icon-shijianchuozhuanriqi',
        category: 'data_pocess',
        disabled: true,
        config: {
          timestamp: '',
          format: '%Y-%m-%d',
        },
        outputs: ['date'],
      },
      {
        type: 'date_diff',
        label: '日期差值',
        description: '计算两个日期之间的差值',
        icon: 'action-iconfont icon-huoqushijianxiangxixinxi',
        category: 'data_pocess',
        disabled: true,
        config: {
          date1: '',
          date2: '',
          unit: 'days',
        },
        outputs: ['date_difference'],
      },
      {
        // todo 仅前端写了
        type: 'base64_encode',
        label: 'Base64编码',
        description: '文本编码转换为Base64编码',
        icon: 'action-iconfont icon-Base64bianma',
        category: 'data_pocess',
        disabled: true,
        config: {
          text: '',
        },
        outputs: ['base64_encoded'],
      },
      {
        // todo 仅前端写了
        type: 'base64_decode',
        label: 'Base64解码',
        description: '将编码格式为Base64的文本进行解码',
        icon: 'action-iconfont icon-Base64jiema',
        category: 'data_pocess',
        disabled: true,
        config: {
          base64_string: '',
        },
        outputs: ['decoded_text'],
      },
    ],
  },
  {
    name: 'message',
    label: '消息通知',
    icon: 'message',
    color: categoryColors.message,
    components: [
      {
        type: 'notifier_send',
        label: '消息通知',
        description: '发送系统弹窗消息、短信消息',
        icon: 'action-iconfont icon-xiaoxitongzhi',
        category: 'message',
        disabled: false,
        config: {
          methods: ['sys'],
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
      },
      {
        type: 'text_to_speech',
        label: '语音通知',
        description: '将输入的文本内容转换成语音',
        icon: 'action-iconfont icon-yuyintongzhi',
        category: 'message',
        disabled: false,
        config: {
          input: '',
          retry_times: 1,
          retry_delay: 1,
          timeout: 15,
          error_handle: 'stop',
        },
      },
      // {
      //   // todo 仅前端写了
      //   type: 'tts_speak',
      //   label: '语音通知',
      //   description: '将文本转换为语音并播放',
      //   icon: 'action-iconfont icon-yuyintongzhi',
      //   category: 'message',
      //   disabled: true,
      //   config: {
      //     text: '',
      //     language: 'en',
      //   },
      // },
      // {
      //   type: 'user_confirm',
      //   label: '确认框',
      //   description: '指定人员确认后，继续下一步',
      //   icon: 'action-iconfont icon-querenkuang',
      //   category: 'message',
      //   disabled: false,
      //   config: {
      //     variable_name: '',
      //     prompt_message: '',
      //     default_answer: true,
      //   },
      //   outputs: ['user_confirm'],
      // },
    ],
  },
  {
    name: 'process',
    label: '流程',
    icon: 'process',
    color: categoryColors.process,
    components: [
      // 执行任务
      {
        type: 'execute_task',
        label: '执行任务',
        description: '执行指定的任务，等待子任务执行结束后继续执行',
        icon: 'action-iconfont icon-zhihangrenwu',
        category: 'process',
        disabled: true,
        config: {
          task_name: '',
          task_inputs: '',
        },
        outputs: ['task_output'],
      },
      // 发起工单流程
      {
        type: 'start_workflow',
        label: '发起工单流程',
        description: '发起指定的工单流程',
        icon: 'action-iconfont icon-gongdanliucheng',
        category: 'process',
        disabled: !isDevelopment,
        config: {
          procCode: '',
          procName: '',
          params: {},
        },
        outputs: ['process_result'],
      },
    ],
  },
  {
    name: 'system',
    label: '系统操作',
    icon: 'system',
    color: categoryColors.system,
    components: [
      {
        type: 'run_command',
        label: '运行或打开',
        description: '运行软件、打开文件、打开文件夹、打开网址、执行系统命令等',
        icon: 'action-iconfont icon-yunhang',
        category: 'system',
        disabled: !isDevelopment,
        config: {
          command: '',
          shell: true,
          timeout: 30,
          capture_output: true,
        },
        outputs: ['command_output', 'return_code'],
      },
      // {
      //   type: 'open_application',
      //   label: '打开应用程序',
      //   description: '启动指定的桌面应用程序',
      //   icon: 'action-iconfont',
      //   category: 'system',
      //   disabled: true,
      //   config: {
      //     application: 'notepad.exe',
      //     arguments: '',
      //     timeout: 30,
      //   },
      //   outputs: ['application_handle'],
      // },
      {
        // todo 仅前端写了
        type: 'close_window',
        label: '关闭软件窗口',
        description: '此操作将关闭指定窗口',
        icon: 'action-iconfont icon-guanbi',
        category: 'system',
        disabled: true,
        config: {
          window_name: '',
        },
      },
      {
        type: 'desktop_screenshot',
        label: '桌面截图',
        description: '对屏幕或者窗口截图，保存到剪切板或者文件',
        icon: 'action-iconfont icon-jieping',
        category: 'system',
        disabled: true,
        config: {
          filename: 'desktop_screenshot.png',
          region: '',
        },
        outputs: ['screenshot_path'],
      },
      {
        // todo 仅前端写了
        type: 'lock_screen',
        label: '屏幕锁屏',
        description: '锁定屏幕，显示密码输入框',
        icon: 'action-iconfont icon-pingmusuoping',
        category: 'system',
        disabled: true,
        config: {
          message: '请输入密码',
          timeout: 30,
        },
        outputs: ['lock_success'],
      },
      {
        // todo 仅前端写了
        type: 'unlock_screen',
        label: '屏幕解锁',
        description: '解锁屏幕，显示密码输入框',
        icon: 'action-iconfont icon-pingmujiesuo',
        category: 'system',
        disabled: true,
        config: {
          message: '请输入密码',
          timeout: 30,
        },
      },
      {
        // todo 仅前端写了
        type: 'dos_command',
        label: '运行DOS命令',
        description: '执行DOS命令',
        icon: 'action-iconfont icon-yunhangDOSmingling',
        category: 'system',
        disabled: true,
        config: {
          message: '请输入密码',
          timeout: 30,
        },
      },
      {
        type: 'process_kill',
        label: '终止进程',
        description: '终止指定进程',
        icon: 'action-iconfont icon-zhongzhijincheng',
        category: 'system',
        disabled: true,
        config: {
          process_name: '',
          process_id: '',
          force: false,
        },
      },
      {
        type: 'desktop_click',
        label: '桌面点击',
        description: '在桌面应用中点击指定位置或元素',
        icon: 'action-iconfont icon-dianjiyuansu',
        category: 'system',
        disabled: !isDevelopment,
        config: {
          locator: 'name:Button',
          action: 'click',
          timeout: 10,
        },
      },
      {
        type: 'desktop_type',
        label: '桌面输入',
        description: '在桌面应用中输入文本',
        icon: 'action-iconfont icon-tianxieshurukuang',
        category: 'system',
        disabled: !isDevelopment,
        config: {
          text: 'Hello World',
          clear_first: false,
        },
      },
      // {
      //   type: 'environment_get',
      //   label: '获取环境变量',
      //   description: '获取系统环境变量的值',
      //   icon: 'action-iconfont',
      //   category: 'system',
      //   disabled: true,
      //   config: {
      //     variable_name: '',
      //     default_value: '',
      //   },
      //   outputs: ['env_value'],
      // },
      // {
      //   type: 'environment_set',
      //   label: '设置环境变量',
      //   description: '设置系统环境变量',
      //   icon: 'action-iconfont',
      //   category: 'system',
      //   disabled: true,
      //   config: {
      //     variable_name: '',
      //     value: '',
      //     permanent: false,
      //   },
      // },
      // {
      //   type: 'process_list',
      //   label: '列出进程',
      //   description: '获取系统运行进程列表',
      //   icon: 'action-iconfont',
      //   category: 'system',
      //   disabled: true,
      //   config: {
      //     filter_name: '',
      //     include_children: false,
      //   },
      //   outputs: ['process_list'],
      // },
    ],
  },
  {
    name: 'file',
    label: '文件',
    icon: 'file',
    color: categoryColors.file,
    components: [
      {
        type: 'word_create',
        label: '创建Word文件',
        description: '创建Word文件并添加内容',
        icon: 'action-iconfont icon-Wordwenjian',
        category: 'file',
        disabled: false,
        config: {
          content: '',
          output_folder: '',
          output_filename: '${innerCurrentTime}',
          output_filename_format: '',
          title: '',
          is_open_folder: true,
          font_size: 12,
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        inputs: ['content'],
        outputs: ['word_path', 'file_path', 'file_name'],
      },
      // 打开Word文件
      {
        // todo 仅前端写了
        type: 'word_open',
        label: '打开Word文件',
        description: '打开指定的Word文档',
        icon: 'action-iconfont icon-Wordwenjian',
        category: 'file',
        disabled: true,
        config: {
          path: 'document.docx',
          read_only: true,
        },
        outputs: ['document'],
      },
      // 读取Word文件
      {
        type: 'word_read',
        label: '读取Word内容',
        description: '读取指定Word文档中的内容',
        icon: 'action-iconfont icon-Wordwenjian',
        category: 'file',
        disabled: true,
        config: {
          document: '',
          start_page: 1,
          end_page: 1,
        },
        inputs: ['document'],
        outputs: ['content'],
      },
      {
        type: 'excel_create',
        label: '创建Excel文件',
        description: '创建一个新的Excel工作簿，支持使用报表模板',
        icon: 'action-iconfont icon-Excelwenjian',
        category: 'file',
        disabled: false,
        config: {
          use_template: false,
          template_id: '',
          variable_mapping: {},
          excel_response: [],
          file_path: '',
          file_name: '${innerCurrentTime}',
          file_name_format: '',
          is_open_folder: true,
          worksheet: 'Sheet1',
          fmt: 'xlsx',
        },
        outputs: ['workbook'],
      },
      {
        type: 'excel_open',
        label: '打开Excel文件',
        description: '打开指定的Excel表格',
        icon: 'action-iconfont icon-Excelwenjian',
        category: 'file',
        disabled: !isDevelopment,
        config: {
          path: 'data.xlsx',
          read_only: false,
        },
        outputs: ['workbook'],
      },
      {
        type: 'excel_read',
        label: '读取Excel数据',
        description: '读取指定Excel表格中的内容',
        icon: 'action-iconfont icon-Excelwenjian',
        category: 'file',
        disabled: !isDevelopment,
        config: {
          worksheet: 'Sheet1',
          range: '',
          header: true,
        },
        inputs: ['workbook'],
        outputs: ['data'],
      },
      {
        type: 'excel_write',
        label: '写入Excel数据',
        description: '向Excel工作表写入数据，支持报表模板',
        icon: 'action-iconfont icon-Excelwenjian',
        category: 'file',
        disabled: true,
        config: {
          worksheet: 'Sheet1',
          use_template: false,
          data: '',
          start_cell: 'A1',
          template_id: '',
          variable_mapping: {},
          overwrite: true,
          auto_fit: false,
        },
        inputs: ['workbook', 'data'],
      },
      {
        type: 'excel_append',
        label: 'Excel追加数据',
        description: '向已有Excel文件追加数据，支持模板格式继承和智能检测',
        icon: 'action-iconfont icon-Excelwenjian',
        category: 'file',
        disabled: false,
        config: {
          // 基础配置
          file_source: 'file_path', // 'file_path' | 'from_node' | 'auto_detect'
          file_path: '',
          source_node_id: '', // 来源Excel创建节点ID
          worksheet_name: '', // 空则自动使用第一个工作表
          data: '', // 要追加的数据

          // 追加模式
          append_mode: 'end', // 'end' | 'after_template' | 'custom'
          target_row: 0, // 自定义追加位置（当append_mode为custom时）

          // 模板集成
          template_mode: 'auto', // 'auto' | 'inherit' | 'specify' | 'none'
          template_id: '', // 指定模板ID

          // 数据处理
          data_format: 'auto', // 'auto' | 'rows' | 'columns' | 'csv'
          include_header: false, // 是否包含表头

          // 高级选项
          preserve_formatting: true, // 保持格式
          create_if_not_exists: true, // 文件不存在时创建
          backup_original: false, // 备份原文件

          // 用户体验
          open_after_complete: false, // 完成后打开文件
          show_summary: true, // 显示操作摘要
        },
        inputs: ['data'],
        outputs: ['result'],
      },
      {
        type: 'pdf_read',
        label: '读取PDF文本',
        description: '从PDF文件中提取文本内容',
        icon: 'action-iconfont icon-PDFwenjian',
        category: 'file',
        disabled: true,
        config: {
          path: 'document.pdf',
          pages: '',
        },
        outputs: ['text'],
      },
      // {
      //   type: 'pdf_create',
      //   label: '创建PDF文档',
      //   description: '从HTML内容创建PDF文档',
      //   icon: 'action-iconfont icon-PDFwenjian',
      //   category: 'file',
      //   disabled: true,
      //   config: {
      //     html_content: '<h1>${title}</h1><p>${content}</p>',
      //     output_folder: '',
      //     output_filename: 'report_${date}.pdf',
      //     page_size: 'A4',
      //     orientation: 'Portrait',
      //   },
      //   inputs: ['html_content'],
      //   outputs: ['pdf_path'],
      // },
      // {
      //   type: 'markdown_save',
      //   label: '保存Markdown文件',
      //   description: '将内容保存为Markdown格式文件',
      //   icon: 'action-iconfont',
      //   category: 'file',
      //   disabled: true,
      //   config: {
      //     content: '# ${title}\n\n## 概述\n${overview}\n\n## 详细内容\n${details}',
      //     output_folder: '',
      //     output_filename: '${title}_${version}.md',
      //     encoding: 'utf-8',
      //   },
      //   inputs: ['content'],
      //   outputs: ['markdown_path'],
      // },
      // {
      //   type: 'text_file_save',
      //   label: '保存文本文件',
      //   description: '将内容保存为文本文件',
      //   icon: 'action-iconfont',
      //   category: 'file',
      //   disabled: true,
      //   config: {
      //     content: '日志时间：${timestamp}\n操作用户：${username}\n执行结果：${result}',
      //     output_folder: '',
      //     output_filename: 'app_${date}_${time}.log',
      //     encoding: 'utf-8',
      //     append: false,
      //   },
      //   inputs: ['content'],
      //   outputs: ['file_path'],
      // },
    ],
  },
  {
    name: 'folder',
    label: '文件夹',
    icon: 'folder',
    color: categoryColors.folder,
    components: [
      // 获取文件夹列表
      {
        // todo 仅前端写了
        type: 'folder_list',
        label: '获取文件夹列表',
        description: '获取指定路径下的子文件夹列表',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          path: '',
          recursive: false,
        },
        outputs: ['folder_list'],
      },
      // 获取选中文件(夹)列表
      {
        // todo 仅前端写了
        type: 'folder_select_list',
        label: '获取选中文件(夹)列表',
        description: '获取当前激活文件资源管理器（若激活桌面则目标为桌面）中选择的文件、文件夹列表',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          title: '选择文件夹',
          multiple: true,
        },
        outputs: ['selected_folders'],
      },
      {
        type: 'folder_create',
        label: '创建文件夹',
        description: '在指定的目录下创建新的文件夹',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          path: '',
          parents: true,
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_delete',
        label: '删除文件夹',
        description: '删除文件夹，包括该文件夹下所有的子文件和文件',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          path: '',
          recursive: false,
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_clear',
        label: '清空文件夹',
        description: '删除该文件夹下所有的文件和子文件夹，但保留文件夹本身',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          path: '',
          recursive: false,
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_copy',
        label: '拷贝文件夹',
        description: '将源文件夹拷贝到目标文件夹',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          source: '',
          destination: '',
          overwrite: false,
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_move',
        label: '移动文件夹',
        description: '将源文件夹移动到目标文件夹',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          source: '',
          destination: '',
          overwrite: false,
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_rename',
        label: '重命名文件夹',
        description: '将指定文件夹重命名',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          path: '',
          new_name: '',
        },
      },
      {
        // todo 仅前端写了
        type: 'folder_get_system',
        label: '获取系统文件夹路径',
        description: '用于获取当前设备的系统文件夹路径',
        icon: 'action-iconfont icon-wenjian',
        category: 'folder',
        disabled: true,
        config: {
          folder_name: '',
        },
        outputs: ['folder_path'],
      },
    ],
  },
  {
    name: 'zipunzip',
    label: '压缩/解压缩',
    icon: 'zipunzip',
    color: categoryColors.zipunzip,
    components: [
      {
        type: 'zip_create',
        label: '压缩文件/文件夹',
        description: '压缩文件/文件夹',
        icon: 'action-iconfont icon-yasuo',
        category: 'zipunzip',
        disabled: true,
        config: {
          source: '',
          archive_name: 'archive.zip',
          compression_level: 6,
        },
        outputs: ['archive_path'],
      },
      {
        type: 'zip_extract',
        label: '解压文件/文件夹',
        description: '解压文件/文件夹',
        icon: 'action-iconfont icon-jieya',
        category: 'zipunzip',
        disabled: true,
        config: {
          archive_path: '',
          extract_to: '',
          overwrite: true,
        },
      },
    ],
  },
  {
    name: 'mousekeyboard',
    label: '鼠标键盘',
    icon: 'mousekeyboard',
    color: categoryColors.mousekeyboard,
    components: [
      //键盘输入
      {
        // todo 仅前端写了
        type: 'keyboard_input',
        label: '键盘输入',
        description: '给当前激活窗口发送文本',
        icon: 'action-iconfont icon-jianpanshuru',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          text: 'Hello World',
          delay: 0.1,
        },
      },
      // 鼠标点击
      {
        // todo 仅前端写了
        type: 'mouse_click',
        label: '鼠标点击',
        description: '可进行鼠标按下、弹起、单击、双击、右键点击等动作',
        icon: 'action-iconfont icon-shubiaodianji',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          x: 100,
          y: 100,
        },
      },
      // 鼠标移动
      {
        // todo 仅前端写了
        type: 'mouse_move',
        label: '鼠标移动',
        description: '将鼠标移动到指定位置',
        icon: 'action-iconfont icon-yidongshubiao',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          x: 100,
          y: 100,
        },
      },
      // 获取鼠标当前位置
      {
        // todo 仅前端写了
        type: 'mouse_position',
        label: '获取鼠标当前位置',
        description: '获取机器人在运行过程中，执行该条指令时的鼠标位置',
        icon: 'action-iconfont icon-shubiaoweizhi',
        category: 'mousekeyboard',
        disabled: true,
        config: {},
        outputs: ['x', 'y'],
      },
      // 滚动鼠标滚轮
      {
        // todo 仅前端写了
        type: 'mouse_scroll',
        label: '滚动鼠标滚轮',
        description: '滚动鼠标滚轮',
        icon: 'action-iconfont icon-shubiaogunlun',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          delta_x: 0,
          delta_y: 100,
        },
      },
      // 鼠标悬停
      {
        // todo 仅前端写了
        type: 'mouse_hover',
        label: '鼠标悬停',
        description: '鼠标悬停在指定元素上',
        icon: 'action-iconfont icon-shubiaoxuanting',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          locator: 'name:Button',
        },
      },
      // 模拟真人操作
      {
        // todo 仅前端写了
        type: 'human_operation',
        label: '模拟真人操作',
        description: '一次对多个指令用模拟真人的操作习惯运行（从开启到结束区间内的指令）',
        icon: 'action-iconfont icon-caozuoweixiu',
        category: 'mousekeyboard',
        disabled: true,
        config: {
          actions: '',
        },
      },
    ],
  },
  {
    name: 'code_exceute',
    label: '代码执行',
    icon: 'code_exceute',
    color: categoryColors.code_exceute,
    components: [
      {
        type: 'python_execute',
        label: '执行Python代码',
        description: '执行Python代码片段，最终输出结果必须赋值到result参数，result内容必须是字典',
        icon: 'action-iconfont icon-Python3daimazhihang',
        category: 'code_exceute',
        disabled: false,
        config: {
          code: 'result = {\n "arg1":"参数1",\n "arg2":"参数2",\n "arg3":"参数3",\n}',
          script_file: '',
          python_path: 'python',
          timeout: 15,
          capture_output: true,
          working_directory: '',
          arguments: '',
          output_variable: [],
          error_variable: '',
          return_code_variable: '',
        },
        outputs: ['python_output', 'output_variable', 'return_code', 'error_output'],
      },
      {
        type: 'python_evaluate',
        label: 'Python表达式求值',
        description: '执行Python表达式并返回结果',
        icon: 'action-iconfont icon-Python3daimazhihang',
        category: 'code_exceute',
        disabled: true,
        config: {
          expression: '',
          variables: '',
          modules: '',
          result_variable: 'eval_result',
        },
        outputs: ['eval_result'],
      },
      {
        type: 'python_import',
        label: '导入Python模块',
        description: '动态导入Python模块或库',
        icon: 'action-iconfont icon-Python3daimazhihang',
        category: 'code_exceute',
        disabled: true,
        config: {
          module_name: '',
          alias: '',
          from_module: '',
          install_if_missing: false,
        },
        outputs: ['import_success'],
      },
      {
        type: 'javascript_execute',
        label: '执行Javascript代码',
        description: '执行Javascript代码片段或脚本文件',
        icon: 'action-iconfont icon-JavaScriptdaimazhihang',
        category: 'code_exceute',
        disabled: false,
        config: {
          code: '', // js片段
          output_variable: ["arg1"], // 输出变量
          script_file: '', // js脚本路径
          node_path: 'javascript', // Node.js执行路径
          timeout: 15, // 超时时间 秒
          capture_output: true, // 是否记录控制台输出
          working_directory: '', // 执行目录
          arguments: '', // 传递给脚本的参数
          error_variable: '', // 保存错误输出变量
          return_code_variable: '', // 保存返回码变量
        },
        outputs: ['js_output', 'return_code', 'error_output'],
      },
    ],
  },
  {
    // 信息模型
    name: 'business',
    label: '业务',
    icon: 'business',
    color: categoryColors.business,
    components: [
      // 监测数据
      {
        type: 'monitor_data',
        label: '监测数据',
        description: '查询信息模型的监测数据',
        icon: 'action-iconfont icon-jianceshuju',
        category: 'business',
        disabled: false,
        config: {
          url: 'http://**********:31886/uniwim/imb/open/ai/historyByNames',
          headers: {
            authorization: '${authorization}',
          },
          timeout: 60, // 超时时间 秒
          response_content_variable: 'Reponse',
          response_status_variable: 'Code',
        },
        outputs: ['response'],
      },
      // 数据预测
      {
        type: 'data_forecast',
        label: '数据预测',
        description: '对流量、压力、液位数据进行预测，根据历史数据规律预测未来一天的数据',
        icon: 'action-iconfont icon-caozuoxitong',
        category: 'business',
        disabled: false,
        config: {
          forecast_type: '流量',
          forecast_format: '时间序列',
          his_win: '',
          day_count: 30,
          set_step: 1,
          forecast_data: 'forecast_data',
          // forecast_img: 'forecast_img',
          // forecast_avg: 'forecast_avg',
          // forecast_sum: 'forecast_sum',
          // forecast_max: 'forecast_max',
          // forecast_min: 'forecast_min',
        },
        outputs: ['forecast_data'], // , 'forecast_img', 'forecast_avg', 'forecast_sum', 'forecast_max', 'forecast_min'
      },
      // 置信区间
      {
        type: 'limit_interval',
        label: '置信区间',
        description: '对流量、压力数据进行置信区间计算，根据历史数据规律计算数据报警的上下限值',
        icon: 'action-iconfont icon-zhixinqujian',
        category: 'business',
        disabled: false,
        config: {
          data_type: '压力',
          data_format: '时间序列',
          his_win: '',
          day_count: 30,
          percent: 1.5,
          warning_time: 'warning_time',
          warning_up: 'warning_up',
          warning_down: 'warning_down',
          outlier_count: 'outlier_count',
        },
        outputs: ['warning_time', 'warning_up', 'warning_down', 'outlier_count'],
      },
      // 数据清晰
      {
        type: 'data_wash',
        label: '数据清洗',
        description: '对监测数据进行数据清洗，获得清洗后的结果，以及有效数据步长、数据评分等信息',
        icon: 'action-iconfont icon-shujuqingxi',
        category: 'business',
        disabled: false,
        config: {
          wash_type: '流量',
          wash_format: '时间序列',
          his_win: '',
          day_count: 30,
          fill_in_data: '不填充',
          set_step: 1,
          allow_max_data: '',
          allow_min_data: '',
          wash_win: 'wash_win',
          wash_time: 'wash_time',
          miss_count: 'miss_count',
          invalid_count: 'invalid_count',
          valid_count: 'valid_count',
          grade: 'grade',
        },
        outputs: ['wash_win', 'wash_time', 'miss_count', 'invalid_count', 'valid_count', 'grade'],
      },
      // 关阀分析
      {
        type: 'water_shutoff_valve',
        label: '停水关阀分析',
        description: '进行停水关阀分析，获取关阀方案、影响管网、影响用户',
        icon: 'action-iconfont icon-caozuoxitong',
        category: 'business',
        disabled: false,
        config: {
          input_type: '管道中心坐标',
          input_data: '',
          response_content_variable: 'response_content_variable',
          gis_input: 'gis_input',
        },
        outputs: ['response_content_variable', 'gis_input'],
      },
    ],
  },
  {
    // 其他
    name: 'other',
    label: '其他',
    icon: 'other',
    color: categoryColors.other,
    components: [
      // 获取天气
      {
        type: 'weather_query',
        label: '获取天气',
        description: '获取指定城市的天气信息',
        icon: 'action-iconfont icon-qing',
        category: 'other',
        disabled: false,
        config: {
          city: '',
          days: 0,
          weather_type: [],
          weather_text: 'weather_text',
          response_content_variable: 'Weather',
          timeout: 15,
          retry_times: 1,
          retry_delay: 1,
          error_handle: 'stop',
        },
        outputs: ['reponse', 'weather_text'],
      },
    ],
  },
]
