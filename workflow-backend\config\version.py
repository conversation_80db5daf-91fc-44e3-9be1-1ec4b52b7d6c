

'''
<AUTHOR>   liuch
@Version :   1.0
@Time    :   2025/07/10 13:49:00
'''
import os
import json
import argparse
import sys

import requests
from typing import Dict
from typing import List
from typing import Any, Optional

import uvicorn
from fastapi import FastAPI
from fastapi import Body
from fastapi import Query
from fastapi import Header
from fastapi import APIRouter
from fastapi import HTTPException
from fastapi import Response
from pydantic import BaseModel

from fastapi.responses import FileResponse
from loguru import logger

from pathlib import Path
from config.env_config import YN_URL,get_config_item
router = APIRouter()
@router.get("/version")
def version( authorization: str = Header(None)) -> str:
   return getVersion()


@router.get("/checkUpdate")
def checkUpdate( authorization: str = Header(None)):
    """获取版本更新信息
       """
    headers = {
        "Content-Type": "application/json",
        "Authorization": authorization
    }
    version = getVersion()
    url = f"{get_config_item(YN_URL)}/wimai/api/task/packagePublishing/getLatestVersion"
    try:
        response = requests.get(url, params={"versionNumber": version}, headers=headers)
        if response.status_code != 200:
            logger.info("java /wimai/api/task/packagePublishing/getLatestVersion {}".format(response.json()))
        return response.json()
    except Exception as e:
        logger.info("/wimai/api/task/packagePublishing/getLatestVersion fail")
        logger.info("message:{}".format(str(e)))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return Response(content={"message": str(e)})

def getVersion() -> str:
    """获取版本信息（兼容打包前后环境）

          Returns:
              Dict[str, str]: 版本信息字典，包含 version 字段
          """
    try:
        # 确定基础路径
        base_path = Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent

        version_file = base_path / "config" / "version.json"

        if not version_file.exists():
            logger.warning(f"Version file not found at {version_file}")
            # 默认版本
            return "1.0.0"

        # 读取并解析JSON文件
        version_data = json.loads(version_file.read_text(encoding='utf-8'))
        return version_data.get("version", "1.0.0")

    except json.JSONDecodeError as e:
        logger.error(f"Version file JSON parse error: {e}")
        return "1.0.0"
    except Exception as e:
        logger.error(f"Failed to read version info: {e}")
        return "1.0.0"
# 增加版本号+1
def incrVersion() -> str:

    # 确定基础路径
    base_path = Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent
    # 更新json文件中的版本号
    version_file = base_path / "config" / "version.json"

    if not version_file.exists():
        logger.warning(f"Version file not found at {version_file}")
        # 默认版本
        return "1.0.0"

    # 读取并解析JSON文件
    version_data = json.loads(version_file.read_text(encoding='utf-8'))
    version = version_data.get("version", "1.0.0")
    # 增加末尾版本号，如1.0.0 -> 1.0.1
    version = ".".join(version.split(".")[:-1]) + "." + str(int(version.split(".")[-1])+1)
    version_data["version"] = version
    version_file.write_text(json.dumps(version_data), encoding='utf-8')
    return version
