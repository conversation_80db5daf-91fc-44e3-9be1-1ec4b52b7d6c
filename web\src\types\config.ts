/**
 * 统一的组件配置类型定义
 */

// 基础配置字段类型
export type ConfigFieldType =
  | 'string'
  | 'number'
  | 'boolean'
  | 'select'
  | 'multiselect'
  | 'textarea'
  | 'file'
  | 'color'
  | 'date'
  | 'time'
  | 'datetime'
  | 'url'
  | 'email'
  | 'password'
  | 'code'
  | 'json'
  | 'xpath'
  | 'css'
  | 'regex'
  | 'folder'
  | 'conditions'
  | 'text'
  | 'radio'
  | 'variables'
  | 'stringArray'
  | 'processForm'
  | 'errorretry'
  | 'variables_map'
  | 'variable_picker'

// 配置字段验证规则
export interface ConfigValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message?: string
  validator?: (value: any) => boolean | string
}

// 选择项定义
export interface ConfigOption {
  label: string
  value: any
  description?: string
  disabled?: boolean
  icon?: string
  group?: string
}

// 条件显示规则
export interface ConfigCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater' | 'less'
  value: any
}

// 配置字段定义
export interface ConfigFieldDefinition {
  id?: string
  // 基本信息
  type: ConfigFieldType
  value?: string
  enabled?: boolean
  label: string
  description?: string
  placeholder?: string
  help?: string

  // 显示控制
  required?: boolean
  readonly?: boolean
  hidden?: boolean
  group?: string
  order?: number
  labelHidden?: boolean
  switchTrue?: string
  switchFalse?: string

  // 默认值和选项
  default?: any
  options?: ConfigOption[]

  // 异步选项加载
  async?: boolean
  optionsLoader?: () => Promise<ConfigOption[]>
  // 下拉选择事件
  changeEvent?: (...args: any) => void

  // 验证规则
  validation?: ConfigValidationRule[]

  // 条件显示
  conditions?: ConfigCondition[]

  // 是否允许添加新变量
  addVariableDatas?: []

  // 类型特定配置
  min?: number
  max?: number
  step?: number
  precision?: number
  rows?: number
  accept?: string // 文件类型
  multiple?: boolean

  // UI增强
  prefix?: string
  suffix?: string
  append?: object
  unit?: string
  icon?: string

  // 联动配置
  dependsOn?: string[]
  affects?: string[]

  // 高级配置
  advanced?: boolean
  experimental?: boolean
  deprecated?: boolean

  // 日期时间配置
  format?: string,
  valueFormat?: string,
  shortcuts?: object,

  // json格式化默认值
  formatJson?: string,

  // 支持变量的JSON编辑器
  variableSupport?: boolean
  language?: string

  // 支持输出变量类型切换
  outputVariable?: boolean

  // 子节点
  children?: object[]

  // 隐藏提取变量的映射字段
  hideRealKey?: boolean
}

// 配置组定义
export interface ConfigGroup {
  id: string
  label: string
  description?: string
  icon?: string
  order?: number
  collapsible?: boolean
  collapsed?: boolean
  advanced?: boolean
}

// 组件配置Schema
export interface ComponentConfigSchema {
  // 基本信息
  componentType: string
  version: string

  // 配置组
  groups?: ConfigGroup[]

  // 配置字段
  fields: Record<string, ConfigFieldDefinition>

  // 预设配置
  presets?: Record<
    string,
    {
      label: string
      description?: string
      config: Record<string, any>
    }
  >

  // 示例配置
  examples?: Array<{
    title: string
    description?: string
    config: Record<string, any>
  }>
}

// 配置值类型
export interface ConfigValues {
  [key: string]: any
}

// 验证结果
export interface ValidationResult {
  isValid: boolean
  errors: Array<{
    field: string
    message: string
    type: string
    variables?: Record<string, any>
  }>
  warnings: Array<{
    field: string
    message: string
    type: string
  }>
}

// 配置变更事件
export interface ConfigChangeEvent {
  field: string
  oldValue: any
  newValue: any
  source: 'user' | 'preset' | 'system'
}

// 配置上下文
export interface ConfigContext {
  componentType: string
  nodeId: string
  workflowId: string
  variables: Record<string, any>
  globalConfig: Record<string, any>
}
