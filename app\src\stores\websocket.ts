import { defineStore } from 'pinia'
import { computed, reactive } from 'vue'

export const useWebsocketStore = defineStore('websocket', () => {
  // socket数据 - 使用嵌套Map结构
  const messageStore = reactive(new Map<string, Map<string, string[]>>())

  // 初始化socket数据
  const initData = (data: Record<string, Record<string, string[]>>) => {
    messageStore.clear()
    Object.entries(data).forEach(([historyId, typeMap]) => {
      const innerMap = reactive(new Map<string, string[]>())  // 使用reactive创建响应式Map
      Object.entries(typeMap).forEach(([msgType, messages]) => {
        innerMap.set(msgType, [...messages])
      })
      messageStore.set(historyId, innerMap)
    })
  }

  // 添加socket数据
  const addData = (item: any) => {
    try {
      const messageData = typeof item === 'string' ? JSON.parse(item) : item
      const { history_id, type } = messageData

      // 如果不存在该history_id的分组，则创建
      if (!messageStore.has(history_id)) {
        messageStore.set(history_id, new Map<string, string[]>())
      }

      const historyGroup = messageStore.get(history_id)!
      const messageType = type || 'default'

      // 如果不存在该类型的消息数组，则创建
      if (!historyGroup.has(messageType)) {
        historyGroup.set(messageType, [])
      }

      // 获取消息数组并添加新消息
      const messages = historyGroup.get(messageType)!
      messages.push(JSON.stringify(messageData))
    } catch (e) {
      console.error('添加WebSocket数据失败:', e)
    }
  }

  // 清除数据
  const clearData = () => {
    messageStore.clear()
  }

  // 获取所有数据（计算属性）
  const allMessage = computed(() => {
    const result: Record<string, Record<string, string[]>> = {}
    messageStore.forEach((typeMap, historyId) => {
      result[historyId] = Object.fromEntries(typeMap.entries())
    })
    return result
  })

  // 根据 historyId 获取数据（计算属性）
  const getMessagesByHistoryId = computed(() => (historyId: string) => {
    if (!messageStore.has(historyId)) {
      return []
    }
    const typeMap = messageStore.get(historyId)!
    return Object.fromEntries(typeMap.entries())
  })

  // 根据 historyId 和 type 获取特定类型的消息（计算属性）
  // 类型：exec 执行记录， ai AI的流式输出消息，invoice_ocr 发票识别
  const getMessagesByType = computed(() => (historyId: string, type: string) => {
    if (!messageStore.has(historyId)) {
      return []
    }
    const typeMap = messageStore.get(historyId)!
    return typeMap.get(type) || []
  })

  // 根据 historyId 和 type 获取特定类型的消息，并且过滤出其中指定node_id的数据
  const getMessagesByNodeId = computed(() => (historyId: string, type: string, nodeId: string) => {
    if (!messageStore.has(historyId)) {
      return []
    }
    const typeMap = messageStore.get(historyId)!
    const messages = typeMap.get(type) || []
    return messages.filter((msg) => {
      try {
        const msgData = typeof msg === 'string' ? JSON.parse(msg) : msg
        return msgData.node_id === nodeId
      } catch (e) {
        return false
      }
    })
  })


  return {
    // 变量
    messageStore,
    allMessage,

    // 方法
    initData,
    addData,
    clearData,
    getMessagesByHistoryId,
    getMessagesByType,
    getMessagesByNodeId,
  }
})
