<template>
  <div class="read-field">
    <el-button @click="selectFile" type="primary" style="width: 100%;">
      <el-icon>
        <Folder />
      </el-icon>
      <span> 选择文件</span>
    </el-button>
    <div class="file-list">
      <div class="file-header">
        <div class="file-info">已选择{{ modelValue.length }}个文件：</div>
        <div class="file-action">
          <el-button @click="clearFile" link type="primary" size="small" :disabled="!modelValue.length">清空</el-button>
        </div>
      </div>
      <div v-for="(item, index) in modelValue" :key="index" class="file-item">
        <el-icon class="icon"><Document /></el-icon>
<!--        <el-text class="text" truncated>{{ item.name }}</el-text>-->
        <div class="text">
          <el-input v-model="item.path" placeholder="输入文件路径">
            <template #suffix>
              <el-button @click="addFile(item)" size="small" text>
                <el-icon>
                  <Folder />
                </el-icon>
                浏览
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="header_rows" v-if="false">
          <span class="label">表头行：</span>
          <el-input-number v-model="item.header_rows" :min="1" :max="100" size="small" controls-position="right" @change="handleChange"/>
        </div>
        <el-icon @click="deleteFile(index)" title="删除" color="var(--el-color-danger)"><Close /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick } from 'vue'
import {Folder} from "@element-plus/icons-vue";

interface Props {
  modelValue: Array<{
    name: string
    path: string
    header_rows: number
  }>
}

const props = defineProps<Props>()
// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const selectFile = () => {
  const newGroup = []
  newGroup.push({
    name: '',
    path: '',
    header_rows: 1, // 表头行
  })
  emit('update:modelValue', [...(props.modelValue || []), ...newGroup])
  // // Web环境下的文件选择
  // const input = document.createElement('input')
  // input.type = 'file'
  // input.multiple = true
  // input.accept = '.xlsx,.xls'
  // input.max = 20
  // input.onchange = (e) => {
  //   const files = (e.target as HTMLInputElement).files || []
  //   console.warn({files})
  //   const newGroup = []
  //   for (const item of files) {
  //     newGroup.push({
  //       name: item.name,
  //       path: item.path,
  //       header_rows: 1, // 表头行
  //     })
  //   }
  //
  //   emit('update:modelValue', [...(props.modelValue || []), ...newGroup])
  // }
  // input.click()
}

// 添加文件
const addFile = (item: {name: string, path: string, header_rows: number}) => {
  // Web环境下的文件选择
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    item.path = file.name

    emit('update:modelValue', [...props.modelValue])
  }
  input.click()
}

// 内容变更
const handleChange = () => {
  nextTick(()=>{
    emit('update:modelValue', [...props.modelValue])
  })
}

// 删除文件
const deleteFile = (index: number) => {
  const newGroups = [...props.modelValue]
  newGroups.splice(index, 1)
  emit('update:modelValue', newGroups)
}

// 清除文件
const clearFile = () => {
  emit('update:modelValue', [])
}
</script>

<style scoped lang="scss">
.read-field{
  width: 100%;
}
.file-list{
  border-radius: 4px;
  border: 1px solid var(--el-input-border-color, var(--el-border-color));
  margin-top: 10px;
  overflow: hidden;
  .file-header{
    color: #5C5F66;
    padding: 10px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f5f7fa;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-size: 12px;
  }
  .file-item{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    margin: 4px;
    font-size: 12px;
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    border-radius: 4px;
    .header_rows{
      margin: 0 8px 0 6px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      .label{
        flex-shrink: 0;
        color: #5C5F66;
      }
    }
    .icon{
      margin-right: 4px;
    }
    .text{
      width: 100%;
      font-size: 12px;
      .el-input{
        :deep(.el-input__wrapper){
          box-shadow: none;
          height: 28px;
        }
      }
    }
    .el-icon{
      cursor: pointer;
    }
  }
}
</style>
