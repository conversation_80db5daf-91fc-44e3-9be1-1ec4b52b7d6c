{"workflow": {"nodes": [{"id": "start_node", "type": "start", "position": {"x": -520, "y": 180}, "data": {"label": "开始", "config": {"workflow_name": "", "description": "", "execution_mode": "immediate", "schedule_type": "once", "schedule_time": "2025-06-30 00:00:00", "daily_time": "09:00", "error_strategy": "stop", "max_retries": 3, "retry_delay": 5, "log_level": "INFO", "confirm_start": false, "timeout": 300, "type": "taskMonitor"}, "category": "control", "description": "工作流的开始节点，标识流程入口点", "componentType": "workflow_start", "inputs": [], "outputs": ["workflow_context"], "isSystemNode": true}}, {"id": "end_node", "type": "end", "position": {"x": 2360, "y": 320}, "data": {"label": "结束", "config": {"status": "success", "message": "", "return_data": "", "notify_completion": true, "notification_title": "工作流执行完成", "notification_message": "", "notification_type": "success", "notification_duration": 5, "cleanup": true, "log_summary": true, "notify_completion_end": false, "type": "taskMonitor"}, "category": "control", "description": "工作流的结束节点，标识流程完成", "componentType": "workflow_end", "inputs": ["workflow_context"], "outputs": [], "isSystemNode": true}}, {"type": "workflow", "position": {"x": -40, "y": -740}, "data": {"label": "分析内部管道问题-用水突变报警", "icon": "action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://************:7001/ycb/alarm/history/query.json", "json_data": {"Token": "6399804926589e0470af26ab", "sn": "1200003113", "alarm_type": "水量突变", "st": 1672502400, "et": 1750942026}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_tbbj", "url_method": "POST", "response_status_variable": "http_code_tbbj", "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true, "type": "taskMonitor", "extract_variable": [{"variable": "alarm_ystb", "type": "number", "desc": "用水突变报警数量", "realkey": "Response.total"}], "response_content_variable$$type": "json"}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1751421177823_1hov7fzjn"}, {"type": "workflow", "position": {"x": -40, "y": -440}, "data": {"label": "分析内部管道问题-0水量报警", "icon": "action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://************:7001/ycb/alarm/history/query.json", "json_data": {"Token": "6399804926589e0470af26ab", "sn": "1200003113", "alarm_type": "零水量", "st": 1672502400, "et": 1750942026}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_fzc", "url_method": "POST", "response_status_variable": "http_code_fzc", "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor", "show_monitor": true, "extract_variable": [{"variable": "alarm_lsl", "type": "number", "desc": "零水量报警", "realkey": "Response.total"}]}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1751421258628_p5ri51j1h"}, {"type": "workflow", "position": {"x": 1980, "y": 320}, "data": {"label": "创建Word文件", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON>", "config": {"content": "${code}", "output_folder": "C:\\Users\\<USER>\\Desktop\\新建文件夹", "output_filename": "值班机器人-水质投诉问题分析报告", "title": "值班机器人-水质投诉问题分析报告", "font_size": 12, "timeout": 15, "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "use_template": true, "show_monitor": true, "type": "taskMonitor", "template_id": "c1d3b132-4d24-40c4-91f2-d94405cb2758"}, "category": "file", "description": "创建Word文件并添加内容", "componentType": "word_create", "inputs": ["content"], "outputs": ["word_path"]}, "id": "node_1751873980806_f7pevz7tr"}, {"type": "workflow", "position": {"x": 440, "y": -740}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928451382059208704", "field": "${alarm_ystb}", "value": "0", "operator": "greaterThan"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752754627355_rz9zgiyah"}, {"type": "workflow", "position": {"x": 860, "y": -660}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_ystbbj\",\"value\":\"未发生\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752754683410_bbziwmsb2"}, {"type": "workflow", "position": {"x": 440, "y": -440}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928452435991334912", "field": "${alarm_lsl}", "value": "0", "operator": "greaterThan"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752754876977_mz1e7a99p"}, {"type": "workflow", "position": {"x": 860, "y": -500}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_lslbj\",\"value\":\"异常，持续30天用水量为0；\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752754911238_494rzsdhu"}, {"type": "workflow", "position": {"x": 860, "y": -360}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_lslbj\",\"value\":\"正常，未出现持续零水量情况；\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752754945248_wyocpalsd"}, {"type": "workflow", "position": {"x": 440, "y": -120}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928453318678417408", "field": "${hbjr_result}", "value": "10", "operator": "greaterThan"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752755080729_iylemw0d5"}, {"type": "workflow", "position": {"x": 440, "y": 140}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928454642274930688", "field": "${alarm_sdl}", "value": "0", "operator": "greaterThan"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752755390433_xsj80mz9b"}, {"type": "workflow", "position": {"x": 860, "y": -820}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_ystbbj\",\"value\":\"发生\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752755656098_bkq3qi3rw"}, {"type": "workflow", "position": {"x": 860, "y": -220}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_hbjr\",\"value\":\"大于10年\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752755912987_xsgp1ocha"}, {"type": "workflow", "position": {"x": 860, "y": -80}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_hbjr\",\"value\":\"小于10年；\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node-1752755989591-7yh3r6036"}, {"type": "workflow", "position": {"x": 860, "y": 80}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_sdl\",\"value\":\"发生\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752756079542_6sbxpbig7"}, {"type": "workflow", "position": {"x": 860, "y": 220}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_sdl\",\"value\":\"未发生\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node-1752756122475-662vtsgca"}, {"type": "workflow", "position": {"x": 440, "y": 420}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928458193868951552", "field": "${szts7_num}", "value": "5", "operator": "greaterThanOrEqual"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752756198883_1eyfbpxib"}, {"type": "workflow", "position": {"x": 860, "y": 360}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_szts7d\",\"value\":\"大于等于5户，用户问题的可能性较高\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752756234403_g40ls21bg"}, {"type": "workflow", "position": {"x": 860, "y": 500}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"sz_szts7d\",\"value\":\"小于5户，用户问题的可能性较低\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node-1752756391311-a7i8xoypd"}, {"type": "workflow", "position": {"x": 860, "y": 720}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"level\",\"value\":\"一级（特别严重）\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752756688593_16bbu4xo5"}, {"type": "workflow", "position": {"x": 860, "y": 860}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"level\",\"value\":\"二级（严重）\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1752756961412_mzudgxgjp"}, {"type": "workflow", "position": {"x": 860, "y": 1020}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"level\",\"value\":\"三级（较重）\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node-1752757066387-jp6zizm1g"}, {"type": "workflow", "position": {"x": 440, "y": 780}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "or", "condition_id": "node_start", "conditions": [{"condition_id": "node_1928462592762712064", "field": "${tsxqsl_dxxq2}", "value": "15", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928462648190439424", "field": "${tsyhsl_dxxq2}", "value": "20", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928462754352467968", "field": "${codeCount}", "value": "8", "operator": "greaterThanOrEqual"}]}, {"condition_id": "node_1928462879363698688", "relation": "or", "conditions": [{"condition_id": "node_1928462887676809216", "field": "${tsxqsl_dxxq4}", "value": "10", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928462900406521856", "field": "${tsyhsl_dxxq4}", "value": "15", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928462904311418880", "field": "${codeCount}", "value": "6", "operator": "greaterThanOrEqual"}]}, {"condition_id": "node_1928463021462523904", "relation": "and", "conditions": [{"condition_id": "node_1928463084469358592", "field": "${tsxqsl_dxxq4}", "value": "5", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928463089057927168", "field": "${tsyhsl_dxxq4}", "value": "10", "operator": "greaterThanOrEqual"}, {"condition_id": "node_1928463093524860928", "field": "${station_codeCount}", "value": "4", "operator": "greaterThanOrEqual"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1752757256768_i8wceod2z"}, {"type": "workflow", "position": {"x": 860, "y": 1160}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"level\",\"value\":\"不构成事故\"}]", "retry_times": 1, "timeout": 15, "retry_delay": 1, "error_handle": "stop"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node-1752757523451-otjvfjc26"}, {"type": "workflow", "position": {"x": 1480, "y": 280}, "data": {"label": "AI分析", "icon": "action-iconfont icon-AIfenxi", "config": {"model": "qwen3-32b", "ai_analyze_response": "AI_Text", "retry_times": 1, "retry_delay": 1, "timeout": 60, "error_handle": "stop", "timeOut": 60, "show_monitor": true, "question": "一、事件描述\n投诉人：张三\n所在小区：世纪花园小区\n投诉问题：水黄、水浑浊\n二、原因分析\n1. 分析内部管道问题\n\t用户发生用水突变报警；\n\t用户用水异常，持续30天用水量为0；\n\t户表接入7年，小于10年；；\n2. 分析自备水用户逆流问题\n\t小区500米范围内未发生水倒流事件；\n3. 分析同小区用户热线\n\t同小区7日内共计15户投诉，大于等于5户，用户问题的可能性较高；\n4. 分析二供设备问题\n\t小区二供泵房水箱距上次清洗121天，超过60天，可能是水箱长期未清洗影响；\n5. 分析管网工程问题\n\t近2日内小区500米范围内发生2起管网工程事件，管网工程问题导致的可能性较大；\n\t管网事件明细如下：\n工单名称\t事件名称\t经纬度\t施工日期\t所属分公司\t是否结束\n管网维修\t供水主干管渗漏维修\t\t2025.7.24\t城东分公司\t是\n管道改造\t中山路DN300管道改造\t\t2025.7.23\t城东分公司\t否\n6. 分析管网运行问题\n\t2年内小区及附近500米范围内的主管未进行过冲洗，可能是管网运行引起的问题；\n7. 分析管道管龄、管材问题\n\t小区及附件500米范围内不存在管龄超过50年且材质为镀锌管、铸铁、水泥管的管道，管龄、管材问题不大；\n8. 分析管道逆流问题\n\t小区500米范围内共有2个流量监测点发生大流量反向流问题；\n\t监测点明细如下：\n监测点名称\t位置\t报警原因\n中山一路流量监测点\t中山一路交叉口\t2025-07-23 12:34，瞬时流量小于0，当前值-64m³/h\n城西路流量监测点\t城西路124号\t2025-07-24 09:20，瞬时流量小于0，当前值-120m³/h\n三、定性分析\n\t2小时内，共有6个小区，8个用户反映水黄、水浑等水质投诉事件；\n\t4小时内，共有8个小区，16个用户反映水黄、水浑等水质投诉事件；\n\t2小时内，共有5个水质监测点报警；\n\t本次事件定性为：二级（严重）【等级】", "prompt": "你是水质值班机器人，主要是对水质的问题进行分析，并给出建议", "type": "taskMonitor"}, "category": "ai", "description": "AI大模型对输入的问题进行总结分析", "componentType": "ai_analyze", "inputs": [], "outputs": ["ai_analyze_response"]}, "id": "node_1752759392689_n0tyxf10r"}, {"type": "workflow", "position": {"x": -40, "y": -120}, "data": {"label": "分析内部管道问题-户表接入大于10年", "icon": "action-iconfont action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://*************:27704/shoufeizjj3/api/Data/V1/GetRegdateByGS?gs=10246", "json_data": "", "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_hbjr", "url_method": "POST", "extract_variable": [{"variable": "year_hbjr", "type": "string", "desc": "户表接入年份", "realkey": "jsonData"}], "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1753339160340_7b9uwz4fm"}, {"type": "workflow", "position": {"x": 20, "y": -260}, "data": {"label": "执行Python代码", "icon": "action-iconfont action-iconfont icon-Python3daimazhihang", "config": {"code": "result = {\n \"hbjr_result\":7,\n}", "script_file": "", "python_path": "python", "timeout": 15, "capture_output": true, "working_directory": "", "arguments": "", "output_variable": [{"variable": "hbjr_result", "type": "string", "desc": "", "realkey": "", "example": null}], "error_variable": "", "return_code_variable": "", "show_monitor": false, "retry_times": 1, "retry_delay": 1}, "category": "code_exceute", "description": "执行Python代码片段，最终输出结果必须赋值到result参数，result内容必须是字典", "componentType": "python_execute", "inputs": [], "outputs": ["output_variable", "python_output", "return_code", "error_output"]}, "id": "node_1753339592610_t8pdgo09u"}, {"type": "workflow", "position": {"x": -40, "y": 140}, "data": {"label": "分析自备水用户逆流问题-倒流报警", "icon": "action-iconfont action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://************:7001/ycb/alarm/history/query.json", "json_data": {"Token": "6399804926589e0470af26ab", "sn": "1200003113", "alarm_type": "水倒流", "st": 1672502400, "et": 1750942026}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_sdl", "url_method": "POST", "extract_variable": [{"variable": "alarm_sdl", "type": "number", "desc": "水倒流报警", "realkey": "Response.total"}], "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1753341272613_ud3yn9miq"}, {"type": "workflow", "position": {"x": -40, "y": 420}, "data": {"label": "分析同小区用户热线-7日水质热线事件", "icon": "action-iconfont action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://*************:31080/customerserver3/api/MessageApi/GetRXSJ_TSInfo?Token=8A5DE995-D142-4B3D-84E3-E126C8C25013", "json_data": {"WhereList": [{"WhereName": "AA.hddev_proc_status", "Comparison": 0, "Value": "ACTIVE", "GroupName": "hddev_proc_status"}, {"WhereName": "AA.gzlb", "Comparison": 8, "Value": "'黄水','水浑浊','牛奶水','有杂质','像河水','水发蓝'", "GroupName": "gzlb"}, {"WhereName": "AA.slsj", "Comparison": 4, "Value": "2021-04-20 00:00:00", "GroupName": "slsj"}], "orderBy": ["AA.slsj desc"], "Index": 0, "PageRecordCount": 25, "isPage": false}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_7rts", "url_method": "POST", "extract_variable": [{"variable": "szts7_num", "type": "number", "desc": "水质投诉数量", "realkey": "data.0.tsyhsl", "example": null}], "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1753341611555_9g427ox9o"}, {"type": "workflow", "position": {"x": -40, "y": 620}, "data": {"label": "定性分析-2小时小区投诉事件", "icon": "action-iconfont action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://*************:31080/customerserver3/api/MessageApi/GetRXSJInfo_FQ?Token=8A5DE995-D142-4B3D-84E3-E126C8C25013", "json_data": {"WhereList": [{"WhereName": "AA.hddev_proc_status", "Comparison": 8, "Value": "'ACTIVE'", "GroupName": "hddev_proc_status"}, {"WhereName": "AA.slsj", "Comparison": 4, "Value": "2025-07-02 13:40:00", "GroupName": "slsj_s"}, {"WhereName": "AA.slsj", "Comparison": 7, "Value": "2025-07-02 15:40:00", "GroupName": "slsj_e"}, {"WhereName": "AA.gzlb", "Comparison": 8, "Value": "'有杂质'", "GroupName": "gzlb"}], "orderBy": ["AA.slsj desc"], "Index": 0, "PageRecordCount": 0, "isPage": false}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_dxxq2", "url_method": "POST", "extract_variable": [{"variable": "tsxqsl_dxxq2", "type": "number", "desc": "投诉小区数量", "realkey": "data.tsxqsl"}, {"variable": "tsyhsl_dxxq2", "type": "number", "desc": "投诉用户数量", "realkey": "data.tsyhsl", "example": null}], "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1753342557433_26yns72xc"}, {"type": "workflow", "position": {"x": -40, "y": 880}, "data": {"label": "执行Python代码", "icon": "action-iconfont action-iconfont icon-Python3daimazhihang", "config": {"code": "result = {\n \"codeCount\":\"5\",\n}", "script_file": "", "python_path": "python", "timeout": 15, "capture_output": true, "working_directory": "", "arguments": "", "output_variable": [{"variable": "codeCount", "type": "number", "desc": "报警数", "realkey": "", "example": null}], "error_variable": "", "return_code_variable": "", "show_monitor": false, "retry_times": 1, "retry_delay": 1}, "category": "code_exceute", "description": "执行Python代码片段，最终输出结果必须赋值到result参数，result内容必须是字典", "componentType": "python_execute", "inputs": [], "outputs": ["output_variable", "python_output", "return_code", "error_output"]}, "id": "node_1753342896324_q5rll5gh2"}, {"type": "workflow", "position": {"x": -40, "y": 1160}, "data": {"label": "定性分析-4小时小区投诉事件", "icon": "action-iconfont action-iconfont icon-HTTPPOSTqingqiu", "config": {"url": "http://*************:31080/customerserver3/api/MessageApi/GetRXSJInfo_FQ?Token=8A5DE995-D142-4B3D-84E3-E126C8C25013", "json_data": {"WhereList": [{"WhereName": "AA.hddev_proc_status", "Comparison": 8, "Value": "'ACTIVE'", "GroupName": "hddev_proc_status"}, {"WhereName": "AA.slsj", "Comparison": 4, "Value": "2025-07-02 13:40:00", "GroupName": "slsj_s"}, {"WhereName": "AA.slsj", "Comparison": 7, "Value": "2025-07-02 17:40:00", "GroupName": "slsj_e"}, {"WhereName": "AA.gzlb", "Comparison": 8, "Value": "'有杂质'", "GroupName": "gzlb"}], "orderBy": ["AA.slsj desc"], "Index": 0, "PageRecordCount": 0, "isPage": false}, "headers": {"Content-Type": "application/json", "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE5NzIxMTksImlkIjoiMTc5NDAyMDU5MzM2Mjg0NTY5NyIsImp3dElkIjoiZGRiZWMwNTA2YmYxNDg0MThlNDZlNjVkNjY1M2VlODMiLCJ1aWQiOiIxNzk0MDIwNTkzMzYyODQ1Njk3IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIyMjIwIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTQwMjA1OTMzNjI4NDU2OTcucG5nIiwibmFtZSI6IuWImOS9s-mbryIsImFjY291bnQiOiJaVDAxMyIsIm1vYmlsZSI6IjEzNzY0MDk1NDg3Iiwic24iOiJaVDAxMyIsImdyb3VwIjoiNjkiLCJ5aGxvTnVtIjoiMTAwMDI0MTc3IiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjkifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyNTc3MjE5fQ.jkvasKxDfIs-JIWSVna_zY9XVDmTAIY-xiBEv_hOVfk"}, "timeout": 15, "response_content_variable": "http_response_dxxq4", "url_method": "POST", "extract_variable": [{"variable": "tsxqsl_dxxq4", "type": "number", "desc": "投诉小区数4", "realkey": "data.tsxqsl"}, {"variable": "tsyhsl_dxxq4", "type": "number", "desc": "投诉用户数4", "realkey": "data.tsyhsl"}], "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "show_monitor": true}, "category": "database", "description": "发送HTTP请求", "componentType": "http_request", "inputs": [], "outputs": ["response", "response_text"]}, "id": "node_1753342931663_v4gdu7wuy"}], "edges": [{"source": "start_node", "target": "node_1751421177823_1hov7fzjn", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1751421290781_rn6fvvvd9"}, {"source": "start_node", "target": "node_1751421258628_p5ri51j1h", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1751421294692_2nwu2qhad"}, {"source": "node_1751873980806_f7pevz7tr", "target": "end_node", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1751874225496_j03ost3qf"}, {"source": "node_1752754627355_rz9zgiyah", "target": "node_1752754683410_bbziwmsb2", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752754687768_h47jk1api"}, {"source": "node_1752754876977_mz1e7a99p", "target": "node_1752754911238_494rzsdhu", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752754937368_fxp1lqngq"}, {"source": "node_1752754876977_mz1e7a99p", "target": "node_1752754945248_wyocpalsd", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752754961725_h1exf7b6u"}, {"source": "node_1752754627355_rz9zgiyah", "target": "node_1752755656098_bkq3qi3rw", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752755660092_4n8ti390e"}, {"source": "node_1752755080729_iylemw0d5", "target": "node_1752755912987_xsgp1ocha", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752755919405_t7dkb2c04"}, {"source": "node_1752755080729_iylemw0d5", "target": "node-1752755989591-7yh3r6036", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752755993323_vnnkvsv1v"}, {"source": "node_1752755390433_xsj80mz9b", "target": "node_1752756079542_6sbxpbig7", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752756085118_2odeqpo76"}, {"source": "node_1752755390433_xsj80mz9b", "target": "node-1752756122475-662vtsgca", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752756128168_bkjgjvwym"}, {"source": "node_1752756198883_1eyfbpxib", "target": "node_1752756234403_g40ls21bg", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752756284008_jz0ro3esz"}, {"source": "node_1752756198883_1eyfbpxib", "target": "node-1752756391311-a7i8xoypd", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752756395998_je7zo85c8"}, {"source": "node_1752757256768_i8wceod2z", "target": "node_1752756688593_16bbu4xo5", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752757373307_dtms7ssn7"}, {"source": "node_1752757256768_i8wceod2z", "target": "node_1752756961412_mzudgxgjp", "sourceHandle": "node_1928462879363698688", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752757417174_0r4z2vcgb"}, {"source": "node_1752757256768_i8wceod2z", "target": "node-1752757066387-jp6zizm1g", "sourceHandle": "node_1928463021462523904", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752757503541_7cv8fgpio"}, {"source": "node_1752757256768_i8wceod2z", "target": "node-1752757523451-otjvfjc26", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752757527927_jnbwqjwdz"}, {"source": "node_1752755656098_bkq3qi3rw", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759467123_cj7crkdhk"}, {"source": "node_1752754683410_bbziwmsb2", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759469883_cbf2w5kz2"}, {"source": "node_1752754945248_wyocpalsd", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759472171_h83gi48mz"}, {"source": "node_1752755912987_xsgp1ocha", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759475105_oxbyktm0b"}, {"source": "node-1752755989591-7yh3r6036", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759481957_m2oka0n7a"}, {"source": "node_1752756079542_6sbxpbig7", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759487399_t5biebk7k"}, {"source": "node-1752756122475-662vtsgca", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759489539_i50wb5xhf"}, {"source": "node_1752756234403_g40ls21bg", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759492372_1n4e2n76b"}, {"source": "node-1752756391311-a7i8xoypd", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759495915_oezta5m7d"}, {"source": "node_1752756688593_16bbu4xo5", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759499039_hjrffo2s8"}, {"source": "node_1752756961412_mzudgxgjp", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759501821_j03yumkj1"}, {"source": "node-1752757066387-jp6zizm1g", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759504672_l55y3otrd"}, {"source": "node-1752757523451-otjvfjc26", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759507550_8edyn4fav"}, {"source": "node_1752759392689_n0tyxf10r", "target": "node_1751873980806_f7pevz7tr", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759511702_8eg7f11tt"}, {"source": "node_1752754911238_494rzsdhu", "target": "node_1752759392689_n0tyxf10r", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1752759518752_pfa3ms76d"}, {"source": "node_1751421177823_1hov7fzjn", "target": "node_1752754627355_rz9zgiyah", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753338530196_rotddi6ez"}, {"source": "node_1751421258628_p5ri51j1h", "target": "node_1752754876977_mz1e7a99p", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753338927969_c5htjnqlu"}, {"source": "start_node", "target": "node_1753339160340_7b9uwz4fm", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753339206709_6j1gzo2wh"}, {"source": "node_1753339160340_7b9uwz4fm", "target": "node_1753339592610_t8pdgo09u", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753339648300_z3ssgvhxs"}, {"source": "node_1753339592610_t8pdgo09u", "target": "node_1752755080729_iylemw0d5", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753339772527_ld547bfa5"}, {"source": "start_node", "target": "node_1753341272613_ud3yn9miq", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753341318436_a2qhzotvh"}, {"source": "node_1753341272613_ud3yn9miq", "target": "node_1752755390433_xsj80mz9b", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753341386073_ai4c2w9eh"}, {"source": "start_node", "target": "node_1753341611555_9g427ox9o", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753341666403_m8yl3srf3"}, {"source": "node_1753341611555_9g427ox9o", "target": "node_1752756198883_1eyfbpxib", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753341764315_dfhwfz166"}, {"source": "start_node", "target": "node_1753342557433_26yns72xc", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753342609774_upgmzrkbx"}, {"source": "node_1753342557433_26yns72xc", "target": "node_1752757256768_i8wceod2z", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753342740994_bhf6p6qhi"}, {"source": "node_1753342896324_q5rll5gh2", "target": "node_1752757256768_i8wceod2z", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753342923346_t7hontzom"}, {"source": "start_node", "target": "node_1753342931663_v4gdu7wuy", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753342975732_9js1koyxk"}, {"source": "node_1753342931663_v4gdu7wuy", "target": "node_1752757256768_i8wceod2z", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753343090058_bgrr4cmue"}, {"source": "start_node", "target": "node_1753342896324_q5rll5gh2", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1753453965720_rvrox7c93"}], "viewport": {"x": 56.39765931275707, "y": 184.3993482514636, "zoom": 0.6160439252935217}, "metadata": {"name": "水质监测", "description": "", "version": "1.0.0", "createdAt": "2025-06-30T05:05:51.853Z", "updatedAt": "2025-08-11T09:06:07.581Z"}, "variables": [{"name": "token", "value": "", "type": "string", "scope": "local", "description": "获取当前用户token信息", "isSystem": true}, {"name": "innerCurrentTime", "value": "%Y-%m-%d %H:%M:%S", "type": "currentTime", "scope": "local", "description": "获取当前时间", "isSystem": true}, {"name": "sz_ystbbj", "type": "string", "scope": "local", "value": "用户未发生用水突变报警；", "description": "用水突变报警"}, {"name": "sz_lslbj", "type": "string", "scope": "local", "value": "用户用水正常，未出现持续零水量情况；", "description": "零水量报警"}, {"name": "sz_hbjr", "type": "string", "scope": "local", "value": "户表接入小于10年；", "description": "户表接入年份"}, {"name": "sz_sdl", "type": "string", "scope": "local", "value": "小区500米范围内无大用户监测点发生水倒流情况", "description": "水倒流"}, {"name": "sz_szts7d", "type": "string", "scope": "local", "value": "同小区7日内共计【X】户投诉，小于5户，用户设备问题的可能性较大；（7日水质热线事件＜5）", "description": "同小区用户热线"}, {"name": "level", "type": "string", "scope": "local", "value": "不构成事故", "description": "事故等级"}]}, "options": {}, "taskId": "95a430f5-20c8-4701-b451-c1043724d969"}