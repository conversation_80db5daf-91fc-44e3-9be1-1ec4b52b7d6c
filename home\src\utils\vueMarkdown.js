// 继承自 vue-markdown，并添加了一个自定义的 render 方法
import VueMarkdown from 'vue-markdown';
export default {
    extends: VueMarkdown,
    render(h) {
        // 调用父组件的 render 方法获取默认的渲染结果
        const defaultRender = VueMarkdown.render.call(this, h);
        const md = defaultRender.context.md
        // 自定义渲染逻辑
        md.renderer.rules.image = (tokens, idx, options, env, self) => {
            const srcItem = tokens[idx].attrs.find(item => {
                return item[0] === 'src'
            })
            const src = srcItem[1]
            const alt = tokens[idx].content
            return `<img data-key="${src}" src="${src}" alt="${alt}" onerror="this.style.display='none';"/>`;
        }
        // 自定义超链接逻辑
        const default_link_open = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
            return self.renderToken(tokens, idx, options, env, self);
        };
        md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
            // 查找当前链接的 token
            const aIndex = tokens[idx].attrIndex('target');
            // 如果没有 target 属性，则添加 target="_blank"
            if (aIndex < 0) {
                tokens[idx].attrPush(['target', '_blank']);
            }
            return default_link_open(tokens, idx, options, env, self);
        }

        md.renderer.rules.table_open = () => `<div class="table-wrapper"><table class="${this.tableClass}">\n`
        md.renderer.rules.table_close = () => `</table></div><div class="table-fullscreen table-markdown-fullscreen"><i class="el-icon-full-screen table-markdown-fullscreen">全屏预览</i></div>`
        // 跟踪当前是否在列表项内部
        let inListItem = false;

        // 自定义列表项渲染器
        md.renderer.rules.list_item_open = (tokens, idx) => {
            inListItem = true;
            return '<li>';
        };

        md.renderer.rules.list_item_close = (tokens, idx) => {
            inListItem = false;
            return '</li>';
        };

        // 完全自定义段落渲染 - 列表项内智能处理
        md.renderer.rules.paragraph_open = (tokens, idx, options, env, self) => {
            if (inListItem) {
                // 找到匹配的闭合标签
                const closeIdx = findMatchingCloseToken(tokens, idx);

                if (closeIdx === -1) {
                    return self.renderToken(tokens, idx, options);
                }

                // 标记整个段落范围为已处理，防止默认渲染
                for (let i = idx; i <= closeIdx; i++) {
                    tokens[i].hidden = true;
                }

                // 完全自定义渲染段落内容
                return renderTokensInRange(tokens, idx + 1, closeIdx, options, env);
            }

            return self.renderToken(tokens, idx, options);
        };

        // 完全跳过段落闭合标签
        md.renderer.rules.paragraph_close = (tokens, idx, options, env, self) => {
            if (inListItem) {
                return '';
            }
            return self.renderToken(tokens, idx, options);
        };

        // 渲染指定范围内的所有令牌，防止重复
        function renderTokensInRange(tokens, startIdx, endIdx, options, env) {
            let result = '';

            for (let i = startIdx; i < endIdx; i++) {
                const token = tokens[i];

                // 跳过已标记的标签
                if (token.hidden) continue;

                // 处理内联内容
                if (token.type === 'inline') {
                    result += renderInlineContent(token, options, env);
                    continue;
                }

                // 处理嵌套块元素
                if (token.nesting === 1) { // 开始标签
                    const nestedCloseIdx = findMatchingCloseToken(tokens, i);

                    if (nestedCloseIdx !== -1 && nestedCloseIdx < endIdx) {
                        // 递归渲染嵌套块
                        result += md.renderer.renderToken(token, 0, options, env);
                        result += renderTokensInRange(tokens, i + 1, nestedCloseIdx, options, env);
                        result += md.renderer.renderToken(tokens[nestedCloseIdx], 0, options, env);

                        i = nestedCloseIdx; // 跳到闭合标签后继续
                        continue;
                    }
                }

                // 渲染普通标签
                result += md.renderer.renderToken(token, 0, options, env);
            }

            return result;
        }

        // 渲染内联内容
        function renderInlineContent(token, options, env) {
            let result = '';

            // 使用默认内联渲染器，确保格式正确
            result = md.renderer.renderInline(token.children, options, env);

            return result;
        }

        // 辅助函数：查找匹配的闭合标签
        function findMatchingCloseToken(tokens, openIdx) {
            const openToken = tokens[openIdx];
            let counter = 1;

            for (let i = openIdx + 1; i < tokens.length; i++) {
                if (tokens[i].type === openToken.type.replace('_open', '_close')) {
                    counter--;
                    if (counter === 0) {
                        return i;
                    }
                } else if (tokens[i].type === openToken.type) {
                    counter++;
                }
            }

            return -1; // 未找到匹配的闭合标签
        }

        let outHtml = this.show ? md.render(this.prerender(this.sourceData)) : '';
        outHtml = this.postrender(outHtml);
        this.$emit('rendered', outHtml);
        return h('div', {
            domProps: {
                innerHTML: outHtml
            }
        });
    },
}