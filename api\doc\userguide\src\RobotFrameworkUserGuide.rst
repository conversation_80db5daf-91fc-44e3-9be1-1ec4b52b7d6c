.. include:: <isonum.txt>
.. include:: version.rst
.. include:: roles.rst

==============================
  Robot Framework User Guide
==============================

---------------------
  Version |version|
---------------------

| Copyright |copy| 2008-2015 Nokia Networks
| Copyright |copy| 2016- Robot Framework Foundation
| Licensed under the `Creative Commons Attribution 3.0 Unported`_ license

.. contents:: Table of Contents
   :depth: 3

.. sectnum::
   :depth: 3

~~~~~~~~~~~~~~~~~~~
  Getting started
~~~~~~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: GettingStarted/Introduction.rst
.. include:: GettingStarted/CopyrightAndLicense.rst
.. INSTALL.rst is copied from project root by ug2html.py
.. include:: GettingStarted/INSTALL.rst
.. include:: GettingStarted/Demonstration.rst

~~~~~~~~~~~~~~~~~~~~~~
  Creating test data
~~~~~~~~~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: CreatingTestData/TestDataSyntax.rst
.. include:: CreatingTestData/CreatingTestCases.rst
.. include:: CreatingTestData/CreatingTasks.rst
.. include:: CreatingTestData/CreatingTestSuites.rst
.. include:: CreatingTestData/UsingTestLibraries.rst
.. include:: CreatingTestData/Variables.rst
.. include:: CreatingTestData/CreatingUserKeywords.rst
.. include:: CreatingTestData/ResourceAndVariableFiles.rst
.. include:: CreatingTestData/ControlStructures.rst
.. include:: CreatingTestData/AdvancedFeatures.rst

~~~~~~~~~~~~~~~~~~~~~~~~
  Executing test cases
~~~~~~~~~~~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: ExecutingTestCases/BasicUsage.rst
.. include:: ExecutingTestCases/TestExecution.rst
.. include:: ExecutingTestCases/TaskExecution.rst
.. include:: ExecutingTestCases/PostProcessing.rst
.. include:: ExecutingTestCases/ConfiguringExecution.rst
.. include:: ExecutingTestCases/OutputFiles.rst

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  Extending Robot Framework
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: ExtendingRobotFramework/CreatingTestLibraries.rst
.. include:: ExtendingRobotFramework/RemoteLibrary.rst
.. include:: ExtendingRobotFramework/ListenerInterface.rst
.. include:: ExtendingRobotFramework/ParserInterface.rst

~~~~~~~~~~~~~~~~~~~~
  Supporting Tools
~~~~~~~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: SupportingTools/Libdoc.rst
.. include:: SupportingTools/Testdoc.rst
.. include:: SupportingTools/Tidy.rst
.. include:: SupportingTools/OtherTools.rst

~~~~~~~~~~~~~~
  Appendices
~~~~~~~~~~~~~~

.. contents::
   :depth: 1
   :local:

.. include:: Appendices/AvailableSettings.rst
.. include:: Appendices/CommandLineOptions.rst
.. include:: Appendices/Translations.rst
.. include:: Appendices/DocumentationFormatting.rst
.. include:: Appendices/TimeFormat.rst
.. include:: Appendices/BooleanArguments.rst
.. include:: Appendices/EvaluatingExpressions.rst
.. include:: Appendices/Registrations.rst

.. footer:: Generated by reStructuredText_. Syntax highlighting by Pygments_.


.. Commonly used Robot Framework related link targets

.. 1. Getting started

.. _Creative Commons Attribution 3.0 Unported: http://creativecommons.org/licenses/by/3.0
.. _Apache License 2.0: http://apache.org/licenses/LICENSE-2.0
.. _mailing list: `Mailing lists`_

.. 2. Creating test data

.. _test data: `Creating test data`_
.. _general parsing rules: `Test data syntax`_
.. _section headers: `Test data sections`_
.. _test case: `Creating test cases`_
.. _test cases: `test case`_
.. _test suite: `Creating test suites`_
.. _test suites: `test suite`_
.. _user keyword: `Creating user keywords`_
.. _user keywords: `user keyword`_
.. _higher-level keywords: `user keyword`_
.. _keyword-driven: `Keyword-driven style`_
.. _data-driven: `Data-driven style`_
.. _data-driven approach: `Data-driven style`_
.. _suite file: `Suite files`_
.. _suite directory: `Suite directories`_
.. _initialization file: `Suite initialization files`_
.. _suite initialization file: `Suite initialization files`_
.. _test case name: `Test case name and documentation`_
.. _test case documentation: `Test case name and documentation`_
.. _test setup: `Test setup and teardown`_
.. _test teardown: `Test setup and teardown`_
.. _suite setup: `Suite setup and teardown`_
.. _suite teardown: `Suite setup and teardown`_
.. _teardown: `Test teardown`_
.. _teardowns: teardown_
.. _tag: `Tagging test cases`_
.. _tags: tag_
.. _test template:  `Test templates`_
.. _template keyword: `Test templates`_
.. _test case timeouts: `Test case timeout`_
.. _test timeout: `Test case timeout`_
.. _user keyword timeouts: `User keyword timeout`_
.. _keyword timeout: `User keyword timeout`_
.. _variable: Variables_
.. _automatic variable: `Automatic variables`_
.. _test libraries: `Using test libraries`_
.. _test library: `test libraries`_
.. _libraries: `test libraries`_
.. _library keyword: `test libraries`_
.. _library keywords: `library keyword`_
.. _SeleniumLibrary: https://github.com/robotframework/SeleniumLibrary
.. _SwingLibrary: https://github.com/robotframework/SwingLibrary
.. _localized: Localization_

.. 3. Executing test cases

.. _syslog: `System log`_
.. _test execution: `Starting test execution`_
.. _execution errors: `Errors and warnings during execution`_
.. _test execution errors: `execution errors`_
.. _simple pattern: `Simple patterns`_
.. _output: `Output file`_
.. _outputs: `output`_
.. _output files: `output`_
.. _XML output files: `output`_
.. _log: `Log file`_
.. _logs: log_
.. _log files: log_
.. _test logs: log_
.. _report: `Report file`_
.. _reports: report_
.. _report files: report_
.. _test reports: report_
.. _debug files: `Debug file`_
.. _log level: `Log levels`_
.. _return code: `return codes`_
.. _post-process outputs: `post-processing outputs`_

.. 4. Extending

.. _library API: `Creating test libraries`_
.. _static library API: `Creating keywords`_
.. _listener: `listener interface`_
.. _listeners: `listener interface`_

.. 5. Appendices

.. _HTML formatting: `Documentation formatting`_
.. _settings: `Available settings`_

.. 6. Misc

.. _download page: http://downloads.robotframework.org
.. _version control system: http://source.robotframework.org
.. _previous User Guides: http://robotframework.org/robotframework/#user-guide
.. _schema file: https://github.com/robotframework/robotframework/tree/master/doc/schema#readme

.. API docs

.. _API documentation: http://robot-framework.readthedocs.org
.. _visitor interface: http://robot-framework.readthedocs.org/en/master/autodoc/robot.model.html#module-robot.model.visitor
.. _running.TestSuite: http://robot-framework.readthedocs.org/en/master/autodoc/robot.running.html#robot.running.model.TestSuite
.. _running.TestCase: http://robot-framework.readthedocs.org/en/master/autodoc/robot.running.html#robot.running.model.TestCase
.. _running.Keyword: http://robot-framework.readthedocs.org/en/master/autodoc/robot.running.html#robot.running.model.Keyword
.. _running.UserKeyword: https://robot-framework.readthedocs.io/en/master/autodoc/robot.running.html#robot.running.resourcemodel.UserKeyword
.. _running.LibraryKeyword: https://robot-framework.readthedocs.io/en/master/autodoc/robot.running.html#robot.running.librarykeyword.LibraryKeyword
.. _running.InvalidKeyword: https://robot-framework.readthedocs.io/en/master/autodoc/robot.running.html#robot.running.invalidkeyword.InvalidKeyword
.. _running.TestLibrary: https://robot-framework.readthedocs.io/en/stable/autodoc/robot.running.html#robot.running.testlibraries.TestLibrary
.. _running.ResourceFile: https://robot-framework.readthedocs.io/en/stable/autodoc/robot.running.html#robot.running.resourcemodel.ResourceFile
.. _running.Import: https://robot-framework.readthedocs.io/en/stable/autodoc/robot.running.html#robot.running.resourcemodel.Import
.. _running model: http://robot-framework.readthedocs.org/en/master/autodoc/robot.running.html#module-robot.running.model
.. _result.TestSuite: http://robot-framework.readthedocs.org/en/master/autodoc/robot.result.html#robot.result.model.TestSuite
.. _result.TestCase: http://robot-framework.readthedocs.org/en/master/autodoc/robot.result.html#robot.result.model.TestCase
.. _result.Keyword: http://robot-framework.readthedocs.org/en/master/autodoc/robot.result.html#robot.result.model.Keyword
.. _result.Message: http://robot-framework.readthedocs.org/en/master/autodoc/robot.result.html#robot.result.model.Message
.. _result model: http://robot-framework.readthedocs.org/en/master/autodoc/robot.result.html#module-robot.result.model
.. _ListenerV2: https://robot-framework.readthedocs.io/en/master/autodoc/robot.api.html#robot.api.interfaces.ListenerV2
.. _ListenerV3: https://robot-framework.readthedocs.io/en/master/autodoc/robot.api.html#robot.api.interfaces.ListenerV3

.. External link targets

.. _reStructuredText: https://en.wikipedia.org/wiki/ReStructuredText
.. _docutils: https://pypi.python.org/pypi/docutils
.. _Sphinx: http://sphinx-doc.org/
.. _Pygments: http://pygments.org/
.. _Read the Docs: http://readthedocs.org
.. _AutoIT: http://www.autoitscript.com/autoit3
.. _XML-RPC: http://www.xmlrpc.com/
.. _RIDE: https://github.com/robotframework/RIDE
.. _Slack: http://slack.robotframework.org
