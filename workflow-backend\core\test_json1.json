{"workflow": {"nodes": [{"id": "start_node", "type": "start", "position": {"x": -180, "y": 120}, "data": {"label": "开始", "config": {"workflow_name": "", "description": "", "execution_mode": "immediate", "schedule_type": "once", "schedule_time": "", "daily_time": "09:00", "error_strategy": "stop", "max_retries": 3, "retry_delay": 5, "log_level": "INFO", "confirm_start": false, "timeout": 300, "type": "taskMonitor"}, "category": "control", "description": "工作流的开始节点，标识流程入口点", "componentType": "workflow_start", "inputs": [], "outputs": ["workflow_context"], "isSystemNode": true}}, {"id": "end_node", "type": "end", "position": {"x": 1840, "y": 200}, "data": {"label": "结束", "config": {"status": "success", "message": "", "return_data": "", "notify_completion": true, "notification_title": "工作流执行完成", "notification_message": "", "notification_type": "success", "notification_duration": 5, "cleanup": true, "log_summary": true, "type": "taskMonitor"}, "category": "control", "description": "工作流的结束节点，标识流程完成", "componentType": "workflow_end", "inputs": ["workflow_context"], "outputs": [], "isSystemNode": true}}, {"type": "workflow", "position": {"x": 20, "y": -140}, "data": {"label": "执行Javascript代码", "icon": "action-iconfont icon-JavaScriptdaimazhihang", "config": {"code": "const result = \"aaabbc\"", "script_file": "", "node_path": "", "timeout": 60, "capture_output": true, "working_directory": "", "arguments": "", "output_variable": "js_result2", "error_variable": "", "return_code_variable": "", "encoding": "utf-8", "retry_times": 1, "retry_delay": 1, "type": "taskMonitor", "show_monitor": true}, "category": "code_exceute", "description": "执行Javascript代码片段或脚本文件", "componentType": "javascript_execute", "inputs": [], "outputs": ["js_output", "return_code", "error_output"]}, "id": "node_1750988425918_oepvajhl8"}, {"type": "workflow", "position": {"x": 20, "y": 340}, "data": {"label": "执行Javascript代码", "icon": "action-iconfont icon-JavaScriptdaimazhihang", "config": {"code": "const result = 15", "script_file": "", "node_path": "", "timeout": 60, "capture_output": true, "working_directory": "", "arguments": "", "output_variable": "js_result", "error_variable": "", "return_code_variable": "", "encoding": "utf-8", "retry_times": 1, "retry_delay": 1, "type": "taskMonitor"}, "category": "code_exceute", "description": "执行Javascript代码片段或脚本文件", "componentType": "javascript_execute", "inputs": [], "outputs": ["js_output", "return_code", "error_output"]}, "id": "node_1750988427409_rurcx69u3"}, {"type": "workflow", "position": {"x": 280, "y": -360}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1921043720967294976", "field": "${js_result}", "value": "20", "operator": "equals"}]}, {"condition_id": "node_1921044001658507264", "relation": "and", "conditions": [{"condition_id": "node_1921044005424992256", "field": "${js_result}", "value": "10", "operator": "equals"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1750988510890_9exorvmub"}, {"type": "workflow", "position": {"x": 820, "y": -400}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了条件20\"}]", "timeout": 30, "retry_times": 1, "retry_delay": 1}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988612947_w7eh66hl0"}, {"type": "workflow", "position": {"x": 840, "y": -140}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了条件10\"}]", "retry_times": 1, "retry_delay": 1}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988660005_ppo81be8u"}, {"type": "workflow", "position": {"x": 800, "y": 0}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了第二个条件判断等于5\"}]", "retry_times": 1, "retry_delay": 1}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988661544_ftpq8bwqm"}, {"type": "workflow", "position": {"x": 800, "y": 120}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了第二个条件判断包含ab\"}]", "retry_times": 1, "retry_delay": 1, "timeout": 10, "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988662774_tvmeojxj3"}, {"type": "workflow", "position": {"x": 620, "y": 260}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了第二个条件的else\"}]", "retry_times": 1, "retry_delay": 1}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988664737_djnw6ohe7"}, {"type": "workflow", "position": {"x": 400, "y": 40}, "data": {"label": "条件判断", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"conditions": [{"relation": "and", "condition_id": "node_start", "conditions": [{"condition_id": "node_1921044510809264128", "field": "${js_result}", "value": "5", "operator": "equals"}]}, {"condition_id": "node_1921044711829671936", "relation": "and", "conditions": [{"condition_id": "node_1921044715885563904", "field": "${js_result2}", "value": "ab", "operator": "contains"}]}]}, "category": "judgecycle", "description": "根据条件执行不同的分支", "componentType": "condition", "inputs": [], "outputs": []}, "id": "node_1750988694241_8c564peot"}, {"type": "workflow", "position": {"x": 160, "y": 680}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output_title\",\"value\":\"条件判断\"}]", "retry_times": 1, "retry_delay": 1, "type": "taskMonitor"}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1750988906248_fqyjb4455"}, {"type": "workflow", "position": {"x": 1440, "y": 300}, "data": {"label": "创建Word文件", "icon": "action-iconfont icon-<PERSON><PERSON><PERSON><PERSON>", "config": {"content": "${output}", "output_folder": "d:/temp", "output_filename": "xxx.docx", "title": "${output_title}", "font_size": 12, "timeout": 10, "retry_times": 1, "retry_delay": 1, "error_handle": "stop", "type": "taskMonitor"}, "category": "file", "description": "将内容保存为一个Word文档", "componentType": "word_create", "inputs": ["content"], "outputs": ["word_path"]}, "id": "node_1750988968933_bwzrlt0ud"}, {"type": "workflow", "position": {"x": 840, "y": -260}, "data": {"label": "变量赋值", "icon": "action-iconfont icon-ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"variables": "[{\"field\":\"output\",\"value\":\"执行了条件20，并过两次赋值\"}]", "retry_times": 1, "retry_delay": 1}, "category": "data_pocess", "description": "变量赋值节点用于向可写入变量进行变量赋值", "componentType": "variable_assignment", "inputs": [], "outputs": []}, "id": "node_1751001789805_8envtimux"}], "edges": [{"source": "start_node", "target": "node_1750988425918_oepvajhl8", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988495576_3o5c94un9"}, {"source": "start_node", "target": "node_1750988427409_rurcx69u3", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988497941_xl8im59bo"}, {"source": "node_1750988425918_oepvajhl8", "target": "node_1750988510890_9exorvmub", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988564905_zdrhyr25t"}, {"source": "node_1750988427409_rurcx69u3", "target": "node_1750988510890_9exorvmub", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988571690_5znvk1fgw"}, {"source": "node_1750988510890_9exorvmub", "target": "node_1750988612947_w7eh66hl0", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988627364_l6ugxzci9"}, {"source": "node_1750988510890_9exorvmub", "target": "node_1750988660005_ppo81be8u", "sourceHandle": "node_1921044001658507264", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988668083_cnjf26njy"}, {"source": "node_1750988510890_9exorvmub", "target": "node_1750988694241_8c564peot", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988699522_9mvagi2dg"}, {"source": "node_1750988694241_8c564peot", "target": "node_1750988661544_ftpq8bwqm", "sourceHandle": "node_start", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988812462_dywgq7q72"}, {"source": "node_1750988694241_8c564peot", "target": "node_1750988664737_djnw6ohe7", "sourceHandle": "node_end", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988873504_uvnbq2vqa"}, {"source": "start_node", "target": "node_1750988906248_fqyjb4455", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988945325_kb1ttqbxo"}, {"source": "node_1750988906248_fqyjb4455", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988972583_td90mmtga"}, {"source": "node_1750988612947_w7eh66hl0", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988974616_r5r32910m"}, {"source": "node_1750988660005_ppo81be8u", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988976719_aawyg39eb"}, {"source": "node_1750988661544_ftpq8bwqm", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988978039_vmyzjrubt"}, {"source": "node_1750988662774_tvmeojxj3", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988984582_pt2nh65zf"}, {"source": "node_1750988664737_djnw6ohe7", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988986586_rwv1ck362"}, {"source": "node_1750988968933_bwzrlt0ud", "target": "end_node", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750988991840_t7f018i59"}, {"source": "node_1750988694241_8c564peot", "target": "node_1750988662774_tvmeojxj3", "sourceHandle": "node_1921044711829671936", "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1750996411604_3q12hqmna"}, {"source": "node_1751001789805_8envtimux", "target": "node_1750988968933_bwzrlt0ud", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1751001810795_d2pm0bj31"}, {"source": "node_1750988612947_w7eh66hl0", "target": "node_1751001789805_8envtimux", "sourceHandle": null, "targetHandle": null, "type": "workflow", "animated": true, "data": {"type": "control"}, "id": "edge_1751001825366_976kmzsvd"}], "viewport": {"x": 219.88899275531662, "y": 336.34470670717457, "zoom": 0.7338163122224819}, "metadata": {"name": "test_if_else", "description": "", "version": "1.0.0", "createdAt": "2025-06-25T02:00:27.952Z", "updatedAt": "2025-08-08T08:50:56.099Z"}, "variables": [{"name": "token", "value": "", "type": "string", "scope": "local", "description": "获取当前用户token信息", "isSystem": true}, {"name": "innerCurrentTime", "value": "%Y-%m-%d %H:%M:%S", "type": "currentTime", "scope": "local", "description": "获取当前时间", "isSystem": true}]}, "options": {}, "taskId": "d6a44481-d897-400f-b6c6-07358a9005af"}