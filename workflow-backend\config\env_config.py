# 环境配置
import requests
from loguru import logger

# test env
# WIMTASK_URL = "http://saasbate.dlmeasure.com:1801/wimai/api/task/config/query"
WIMTASK_API = "https://wimtask.dlmeasure.com"
# prod env


config = {}
# 度量云
DLY_URL = "dlyUrl"
# bpm
BPM_URL = "bpmUrl"
# AI
AI_RUL = "aiUrl"
# 图像识别
CV_URL = "cvUrl"
# 一诺平台地址
YN_URL = "ynUrl"

#监控通知地址
TaskMonitor_Url = "taskMonitorUrl"

# 算法平台
ALGORITHM_PLATFORM_URL = "algorithmPlatformUrl"


def get_config_item(key: str) -> any:
    from .globals import token

    if not config:

        param = {"index": -1, "size": -1, "data": {}}

        headers = {"Content-Type": "application/json", "Authorization": token}
        url = f"{WIMTASK_API}/wimai/api/task/config/query"
        logger.info(f"初始化请求配置{url}")
        response = requests.post(url, json=param, headers=headers)
        if (
            response.status_code == 200
            and response.json()["Code"] == 0
            and response.json()["Response"]["rows"]
        ):
            for row in response.json()["Response"]["rows"]:
                config[row["code"]] = row["value"]
            logger.info(f"获得远端的配置信息{config}")
        else:
            logger.error(f"请求配置失败,response:{response.text}")
    return config.get(key)
