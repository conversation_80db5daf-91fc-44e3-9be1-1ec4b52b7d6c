#!/usr/bin/env python3
"""
统一浏览器管理器
让录制和执行使用同一个浏览器安装，便于打包分发
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

class BrowserManager:
    """统一浏览器管理器"""

    def __init__(self, project_root: Optional[Path] = None):
        # workflow-backend 目录作为根目录
        if project_root is None:
            # 从 workflow-backend/utils/browser_manager.py 推导 workflow-backend 目录
            self.project_root = Path(__file__).parent.parent
        else:
            self.project_root = project_root

        # 浏览器放在 workflow-backend/plugins 目录下
        self.plugins_dir = self.project_root / "plugins"
        self.browsers_dir = self.plugins_dir / "browsers"
        self.browser_data_dir = self.plugins_dir / "browser_data"

    def get_browsers_path(self) -> str:
        """获取浏览器安装路径"""
        return str(self.browsers_dir.resolve())

    def get_browser_data_path(self) -> str:
        """获取浏览器数据路径"""
        return str(self.browser_data_dir.resolve())

    def setup_environment(self) -> Dict[str, str]:
        """设置统一的浏览器环境变量"""
        env = os.environ.copy()

        # 设置Playwright浏览器路径（录制和执行都使用）
        browsers_path = self.get_browsers_path()
        env['PLAYWRIGHT_BROWSERS_PATH'] = browsers_path

        # 设置浏览器数据目录
        env['BROWSER_DATA_PATH'] = self.get_browser_data_path()

        logger.info(f"设置浏览器路径: {browsers_path}")
        logger.info(f"设置数据路径: {self.get_browser_data_path()}")

        return env

    def ensure_directories(self):
        """确保必要的目录存在"""
        # 确保插件目录存在
        self.plugins_dir.mkdir(parents=True, exist_ok=True)
        self.browsers_dir.mkdir(parents=True, exist_ok=True)
        self.browser_data_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"插件目录: {self.plugins_dir}")
        logger.info(f"浏览器目录: {self.browsers_dir}")
        logger.info(f"数据目录: {self.browser_data_dir}")

    def install_browsers(self) -> bool:
        """安装两个版本的浏览器到项目目录"""
        logger.info("开始安装两个版本的浏览器到项目目录...")
        logger.info("录制需要: Patchright chromium-1169")
        logger.info("执行需要: Playwright chromium-1178")

        try:
            # 确保目录存在
            self.ensure_directories()

            # 设置环境变量
            env = self.setup_environment()

            success_count = 0

            # 1. 安装Playwright浏览器 (执行用)
            logger.info("1. 安装Playwright浏览器 (chromium-1178)...")
            try:
                cmd = ["rfbrowser", "init", "chromium"]
                logger.info(f"执行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    logger.info("Playwright浏览器安装成功")
                    success_count += 1
                else:
                    logger.warning(f"Playwright浏览器安装失败: {result.stderr}")
            except Exception as e:
                logger.warning(f"Playwright浏览器安装异常: {e}")

            # 2. 安装Patchright浏览器 (录制用)
            logger.info("2. 安装Patchright浏览器 (chromium-1169)...")
            try:
                cmd = [sys.executable, "-m", "patchright", "install", "chromium"]
                logger.info(f"执行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    logger.info("Patchright浏览器安装成功")
                    success_count += 1
                else:
                    logger.warning(f"Patchright浏览器安装失败: {result.stderr}")
            except Exception as e:
                logger.warning(f"Patchright浏览器安装异常: {e}")

            if success_count > 0:
                logger.info(f"浏览器安装完成，成功安装 {success_count}/2 个版本")
                return True
            else:
                logger.error("所有浏览器版本安装都失败了")
                return False

        except Exception as e:
            logger.error(f"安装浏览器时发生错误: {e}")
            return False

    def verify_installation(self) -> bool:
        """验证浏览器安装"""
        logger.info("验证浏览器安装...")

        if not self.browsers_dir.exists():
            logger.error("浏览器目录不存在")
            return False

        # 查找Chromium安装
        chromium_dirs = list(self.browsers_dir.glob("chromium-*"))
        if chromium_dirs:
            logger.info(f"找到Chromium: {chromium_dirs[0]}")
            return True
        else:
            logger.error("未找到Chromium安装")
            return False

    def get_browser_info(self) -> Dict[str, Any]:
        """获取浏览器信息"""
        info = {
            "browsers_path": self.get_browsers_path(),
            "data_path": self.get_browser_data_path(),
            "installed": self.verify_installation(),
            "browsers": []
        }

        if self.browsers_dir.exists():
            for item in self.browsers_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    try:
                        size_mb = sum(f.stat().st_size for f in item.rglob('*') if f.is_file()) / (1024 * 1024)
                        info["browsers"].append({
                            "name": item.name,
                            "path": str(item),
                            "size_mb": round(size_mb, 1)
                        })
                    except Exception as e:
                        logger.warning(f"无法计算 {item.name} 大小: {e}")

        return info

    def configure_recording_service(self):
        """配置录制服务使用统一浏览器"""
        # 设置Workflow-Use配置
        try:
            from experimental.workflow_use.config import WorkflowUseConfig
            config = WorkflowUseConfig()
            config.set('browser_path', self.get_browsers_path())
            config.set('browser_data_path', self.get_browser_data_path())
            logger.info("录制服务配置已更新")
        except ImportError:
            logger.warning("Workflow-Use配置模块不可用")

    def configure_execution_service(self):
        """配置执行服务使用统一浏览器"""
        # 设置RPA Framework环境变量
        os.environ['PLAYWRIGHT_BROWSERS_PATH'] = self.get_browsers_path()
        logger.info("执行服务配置已更新")

    def apply_unified_config(self):
        """应用统一配置"""
        logger.info("应用统一浏览器配置...")

        # 设置环境变量
        env = self.setup_environment()
        for key, value in env.items():
            if key.startswith(('PLAYWRIGHT_', 'BROWSER_')):
                os.environ[key] = value

        # 配置各个服务
        self.configure_recording_service()
        self.configure_execution_service()

        logger.info("统一浏览器配置已应用")

# 全局浏览器管理器实例
browser_manager = BrowserManager()

def setup_unified_browser():
    """设置统一浏览器（在应用启动时调用）"""
    browser_manager.apply_unified_config()
    return browser_manager

def get_browser_environment() -> Dict[str, str]:
    """获取浏览器环境变量"""
    return browser_manager.setup_environment()

def install_project_browsers() -> bool:
    """安装浏览器到项目目录"""
    return browser_manager.install_browsers()

def verify_browser_installation() -> bool:
    """验证浏览器安装"""
    return browser_manager.verify_installation()

def get_browser_status() -> Dict[str, Any]:
    """获取浏览器状态信息"""
    return browser_manager.get_browser_info()

if __name__ == "__main__":
    # 命令行使用
    import argparse

    parser = argparse.ArgumentParser(description="统一浏览器管理器")
    parser.add_argument("--install", action="store_true", help="安装浏览器")
    parser.add_argument("--verify", action="store_true", help="验证安装")
    parser.add_argument("--info", action="store_true", help="显示信息")
    parser.add_argument("--setup", action="store_true", help="设置统一配置")

    args = parser.parse_args()

    if args.install:
        success = install_project_browsers()
        sys.exit(0 if success else 1)
    elif args.verify:
        success = verify_browser_installation()
        sys.exit(0 if success else 1)
    elif args.info:
        info = get_browser_status()
        print(f"浏览器路径: {info['browsers_path']}")
        print(f"数据路径: {info['data_path']}")
        print(f"安装状态: {'已安装' if info['installed'] else '未安装'}")
        for browser in info['browsers']:
            print(f"  - {browser['name']}: {browser['size_mb']} MB")
    elif args.setup:
        setup_unified_browser()
        print("统一浏览器配置已设置")
    else:
        parser.print_help()
