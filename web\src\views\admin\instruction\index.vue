<template>
    <div class="instruction" v-loading="loading">
        <div class="header">
            <div class="title-section">
                指令管理
                <div class="header-tools">
                    <!-- <el-button class="header-tools-item" type="circle" size="mini" @click="handleCateManage">
                        分类管理
                    </el-button>
                    <el-divider direction="vertical" style="margin: 0 16px" /> -->
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
                        <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                        刷新
                    </el-button>
                    <!-- <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                        新增
                    </el-button> -->
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onEdit(null)">
                        <i class="action-iconfont icon-bianji"></i>
                        编辑
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu"></i>
                        删除
                    </el-button>
                </div>
            </div>
            <div class="condition-section">
                <el-form :inline="true" :model="params">
                    <el-form-item label="指令分类">
                        <el-select v-model="params.type" placeholder="" style="width: 160px" @change="changeType" clearable>
                            <el-option label="全部类型" value="all" />
                            <el-option v-for="it in instructionTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.isFree" placeholder="请选择状态" @change="changeFree" style="width: 160px" clearable>
                            <el-option v-for="it in [{'Value':'all','Name':'全部'},{'Value':1,'Name':'免费'},{'Value':0,'Name':'收费'}]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="params.name" placeholder="指令名称" maxlength="16" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="table-section">
            <div class="table-content" v-show="tabType == 'table'">
                <!-- stripe -->
                <el-table class="service-table" ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height:calc(100% - 48px)"
                    @rowClick="handleRowClick">
                    <el-table-column type="index" label="序号"
                        :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                    <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'status'" #default="{ row }">
                            <el-tag type="info" round v-if="row.status === 0">
                                已下架
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.status === 1">
                                已上架
                            </el-tag>
                        </template>
                        <template v-if="it.scoped == 'isFree'" #default="{ row }">
                            <el-tag type="success" round v-if="row.isFree === 0">
                                收费
                            </el-tag>
                            <el-tag type="info" round v-else-if="row.isFree === 1">
                                免费
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'iconPath'" #default="{ row }">
                            <i class="icon action-iconfont" :class="row.iconPath"></i>
                        </template>
                        <template v-else-if="it.scoped == 'commandType'" #default="{ row }">
                            {{ instructionTypes.find(it => it.Value === row.commandType)?.Name || '' }}
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <!-- <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link> -->
                            <el-link type="primary" class="task-link" @click="changeStatus(row)">{{ row.status === 0 ? '上架' : '下架' }}</el-link>
                            <!-- <el-link type="danger" class="task-link" @click="onDelete(row)" :disabled="row.status === 1">删除</el-link> -->
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 50vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                        :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total" @change="tableQuery(false)" />
                </div>
            </div>
        </div>
    </div>

    <!-- 新增指令弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="dialogFormVisible" :title="isNew?'新增':'编辑'" width="500" style="border-radius: 4px;" :append-to-body="false" :destroy-on-close="true" top="10vh">
        <el-form class="instruction-form instruction-template-form" :model="form" ref="ruleFormRef" label-width="110px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="code" label="选择指令" required :label-line="true">
                        <el-select v-model="form.code" :disabled="!isNew" @change="commandChange" filterable placeholder="请选择指令分类">
                            <el-option v-for="it in instructionList" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="24">
                    <el-form-item prop="name" label="指令名称" required :label-line="true">
                        <el-input v-model.trim="form.name" :maxlength="20" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="categoryMain" label="指令分类" required :label-line="true">
                        <el-select v-model="form.categoryMain" placeholder="请选择指令分类">
                            <el-option v-for="it in instructionTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col> -->
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="指令图标" :label-line="true">
                        <div v-if="!form.iconPath">
                            <el-button style="margin-left: 8px;" type="primary" size="mini" @click.stop="showIconDialog = true">
                                选择默认图标
                            </el-button>
                        </div>
                        <div class="icon-preview small-height" v-else>
                            <div class="icon-preview-item">
                                <i class="icon action-iconfont" :class="form.iconPath"></i>
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="form.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="描述" required :label-line="true">
                            <!-- contentType="text"   默认delta  html  text -->
                            <el-input v-model="form.description" maxlength="100" :rows="4" type="textarea" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="isFree" label="是否免费" required :label-line="true">
                        <el-switch v-model="form.isFree" inline-prompt active-text="免费" inactive-text="收费" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="save()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理弹窗 -->
    <el-dialog class="servicePackage-dialog" :append-to-body="false" v-model="cateManageVisible" :title="'分类管理'" width="1042" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <div class="title-section cate-header-tools">
            分类列表
            <div class="header-tools">
                <el-button class="header-tools-item" type="circle" size="mini" @click="onResetCate">
                    <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                    刷新
                </el-button>
                <!-- <el-button class="header-tools-item" type="circle" size="mini" @click="onAddCate">
                    <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                    新增
                </el-button> -->
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onEditCate(null)">
                    <i class="action-iconfont icon-bianji"></i>
                    编辑
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onDeleteCate(null)">
                    <i class="action-iconfont icon-huishouzhanshanchu"></i>
                    删除
                </el-button>
            </div>
        </div>
        <el-table class="cate-table" ref="cateTableRef" :data="cateTableData" border :show-overflow-tooltip="true"
            :highlight-current-row="true" style="width: 100%;height: 545px"
            @rowClick="handleCateRowClick">
            <el-table-column type="index" label="序号"
                :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                <el-table-column v-for="it in cateTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                <template v-if="it.scoped == 'status'" #default="{ row }">
                    <el-tag type="danger" round v-if="row.status === 'disable'">
                        禁用
                    </el-tag>
                    <el-tag type="success" round v-else-if="row.status === 'enable'">
                        启用
                    </el-tag>
                </template>
                <template v-else-if="it.scoped == 'image'" #default="{ row }">
                    <img v-if="row.iconPath" class="table-icon" :src="utils.require(row.iconPath)" alt="">
                </template>
                <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                    <el-link type="primary" class="task-link" @click="onEditCate(row)">编辑</el-link>
                    <el-link type="danger" class="task-link" style="margin-left:16px;" @click="onDeleteCate(row)">删除</el-link>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" style="height: 50vh;" />
            </template>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cateManageVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理编辑弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="editCateVisible" :title="params.type==='template'? '编辑模版分类' : '编辑场景分类'" width="480" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <el-form class="instruction-form instruction-template-form" :model="cateForm" ref="cateFormRef" label-width="100px" :rules="cateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="分类名称" required :label-line="true">
                        <el-input v-model.trim="cateForm.name" :maxlength="32" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="分类图标" :label-line="true">
                        <div v-if="!cateForm.iconPath">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onCateUploaded" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div class="upload-item">
                                    <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview small-height" v-else>
                            <div class="icon-preview-item">
                                <img :src="cateForm.iconPath" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="cateForm.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="order" label="排序" :label-line="true">
                        <el-input-number
                            style="width:100px;"
                            :controls="false"
                            v-model="cateForm.order"
                            :min="1"
                            :max="10"
                            controls-position="right"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editCateVisible = false">取消</el-button>
                <el-button type="primary" @click="saveCate()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 默认图标选择弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="showIconDialog" title="选择默认图标" width="500" style="border-radius: 4px;" :destroy-on-close="true" top="20vh">
        <div class="icon-box icon_lists">
            <div class="icon-item" :class="{ active: iconId === 'icon-'+item.font_class }" v-for="(item, index) in iconfontJson.glyphs" :key="'icon'+index" @click="iconId = 'icon-'+ item.font_class">
                <span class="icon action-iconfont" :class="'icon-'+item.font_class"></span>
                <div class="name">{{ item.name }}</div>
                <!-- <div class="code-name">{{ '&#x'+item.unicode }};</div> -->
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showIconDialog = false">取消</el-button>
                <el-button type="primary" @click="form.iconPath = iconId;showIconDialog = false;">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { componentCategories } from "@/utils/componentCategories"
import iconfontJson from "@/assets/action-fonts/iconfont.json"
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'

const route = useRoute()
const userStore = useUserStore()

;
const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
    "Authorization": Authorization.value,
    "FROM_CHANNEL": "web"
})
const iconId = ref(0)
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const isNew = ref(true)
const form = ref({
    name: "",
    commandType: "",
    description:"",
    iconPath:"",
    isFree:1
})
const dialogFormVisible = ref(false)

const editorOptions = ref({
    // debug: 'info',
    modules: {
        toolbar: ["bold", "italic", "underline", "strike", "link", { list: "ordered" }, { list: "bullet" }]
    },
    placeholder: '请输入详情',
    // readOnly: true,
    theme: 'snow'
})

const types = ref([
    { Name: '场景', Value: 'scene' },
    { Name: 'MCP', Value: 'mcp' },
    { Name: '模板', Value: 'template' },
])
const classify = ref([])
const status = ref([
    { Name: '全部状态', Value: 'all' },
    { Name: '已上架', Value: 1 },
    { Name: '已下架', Value: 0 }
])
const tabType = ref('table')
const params = ref({
    type: 'all',
    isFree: 'all',
    name: ""
})
// 定义 tableData 中元素的类型
interface TableDataItem {
    id: string;
    iconPath: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscriptionCount?: string;
    downloadCount?: number;
    formatType?: string;
    status: any;
}
const tableData = ref<TableDataItem[]>([])
const tableColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true },
    { data: 'name', title: '指令名称', minWidth: 200, orderable: true, filterable: true },
    { data: 'commandType', title: '指令分类', scoped: 'commandType', minWidth: 160, orderable: true, filterable: true },
    { data: 'description', title: '描述', minWidth: 240, orderable: true, filterable: true },
    { data: 'isFree', title: '是否免费', scoped: 'isFree', minWidth: 120, orderable: true, filterable: true },
    { data: 'updater', title: '最后编辑人', minWidth: 120, orderable: true, filterable: true },
    { data: 'updated', title: '最后编辑时间', minWidth: 160, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 80, fixed: 'right' },
])
interface versionTableDataItem {
    id: string;
    name: string;
    versionNumber: string;
    downloadCount: number;
    status: any;
}
const versionTableData = ref<versionTableDataItem[]>([])
const versionTableColumns = ref([
    { data: 'versionNumber', title: '版本号', scoped: 'versionNumber', width: 150, orderable: true, filterable: true },
    { data: 'created', title: '发布日期', scoped: 'created', orderable: true, filterable: true },
    { data: 'description', title: '版本描述', width: 160, orderable: true, filterable: true },
    // { data: 'releaseStatus', title: '状态', scoped: 'releaseStatus', width: 100, orderable: true, filterable: true },
    { data: 'downloadCount', title: '使用量', width: 80, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 210, orderable: true, filterable: true },
])

// 图片选择弹窗
const showIconDialog = ref(false)

const ruleFormRef = ref<FormInstance>()


//ref
const tableRef = ref<HTMLElement>()
const serviceTableRef = ref<HTMLElement>()


const versionLoading = ref(false)
const currentVersionRow = ref(null)

//分类管理
const cateManageVisible = ref(false)
const cateTableColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'image', width: 120, orderable: true, filterable: true },
    { data: 'name', title: '名称', minWidth: 150, orderable: true, filterable: true },
    { data: 'order', title: '排序', minWidth: 50, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', minWidth: 150, fixed: 'right' }
])
const currentCateRow = ref(null)
const cateTableData = ref([
])
const isCateNew = ref(true)
const cateForm = ref({
    name: '',
    iconPath:"",
    order:1
})
const editCateVisible = ref(false)

const cateFormRef = ref<FormInstance>()



//computed
// isUniwimPc
const isUniwimPc = computed(() => {
    return route.query.uniwim === 'pc'
})

const instructionTypes = computed(()=>{
    let types:any = []
    componentCategories.forEach(category=>{
        types.push({
            Name:category.label,
            Value:category.name
        })
    })
    return types
})
const instructionMap = {}
const instructionTypeMap = {}
const instructionList = computed(()=>{
    let list:any = []
    debugger;
    componentCategories.forEach(category=>{
        instructionTypeMap[category.name] = category.label
        if(category.components){
            category.components.forEach(com=>{
                list.push({
                    Name:category.label+"："+com.label,
                    Label:com.label,
                    category:category.name,
                    Value:com.type
                })
                instructionMap[com.type]=com.label
            })
        }
    })
    return list
})

const currentUser = computed(() => {
    return userStore.userInfo
})

//分类管理方法

const commandChange = (val:string)=>{
    let obj = instructionList.value.find(it=>it.Value==val)
    debugger;
    if(obj){
        form.value.name = obj.Label
        form.value.commandType = obj.category
    }
}

const handleCateManage = () => {
   cateManageVisible.value = true;
   cateTableQuery();
}

const cateTableQuery = (noloading: boolean = false) => {
    ;
    if (!noloading) {
        loading.value = true
    }
    currentCateRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},

    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach((row:any)=>{
                if(row.typeList){
                    row.applicableType = row.typeList.map((tp:any)=>tp.type)
                }
            })
            cateTableData.value = res.rows
        } else {
            cateTableData.value = []
        }
    })
    .catch((err: any) => {
        cateTableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}

const onResetCate = () => {
    isCateNew.value = true;
    currentCateRow.value = null
    cateTableData.value = []
    cateTableQuery()
}

const onAddCate = () => {
    isCateNew.value = true
    editCateVisible.value = true
    cateForm.value = {
        name: '',
        iconPath: "",
        order: cateTableData.value.length+1
    }
}
const onEditCate = async (row: any) => {
    if (row) currentCateRow.value = row
    isCateNew.value = false
    cateForm.value = JSON.parse(JSON.stringify(await formSet(currentCateRow.value)))
    editCateVisible.value = true
}
const onDeleteCate = async (row: any) => {
    await ElMessageBox.confirm('是否删除当前分类？', '确认删除', {
        type: 'warning',
    })
    if (row) currentCateRow.value = row
    saasApi.AIAgentResourceCategoryDelete([currentCateRow.value.id]).then((res: any) => {
        if (res.Code === 0) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            cateTableQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {},
        size:Infinity,
        index:1
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            classify.value = res.rows.map(it => {
                return {
                    ...it,
                    Name: it.name,
                    Value: it.id
                }
            })
        } else {
            classify.value = []
        }
    })
    .catch((err: any) => {
        classify.value = []
    })
    .finally(() => {
    })

}
//上传回调
const onUploaded = (response: any, file: string, fileList: any) => {
    form.value.iconPath = response?.Response
}
const onCateUploaded = (response: any, file: string, fileList: any) => {
    cateForm.value.iconPath = response?.Response
}
const onUploadedFilePath = (response: any, file: string, fileList: any) => {
    form.value.filePath = response?.Response
}
// 拖拽上传之前事件,只能json文件且不超过10M
interface UploadRawFile extends File {
  uid: number
  isDirectory?: boolean
}
const beforeTemplateUpload = (rawFile: UploadRawFile) => {
    console.log('模板上传', rawFile);

    // if (rawFile.type !== 'application/json' || rawFile.size > 10 * 1024 * 1024) {
    //     return false
    // }
    if (rawFile.size > 10 * 1024 * 1024) {
        return false
    }
}
const beforeUpload = (rawFile: UploadRawFile) => {
    if (rawFile.type !== 'application/json' || rawFile.size > 10 * 1024 * 1024) {
        return false
    }
}
// 验证版本号格式 xx.xx.xx 的正则表达式
const validateVersion = (rule: any, value: any, callback: any) => {
    const regex = /^\d{1,2}\.\d{1,2}\.\d{1,2}$/;
    if (!regex.test(value)) {
        callback(new Error('版本号格式不正确'))
    } else {
        callback()
    }
};

const cateRules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入分类名称', trigger: 'change' }
    ]
})

const save = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return

    let update_params = {
        ...form.value
    }
    if (isNew.value) {
        tableInsert(update_params)
    } else {
        tableUpdate(update_params)
    }
}
const saveCate = async () => {
    let isValidate = await cateFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    if (isCateNew.value) {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            ...cateForm.value,
            typeList
            // type:params.value.type
        }
        delete update_params.applicableType;
        cateInsert(update_params)
    } else {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            id: currentCateRow.value.id,
            name: cateForm.value.name,
            description: cateForm.value.description,
            status: cateForm.value.status,
            typeList
            // type:params.value.type
        }
        cateUpdate(update_params)
    }
}


const handleRowClick = (row: any, show: boolean) => {
    debugger;
    currentRow.value = row
}
const handleCateRowClick = (row: any, show: boolean) => {
    currentCateRow.value = row
}


const tableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    currentRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"updated",
                Type:-1
            }
        ],
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    if (params.value.type !== 'all') {
        query_params.data.commandType = params.value.type
    }
    if (params.value.isFree !== 'all') {
        query_params.data.isFree = params.value.isFree
    }
    if (params.value.name) {
        query_params.data.name = params.value.name
    }
    saasApi.AIAgentWimtaskCommandQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            pagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            tableData.value = res.rows
        } else {
            tableData.value = []
        }
    })
    .catch((err: any) => {
        tableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}
const tableInsert = (insert_params: any) => {
    saasApi.AIAgentWimtaskCommandInsert(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const tableUpdate = (update_params: any, noQuery?: boolean) => {
    let input_params = {
        ...currentRow.value,
        ...update_params
    }
    saasApi.AIAgentWimtaskCommandUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                if (!noQuery) tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}

const cateInsert = (insert_params: any) => {
    saasApi.AIAgentResourceCategoryAdd(insert_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const cateUpdate = (update_params: any) => {
    let input_params = {
        ...update_params
    }
    saasApi.AIAgentResourceCategoryUpdate(input_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const changeType = (val:any) => {
    if (!val) {
        params.value.type = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

const changeFree = (val:any) => {
    if (!val&&val!==0) {
        params.value.isFree = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

changeType(null)

const onSubmit = () => {
    tableQuery()
}
const selectClassifyChange = (val: any) => {
    if (!val) {
        params.value.classify = 'all'
    }
    tableQuery()
}
const selectStatusChange = (val: any) => {
    if (!val && val !== 0) {
        params.value.status = 'all'
    }
    tableQuery()
}

const formatTime = (data: any, format: string = 'YYYY-MM-DD HH:mm') => {
    if (!data) return ''
    return moment(data).format(format)
}

const formatCategoryIds = (data: any, text: any) => {
    (data || []).forEach((item, index) => {
        const name = classify.value.find(it => it.Value === item)?.Name || ''
        if (name) {
            if (index === 0) {
                text += `${name}`
            } else {
                text += `,${name}`
            }
        }
    })
    return text
}

//发布
const onPublish = (row: any) => {

}

//撤回
const onRevoke = (row: any) => {

}

const onReset = () => {
    params.value.type = 'all'
    params.value.isFree = 'all'
    params.value.name = ''

    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isNew.value = true
    form.value = {
        name: "",
        commandType: "",
        iconPath:"",
        isFree:1
    }
    currentRow.value = null
    tableData.value = []
    tableQuery()
}
const formSet = async (model: any) => {
    // const form_params: any = {
    //     name: "",
    //     describe: "",
    //     price: "",
    //     classify: '',
    //     icon: [],
    //     templates: []
    // }
    let data = JSON.parse(JSON.stringify(model))
    // // 设置默认值
    // Object.keys(form_params).forEach((key) => {
    //     if (!data[key]) {
    //         data[key] = form_params[key]
    //     }
    // })

    return data
}
const onAdd = async () => {
    isNew.value = true
    form.value = JSON.parse(JSON.stringify(await formSet({
        name: "",
        commandType: "",
        iconPath:"",
        isFree:1
    })))
    // tableQuery()
    dialogFormVisible.value = true
}
const onEdit = async (row: any) => {
    if (row) currentRow.value = row
    isNew.value = false
    form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
    // tableQuery()
    dialogFormVisible.value = true
}
const changeStatus = async(row: any) => {
    if(row.status!==0){
        await ElMessageBox.confirm(`指令下架后将无法在任务编排时使用，是否下架？`, `提示`, {
            type: 'warning',
            customClass: 'default-confirm-class',
        })
    }

    currentRow.value = row
    row.status = row.status === 0 ? 1 : 0
    const update_params = {
        status: row.status
    }
    tableUpdate(update_params)
}
const onDelete = (row: any) => {
    if (row) currentRow.value = row
    const text = '确认删除指令吗？'
    ElMessageBox.alert(text, '提示', {
        // autofocus: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: (action) => {
            if (action === 'confirm') {
                saasApi.AIAgentWimtaskCommandDelete([currentRow.value.id]).then((res: any) => {
                    if (res.Code === 0 || res === true) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        tableQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            }
        },
    })
}


onMounted(() => {
    getClassifyData();
    // tableQuery()
})
</script>
<style scoped lang="scss">
:deep(.service-table) {
    .el-table__cell {
        border-bottom: 1px solid #EEEEEE !important;
    }
}
.small-height {
    height: 64px;
    width:64px;
}
.icon_lists .icon-item{
    display:flex;
    flex-direction: column;
    align-items: center;
}
.icon_lists .icon {
  display: block;
  height: 100px;
  line-height: 100px;
  font-size: 42px;
  margin: 10px auto;
  color: #333;
  -webkit-transition: font-size 0.25s linear, width 0.25s linear;
  -moz-transition: font-size 0.25s linear, width 0.25s linear;
  transition: font-size 0.25s linear, width 0.25s linear;
}

.icon_lists .icon:hover {
//   font-size: 100px;
}

.icon_lists .svg-icon {
  /* 通过设置 font-size 来改变图标大小 */
  width: 1em;
  /* 图标和文字相邻时，垂直对齐 */
  vertical-align: -0.15em;
  /* 通过设置 color 来改变 SVG 的颜色/fill */
  fill: currentColor;
  /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
      normalize.css 中也包含这行 */
  overflow: hidden;
}

.icon_lists li .name,
.icon_lists li .code-name {
  color: #666;
}
:deep(.icon-preview-item) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
    width: 64px;
    }

    .icon-preview-item-tool {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
    .el-icon {
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
    }
    }
    &:hover {
    .icon-preview-item-tool {
        display: flex;
    }
    }
}
.instruction {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px;
    box-sizing: border-box;
    background: #f7f7f9 !important;
    // display: flex;
    flex-direction: column;

    .header {
        background: #fff;
        width: 100%;
        height: 112px;
        box-sizing: border-box;
        //overflow: hidden;



        .condition-section {
            padding: 8px 16px;
            box-sizing: border-box;
            // border-top: solid 1px #e8ecf0;
            display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 16px;
            }

            .tab-list {
                .tab-list-item {
                    width: 80px;
                    height: 32px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    border: 1px solid #E6E7E9;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &.left {
                        border-radius: 4px 0 0 4px;
                    }

                    &.right {
                        border-radius: 0 4px 4px 0;
                    }

                    &.active {
                        color: #FFFFFF;
                        background: #0054D9;
                        border-color: #0054D9;
                    }
                }
            }
        }
    }

    .table-section {
        // flex: 1;
        height:calc(100% - 112px);
        background: #fff;
    }

    .table-content {
        height: 100%;

        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }

        .table-icon {
            width: 24px;
            height: 24px;
        }

        .table-content-pagination {
            height: 48px;
            padding: 0 12px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        ::v-deep(.el-scrollbar__view) {
            height: 100%;
        }
    }


}
.title-section {
    height: 64px;
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSansSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #222222;

    .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
    }
}

    .header-tools {
    display: flex;
    align-items: center;

    .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
            margin-right: 4px;
            font-size: 14px;
        }

        span {
            margin-left: 6px;
            line-height: 17px;
        }

        &:hover {
            color: rgba(0, 84, 210, 0.8);
        }

        &:active {
            color: #0044A9;
        }

        &.is-disabled {
            color: #BCBFC3;
            cursor: not-allowed;
        }
    }
    }
    .cate-table{
        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }
        .scene-tag{
            height: 22px;
            background: #F0F5FF;
            border: 1px solid #BED2FF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #1E39C3;
            line-height: 22px;
            margin-right:8px;
        }
        .mcp-tag{
            height: 22px;
            background: #FFFDDF;
            border: 1px solid #E9DE9A;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #D7A710;
            line-height: 22px;
            margin-right:8px;
        }
        .template-tag{
            height: 22px;
            background: #F0FFFB;
            border: 1px solid #C0E7DF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #2B9196;
            line-height: 22px;
            margin-right:8px;
        }
    }

</style>
