import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const switchVariants = cva(
  "cursor-pointer  relative inline-flex items-center rounded-full transition-colors disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default: "data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
        destructive: "data-[state=checked]:bg-destructive data-[state=unchecked]:bg-input/70",
        secondary: "data-[state=checked]:bg-secondary data-[state=unchecked]:bg-input/50",
      },
      size: {
        default: "h-6 w-11",
        sm: "h-5 w-9",
        lg: "h-7 w-13",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// 在 switchThumbVariants 中添加绝对定位和过渡
const switchThumbVariants = cva(
  "absolute block rounded-full bg-background shadow-sm transition-transform duration-200 ease-in-out data-[state=checked]:translate-x-full",
  {
    variants: {
      size: {
        default: "h-5 w-5 top-0.5", // 添加定位偏移
        sm: "h-4 w-4 top-0.5",
        lg: "h-6 w-6 top-0.5",
      },
      currentChecked:{
        true: 'right-0.5',
        false: 'left-0.5'
      }
    },
    defaultVariants: {
      size: "default"
    },
  }
);

interface SwitchProps
  extends React.ComponentPropsWithoutRef<"button">,
    VariantProps<typeof switchVariants> {
  asChild?: boolean;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}

const Switch = React.forwardRef<HTMLButtonElement, SwitchProps>(
  ({ className, variant, size, asChild = false, checked = false, onCheckedChange, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    const [internalChecked, setInternalChecked] = React.useState(checked);
    const isControlled = checked !== undefined;
    const currentChecked = isControlled ? checked : internalChecked;

    const handleClick = (e: React.MouseEvent) => {
      props.onClick?.(e);
      const newChecked = !currentChecked;
      if (!isControlled) {
        setInternalChecked(newChecked);
      }
      onCheckedChange?.(newChecked);
    };

    return (
      <Comp
        ref={ref}
        role="switch"
        type="button"
        aria-checked={currentChecked}
        data-state={currentChecked ? "checked" : "unchecked"}
        className={cn(switchVariants({ variant, size, className }))}
        onClick={handleClick}
        {...props}
      >
        <span
          className={cn(switchThumbVariants({ size, currentChecked }), {
            "translate-x-0": !currentChecked,
          })}
          aria-hidden="true"
        />
      </Comp>
    );
  }
);

Switch.displayName = "Switch";

export { Switch, switchVariants, switchThumbVariants };