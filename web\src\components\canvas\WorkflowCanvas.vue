<template>
  <div class="workflow-canvas">
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      delete-key-code=""
      :fit-view-on-init="true"
      :default-viewport="viewport"
      :min-zoom="0.1"
      :max-zoom="4"
      :snap-to-grid="true"
      :snap-grid="[20, 20]"
      :connection-mode="ConnectionMode.Strict"
      :connect-on-click="false"
      :only-render-visible-elements="true"
      @nodes-change="onNodesChange"
      @edges-change="onEdgesChange"
      @connect="onConnect"
      @drop="onDrop"
      @dragover="onDragOver"
      @node-click="onNodeClick"
      @edge-click="onEdgeClick"
      @pane-click="onPaneClick"
      @viewport-change="onViewportChange"
      class="vue-flow-container"
    >
      <!-- 背景 -->
      <Background pattern-color="#f2f4f8" :gap="20" />

      <!-- 控制面板 -->
      <Controls position="bottom-right" />

      <!-- 小地图 -->
      <MiniMap
        :width="106"
        :height="106"
        position="bottom-right"
        ariaLabel="流程预览"
        pannable
        :zoomable="true"
        :zoom-step="0.2"
        :node-border-radius="14"
        :node-color="getNodeColor"
        :mask-color="'rgba(255, 255, 255, 0.4)'"
      />

      <!-- 自定义节点模板 -->
      <template #node-workflow="{ data, id }">
        <WorkflowNode
          :key="`${id}-${forceUpdateKey}`"
          :id="id"
          :data="data"
          :edges="edges"
          :selected="selectedNodeId === id"
          @delete="deleteNode"
          @duplicate="duplicateNode"
        />
      </template>

      <!-- 开始节点模板 -->
      <template #node-start="{ data, id }">
        <StartNode
          :key="`${id}-${forceUpdateKey}`"
          :data="data"
          :edges="edges"
          :selected="selectedNodeId === id"
        />
      </template>

      <!-- 结束节点模板 -->
      <template #node-end="{ data, id }">
        <EndNode :key="`${id}-${forceUpdateKey}`" :data="data" :edges="edges" :selected="selectedNodeId === id" />
      </template>

      <!-- 自定义边模板 -->
      <template #edge-workflow="{ id, sourceX, sourceY, targetX, targetY, data }">
        <WorkflowEdge
          :id="id"
          :source-x="sourceX"
          :source-y="sourceY"
          :target-x="targetX"
          :target-y="targetY"
          :data="data"
          :selected="selectedEdgeId === id"
          @delete="deleteEdge"
          @click="selectEdge"
        />
      </template>
    </VueFlow>

    <!-- 空状态提示 -->
    <div v-if="nodes.length === 0" class="empty-state">
      <div class="empty-content">
        <el-icon :size="64" color="#c0c4cc">
          <Box />
        </el-icon>
        <h3>开始构建您的工作流</h3>
        <p>从左侧指令库拖拽指令到此处开始设计</p>
      </div>
    </div>

    <!-- 右键菜单 -->
    <ContextMenu
      v-if="contextMenu.visible"
      :x="contextMenu.x"
      :y="contextMenu.y"
      :items="contextMenu.items"
      @select="onContextMenuSelect"
      @close="closeContextMenu"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import {ConnectionMode, VueFlow, useVueFlow} from '@vue-flow/core'
const { fitView} = useVueFlow()
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type { Node, Edge, Connection, ViewportTransform } from '@vue-flow/core'
import { Box } from '@element-plus/icons-vue'

import WorkflowNode from './WorkflowNode.vue'
import WorkflowEdge from './WorkflowEdge.vue'
import ContextMenu from './ContextMenu.vue'
import StartNode from '../nodes/StartNode.vue'
import EndNode from '../nodes/EndNode.vue'

import { useWorkflowStore } from '@/stores/workflow'
import type { WorkflowNode as WorkflowNodeType, ComponentDefinition } from '@/stores/workflow'

const workflowStore = useWorkflowStore()

// 强制更新标记
const forceUpdateKey = ref(0)

// 监听节点变化，强制更新组件
watch(
  () => workflowStore.nodes,
  () => {
    // 强制更新所有节点组件
    forceUpdateKey.value++
  },
  { deep: true },
)

// 响应式数据
const nodes = computed({
  get: () => {
    // 确保深度响应式更新 - 创建新的数组引用
    return [...workflowStore.nodes]
  },
  set: (value) => {
    // 这里可以添加节点变化的处理逻辑
  },
})

const edges = computed({
  get: () => {
    // 确保深度响应式更新 - 创建新的数组引用
    return [...workflowStore.edges]
  },
  set: (value) => {
    // 这里可以添加边变化的处理逻辑
  },
})

const viewport = computed(() => workflowStore.viewport)
const selectedNodeId = computed(() => workflowStore.selectedNodeId)
const selectedEdgeId = computed(() => workflowStore.selectedEdgeId)

// 右键菜单
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  items: [] as Array<{ label: string; action: string; icon?: string }>,
})

// 自动定位到指定节点的方法
const focusOnNode = (nodeId: string) => {
  const node = nodes.value.find(n => n.id === nodeId);
  if (node) {
    fitView({
      nodes: [nodeId], // 要定位的节点 ID
      padding: 0.2, // 节点周围的边距比例
      duration: 500, // 动画过渡时间（毫秒）
    });
  }
};

// 事件处理
const onNodesChange = (changes: any[]) => {
  // 处理节点位置变化等
  changes.forEach((change) => {
    if (change.type === 'position' && change.position) {
      workflowStore.updateNode(change.id, { position: change.position })
    }
  })
}

const onEdgesChange = (changes: any[]) => {
  // 处理边的变化
  changes.forEach((change) => {
    if (change.type === 'remove') {
      workflowStore.removeEdge(change.id)
    }
  })
}

const onConnect = (connection: Connection) => {
  workflowStore.onConnect(connection)
}

const onNodeClick = (event: any) => {
  if (event.node) {
    workflowStore.selectNode(event.node.id)
  }
}

const onEdgeClick = (event: any) => {
  if (event.edge) {
    workflowStore.selectEdge(event.edge.id)
  }
}

const onPaneClick = () => {
  workflowStore.clearSelection()
  closeContextMenu()
}

const onViewportChange = (viewport: ViewportTransform) => {
  workflowStore.updateViewport(viewport)
}

// 拖拽处理
const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()

  if (!event.dataTransfer) return

  try {
    const data = JSON.parse(event.dataTransfer.getData('application/json'))

    if (data.type === 'component') {
      const component = data.component as ComponentDefinition

      // 获取画布容器元素
      const flowContainer = document.querySelector('.vue-flow-container') as HTMLElement
      if (!flowContainer) return

      // 获取画布容器相对于视口的位置
      const containerRect = flowContainer.getBoundingClientRect()

      // 确保获取最新的 viewport 值，提供默认值
      const currentViewport = workflowStore.viewport

      // 计算相对于画布容器的位置（考虑滚动偏移和缩放）
      const position = {
        x: (event.clientX - containerRect.left - currentViewport.x) / currentViewport.zoom,
        y: (event.clientY - containerRect.top - currentViewport.y) / currentViewport.zoom
      }

      // 确定节点类型
      let nodeType = 'workflow'
      if (component.type === 'workflow_start') {
        nodeType = 'start'
      } else if (component.type === 'workflow_end') {
        nodeType = 'end'
      }

      // 创建新节点
      const newNode: Omit<WorkflowNodeType, 'id'> = {
        type: nodeType,
        position,
        data: {
          label: component.label,
          icon: component.icon,
          config: { ...component.config },
          category: component.category,
          description: component.description,
          componentType: component.type,
          inputs: component.inputs || [],
          outputs: component.outputs || [],
        },
      }

      const addedNode = workflowStore.addNode(newNode)

      // 选中新添加的节点
      nextTick(() => {
        workflowStore.selectNode(addedNode.id)
      })
    }
  } catch (error) {
    console.error('Failed to parse drop data:', error)
  }
}

// 节点操作
const deleteNode = (nodeId: string) => {
  workflowStore.removeNode(nodeId)
}

const duplicateNode = (nodeId: string) => {
  const originalNode = workflowStore.nodes.find((n) => n.id === nodeId)
  if (originalNode) {
    // 创建新节点时使用深拷贝
    const newNode = JSON.parse(JSON.stringify(originalNode)) as WorkflowNodeType

    // 设置新位置，考虑当前视图缩放
    const offset = 100 / (viewport.value.zoom || 1)
    newNode.position = {
      x: originalNode.position.x + offset,
      y: originalNode.position.y + offset
    }

    // 确保新节点有唯一ID
    newNode.id = `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // 添加到store
    workflowStore.addNode(newNode)

    // 选中新节点并强制更新视图
    nextTick(() => {
      workflowStore.selectNode(newNode.id)
      forceUpdateKey.value++
    })
  }
}

// 边操作
const selectEdge = (edgeId: string) => {
  workflowStore.selectEdge(edgeId)
}

const deleteEdge = (edgeId: string) => {
  workflowStore.removeEdge(edgeId)
}

// 右键菜单
const onContextMenuSelect = (action: string) => {
  // 处理右键菜单选择
  switch (action) {
    case 'paste':
      // TODO: 实现粘贴功能
      break
    case 'selectAll':
      // TODO: 实现全选功能
      break
    case 'clear':
      workflowStore.nodes.splice(0)
      workflowStore.edges.splice(0)
      break
  }
  closeContextMenu()
}

const closeContextMenu = () => {
  contextMenu.value.visible = false
}

// 工具函数
const getNodeColor = (node: Node) => {
  // 特殊节点类型颜色
  if (node.type === 'start') {
    return '#67c23a' // 绿色 - 开始
  }
  if (node.type === 'end') {
    const status = (node.data as any)?.config?.status || 'success'
    switch (status) {
      case 'success':
        return '#67c23a' // 绿色 - 成功
      case 'failure':
        return '#f56c6c' // 红色 - 失败
      case 'cancelled':
      case 'timeout':
        return '#e6a23c' // 橙色 - 警告
      default:
        return '#f56c6c'
    }
  }

  // 普通节点按分类着色
  const categoryColors: Record<string, string> = {
    browser: '#409eff',
    interaction: '#67c23a',
    control: '#e6a23c',
    data: '#f56c6c',
    desktop: '#9c27b0',
    file: '#ff9800',
    network: '#00bcd4',
    system: '#795548',
  }

  const category = (node.data as any)?.category || 'default'
  return categoryColors[category] || '#909399'
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  // 检查是否在输入元素中，如果是则不处理快捷键
  const target = event.target as HTMLElement
  const isInputElement =
    target.tagName === 'INPUT' ||
    target.tagName === 'TEXTAREA' ||
    target.contentEditable === 'true' ||
    target.closest('.el-input') ||
    target.closest('.el-textarea') ||
    target.closest('.el-select') ||
    target.closest('.property-panel')

  if (isInputElement) {
    return // 在输入元素中时不处理快捷键
  }

  // Delete键删除选中的节点或边
  if (event.key === 'Delete' || event.key === 'Backspace') {
    event.preventDefault()
    event.stopPropagation()
    if (selectedNodeId.value) {
      const node = workflowStore.nodes.find((n) => n.id === selectedNodeId.value)
      if (node?.type === 'start' || node?.type === 'end') {
        console.warn('开始结束节点不能删除')
        return
      }
      // 删除选中的节点
      const success = workflowStore.removeNode(selectedNodeId.value)
      if (!success) {
        // 如果是系统节点，显示提示
        console.warn('系统节点不能删除')
      }
    }
    else if (selectedEdgeId.value) {
      // 删除选中的边
      workflowStore.removeEdge(selectedEdgeId.value)
    }
  }

  // Escape键取消选择
  if (event.key === 'Escape') {
    // workflowStore.clearSelection()
    closeContextMenu()
  }
}

// 暴露方法，使外部可以调用
defineExpose({
  focusOnNode
});

// 组件挂载和卸载时添加/移除键盘监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.workflow-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.vue-flow-container {
  width: 100%;
  height: 100%;
}

/* 修复鼠标样式 */
:deep(.vue-flow__pane) {
  cursor: default !important;
}

:deep(.vue-flow__pane.dragging) {
  cursor: grabbing !important;
}

:deep(.vue-flow__pane.selection) {
  cursor: crosshair !important;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 1;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: #909399;
  font-size: 18px;
  font-weight: 500;
}

.empty-content p {
  margin: 0;
  color: #c0c4cc;
  font-size: 14px;
}

/* Vue Flow 样式定制 */
:deep(.vue-flow__background) {
  //background-color: #fafafa;
}

:deep(.vue-flow__controls) {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
}

:deep(.vue-flow__controls-button) {
  background: white;
  border: none;
  border-bottom: 1px solid #e4e7ed;
  color: #606266;
  transition: all 0.2s ease;
}

:deep(.vue-flow__controls-button:hover) {
  background: #f5f7fa;
  color: var(--el-color-primary);
}

:deep(.vue-flow__controls-button:last-child) {
  border-bottom: none;
}

:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  bottom: 38px;
}

:deep(.vue-flow__edge-path) {
  stroke-width: 2;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: var(--el-color-primary);
  stroke-width: 3;
}

:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 3px solid white;
  background: var(--el-color-primary);
  transition: all 0.2s ease;
  cursor: crosshair;
  z-index: 4;
  &:not(.connectablestart){
    cursor: default !important;
  }
}

:deep(.vue-flow__handle:hover) {
  width: 16px;
  height: 16px;
  border: 4px solid white;
  box-shadow:
    0 0 0 2px var(--el-color-primary),
    0 2px 8px rgba(64, 158, 255, 0.3);
  //transform: scale(1.1);
}

:deep(.vue-flow__handle.source) {
  background: var(--el-color-success);
}

:deep(.vue-flow__handle.source:hover) {
  box-shadow:
    0 0 0 2px var(--el-color-success),
    0 2px 8px rgba(103, 194, 58, 0.3);
}

:deep(.vue-flow__handle.target) {
  background: var(--el-color-danger);
}

:deep(.vue-flow__handle.target:hover) {
  box-shadow:
    0 0 0 2px var(--el-color-danger),
    0 2px 8px rgba(245, 108, 108, 0.3);
}
</style>
