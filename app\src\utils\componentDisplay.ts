/**
 * 组件显示工具函数
 * 用于生成组件在面板中的描述信息
 */

import type { WorkflowNode } from '@/stores/workflow'

/**
 * 生成组件的显示描述
 */
export function generateComponentDescription(node: WorkflowNode): string {
  const componentType = node.data.componentType
  const config = node.data.config || {}
  const description = node.data.description || ''

  switch (componentType) {
    case 'user_input':
      return generateUserInputDescription(config)

    case 'user_choice':
      return generateUserChoiceDescription(config)

    case 'user_confirm':
      return generateUserConfirmDescription(config)

    case 'http_post':
    case 'monitor_data':
      return generateHttpPostDescription(config)

    case 'http_request':
      return generateHttpRequestDescription(config)

    case 'http_get':
      return generateHttpGetDescription(config)

    case 'set_variable':
      return generateSetVariableDescription(config)

    case 'new_browser':
      return generateOpenBrowserDescription(config)

    case 'click_element':
      return generateClickElementDescription(config)

    case 'input_text':
    case 'text_template':
      return generateInputTextDescription(config)

    case 'wait':
      return generateWaitDescription(config)

    case 'log_message':
      return generateLogMessageDescription(config)

    case 'ai_analyze':
      return generateAiAnalyzeDescription(config)

    case 'text_to_speech':
      return generatTextToSpeechDescription(config)

    case 'excel_create':
      return generateExcelCreateDescription(config)

    case 'weather_query':
      return generateWeatherQueryDescription(config)

    case 'db_connect':
      return generateDbConnectDescription(config)

    case 'db_query':
      return generateDbQueryDescription(config)

    case 'add_time':
      return generateAddTimeDescription(config)

    case 'notifier_send':
      return generatNotifierSendDescription(config)

    case 'javascript_execute':
      return generateJavascriptExecuteDescription(config)

    case 'python_execute':
      return generatePythonExecuteDescription(config)

    case 'variable_assignment':
      return generateVariableAssignmentDescription(config)

    case 'img_recognition':
    case 'invoice_recognition':
      return generateImgRecognitionDescription(config)

    case 'navigate_to':
      return generateNavigateToDescription(config)

    case 'condition':
      return ''

    default:
      return description || '点击配置组件参数'
  }
}

/**
 * 增加减少时间描述
 */
 function generateAddTimeDescription(config: any): string {
  const date = config.date || ''
  const method = config.method;
  const duration = config.duration;
  const duration_unit = config.duration_unit;
  let description = ''


  if (date) {
    description += `${date}`
  }

  if (method) {
    description += `${method=='add'?'增加':'减少'}`
  }

  if (duration) {
    description += `${duration}${getUnitLabel(duration_unit)}`
  }

  return description
}


/**
 * 获取天气描述
 */
 function generateWeatherQueryDescription(config: any): string {
  const city = config.city || ''
  const days = config.days
  let description = '获取'
  const map = {
    0: '今天',
    1: '明天',
    3: '未来3日',
    7: '未来7日',
    15: '未来15日'
  }

  if (city) {
    description += `${city}`
  }

  if (days || days == 0) {
    description += `${map[days]}天气`
  }

  return description
}

/**
 * 获取连接数据库描述
 */
 function generateDbConnectDescription(config: any): string {
  const driver = config.driver || ''
  const host = config.host
  let description = ''

  if (driver) {
    description += `${driver}`
  }

  if (host) {
    description += `:${host}`
  }

  return description
}

/**
 * 获取python描述
 */
 function generatePythonExecuteDescription(config: any): string {
  const code = config.code || ''
  let description = 'python代码：'

  if (code) {
    description += `${truncateText(code,20, config.type)}`
  }

  return description
}

/**
 * 获取javascript描述
 */
 function generateJavascriptExecuteDescription(config: any): string {
  const code = config.code || ''
  let description = 'javascript代码：'

  if (code) {
    description += `${truncateText(code,20, config.type)}`
  }

  return description
}

/**
 * 获取SQL查询描述
 */
 function generateDbQueryDescription(config: any): string {
  const query = config.query || ''
  let description = 'SQL查询语句：'

  if (query) {
    description += `${truncateText(query,20, config.type)}`
  }

  return description
}

/**
 * 获取变量赋值描述
 */
 function generateVariableAssignmentDescription(config: any): string {
  let variables = config.variables || []
  let description = '变量：'

  if (variables) {
    if (typeof variables === 'string') {
      try {
        variables = JSON.parse(variables)
      } catch (error) {
        variables = []
      }
    }
    const text = variables.map((variable: any) => {
      if(variable.field&&variable.value){
        return variable.field+"="+variable.value
      }
      return ''
    })
    description += `${truncateText(text.join(" "),20, config.type)}`
  }

  return description
}

/**
 * 获取图片识别描述
 */
 function generateImgRecognitionDescription(config: any): string {
  return '识别并理解图片中的内容'
}

/**
 * 获取导航到描述
 */
function generateNavigateToDescription(config: any): string {
  const url = config.url || ''
  if(url){
    return `导航到 ${truncateText(url,30, config.type)}`
  }
  return '导航到指定页面'
}


/**
 * 用户输入组件描述
 */
function generateUserInputDescription(config: any): string {
  const inputType = config.input_type || 'text'
  const variableName = config.variable_name
  const prompt = config.prompt_message

  let description = ''

  if (prompt) {
    description += `输入"${truncateText(prompt, 25, config.type)}"`
  } else {
    description += `用户输入(${getInputTypeLabel(inputType)})`
  }

  if (variableName) {
    description += `\n→ ${variableName}`
  }

  return description
}

/**
 * 用户选择组件描述
 */
function generateUserChoiceDescription(config: any): string {
  const variableName = config.variable_name
  const prompt = config.prompt_message
  const choices = config.choices

  let description = ''

  if (prompt) {
    description += `选择"${truncateText(prompt, 20, config.type)}"`
  } else {
    description += '用户选择'
  }

  if (choices) {
    const choiceList = choices.split(',').map((c: string) => c.trim())
    if (choiceList.length > 0) {
      description += `(${choiceList.slice(0, 2).join('/')}`
      if (choiceList.length > 2) {
        description += `等${choiceList.length}项`
      }
      description += ')'
    }
  }

  if (variableName) {
    description += ` → ${variableName}`
  }

  return description
}

/**
 * 用户确认组件描述
 */
function generateUserConfirmDescription(config: any): string {
  const variableName = config.variable_name
  const prompt = config.prompt_message

  let description = ''

  if (prompt) {
    description += `确认"${truncateText(prompt, 15, config.type)}"`
  } else {
    description += '用户确认'
  }

  if (variableName) {
    description += ` → ${variableName}`
  }

  return description
}

/**
 * HTTP REQUEST请求组件描述
 */
function generateHttpRequestDescription(config: any): string {
  const url = config.url
  const method = (config.url_method || 'POST').toUpperCase()
  const responseVar = config.response_variable || config.response_content_variable

  let description = ''

  if (url) {
    const shortUrl = truncateUrl(url,config.type)
    description += `${method} ${shortUrl}`
  } else {
    description += `${method} 请求`
  }

  if (responseVar) {
    description += `\n→ ${responseVar}`
  }

  return description
}

/**
 * HTTP POST请求组件描述
 */
function generateHttpPostDescription(config: any): string {
  const url = config.url
  const responseVar = config.response_variable || config.response_content_variable

  let description = ''

  if (url) {
    const shortUrl = truncateUrl(url,config.type)
    description += `POST ${shortUrl}`
  } else {
    description += 'POST请求'
  }

  if (responseVar) {
    description += `\n→ ${responseVar}`
  }

  return description
}

/**
 * HTTP GET请求组件描述
 */
function generateHttpGetDescription(config: any): string {
  const url = config.url
  const responseVar = config.response_variable || config.response_content_variable

  let description = ''

  if (url) {
    description += `GET ${truncateUrl(url,config.type)}`
  } else {
    description += 'GET请求'
  }

  if (responseVar) {
    description += ` → ${responseVar}`
  }

  return description
}

/**
 * 设置变量组件描述
 */
function generateSetVariableDescription(config: any): string {
  const variableName = config.variable_name
  const value = config.value
  const valueType = config.value_type || 'string'

  let description = ''

  if (variableName) {
    description += `${variableName} = `
    if (value) {
      description += `${truncateText(String(value), 20, config.type)}`
    } else {
      description += `(${getValueTypeLabel(valueType)})`
    }
  } else {
    description += '设置变量'
  }

  return description
}

/**
 * 打开浏览器组件描述
 */
function generateOpenBrowserDescription(config: any): string {
  const url = config.url
  const browser = config.browser || 'chrome'

  let description = ''

  if (url) {
    description += `打开 ${truncateUrl(url,config.type)}`
  } else {
    description += `打开浏览器(${browser})`
  }

  return description
}

/**
 * 点击元素组件描述
 */
function generateClickElementDescription(config: any): string {
  const locator = config.locator
  const locatorType = config.locator_type || 'xpath'

  let description = ''

  if (locator) {
    description += `点击 ${truncateText(locator, 20, config.type)}`
  } else {
    description += `点击元素(${locatorType})`
  }

  return description
}

/**
 * 输入文本组件描述
 */
function generateInputTextDescription(config: any): string {
  const locator = config.locator
  const text = config.text

  let description = ''

  if (text) {
    description += `输入"${truncateText(text, 15, config.type)}"`
  } else {
    description += '输入文本'
  }

  if (locator) {
    description += ` → ${truncateText(locator, 15, config.type)}`
  }

  return description
}

/**
 * 等待组件描述
 */
function generateWaitDescription(config: any): string {
  const duration = config.duration
  const unit = config.unit || 'seconds'

  if (duration) {
    let text = `等待 ${duration}${getUnitLabel(unit)}`
    if (config.reason) {
      text += `，原因：${config.reason}`
    }
    return text
  }
  else if (config.reason) {
    return `原因：${config.reason}`
  }
  else {
    return '等待'
  }
}

/**
 * 日志消息组件描述
 */
function generateLogMessageDescription(config: any): string {
  const message = config.message
  const level = config.level || 'INFO'

  if (message) {
    return `记录: ${truncateText(message, 20, config.type)}`
  } else {
    return `记录日志(${level})`
  }
}

/**
 * AI分析
 */
function generateAiAnalyzeDescription(config: any): string {
  const question = config.question || ''
  const prompt = config.prompt || ''
  const responseVar = config.ai_analyze_response
  let text = ''
  if (question) {
    const shortQuestion = truncateText(question, 20, config.type)
    text += `问题: ${shortQuestion}`
  }
  if (prompt) {
    if (text) text += `\n`
    text += `提示词: ${prompt}`
  }
  if (!text) {
    text = `点击配置组件参数`
  }

  if (responseVar) {
    text += `\n→ ${responseVar}`
  }

  return text
}

/**
 * 语音播报
 */
 function generatTextToSpeechDescription(config: any): string {
  const input = config.input || ''
  const responseVar = config.ai_analyze_response
  let text = ''
  if (input) {
    text += `播报内容: ${input}`
  }
  if (!text) {
    text = `点击配置组件参数`
  }

  if (responseVar) {
    text += `\n→ ${responseVar}`
  }

  return text
}


/**
 * 语音播报
 */
 function generatNotifierSendDescription(config: any): string {
  const content = config.content || ''
  let text = ''
  if (content) {
    text += `通知内容: ${content}`
  }
  if (!text) {
    text = `点击配置组件参数`
  }

  return text
}

/**
 * excel创建描述
 */
function generateExcelCreateDescription(config: any): string {
  const file_name = config.file_name || ''
  const file_path = config.file_path || ''
  let text = ''
  if (file_name) {
    text += `文件名: ${file_name}`
  }
  if (file_path) {
    if (text) text += `\n`
    text += `保存路径: ${file_path}`
  }
  if (!text) {
    text = `点击配置组件参数`
  }

  return text
}

/**
 * 辅助函数
 */

function truncateText(text: string, maxLength: number, type: string): string {
  if (!text) return ''
  if (type === 'taskMonitor') maxLength = 30
  if (text.length <= maxLength) return text

  // 在合适的位置截断，避免截断中文字符
  let truncated = text.substring(0, maxLength)

  // 如果截断点是中文字符的一部分，向前调整
  if (text.length > maxLength && /[\u4e00-\u9fff]/.test(text[maxLength])) {
    while (truncated.length > 0 && /[\u4e00-\u9fff]/.test(truncated[truncated.length - 1])) {
      if (text[truncated.length] && /[\u4e00-\u9fff]/.test(text[truncated.length])) {
        break
      }
      truncated = truncated.substring(0, truncated.length - 1)
    }
  }

  return truncated + '...'
}

function truncateUrl(url: string, type: string): string {
  if (!url) return ''

  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const path = urlObj.pathname

    // 移除常见的协议前缀
    const displayDomain = domain.replace(/^www\./, '')

    if (path === '/' || path === '') {
      return displayDomain
    } else {
      // 对于长路径，只显示关键部分
      const pathParts = path.split('/').filter((p) => p)
      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1]
        if (lastPart.length > 10) {
          return `${displayDomain}/...${lastPart.substring(0, 10)}...`
        } else {
          return `${displayDomain}/${lastPart}`
        }
      }
      return displayDomain
    }
  } catch {
    return truncateText(url, 30, type)
  }
}

function getInputTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    text: '文本',
    multiline: '多行',
    password: '密码',
    number: '数字',
    email: '邮箱',
    url: '网址',
  }
  return labels[type] || type
}

function getValueTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    string: '文本',
    number: '数字',
    boolean: '布尔',
    json: 'JSON',
    array: '数组',
  }
  return labels[type] || type
}

function getUnitLabel(unit: string): string {
  const labels: Record<string, string> = {
    days: '天',
    seconds: '秒',
    minutes: '分钟',
    hours: '小时',
    milliseconds: '毫秒',
  }
  return labels[unit] || unit
}
