<template>
  <div
    class="workflow-node"
    :class="{
      selected: selected,
      'has-error': hasError,
      'is-running': isRunning,
    }"
    :style="{ '--el-color-primary': getCategoryColor(data.category) }"
    @contextmenu.prevent="onContextMenu"
  >
    <!-- 输入连接点 -->
    <div>
      <Handle type="target" :position="Position.Left" :connectable-start="false" class="custom-handle input-handle" :class="{ connected: isInputConnected }"/>
    </div>

    <!-- 节点头部 -->
    <div class="node-header">
      <div class="node-icon">
        <i v-if="data.icon" :class="data.icon"></i>
        <el-icon v-else :size="16">
          <Setting />
        </el-icon>
      </div>
      <div class="node-title">{{ data.label }}</div>
      <div class="node-actions" v-if="!data.isSystemNode">
        <el-dropdown trigger="hover" @command="onActionCommand">
          <el-icon class="action-icon" :size="14">
            <MoreFilled />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="duplicate">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon>
                  <Delete />
                </el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <!-- 系统节点标识 -->
      <div class="system-node-badge" v-if="data.isSystemNode">
        <el-icon :size="12">
          <Lock />
        </el-icon>
      </div>
    </div>
    <!-- 条件判断节点 -->
    <div v-if="data.componentType === 'condition'">
      <div v-for="(condition, index) in data.config.conditions" class="condition_target">
        <div class="target_title">
          <span class="case">CASE {{ index + 1 }}</span>
          <span>{{ index === 0 ? 'IF' : 'ELIF' }}</span>
        </div>
        <div class="target_content" v-if="condition.conditions.length > 0">
          <template v-if="condition?.conditions?.length > 1">
            <div class="condition-actions" :class="{'and-action': condition.relation.toUpperCase() === 'AND'}">
              <div class="relation-switch">
                {{ condition.relation.toUpperCase() }}
              </div>
            </div>
            <div class="condition-relation-line"></div>
          </template>
          <div class="content_item" :key="cdx" v-for="(child, cdx) in condition.conditions">
            <template v-if="child.field">
              <span class="field">{{ child.field }}</span>
              <span class="operator">{{ formatOperator(child.operator) }}</span>
              <span class="value">{{ child.value }}</span>
            </template>
            <template>
              <span>条件未设置</span>
            </template>
          </div>
        </div>
        <Handle
          class="custom-handle output-handle condition-handle"
          type="source"
          :id="condition.condition_id"
          :position="Position.Right"
          :class="{ connected: isConditionConnected(condition.condition_id) }"
        >
          <el-icon size="12" class="handle-el-icon"><Plus /></el-icon>
        </Handle>
      </div>
      <div class="condition_target">
        <div class="target_title">
          <span></span>
          <span>ELSE</span>
        </div>
        <Handle
          id="node_end"
          class="custom-handle output-handle condition-handle"
          type="source"
          :position="Position.Right"
          :class="{ connected: isConditionConnected('node_end') }"
        >
          <el-icon size="12" class="handle-el-icon"><Plus /></el-icon>
        </Handle>
      </div>
    </div>
    <!-- 普通节点 -->
    <div v-else>
      <!-- 节点内容 -->
      <div class="node-content">
        <div class="node-description">{{ computedDescription }}</div>

        <!-- 配置预览 -->
        <div v-if="hasImportantConfig" class="config-preview">
          <div v-for="(value, key) in importantConfig" :key="key" class="config-item">
            <span class="config-key">{{ formatConfigKey(key) }}:</span>
            <span class="config-value">{{ formatConfigValue(value) }}</span>
          </div>
        </div>

        <!-- 状态指示器 -->
        <div v-if="status && status !== 'idle'" class="node-status">
          <el-icon :size="12" :color="getStatusColor()">
            <component :is="getStatusIcon()" />
          </el-icon>
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
      </div>

      <!-- 输出连接点 -->
      <Handle type="source" :position="Position.Right" class="custom-handle output-handle" :class="{ connected: isOutputConnected }">
        <el-icon size="12" class="handle-el-icon"><Plus /></el-icon>
      </Handle>
    </div>

    <!-- 运行状态指示器 -->
    <div v-if="isRunning" class="running-indicator">
      <div class="pulse"></div>
    </div>

    <!-- 可变大小 -->
<!--    <NodeResizer :min-width="264" :min-height="140" />-->
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { Handle, Position} from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import { MoreFilled, CopyDocument, Delete, Setting, Lock } from '@element-plus/icons-vue'
import { generateComponentDescription } from '@/utils/componentDisplay'
import { getCategoryColor } from '@/utils/componentCategories'

interface Props {
  id: string
  data: {
    label: string
    config: Record<string, any>
    category: string
    description?: string
    componentType?: string
    icon?: string
    inputs?: string[]
    outputs?: string[]
    isSystemNode?: boolean
  }
  selected?: boolean
  status?: 'idle' | 'running' | 'success' | 'error',
  edges?: any[] // 添加edges属性
}

interface Emits {
  (e: 'delete', nodeId: string): void

  (e: 'duplicate', nodeId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  status: 'idle',
})

const emit = defineEmits<Emits>()

// 调试：监听 props 变化
watch(
  () => props.data,
  (newData, oldData) => {
    console.log(`Node ${props.id} data changed:`, {
      old: oldData,
      new: newData,
      configChanged: JSON.stringify(oldData?.config) !== JSON.stringify(newData?.config),
    })
  },
  { deep: true },
)

// 计算属性
const inputs = computed(() => props.data.inputs || [])
const outputs = computed(() => props.data.outputs || [])

// 计算输入输出是否已连接
const isInputConnected = computed(() => {
  return props.edges?.some(edge => edge.target === props.id)
})

const isOutputConnected = computed(() => {
  return props.edges?.some(edge => edge.source === props.id)
})

// 计算条件判断节点的连接状态
const isConditionConnected = (handleId: string) => {
  return props.edges?.some(edge =>
    edge.source === props.id && edge.sourceHandle === handleId
  )
}

const computedDescription = computed(() => {
  // 创建一个完整的节点对象用于描述生成
  // 通过访问 props.data 的所有属性来确保响应式更新
  // 使用 JSON.stringify 来确保深度响应式更新
  const configString = JSON.stringify(props.data.config)
  const nodeForDescription = {
    id: props.id,
    data: {
      ...props.data,
      label: props.data.label,
      description: props.data.description,
      config: JSON.parse(configString), // 确保深度复制
      componentType: props.data.componentType,
    },
  }
  return generateComponentDescription(nodeForDescription)
})

const hasError = computed(() => props.status === 'error')
const isRunning = computed(() => props.status === 'running')

const hasImportantConfig = computed(() => {
  return Object.keys(importantConfig.value).length > 0
})

const importantConfig = computed(() => {
  // 确保深度响应式更新 - 访问所有可能的配置属性
  const config = props.data.config
  const componentType = props.data.componentType
  const important: Record<string, any> = {}

  // 根据组件类型显示重要配置
  switch (componentType) {
    case 'new_browser':
      if (config?.url) important.url = config.url
      if (config?.browser) important.browser = config.browser
      if (config?.headless !== undefined)
        important.headless = config.headless ? '无头模式' : '有界面'
      break
    case 'new_page':
    case 'go_to':
      if (config?.url) important.url = config.url
      break
    case 'click':
    case 'double_click':
    case 'hover':
    case 'fill_text':
    case 'type_text':
    case 'get_text':
    case 'click_element':
    case 'input_text':
      if (config?.selector) important.selector = config.selector
      if (config?.locator) important.locator = config.locator
      if (config?.text) important.text = config.text
      break
    case 'select_option':
      if (config?.selector) important.selector = config.selector
      if (config?.value) important.value = config.value
      break
    case 'press_key':
      if (config?.selector) important.selector = config.selector
      if (config?.key) important.key = config.key
      break
    case 'wait_for_element':
      if (config?.selector) important.selector = config.selector
      if (config?.timeout) important.timeout = `${config.timeout}s`
      break
    case 'upload_file':
      if (config?.selector) important.selector = config.selector
      if (config?.file_path) important.file = config.file_path
      break
    case 'get_attribute':
      if (config?.selector) important.selector = config.selector
      if (config?.attribute) important.attribute = config.attribute
      break
    case 'drag_and_drop':
      if (config?.source_selector) important.from = config.source_selector
      if (config?.target_selector) important.to = config.target_selector
      break
    case 'scroll_page':
      const directionDict = {
        'up': '向上',
        'down': '向下',
        'left': '向左',
        'right': '向右',
      }
      if (config?.direction) important.direction = directionDict[config.direction]
      if (config?.distance) important.distance = `${config.distance}px`
      break
    case 'wait_for_load':
      if (config?.state) important.state = config.state
      if (config?.timeout) important.timeout = `${config.timeout}ms`
      break
    case 'take_screenshot':
      const screenshot_typeDict = {
        'full_page': '整个页面',
        'viewport': '可视区域',
        'element': '元素',
      }
      if (config?.filename) important.filename = config.filename
      if (config?.screenshot_type !== undefined)
        important.screenshot_type = screenshot_typeDict[config.screenshot_type]
      break
    case 'send_keys':
      if (config?.keys) important.send_keys = config.keys
      break
    // case 'wait':
    //   if (config?.duration) important.duration = `${config.duration}${config.unit || 's'}`
    //   break
    case 'set_variable':
      if (config?.variable_name) important.name = config.variable_name
      if (config?.value) important.value = config.value
      break
    case 'log_message':
      if (config?.message) important.message = config.message
      break
    case 'http_post':
    case 'http_get':
    case 'http_request':
      if (config?.url) important.url = config.url
      if (config?.response_variable) important.response = config.response_variable
      break
    case 'user_input':
      if (config?.prompt_message) important.prompt = config.prompt_message
      if (config?.variable_name) important.variable = config.variable_name
      break
    case 'user_choice':
      if (config?.prompt_message) important.prompt = config.prompt_message
      if (config?.choices) important.choices = config.choices
      break
    case 'condition':
      if (config?.conditions) important.conditions = config.conditions
      break
  }

  return important
})

// 方法

const getInputPosition = (index: number) => {
  const headerHeight = 40
  const spacing = 20
  return headerHeight + (index + 1) * spacing
}

const getOutputPosition = (index: number) => {
  const headerHeight = 40
  const spacing = 20
  return headerHeight + (index + 1) * spacing
}

const getStatusColor = () => {
  const colorMap = {
    idle: '#909399',
    running: '#0054D2',
    success: '#67c23a',
    error: '#f56c6c',
  }
  return colorMap[props.status]
}

const getStatusIcon = () => {
  const iconMap = {
    idle: 'Timer',
    running: 'Loading',
    success: 'CircleCheckFilled',
    error: 'CircleCloseFilled',
  }
  return iconMap[props.status]
}

const getStatusText = () => {
  const textMap = {
    idle: '就绪',
    running: '执行中',
    success: '成功',
    error: '失败',
  }
  return textMap[props.status]
}

const formatOperator = (operator: string) => {
  const operatorMap: Record<string, string> = {
    contains: '包含',
    notContains: '不包含',
    equals: '等于',
    notEquals: '不等于',
    greaterThan: '大于',
    lessThan: '小于',
    greaterThanOrEqual: '大于等于',
    lessThanOrEqual: '小于等于',
    isEmpty: '为空',
    isNotEmpty: '不为空',
  }
  return operatorMap[operator] || operator
}

const formatConfigKey = (key: string) => {
  const keyMap: Record<string, string> = {
    url: 'URL',
    browser: '浏览器',
    selector: '选择器',
    duration: '时长',
    name: '变量名',
    value: '值',
    message: '消息',
    text: '文本',
    filename: '文件名',
    headless: '模式',
    screenshot_type: '截图类型',
    send_keys: '按键内容',
    key: '按键',
    timeout: '超时时间',
    file: '文件',
    attribute: '属性',
    from: '源元素',
    to: '目标元素',
    direction: '方向',
    distance: '距离',
    state: '状态',
  }
  return keyMap[key] || key
}

const formatConfigValue = (value: any) => {
  if (typeof value === 'string' && value.length > 30) {
    return value.substring(0, 30) + '...'
  }
  return String(value)
}

const onActionCommand = (command: string) => {
  switch (command) {
    case 'duplicate':
      emit('duplicate', props.id)
      break
    case 'delete':
      emit('delete', props.id)
      break
  }
}

const onContextMenu = (event: MouseEvent) => {
  // 右键菜单处理可以在这里实现
  event.preventDefault()
}
</script>

<style scoped lang="scss">
:deep(.vue-flow__resize-control){
  opacity: 0;
}
.workflow-node {
  min-width: 220px;
  max-width: 350px;
  //height: inherit;
  //width: inherit;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  //overflow: hidden;
}

.workflow-node:hover {
  border-color: #c6e2ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.workflow-node:hover .custom-handle {
  width: 16px !important;
  height: 16px !important;
  border: 2px solid white !important;
  //box-shadow:
  //  0 0 0 2px currentColor,
  //  0 2px 8px rgba(0, 0, 0, 0.2) !important;
  //transform: scale(1.1);
  transition: all 0.2s ease !important;
}

.workflow-node.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.workflow-node.has-error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.workflow-node.is-running {
  border-color: var(--el-color-primary);
  background: var(--el-button-bg-color);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  min-height: 40px;
}

.node-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary);
  color: white;
  border-radius: 6px;

  .action-iconfont {
    font-size: 14px;
  }
}

.node-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-actions {
  flex-shrink: 0;
}

.action-icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.2s ease;
}

.action-icon:hover {
  color: var(--el-color-primary);
}

.system-node-badge {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6a23c;
  color: white;
  border-radius: 50%;
  font-size: 10px;
}

.node-content {
  padding: 6px 12px 12px;
}

.node-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-bottom: 8px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-line;
  overflow-wrap: break-word;
  max-width: 100%;
  min-height: 18px;
}

.config-preview {
  margin-bottom: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 11px;
}

.config-key {
  color: #606266;
  font-weight: 500;
  min-width: 0;
  flex-shrink: 0;
}

.config-value {
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.node-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #909399;
}

.status-text {
  font-weight: 500;
}

/* 自定义Handle样式 */
.custom-handle {
  width: 12px !important;
  height: 12px !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.custom-handle.connected {
  opacity: 1;
}


.input-handle {
  background: var(--el-color-danger) !important;
}

.output-handle {
  background: var(--el-color-success) !important;
}

.output-handle:hover {
  .handle-el-icon{
    opacity: 1;
  }
}

.handle-el-icon{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
  color: #ffffff;
  font-weight: bold;
  opacity: 0;
}

.condition-handle {
  top: 7px;
}

.running-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 8px;
  pointer-events: none;
}

.pulse {
  width: 100%;
  height: 100%;
  border: 2px solid var(--el-color-primary);
  border-radius: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Handle 连接状态样式 */
:deep(.vue-flow__handle.connecting) {
  background: var(--el-color-primary);
  //transform: scale(1.2);
}

:deep(.vue-flow__handle.valid) {
  background: var(--el-color-success);
}

:deep(.vue-flow__handle.invalid) {
  background: var(--el-color-danger);
}

.condition_target {
  position: relative;
  padding: 0 22px 8px;
  min-width: 220px;

  .target_title {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 12px;
    color: #333333;
    margin-bottom: 8px;

    .case {
      color: #666666;
    }
  }

  .target_content {
    position: relative;
    padding-left: 25px;

    .content_item {
      background: #f2f4f8;
      border-radius: 4px;
      padding: 0 4px;
      line-height: 1.5rem;
      &~.content_item {
        margin-top: 4px;
      }

      span {
        font-size: 13px;

        & ~ span {
          margin-left: 4px;
        }

        &.field {
          color: #0066cc;
        }

        &.operator {
        }

        &.value {
        }
      }
    }

    .condition-actions {
      display: flex;
      align-items: center;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: #ffffff;
      left: -8px;
      border: 4px solid #fff;
      border-radius: 24px;
      z-index: 2;
      font-size: 12px;
      &.and-action{
        left: -18px;
      }
    }
    .condition-relation-line {
      border: 1px solid #dddee1;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      border-right-width: 0;
      position: absolute;
      height: calc(100% - 30px);
      top: 15px;
      width: 10px;
      z-index: 1;
      left: 10px;
    }
    .relation-switch {
      border: 2px solid #ebeef5;
      border-radius: 24px;
      line-height: 1;
      padding: 2px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #0054D2;
      font-weight: bold;
      font-size: 12px;
    }
  }
}
</style>
