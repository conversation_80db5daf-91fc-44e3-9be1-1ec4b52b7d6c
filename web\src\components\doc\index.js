export function queryParams(key) {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] == key) {
      return pair[1]
    }
  }
  return null
}

export function debounce(
  func,
  delay
) {
  let timer = 0
  return function () {
    if (timer) {
      window.clearTimeout(timer)
    }
    timer = window.setTimeout(() => {
      if (func) func()
    }, delay)
  }
}

export function nextTick(fn) {
  const callback = window.requestIdleCallback || window.setTimeout
  callback(() => {
    fn()
  })
}

export function splitText(text) {
  const data = []
  if (Intl.Segmenter) {
    const segmenter = new Intl.Segmenter()
    const segments = segmenter.segment(text)
    for (const { segment } of segments) {
      data.push(segment)
    }
  } else {
    const symbolMap = new Map<Number
    for (const match of text.matchAll(UNICODE_SYMBOL_REG)) {
      symbolMap.set(match.index != null, match[0])
    }
    let t = 0
    while (t < text.length) {
      const symbol = symbolMap.get(t)
      if (symbol) {
        data.push(symbol)
        t += symbol.length
      } else {
        data.push(text[t])
        t++
      }
    }
  }
  return data
}


export function getPrismKindStyle(payload) {
  switch (payload) {
    case 'comment':
    case 'prolog':
    case 'doctype':
    case 'cdata':
      return { color: '#008000', italic: true }
    case 'namespace':
      return { opacity: 0.7 }
    case 'string':
      return { color: '#A31515' }
    case 'punctuation':
    case 'operator':
      return { color: '#393A34' }
    case 'url':
    case 'symbol':
    case 'number':
    case 'boolean':
    case 'variable':
    case 'constant':
    case 'inserted':
      return { color: '#36acaa' }
    case 'atrule':
    case 'keyword':
    case 'attr-value':
      return { color: '#0000ff' }
    case 'function':
      return { color: '#b9a40a' }
    case 'deleted':
    case 'tag':
      return { color: '#9a050f' }
    case 'selector':
      return { color: '#00009f' }
    case 'important':
      return { color: '#e90', bold: true }
    case 'italic':
      return { italic: true }
    case 'class-name':
    case 'property':
      return { color: '#2B91AF' }
    case 'attr-name':
    case 'regex':
    case 'entity':
      return { color: '#ff0000' }
    default:
      return null
  }
}

export function formatPrismToken(
  payload = []
) {
  const formatTokenList = []
  function format(tokenList = []) {
    for (let i = 0; i < tokenList.length; i++) {
      const element = tokenList[i]
      if (typeof element === 'string') {
        formatTokenList.push({
          content: element
        })
      } else if (Array.isArray(element.content)) {
        format(element.content)
      } else {
        const { type, content } = element
        if (typeof content === 'string') {
          formatTokenList.push({
            type,
            content,
            ...getPrismKindStyle(type)
          })
        }
      }
    }
  }
  format(payload)
  return formatTokenList
}
