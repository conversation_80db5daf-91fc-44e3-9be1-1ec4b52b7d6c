<template>
  <div class="result">
    <van-empty description="无执行结果" v-if="!isLoading && !motitorFileList.length" />
    <div class="file-item" v-for="(item, index) in motitorFileList" :key="index">
      <div class="file-item-title">
        <img :src="getFileType(item.nodeType)" alt="" />
        <div class="file-item-info">
          <div class="file-item-title-text" :title="item?.output?.file_name">{{ item.output.file_name }}</div>
          <div class="file-item-size-text">
            <span>{{ item.excTime }}</span>
          </div>
        </div>
      </div>
      <div class="file-item-buttons">
        <div class="file-item-botton" @click="showMessage('暂未实现')">预览</div>
        <div class="file-item-botton" @click="showMessage('暂未实现')">分享</div>
        <div class="file-item-botton" @click="showMessage('暂未实现')">下载</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import utils from '@/utils/utils.ts'
import { isNull } from '@/utils/validate'
import { computed } from 'vue'

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  recordData: {
    type: Object,
    default: () => [],
  },
})

// 分析节点，返回有fileName的节点
const motitorFileList = computed(() => {
  const list = props.recordData
    .filter((it: object) => {
      try {
        const newData = JSON.parse(isNull(it.output) ? '{}' : it.output)
        return newData.file_name ? true : false
      } catch (e) {
        return false
      }
    })
    .map((it: object) => {
      return {
        ...it,
        output: JSON.parse(isNull(it.output) ? '{}' : it.output),
      }
    })
  return list
})

const showMessage = (msg: string) => {
  showToast(msg)
}

const getFileType = (name: string) => {
  return utils.getFileType(name)
}
const formatSize = (size: number) => {
  return utils.formatSize(size)
}
</script>

<style scoped lang="scss">
.result {
  overflow-y: auto;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  background: #f1f3f5;
}

// 执行结果样式
.file-item {
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
  background: #ffffff;

  &:last-child {
    margin-top: 0;
  }

  .file-item-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    img {
      width: 30px;
      height: 30px;
      object-fit: contain;
    }

    .file-item-title-text {
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      margin-left: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .file-item-title-tag {
      width: 40px;
      height: 22px;
      background: #f7f7f9;
      border-radius: 11px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      margin-left: 8px;

      &.online {
        color: #1e39c3;
        background: #f0f5ff;
      }
    }
  }

  .file-item-info {
    flex: 1;

    .file-item-size-text {
      margin-left: 8px;
      font-size: 12px;
      color: #999999;
    }
  }

  .file-item-size {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 12px;
    color: #bcbfc3;
    padding-left: 32px;
  }

  .file-item-path {
    padding-left: 32px;
    margin-bottom: 12px;

    .file-item-path-text {
      background: #f7f7f9;
      border-radius: 4px;
      padding: 10px;
      box-sizing: border-box;
      font-weight: 400;
      font-size: 12px;
      color: #bcbfc3;
    }
  }

  .file-item-buttons {
    display: flex;
    justify-content: space-around;
    position: relative;
    padding: 16px 0 0;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: #f1f1f1;
    }

    .file-item-botton {
      display: flex;
      align-content: center;
      justify-content: center;
      position: relative;
      width: 100%;
      font-size: 14px;
      color: var(--van-primary-color);

      &:not(:first-child):before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        width: 1px;
        height: 24px;
        background: #f1f1f1;
      }
    }
  }
}
</style>
