<template>
    <div class="service-market" @scroll="handleScroll">
        <!-- <div class="banner-top" :class="{'banner-top-fixed':isFixed,'is-from-package':isUniwimPc&&isFromPackage}">
                <div class="banner-left">
                    <img style="width:123px;height: 48px;cursor: pointer;" src="../../assets/images/admin/logoV2.png" alt="WimTask logo" />
                    <div class="tabs-box">
                        <el-tabs v-model="activeName" class="tabs" @tab-click="handleTab">
                            <el-tab-pane label="产品介绍" name="product"></el-tab-pane>
                            <el-tab-pane label="服务包市场" name="market"></el-tab-pane>
                            <el-tab-pane label="学习中心" name="study"></el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
                <div class="banner-right">
                    <div class="logo-tel">
                        <img style="width: 16px; height: 16px; margin-right: 15px"
                            src="../../assets/images/homepage/tel.png" alt="电话图标" />
                        热线：0573-82875638
                    </div>
                    <div class="menus">
                        <div class="menu-item" @click="jumpToPage('manager')" v-if="token">工作台</div>
                        
                    </div>
                </div>
        </div> -->
        <div class="page-content">
            <div class="banner">
                <div class="banner-inner">
                    <div class="banner-desc">
                        <img src="../../assets/images/homepage/banner-text.png" alt="服务市场标题" width="1200px">
                    </div>
                </div>
            </div>
            <div class="main">
                <div class="main-left">
                    <!-- <div class="checkbox-item" v-for="cate in staticCategories" :key="cate.cate">
                        <span>{{ cate.cate }}</span>
                        <el-checkbox
                            style="line-height:36px;"
                                v-model="checkAll"
                                :indeterminate="isIndeterminate"
                                @change="handleCheckAllChange"
                            >
                            <div class="checkbox-label">
                                <span class="checkbox-label-name">{{ cate.items[0].name }}</span>
                                <span class="checkbox-label-number">({{ cate.items[0].num  }})</span>
                            </div>
                        </el-checkbox>
                        <el-checkbox-group v-model="cate.checkList" @change="handleCheckedChange($event,cate)">
                            <template  v-for="item in cate.items"  :key="item.code">

                                <el-checkbox v-if="item.code!=='all'" :label="item.name" :value="item.code">
                                    <div class="checkbox-label">
                                        <span class="checkbox-label-name">{{ item.name }}</span>
                                        <span class="checkbox-label-number">({{ item.num }})</span>
                                    </div>
                                </el-checkbox>
                            </template>

                        </el-checkbox-group>
                    </div> -->
                    <div class="checkbox-item" v-for="cate in categories" :key="cate.cate">
                        <span>{{ cate.name }}</span>
                        <el-checkbox-group v-model="cate.checkList" @change="handleCheckedChange($event,cate)">
                            <template  v-for="item in cate.items"  :key="item.id">

                                <el-checkbox :label="item.name" :value="item.id">
                                    <div class="checkbox-label">
                                        <span class="checkbox-label-name">{{ item.name||item.category }}</span>
                                        <span class="checkbox-label-number">({{ item.resourceNum }})</span>
                                    </div>
                                </el-checkbox>
                            </template>

                        </el-checkbox-group>
                    </div>
                </div>
                <div class="main-right">
                    <div class="card-list" v-loading="loading">
                        <div class="card-conditions">
                            <div class="card-search">
                                <el-input v-model="keyword" @keyup.enter="getListData" style="width: 450px" size="large" placeholder="搜索场景、MCP、模版…"
                                    :prefix-icon="Search" />
                                <div class="condition-extend">
                                    <div class="total">共找到 {{ total }} 个结果</div>
                                    <el-select @change="getListData" v-model="orderType" slot="prepend" placeholder="请选择">
                                        <el-option :label="type.label" :value="type.value" :key="type.value"
                                            v-for="type in orderTypes"></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="checklist-box" v-show="checklist&&checklist.length">
                                <span>已选择:</span>
                                <el-tag
                                    v-for="item in checklist"
                                    :key="item.code"
                                    @close="closeTag(item)"
                                    class="normal-tag"
                                    round
                                    effect="dark"
                                    closable
                                >
                                {{ item.name }}
                                </el-tag>
                                <el-tag @click="clearTags"
                                    class="clear-tag"
                                    type="danger"
                                    effect="dark"
                                    round
                                    >清空全部</el-tag>
                                </div>
                            <!-- <div class="card-radio">
                                <div class="radio-con">
                                    <div class="radio-item" :class="[activeRadio === radio.value ? 'active' : '']"
                                        @click="handleRadioChange(radio.value)" v-for="radio in radios" :key="radio.value">
                                        {{ radio.label }}</div>
                                </div>
                            </div> -->
                        </div>
                        <el-scrollbar style="height: calc(100% - 96px)">
                            <div class="card-content clearfix">
                                <div class="card-item" @click="clickCard(package_item)" v-for="package_item in servicePackages" :key="package_item.title">
                                    <div class="card-img">
                                        <img :src="package_item.image" alt="" style="width:100%;height:100%;">
                                    </div>
                                    <div class="card-item-inner">


                                    <div class="card-detail">
                                        <div class="card-name">
                                            <i class="card-icon">
                                                <img style="width:100%;height:100%" :src="package_item.iconPath||packageIcon" alt="">
                                            </i>
                                            <span>{{ package_item.name }}</span>
                                        </div>
                                        <div class="card-desc">
                                            {{ package_item.summary }}
                                        </div>
                                        <div class="card-rate">
                                            <!-- <el-rate v-model="rate" disabled show-score text-color="#ff9900"
                                                score-template="{value}" /> -->
                                            <div class="card-group">{{ package_item.provider }}</div>
                                            <!-- <span class="subscribe-number">已使用{{ package_item.downloadCount || 0 }}次</span> -->
                                        </div>
                                        <!-- <div class="card-price" :class="[package_item.price===0||package_item.price==='免费'?'free':'']">{{ package_item.price }}</div> -->
                                        <!-- <div class="card-group">{{ package_item.developer }}</div> -->
                                        <!-- <div class="card-buttons">
                                            <div class="detail-button">查看详情</div>
                                            <div class="buy-button">立即购买</div>
                                        </div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                    <div class="pager">
                        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                            :page-sizes="[9, 12, 15, 18]" layout="total, sizes, prev, pager, next, jumper"
                            :total="pagination.total" @change="getListData"/>
                    </div>
                </div>
            </div>
        </div>
        <footer class="footer-section">
            <div class="container">
                <!-- <div class="footer-main">
                    <div class="footer-left">
                        <div class="footer-left-title">联系我们</div>
                        <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                        <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                        <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                    </div>
                    <div class="footer-middle">
                        <p>一诺数字助理</p>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                            class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                            class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                    </div>
                    <div class="footer-right">
                        <div class="qrcode-box">
                            <p>一诺APP</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                            </div>
                        </div>
                        <div class="qrcode-box">
                            <p>度量公众号</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                            </div>
                        </div>
                    </div>
                </div> -->
                <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
            </div>

        </footer>
        <div class="mask" v-show="isDetailShow"></div>
        <div class="detail-dialog" v-show="isDetailShow">
            <div class="dialog-top">
                <div class="dialog-title">
                    <img :src="currentCard.iconPath" alt="">
                    <span>{{ currentCard.name }}</span>
                </div>
                <div class="dialog-tags">
                    <div class="tag-item" v-show=" currentCard.categoryMain">
                        {{ currentCard.categoryMain==='scene'?'场景':(currentCard.categoryMain==='template'?'模板':'MCP') }}
                    </div>
                    <div class="tag-item" v-show="currentCard.categoryIds" :key="item" v-for="item in currentCard.categoryIds">
                        {{ categoriesMap[item] }}
                    </div>
                    <div class="tag-item" v-show="currentCard.versionNumber">
                        {{ currentCard.versionNumber?'v'+currentCard.versionNumber:'' }}
                    </div>
                </div>
                <div class="dialog-time-number">
                    <div class="dialog-time">更新时间：{{ currentCard.updated }}</div>
                    <!-- <div class="dialog-number">使用次数：{{ currentCard.downloadCount ||0 }}</div> -->
                </div>
                <div class="close-btn" @click="isDetailShow=false">
                    <el-icon><Close /></el-icon>
                </div>
            </div>
            <div class="dialog-content">
                <div class="profile">
                    <div class="profile-title">
                        简介
                    </div>

                    <el-scrollbar height="80px">
                        <div class="profile-content">
                            {{ currentCard.summary }}
                        </div>
                    </el-scrollbar>
                </div>
                <div class="detail ql-snow">
                    <div class="detail-title">详情</div>
                    <el-scrollbar height="100%">
                        <div class="detail-content ql-editor" v-html="currentCard.description">
                        </div>
                    </el-scrollbar>
                </div>

                <!-- <div class="detail-button">
                    联系我们
                </div> -->
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive,computed, onMounted } from 'vue';
import utils from '@/utils/utils'
import saasApi from '@/api/index';
import moment from 'moment'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

const iconsModule = import.meta.glob('@/assets/images/servicePackage/icon/*.png', { eager: true })
const coversModule = import.meta.glob('@/assets/images/servicePackage/image/*.png', { eager: true })

const isIndeterminate = ref(false)
const keyword = ref('')
const isFixed = ref(true)
const checkAll = ref(true)
const isDetailShow = ref(false)
const desc_str =ref('<ol><li>你好</li><li>我是</li><li>小I</li><li><a href= "www.baidu.com" rel="noopener noreferrer" target="_blank">百度</a ></li></ol>')

// 定义分类项接口
interface CategoryItem {
    name: string;
    code: string;
    num: number;
}

// 定义分类接口
interface Category {
    cate: string;
    checkList: string[];
    items: CategoryItem[];
}

interface OrderTypes {
    label: string;
    value: string;
}

interface RadioType {
    label: string;
    value: string;
}

interface cardType {
    categoryIds:object;
    categoryMain:string;
    iconPath:string;
    image:string;
    name:string;
    downloadCount:string;
}


const currentCard = ref({
    categoryIds:[],
    categoryMain:"",
    iconPath:"",
    image:"",
    name:"",
    versionNumber:"",
    updated:"",
    downloadCount:""
})


const staticCategories = reactive([
    {
        cate: '分类浏览',
        checkList: [],
        items: [
            // { name: '全部服务', code: 'all', num: 0 },
            { name: '场景', id: 'scene', num: 0 },
            { name: 'MCP', id: 'mcp', num: 0 },
            { name: '模板', id: 'template', num: 0 }
        ]
    }
])

const checklist = ref([
    { name: '场景', id: 'scene' },
])
const checklistMap = ref({
    "static":[
        { name: '场景', id: 'scene' }
    ]
})


const categoriesMap = ref({})
// 初始化分类数据
const categories: Category[] = reactive([
    {
        category: 'static',
        name: '类型',
        checkList: ["scene"],
        items: [
            // { name: '全部服务', code: 'all', num: 0 },
            { name: '场景', id: 'scene', resourceNum: 0 },
            { name: 'MCP', id: 'mcp', resourceNum: 0 },
            { name: '模板', id: 'template', resourceNum: 0 }
        ]
    },
    {
        category: 'dynamics',
        name: '分类',
        checkList: [],
        items: [
            // { name: '水务管理', code: '/services/industries/water', num: 89 },
            // { name: '办公自动化', code: '/services/industries/oa', num: 156 },
            // { name: '制造业', code: '/services/industries/manufacturing', num: 78 },
            // { name: '金融服务', code: '/services/industries/finance', num: 45 },
            // { name: '零售电商', code: '/services/industries/retail', num: 67 },
            // { name: '医疗健康', code: '/services/industries/healthcare', num: 34 }
        ]
    },
    // {
    //     cate: '价格筛选',
    //     checkList: [],
    //     items: [
    //         { name: '免费', code: '/services/prices/free', num: 234 },
    //         { name: '¥1-99', code: '/services/prices/1-99', num: 345 },
    //         { name: '¥100-499', code: '/services/prices/100-499', num: 456 },
    //         { name: '¥500+', code: '/services/prices/500plus', num: 199 }
    //     ]
    // }
]);

const activeName = ref('market')

const loading = ref(false)

const pagination = ref({
    currentPage: 1,
    pageSize: 9,
    total: 0
})


const classify = ref([])
const total = ref(0)

const typeMap = ref({})

const orderTypes: OrderTypes[] = [
    // { label: '综合排序', value: 'comprehensive' },
    { label: '最新发布', value: 'created' },
    // { label: '评分最高', value: 'highestRating' },
    // { label: '价格最低', value: 'lowestPrice' },
    // { label: '使用最多', value: 'downloadCount' }
];

const servicePackages = ref([
    // {
    //     name: '和达水务专业包',
    //     description: '专为水务行业设计的完整解决方案，包含水源监测、设备管理等功能',
    //     rating: 5.0,
    //     subscription: '1.2345万订阅',
    //     price: '¥待定',
    //     category: '专业包',
    //     developer: '浙江和达科技',
    //     image: servicePackage1,
    // },
    // {
    //     name: '办公自动化包',
    //     description: '提升办公效率的自动化工具集，支持文档处理、数据分析等',
    //     rating: 5.0,
    //     subscription: '1.1234万订阅',
    //     price: '¥待定',
    //     category: '专业包',
    //     developer: 'WimTask官方',
    //     image: servicePackage2,
    // },
    // {
    //     name: '制造业智能包',
    //     description: '工业4.0智能制造解决方案，包含生产调度、质量控制等',
    //     rating: 5.0,
    //     subscription: '1.1345万订阅',
    //     price: '¥待定',
    //     category: '专业包',
    //     developer: '智造科技',
    //     image: servicePackage3,
    // },
    // {
    //     name: '水质监测工作流模板',
    //     description: '自动采集水质数据，生成监测报告，异常情况自动报警，开箱即用的水质监测解决方案',
    //     rating: 5.0,
    //     subscription: '1.2345万订阅',
    //     price: '¥待定',
    //     category: '模板',
    //     developer: '模板库',
    //     image: servicePackage4,
    // },
    // {
    //     name: '财务报表自动化模板',
    //     description: '自动收集财务数据，生成标准化报表，支持多种格式导出，提升财务工作效率',
    //     rating: 5.0,
    //     subscription: '1.1234万订阅',
    //     price: '¥待定',
    //     category: '模板',
    //     developer: 'WimTask官方',
    //     image: servicePackage5,
    // },
    // {
    //     name: '客户服务自动化模板',
    //     description: '智能客服工作流，自动回复常见问题，工单自动分配，提升客户满意度',
    //     rating: 5.0,
    //     subscription: '1.1234万订阅',
    //     price: '¥待定',
    //     category: '模板',
    //     developer: '客服专家',
    //     image: servicePackage6,
    // },
    // {
    //     name: 'Excel数据处理MCP',
    //     description: '强大的Excel文件处理工具，支持数据清洗、格式转换、图表生成，让数据处理更简单',
    //     rating: 5.0,
    //     subscription: '1.2345万订阅',
    //     price: '¥待定',
    //     category: 'MCP',
    //     developer: '数据工具',
    //     image: servicePackage7,
    // },
    // {
    //     name: '邮件发送MCP',
    //     description: '批量邮件发送工具，支持模板定制、发送统计、退信处理，提升邮件营销效果',
    //     rating: 5.0,
    //     subscription: '1.1234万订阅',
    //     price: '¥待定',
    //     category: 'MCP',
    //     developer: '通信工具',
    //     image: servicePackage8,
    // },
    // {
    //     name: '数据库连接MCP',
    //     description: '支持多种数据库连接，提供数据查询、更新、备份等功能，简化数据库操作',
    //     rating: 5.0,
    //     subscription: '1.1234万订阅',
    //     price: '¥待定',
    //     category: 'MCP',
    //     developer: '数据专家',
    //     image: servicePackage9,
    // }
]);


const radios: RadioType[] = [
    { label: '全部', value: 'all' },
    { label: '热门', value: 'hot' },
    { label: '最新', value: 'new' },
    { label: '免费', value: 'free' },
    { label: '付费', value: 'pay' }
];
// 搜索框数据
const input1 = ref('');


const rate = ref(5)

const activeRadio = ref('all')


const orderType = ref('created');


// 定义计算属性，和 Vue2 中 computed 配置项里的函数作用一致
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc'||utils.GetQueryString('uniwim')=='pc'
});

const isFromPackage = computed(() => {
  return route.query.from === 'package'||utils.GetQueryString('from')=='package'
});

const token = computed(() => {
  return utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
});


const formatTime = (time:number)=>{
      return moment(time*1000).format("YYYY-MM-DD HH:mm")
}

const clearTags = ()=>{
    checklist.value = []
    checklistMap.value = {}
    categories.forEach(cate=>{
        cate.checkList = []
    })
    getListData();
}

const clickCard = (row:any)=>{
    currentCard.value = row
    let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
    if(token){
        route.query.uniwater_utoken = token;
    }
    let pathname = window.location.pathname;
    let url = utils.generateUrlWithQuery(pathname+'#/serviceDetail/'+row.id,route.query)
    window.open(url)
    // isDetailShow.value = true;
    // router.push({
    //     path:'/serviceDetail/'+row.id,
    //     query:route.query
    // })
}

const closeTag = (item:any,cate:any)=>{
    let index = checklist.value.findIndex(it=>it.id==item.id);
    if(index>-1){
       checklist.value.splice(index,1)
       let static_index = -1;
       let dynamics_index = -1;
       if(checklistMap.value['static']){
        static_index = checklistMap.value['static'].findIndex(it=>it.id==item.id);
       }
       if(checklistMap.value['dynamics']){
        dynamics_index = checklistMap.value['dynamics'].findIndex(it=>it.id==item.id);
       }
       if(static_index>-1){
        checklistMap.value['static'].splice(static_index,1)
       }
       if(dynamics_index>-1){
        checklistMap.value['dynamics'].splice(dynamics_index,1)
       }
    }

    categories.forEach(cate=>{
        let index = cate.checkList.findIndex(it=>it===item.id)
        if(index>-1){
            cate.checkList.splice(index,1)
        }
    })
    getListData();
}

const handleScroll = (event:any)=>{
    if(event.target.scrollTop==0){
        isFixed.value = true;
    }else{
        isFixed.value = false;
    }
}

const getListData = ()=>{
    const params = {
        url: "/wimai/api/task/template/query",
        body_param: {
            conditions: [],
            data: {
                // type:''
                status:1
            },
            order:[
                {
                    Field:"sort",
                    Type:1
                },
                {
                    Field:"updated",
                    Type:-1
                }
            ],
            size:pagination.value.pageSize,
            index: pagination.value.currentPage
        },
        method: "post"
    }
    if(orderType.value){
        params.body_param.order.push({
            Field:orderType.value,
            Type:-1
        })
    }
    if(checklistMap.value){
        if(!checklist.value.length){
            params.body_param.data.categoryMains = []
            params.body_param.data.categoryIds = []
            // servicePackages.value = []
            // total.value = 0;
            // pagination.value.total = 0
            // return;
        }
        for(let key in checklistMap.value){
            if(key==='static'){
                params.body_param.data.categoryMains = checklistMap.value[key].map(it=>it.id)
            }else{
                params.body_param.data.categoryIds = checklistMap.value[key].map(it=>it.id)
            }
        }
    }
    if(keyword.value){
        params.body_param.data.name = keyword.value
    }
    loading.value = true
    saasApi.AIAgentTaskTemplateQuery(params.body_param).then((res:any) => {
        // let img_list = [servicePackage1,servicePackage2,servicePackage3,servicePackage4,servicePackage5,servicePackage6,servicePackage7,servicePackage8,servicePackage9]
        if(res?.rows){
            // res.rows.forEach((row:any,index:number)=>{
            //     row.img = img_list[index]
            //     // row.developer = classify.value.find(item=>item.Value==row.templateType)?.Name
            // })
            servicePackages.value = res.rows
            total.value = res.total
            pagination.value.total = res.total
        }else{
            servicePackages.value = []
            total.value = 0;
            pagination.value.total = 0
            throw new Error('获取服务包数据失败')
        }
    })
    .catch((err:any) => {
        total.value = 0
        pagination.value.total = 0
        servicePackages.value = []
    }).finally(()=>{
        loading.value = false
    })
}


const getTypes  = ()=>{
    const params = {
        url: "/wimai/api/task/resource/category/query",
        body_param: {
            conditions: [],
            data: {
                status:"enable"
            },
            order:[
                {
                    Field:"sort",
                    Type:1
                },
                {
                    Field:"updated",
                    Type:-1
                }
            ],
            index: 1,
        },
        method: "post"
    }
    saasApi.AIAgentTaskTemplateCountCategoryMainIg({
            status:1
        }).then((res:any) => {
        if(res){
            for(let key in res){
                let staticCategoriesList = categories[0].items
                let obj = staticCategoriesList.find(cate=>cate.id==key)
                if(obj){
                    obj.resourceNum = res[key]
                }
            }

        }

    })
    saasApi.AIAgentTaskCategoryQueryIg(params.body_param).then((res:any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach((row:any)=>{
                categoriesMap.value[row.id] = row.name
                // if(row.typeList){
                //     row.typeList.forEach(tp=>{
                //         let staticCategoriesList = categories[0].items
                //         let obj = staticCategoriesList.find(cate=>cate.id==tp.type)
                //         if(obj){
                //             obj.resourceNum++
                //         }
                //     })
                // }
            })
            categories[1].items = res.rows;
        }
    })
    .catch((err: any) => {
    })
    .finally(() => {
    })

}

const handleCheckedChange = (value: any[],cate:any) => {
    checklist.value = []
    let staticType =  JSON.parse(JSON.stringify(categories[0].items.filter(item=>value.includes(item.id))));
    let dynamicsType = JSON.parse(JSON.stringify(categories[1].items.filter(item=>value.includes(item.id))));
    checklistMap.value[cate.category] = cate.category==='static'?staticType:dynamicsType;
    for(let key in checklistMap.value){
        checklist.value = checklist.value.concat(checklistMap.value[key])
    }
    getListData();
}

const handleCheckAllChange = (val: any) => {
  staticCategories[0].checkList = val ? staticCategories[0].items.map(item=>item.id) : []
  isIndeterminate.value = false
}

const handleRadioChange = (value: string) => {
    activeRadio.value = value;
    getListData()
}

const toHome = ()=>{
    router.push('/homepage')
}

const handleTab = (type:string)=>{
    let path = '/homepage'
    if(type.paneName==='product'){
        path = '/homepage'
    }else if(type.paneName==='market'){
        path = '/serviceMarket'
    }else if(type.paneName==='study'){
        path = '/study'
    }
            
    router.push({
        path: path,
        query: route.query
    })
}

const jumpToPage = (path:string)=>{
    // router.push({
    //     name: path,
    //     query: route.query
    // })
    let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
    if(token){
        route.query.token = token;
    }
    let url = utils.generateUrlWithQuery("https://www.dlmeasure.com/extends/WimTask/index.html#/manager",route.query)
    window.location.href = url
}
onMounted(async()=>{
    await getTypes();
    getListData();
})
</script>
<style>

</style>

<style lang='less' scoped>
:deep(ol) {
     padding-left: 0; /* 移除默认左侧内边距 */
    list-style-position: inside; /* 让列表项编号显示在内容内部 */
}
:deep(li) {
}
.service-market {
    width: 100%;
    height: 100%;
    background:#fff;
    overflow: auto;
    position:relative;
    .clearfix::after {
        content: "";
        display: table;
        clear: both;
    }
    .mask{
        position: fixed;
        top:0;
        left:0;
        bottom:0;
        right:0;
        background: rgba(0,0,0,0.4);
        z-index:100;
    }
    .detail-dialog{
        width:1000px;
        position: fixed;
        top:50%;
        left:50%;
        height:90%;
        box-sizing: border-box;
        transform:translate(-50%,-50%);
        z-index: 101;
        border-radius: 8px;
        overflow: hidden;
        .dialog-top{
            height:140px;
            width:100%;
            padding:26px 24px;
            background: url('../../assets/images/servicePackage/detailDialog.png') no-repeat center;
            background-size: cover;
            .dialog-title{
                display:flex;
                align-items: center;
                img{

                    width:24px;
                    height:24px;
                }
                span{
                    margin-left:12px;
                    flex:1;
                    height: 29px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 20px;
                    color: #222222;
                }

            }
            .dialog-tags{
                display:flex;
                margin-top:12px;
                gap:12px;
                .tag-item{
                    padding:5px 8px;
                    background: rgba(255,255,255,0.4);
                    border-radius: 14px;
                    text-align:center;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #616770;
                }
            }
            .dialog-time-number{
                margin-top:12px;
                height: 18px;
                display:flex;
                gap:12px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 12px;
                color: #616770;
            }
            .close-btn{
                position:absolute;
                right:24px;
                top:24px;
                font-size:12px;
                font-weight:bold;
                line-height: 36px;
                text-align:center;
                cursor: pointer;
                background:rgba(255,255,255,0.5);
                border-radius: 50%;;
                width:36px;
                height:36px;
                color:#000;
            }
        }
        .dialog-content{
            background:#fff;
            padding:24px;
            height:calc(100% - 140px);
            .detail{

                height:calc(100% - 140px);
                margin-top:24px;
                :deep(.el-scrollbar){
                    height:calc(100% - 24px);
                }
            }
            .profile-title,.detail-title{
                height: 24px;
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 16px;
                color: #222222;
                font-weight:bold;
                margin-bottom:12px;
            }
            .profile-content,.detail-content{
                // line-height: 20px;
                // font-family: SourceHanSansSC-Regular;
                // font-weight: 400;
                font-size: 13px;
                line-height:2;
                // height:80px;
                // color: #808A94;
                overflow:auto;
            }
            .detail-content{
                // height:260px
                :deep(img){
                    width:100%;
                }
            }
        }
        .detail-button{
            margin:0 auto;
            margin-top:24px;
            width: 552px;
            height: 48px;
            background: #0054D2;
            border-radius: 4px;
            line-height:48px;
            text-align: center;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            cursor:pointer;
        }
    }
}

.page-content{
    // min-width:1400px;
    min-width:1440px;
    // height:100%;
}
.banner-top {
    width:100%;
    // width:1400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    background:#fff;
    // height: 80px;
    margin: 0 auto;
    position:fixed;
    top:0;
    z-index:10;
    transition: background-color 0.3s ease; // 添加过渡效果
    box-shadow: 0 1px 30px 0 #4a5c7a29;
    &.banner-top-fixed{
        background: transparent;
        box-shadow: none;
    }
    &.is-from-package{
        top:42px;
    }

    .banner-left{
        display:flex;
        // height:58px;
        // align-items: center;
        img{
            margin-top:5px;
        }
        .tabs-box{
            display:flex;
            align-items: center;
            height:58px;
        }
        .el-tabs{
            height:40px;
            margin-left:68px;
        }
        /deep/.el-tabs__header{
            margin:0;
        }
        /deep/.el-tabs__nav-wrap::after{
            background:transparent;
        }
        /deep/.el-tabs__item{
            font-size: 16px;
        }
        /deep/.el-tabs__item.is-active{
            font-weight: 700;
            font-size: 16px;
        }
    }
    .banner-right {
        display: flex;
        font-size: 16px;
        align-items: center;
        .logo-tel{
            display:flex;
            align-items: center;
        }
        .menus {
            display: flex;
            margin-left: 50px;

            .menu-item {
                margin-right: 30px;
                cursor: pointer;
                &:last-of-type {
                    margin-right: 0;
                }
            }
        }
    }

}
.banner {
    width: 100%;
    min-width: 1400px;
    height: 340px;
    background: url('../../assets/images/homepage/banner-bg.jpg') no-repeat center top;
    background-size: cover;
    .banner-inner{
        // width:1400px;
    }
    .banner-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        // height: 80px;
        height:58px;
        margin: 0 auto;
        .banner-left{
            display:flex;
            // height:58px;
            // align-items: center;
            img{
                margin-top:20px;
            }
            .tabs-box{
                display:flex;
                align-items: center;
                height:58px;
            }
            .el-tabs{
                height:40px;
                margin-left:68px;
            }
            /deep/.el-tabs__header{
                margin:0;
            }
            /deep/.el-tabs__nav-wrap::after{
                background:transparent;
            }
            /deep/.el-tabs__item{
                font-size: 16px;
            }
            /deep/.el-tabs__item.is-active{
                font-weight: 700;
                font-size: 16px;
            }
        }
        .banner-right {
            display: flex;
            font-size: 16px;
            align-items: center;

            .logo-tel{
                display:flex;
                align-items: center;
            }
            .menus {
                display: flex;
                margin-left: 50px;

                .menu-item {
                    cursor: pointer;
                    margin-right: 30px;

                    &:last-of-type {
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .banner-desc {
        // height: 132px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 40px;
        margin: 0 auto;
        padding-top: 100px;
        width: 1200px;
        color: #232425;
        display: flex;
        align-items: center;

        b {
            font-family: YouSheBiaoTiHei;
            font-size: 56px;
            color: #222222;
            background: -webkit-linear-gradient(left, #ff7e5f, #feb47b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            text-shadow: 5px 8px 9px #0054d238;
        }
    }

    .banner-button {
        width: 200px;
        height: 64px;
        box-sizing: border-box;
        background: url('../../assets/images/homepage/banner-button.png') no-repeat center;
        background-size: cover;
        margin: 0 auto;
        margin-top: 97px;
        cursor: pointer;
        line-height: 64px;
        padding-left: 40px;
        font-size: 20px;
        color: #FFFFFF;
    }
}

.main {
    width: 1400px;
    margin: 0 auto;
    padding: 30px 0;
    display: flex;
    background:#fff;
    /* 补充flex布局，让左右部分并排显示 */
    gap: 20px;

    .main-left {
        width: 240px;

        /deep/.el-checkbox {
            height: 36px;
            width: 100%;
        }

        /deep/.el-checkbox__label {
            font-size: 12px;
            height:100%;
            line-height: 36px;
        }

        .checkbox-item {
            margin-bottom: 30px;

            span {
                display: block;
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 14px;
                color: #222222;
                margin-bottom: 8px;
            }

            .checkbox-label {
                width: 220px;
                display: flex;
                justify-content: space-between;
                .checkbox-label-name {
                    max-width:120px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    line-height: 36px;
                    overflow:hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .checkbox-label-number {
                    font-weight: 400;
                    font-size: 12px;
                    color: #BCBFC3;
                    text-align: right;
                    line-height: 36px;
                }
            }
        }
    }

    .main-right {
        margin-left: 40px;
        flex: 1;

        /* 占剩余空间 */
        .card-list {
            width: 100%;

            // height: 722px;
            height:1124px;
            overflow: visible;
            // overflow-y: auto;

            .card-conditions {
                width: 100%;

                /deep/.el-input {
                    height: 32px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #BCBFC3;
                }

                /deep/.el-select {
                    width: 160px;
                    height: 32px;
                    background: #FFFFFF;
                    // border: 1px solid #E6E7E9;
                    border-radius: 4px;

                    .el-select__selected-item.el-select__placeholder {
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #222222;
                    }
                }

                .card-search {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;

                    .condition-extend {
                        display: flex;
                        align-items: center;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #222222;

                        /deep/.el-select {
                            margin-left: 16px;
                        }
                    }
                }
                .checklist-box{
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    text-align: left;
                    margin-top:16px;
                    span{
                        margin-right:8px;
                    }
                    .clear-tag{
                        height:32px;
                        line-height:32px;
                        cursor: pointer;
                        padding:8px 16px;
                    }
                    .normal-tag{
                        padding:8px 16px;
                        cursor: pointer;
                        height: 32px;
                        background: #FFFFFF;
                        border: 1px solid #E6E7E9;
                        border-radius: 16px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #222222;
                        text-align: left;
                        margin-right:8px;
                        :deep(.el-tag__close){
                            color:#BCBFC3;
                        }
                        :deep(&.el-tag .el-tag__close:hover){
                            background: #999;
                        }
                    }
                }

            }

            .card-radio {
                width: 100%;

                .radio-con {
                    margin-top: 16px;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    gap: 16px;
                }

                .radio-item {
                    width: 56px;
                    cursor: pointer;
                    height: 32px;
                    line-height: 32px;
                    text-align: center;
                    background: #0054D2;
                    border-radius: 16px;
                    font-weight: 400;
                    font-size: 12px;
                    background: #FFFFFF;
                    border: 1px solid #E6E7E9;
                    border-radius: 16px;
                    color: #222222;

                    &.active {
                        background: #0054D2;
                        border-radius: 16px;
                        color: #fff;
                    }
                }
            }

            .card-content {
                margin-top: 4px;
                // height:calc(100% - 96px);
                height:100%;
                overflow:auto;
                padding-left:12px;
                padding-top:12px;
                .card-item {
                    cursor:pointer;
                    float:left;
                    // width: 321px;
                    width:calc(33.3% - 2px);
                    height: 324px;
                    background: #fff;
                    border-radius: 8px;
                    padding:10px;
                    box-sizing: border-box;
                    margin-bottom:12px;
                    margin-right:2px;

                    &:hover {
                        box-shadow: 0 2px 12px 0 #0000001a;
                    }

                    .card-img {
                        width: 339px;
                        height: 188px;
                        // background: #EDF1FF;
                        border-radius: 8px;
                        margin-bottom: 12px;
                        margin:0 auto;
                    }
                    .card-item-inner{
                        width: 339px;
                        margin:0 auto;
                        margin-top:12px;
                    }
                    .card-name {
                        height: 24px;
                        width:100%;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 16px;
                        color: #222222;
                        display: flex;
                        align-items: center;
                        span{
                            width:100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .card-icon {
                        width: 24px;
                        display: block;
                        height: 24px;
                        // background: url('../../assets/images/homepage/package.png') no-repeat;
                        background-size: 100% 100%;
                        margin-right: 10px;
                    }

                    .card-desc {
                        width:100%;
                        margin-top: 10px;
                        height:40px;
                        line-height:20px;
                        overflow: hidden;
                        display: -webkit-box;             /* 必须设置，用于多行溢出 */
                        -webkit-box-orient: vertical;     /* 设置盒子排列方向为垂直 */
                        -webkit-line-clamp: 2;            /* 限制显示的行数，例如显示 2 行 */
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 14px;
                        color: #808A94;
                    }

                    .card-rate {
                        display: flex;
                        align-items: center;
                        margin-top: 10px;
                        justify-content: space-between;
                        .subscribe-number {
                            font-family: SourceHanSansSC-Regular;
                            font-weight: 400;
                            font-size: 12px;
                            color: #BCBFC3;
                            // margin-left: 23px;
                        }
                    }

                    .card-price {
                        height: 24px;
                        font-family: SourceHanSansSC-Bold;
                        font-weight: 700;
                        font-size: 16px;
                        color: #E6A23C;
                        &.free{
                            color: #5ACD90;
                        }
                    }

                    .card-group {
                        height: 20px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 14px;
                        color: #808A94;
                        // margin-top: 10px;
                    }

                    .card-buttons {
                        margin-top: 24px;
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;

                        .detail-button {
                            width: 141px;
                            height: 32px;
                            line-height: 32px;
                            text-align: center;
                            background: #FFFFFF;
                            border: 1px solid #E6E7E9;
                            border-radius: 4px;
                            font-size: 12px;
                            color: #222222;
                        }

                        .buy-button {
                            width: 141px;
                            height: 32px;
                            line-height: 32px;
                            text-align: center;
                            background: #0054D2;
                            border-radius: 4px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #FFFFFF;
                        }
                    }
                }
            }
        }
    }

    .pager {
        // float: right
        // margin-top:16px;
        width:100%;
        display:flex;
        justify-content: flex-end;
    }
}

// 底部样式
.footer-section {
    background-color: #f5f5f6;
    // padding: 20px 0;
    width: 100%;
    min-width: 1400px;
    .container {
        width: 1400px;
        margin: 0 auto;

        .footer-left-title {
            width: 64px;
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            margin-bottom: 12px;
            color: #191919;
        }

        .footer-left {
            p {
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #595959;
                line-height: 40px;
                display: flex;
                align-items: center;

                &.address i {
                    background: url('../../assets/images/homepage/position.png') no-repeat;
                    background-size: cover;
                }

                &.tel i {
                    background: url('../../assets/images/homepage/telephone.png') no-repeat;
                    background-size: cover;
                }

                &.email i {
                    background: url('../../assets/images/homepage/email.png') no-repeat;
                    background-size: cover;
                }
            }

            i {
                display: block;
                width: 14px;
                height: 14px;
                margin-right: 10px;

            }
        }
    }

    .footer-main {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .footer-middle {
        height: 144px;

        p {
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            margin-bottom: 24px;
        }

        .download-btn {
            width: 149px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            border: 1px solid #0054d2;
            border-radius: 4px;
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 0 10px;
            margin-bottom: 20px;
            color: #0054d2;

            &:hover {
                opacity: 0.8;
            }

            i {
                display: block;
                margin-right: 10px;
            }

            .ios-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/ios.png") no-repeat;
                background-size: cover;
            }

            .windows-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/win.png") no-repeat;
                background-size: cover;
            }
        }
    }

    .footer-right {
        height: 144px;
        font-weight: 400;
        font-size: 16px;
        color: #191919;
        display: flex;
        gap: 30px;
    }

    .qrcode-box {
        p {
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            height: 24px;
            margin-bottom: 15px;
            text-align: center;
        }
    }

    .qrcode {
        width: 118px;
        height: 118px;
        background: #ffffff00;
        border: 1px solid #DEDEDE;
        border-radius: 2px;
        text-align: center;

        img {
            width: 100px;
            height: 100px;
            display: block;
            margin-top: 9px;
            margin-left: 9px;
        }
    }

    .copyright {
        width: 100%;
        text-align: center;
        // margin-top: 75px;
        color: #898e96;
        height:48px;
        line-height:48px;
        font-size: 12px;
        position: inherit;
        a{
            color: #898e96;
            text-decoration: none;
        }
    }

}
</style>
