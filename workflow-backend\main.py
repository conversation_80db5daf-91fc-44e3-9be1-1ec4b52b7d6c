#!/usr/bin/env python3
"""
WimTask Designer - Python Engine
主要负责接收前端的工作流定义，转译为Robot Framework代码并执行
"""
import atexit
import json
import multiprocessing
import shutil
import sys
import asyncio
import signal
import argparse
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from uuid import uuid4

import config.env_config as env_config

import yaml
from Browser.playwright import Play<PERSON>
from fastapi import FastAPI, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from loguru import logger
from starlette.staticfiles import StaticFiles

from api.crud import router, task_manager
from api.req_robot import take_task
from api.report_templates import router as report_templates_router
from api.node_data import router as node_data_router
from api.execute_node import router as execute_node_router
from config.version import router as version_manager
from common import setup_logging
from api.node_monitor import app as node_monitor_router
from executor.lifecycle import NodeStatus
from executor.task_executor import task_manager as queued_task_executor
from perpare import delete_prefixed_directories, add_exit_handler, run_exit_handlers
from scheduler.task_runner import task_runner
from utils.path import (
    copy_packaged_files_to_current_dir,
    get_resource_path,
    is_pyinstaller_pack,
)
from servers.websocket import (
    ws_broadcast_proxy,
    start_ws_server,
    stop_ws_server,
)
from cleanfile import clean_temp_dir
import ctypes

# 导入workflow-use路由
try:
    from api.workflow_use_routes import router as workflow_use_router

    WORKFLOW_USE_ROUTES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Workflow-Use路由不可用: {e}")
    WORKFLOW_USE_ROUTES_AVAILABLE = False
    workflow_use_router = None

from transpiler.workflow_transpiler import WorkflowTranspiler
from executor.task_executor import (
    TaskExecutor,
    ExecuteParam,
)
from scheduler.task_scheduler import TaskScheduler
from models.workflow import (
    WorkflowData,
    ExecutionResult,
    ExecutionNode,
    create_execution_id,
)
from services.intelligent_recording_service import intelligent_recording_service

# 使用新的workflow-use录制服务替换原有实现
from services.workflow_use_recording_service import (
    workflow_use_recording_service as browser_recording_service,
)
from utils.process_manager import ProcessManager
from utils.browser_manager import setup_unified_browser

from config import globals
from config import settings
from datetime import datetime
from api.req_robot import upload_task_update
from api.node_monitor import step_call_back
from concurrent.futures import ProcessPoolExecutor


def setup_temp_dir():
    if getattr(sys, "frozen", False):
        # 获取EXE所在目录（执行目录）
        exe_dir = Path(sys.executable).parent.resolve()
        # 在执行目录下创建临时文件夹（用于存放_MEI目录）
        temp_root = exe_dir / "pyi_temp"
        temp_root.mkdir(exist_ok=True)  # 确保目录存在

        # 设置环境变量，强制PyInstaller将_MEI目录生成到temp_root
        os.environ["PYINSTALLER_TEMP_DIR"] = str(temp_root)

        # 记录当前_MEI目录路径（运行时由PyInstaller生成）
        global mei_dir
        mei_dir = Path(sys._MEIPASS).parent


setup_temp_dir()

multiprocessing.freeze_support()
parser = argparse.ArgumentParser(description="FastAPI App CLI Parameters")
parser.add_argument(
    "--authorization",
    type=str,
    required=False,
    default=None,
    # action='store',
    help="authorization",
)
parser.add_argument(
    "--wimtaskApi",
    type=str,
    required=False,
    default=None,
    # action='store',
    help="WimTask API endpoint URL",
)
args = parser.parse_args()


class Config:
    authorization = args.authorization
    wimtask_api = args.wimtaskApi


class ExecuteWorkflowRequest(BaseModel):
    workflow: WorkflowData
    taskId: str
    options: Optional[Dict[str, Any]] = None


class ExecutionResponse(BaseModel):
    success: bool
    execution_id: str
    message: str
    result: Optional[ExecutionResult] = None
    error: Optional[str] = None


class ExecutionNodeResponse(BaseModel):
    success: bool
    execution_id: str
    message: str
    result: Optional[Dict] = None
    error: Optional[str] = None


class RecordingStartRequest(BaseModel):
    browser: str = "chromium"
    output_file: str = "recorded.robot"


class RecordingStopRequest(BaseModel):
    process_id: Optional[str] = None


# 全局组件
transpiler: Optional[WorkflowTranspiler] = None
executor: Optional[TaskExecutor] = None
scheduler: Optional[TaskScheduler] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global transpiler, executor, scheduler

    # 启动时初始化
    logger.info("正在启动WimTask Engine...")

    try:
        # 设置统一浏览器配置
        setup_unified_browser()
        logger.info("统一浏览器配置已设置")

        # 初始化组件
        transpiler = WorkflowTranspiler()
        executor = TaskExecutor(False, False)
        scheduler = TaskScheduler()

        # 进程池
        logger.info(("🚀 初始化进程池..."))
        num_workers = os.cpu_count()
        global process_pool
        # process_pool = ProcessPoolExecutor(max_workers=num_workers)
        # 启动调度器
        await scheduler.start()

        logger.info("WimTask Engine启动成功")
        # 注意：这里的 available_port 将在 main() 函数中设置
        logger.info("API服务已启动")

        yield

    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭WimTask Engine...")

        if scheduler:
            await scheduler.shutdown()

        if executor:
            await executor.cleanup()
        # 关闭进程池
        logger.info("🔴 关闭进程池...")
        # process_pool.shutdown(wait=True, cancel_futures=True)
        logger.info("🛑 进程池已关闭")
        # 清理录制服务
        try:
            intelligent_recording_service.cleanup()
        except:
            pass
        try:
            await browser_recording_service.cleanup()
        except:
            pass
        intelligent_recording_service.cleanup()
        await browser_recording_service.cleanup()

        logger.info("WimTask Engine已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="WimTask Engine",
    description="可视化自动化设计器的Python执行引擎",
    version="1.0.0",
    lifespan=lifespan,
)
# 提供前端静态文件服务
frontend_path = None
if getattr(sys, "frozen", False):
    # 打包后的环境
    frontend_path = Path(sys._MEIPASS) / "web"
    if frontend_path.exists():
        app.mount(
            "/web", StaticFiles(directory=frontend_path, html=True), name="frontend"
        )
        logger.info(f"前端静态文件服务已启用: {frontend_path}")
    else:
        logger.warning(
            f"前端静态文件目录不存在: {frontend_path}，不启用前端静态文件服务"
        )
else:
    logger.info(f"本地运行模式，不启用前端静态文件服务...")


# 根路径重定向到前端页面
@app.get("/")
async def root_redirect():
    """根路径重定向到前端页面"""
    from fastapi.responses import RedirectResponse

    return RedirectResponse(url="/web")


def get_config():
    return Config()


app.include_router(router, prefix="/api")
app.include_router(report_templates_router)
app.include_router(node_data_router)
app.include_router(execute_node_router)
app.include_router(node_monitor_router)
app.include_router(version_manager)

# 添加workflow-use路由
if WORKFLOW_USE_ROUTES_AVAILABLE and workflow_use_router:
    app.include_router(workflow_use_router)
    logger.info("Workflow-Use路由已加载")
else:
    logger.warning("Workflow-Use路由未加载")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://*************:31830",
        "http://saasbate.dlmeasure.com:1801",
        "https://www.dlmeasure.com",
    ],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/cache")
async def read_root(config: Config = Depends(get_config)):
    return {"authorization": config.authorization}


@app.get("/exit")
async def exit():
    try:
        # 关闭时清理
        logger.info("正在关闭WimTask Engine...")

        if scheduler:
            await scheduler.shutdown()

        if executor:
            await executor.cleanup()
        # 关闭进程池
        logger.info("🔴 关闭进程池...")
        run_exit_handlers()
        logger.info("🛑 进程池已关闭")
        # 清理录制服务
        try:
            intelligent_recording_service.cleanup()
        except:
            pass
        try:
            await browser_recording_service.cleanup()
        except:
            pass
        intelligent_recording_service.cleanup()
        await browser_recording_service.cleanup()

        logger.info("WimTask Engine已关闭")
    except Exception as e:
        logger.error(f"关闭失败: {e}")
        pass
    os._exit(0)


@app.get("/")
async def root():
    """根路径，返回引擎状态"""
    return {
        "name": "WimTask Engine",
        "version": "1.0.0",
        "status": "running",
        "components": {
            "transpiler": transpiler is not None,
            "executor": executor is not None,
            "scheduler": scheduler is not None,
        },
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": asyncio.get_event_loop().time()}


@app.post("/execute")
async def execute_workflow_new(
    request: ExecuteWorkflowRequest,
    authorization: Optional[str] = Header(None, alias="Authorization"),
):
    """执行工作流"""
    if not transpiler or not executor:
        raise HTTPException(status_code=500, detail="引擎未正确初始化")

    try:
        logger.info(f"收到工作流执行请求，节点数: {len(request.workflow.nodes)}")

        # 记录接收到的数据
        logger.debug(f"工作流数据: {request.workflow}")
        # 记录最后一次执行的时间
        now_ts = int(datetime.now().timestamp() * 1000)
        upload_result = upload_task_update(
            authorization=authorization,
            task_id=request.taskId,
            last_execute_time=now_ts,
        )
        logger.info(
            f"运行任务 {request.taskId} 执行前信息已上传，结果: {upload_result}"
        )

        # 转译工作流为Robot Framework代码
        robot_code = await transpiler.transpile(request.workflow, request.taskId)
        logger.info("工作流转译完成")
        logger.debug(f"生成的Robot代码长度: {len(robot_code)} 字符")

        execute_param = ExecuteParam(
            robot_code=robot_code,
            task_id=request.taskId,
            token=authorization,
            node_num=len(request.workflow.nodes),
            options={},
        )

        execution_id = create_execution_id()
        execute_param.execution_id = execution_id

        def callback(data: dict):
            ns = NodeStatus(
                node_id=data.get("nodeId", ""),
                node_type=data.get("nodeType", ""),
                node_name=data.get("title", ""),
                history_id=data.get("historyId", ""),
                task_id=data.get("missionId", ""),
                describe=data.get("describe", ""),
            )

            if data.get("end", "0") == "1":
                ns.flow_end(
                    state=data.get("state", ""),
                    msg=data.get("exception", ""),
                    errors=data.get("errors", []),
                )
            else:
                state = data.get("state", "unknown")
                if state == "failed":
                    ns.failed(data.get("exception", ""))
                elif state == "progress":
                    ns.progress(
                        inputs=data.get("input", {}), outputs=data.get("output", {})
                    )
                elif state == "passed":
                    ns.done(
                        inputs=data.get("input", {}), outputs=data.get("output", {})
                    )

            ws_broadcast_proxy.broadcast_data(ns.to_dict())

        execute_param.step_end_callback = callback

        # 执行Robot Framework代码并等待完成
        asyncio.create_task(executor.execute_in_queue(execute_param))

        return {
            "historyId": execute_param.execution_id,
            "taskId": execute_param.task_id,
        }

    except ValueError as e:
        logger.error(f"工作流验证失败: {e}")
        raise e
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        logger.exception("详细错误信息:")
        raise e


@app.post("/execute_old")
async def execute_workflow(
    request: ExecuteWorkflowRequest,
    authorization: Optional[str] = Header(None, alias="Authorization"),
):
    """执行工作流"""
    if not transpiler or not executor:
        raise HTTPException(status_code=500, detail="引擎未正确初始化")

    if not authorization:
        authorization = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTE0Mzg1MjAsImlkIjoiMTg3MjE2MTA0NTg3OTM3MzgyNSIsImp3dElkIjoiOWI0NzNlMDIzNjhmNDY2NjhjNDMwMGI2MjU4ZTk4MjYiLCJ1aWQiOiIxODcyMTYxMDQ1ODc5MzczODI1IiwidGVuYW50SWQiOiI5MDUwIiwiY2lkIjoiOTA1MCIsIm1haW5JZCI6IjE1MDUiLCJhdmF0YXIiOiJodHRwOi8vd2ltLmRsbWVhc3VyZS5jb206MTAwMDEvdW5pd2ltL3VwbG9hZHMvMjAyNC83L3RodW1ibmFpbDc1YTc0NjAwNTAwNzRjN2VhNTAyNTU2ZWUzYmM1OWU2LnBuZyIsIm5hbWUiOiLog6Hlj4zmiJAiLCJhY2NvdW50IjoiMTM3MzY0NDAzNTgiLCJtb2JpbGUiOiIxMzczNjQ0MDM1OCIsInNuIjoiMTM3MzY0NDAzNTgiLCJncm91cCI6IjE4NzIxODE3NzQzNTYyMzQyNDIiLCJ5aGxvTnVtIjoiMzY1MjQiLCJpc0FkbWluIjp0cnVlLCJjaGFubmVsIjoid2ViIiwiY29tcGFueSI6eyJpZCI6IjE4NzIxODE3NzQzNTYyMzQyNDIifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUyMDQzNjIwfQ.WwBPSgNJ0Hlzc3EmmEI9Wd2PkTScndbZfvV5agogS2E"
        # authorization = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA3NjczNjAsImlkIjoiNjI1OGNjYzA1NmE3YjMwN2NjNmVhMmZjIiwiand0SWQiOiJmMDIxODRhMWNhYjY0OTAyYjdkOTQ2MjFlYWIwMzE1YiIsInVpZCI6IjYyNThjY2MwNTZhN2IzMDdjYzZlYTJmYyIsInRlbmFudElkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwiY2lkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwibWFpbklkIjoiMjAyMyIsImF2YXRhciI6Imh0dHBzOi8vaGRrai5kbG1lYXN1cmUuY29tL3VuaXdpbS91cGxvYWRzLzIwMjQvMS90aHVtYm5haWxlYzM0MDk3MTBkMGU0NGE4ODA4ZjZiZDdlMGYzNTc3YS5wbmciLCJuYW1lIjoi546L5p6X5p2wIiwiYWNjb3VudCI6Ijk0MCIsIm1vYmlsZSI6IjE1MDY3MzM0NDY2Iiwic24iOiI5NDAiLCJncm91cCI6Ijc4IiwieWhsb051bSI6Ijc5MTgzIiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNzgifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUxMzcyNDYwfQ.uzSStnbiMzKGc0lfKkFj9xZ3j-q47R4hCwJBGDCEV0A"

    try:
        logger.info(f"收到工作流执行请求，节点数: {len(request.workflow.nodes)}")

        # 记录接收到的数据
        logger.debug(f"工作流数据: {request.workflow}")
        # 记录最后一次执行的时间
        now_ts = int(datetime.now().timestamp() * 1000)
        upload_result = upload_task_update(
            authorization=authorization,
            task_id=request.taskId,
            last_execute_time=now_ts,
        )
        logger.info(
            f"运行任务 {request.taskId} 执行前信息已上传，结果: {upload_result}"
        )

        # 转译工作流为Robot Framework代码
        robot_code = await transpiler.transpile(request.workflow, request.taskId)
        logger.info("工作流转译完成")
        logger.debug(f"生成的Robot代码长度: {len(robot_code)} 字符")

        # 监听节点执行数据
        exc_result = []

        def callback(data: dict):
            exc_result.append(data)

        execute_param = ExecuteParam(
            robot_code=robot_code,
            task_id=request.taskId,
            token=authorization,
            node_num=len(request.workflow.nodes),
            options={},
        )
        execute_param.step_end_callback = callback

        execution_id = create_execution_id()
        execute_param.execution_id = execution_id
        # 执行Robot Framework代码并等待完成
        asyncio.create_task(executor.execute_in_queue(execute_param))

        async def generate():
            # 发送初始化消息
            yield "event: connected\ndata: OK\n\n"
            stops1 = True
            start = int(time.time())
            while stops1:
                # 重新获取
                # 校验返回结果是否被删除
                while exc_result:
                    item = exc_result.pop(0)
                    end = item.get("end", "0")
                    # 获取到结束标识
                    if end == "1":
                        yield f"data: End {json.dumps(item, ensure_ascii=False)}n\n"
                        stops1 = False
                        break
                    else:
                        yield f"data: Event {json.dumps(item, ensure_ascii=False)}\n\n"

                    now = int(time.time())
                    if now > start + 300:
                        yield f"data: End 超时已结束\n\n"
                        stops1 = False
                await asyncio.sleep(2)

        logger.info(f"工作流开始执行，执行ID: {execution_id}")
        return StreamingResponse(
            generate(),
            media_type="text/event-stream",
            headers={"Cache-Control": "no-cache"},
        )
    except ValueError as e:
        logger.error(f"工作流验证失败: {e}")
        raise e
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        logger.exception("详细错误信息:")
        raise e


@app.post("/execute_node")
async def execute_node(
    request: Optional[list[ExecutionNode]],
    authorization: Optional[str] = Header(None, alias="Authorization"),
):
    try:

        task_id = str(uuid4())
        # 转译工作流为Robot Framework代码
        robot_code = await transpiler.transpile_node(request, task_id)
        logger.info("工作流转译完成")
        logger.debug(f"生成的Robot代码长度: {len(robot_code)} 字符")

        # 执行Robot Framework代码并等待完成
        execute_param = ExecuteParam(
            robot_code=robot_code,
            task_id=task_id,
            token=authorization,
            node_num=0,
            options={},
        )
        r = await executor.execute_in_queue(execute_param)

        execution_id = r.get("execution_id", "")

        if r.get("error", "") != "":
            return ExecutionNodeResponse(
                success=False,
                execution_id=execution_id,
                message="提交任务失败",
                error=r.get("error", ""),
            )

        logger.info(f"工作流开始执行，执行ID: {execution_id}")

        # 等待执行完成（最多等待180秒，适应包含网络请求的工作流）
        max_wait = 180
        wait_time = 0

        while wait_time < max_wait:
            execution_result = await executor.get_execution_status(execution_id)

            if execution_result.status in ["success", "failed", "cancelled"]:
                # 执行完成，返回实际结果
                logger.info(f"工作流执行完成，状态: {execution_result.status}")

                # 获取错误信息
                error_message = None
                if execution_result.status == "failed":
                    # 从日志中提取错误信息
                    error_logs = [
                        log for log in execution_result.logs if log.level == "ERROR"
                    ]
                    if error_logs:
                        error_message = error_logs[-1].message  # 获取最后一个错误信息
                    elif execution_result.robot_output:
                        # 如果没有错误日志，尝试从Robot输出中提取
                        if "FAIL" in execution_result.robot_output:
                            lines = execution_result.robot_output.split("\n")
                            for line in lines:
                                if "FAIL" in line:
                                    error_message = line.strip()
                                    break

                    if not error_message:
                        error_message = "执行失败，未知错误"

                return ExecutionNodeResponse(
                    success=(execution_result.status == "success"),
                    execution_id=execution_id,
                    message=f"工作流执行{execution_result.status}",
                    result=execution_result.return_obj,
                    error=error_message,
                )

            # 等待1秒后再检查
            await asyncio.sleep(1)
            wait_time += 1

        # 超时，返回运行中状态
        logger.warning(f"工作流执行超时，执行ID: {execution_id}")
        return ExecutionNodeResponse(
            success=False,
            execution_id=execution_id,
            result=execution_result.return_obj,
            message="工作流执行超时",
            error="执行时间超过180秒",
        )

    except ValueError as e:
        logger.error(f"工作流验证失败: {e}")
        return ExecutionResponse(
            success=False, execution_id="", message="工作流验证失败", error=str(e)
        )
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        logger.exception("详细错误信息:")
        return ExecutionResponse(
            success=False, execution_id="", message="执行失败", error=str(e)
        )


@app.get("/execution/{execution_id}")
async def get_execution_status(execution_id: str):
    """获取执行状态"""
    if not executor:
        raise HTTPException(status_code=500, detail="执行器未初始化")

    try:
        status = await executor.get_execution_status(execution_id)
        return status
    except Exception as e:
        logger.error(f"获取执行状态失败: {e}")
        raise HTTPException(status_code=404, detail="执行记录不存在")


@app.post("/execution/{execution_id}/stop")
async def stop_execution(execution_id: str):
    """停止执行"""
    if not executor:
        raise HTTPException(status_code=500, detail="执行器未初始化")

    try:
        # result = await executor.stop_current_execute_in_queue()
        result = await executor.stop_execution(execution_id)
        return {"success": result, "message": "执行已停止" if result else "停止失败"}
    except Exception as e:
        logger.error(f"停止执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/executions")
async def list_executions():
    """列出所有执行记录"""
    if not executor:
        raise HTTPException(status_code=500, detail="执行器未初始化")

    try:
        executions = await executor.list_executions()
        return {"executions": executions}
    except Exception as e:
        logger.error(f"获取执行列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/transpile")
async def transpile_workflow(workflow: WorkflowData):
    """仅转译工作流，不执行"""
    if not transpiler:
        raise HTTPException(status_code=500, detail="转译器未初始化")

    try:
        robot_code = await transpiler.transpile(workflow)
        return {"success": True, "robot_code": robot_code, "message": "转译成功"}
    except Exception as e:
        logger.error(f"转译失败: {e}")
        return {
            "success": False,
            "robot_code": "",
            "message": "转译失败",
            "error": str(e),
        }


@app.get("/components")
async def list_available_components():
    """列出可用的组件定义"""
    if not transpiler:
        raise HTTPException(status_code=500, detail="转译器未初始化")

    try:
        components = transpiler.get_available_components()
        return {"components": components}
    except Exception as e:
        logger.error(f"获取组件列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 智能录制API ====================


@app.post("/intelligent-recording/start")
async def start_intelligent_recording(request: Dict[str, Any]):
    """开始智能录制（真正的智能录制）"""
    try:
        config = request
        logger.info(f"开始智能录制: {config}")
        result = await browser_recording_service.start_recording_session(config)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动智能录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/intelligent-recording/stop")
async def stop_intelligent_recording(request: Dict[str, Any]):
    """停止智能录制"""
    try:
        session_id = request.get("session_id") or request.get("recording_id")
        logger.info(f"停止智能录制: {session_id}")
        result = await browser_recording_service.stop_recording_session(session_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止智能录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/intelligent-recording/status")
async def get_intelligent_recording_status():
    """获取智能录制状态"""
    try:
        result = browser_recording_service.get_recording_status()
        return result
    except Exception as e:
        logger.error(f"获取智能录制状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/intelligent-recording/operations")
async def get_real_time_operations():
    """获取实时操作列表"""
    try:
        result = browser_recording_service.get_real_time_operations()
        return result
    except Exception as e:
        logger.error(f"获取实时操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/intelligent-recording/pause")
async def pause_intelligent_recording():
    """暂停智能录制"""
    try:
        result = await browser_recording_service.pause_recording()
        return result
    except Exception as e:
        logger.error(f"暂停智能录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/intelligent-recording/resume")
async def resume_intelligent_recording():
    """恢复智能录制"""
    try:
        result = await browser_recording_service.resume_recording()
        return result
    except Exception as e:
        logger.error(f"恢复智能录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# def setup_logging():
#     """设置日志配置"""
#     # 移除默认的logger
#     logger.remove()
#
#     # 添加控制台输出
#     logger.add(
#         sys.stdout,
#         format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
#         level="INFO",
#     )
#
#     # 添加文件输出
#     log_dir = Path("logs")
#     log_dir.mkdir(exist_ok=True)
#
#     logger.add(
#         log_dir / "phoenix_engine.log",
#         format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
#         level="DEBUG",
#         rotation="10 MB",
#         retention="7 days",
#         compression="zip",
#     )


def handle_signal(signum, frame):
    """处理系统信号"""
    logger.info(f"收到信号 {signum}，正在关闭...")
    run_exit_handlers()
    os._exit(0)


def get_dll_path():
    """获取打包后 DLL 的实际路径（临时目录）"""
    if getattr(sys, "frozen", False):
        # 打包后的环境
        return sys._MEIPASS  # PyInstaller 临时目录
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))


# 在调用 COM 组件前设置路径
dll_path = get_dll_path()
path = dll_path + os.pathsep + os.environ["PATH"]
path = dll_path + "\\pywin32_system32" + os.pathsep + os.environ["PATH"]
os.environ["PATH"] = path


async def main():
    """主函数"""
    # 检查是否在子进程中运行
    if ProcessManager.is_subprocess():
        logger.info("检测到子进程环境，跳过服务器启动")
        return

    # 设置日志
    # setup_logging()

    logger.info("启动 WimTask...")

    logger.info(f"wimtask.api={env_config.WIMTASK_API}")

    logger.info(f'临时目录地址={get_resource_path(".")}')

    globals.set_globals(args)

    settings.set_settings("https://www.dlmeasure.com", "", "", "")

    # 全局变量
    if globals.token:
        first_cache = take_task(globals.token)
        for task in first_cache:
            task["authorization"] = globals.token
        try:
            first_cache = [task for task in first_cache if task.get("active") != 0]
            logger.info("开始注册定时任务...")
            task_manager.init_tasks(first_cache)
        except Exception as e:
            logger.error(f"初始化定时任务失败: {e}")
            pass

    # 清理僵尸进程
    ProcessManager.cleanup_zombie_processes()

    asyncio.create_task(queued_task_executor.start())

    # 查找可用端口
    try:
        available_port = ProcessManager.check_port(39876)
        logger.info(f"使用端口: {available_port}")
    except Exception as e:
        logger.error(f"端口分配失败: {e}")
        await exit()

    # clean_temp_files()

    # 注册程序退出时执行的清理函数
    # atexit.register(clean_temp_dir)

    # 启动服务器
    # config = uvicorn.Config(
    #     app,
    #     host="127.0.0.1",
    #     port=available_port,
    #     log_level="info",
    #     access_log=False,  # 使用我们自己的日志
    #     workers=4,  # 进程数（根据CPU核心）
    #     reload=True,  # 开发模式热重载
    #     timeout_keep_alive=30,  # 保持连接超时
    #
    # )
    config = uvicorn.Config(
        app,
        host="127.0.0.1",
        port=available_port,
        log_config=None,  # 禁用 Uvicorn 的默认日志配置
        access_log=False,  # 关闭访问日志
    )

    server = uvicorn.Server(config)
    await server.serve()


def set_playwright_node_path():
    """设置 Playwright 所需的 Node.js 路径"""
    if getattr(sys, "frozen", False):
        # 打包后：获取 PyInstaller 的临时目录 _MEIPASS
        base_dir = sys._MEIPASS
    else:
        # 开发环境：项目根目录
        base_dir = os.path.dirname(os.path.abspath(__file__))

    # 根据系统拼接 Node 路径
    if sys.platform.startswith("win32"):
        node_path = os.path.join(base_dir, "node.exe")
    else:
        node_path = os.path.join(base_dir, "bin", "node")

    # 验证路径有效性
    if not os.path.exists(node_path):
        raise FileNotFoundError(f"Node.js 未找到：{node_path}")

    # 设置环境变量，告诉 Playwright 去哪里找 Node.js
    os.environ["PLAYWRIGHT_NODEJS_PATH"] = node_path

    logger.info(f"设置 Playwright node 路径{os.environ['PLAYWRIGHT_NODEJS_PATH']}")

    copy_packaged_files_to_current_dir(["node.exe"])


def init_config(path):
    # 如果命令行参数提供了 wimtaskApi,而且是http开头 使用命令行参数配置
    if args.wimtaskApi and args.wimtaskApi.startswith("http"):
        env_config.WIMTASK_API = args.wimtaskApi

    # 如果配置文件存在，则读取配置文件，优先用配置文件的配置覆盖命令行参数的配置
    def read_yaml_file(file_path):
        try:
            # 打开并读取 YAML 文件
            with open(file_path, "r", encoding="utf-8") as f:
                # 解析 YAML 内容，返回 Python 对象（字典/列表等）
                data = yaml.safe_load(f)

                if data.get("wimtask_api", None) is not None:
                    env_config.WIMTASK_API = data["wimtask_api"]

                return data
        except FileNotFoundError:
            logger.info(f"配置文件 {file_path} 不存在，不做配置替换")
            return None
        except yaml.YAMLError as e:
            logger.info(f"YAML 解析错误：{e}")
            return None
        except Exception as e:
            logger.info(f"读取文件出错：{e}")
            return None

    if len(path) == 0:
        path = "config.yaml"

    return read_yaml_file(path)


def clean_temp_files():

    if not is_pyinstaller_pack():
        logger.info("不是pyinstaller pack，不用删除临时文件，跳过")
        return

    temp_dir = get_resource_path(".")

    skip_dirs: List[Path] = [Path(temp_dir)]

    delete_prefixed_directories(Path(temp_dir).parent, "_MEI", skip_dirs)


def playwright_node_patch():

    def disabled_ensure_node_dependencies(self):
        """被替换后的空实现"""
        # self.library.logger.info("已通过Monkey Patch禁用依赖检查")

    #     def start_playwright(self) -> Optional[subprocess.Popen]:
    #         existing_port = self.port or os.environ.get("ROBOT_FRAMEWORK_BROWSER_NODE_PORT")
    #         if existing_port is not None:
    #             self.port = existing_port
    #             logger.info(
    #                 f"ROBOT_FRAMEWORK_BROWSER_NODE_PORT {existing_port} defined in env skipping Browser process start"
    #             )
    #             return None
    #         current_dir = Path(inspect.getfile(Playwright))
    #         workdir = current_dir / "wrapper"
    #         playwright_script = workdir / "index.js"
    #         if self.playwright_log:
    #             logfile = self.playwright_log.open("w")
    #         else:
    #             logfile = Path(os.devnull).open("w")  # noqa: SIM115
    #         port = str(find_free_port())
    #         if self.enable_playwright_debug == PlaywrightLogTypes.playwright:
    #             os.environ["DEBUG"] = "pw:api"
    #         logger.info(f"Starting Browser process {playwright_script} using port {port}")
    #         self.port = port
    #         node_args = ["node"]
    #         node_debug_options = os.environ.get(
    #             "ROBOT_FRAMEWORK_BROWSER_NODE_DEBUG_OPTIONS"
    #         )
    #         if node_debug_options:
    #             node_args.extend(node_debug_options.split(","))
    #         node_args.append(str(playwright_script))
    #         node_args.append(port)
    #         if not os.environ.get("PLAYWRIGHT_BROWSERS_PATH"):
    #             os.environ["PLAYWRIGHT_BROWSERS_PATH"] = "0"
    #         logger.info(f"Node startup parameters: {node_args}")
    #
    #         if not os.environ.get("PLAYWRIGHT_BROWSERS_PATH"):
    #             os.environ["PLAYWRIGHT_BROWSERS_PATH"] = "0"
    #
    #         logger.trace(f"Node startup parameters: {node_args}")
    #
    #         # 关键修改：添加窗口隐藏配置（仅Windows系统）
    #         startupinfo = None
    #         if os.name == "nt":  # 判断是否为Windows系统
    #             startupinfo = subprocess.STARTUPINFO()
    #             # 设置隐藏窗口的标志
    #             startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    #             startupinfo.wShowWindow = subprocess.SW_HIDE  # 隐藏窗口
    #
    #         return subprocess.Popen(
    #             node_args,
    #             shell=False,
    #             cwd=workdir,
    #             env=os.environ,
    #             stdin=subprocess.DEVNULL,  # 忽略输入
    #             stdout=subprocess.DEVNULL,  # 忽略标准输出
    #             stderr=subprocess.DEVNULL,  # 忽略错误输出
    #             startupinfo=startupinfo,  # 应用窗口配置
    #         )
    #
    # 在创建Playwright实例前，替换原方法
    Playwright.ensure_node_dependencies = disabled_ensure_node_dependencies


# Playwright.start_playwright = start_playwright


def clean_temp_dir():
    # 判断是否为 PyInstaller 打包的程序
    if getattr(sys, "frozen", False) and hasattr(sys, "_MEIPASS"):
        temp_dir = sys._MEIPASS
        try:
            # 尝试删除临时目录（忽略正在使用的文件）
            shutil.rmtree(temp_dir, ignore_errors=True)
            logger.info(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            logger.info(f"清理临时目录失败: {e}")


def clean_temp_folder():
    # 检查是否是打包后的环境
    if hasattr(sys, "_MEIPASS"):
        temp_dir = sys._MEIPASS
        try:
            # 禁用系统错误弹框（仅 Windows 有效）
            if sys.platform == "win32":
                ctypes.windll.kernel32.SetErrorMode(0x0002)  # SEM_FAILCRITICALERRORS

            clean_temp_dir(temp_dir)
            # 尝试删除临时目录及其内容
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
                print(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")
        finally:
            # 恢复系统错误模式（可选）
            if sys.platform == "win32":
                ctypes.windll.kernel32.SetErrorMode(0x0000)


# 注册正常退出回调（程序自然结束时触发）
atexit.register(clean_temp_folder)


# 捕获强制终止信号（如Ctrl+C、任务管理器结束进程）
def handle_signal1(signum, frame):
    print(f"⚠️ 收到终止信号 {signum}，开始清理临时文件...")
    clean_temp_folder()
    sys.exit(1)


# 注册常见终止信号
for sig in (signal.SIGINT, signal.SIGTERM, signal.SIGABRT):
    signal.signal(sig, handle_signal1)

if __name__ == "__main__":
    init_config("config.yaml")
    setup_logging()
    start_ws_server()
    add_exit_handler("关闭ws", stop_ws_server)

    playwright_node_patch()

    set_playwright_node_path()

    def handler(data):
        ws_broadcast_proxy.broadcast_data(data)

    task_runner.start(handler)
    add_exit_handler("关闭st", task_runner.stop)
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)

    asyncio.run(main())
