<template>
  <div class="register-box" v-loading.fullscreen.lock="fullscreenLoading">
    <img 
      src="../../assets/images/login/popQrCode.png" 
      alt="" 
      class="login-img" 
      @click="onSwitchLoginType('1-2')" 
    />
    <div class="register__header">
      <p>注册</p>
      <div>
        <span>已有账号？</span>
        <span class="login-btn" @click="onSwitchLoginType('1-1')">去登录</span>
      </div>
    </div>
    <!-- 错误信息 -->
    <div class="error-info" v-if="model.err_info">
      <el-icon><Remove /></el-icon>{{ model.err_info }}
    </div>
    <div class="register-input-group">
      <el-form 
        ref="formRef" 
        label-position="top" 
        :model="model" 
        hide-required-asterisk 
        :show-message="false" 
        :validate-on-rule-change="false"
      >
        <el-form-item label="企业" prop="tenantName">
          <el-autocomplete 
            v-model="model.tenantName" 
            name="tenantName" 
            value-key="tenantName" 
            :fetch-suggestions="querySearchAsync" 
            placeholder="请输入企业名" 
            @select="handleSelect" 
            @blur="blur"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="用户名" prop="username" required>
          <el-input 
            v-model="model.username" 
            clearable 
            placeholder="请输入用户名"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile" required>
          <el-input 
            v-model.trim="model.mobile" 
            clearable 
            placeholder="请输入手机号" 
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" required>
          <el-tooltip class="item" effect="dark" placement="right-end">
            <template #content>
              <div v-html="pwdRuleTips"></div>
            </template>
            <el-input 
              v-model="model.password" 
              placeholder="请输入密码" 
              show-password 
              autocomplete="new-password"
            ></el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="确认密码" prop="rpwd" required>
          <el-tooltip class="item" effect="dark" placement="right-end">
            <template #content>
              <div v-html="pwdRuleTips"></div>
            </template>
            <el-input 
              v-model="model.rpwd" 
              placeholder="请输入确认密码" 
              show-password
            ></el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="短信验证码" prop="code" required>
          <div class="send-code">
            <el-input 
              placeholder="请输入验证码" 
              v-model.trim="model.code"
            ></el-input>
            <button 
              type="button" 
              class="send-btn" 
              @click="sendCode" 
              :disabled="isSended"
            >
              {{ text }}
            </button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="register-frame-button">
      <el-button type="primary" @click="onRegister">注册</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, inject } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { Remove } from '@element-plus/icons-vue';
import JSEncrypt from 'jsencrypt';
import { checkIsPhone, timer } from '@/utils/validate.js';
import systemApi from "@/api/system";
import utils from '@/utils/utils';

// 类型定义
interface Configs {
  pwdRuleType?: string;
  pwdRule?: string;
  pwdMinLength?: number;
  weakPasswordSetting?: string;
  [key: string]: any;
}

interface Props {
  configs?: Configs;
}

interface ModelType {
  tenantName: string;
  tenantId?: string | null;
  username: string | null;
  mobile: string | null;
  password: string | null;
  rpwd: string | null;
  code: string | null;
  validShow: boolean;
  err_info: string | null;
}

interface TenantItem {
  id: string;
  tenantName: string;
  [key: string]: any;
}

interface VerificationCodeResponse {
  Code: number;
  Message?: string;
}

interface RegisterResponse {
  Code: number;
  Message?: string;
  Response?: {
    token: string;
    expire: string;
    refreshToken: string;
    tenantId: string;
  };
}

interface SystemApi {
  verificationCode: (
    params: { mobile: string; type: string },
    options: { meta: { isData: boolean } }
  ) => Promise<VerificationCodeResponse>;
  getDetailByName: (name: string) => Promise<TenantItem[]>;
}

interface DlyApi {
  register: (
    params: { data: string },
    options: { meta: { isData: boolean } }
  ) => Promise<RegisterResponse>;
}

interface ParentProvide {
  getEncryptKey: () => Promise<void>;
  publicKey: string;
}

// 声明全局HD对象
declare global {
  interface Window {
    HD: {
      base64: {
        encode: (str: string) => string;
      };
    };
  }
}

// 接收Props
const props = withDefaults(defineProps<Props>(), {
  configs: () => ({})
});

// 注入依赖

// 响应式状态
const formRef = ref<any>(null);
const fullscreenLoading = ref<boolean>(false);
const isSended = ref<boolean>(false);
const time = ref<number>(60);
const text = ref<string>('获取验证码');
const publicKey = ref<string>('');

const model = reactive<ModelType>({
  tenantName: '',
  tenantId: null,
  username: null,
  mobile: null,
  password: null,
  rpwd: null,
  code: null,
  validShow: false,
  err_info: null
});

// 计算属性 - 密码规则提示
const pwdRuleTips = computed<string>(() => {
  let str = `新密码需要符合以下规则：<br/>1.`;
  switch (props.configs.pwdRuleType) {
    case '1':
      str += '字母大写+字母小写+数字组合';
      break;
    case '2':
      str += '字母+数字+特殊字符组合';
      break;
    case '3':
      str += '字母+数字组合';
      break;
    case '4':
      str += props.configs.pwdRule 
        ? `满足正则策略：${props.configs.pwdRule}` 
        : '新密码不能为空';
      break;
    case '5':
      str += '字母大写+字母小写+特殊字符+数字';
      break;
    default:
      str += '新密码不能为空';
      break;
  }
  str += `<br/>2.密码长度不能小于${props.configs.pwdMinLength || 6}`;
  
  if (props.configs.weakPasswordSetting) {
    str += `<br/>3.不能设置的密码名单有：${props.configs.weakPasswordSetting}`;
  }
  return str;
});

// 监听configs变化
watch(
  () => props.configs,
  (v) => {
    // 原逻辑为空，保持兼容
  },
  { deep: true, immediate: true }
);

// 生命周期 - 组件卸载前清理定时器
onBeforeUnmount(() => {
  // if (timer.value) {
  //   clearTimeout(timer.value);
  //   timer.value = null;
  // }
});

onMounted(() => {
  // 原mounted逻辑为空
});

// 方法 - 验证确认密码
const checkRpwd = (value: string): string | null => {
  if (value === '') {
    return '请再次输入密码';
  } else if (value !== model.password) {
    return '两次输入密码不一致!';
  } else {
    return null;
  }
};

// 方法 - 发送验证码
const sendCode = async () => {
  if (isSended.value) return;
  
  if (!model.mobile) {
    model.err_info = '请先输入手机号';
    return;
  }
  
  try {
    const promise = handleSendCode();
    if (promise) {
      await promise;
      ElMessage({
        type: 'success',
        message: '验证码已发送，请注意查收'
      });
      isSended.value = true;
      letTimer();
    }
  } catch (err) {
    ElMessage.error(
      err instanceof Error ? err.message : (err as string) || '操作失败，请联系管理员'
    );
  }
};

// 方法 - 验证码倒计时
const letTimer = () => {
  let times = time.value;
  
  timer(
    times,
    (s: number) => {
      text.value = `${s}秒后重发`;
      time.value = s;
      window.localStorage.setItem('UniWimSmsTime', s.toString());
    },
    () => {
      const isRight = model.mobile ? checkIsPhone(model.mobile) : false;
      if (!isRight) {
        model.err_info = '手机号有误';
      }
      text.value = '重新获取';
      isSended.value = false;
      time.value = 60;
      window.localStorage.removeItem('UniWimSmsTime');
    }
  );
};

// 方法 - 处理验证码发送逻辑
const handleSendCode = (): Promise<void> | undefined => {
  model.err_info = null;
  
  // 验证手机号
  const isRight = model.mobile ? checkIsPhone(model.mobile) : false;
  if (!isRight) {
    model.err_info = '手机号有误';
    return;
  }
  
  return new Promise((resolve, reject) => {
    (systemApi as unknown as SystemApi)
      .verificationCode(
        {
          mobile: model.mobile,
          type: 'REGISTER_CODE'
        },
        {
          meta: {
            isData: false
          }
        }
      )
      .then((res) => {
        if (res.Code === 0) {
          resolve();
        } else {
          reject(res.Message);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

// 方法 - 企业名搜索
const querySearchAsync = (queryString: string, cb: (data: TenantItem[]) => void) => {
  model.tenantId = null;
  ;
  if (model.tenantName) {
    (systemApi as unknown as SystemApi)
      .getDetailByName(model.tenantName)
      .then((res) => {
        const data = res || [];
        cb(data);
      });
  } else {
    cb([]);
  }
};

// 方法 - 选择企业
const handleSelect = (item: TenantItem) => {
  if (item) {
    model.tenantId = item.id;
  } else {
    model.tenantName = '';
    model.tenantId = '';
  }
};

// 方法 - 企业名输入框失焦
const blur = () => {
  if (!model.tenantId) {
    model.tenantName = '';
  }
};

function getEncryptKey(): Promise<void> {
  return new Promise((resolve, reject) => {
    systemApi.getEncryptKey().then((res) => {
      publicKey.value = res.publicKey || '';
      if (res.publicKey) {
        sessionStorage.setItem('publicKey', res.publicKey);
        resolve();
      } else {
        reject(new Error('获取加密密钥失败'));
      }
    }).catch(reject);
  });
}

// 方法 - 注册提交
const onRegister = async () => {
  model.err_info = null;
  
  // 基础验证
  if (!model.username) {
    model.err_info = '请输入用户名';
    return;
  }
  
  if (!model.mobile) {
    model.err_info = '请输入手机号';
    return;
  }
  
  const isRight = checkIsPhone(model.mobile);
  if (!isRight) {
    model.err_info = '手机号有误';
    return;
  }
  
  if (!model.password) {
    model.err_info = '请输入密码';
    return;
  }
  
  if (!model.rpwd) {
    model.err_info = '请输入确认密码';
    return;
  }
  
  const pwdError = checkRpwd(model.rpwd);
  if (pwdError) {
    model.err_info = pwdError;
    return;
  }
  
  if (!model.code) {
    model.err_info = '请输入手机验证码';
    return;
  }
  
  // 表单验证
  const isValid = await formRef.value.validate();
  if (!isValid) {
    model.err_info = '请检查必填项';
    return;
  }
  
  try {
    fullscreenLoading.value = true;
    
    // 获取加密公钥（从父组件）
    await getEncryptKey();
    
    // 密码加密
    const encryptStr = new JSEncrypt();
    encryptStr.setPublicKey(publicKey.value);
    const encryptPwd = encryptStr.encrypt(model.password || '');
    const pwd = window.HD.base64.encode(encryptPwd || '');
    
    // 准备注册参数
    const params = {
      tenantId: model.tenantId,
      username: model.username,
      mobile: model.mobile,
      password: pwd,
      code: model.code
    };
    
    const body = {
      data: window.HD.base64.encode(JSON.stringify(params))
    };
    
    // 调用注册接口
    const res = await systemApi.register(body, {
      meta: { isData: false }
    });
    
    if (!res) {
      throw new Error('注册失败，请重试！');
    }
    
    if (res.Code === 0 && res.Response) {
      model.err_info = null;
      ElMessage({
        showClose: true,
        type: 'success',
        message: '注册成功'
      });
      
      // 存储登录信息并跳转
      window.localStorage.setItem('UniWimAuthorization', res.Response.token);
      window.localStorage.setItem('UniWimExpire', res.Response.expire);
      window.localStorage.setItem('UniWimRefreshToken', res.Response.refreshToken);
      window.localStorage.setItem('UniWimTenantId', res.Response.tenantId);
      
      location.replace(`${import.meta.env.BASE_URL}index.html`);
    } else {
      model.err_info = res.Message || '注册失败';
    }
  } catch (err) {
    model.err_info = err instanceof Error ? err.message : (err as string);
  } finally {
    fullscreenLoading.value = false;
  }
};

// 方法 - 切换登录类型
const onSwitchLoginType = (type: string) => {
  emit('onSwitchLoginType', type);
};

// 定义事件
const emit = defineEmits<{
  (e: 'onSwitchLoginType', type: string): void;
}>();
</script>

<style scoped lang="less">
.register-box {
  height: 100%;
  padding: 35px 28px 40px 32px;
  position: relative;

  .login-img {
    width: 60px;
    height: 60px;
    cursor: pointer;
    position: absolute;
    right: 8px;
    top: 8px;
  }

  .register__header {
    display: block;
    width: 100%;
    margin-bottom: 30px;

    p {
      font-weight: 400;
      font-size: 24px;
      color: #333333;
      letter-spacing: 0;
      line-height: 33px;
      margin-bottom: 10px;
    }

    .login-btn {
      color: #0054d2;
      cursor: pointer;
    }
  }

  .error-info {
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 28px;
    background: #fcddd9;
    border: 1px solid #db3b35;
    border-radius: 2px;
    font-weight: 400;
    font-size: 14px;
    color: #db3b35;
    letter-spacing: 0;
    padding: 8px 15px;
    box-sizing: border-box;
    margin-bottom: 20px;

    i {
      margin-right: 13px;
    }
  }

  .register-input-group {
    .el-form-item {
      margin-bottom: 20px !important;

      .el-form-item__label {
        font-size: 14px;
        color: #333333;
        line-height: 20px;

        &::before {
          display: none;
        }
      }

      .el-form-item__content {
        .el-select {
          width: 100%;
        }
      }
    }
  }

  .register-frame-button {
    width: 342px;
    height: 43px;
    margin-top: 30px;

    .el-button {
      font-size: 18px;
      font-weight: 400;
      width: 100%;
      height: 100%;
      border-radius: 2px;
      overflow: hidden;
      background: #0054d2;
      border-color: #0054d2;
    }
  }

  .error {
    color: Red;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
    transition: all 0.2s ease;
  }

  :deep(.el-input__wrapper) {
    height: 44px;
    line-height: 44px;
    border: 1px solid #dadada;
    border-radius: 2px;
    background: #fff !important;
    box-shadow: none;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      color: #bfbfbf;
      letter-spacing: 0;
    }

    &:hover {
      border: 1px solid #0862ea !important;
    }
  }

  .send-code {
    width: 100%;
    height: 100%;
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    display: flex;
    border: 1px solid #dadada;
    border-radius: 2px;
    background: #fff;

    &:hover {
      border: 1px solid #0862ea;
    }

    :deep(.el-input__wrapper) {
      border: 0 !important;
      box-shadow: none;

      &:hover {
        border: 0 !important;
      }
    }

    .el-input {
      flex: 1;
      height: 42px;
      overflow: hidden;
    }

    .send-btn {
      border: 0;
      height: 100%;
      padding: 0 16px;
      position: relative;
      cursor: pointer;
      line-height: 44px;
      font-weight: 400;
      font-size: 14px;
      color: #0054d2;
      letter-spacing: 0;
      background-color: transparent;

      &:after {
        content: '';
        position: absolute;
        left: 0;
        top: 12px;
        height: 20px;
        width: 1px;
        background: #dadada;
      }

      &:disabled {
        color: #999;
        cursor: not-allowed;
      }
    }
  }
}
</style>