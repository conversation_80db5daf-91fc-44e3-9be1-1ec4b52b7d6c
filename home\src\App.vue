<template>
  <div id="app" class="app-container" @scroll="handleScroll">
    <div
      class="banner-top"
      :class="{'is-from-package': isUniwimPcV1 }"
    >
      <div class="banner-left">
        <img
          style="width: auto; height: 44px; cursor: pointer"
          src="./assets/images/admin/logoV2.png"
          alt="WimTask logo"
        />
        <div class="tabs-box">
          <el-tabs v-model="activeName" class="tabs" @tab-click="handleTab">
            <template v-if="route.name !== '/mineService' && route.name !== '/mineInstruction'">
              <el-tab-pane label="产品介绍" name="product"></el-tab-pane>
              <el-tab-pane label="服务包市场" name="market"></el-tab-pane>
              <el-tab-pane label="学习中心" name="study"></el-tab-pane>
              <el-tab-pane label="联系我们" name="comment"></el-tab-pane>
            </template>
            <template v-else>
              <el-tab-pane label="我的服务包" name="mineService"></el-tab-pane>
              <el-tab-pane label="我的指令" name="mineInstruction"></el-tab-pane>
            </template>
          </el-tabs>
        </div>
      </div>
      <div class="banner-right">
        <!-- <div class="logo-tel"> -->
          <!-- <span @click="jumpTo('/comment?from=首页')">联系我们</span>  -->
          <!-- <img
            style="width: 16px; height: 16px; margin-right: 15px"
            src="./assets/images/homepage/tel.png"
            alt="电话图标"
          />
          热线：0573-82875638 -->
        <!-- </div> -->
        <div class="download-operation" v-if="!isUniwimPc"  @click="jumpTo('/download')">
          <img  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAhpJREFUSEvt1surTWEYBvDfSYSiDJRyS0SiXJJIZm4hMXArKYRMMDI04A8QA4UkuYSRAQMhE0lCiihKQsREkU5yXa/eo90+a+9z1jl7YLDfWrXW/t7ved7b93y7Q3WbjpO5bRueVIHoqOKcvptxKt+34nQVjL4Q7sCxJNmJ423C2gq0S1o2D+2haXpK/quhiWCWYXlK2aMMvVEPQ/K24Dqu4XdZqs0ynIhLmI2PWIEH2F6nNCcwC1cxChHYOryoSjgSZ7A0N95DSNncOsK7+T0v/W5hIz40IhyCAH+LX3VOo3EFMxDVeIXL2Jt+h4qMVhcCPiG/n2ElXtbhDMC4qFSAHMRiHMH5kqgWZAbTcu0LhuX7ZwzP9yjhLtwswdiEPbEWhN8wKBsdtQ+QepuUmU5pMPOR+So8LlmPgGIWojU/grBrmm5ksz81AF2Co0VfY5hq7XVGH6UusxFJuCgWqxCG/8zs4fhEfoe1uNOALH7uF2EArEEMy0Dsw7kmZC0hDJChWZ2vPZC1jLAXPP9cupW0E4NxO4fmfRW0XviG+sSULkRnDM1TTM2Dv76HAegFfjeX+QX+xUKPxwZXEO7PJ9TgPnYjRv17X9Br9sTZDnU5XJzhOfiJA0EYCnIBofZhb/JpBWFkNSZx4w/zhq7bIm6Es4W0TS6kLjJtpUVmzxHy9rD2egqBjh7GE1FFSfpjUaG4EKJ6MTR/79M/58GBGlB0ATEAAAAASUVORK5CYII=" alt="" class="img-item">
          <span>下载</span>
        </div>
        <div class="menus" :class="[isUniwimPc?'isuniwim-pc':'']" v-if="currentUser&&isUniwimPc">
          <div class="menu-item personal-workbench" @click="jumpToPage('manager')" v-if="currentUser">前往工作台</div>
          <!-- <div class="menu-item" @click="openDetail('xiangshe')">文档</div> -->
        </div>
        <!-- 用户信息 -->
        <div class="user-info" v-if="!isUniwimPc">
          <user-set v-if="currentUser" :isMultiTenancy="isMultiTenancy" :defaultAvatar="defaultAvatar" />
          <el-button  type="primary" class="login-button" plain size="mini" @click="onLogin" v-if="!currentUser&&configs">登录</el-button>
        </div>
        
      </div>
    </div>

    <el-dialog v-model="loginVisible" :show-close="false" ref="loginPop" class="login-pop" width="400px" :close-on-click-modal="false">
        <img src="./assets/images/close.png" class="close_item" @click="closeLoginPop" />
        <saasLogin :configs="configs" :loginPop="true" v-if="loginVisible"></saasLogin>
    </el-dialog>
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import {computed,ref,onMounted} from 'vue';
import { RouterView } from 'vue-router'
import { useRoute, useRouter } from 'vue-router'
import saasLogin from '@/views/saasLogin.vue';
import systemApi from "@/api/system";
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";
import userSet from '@/components/layout/userset.vue'
import defaultAvatar from '@/assets/images/login/avatar.png'

const userStore = useUserStore()
const loginStore = useLoginStore()
const route = useRoute()
const router = useRouter()
import utils from '@/utils/utils'
const loading = document.getElementById('loading');
const isFixed = ref(true)
if (loading) {
    loading.classList.add('loading-hidden');
    loading.style.display = 'none';
    loading.remove();
}

// 定义计算属性，和 Vue2 中 computed 配置项里的函数作用一致
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc' || utils.GetQueryString('uniwim') == 'pc' || utils.GetQueryString('uniwim','hash') == 'pc' || window.top !== window.self
})
const isUniwimPcV1 = computed(() => {
  return route.query.uniwim === 'pc' || utils.GetQueryString('uniwim') == 'pc' || utils.GetQueryString('uniwim','hash') == 'pc'
})
const isMultiTenancy = computed(()=>{
  return window.top.location.pathname.indexOf('admin.html') != -1;
})
const configs = computed(() => {
    return userStore.configs
})
const workspaceUrl = computed(() => {
    return userStore.aiConfigs?userStore.aiConfigs.find((item:any)=>item.configKey == 'workspaceUrl')?.value:'https://www.dlmeasure.com/oauth.html?actionType=changeToken&pagePath=https%3A%2F%2Fwww.dlmeasure.com%2Fextends%2FWimTask%2Findex.html%23%2Fmanager'
})
const currentUser = computed(() => {
    return userStore.userInfo
})
const loginVisible = computed(() => {
    return loginStore.loginPopVisible
})
const activeName = computed(() => {
  let name = route.path.replace('/','')
  if(route.path.indexOf('serviceMarket')>-1){
    name = 'market'
  }else if(route.path.indexOf('study')>-1){
    name = 'study'
  }else if(route.path.indexOf('homepage')>-1){
    name = 'product'
  }else if(route.path.indexOf('/mine/instruction')>-1){
    name = 'mineInstruction'
  }else if(route.path.indexOf('/mine/service')>-1){
    name = 'mineService'
  }
  return name
})
const isFromPackage = computed(() => {
  return route.query.from === 'package' || utils.GetQueryString('from') == 'package'
})
const token = computed(() => {
  return utils.getUniwaterUtoken()
})


const closeLoginPop = ()=>{
  loginStore.LOGIN_POP_VISIBLE(false)
}
const handleScroll = (event: any) => {
  if (event.target.scrollTop == 0) {
    isFixed.value = true
  } else {
    isFixed.value = false
  }
}

const onLogin = ()=> {
  ;
  loginStore.LOGIN_POP_VISIBLE(true)
}

const handleTab = (type: string) => {
  let path = ''
  if (type.paneName === 'product') {
    path = '/homepage'
  } else if (type.paneName === 'market') {
    path = '/serviceMarket'
  } else if (type.paneName === 'study') {
    path = '/study'
  }else if (type.paneName === 'mineInstruction') {
    path = '/mine/instruction'
  }else if (type.paneName === 'mineService') {
    path = '/mine/service'
  }else if (type.paneName === 'comment') {
    path = 'comment'
  }
  delete route.query.serviceId;
  route.query.from = '首页'
  router.push({
    path: path,
    query: route.query,
  })
}
const jumpTo = (url:string)=>{
  let path = utils.generateUrlWithQuery(
    url
  )
  router.push(path)
}
const jumpToPage = (path: string) => {
  // router.push({
  //     name: path,
  //     query: route.query
  // })
  // let url = utils.generateUrlWithQuery(
  //   'https://www.dlmeasure.com/extends/WimTask/index.html#/manager',
  //   route.query,
  // )
  // window.open(url)
  // let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
  let token = utils.getUniwaterUtoken()
  if(token){
      route.query.uniwater_utoken = token
  }
  // let url = utils.generateUrlWithQuery("https://www.dlmeasure.com/extends/WimTask/index.html#/manager",route.query)
  let url = 'https://www.dlmeasure.com/oauth.html?actionType=changeToken&pagePath=https%3A%2F%2Fwww.dlmeasure.com%2Fextends%2FWimTask%2Findex.html%23%2Fmanager&uniwater_utoken='+token
  // window.open(url)
  if(workspaceUrl.value){
    url = utils.appendUrlParams(workspaceUrl.value,{
      uniwater_utoken:token
    })
  }
  window.location.href = url
}
onMounted(async()=>{
  if (token.value) {
      await systemApi.initUserInfo();
  }
  if(isUniwimPc){
    await systemApi.getAiConfig();
  }
  await systemApi.initConfigs()
})
</script>
<style scoped lang="less">
.close_item {
    width: 30px;
    height: 30px;
    position: absolute;
    top: -26px;
    right: -26px;
    cursor: pointer;
}
:deep(.el-overlay-dialog){
  display: flex;
  align-items: center;
}
.banner-top {
    width: 100%;
    // width:1400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: #fff;
    // height: 80px;
    margin: 0 auto;
    position: fixed;
    top: 0;
    z-index: 10;
    transition: background-color 0.3s ease; // 添加过渡效果
    box-shadow: 0 1px 30px 0 #4a5c7a29;
    &.banner-top-fixed {
      background: transparent;
      box-shadow: none;
    }
    &.is-from-package {
      top: 48px;
    }

    .banner-left {
      display: flex;
      // height:58px;
      // align-items: center;
      img {
        margin-top: 10px;
      }
      .tabs-box {
        display: flex;
        align-items: center;
        height: 64px;
      }
      .el-tabs {
        height: 64px;
        line-height: 64px;
        margin-left: 50px;
      }
      /deep/.el-tabs__header {
        margin: 0;
      }
      /deep/.el-tabs__nav-wrap::after {
        background: transparent;
      }
      /deep/.el-tabs__item {
        font-size: 16px;
        height:64px;
      }
      /deep/.el-tabs__item.is-active {
        font-weight: 700;
        font-size: 16px;
      }
    }
    .banner-right {
      display: flex;
      font-size: 16px;
      align-items: center;
      .logo-tel {
        display: flex;
        align-items: center;
        margin-right:20px;
        span{
          cursor: pointer;
          // margin-right:16px;
        }
      }
      .user-info{
        // margin-left:16px;
      }
      .download-operation{
          background: #f5f5f5;
          display: inline-block;
          color: #222;
          text-align: center;
          display: flex;
          align-items: center;
          height: 32px;
          line-height: 32px;
          border-radius: 6px;
          padding: 0 12px;
          font-weight: 400;
          font-size: 12px;
          cursor: pointer;
          margin-right: 20px;
          img{
            width: 14px;
            height: 14px;
            margin-right: 8px;
          }
      }
      .menus {
        display: flex;
        margin-right: 20px;
        &.isuniwim-pc{
          margin-right:0;
        }
        .menu-item {
          margin-right: 30px;
          cursor: pointer;
          &:last-of-type {
            margin-right: 0;
          }
        }
        .personal-workbench{
          height: 32px;
          line-height: 32px;
          border-radius: 6px;
          padding: 0 12px;
          font-weight: 400;
          font-size: 12px;
          cursor: pointer;
          margin-right: 20px;
          color: #fff;
          background-image: linear-gradient(90deg, #0054c9, #3686ff);
        }
      }
    }
  }
</style>
<style>
body .login-pop.el-dialog {
    padding: 0 !important;
    margin:0 auto;
}
.login-pop .el-dialog__body {
    padding: 0 !important;
}

.login-pop .el-dialog__header{
  display:none
}
.login-pop.el-dialog .el-dialog__body {
    padding: 16px;
    font-size: 12px;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 主题定制 */
:root {

}
</style>
