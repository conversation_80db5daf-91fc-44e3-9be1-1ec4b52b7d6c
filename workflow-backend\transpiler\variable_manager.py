"""
Variable Management System for Phoenix RPA
Handles variable storage, substitution, and scope management
"""

import re
import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum


logger = logging.getLogger(__name__)


class VariableScope(Enum):
    """Variable scope types"""
    LOCAL = "local"
    GLOBAL = "global"
    WORKFLOW = "workflow"
    ENVIRONMENT = "environment"


@dataclass
class Variable:
    """Variable definition"""
    name: str
    value: Any
    type: str = "string"
    scope: VariableScope = VariableScope.LOCAL
    source_node_id: Optional[str] = None
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


@dataclass
class VariableContext:
    """Variable context for workflow execution"""
    variables: Dict[str, Variable] = field(default_factory=dict)
    global_variables: Dict[str, Variable] = field(default_factory=dict)
    workflow_variables: Dict[str, Variable] = field(default_factory=dict)

    def get_variable(self, name: str) -> Optional[Variable]:
        """Get variable by name, checking scope hierarchy"""
        # Check local variables first
        if name in self.variables:
            return self.variables[name]

        # Check workflow variables
        if name in self.workflow_variables:
            return self.workflow_variables[name]

        # Check global variables
        if name in self.global_variables:
            return self.global_variables[name]

        return None

    def set_variable(self, variable: Variable):
        """Set variable in appropriate scope"""
        if variable.scope == VariableScope.GLOBAL:
            self.global_variables[variable.name] = variable
        elif variable.scope == VariableScope.WORKFLOW:
            self.workflow_variables[variable.name] = variable
        else:
            self.variables[variable.name] = variable

    def get_all_variables(self) -> Dict[str, Variable]:
        """Get all variables from all scopes"""
        all_vars = {}
        all_vars.update(self.global_variables)
        all_vars.update(self.workflow_variables)
        all_vars.update(self.variables)
        return all_vars


class VariableManager:
    """Manages variables throughout workflow execution"""

    def __init__(self):
        self.context = VariableContext()
        self.variable_pattern = re.compile(r'\$\{([a-zA-Z_][a-zA-Z0-9_]*)\}')

    def substitute_variables(self, text: str) -> str:
        """Substitute variable references in text with actual values"""
        if not isinstance(text, str):
            return text

        def replace_var(match):
            var_name = match.group(1)
            variable = self.context.get_variable(var_name)

            if variable is None:
                logger.warning(f"Variable '{var_name}' not found, keeping original reference")
                return match.group(0)  # Return original ${var_name}

            # Convert value to string for substitution
            if isinstance(variable.value, (dict, list)):
                return json.dumps(variable.value)
            else:
                value_str = str(variable.value)
                # 处理换行符，将其转换为 Robot Framework 可以处理的格式
                if '\n' in value_str:
                    # 将换行符替换为 \\n，这样在 Robot Framework 中会被正确解析为换行符
                    value_str = value_str.replace('\n', '\\n')
                # 处理其他特殊字符
                value_str = value_str.replace('\r', '\\r')
                value_str = value_str.replace('\t', '\\t')
                return value_str

        return self.variable_pattern.sub(replace_var, text)

    def substitute_in_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively substitute variables in configuration dictionary"""
        result = {}

        for key, value in config.items():
            if isinstance(value, str):
                result[key] = self.substitute_variables(value)
            elif isinstance(value, dict):
                result[key] = self.substitute_in_config(value)
            elif isinstance(value, list):
                result[key] = [
                    self.substitute_variables(item) if isinstance(item, str)
                    else self.substitute_in_config(item) if isinstance(item, dict)
                    else item
                    for item in value
                ]
            else:
                result[key] = value

        return result

    def extract_variables_from_text(self, text: str) -> List[str]:
        """Extract variable names from text"""
        if not isinstance(text, str):
            return []

        matches = self.variable_pattern.findall(text)
        return list(set(matches))  # Remove duplicates

    def validate_variable_name(self, name: str) -> bool:
        """Validate variable name format"""
        if not name:
            return False

        # Variable names must start with letter or underscore, followed by letters, numbers, or underscores
        pattern = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$')
        return bool(pattern.match(name))

    def set_variable(self, name: str, value: Any, var_type: str = "string",
                    scope: VariableScope = VariableScope.LOCAL,
                    source_node_id: Optional[str] = None,
                    description: Optional[str] = None) -> bool:
        """Set a variable value"""
        if not self.validate_variable_name(name):
            logger.error(f"Invalid variable name: {name}")
            return False

        # Convert value based on type
        converted_value = self._convert_value(value, var_type)

        variable = Variable(
            name=name,
            value=converted_value,
            type=var_type,
            scope=scope,
            source_node_id=source_node_id,
            description=description
        )

        self.context.set_variable(variable)
        logger.info(f"Set variable '{name}' = {converted_value} (type: {var_type}, scope: {scope.value})")
        return True

    def get_variable(self, name: str) -> Optional[Any]:
        """Get variable value by name"""
        variable = self.context.get_variable(name)
        return variable.value if variable else None

    def get_variable_info(self, name: str) -> Optional[Variable]:
        """Get complete variable information"""
        return self.context.get_variable(name)

    def list_variables(self) -> Dict[str, Variable]:
        """List all available variables"""
        return self.context.get_all_variables()

    def clear_variables(self, scope: Optional[VariableScope] = None):
        """Clear variables from specified scope or all scopes"""
        if scope is None:
            self.context = VariableContext()
        elif scope == VariableScope.LOCAL:
            self.context.variables.clear()
        elif scope == VariableScope.WORKFLOW:
            self.context.workflow_variables.clear()
        elif scope == VariableScope.GLOBAL:
            self.context.global_variables.clear()

    def _convert_value(self, value: Any, var_type: str) -> Any:
        """Convert value to specified type"""
        try:
            if var_type == "string":
                return str(value)
            elif var_type == "number":
                if isinstance(value, str):
                    return float(value) if '.' in value else int(value)
                return value
            elif var_type == "boolean":
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif var_type == "json":
                if isinstance(value, str):
                    return json.loads(value)
                return value
            elif var_type == "list":
                if isinstance(value, str):
                    return json.loads(value) if value.startswith('[') else [value]
                elif isinstance(value, list):
                    return value
                else:
                    return [value]
            else:
                return value
        except (ValueError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to convert value '{value}' to type '{var_type}': {e}")
            return value

    def export_variables_for_robot(self) -> Dict[str, str]:
        """Export variables in Robot Framework format"""
        robot_vars = {}

        for name, variable in self.context.get_all_variables().items():
            # Convert complex types to Robot Framework compatible format
            if isinstance(variable.value, (dict, list)):
                robot_vars[name] = json.dumps(variable.value)
            else:
                robot_vars[name] = str(variable.value)

        return robot_vars
    
    
    






