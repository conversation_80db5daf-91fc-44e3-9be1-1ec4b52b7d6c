import { defineStore } from 'pinia'
import { ref, computed } from 'vue'


export const useTargetSourceStore = defineStore('targetSource', () => {
  // 标记，服务包是否请求过接口
  const isAuthQueried = ref(false)
  // 指令数据
  const command = ref([])
  // 场景数据
  const scene = ref([])
  // mcp数据
  const mcp = ref([])
  // 模板数据
  const template = ref([])

  // 标记，指令库是否请求过接口
  const isAuthCommandQueried = ref(false)
  // 指令列表（比对数据用）
  const commandList = ref([])

  // 初始化数据
  const initData = (res: any) => {
    command.value = res.command || []
    scene.value = res.scene || []
    mcp.value = res.mcp || []
    template.value = res.template || []
  }

  // 是否请求过接口
  const initQueryStatus = (status: boolean) => {
    isAuthQueried.value = status
  }

  // 指令库是否请求过接口
  const initCommandQueryStatus = (status: boolean) => {
    isAuthCommandQueried.value = status
  }

  // 初始化指令数据
  const initCommandData = (res: any) => {
    commandList.value = res || []
  }


  return {
    // 变量
    isAuthQueried,
    command,
    scene,
    mcp,
    template,
    isAuthCommandQueried,
    commandList,

    // 方法
    initData,
    initQueryStatus,
    initCommandQueryStatus,
    initCommandData,
  }
})
