<template>
  <div class="component-panel">
    <div class="panel-header">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索"
          :prefix-icon="Search"
          clearable
          class="search-input"
        />
      </div>
    </div>
    <el-tabs v-model="activeTab" stretch class="panel-tabs" @tab-click="handleClick">
      <div class="package-auth-loading" v-if="!isAuthQueried" v-loading="!isAuthQueried" element-loading-text="服务包权限验证中...请稍等"></div>

      <el-tab-pane label="场景" name="scene">
        <el-scrollbar
          style="height: 100%"
          wrap-style="overflow-x:hidden; padding-right: 8px; box-sizing: border-box;"
        >
          <div class="panel-content">
            <div class="panel-item" v-for="(item, index) in sceneDataSearch" :key="index">
              <div class="panel-item-header">
                <div class="icon">
                  <img :src="item.iconPath" alt="" />
                </div>
                <div class="title" :title="item.name">{{ item.name }}</div>
              </div>
              <div class="panel-item-banner">
                <template v-for="tag in (item.categoryIds || [])">
                  <span class="bk" v-if="categoriesMap[tag]">{{ categoriesMap[tag] }}</span>
                </template>
              </div>
              <div class="panel-item-content" :title="item.summary">
                {{ item.summary }}
              </div>
              <div class="panel-item-footer">
                <div class="rate">
<!--                  <el-rate :model-value="5" disabled />-->
                  {{ item.provider }}
                </div>
                <!-- <div class="favorite">
                  <span>使用次数 {{ item.downloadCount || 0 }}</span>
                </div> -->
              </div>
              <div class="panel-item-action">
                <el-button type="success" size="large" :disabled="!item.filePath" @click="handleSceneFile(item)">使用模板</el-button>
              </div>
            </div>
            <el-empty
              v-if="!sceneDataSearch.length"
              :image-size="80"
              description="暂无数据"
            ></el-empty>
          </div>
        </el-scrollbar>
      </el-tab-pane>

      <el-tab-pane label="MCP" name="mcp">
        <el-scrollbar
          style="height: 100%"
          wrap-style="overflow-x:hidden; padding-right: 8px; box-sizing: border-box;"
        >
          <div class="panel-content">
            <div class="panel-item" v-for="(item, index) in mcpDataSearch" :key="index">
              <div class="panel-item-header">
                <div class="icon bg">
                  <i class="action-iconfont" :class="item.icon"></i>
                </div>
                <div class="title" :title="item.name">{{ item.name }}</div>
              </div>
              <div class="panel-item-banner">
                <span class="bk">服务包订阅</span>
              </div>
              <div class="panel-item-content">
                {{ item.description }}
              </div>
              <div class="panel-item-footer">
                <div class="rate">
<!--                  <el-rate v-model="item.rate" disabled />-->
                  {{ item.provider }}
                </div>
                <!-- <div class="favorite">
                  <span>使用次数 {{ item.downloadCount || 0 }}</span>
                </div> -->
              </div>
              <div class="panel-item-action">
                <el-button type="success" size="large" disabled>使用模板</el-button>
              </div>
            </div>
            <el-empty
              v-if="!mcpDataSearch.length"
              :image-size="80"
              description="暂无数据"
            ></el-empty>
          </div>
        </el-scrollbar>
      </el-tab-pane>

      <el-tab-pane label="模板" name="template">
        <el-scrollbar
          style="height: 100%"
          wrap-style="overflow-x:hidden; padding-right: 8px; box-sizing: border-box;"
        >
          <div class="panel-content">
            <div
              class="panel-item"
              v-for="(item, index) in templateDataSearch"
              :key="index"
              style="cursor: pointer"
            >
              <div class="panel-item-header">
                <div class="icon">
                  <img :src="item.iconPath" alt="" />
                </div>
                <div class="title" :title="item.name">{{ item.name }}</div>
<!--                <div class="icon-right">-->
<!--                  <el-icon size="12"><ArrowRight /></el-icon>-->
<!--                </div>-->
              </div>
              <div class="panel-item-banner">
                <span class="bk">{{ formatType(item.formatType) }}</span>
                <template v-for="tag in (item.categoryIds || [])">
                  <span class="bk" v-if="categoriesMap[tag]">{{ categoriesMap[tag] }}</span>
                </template>
              </div>
              <div class="panel-item-content" :title="item.summary">
                {{ item.summary }}
              </div>
              <div class="panel-item-footer">
                <div class="rate">
<!--                  <el-rate :model-value="5" disabled />-->
                  {{ item.provider }}
                </div>
                <!-- <div class="favorite">
                  <span>使用次数 {{ item.downloadCount || 0 }}</span>
                </div> -->
              </div>
              <div class="panel-item-action">
                <el-button type="success" size="large" :disabled="!item.filePath" @click="handleTemplateFile(item)">导入模板</el-button>
              </div>
            </div>
            <el-empty
              v-if="!templateDataSearch.length"
              :image-size="80"
              description="暂无数据"
            ></el-empty>
          </div>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
    <div class="service-market-box">
      <div class="service-market" @click="handleToStore" type="danger">
        <div class="icon">
          <img src="../../assets/images/servicePackage/serviceMarket.png" alt="" />
        </div>
        <div class="content">
          <div class="title">服务包市场</div>
          <div class="desc">搜索更多场景、MCP、模板</div>
        </div>
        <div class="right">
          <el-icon color="#0054D2"><Right /></el-icon>
        </div>
      </div>
    </div>
  </div>
  <div class="copyright-panel">
    <span>Copyright ©浙江和达科技股份有限公司</span>
  </div>
  <div
    class="right-collapse-icon"
    :title="!isCollapsed ? '折叠' : '展开'"
    @click.stop="toggleCollapse"
  >
    <el-icon v-if="isCollapsed" size="14" color="#999"><CaretRight /></el-icon>
    <el-icon v-else size="14" color="#999"><CaretLeft /></el-icon>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watch, onMounted } from 'vue'
import { CaretRight, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { useRoute } from 'vue-router'
import type { WorkflowData } from '@/stores/workflow.ts'
import { usePanelStore } from '@/stores/panel.ts'
import { useTargetSourceStore } from '@/stores/targetSource'

// @ts-ignore
import saasApi from '@/api/index'
// @ts-ignore
import { request } from '@/utils/axios'
// @ts-ignore
import utils from '@/utils/utils'

const route = useRoute()

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: string
  data: any
  bindings: any
  createTime: string
}

const emit = defineEmits<{
  (e: 'imported-template', template: WorkflowData): void
  (e: 'template-saved', template: ReportTemplate): void
}>()

const panelStore = usePanelStore()

// 新增折叠状态
const isCollapsed = computed(() => {
  return panelStore.isCollapsed
})
const toggleCollapse = () => {
  panelStore.setCollapsed(!isCollapsed.value)
}

// 定义是否还在获取权限中
const targetSourceStore = useTargetSourceStore()
const isAuthQueried = computed(() => targetSourceStore.isAuthQueried)
// 权限获取完毕后获取服务包数据
watch(isAuthQueried, async (v) => {
  if (v){
    if(targetSourceStore.scene.length){
      await GetTemplates('scene')
    }
    if(targetSourceStore.mcp.length){
      // await GetTemplates('mcp')
    }
    if(targetSourceStore.template.length){
      await GetTemplates('template')
    }
  }
})


// 定义搜索内容
const searchQuery = ref('')
const activeTab = ref('scene')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

// 数据
const sceneData = ref([])
const sceneDataSearch = computed(() => {
  return sceneData.value.filter((item: any) => {
    const name = item.name.toLowerCase()
    const description = item.description.toLowerCase()
    const query = searchQuery.value.toLowerCase()
    return name.includes(query) || description.includes(query)
  })
})

const mcpData = ref([
  {
    status: 0,
    name: 'Fetch网页内容抓取',
    description:
      '一个提供网页内容抓取功能的模型上下文协议服务器。此服务器使大型语言模型能够从网页中检索和处理内容，并将 HTML 转换为 markdown 以便更容易地使用。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: '转Markdown工具',
    description:
      '将各种文件类型和网页内容转换为Markdown格式。它提供了一套工具，可以将PDF、图像、音频文件、网页等转换为易读且易于分享的Markdown文本。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: '深度调研-MCP',
    description:
      '通过将人工智能代理与搜索引擎、网络爬虫和大型语言模型集成，实现迭代深入研究，以高效收集数据并进行全面报告。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: 'Excel-MCP服务',
    description:
      '通过模型上下文协议（Model Context Protocol）， enables 无缝读取、写入和分析 Excel 文件，具有工作表管理、结构分析和自动缓存等功能。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: 'MCP-Doc 文档处理工具',
    description:
      '基于 FastMCP 的强大 Word 文档处理服务，使 AI 助手能够创建、编辑和管理带有完整格式支持的 docx 文件。在编辑内容时保留原始样式。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: 'SQLite',
    description:
      '一种模型上下文协议（MCP）服务器实现，通过SQLite提供数据库交互和商业智能功能。该服务器支持运行SQL查询、分析业务数据以及自动生成业务洞察备忘录。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
  {
    status: 0,
    name: 'Dify MCP服务',
    description:
      '将Dify应用程序（包括Chatflow和Workflow）作为MCP（模型上下文协议）服务器进行暴露，允许Claude和其他MCP客户端通过标准化协议直接与Dify应用交互。',
    icon: 'icon-jianceshuju',
    downloadCount: 1234,
    rate: 5,
  },
])
const mcpDataSearch = computed(() => {
  return mcpData.value.filter((item: any) => {
    const name = item.name.toLowerCase()
    const description = item.description.toLowerCase()
    const query = searchQuery.value.toLowerCase()
    return name.includes(query) || description.includes(query)
  })
})

const templateData = ref([])
const templateDataSearch = computed(() => {
  return templateData.value.filter((item: any) => {
    const name = item.name.toLowerCase()
    const description = item.description.toLowerCase()
    const query = searchQuery.value.toLowerCase()
    return name.includes(query) || description.includes(query)
  })
})

// 格式化模板类型
const formatType = (str: string) => {
  return (str ? str.charAt(0).toUpperCase() + str.slice(1) : '其他') + '模板'
}

// 接口获取模板数据
const GetTemplates = async (type: string) => {
  const params = {
    url: '/wimai/api/task/template/query',
    body_param: {
      conditions: [],
      data: {
        status: 1,
        categoryMains: [type],
      },
      index: 1,
      size: -1,
    },
    method: 'post',
  }
  saasApi
    .AIDtemplateCrud(params)
    .then((res: any) => {
      if (res?.rows) {
        if (type === 'scene') {
          // 按照权限过滤
          sceneData.value = res.rows.filter((item: any) => {
            return targetSourceStore.scene.includes(item.id)
          })
        }
        if (type === 'mcp') {
          // 按照权限过滤
          mcpData.value = res.rows.filter((item: any) => {
            return targetSourceStore.mcp.includes(item.id)
          })
        }
        if (type === 'template') {
          // 按照权限过滤
          templateData.value = res.rows.filter((item: any) => {
            return targetSourceStore.template.includes(item.id)
          })
        }
      } else {
        throw new Error('获取服务包数据失败')
      }
    })
    .catch(() => {
      if (type === 'scene') {
        sceneData.value = []
      }
      if (type === 'mcp') {
        mcpData.value = []
      }
      if (type === 'template') {
        templateData.value = []
      }
    })
}

// 服务包分类
const categoriesMap = ref({})
interface CategoryItem {
  id?: string
  name?: string
}

const GetCategories = async () => {
  try {
    const params = {
      url: "/wimai/api/task/resource/category/query",
      body_param: {
        conditions: [],
        data: { status: "enable" },
        index: 1,
      },
      method: "post"
    }

    const res = await saasApi.AIDtemplateCrud(params)

    if (res?.rows) {
      const newCategoriesMap: Record<string, string> = {}
      res.rows.forEach((row: CategoryItem) => {
        if (row.id && row.name) {
          newCategoriesMap[row.id] = row.name
        }
      })
      categoriesMap.value = newCategoriesMap
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
    categoriesMap.value = {}
  }
}
onMounted(() => {
  GetCategories()
})


// 处理场景模版
const handleSceneFile = async (item: any) => {
  try {
    // 1. 添加确认提示框
    await ElMessageBox.confirm(
      `此操作将重置当前工作流，请确认是否继续？`,
      `使用模版 - ${item.name}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 2. 获取文件数据
    const responseData = await request.get(
      item.filePath,
      {},
      {
        responseType: 'arraybuffer',
      },
    )

    // 3. 将二进制数据转换为文本
    const textDecoder = new TextDecoder('utf-8')
    const jsonString = textDecoder.decode(responseData)

    // 4. 解析JSON数据
    const jsonData = JSON.parse(jsonString)

    // 5. 处理模板文件
    if (jsonData) {
      emit('imported-template', jsonData)
    } else {
      ElMessage.error(`使用模板 "${item.name}" 失败，请重试`)
    }
  } catch (error) {
    console.error('处理模板文件失败:', error)
  }
}

// 处理导入模板
const handleTemplateFile = async (item: any) => {
  try {
    // 1. 添加确认提示框
    await ElMessageBox.confirm(
      `此操作将添加该模板到${item.formatType.charAt(0).toUpperCase() + item.formatType.slice(1)}模板库中，请确认是否继续？`,
      `添加模版 - ${item.name}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
    // 2. 获取文件数据
    const responseData = await request.get(
      item.filePath,
      {},
      {
        responseType: 'arraybuffer',
      },
    )

    // 3. 将二进制数据转换为文本
    const textDecoder = new TextDecoder('utf-8')
    const jsonString = textDecoder.decode(responseData)

    // 4. 解析JSON数据
    const jsonData = JSON.parse(jsonString)

    // 5. 处理模板文件
    if (jsonData) {
      const templateData = {
        ...jsonData,
        templateType: item.formatType,
        name: item.name
      }
      delete templateData.exportTime

      // 如果是excel模板，需要特殊转换
      if (item.formatType === 'excel') {
        templateData.bindings = excelConvertedBindingsData(templateData)
      }

      const response = await fetch('http://localhost:39876/api/report-templates/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: utils.GetAuthorization(),
        },
        body: JSON.stringify(templateData),
      })
      if (!response.ok) {
        const errorText = await response.text()
        ElMessage.error('服务器响应错误:' + errorText)
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }
      const data = await response.json()
      ElMessage.success(`模板 "${data.name}" 导入成功，可前往模板库查看`)
      emit('template-saved', data)
    } else {
      ElMessage.error(`导入模板 "${item.name}" 失败，请重试`)
    }
  } catch (error) {
    console.error('处理模板文件失败:', error)
  }
}

// 打开服务市场
const handleToStore = () => {
  const urlToken = utils.GetAuthorization()
  const uniwim = utils.GetQueryString('uniwim', 'hash') || utils.GetQueryString('uniwim')
  if (uniwim === 'pc') {
    const urlOrigin = location.origin + location.pathname + `#/serviceMarket?_t=1&from=package&uniwater_utoken=${urlToken}`
    window.open(
      urlOrigin,
      '_blank',
      `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`,
    )
  } else {
    const urlOrigin = location.origin + location.pathname + `#/serviceMarket?_t=1&uniwater_utoken=${urlToken}`
    window.open(urlOrigin)
  }
}

// excel 数据特殊转换
const excelConvertedBindingsData = (template: ReportTemplate) => {
  const cellBindings = template.bindings

  // 转换绑定数据格式以匹配后端API要求
  const convertedBindings: Record<string, any> = {}

  // 如果绑定数据有cellBindings数组，转换为后端期望的格式
  if (template.bindings && template.bindings.cellBindings) {
    for (const cellBinding of template.bindings.cellBindings) {
      if (cellBinding.cell) {
        // 确保保存完整的字段路径
        let fieldPath = cellBinding.fieldPath || ''
        if (!fieldPath && cellBinding.expression) {
          // 从表达式中提取字段路径
          fieldPath = cellBinding.expression.replace(/^\$\{|\}$/g, '').replace(/^#\{|\}$/g, '')
        }

        // 如果还是没有完整路径，记录警告但仍然保存
        if (!fieldPath) {
          console.warn(`单元格 ${cellBinding.cell} 没有有效的字段路径`)
          fieldPath = cellBinding.fieldPath || 'unknown'
        }

        // 获取完整的绑定信息，包括循环配置
        const fullBinding = cellBindings[cellBinding.cell]
        const bindingData: any = {
          variable: fieldPath,
          type: cellBinding.fieldType || 'string',
          format: cellBinding.format || '',
        }

        // 如果是循环绑定，保存循环配置
        if (fullBinding && fullBinding.loopConfig) {
          bindingData.loopConfig = {
            arrayField: fullBinding.loopConfig.arrayField,
            direction: fullBinding.loopConfig.direction || 'vertical',
            actualField: fullBinding.loopConfig.actualField,
          }
          console.log(`保存循环配置 ${cellBinding.cell}:`, bindingData.loopConfig)
        }

        // 如果有条件表达式，保存条件表达式
        if (fullBinding && fullBinding.conditionalExpression) {
          bindingData.conditionalExpression = fullBinding.conditionalExpression
          console.log(`保存条件表达式 ${cellBinding.cell}:`, bindingData.conditionalExpression)
        }

        // 如果有条件表达式，保存条件表达式
        if (fullBinding && fullBinding.displayConditions) {
          bindingData.displayConditions = fullBinding.displayConditions
          console.log(`保存条件表达式 ${cellBinding.cell}:`, bindingData.displayConditions)
        }

        convertedBindings[cellBinding.cell] = bindingData

        console.log(`保存绑定 ${cellBinding.cell}: ${fieldPath}`)
      }
    }
  }

  // 如果绑定数据是直接的对象格式，也进行转换
  if (template.bindings && !template.bindings.cellBindings) {
    for (const [cellAddress, binding] of Object.entries(template.bindings)) {
      if (typeof binding === 'object' && binding !== null) {
        // 确保保存完整的字段路径
        let fieldPath = (binding as any).fieldPath || ''
        if (!fieldPath && (binding as any).expression) {
          // 从表达式中提取字段路径
          fieldPath = (binding as any).expression.replace(/^\$\{|\}$/g, '').replace(/^#\{|\}$/g, '')
        }

        // 获取完整的绑定信息，包括循环配置
        const fullBinding = cellBindings[cellAddress]
        const bindingData: any = {
          variable: fieldPath,
          type: (binding as any).fieldType || (binding as any).type || 'string',
          format: (binding as any).format || '',
        }

        // 如果是循环绑定，保存循环配置
        if (fullBinding && fullBinding.loopConfig) {
          bindingData.loopConfig = {
            arrayField: fullBinding.loopConfig.arrayField,
            direction: fullBinding.loopConfig.direction || 'vertical',
            actualField: fullBinding.loopConfig.actualField,
          }
          console.log(`保存循环配置 ${cellAddress}:`, bindingData.loopConfig)
        }

        convertedBindings[cellAddress] = bindingData
      }
    }
  }

  console.log('原始绑定数据:', template.bindings)
  console.log('转换后的绑定数据:', convertedBindings)
  return convertedBindings
}
</script>

<style scoped lang="scss">
.package-auth-loading{
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}
.component-panel {
  width: 249px;
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  background: white;
  transition: width 0.3s ease; /* 添加过渡效果 */
  position: relative;
}

.component-panel.collapsed {
  width: 60px;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;

  .panel-header {
    padding: 16px 12px;
  }
  .panel-title {
    justify-content: center;

    .num-total,
    .collapse-expand-icon {
      display: none;
    }
  }
}

.panel-header {
  padding: 16px;
}

.panel-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  width: 100%;
  position: relative;
  cursor: pointer;

  .num-total {
    font-size: 12px;
    color: #999999;
    position: absolute;
    right: 26px;
    font-weight: normal;
  }
  .collapse-expand-icon {
    padding: 0 4px;
  }
}
.search-box {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 100%;
}

:deep(.panel-tabs) {
  padding: 0;
  flex: 1;
  overflow: hidden;
  .el-tabs__header {
    padding: 0 16px;
    margin-bottom: 8px;
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #eeeeee;
  }
  .el-tabs__active-bar {
    border-radius: calc(var(--el-border-radius-base) - 2px);
  }
  .el-tabs__item {
    padding: 0;
    height: 32px;
    font-size: 14px;
    &:not(.is-active) {
      color: #222222;
    }
  }
  .el-tabs__content {
    .el-tab-pane {
      height: 100%;
      overflow: hidden;
    }
  }
}

.current-scene {
  position: sticky;
  top: 0;
  background: #dcf4ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  height: 32px;
  margin-bottom: 8px;
  margin-left: 16px;
  color: var(--el-color-primary);
  user-select: none;
  .el-icon {
    cursor: pointer;
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 8px 0 16px;
  display: flex;
  flex-direction: column;
}

.panel-item {
  border: 1px solid #e6e7e9;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
  margin-bottom: 16px;
  gap: 12px;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
  &:hover {
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.15);
  }

  .panel-item-banner {
    display: flex;
    flex-flow: row wrap;
    gap: 8px;
    .bk {
      padding: 0 8px;
      line-height: 22px;
      box-sizing: border-box;
      background: #f7f7f9;
      border: 1px solid #e6e7e9;
      border-radius: 11px;
      font-size: 12px;
      color: #666666;
      &.success {
        border: 1px solid #c3e9b5;
        background: #f0ffec;
        color: #00994e;
      }
      &.warning {
        background: #fff2e8;
        border: 1px solid #ffbb96;
        color: #d4380d;
      }
    }
  }
  .panel-item-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    .icon {
      width: 24px;
      height: 24px;
      padding: 0;
      border-radius: 4px;
      overflow: hidden;
      flex-shrink: 0;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &.bg {
        background: #409eff;
      }
      .action-iconfont {
        font-size: 12px;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block;
      }
    }
    .title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 16px;
      color: #222222;
    }
    .price {
      font-size: 12px;
      color: #e6a23c;
      font-weight: bold;
    }
    .icon-right {
      height: 24px;
      flex-shrink: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .panel-item-content {
    font-size: 12px;
    color: #5c5f66;
    letter-spacing: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .panel-item-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    .rate {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 12px;
      color: #bcbfc3;
      :deep(.el-rate) {
        height: auto;
        .el-rate__icon {
          margin-right: 0;
        }
      }
    }
    .favorite {
      color: #bcbfc3;
      font-size: 12px;
    }
  }
  .panel-item-action {
    display: flex;
    gap: 12px;
    .el-button {
      flex: 1;
      width: 0;
      height: 32px;
      line-height: 32px;
      & + .el-button {
        margin-left: 0;
      }
    }
  }
}

.copyright-panel {
  box-sizing: border-box;
  flex-shrink: 0;
  height: 40px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 40px;
  padding: 0 16px;
  position: relative;
  font-size: 12px;
  color: #bcbfc3;
  user-select: none;
  text-align: center;
}
.right-collapse-icon {
  position: absolute;
  right: -17px;
  top: 50%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  /* 新增梯形样式 */
  &::before {
    content: '';
    position: absolute;
    width: 18px;
    height: 45px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-left: none;
    transform: perspective(5px) rotateY(8deg);
    transform-origin: left;
    border-radius: 0 6px 6px 0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    transition: background 0.2s;
  }
  .el-icon {
    transition: color 0.2s;
    left: -3px;
  }
  &:hover {
    .el-icon {
      color: #666;
    }
    &::before {
      background: #eef4ff;
    }
  }
}
.service-market-box{
  box-shadow: 0 -4px 8px rgb(148 148 148 / 16%);
}
.service-market {
  margin: 16px 16px 0;
  height: 56px;
  background-image: linear-gradient(180deg, #ffffff 0%, #f4f4f4 100%);
  border: 1px solid #e6e7e9;
  border-radius: 4px;
  cursor: pointer;
  transition: box-shadow 0.2s;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  position: relative;
  &:hover {
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.15);
  }
  .icon {
    width: 36px;
    height: 36px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 36px;
    .title {
      font-size: 14px;
      color: #222222;
    }
    .desc {
      font-size: 12px;
      color: #5c5f66;
    }
  }
  .right {
    position: absolute;
    right: 10px;
    top: 8px;
    font-size: 18px;
  }
}
</style>
