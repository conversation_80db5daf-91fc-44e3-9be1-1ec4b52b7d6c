<template>
  <van-nav-bar
    class="van-nav-bar"
    :title="props.title"
    left-arrow
    @click-left="onClickLeft"
  />
</template>

<script setup lang="ts">
import utils from '@/utils/utils.ts'
import native from '@/utils/native.ts'
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})

const onClickLeft = () => {
  if(utils.isHdkj()){
    native.goback({
      params:{},
    })
  }
  else{
    history.back()
  }
};

</script>

<style scoped lang="scss">
.van-nav-bar{
  width: 100%;
}
</style>
