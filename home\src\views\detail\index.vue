<template>
    <div class="service-detail" @scroll="handleScroll" v-loading="loading">
        <!-- <div class="page-bg"></div> -->
        <div class="page-content">
            <div class="content-inner clearfix">
                <!-- <div class="breadcrumb">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item :to="{ path: '/serviceMarket',query:route.query }">服务包市场</el-breadcrumb-item>
                        <el-breadcrumb-item>
                            产品详情
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </div> -->
                <div class="product-content clearfix">
                    <div class="product-left">
                        <div class="product-card">
                            <div class="product-icon">
                                <img :src="currentCard.iconPath" alt="">
                            </div>          
                            <div class="prduct-summary">
                                <div class="product-name">
                                    {{ currentCard.name }}
                                </div>
                                <div class="product-summary-text" :title="currentCard.summary">
                                    {{ currentCard.summary }}
                                </div>
                                <div class="product-category-version">
                                    <div class="product-category-version-left">
                                        <div class="product-category">
                                            <div class="tag-item" v-show=" currentCard.categoryMain">
                                                {{ currentCard.categoryMain==='scene'?'场景':(currentCard.categoryMain==='template'?'模板':'MCP') }}
                                            </div>
                                            <div class="tag-item" v-show="currentCard.categoryIds" :key="item" v-for="item in currentCard.categoryIds">
                                                {{ categoriesMap[item] }}
                                            </div>
                                        </div>
                                        <div class="product-version">
                                            {{ currentCard.versionNumber?'v'+currentCard.versionNumber:'' }}
                                        </div>
                                    </div>
                                    
                                    <div class="product-category-version-right">
                                        <div class="product-updated-used">
                                            <div class="product-updated">更新日期：{{ moment(currentCard.updated).format('YYYY-MM-DD') }}</div>
                                            <!-- <div class="product-used">使用次数：{{ currentCard.downloadCount ||0 }}</div> -->
                                        </div>
                                    </div>
                                </div>
                                
                            </div>   
                        </div>
                        <div class="product-detail">
                            <div class="detail-content ql-editor" v-html="currentCard.description">
                            </div>
                        </div>
                    </div>
                    <div class="product-right">
                        <div class="product-right-top">
                            <div class="product-provider" :title="currentCard.provider">
                                {{ currentCard.provider }}
                            </div>
                            <div class="link-button" @click="jumpTo('/comment?from=服务包详情&serviceId='+currentCard.id)">
                                <i class="link-button-icon"></i><span>立即联系</span> 
                            </div>
                        </div>
                        <div class="product-recommend">
                            <div class="product-recommend-title">热门推荐</div>
                            <div class="product-recommend-list">
                                <div class="product-recommend-item" @click="clickHotCard(card)" v-for="card in hotList" :key="card.id">
                                    <div class="product-recommend-item-inner">
                                        <div class="product-recommend-item-top">
                                            <img :src="card.iconPath" alt="" class="product-recommend-item-icon">
                                            <span class="product-recommend-item-name" :title="card.name">{{ card.name }}</span>
                                        </div>
                                        <div class="product-recommend-item-tags">
                                            <div class="product-recommend-item-tag" v-show="card.categoryMain">
                                                {{ card.categoryMain==='scene'?'场景':(card.categoryMain==='template'?'模板':'MCP') }}
                                            </div>
                                            <div class="product-recommend-item-tag" v-show="card.categoryIds" :key="item" v-for="item in card.categoryIds">
                                                {{ categoriesMap[item] }}
                                            </div>
                                        </div>
                                        <div class="product-recommend-item-summary" :title="card.summary">
                                            {{ card.summary }}
                                        </div>
                                        <div class="product-recommend-item-provider">
                                            {{ card.provider }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部信息模块 -->
        <footer class="footer-section">
            <div class="container">
                <!-- <div class="footer-main">
                    <div class="footer-left">
                        <div class="footer-left-title">联系我们</div>
                        <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                        <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                        <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                    </div>
                    <div class="footer-middle">
                        <p>一诺数字助理</p>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                            class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                            class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                    </div>
                    <div class="footer-right">
                        <div class="qrcode-box">
                            <p>一诺APP</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                            </div>
                        </div>
                        <div class="qrcode-box">
                            <p>度量公众号</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                            </div>
                        </div>
                    </div>
                </div> -->
                <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
            </div>

        </footer>
    </div>
</template>

<script setup lang="ts">
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import {
    Search,
    CircleClose
} from '@element-plus/icons-vue'
import { ref, reactive,computed, onMounted, watch } from 'vue';
import utils from '@/utils/utils'
import saasApi from '@/api/index';
import moment from 'moment'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

const isFixed = ref(true)
const activeName = ref('')
const loading = ref(false)
const categoriesMap = ref({})
const hotList = ref([])
const currentCard = ref({
    categoryIds:[],
    categoryMain:"",
    iconPath:"",
    image:"",
    name:"",
    versionNumber:"",
    updated:"",
    downloadCount:"",
    summary:"",
    description:"",
    provider:""
})

const handleScroll = (event:any)=>{
    if(event.target.scrollTop==0){
        isFixed.value = true;
    }else{
        isFixed.value = false;
    }
}


// 监听特定参数
watch(() => route.params, (newVal, oldVal) => {
    getDetail()
}, { deep:true })


// 定义计算属性，和 Vue2 中 computed 配置项里的函数作用一致
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc'||utils.GetQueryString('uniwim')=='pc'
});

const isFromPackage = computed(() => {
  return route.query.from === 'package'||utils.GetQueryString('from')=='package'
});

const token = computed(() => {
  return utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
});


const clickHotCard = (row:any)=>{
    router.push({
        path:'/serviceDetail/'+row.id,
        query:route.query
    })
}
const getTypes  = ()=>{
    const params = {
        url: "/wimai/api/task/resource/category/query",
        body_param: {
            conditions: [],
            data: {
                status:"enable"
            },
            order:[
                {
                    Field:"sort",
                    Type:1
                },
                {
                    Field:"updated",
                    Type:-1
                }
            ],
            index: 1,
        },
        method: "post"
    }
    saasApi.AIAgentTaskCategoryQueryIg(params.body_param).then((res:any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach((row:any)=>{
                categoriesMap.value[row.id] = row.name
                // if(row.typeList){
                //     row.typeList.forEach(tp=>{
                //         let staticCategoriesList = categories[0].items
                //         let obj = staticCategoriesList.find(cate=>cate.id==tp.type)
                //         if(obj){
                //             obj.resourceNum++
                //         }
                //     })
                // }
            })
        }
    })
    .catch((err: any) => {
    })
    .finally(() => {
    })

}
const addImageWidthAttribute = (htmlString:string) => {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlString;
    
    // 获取所有img标签
    const images = tempDiv.getElementsByTagName('img');
    
    // 遍历每个img标签
    for (let i = 0; i < images.length; i++) {
        const img = images[i];
        // 检查是否已经有width属性
        if (!img.hasAttribute('width')) {
            // 如果没有，添加width="100%"
            img.setAttribute('width', '100%');
        }
    }
    
    // 返回处理后的HTML字符串
    return tempDiv.innerHTML;
}
const getDetail = ()=>{
    let id = route.params.id
    ;
    const params = {
        url: "/wimai/api/task/template/",
        body_param: {
            conditions: [
                // {
                //     Field: "id",
                //     Group: 1,
                //     Operate: "=",
                //     Relation: "and",
                //     Value: id
                // }
            ],
            data: {
                id
            },
            order:[
            ],
            index: 1,
        },
        method: "post"
    }
    loading.value = true
    saasApi.AIAgentTaskTemplateQuery(params.body_param).then((res:any) => {
       
        if(res?.rows){
            if(res.rows.length){
                let result = res.rows[0];
                ;
                result.description = addImageWidthAttribute(result.description);
                currentCard.value = result
            }
        }else{
            currentCard.value = null;
        }
    })
    .catch((err:any) => {
        currentCard.value = null;
    }).finally(()=>{
        loading.value = false
    })
}
const getHotList = ()=>{
    let id = route.params.id
    const params = {
        url: "/wimai/api/task/template/",
        body_param: {
            conditions: [],
            data: {
            },
            order:[
                {
                    Field:"sort",
                    Type:1
                }
                // {
                //     created:-1
                // }
            ],
            index: 1,
            size:5
        },
        method: "post"
    }
    saasApi.AIAgentTaskTemplateQuery(params.body_param).then((res:any) => {
       ;
        if(res?.rows){
            hotList.value = res.rows;
        }else{
            hotList.value = [];
        }
    })
    .catch((err:any) => {
        hotList.value = [];
    }).finally(()=>{
    })
}

const handleTab = (type:string)=>{
    let path = '/homepage'
    if(type.paneName==='product'){
        path = '/homepage'
    }else if(type.paneName==='market'){
        path = '/serviceMarket'
    }else if(type.paneName==='study'){
        path = '/study'
    }
    router.push({
        path: path,
        query: route.query
    })
}
const jumpTo = (url:string)=>{
    let uniwim = utils.GetQueryString('uniwim')||utils.GetQueryString('uniwim','hash')
    let params = {}
    if(uniwim){
        params.uniwim = uniwim
    }
    let path = utils.generateUrlWithQuery(
        url,
        params
    )
    router.push(path)
}
const jumpToPage = (path:string)=>{
    // router.push({
    //     name: path,
    //     query: route.query
    // })
    let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
    if(token){
        route.query.uniwater_utoken = token
    }
    let url = utils.generateUrlWithQuery("https://www.dlmeasure.com/extends/WimTask/index.html#/manager",route.query)
    // window.open(url)
    window.location.href = url
}
onMounted(async()=>{
    await getTypes();
    getDetail();
    getHotList();
})
</script>
<style>

</style>

<style lang='less' scoped>
:deep(ol) {
     padding-left: 0; /* 移除默认左侧内边距 */
    list-style-position: inside; /* 让列表项编号显示在内容内部 */
}
:deep(li) {
}
.service-detail {
    width: 100%;
    height: 100%;
    background:#F5F7FA;
    overflow: auto;
    position:relative;
    .clearfix::after {
        content: "";
        display: table;
        clear: both;
    }
}
.page-bg{
    // position: absolute;
    top:0;
    left:50%;
    z-index: 0;
    pointer-events: none;
    transform: translateX(-50%);
    width:1920px;
    margin:0 auto;
    height:577px;
    background: url('../../assets/images/homepage/detail-bg.png') #F5F7FA no-repeat top center;
    background-size: 1920px 577px;
}

.page-content{
    // min-width:1400px;
    min-width:1440px;
    // height:calc(100% - 48px);
    background: url('../../assets/images/homepage/detail-bg.png') #F5F7FA no-repeat top center;
    background-size: 1920px 577px;
    .content-inner{
        width:1400px;
        margin:0 auto;
        padding-top:58px;
    }
    .breadcrumb{
        width:100%;
        height:20px;
        line-height: 20px;
        margin:12px 0;
        :deep(.el-breadcrumb){
            line-height:20px;
        }
        :deep(.el-breadcrumb__separator){
            font-weight: normal;
        }
        :deep(.el-breadcrumb__inner){
            height: 18px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            &.is-link{
                height: 17px;
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 12px;
                color: #222222;
                text-align: right;
            }
        }
    }
    .product-content{
        margin-top:20px;
        .product-left{
            float:left;
            width:calc(100% - 260px);
            margin-right:20px;
            .product-card{
                height: 194px;
                background: #FFFFFF;
                border-radius: 4px;
                padding:24px;
                box-sizing: border-box;
                .product-icon{
                    float:left;
                    width: 60px;
                    height: 60px;
                    img{
                        width:100%;
                        height:100%;
                    }
                }
                .prduct-summary{
                    float:left;
                    margin-left:16px;
                    width:calc(100% - 76px);
                }
                .product-name{
                    height: 36px;
                    line-height: 36px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 24px;
                    color: #222222;
                }
                .product-summary-text{
                    margin-top:8px;
                    height: 48px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 14px;
                    color: #808A94;
                    line-height: 24px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2; /* 限制为2行 */
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
                .product-category-version{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-top:16px;
                    // gap:16px;
                    .product-category-version-left{
                        
                        display: flex;
                        align-items: center;
                        margin-top:16px;
                        gap:16px;
                    }
                    .product-category{
                        display:flex;
                        align-items: center;
                        gap:16px;
                        .tag-item{
                            padding:5px 8px;
                            text-align:center;
                            font-family: SourceHanSansSC-Regular;
                            font-weight: 400;
                            color: #616770;
                            background: #F0F5FF;
                            border-radius: 14px;
                            font-family: SourceHanSansSC-Regular;
                            font-weight: 400;
                            font-size: 12px;
                            color: #1E39C3;
                        }
                    }
                    .product-version{
                        // margin-left:16px;
                        height: 18px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #616770;
                    }
                }
                .product-updated-used{
                    display: flex;
                    justify-content: space-between;
                    margin-top:16px;
                    height: 18px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #808A94;
                }
            }
            .product-detail{
                margin-top:20px;
                background:#fff;
                padding:24px;
                :deep(img){
                    width:100%;
                }
            }
        }
        .product-right{
            float:left;
            width:240px;
            .product-right-top{
                padding:20px 24px 24px 24px;
                box-sizing: border-box;
                width: 240px;
                height: 116px;
                background: #FFFFFF;
                border-radius: 4px;
                margin-bottom:20px;
                .product-provider{
                    width: 100%;
                    height: 20px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 14px;
                    color: #222222;
                    overflow:hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .link-button{
                    margin-top:20px;
                    width: 192px;
                    cursor: pointer;
                    background: #0054D2;
                    border-radius: 2px;
                    color:#fff;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 14px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .link-button-icon{
                        display: block;
                        width: 16px;
                        height: 16px;
                        background: url('../../assets/images/homepage/link-button-icon.png') no-repeat center center;
                        background-size:100% 100%;
                        margin-right:8px;
                    }
                }
            }
            .product-recommend{
                .product-recommend-title{
                    width:100%;
                    background:#fff;
                    padding: 14px 24px;
                    box-sizing: border-box;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 16px;
                    color: #000000;
                    letter-spacing: 0;
                    line-height: 24px;
                    border-bottom:1px solid #EEEEEE;
                }
                .product-recommend-list{
                    height:840px;
                    background: #fff;
                    .product-recommend-item{
                        width: 240px;
                        height: 168px;
                        background: #FFFFFF;
                        padding:0 24px;
                        box-sizing: border-box;
                        overflow: hidden;
                        cursor: pointer;
                        .product-recommend-item-inner{
                            margin:0 auto;
                            // width:292px;
                            width:100%;
                            height:100%;
                            padding: 12px 0;
                            border-bottom:1px solid #EEEEEE;
                            .product-recommend-item-top{
                                display:flex;
                                align-items: center;
                                .product-recommend-item-icon{
                                    width: 24px;
                                    height: 24px;
                                }
                                .product-recommend-item-name{
                                    margin-left:8px;
                                    height: 20px;
                                    font-family: SourceHanSansSC-Regular;
                                    font-weight: 400;
                                    font-size: 14px;
                                    width:100%;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    color: #222222;
                                }
                                
                            }
                            .product-recommend-item-tags{
                                margin-top:10px;
                                display: flex;
                                gap:8px;
                                overflow: hidden;
                            }
                            .product-recommend-item-tag{
                                background: #F7F7F9;
                                border: 1px solid #E6E7E9;
                                border-radius: 11px;
                                line-height: 22px;
                                padding:0 8px;
                                font-family: SourceHanSansSC-Regular;
                                font-weight: 400;
                                font-size: 12px;
                                color: #666666;
                            }
                            .product-recommend-item-summary{
                                margin-top:10px;
                                height: 51px;
                                font-family: SourceHanSansSC-Regular;
                                font-weight: 400;
                                font-size: 12px;
                                color: #5C5F66;
                                letter-spacing: 0;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 3; /* 限制为2行 */
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                            .product-recommend-item-provider{
                                margin-top:10px;
                                height: 17px;
                                font-family: SourceHanSansSC-Regular;
                                font-weight: 400;
                                font-size: 12px;
                                color: #BCBFC3;
                            }
                        }
                    }
                }
            }
        }
    }
}
.banner-top {
    width:100%;
    // width:1400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    background:#fff;
    // height: 80px;
    margin: 0 auto;
    position:fixed;
    top:0;
    z-index:10;
    transition: background-color 0.3s ease; // 添加过渡效果
    box-shadow: 0 1px 30px 0 #4a5c7a29;
    &.banner-top-fixed{
        // background: transparent;
        // box-shadow: none;
    }
    &.is-from-package{
        top:42px;
    }

    .banner-left{
        display:flex;
        // height:58px;
        // align-items: center;
        img{
            margin-top:5px;
        }
        .tabs-box{
            display:flex;
            align-items: center;
            height:58px;
        }
        .el-tabs{
            height:40px;
            margin-left:68px;
        }
        /deep/.el-tabs__header{
            margin:0;
        }
        /deep/.el-tabs__nav-wrap::after{
            background:transparent;
        }
        /deep/.el-tabs__item{
            font-size: 16px;
        }
        /deep/.el-tabs__item.is-active{
            font-weight: 700;
            font-size: 16px;
        }
    }
    .banner-right {
        display: flex;
        font-size: 16px;
        align-items: center;
        .logo-tel{
            display:flex;
            align-items: center;
        }
        .menus {
            display: flex;
            margin-left: 50px;

            .menu-item {
                margin-right: 30px;
                cursor: pointer;
                &:last-of-type {
                    margin-right: 0;
                }
            }
        }
    }

}
.banner {
    width: 100%;
    min-width: 1400px;
    height: 340px;
    background: url('../../assets/images/homepage/banner-bg.jpg') no-repeat center top;
    background-size: cover;
    .banner-inner{
        // width:1400px;
    }
    .banner-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        // height: 80px;
        height:58px;
        margin: 0 auto;
        .banner-left{
            display:flex;
            // height:58px;
            // align-items: center;
            img{
                margin-top:20px;
            }
            .tabs-box{
                display:flex;
                align-items: center;
                height:58px;
            }
            .el-tabs{
                height:40px;
                margin-left:68px;
            }
            /deep/.el-tabs__header{
                margin:0;
            }
            /deep/.el-tabs__nav-wrap::after{
                background:transparent;
            }
            /deep/.el-tabs__item{
                font-size: 16px;
            }
            /deep/.el-tabs__item.is-active{
                font-weight: 700;
                font-size: 16px;
            }
        }
        .banner-right {
            display: flex;
            font-size: 16px;
            align-items: center;

            .logo-tel{
                display:flex;
                align-items: center;
            }
            .menus {
                display: flex;
                margin-left: 50px;

                .menu-item {
                    cursor: pointer;
                    margin-right: 30px;

                    &:last-of-type {
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .banner-desc {
        // height: 132px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 40px;
        margin: 0 auto;
        padding-top: 100px;
        width: 1200px;
        color: #232425;
        display: flex;
        align-items: center;

        b {
            font-family: YouSheBiaoTiHei;
            font-size: 56px;
            color: #222222;
            background: -webkit-linear-gradient(left, #ff7e5f, #feb47b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            text-shadow: 5px 8px 9px #0054d238;
        }
    }

    .banner-button {
        width: 200px;
        height: 64px;
        box-sizing: border-box;
        background: url('../../assets/images/homepage/banner-button.png') no-repeat center;
        background-size: cover;
        margin: 0 auto;
        margin-top: 97px;
        cursor: pointer;
        line-height: 64px;
        padding-left: 40px;
        font-size: 20px;
        color: #FFFFFF;
    }
}
// 底部样式
.footer-section {
    background-color: #f5f5f6;
    // padding: 20px 0;
    width: 100%;
    min-width: 1400px;
    .container {
        width: 1400px;
        margin: 0 auto;
        // display: flex;
        justify-content: space-between;

        .footer-left-title {
            width: 64px;
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            margin-bottom: 12px;
            color: #191919;
        }

        .footer-left {
            p {
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #595959;
                line-height: 40px;
                display: flex;
                align-items: center;

                &.address i {
                    background: url('../../assets/images/homepage/position.png') no-repeat;
                    background-size: cover;
                }

                &.tel i {
                    background: url('../../assets/images/homepage/telephone.png') no-repeat;
                    background-size: cover;
                }

                &.email i {
                    background: url('../../assets/images/homepage/email.png') no-repeat;
                    background-size: cover;
                }
            }

            i {
                display: block;
                width: 14px;
                height: 14px;
                margin-right: 10px;

            }
        }

        >div {
            flex: 1;
            margin-right: 20px;

            &:last-child {
                margin-right: 0;
            }


        }
    }

    .footer-main {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .footer-middle {
        height: 144px;

        p {
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            margin-bottom: 24px;
        }

        .download-btn {
            width: 149px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            border: 1px solid #0054d2;
            border-radius: 4px;
            border-radius: 4px;
            text-decoration: none;
            display: flex;
            align-items: center;
            text-align: center;
            box-sizing: border-box;
            padding: 0 10px;
            // justify-content: center;
            margin-bottom: 20px;
            color: #0054d2;

            &:hover {
                opacity: 0.8;
            }

            i {
                display: block;
                margin-right: 10px;
            }

            .ios-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/ios.png") no-repeat;
                background-size: cover;
            }

            .windows-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/win.png") no-repeat;
                background-size: cover;
            }
        }
    }

    .footer-right {
        height: 144px;
        font-weight: 400;
        font-size: 16px;
        color: #191919;
        display: flex;
        gap: 30px;
    }

    .qrcode-box {
        p {
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            height: 24px;
            margin-bottom: 15px;
            text-align: center;
        }
    }

    .qrcode {
        width: 118px;
        height: 118px;
        background: #ffffff00;
        border: 1px solid #DEDEDE;
        border-radius: 2px;
        text-align: center;

        img {
            width: 100px;
            height: 100px;
            display: block;
            margin-top: 9px;
            margin-left: 9px;
        }
    }

    .copyright {
        width: 100%;
        height:48px;
        line-height:48px;
        text-align: center;
        // margin-top: 75px;
        color: #898e96;
        font-size: 12px;
        position: inherit;
        a{
            color: #898e96;
            text-decoration: none;
        }
    }
}

</style>
