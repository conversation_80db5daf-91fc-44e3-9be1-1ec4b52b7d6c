name: Acceptance tests (CPython + PyPy)

on:
  push:
    branches:
      - main
      - master
      - v*-maintenance
    paths:
      - '.github/workflows/**'
      - 'src/**'
      - 'atest/**'
      - '!**/*.rst'

jobs:
  test_using_builtin_python:

    strategy:
      fail-fast: false
      matrix:
        os: [ 'ubuntu-latest', 'windows-latest' ]
        python-version: [ '3.8', '3.9', '3.10', '3.11', '3.12', '3.13', 'pypy-3.10' ]
        include:
          - os: ubuntu-latest
            set_display: export DISPLAY=:99; Xvfb :99 -screen 0 1024x768x24 -ac -noreset & sleep 3
          - os: windows-latest
            set_codepage: chcp 850
        exclude:
          - os: windows-latest
            python-version: 'pypy-3.10'

    runs-on: ${{ matrix.os }}

    name: Python ${{ matrix.python-version }} on ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup python for starting the tests
        uses: actions/setup-python@v5.6.0
        with:
          python-version: '3.13'
          architecture: 'x64'

      - name: Get test starter Python at Windows
        run: echo "ATEST_PYTHON=$((get-command python.exe).Path)" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        if: runner.os == 'Windows'

      - name: Get test starter Python
        run: echo "ATEST_PYTHON=$(which python)" >> $GITHUB_ENV
        if: runner.os != 'Windows'

      - name: Setup python ${{ matrix.python-version }} for running the tests
        uses: actions/setup-python@v5.6.0
        with:
          python-version: ${{ matrix.python-version }}
          architecture: 'x64'

      - name: Get test runner Python at Windows
        run: echo "BASE_PYTHON=$((get-command python.exe).Path)" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        if: runner.os == 'Windows'

      - name: Get test runner Python
        run: echo "BASE_PYTHON=$(which python)" >> $GITHUB_ENV
        if: runner.os != 'Windows'

      - name: Install Ubuntu PyPy dependencies
        run: |
          sudo apt-get update
          sudo apt-get -y -q install libxslt-dev libxml2-dev
        if: contains(matrix.python-version, 'pypy') && contains(matrix.os, 'ubuntu')

      - name: Install screen and other required libraries to Linux
        run: |
          sudo apt-get update
          sudo apt-get -y -q install xvfb scrot libxml2-dev libxslt1-dev
        if: contains(matrix.os, 'ubuntu')

      - name: Run acceptance tests
        run: |
          python -m pip install -r atest/requirements.txt
          ${{ env.ATEST_PYTHON }} -m pip install -r atest/requirements-run.txt
          ${{ matrix.set_codepage }}
          ${{ matrix.set_display }}
          ${{ env.ATEST_PYTHON }} atest/run.py --interpreter ${{ env.BASE_PYTHON }} --exclude no-ci ${{ matrix.atest_args }} atest/robot

      - name: Archive acceptances test results
        uses: actions/upload-artifact@v4
        with:
          name: at-results-${{ matrix.python-version }}-${{ matrix.os }}
          path: atest/results
        if: always() && job.status == 'failure'

      - name: Install and run rflogs
        if: failure()
        env:
          RFLOGS_API_KEY: ${{ secrets.RFLOGS_API_KEY }}
        working-directory: atest/results
        shell: python
        run: |
          import os
          import glob
          import subprocess

          # Install rflogs
          subprocess.check_call(["pip", "install", "rflogs"])

          # Find the first directory containing log.html
          log_files = glob.glob("**/log.html", recursive=True)
          if log_files:
              result_dir = os.path.dirname(log_files[0])
              print(f"Result directory: {result_dir}")

              # Construct the rflogs command
              cmd = [
                  "rflogs", "upload",
                  "--tag", f"workflow:${{ github.workflow }}",
                  "--tag", f"os:${{ runner.os }}",
                  "--tag", f"python-version:${{ matrix.python-version }}",
                  "--tag", f"branch:${{ github.head_ref || github.ref_name }}",
                  result_dir
              ]

              # Run rflogs upload
              subprocess.check_call(cmd)
          else:
              print("No directory containing log.html found")
              exit(1)
