from typing import List, Dict, Any, Optional

from loguru import logger

from core.executor import Agent, AgentMetadata, ActionManager
from core.model import ActionNode, ActionOptions
from core.workflow_validation import WorkflowValidator
from models.workflow import WorkflowData, WorkflowNode, WorkflowEdge, WorkflowMetadata


class WorkflowTransfer:

    @staticmethod
    def transfer(actions: ActionManager, workflow: WorkflowData, task_id: str) -> Agent:
        validate_result = WorkflowValidator.validate(actions, workflow)
        if not validate_result.is_valid:
            raise ValueError(f"工作流不能通过校验,err={validate_result.errors}")

        action_node_map = WorkflowTransfer._organize_workflow(workflow)

        metadata = WorkflowTransfer._organize_metadata(workflow.metadata)

        variables = WorkflowTransfer._organize_variables(workflow.variables)

        agent = Agent(
            node_map=action_node_map,
            variables=variables,
            metadata=metadata,
            task_id=task_id,
        )

        return agent

    @staticmethod
    def _organize_workflow(workflow: WorkflowData):

        action_node_map: Dict[str, ActionNode] = {}

        for node in workflow.nodes:
            action_node = WorkflowTransfer._transfer_node(node)
            action_node_map[action_node.id] = action_node

        err = WorkflowTransfer._generate_topology(action_node_map, workflow.edges)

        if err is not None:
            raise ValueError(f"组织工作流错误,err={err}")

        return action_node_map

    @staticmethod
    def _organize_metadata(metadata: WorkflowMetadata) -> AgentMetadata:
        agent_metadata = AgentMetadata()
        agent_metadata.name = metadata.name
        agent_metadata.description = metadata.description
        agent_metadata.version = metadata.version
        agent_metadata.created_at = metadata.createdAt
        agent_metadata.updated_at = metadata.updatedAt

        return agent_metadata

    @staticmethod
    def _organize_variables(variables: List[Dict[str, Any]]) -> Dict[str, Any]:
        variable_map: Dict[str, ActionNode] = {}

        for variable in variables:
            name = variable.get("name", "")
            if name == "":
                logger.warning(f"变量管理中有name为空变量跳过{variable}")
                continue
            value = variable.get("value", None)
            if value is None:
                logger.warning(f"变量管理中有value为空变量跳过{variable}")
                continue
            typ = variable.get("type", None)
            if typ is None:
                typ = "string"
                logger.info(f"变量管理中有type为空变量使用默认string")

            v = WorkflowTransfer._format_variable(name, value, typ)

            variable_map[name] = v

        return variable_map

    @staticmethod
    def _format_variable(name: str, value: str, typ: str) -> Optional[Any]:
        # TODO 没有实现
        return value

    @staticmethod
    def _generate_topology(
        action_node_map: Dict[str, ActionNode], edges: List[WorkflowEdge]
    ) -> Optional[str]:
        for edge in edges:
            action_node = action_node_map.get(edge.source, None)
            if action_node is None:
                return f"无效的edge{edge.source},source对应不到node"
            if edge.sourceHandle is not None:
                if edge.sourceHandle not in action_node.next_condition_nodes:
                    action_node.next_condition_nodes[edge.sourceHandle] = [edge.target]
                else:
                    action_node.next_condition_nodes[edge.sourceHandle].append(
                        edge.target
                    )
                action_node.is_condition_node = True
            else:
                action_node.next_nodes.append(edge.target)

        for edge in edges:
            action_node = action_node_map.get(edge.target, None)
            if action_node is None:
                return f"无效的edge{edge.target},target对应不到node"
            action_node.previous_nodes.append(edge.source)

        return None

    @staticmethod
    def _transfer_node(node: WorkflowNode) -> ActionNode:

        action_node = ActionNode()

        action_node.id = node.id
        action_node.type = node.type
        action_node.label = node.data.label
        action_node.category = node.data.category
        action_node.description = node.data.description
        action_node.componentType = node.data.componentType
        action_node.inputs = node.data.inputs
        action_node.outputs = node.data.outputs
        action_node.config = node.data.config
        action_node.options = ActionOptions(**node.data.config)

        return action_node
