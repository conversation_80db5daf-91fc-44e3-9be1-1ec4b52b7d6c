{"specversion": 3, "name": "DataTypesLibrary", "doc": "<p>This Library has Data Types.</p>\n<p>It has some in <code>__init__</code> and others in the <a href=\"#Keywords\" class=\"name\">Keywords</a>.</p>\n<p>The DataTypes are the following that should be linked. <span class=\"name\">HttpCredentials</span> , <a href=\"#type-GeoLocation\" class=\"name\">GeoLocation</a> , <a href=\"#type-Small\" class=\"name\">Small</a> and <a href=\"#type-AssertionOperator\" class=\"name\">AssertionOperator</a>.</p>", "version": "", "generated": "2023-12-08T12:59:03+00:00", "type": "LIBRARY", "scope": "TEST", "docFormat": "HTML", "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 88, "tags": [], "inits": [{"name": "__init__", "args": [{"name": "credentials", "type": {"name": "Small", "typedoc": "Small", "nested": [], "union": false}, "defaultValue": "one", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "credentials: Small = one"}], "returnType": null, "doc": "<p>This is the init Docs.</p>\n<p>It links to <a href=\"#Set%20Location\" class=\"name\">Set Location</a> keyword and to <a href=\"#type-GeoLocation\" class=\"name\">GeoLocation</a> data type.</p>", "shortdoc": "This is the init Docs.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 97}], "keywords": [{"name": "Assert Something", "args": [{"name": "value", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "value"}, {"name": "operator", "type": {"name": "Union", "typedoc": null, "nested": [{"name": "AssertionOperator", "typedoc": "AssertionOperator", "nested": [], "union": false}, {"name": "None", "typedoc": "None", "nested": [], "union": false}], "union": true}, "defaultValue": "None", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "operator: AssertionOperator | None = None"}, {"name": "exp", "type": {"name": "str", "typedoc": "string", "nested": [], "union": false}, "defaultValue": "something?", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "exp: str = something?"}], "returnType": null, "doc": "<p>This links to <a href=\"#type-AssertionOperator\" class=\"name\">AssertionOperator</a> .</p>\n<p>This is the next Line that links to <a href=\"#Set%20Location\" class=\"name\">Set Location</a> .</p>", "shortdoc": "This links to `AssertionOperator` .", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 107}, {"name": "Custom", "args": [{"name": "arg", "type": {"name": "CustomType", "typedoc": "CustomType", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg: CustomType"}, {"name": "arg2", "type": {"name": "CustomType2", "typedoc": "CustomType2", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg2: CustomType2"}, {"name": "arg3", "type": {"name": "CustomType", "typedoc": "CustomType", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg3: CustomType"}, {"name": "arg4", "type": {"name": "Unknown", "typedoc": null, "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg4: Unknown"}], "returnType": null, "doc": "", "shortdoc": "", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 134}, {"name": "Funny Unions", "args": [{"name": "funny", "type": {"name": "Union", "typedoc": null, "nested": [{"name": "bool", "typedoc": "boolean", "nested": [], "union": false}, {"name": "int", "typedoc": "integer", "nested": [], "union": false}, {"name": "float", "typedoc": "float", "nested": [], "union": false}, {"name": "str", "typedoc": "string", "nested": [], "union": false}, {"name": "AssertionOperator", "typedoc": "AssertionOperator", "nested": [], "union": false}, {"name": "Small", "typedoc": "Small", "nested": [], "union": false}, {"name": "GeoLocation", "typedoc": "GeoLocation", "nested": [], "union": false}, {"name": "None", "typedoc": "None", "nested": [], "union": false}], "union": true}, "defaultValue": "equal", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "funny: bool | int | float | str | AssertionOperator | Small | GeoLocation | None = equal"}], "returnType": {"name": "Union", "typedoc": null, "nested": [{"name": "int", "typedoc": "integer", "nested": [], "union": false}, {"name": "List", "typedoc": "list", "nested": [{"name": "int", "typedoc": "integer", "nested": [], "union": false}], "union": false}], "union": true}, "doc": "", "shortdoc": "", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 114}, {"name": "Set Location", "args": [{"name": "location", "type": {"name": "GeoLocation", "typedoc": "GeoLocation", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "location: GeoLocation"}], "returnType": {"name": "bool", "typedoc": "boolean", "nested": [], "union": false}, "doc": "", "shortdoc": "", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 104}, {"name": "Typing Types", "args": [{"name": "list_of_str", "type": {"name": "List", "typedoc": "list", "nested": [{"name": "str", "typedoc": "string", "nested": [], "union": false}], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "list_of_str: List[str]"}, {"name": "dict_str_int", "type": {"name": "Dict", "typedoc": "dictionary", "nested": [{"name": "str", "typedoc": "string", "nested": [], "union": false}, {"name": "int", "typedoc": "integer", "nested": [], "union": false}], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "dict_str_int: Dict[str, int]"}, {"name": "whatever", "type": {"name": "Any", "typedoc": "Any", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "whatever: Any"}, {"name": "args", "type": {"name": "List", "typedoc": "list", "nested": [{"name": "Any", "typedoc": "Any", "nested": [], "union": false}], "union": false}, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*args: List[Any]"}], "returnType": null, "doc": "", "shortdoc": "", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 128}, {"name": "X Literal", "args": [{"name": "arg", "type": {"name": "Literal", "typedoc": "Literal", "nested": [{"name": "1", "typedoc": null, "nested": [], "union": false}, {"name": "'xxx'", "typedoc": null, "nested": [], "union": false}, {"name": "b'yyy'", "typedoc": null, "nested": [], "union": false}, {"name": "True", "typedoc": null, "nested": [], "union": false}, {"name": "None", "typedoc": null, "nested": [], "union": false}, {"name": "one", "typedoc": null, "nested": [], "union": false}], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg: Literal[1, 'xxx', b'yyy', True, None, one]"}], "returnType": null, "doc": "", "shortdoc": "", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py", "lineno": 131}], "typedocs": [{"type": "Standard", "name": "Any", "doc": "<p>Any value is accepted. No conversion is done.</p>", "usages": ["Typing Types"], "accepts": ["Any"]}, {"type": "Enum", "name": "AssertionOperator", "doc": "<p>This is some Doc</p>\n<p>This has was defined by assigning to __doc__.</p>", "usages": ["Assert Something", "Funny Unions"], "accepts": ["string"], "members": [{"name": "equal", "value": "=="}, {"name": "==", "value": "=="}, {"name": "<", "value": "<"}, {"name": ">", "value": ">"}, {"name": "<=", "value": "<="}, {"name": ">=", "value": ">="}]}, {"type": "Standard", "name": "boolean", "doc": "<p>Strings <code>TRUE</code>, <code>YES</code>, <code>ON</code> and <code>1</code> are converted to Boolean <code>True</code>, the empty string as well as strings <code>FALSE</code>, <code>NO</code>, <code>OFF</code> and <code>0</code> are converted to Boolean <code>False</code>, and the string <code>NONE</code> is converted to the Python <code>None</code> object. Other strings and other accepted values are passed as-is, allowing keywords to handle them specially if needed. All string comparisons are case-insensitive.</p>\n<p>Examples: <code>TRUE</code> (converted to <code>True</code>), <code>off</code> (converted to <code>False</code>), <code>example</code> (used as-is)</p>", "usages": ["Funny Unions", "Set Location"], "accepts": ["string", "integer", "float", "None"]}, {"type": "Custom", "name": "CustomType", "doc": "<p>Converter method doc is used when defined.</p>", "usages": ["Custom"], "accepts": ["string", "integer"]}, {"type": "Custom", "name": "CustomType2", "doc": "<p>Class doc is used when converter method has no doc.</p>", "usages": ["Custom"], "accepts": []}, {"type": "Standard", "name": "dictionary", "doc": "<p>Strings must be Python <a href=\"https://docs.python.org/library/stdtypes.html#dict\">dictionary</a> literals. They are converted to actual dictionaries using the <a href=\"https://docs.python.org/library/ast.html#ast.literal_eval\">ast.literal_eval</a> function. They can contain any values <code>ast.literal_eval</code> supports, including dictionaries and other containers.</p>\n<p>If the type has nested types like <code>dict[str, int]</code>, items are converted to those types automatically. This in new in Robot Framework 6.0.</p>\n<p>Examples: <code>{'a': 1, 'b': 2}</code>, <code>{'key': 1, 'nested': {'key': 2}}</code></p>", "usages": ["Typing Types"], "accepts": ["string", "Mapping"]}, {"type": "Standard", "name": "float", "doc": "<p>Conversion is done using Python's <a href=\"https://docs.python.org/library/functions.html#float\">float</a> built-in function.</p>\n<p>Starting from RF 4.1, spaces and underscores can be used as visual separators for digit grouping purposes.</p>\n<p>Examples: <code>3.14</code>, <code>2.9979e8</code>, <code>10 000.000 01</code></p>", "usages": ["Funny Unions"], "accepts": ["string", "Real"]}, {"type": "TypedDict", "name": "GeoLocation", "doc": "<p>Defines the geolocation.</p>\n<ul>\n<li><code>latitude</code> Latitude between -90 and 90.</li>\n<li><code>longitude</code> Longitude between -180 and 180.</li>\n<li><code>accuracy</code> <b>Optional</b> Non-negative accuracy value. Defaults to 0.</li>\n</ul>\n<p>Example usage: <code>{'latitude': 59.95, 'longitude': 30.31667}</code></p>", "usages": ["Funny Unions", "Set Location"], "accepts": ["string", "Mapping"], "items": [{"key": "longitude", "type": "float", "required": true}, {"key": "latitude", "type": "float", "required": true}, {"key": "accuracy", "type": "float", "required": false}]}, {"type": "Standard", "name": "integer", "doc": "<p>Conversion is done using Python's <a href=\"https://docs.python.org/library/functions.html#int\">int</a> built-in function. Floating point numbers are accepted only if they can be represented as integers exactly. For example, <code>1.0</code> is accepted and <code>1.1</code> is not.</p>\n<p>Starting from RF 4.1, it is possible to use hexadecimal, octal and binary numbers by prefixing values with <code>0x</code>, <code>0o</code> and <code>0b</code>, respectively.</p>\n<p>Starting from RF 4.1, spaces and underscores can be used as visual separators for digit grouping purposes.</p>\n<p>Examples: <code>42</code>, <code>-1</code>, <code>0b1010</code>, <code>10 000 000</code>, <code>0xBAD_C0FFEE</code></p>", "usages": ["Funny Unions", "Typing Types"], "accepts": ["string", "float"]}, {"type": "Standard", "name": "list", "doc": "<p>Strings must be Python <a href=\"https://docs.python.org/library/stdtypes.html#list\">list</a> literals. They are converted to actual lists using the <a href=\"https://docs.python.org/library/ast.html#ast.literal_eval\">ast.literal_eval</a> function. They can contain any values <code>ast.literal_eval</code> supports, including lists and other containers.</p>\n<p>If the type has nested types like <code>list[int]</code>, items are converted to those types automatically. This in new in Robot Framework 6.0.</p>\n<p>Examples: <code>['one', 'two']</code>, <code>[('one', 1), ('two', 2)]</code></p>", "usages": ["Funny Unions", "Typing Types"], "accepts": ["string", "Sequence"]}, {"type": "Standard", "name": "Literal", "doc": "<p>Only specified values are accepted. Values can be strings, integers, bytes, Booleans, enums and None, and used arguments are converted using the value type specific conversion logic.</p>\n<p>Strings are case, space, underscore and hyphen insensitive, but exact matches have precedence over normalized matches.</p>", "usages": ["X Literal"], "accepts": ["Any"]}, {"type": "Standard", "name": "None", "doc": "<p>String <code>NONE</code> (case-insensitive) is converted to Python <code>None</code> object. Other values cause an error.</p>", "usages": ["Assert Something", "Funny Unions"], "accepts": ["string"]}, {"type": "Enum", "name": "Small", "doc": "<p>This is the Documentation.</p>\n<p>This was defined within the class definition.</p>", "usages": ["__init__", "Funny Unions"], "accepts": ["string", "integer"], "members": [{"name": "one", "value": "1"}, {"name": "two", "value": "2"}, {"name": "three", "value": "3"}, {"name": "four", "value": "4"}]}, {"type": "Standard", "name": "string", "doc": "<p>All arguments are converted to Unicode strings.</p>", "usages": ["Assert Something", "Funny Unions", "Typing Types"], "accepts": ["Any"]}]}