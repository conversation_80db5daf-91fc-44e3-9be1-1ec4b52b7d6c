# keywords/decode_exec.py
import subprocess
import tempfile
import os
from robot.api import logger
from typing import Tuple, Optional


def run_javascript(
        code=None,
        script_file=None,
        node_path="node",
        timeout=60,
        capture_output=True,
        working_directory=None,
        arguments=""
):
    """
    执行 JavaScript 代码，优先使用 Node.js，否则自动回退到 js2py（纯 Python）。

    参数：
    - code: JS 代码字符串
    - script_file: JS 文件路径
    - node_path: Node.js 可执行文件路径（若为 None，则尝试自动检测或回退 js2py）
    - timeout: 超时时间（秒，仅 Node.js 有效）
    - capture_output: 是否捕获输出（仅 Node.js 有效）
    - working_directory: 工作目录（仅 Node.js 有效）
    - arguments: 传给脚本的命令行参数（仅 Node.js 有效）

    返回：
    - stdout: 标准输出
    - stderr: 错误输出
    - return_code: 返回码（Node.js 有效，js2py 固定返回 0）
    """
    # 检查输入是否合法：不能同时传 code 和 script_file，至少需要提供一个
    if code and script_file:
        raise ValueError("不能同时指定 JavaScript 代码和脚本文件，请选择其一")
    if not code and not script_file:
        raise ValueError("必须提供 JavaScript 代码或脚本文件")

    # 如果用户显式指定了 node_path，强制使用 Node.js
    if node_path is not None and node_path != "":
        return _run_with_node(
            code, script_file, node_path, timeout, capture_output, working_directory, arguments
        )

    # 如果 Node.js 不可用，回退到 js2py
    return _run_with_js2py(code, script_file)
def _run_with_node(
        code=None,
        script_file=None,
        node_path="node",
        timeout=60,
        capture_output=True,
        working_directory=None,
        arguments=""
):
    """
    使用 Node.js 执行 JavaScript 脚本或代码片段，并返回 stdout、stderr 和返回码。

    参数说明：
    - code: JS 代码片段（字符串形式）
    - script_file: JS 脚本文件路径（字符串形式）
    - node_path: Node.js 可执行文件路径，默认是 "node"
    - timeout: 执行超时时间（秒）
    - capture_output: 是否捕获标准输出和错误输出
    - working_directory: 脚本执行的工作目录
    - arguments: 传给脚本的命令行参数（字符串形式，空格分隔）

    返回：
    - stdout: JS 标准输出（尝试转码后）
    - stderr: JS 错误输出
    - return_code: Node.js 执行返回码
    """

    # 检查输入是否合法：不能同时传 code 和 script_file，至少需要提供一个
    if code and script_file:
        raise ValueError("不能同时指定 JavaScript 代码和脚本文件，请选择其一")
    if not code and not script_file:
        raise ValueError("必须提供 JavaScript 代码或脚本文件")

    temp_path = None  # 临时文件路径
    script_to_run = None

    # 如果是代码片段，则写入临时文件
    if code:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".js", mode="w", encoding="utf-8") as f:
            f.write(code)
            temp_path = f.name
            script_to_run = temp_path
    else:
        # 检查脚本文件是否存在
        if not os.path.exists(script_file):
            raise FileNotFoundError(f"脚本文件不存在: {script_file}")
        script_to_run = script_file

    # 构建 Node.js 命令
    cmd = [node_path, script_to_run]
    if arguments:
        cmd += arguments.split()  # 拆分参数字符串

    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            capture_output=capture_output,
            text=True,
            timeout=timeout,
            cwd=working_directory or None
        )
    finally:
        # 如果使用了临时文件，确保其被清理
        if temp_path and os.path.exists(temp_path):
            os.remove(temp_path)

    # 获取执行结果
    stdout = result.stdout
    stderr = result.stderr
    return_code = result.returncode

    # 尝试进行多层编码修复（解决中文或转义字符乱码）
    try:
        stdout = stdout.encode().decode('unicode_escape').encode('latin-1').decode('utf-8')
    except (UnicodeDecodeError, UnicodeEncodeError, LookupError) as e:
        logger.debug(f"JS 输出编码转换失败，保留原样: {e}")

    return stdout.strip(), stderr.strip(), return_code

def _run_with_js2py(
    code: str = None,
    script_file: str = None,
) -> Tuple[str, str, int]:
    """使用 js2py 执行 JavaScript（纯 Python 模式）"""
    import js2py

    if script_file:
        with open(script_file, "r", encoding="utf-8") as f:
            code = f.read()

    try:
        context = js2py.EvalJs()
        result = context.eval(code)
        output = str(result)
        error = ""
        return_code = 0
    except Exception as e:
        output = ""
        error = str(e)
        return_code = 1

    return output, error, return_code