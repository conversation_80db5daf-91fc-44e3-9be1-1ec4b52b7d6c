{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "在这里，您可以找到适用于 Chromium 的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "D:\\工作文件\\开发项目\\WimTask\\WimTask\\workflow-backend\\plugins\\browsers\\chromium-1169\\chrome-win\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "hlgicibjoohooiodfmcklnhkdpabgime": {"account_extension_type": 0, "active_permissions": {"api": ["tabs", "sidePanel"], "explicit_host": ["http://127.0.0.1/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "commands": {}, "content_settings": [], "creation_flags": 38, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["tabs", "sidePanel"], "explicit_host": ["http://127.0.0.1/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 8, "newAllowFileAccess": true, "open_side_panel_on_icon_click": true, "path": "D:\\工作文件\\开发项目\\WimTask\\WimTask\\workflow-backend\\experimental\\workflow-use\\workflow-use-main\\extension\\.output\\chrome-mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "0.0.0"}, "serviceworkerevents": ["tabs.onActivated", "tabs.onCreated", "tabs.onRemoved", "tabs.onUpdated"], "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "D:\\工作文件\\开发项目\\WimTask\\WimTask\\workflow-backend\\plugins\\browsers\\chromium-1169\\chrome-win\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "F24FC32E52270EBE53CF2279A2AEF9025B51C999EAB72985614E2A45024C6FAB"}, "default_search_provider_data": {"template_url_data": "A3536F24CD7CD9C12CDA017F5B33787A2AF0F02FAE1CB470DB5AD665FBDFEE34"}, "enterprise_signin": {"policy_recovery_token": "1A99886E839E1BEFBCEE64036F8CF05DF2D0D0E1E35FED0BA164A4834C21001C"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "2D552465BCE3CF864472705573FD0908547BE27F21D436F357E29A64FD4E464A", "hlgicibjoohooiodfmcklnhkdpabgime": "92537A711ABCEBA091172F0AF7DB0690E2209812E14BBEC2F31121D556B243C3", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "5A60D4198FB1C42637E33498F8BA0B900F45FF8A90CE0016EEEB683B6B6DD235"}, "ui": {"developer_mode": "0EB47CA4B74C8C22165BCE88DF597F98B8B3874236CC8366DEB7E4B2F3E0E0EE"}}, "google": {"services": {"account_id": "ACFD40EB427390CF54BFC0F61DD7E44833848055CAC3968D661C8F7A55AD11E8", "last_signed_in_username": "1711E9845DAD695893C5F943FE70778906308D6CF711C1606E99AACB4C66AB45", "last_username": "E08BF9841932C9D8F196CA89ADDA644861C68D607F808AE3AC364D71C1303146"}}, "homepage": "7BCFA7AEFB3E0CC1FFB8F876CD6C9D9B420429FAFFD7704A80BC3FD8C97AEE30", "homepage_is_newtabpage": "04D3560709C5E34116977CAB60327E6E446E44B9648F0B3BA9A682C009AF30B7", "media": {"cdm": {"origin_data": "6355F3AEBCEB266CB18AA3217DB11D91A9347C154B509D7E3BC193E966662F2F"}, "storage_id_salt": "9B8902B3F8EC4749A40EF3A3D76153964AAA44ADDDA0A52358DED650CF6378BA"}, "pinned_tabs": "76429DB7A5B4EDC3CC796BE54619BC15132B481EDEEBACC0BD3BE9271FD25396", "prefs": {"preference_reset_time": "F68E359D4577824C5AC161422F689C09252899132584924C5EF72BFAEB3DF1F6"}, "safebrowsing": {"incidents_sent": "4C5CB64433CDEF96DB29D36CE9DB33FA13571301B03F1A84B64DEC8E890C57FA"}, "search_provider_overrides": "EE7A360A6DEF6BEB0B776C0113E35AE5E050D56B148165F7B583DE20FEFE24AB", "session": {"restore_on_startup": "6C6676FD6CE9D715119C0500D9E4D5E7AF2A3AB260CB5E2584B7C1A0DCE0BC9A", "startup_urls": "E393471D06A79DB4DA99C39096759AD55CC7003E24E96A8F6C4BF8AD5A050DFC"}}, "super_mac": "687AF00460290E7D477E2122F5592F0284F6B983D5B65B1BD2FE4B1A9EFB585D"}}