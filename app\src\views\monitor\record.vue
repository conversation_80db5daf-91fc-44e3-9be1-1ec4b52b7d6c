<template>
  <div class="record">
    <van-empty description="无执行记录" v-if="!isLoading && !props.recordData.length"/>
    <div class="el-timeline">
      <div class="el-timeline-item" v-for="(it, index) in props.recordData" :key="index"
           :class="{
        'error-tail': it.state === 'failed',
        'loading-tail': it.state !== 'failed' && it.nodeId !== 'end_node' && index === props.recordData.length - 1
      }">
        <div class="el-timeline-item__tail"></div>
        <div class="el-timeline-item__dot">
          <div class="time-icon-box">
            <div class="time-icon loading"
                 v-if="it.state !== 'failed' && it.nodeId !== 'end_node' && index === props.recordData.length - 1">
              <van-loading class="time-icon-select is-loading" size="24px"/>
            </div>
            <div class="time-icon error" v-else-if="it.state === 'failed'">
              <van-icon class="time-icon-select" name="cross"/>
            </div>
            <div class="time-icon" v-else>
              <van-icon class="time-icon-select" name="success"/>
            </div>
          </div>
          <div class="time-box">
            <div class="time-left">
              <div class="time-time">{{ it.excTime }}</div>
              <!-- 显示重试次数 -->
              <div class="retry-count" v-if="it.failedCount > 0">
                重试 {{ it.failedCount }} 次
              </div>
            </div>
          </div>
        </div>
        <div class="el-timeline-item__wrapper">
          <div class="el-timeline-item__timestamp is-top"></div>
          <div class="el-timeline-item__content">
            <div class="time-content">
              <div class="time-title-box">
                <div class="time-title-box-left">
                  <div class="time-title-icon" :style="{ background: formatColor(it)}">
                    <i :class="formatIcon(it)"></i>
                  </div>
                  <div class="time-title">{{ it.title }}</div>
                </div>
              </div>
              <div class="content-item" v-if="handleContent(it)">
                <div class="item-title">
                  <div class="complete-content" :title="handleContent(it)">
                    {{ handleContent(it) }}
                  </div>
                </div>
              </div>
              <template v-if="it.nodeId !== 'start_node' && it.nodeId !== 'end_node'">
                <div class="content-item flex-column code-content"
                     v-if="hasPuts(it.input)">
                  <div class="item-title font-bold" @click="toggleExpand(it, '_inputs')">
                    输入：
                    <div>
                      <van-icon name="arrow-up" v-if="expanedDict[it.historyId + it.nodeId + '_inputs']" title="收起"/>
                      <van-icon name="arrow-down" v-else title="展开"/>
                    </div>
                  </div>
                  <div v-show="expanedDict[it.historyId + it.nodeId + '_inputs']" style="width: 100%;">
                    <json-viewer
                      :value="jsonViewParse(it.input)"
                      :copyable="{ copyText: '复制', copiedText: '已复制' }"
                      sort
                      boxed
                      show-double-quotes
                      class="my-awesome-json-theme"
                      theme="my-awesome-json-theme"
                    />
                  </div>
                </div>
                <div class="content-item flex-column code-content"
                     v-if="hasPuts(it.output) && it.state !== 'failed' && index < props.recordData.length - 1">
                  <div class="item-title font-bold" @click="toggleExpand(it, '_outputs')">
                    输出：
                    <div>
                      <van-icon name="arrow-up" v-if="expanedDict[it.historyId + it.nodeId + '_outputs']" title="收起"/>
                      <van-icon name="arrow-down" v-else title="展开"/>
                    </div>
                  </div>
                  <div v-show="expanedDict[it.historyId + it.nodeId + '_outputs']" style="width: 100%;">
                    <json-viewer
                      :value="jsonViewParse(it.output)"
                      :copyable="{ copyText: '复制', copiedText: '已复制' }"
                      sort
                      boxed
                      show-double-quotes
                      class="my-awesome-json-theme"
                      theme="my-awesome-json-theme"
                    />
                  </div>
                </div>
              </template>
              <template v-if="it.nodeId !== 'end_node' && index === props.recordData.length - 1">
                <div class="log-loading" v-if="it.state !== 'failed' && !it.failedCount">
                  <van-loading type="spinner" size="16px" color="var(&#45;&#45;van-primary-color)" style="margin-right: 8px;"/>
                  <span>正在执行中...</span>
                </div>
<!--                <div class="log-loading" v-if="it.state === 'failed' || it.failedCount">-->
<!--                  <van-loading type="spinner" size="16px" color="var(&#45;&#45;van-primary-color)" style="margin-right: 8px;"/>-->
<!--                  <span>执行失败，重试中...</span>-->
<!--                </div>-->
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import JsonViewer from 'vue-json-viewer'

// 接收 props
import {ref} from "vue";
import {getCategoryColor} from "@/utils/componentCategories.ts";
import {generateComponentDescription} from "@/utils/componentDisplay.ts";

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  recordData: {
    type: Object,
    default: () => ([])
  },
  missionDetail: {
    type: Object,
    default: () => ({})
  }
})

// 折叠状态
const expanedDict = ref({})
// 添加 toggleExpand 方法
const toggleExpand = (item: any, key: string) => {
  expanedDict.value[item?.historyId + item?.nodeId + key] =
    !expanedDict.value[item?.historyId + item?.nodeId + key]
}
// 判断是否要显示
const hasPuts = (input: any) => {
  if (typeof input === 'object') {
    return JSON.stringify(input) !== '{}'
  }
  return input && input !== '{}' && input !== 'null'
}


// 格式化下output内容
const jsonViewParse = (data: string) => {
  try {
    return JSON.parse(data)
  } catch (e) {
    return data
  }
}

// 格式化获取node的颜色
const formatColor = (data: object) => {
  const nodes = props.missionDetail?.nodes || []
  const node = nodes.find((it: object) => it.id === data.nodeId)
  return getCategoryColor(node?.data?.category)
}
const formatIcon = (data: object) => {
  const nodes = props.missionDetail?.nodes || []
  const node = nodes.find((it: object) => it.id === data.nodeId)
  if (node?.type === 'start') {
    return 'action-iconfont icon-shouye1'
  }
  if (node?.type === 'end') {
    return 'action-iconfont icon-xunhuan'
  }
  return node ? node.data.icon : 'action-iconfont icon-zujian'
}
const handleContent = (data: object) => {
  const nodes = props.missionDetail?.nodes || []
  const node = nodes.find((it: object) => it.id === data.nodeId)
  let html = ''
  if (node?.data) {
    const config = node.data.config || {}
    config.type = 'taskMonitor'
    html = generateComponentDescription(node, 'taskMonitor')
  }
  if (html.includes('点击配置组件参数')) html = (data.describe || '')
  return html
}

</script>

<style scoped lang="scss">
.record {
  overflow-y: auto;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  //background: #f1f3f5;
}

.el-timeline {
  --el-timeline-node-size-normal: 12px;
  --el-timeline-node-size-large: 14px;
  --el-timeline-node-color: #e4e7ed;
  margin: 0;
  font-size: 14px;
  list-style: none;
  padding: 12px;
  background: #ffffff;
  border-radius: 4px;


  .el-timeline-item__tail {
    position: absolute;
    left: 4px;
    height: 100%;
    top: 12px;
    border-left: 2px solid #17B26A;
  }

  .el-timeline-item__dot {
    position: absolute;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-start;
    z-index: 1;

    .time-icon-box {
      padding: 5px 0;
      background: #fff;
      margin-left: -4px;

      .time-icon {
        width: 14px;
        height: 14px;
        background: #17b26a;
        border: 3px solid #E8F7F0;
        border-radius: 50%;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        box-sizing: content-box;

        .time-icon-select {
          font-size: 10px;
          color: #fff;
          height: 10px;
          display: flex;
          align-items: center;
        }

        &.error {
          background: #ef3f09;
          border: 3px solid #f8c6b8;
        }

        &.loading {
          background: #0099cb;
          border: 3px solid #e8f7f0;
        }

        &.waiting {
          background: #9e9e9e;
          border: 3px solid #dcfbec;
        }
      }
    }

    .time-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 32px;

      .time-left {
        width: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      .time-time {
        font-size: 12px;
        color: #b0b3c0;
        margin-left: 16px;
        flex-shrink: 0;
      }
    }
  }

  .el-timeline-item__wrapper {
    position: relative;
    top: -3px;
    padding-left: 16px;

    .el-timeline-item__timestamp {
      color: #5C5F66;
      line-height: 1;
      font-size: 13px;

      &.is-top {
        margin-bottom: 8px;
        padding-top: 4px;
      }
    }

    .el-timeline-item__content {
      color: #333333;
      margin-top: 38px;

      .time-content {
        padding: 16px;
        box-sizing: border-box;
        border: 1px solid #17B26A;
        border-radius: 8px;
        margin-left: 16px;

        .time-title-box {
          display: flex;
          align-items: center;

          .time-title-box-left {
            flex: 1;
            display: flex;
            align-items: center;

            .time-title-icon {
              width: 24px;
              height: 24px;
              color: #fff;
              font-size: 12px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 8px;
            }

            .time-title {
              font-size: 14px;
              color: #222;
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }

        .content-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 8px 0;
          border-radius: 4px;
          line-height: 18px;

          .item-title {
            width: 100%;
            display: flex;
            align-items: flex-start;
            color: #5c5f66;
          }

          .complete-content {
            font-size: 12px;
            color: #5c5f66;
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-line;
            overflow-wrap: break-word;
            max-width: 100%;
            min-height: 18px;
          }
        }
      }
    }
  }

  .el-timeline-item {
    position: relative;
    padding-bottom: 10px;

    &:last-child {
      .el-timeline-item__tail {
        display: none;
      }
    }

    &.error-tail {
      :deep(.el-timeline-item__tail) {
        border-left: 2px solid #f6693e;
      }

      .time-content {
        border: 1px solid #f6693e;
      }
    }

    &.loading-tail {
      :deep(.el-timeline-item__tail) {
        border-left: 2px solid #0099cb;
      }

      .time-content {
        border: 1px solid #0099cb;
      }
    }

    &.waiting-tail {
      :deep(.el-timeline-item__tail) {
        border-left: 2px solid #9e9e9e;
      }

      .time-content {
        border: 1px solid #9e9e9e;
      }
    }
  }
}

.flex-column {
  flex-direction: column;
}

.font-bold {
  font-weight: bold;
  color: #333333 !important;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: -13px;
  z-index: 7;
  background: #F5F5F5;
}

.code-content {
  background: #f5f5f5;
  border: 1px solid #ebeef5;
  margin: 8px 0 0;
  padding: 0 !important;
}

// values are default one from jv-light template
// 自定义高亮json样式
.my-awesome-json-theme {
  border: none;
  white-space: nowrap;
  color: #333333;
  font-size: 14px;
  font-family: Consolas, Menlo, Courier, monospace;
  font-weight: 600;
  width: 100%;

  &.boxed {
    box-sizing: border-box;
    border: none;
    border-top: 1px solid #e5e5e5;
    border-radius: 0;

    &:hover {
      box-shadow: none;
      border: none;
      border-top: 1px solid #e5e5e5;
    }
  }

  :deep(.jv-tooltip.right) {
    right: 6px;
    font-weight: normal;
    font-size: 12px;
  }

  :deep(.jv-code) {
    padding: 15px 10px;
    overflow: auto;

    .jv-toggle {
      &:before {
        padding: 0px 2px;
        border-radius: 2px;
      }

      &:hover {
        &:before {
          background: #eee;
        }
      }
    }

    .jv-node {
      line-height: 20px;
    }

    .jv-ellipsis {
      color: #999;
      background-color: #eee;
      display: inline-block;
      line-height: 0.9;
      font-size: 0.9em;
      padding: 0px 4px 2px 4px;
      border-radius: 3px;
      vertical-align: 2px;
      cursor: pointer;
      user-select: none;
    }

    .jv-button {
      color: #49b3ff;
    }

    .jv-key {
      color: #92278f;
      margin-right: 8px;
    }

    .jv-item {
      &.jv-array {
        color: #111111;
      }

      &.jv-boolean {
        color: #fc1e70;
      }

      &.jv-function {
        color: #067bca;
      }

      &.jv-number {
        color: #067bca;
      }

      &.jv-number-float {
        color: #067bca;
      }

      &.jv-number-integer {
        color: #067bca;
      }

      &.jv-object {
        color: #111111;
      }

      &.jv-undefined {
        color: #e08331;
      }

      &.jv-string {
        color: #3ab54a;
        word-break: break-word;
        white-space: normal;
      }
    }
  }
}

.log-loading {
  color: var(--van-primary-color);
  display: flex;
  align-items: center;
  padding: 8px 0 0;
  box-sizing: border-box;
}

.retry-count {
  font-size: 12px;
  color: #e6a23c;
  margin-left: 8px;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>
