// 存储所有连接的端口
const connectedPorts: MessagePort[] = []

/**
 * 存储WebSocket消息的Map
 * 将 messageStore 的类型改为嵌套的 Map 结构：Map<string, Map<string, string[]>>
 * 外层 Map 的 key 是 history_id
 * 内层 Map 的 key 是消息类型 (如 'exec', 'progress' 等)
 * 内层 Map 的 value 是该类型下的消息数组
 */
const messageStore = new Map<string, Map<string, string[]>>()

// websocket每次接收到的数据都格式
const demoData = {
  id: '1e656f2f-8da0-422a-84c7-b2a8cc0300cd',
  time: '2025-07-29 20:50:34',
  type: 'exec',
  node_id: 'end_node',
  node_type: 'workflow_end',
  node_name: '结束',
  history_id: 'exec_b39522ae',
  task_id: '7297db51-9d2a-4c5c-9b33-ab791adea0da',
  state: 'progress',
  describe: '工作流的结束节点，标识流程完成',
  inputs: {},
  outputs: {},
  exception: null,
}

// 节流防抖相关变量
let saveTimeout: number | null = null
let lastSaveTime = 0
const THROTTLE_DELAY = 1000 // 节流时间1秒
const DEBOUNCE_DELAY = 3000 // 防抖时间3秒
let pendingSave = false

// 添加WebSocket相关变量
let socket: WebSocket | null = null
let reconnectAttempts = 0 // 断线重连次数
const maxReconnectAttempts = 15 // 断线重连次数上限
let reconnectDelay = 3000 // 断线重连间隔

// 消息广播
const messageBroadcast = (type: string, data: object) => {
  connectedPorts.forEach((p) => {
    p.postMessage({
      type,
      data,
    })
  })
}
// 保存到localStorage的函数（带节流防抖）
const requestStorageSave = () => {
  const now = Date.now()

  // 立即执行条件：距离上次保存超过节流时间，或者有挂起的保存
  if (now - lastSaveTime > THROTTLE_DELAY || pendingSave) {
    executeSave()
  } else {
    // 否则设置防抖延迟保存
    pendingSave = true
    if (saveTimeout) {
      clearTimeout(saveTimeout)
    }
    saveTimeout = setTimeout(executeSave, DEBOUNCE_DELAY) as unknown as number
  }
}

// 实际执行保存的函数
const executeSave = () => {
  const serializableData = Object.fromEntries(
    Array.from(messageStore.entries()).map(([historyId, typeMap]) => [
      historyId,
      Object.fromEntries(typeMap.entries()),
    ]),
  )
  messageBroadcast('SAVE_TO_STORAGE', serializableData)
  lastSaveTime = Date.now()
  pendingSave = false
  if (saveTimeout) {
    clearTimeout(saveTimeout)
    saveTimeout = null
  }
}

// 添加页面关闭前的保存
const handleBeforeUnload = () => {
  if (pendingSave) {
    executeSave()
  }
}

// 初始化时添加事件监听
self.addEventListener('beforeunload', handleBeforeUnload)

// WebSocket初始化函数
const initWebSocket = () => {
  // 关闭现有连接
  if (socket) {
    socket.close()
  }

  // 创建新的WebSocket连接 (请根据实际API地址修改)
  const wsUrl = `ws://localhost:12765`
  try {
    socket = new WebSocket(wsUrl)

    socket.onopen = () => {
      messageBroadcast('WEBSOCKET_MESSAGE', {
        message: `WebSocket连接已建立，地址：${wsUrl}`,
      })
      reconnectAttempts = 0
    }

    socket.onclose = (event) => {
      messageBroadcast('WEBSOCKET_MESSAGE', {
        message: 'WebSocket连接关闭',
        data: event,
      })
      // 重试次数大于限制次数后，增加间隔时间重新开始重试，最大间隔9秒
      if (reconnectAttempts >= maxReconnectAttempts && reconnectDelay < 9000) {
        reconnectAttempts = 0
        reconnectDelay += 3000
      }
      if (reconnectAttempts < maxReconnectAttempts) {
        messageBroadcast('WEBSOCKET_MESSAGE', {
          message: `将在${reconnectDelay / 1000}秒后尝试重新连接...`,
        })
        setTimeout(initWebSocket, reconnectDelay)
        reconnectAttempts++
      }
    }

    socket.onerror = (error) => {
      messageBroadcast('WEBSOCKET_MESSAGE', {
        message: 'WebSocket错误',
        data: JSON.stringify(error),
      })
      // 重试次数大于限制次数后，增加间隔时间重新开始重试，最大间隔9秒
      if (reconnectAttempts >= maxReconnectAttempts && reconnectDelay < 9000) {
        reconnectAttempts = 0
        reconnectDelay += 3000
      }
      if (reconnectAttempts < maxReconnectAttempts) {
        messageBroadcast('WEBSOCKET_MESSAGE', {
          message: `将在${reconnectDelay / 1000}秒后尝试重新连接...`,
        })
        setTimeout(initWebSocket, reconnectDelay)
        reconnectAttempts++
      }
    }

    // 接收websocket消息并存储到
    socket.onmessage = (event) => {
      // messageBroadcast('WEBSOCKET_MESSAGE', {
      //   message: 'WebSocket收到消息',
      //   data: event.data,
      // })

      try {
        const messageData = JSON.parse(event.data)

        // 目前只处理带 history_id 的数据（任务执行的websocket数据）

        const { history_id, node_id } = messageData

        // 如果不存在该history_id的分组，则创建
        if (!messageStore.has(history_id)) {
          messageStore.set(history_id, new Map<string, string[]>())
        }

        const historyGroup = messageStore.get(history_id)!
        const messageType = messageData.type

        // 如果不存在type，不需要存储到本地
        if (!messageType) return

        // 如果不存在该类型的消息数组，则创建
        if (!historyGroup.has(messageType)) {
          historyGroup.set(messageType, [])
        }
        // 获取消息数组并添加新消息
        const messages = historyGroup.get(messageType)!

        // 查找是否已存在相同node_id的消息
        // 只处理exec类型的消息
        const existingIndex = messages.findIndex(msg =>
          typeof msg === 'object' && msg.node_id === node_id
        );

        // 如果存在则替换
        if (existingIndex !== -1) {
          const existingMsg = messages[existingIndex];
          // 保留已有的失败次数
          const failedCount = existingMsg.failedCount || 0;

          // 如果当前状态是failed，则增加重试次数
          if (messageData.state === 'failed') {
            messageData.failedCount = failedCount + 1;
          }
          // 否则保持原有重试次数
          else {
            messageData.failedCount = failedCount;
          }

          messages[existingIndex] = messageData;
        }
        // 不存在则添加
        else {
          messageData.failedCount = 0;

          messages.push(messageData);
        }

        // 如果超过100条，移除第一条
        if (messages.length > 100) {
          messages.shift()
        }

        // 保存到localStorage
        requestStorageSave()

        // 发送消息给所有连接的页面
        messageBroadcast('REFRESH_WEBSOCKET_STORE', {
          message: '刷新websocketStore数据',
          data: Object.fromEntries(
            Array.from(messageStore.entries()).map(([historyId, typeMap]) => [
              historyId,
              Object.fromEntries(typeMap.entries()),
            ]),
          ),
        })
      } catch (e) {
        // messageBroadcast('WEBSOCKET_MESSAGE', {
        //   message: '解析WebSocket消息失败',
        //   data: e,
        // })
      }
    }
  } catch (error) {
    messageBroadcast('WEBSOCKET_MESSAGE', {
      message: 'WebSocket错误',
      data: JSON.stringify(error),
    })
  }
}

/**
 * SharedWorker连接事件处理函数
 * 当页面通过new SharedWorker()创建连接时触发
 *
 * 功能：
 * 1. 管理所有连接的MessagePort
 * 2. 初始化WebSocket连接(首次连接时)
 * 3. 处理来自页面的消息(STORE_MESSAGE/GET_MESSAGE/CLEAR_MESSAGE)
 * 4. 监听端口关闭事件，清理资源
 */
self.onconnect = (e: MessageEvent) => {
  const port = e.ports[0]
  // 检查端口是否已存在，避免重复添加
  if (!connectedPorts.includes(port)) {
    connectedPorts.push(port)
  }

  // 如果这是第一个连接且WebSocket未初始化，则初始化
  if (!socket) {
    initWebSocket()
  }

  // 在onconnect的port.onmessage中添加存储恢复逻辑
  port.onmessage = (event) => {
    const { type, data } = event.data
    switch (type) {
      case 'RESTORE_FROM_STORAGE':
        if (data) {
          try {
            Object.entries(data).forEach(([historyId, typeMap]) => {
              const typedMap = new Map<string, string[]>()
              Object.entries(typeMap as Record<string, string[]>).forEach(([msgType, messages]) => {
                typedMap.set(msgType, messages)
              })
              messageStore.set(historyId, typedMap)
            })
          } catch (e) {}
        }

        // 数据清理逻辑，清理完1天前的数据
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
        // 遍历所有history_id分组
        messageStore.forEach((typeMap, historyId) => {
          // 遍历每个消息类型
          typeMap.forEach((messages, msgType) => {
            // 过滤掉time超过1天的消息
            const filteredMessages = messages.filter((msg) => {
              try {
                const msgData = typeof msg === 'string' ? JSON.parse(msg) : msg
                if (!msgData.time) return false
                const messageTime = new Date(msgData.time)
                return messageTime >= oneDayAgo
              } catch {
                return false
              }
            })
            // 更新消息数组
            typeMap.set(msgType, filteredMessages)
            // 如果该类型消息数组为空，则删除该类型
            if (filteredMessages.length === 0) {
              typeMap.delete(msgType)
            }
          })
          // 如果该history_id分组为空，则删除该分组
          if (typeMap.size === 0) {
            messageStore.delete(historyId)
          }
        })
        // 保存清理后的数据
        requestStorageSave()

        // 发送消息给所有连接的页面
        messageBroadcast('REFRESH_WEBSOCKET_STORE', {
          message: '刷新websocketStore数据',
          data: Object.fromEntries(
            Array.from(messageStore.entries()).map(([historyId, typeMap]) => [
              historyId,
              Object.fromEntries(typeMap.entries()),
            ]),
          ),
        })
        break;
      case 'CHECK_WEBSOCKET_CONNECTION':
        if (!socket || socket.readyState !== WebSocket.OPEN) {
          initWebSocket();
          messageBroadcast('WEBSOCKET_MESSAGE', {
            message: '检测到WebSocket未连接，正在尝试重新连接...'
          });
        }
        else {
          messageBroadcast('WEBSOCKET_MESSAGE', {
            message: '检测到WebSocket已经连接，无需重新建立连接'
          });
        }
        break;
    }
  }

  // 处理端口关闭
  port.addEventListener('close', () => {
    // 删除数据
    const index = connectedPorts.indexOf(port)
    if (index !== -1) {
      connectedPorts.splice(index, 1)
      // 清除后也更新存储
      requestStorageSave()
    }
    // 当所有端口都关闭时，销毁WebSocket连接
    if (connectedPorts.length === 0 && socket) {
      socket.close()
      socket = null
    }
  })
}

// 导出类型
export type {}
