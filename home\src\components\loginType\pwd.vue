<template>
  <div class="dly-pwd-box" v-loading="loading">
    <template v-if="loginStip === 'first'">
      <div class="dly-error-info" v-if="model.err_info">
        <el-icon><Remove /></el-icon>{{ model.err_info }}
      </div>
      <el-form 
        ref="formRef" 
        label-position="top" 
        :model="model" 
        hide-required-asterisk 
        :show-message="false" 
        :validate-on-rule-change="false"
      >
        <el-form-item label="企业" prop="tenantName">
          <el-input 
            v-model.trim="model.tenantName" 
            clearable 
            placeholder="请输入企业名"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="user" required>
          <el-input 
            v-model.trim="model.user" 
            clearable 
            placeholder="请输入用户名" 
            @keyup.enter="onLogin"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="pwd" required>
          <el-input 
            v-model.trim="model.pwd" 
            placeholder="请输入密码" 
            show-password 
            @keyup.enter="onLogin"
          ></el-input>
        </el-form-item>
        <el-form-item 
          label="验证码" 
          prop="vali" 
          required 
          v-if="configs.showVctCode === 1" 
          class="identify-box"
        >
          <i class="login-bg-icon passwordicon"></i>
          <el-input 
            placeholder="请输入验证码" 
            v-model.trim="model.vali" 
            @keyup.enter="onLogin"
          ></el-input>
          <img 
            style="cursor: pointer" 
            class="identify" 
            :src="identifyCode" 
            @click="onRefreshCode" 
            alt="点击切换" 
            v-if="identifyCode" 
          />
        </el-form-item>
      </el-form>
      <div class="dly-frame-toolbar">
        <div class="dly-frame-toolbar__left select-part">
          <el-checkbox 
            v-model="model.remember" 
            v-if="configs.rememberPwd === '1'"
          >
            记住密码
          </el-checkbox>
          <forget-pwd 
            v-if="configs.forgetPwd === '1'" 
            :cid="cid" 
            :configs="configs" 
          />
        </div>
      </div>

      <div class="dly-frame-button">
        <el-button 
          type="primary" 
          @click="onLogin" 
          class="login-btn"
        >
          登录
        </el-button>
      </div>
    </template>

    <login-valid 
      ref="loginValidRef" 
      v-if="loginStip === 'code'" 
      @setLoginStip="setLoginStip" 
      :loginPop="loginPop"
    ></login-valid>

    <div class="dly-button">
      <el-popover 
        width="200" 
        trigger="hover" 
        placement="bottom" 
        show-arrow
        popper-class="app-popover"
        :append-to-body="false" 
        v-if="configs.appDownload === '1'"
      >
        <template #reference>
          <span class="app-part"><span class="browser">获取APP</span></span>
        </template>
        <div v-if="configs.appDownloadPic" class="img-part">
          <img 
            :src="item" 
            alt="" 
            v-for="(item, index) in configs.appDownloadPic.split(',')" 
            :key="'img' + index" 
          />
        </div>
      </el-popover>
      <div class="register-item">
        <span>没有账号，</span>
        <span class="register-btn" @click="loginChange">注册</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, inject } from 'vue';
import JSEncrypt from 'jsencrypt';
import ForgetPwd from './ForgetPwd.vue';
import LoginValid from './loginValid.vue';
import systemApi from "@/api/system";
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";

const userStore = useUserStore()
const loginStore = useLoginStore()

// 类型定义
interface Configs {
  rememberPwd?: string;
  showVctCode?: number;
  forgetPwd?: string;
  appDownload?: string;
  appDownloadPic?: string;
  pwdRuleType?: string;
  pwdRule?: string;
  pwdMinLength?: number;
  weakPasswordSetting?: string;
  loginAccountType?: string[];
}

interface Props {
  configs?: Configs;
  cid?: string | null;
  loginPop?: boolean;
}

interface ModelType {
  user: string | null;
  pwd: string | null;
  vali: string | null;
  tenantId: string | null;
  validShow: boolean;
  err_info: string | null;
  remember: boolean;
  cid: string | null;
  tenantName?: string;
}

interface CrowType {
  opwd: string;
  npwd: string;
  rpwd: string;
}

interface RulesType {
  opwd: {
    required: boolean;
    message: string;
    trigger: string;
  }[];
  npwd: {
    required: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: string, callback: (error?: Error) => void) => void;
  }[];
  rpwd: {
    required: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: string, callback: (error?: Error) => void) => void;
  }[];
}

interface TenantData {
  id: string;
  url: string;
  cloud: string;
  sysType: string;
}

interface LoginResponse {
  Code: number;
  Message?: string;
  Response?: {
    token: string;
    expire: string;
    refreshToken: string;
    tenantId: string;
    twoFactorAuth?: boolean;
    mobile?: string;
    changePwd?: boolean;
  };
}

// 外部依赖类型
interface DlyApi {
  getLoginCode: (timeVal: string) => string;
  getDetailByName: (name: string) => Promise<TenantData[]>;
  saasLogin: (params: any, options?: any) => Promise<LoginResponse>;
  getEncryptKey: () => Promise<{ publicKey: string }>;
}

interface Store {
  dispatch: (action: string) => Promise<void>;
}

// 接收Props
const props = withDefaults(defineProps<Props>(), {
  configs: () => ({}),
  cid: null,
  loginPop: false
});

// 注入外部依赖

// 响应式状态
const formRef = ref<any>(null);
const loginValidRef = ref<InstanceType<typeof LoginValid> | null>(null);
const identifyCode = ref<string | null>(null);
const visible = ref<boolean>(false);
const publicKey = ref<string>('');
const loginType = ref<string>('1-1');
const loginStip = ref<'first' | 'code'>('first');
const loading = ref<boolean>(false);

// 复杂对象状态
const model = reactive<ModelType>({
  user: null,
  pwd: null,
  vali: null,
  tenantId: null,
  validShow: false,
  err_info: null,
  remember: false,
  cid: null
});

const crow = reactive<CrowType>({
  opwd: '',
  npwd: '',
  rpwd: ''
});

// 表单验证规则
const rules = reactive<RulesType>({
  opwd: [
    {
      required: true,
      message: '原始密码不能为空',
      trigger: 'blur'
    }
  ],
  npwd: [
    {
      required: true,
      message: '新密码不能为空',
      trigger: 'blur'
    },
    { validator: checkPassWord, trigger: 'blur' }
  ],
  rpwd: [
    {
      required: true,
      message: '确认密码不能为空',
      trigger: 'blur'
    },
    { validator: validateEq, trigger: 'blur' }
  ]
});

// 验证函数
function checkPassWord(rule: any, value: string, callback: (error?: Error) => void) {
  if (crow.opwd === value) {
    callback(new Error('原始密码与新密码必须不同'));
    return;
  }
  
  if (props.configs.pwdRuleType === '4' && props.configs.pwdRule) {
    if (!new RegExp(props.configs.pwdRule).test(value)) {
      callback(new Error('密码验证规则不通过'));
    } else {
      callback();
    }
    return;
  }
  callback();
}

function validateEq(rule: any, value: string, callback: (error?: Error) => void) {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== crow.npwd) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
}

// 计算属性
const pwdRuleTips = computed<string>(() => {
  let str = `新密码需要符合以下规则：<br/>1.`;
  switch (props.configs.pwdRuleType) {
    case '1':
      str += '字母大写+字母小写+数字组合';
      break;
    case '2':
      str += '字母+数字+特殊字符组合';
      break;
    case '3':
      str += '字母+数字组合';
      break;
    case '4':
      str += props.configs.pwdRule 
        ? `满足正则策略：${props.configs.pwdRule}` 
        : '新密码不能为空';
      break;
    case '5':
      str += '字母大写+字母小写+特殊字符+数字';
      break;
    default:
      str += '新密码不能为空';
      break;
  }
  str += `<br/>2.密码长度不能小于${props.configs.pwdMinLength || 6}`;
  
  if (props.configs.weakPasswordSetting) {
    str += `<br/>3.不能设置的密码名单有：${props.configs.weakPasswordSetting}`;
  }
  return str;
});

const showAccPwdLogin = computed<boolean>(() => {
  return props.configs.loginAccountType?.includes('1-1') ?? false;
});

const showQrLogin = computed<boolean>(() => {
  return props.configs.loginAccountType?.includes('1-2') ?? false;
});

const showSmsLogin = computed<boolean>(() => {
  return props.configs.loginAccountType?.includes('1-3') ?? false;
});

// 监听配置变化
watch(
  () => props.configs,
  (v) => {
    if (v && v.showVctCode === 1) {
      onRefreshCode();
    }
  },
  { deep: true, immediate: true }
);

// 生命周期
onMounted(() => {
  if (props.configs.rememberPwd !== '1') return;
  
  const userName = HD.Cookie.Get('uniwim_username') || getCookieValue('uniwim_username') || null;
  model.user = userName ? decodeURIComponent(userName) : '';
  
  if (!model.user) return;
  
  model.pwd = HD.base64.decode(HD.Cookie.Get('uniwim_password') || getCookieValue('uniwim_password') || '');
  model.remember = true;
  loginType.value = props.configs.loginAccountType?.length
    ? props.configs.loginAccountType[0] 
    : '1-1';
});

// 方法定义
function getCookieValue(cookieName: string): string | null {
  const cookies = document.cookie.split('; ');
  for (const cookie of cookies) {
    const [name, value] = cookie.split('=');
    if (name === cookieName) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

function onRefreshCode(): void {
  const timestamp = Date.now().toString();
  const randomNumber = Math.floor(1000 + Math.random() * 9000).toString();
  const timeVal = timestamp + randomNumber;
  identifyCode.value = systemApi.getLoginCode(timeVal);
}

function encodepassword(input: string): string {
  const _keyStr = 'NjCG7lX9WbVtnaA1TxzEY5OpuJ8Pr4oZF3s-SKdkchv2mqyLiD0efwRIBH_=6UgMQ';
  let output = '';
  let chr1: number, chr2: number, chr3: number, enc1: number, enc2: number, enc3: number, enc4: number;
  let i = 0;

  const _utf8_encode = (str: string): string => {
    str = str.replace(/\r\n/g, '\n');
    let utftext = '';
    for (let n = 0; n < str.length; n++) {
      const c = str.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if (c > 127 && c < 2048) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  };

  input = _utf8_encode(input);
  while (i < input.length) {
    chr1 = input.charCodeAt(i++);
    chr2 = i < input.length ? input.charCodeAt(i++) : NaN;
    chr3 = i < input.length ? input.charCodeAt(i++) : NaN;

    enc1 = chr1 >> 2;
    enc2 = ((chr1 & 3) << 4) | (chr2 ? (chr2 >> 4) : 0);
    enc3 = isNaN(chr2) ? 64 : ((chr2 & 15) << 2) | (chr3 ? (chr3 >> 6) : 0);
    enc4 = isNaN(chr3) ? 64 : chr3 & 63;

    output += _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
  }
  return output;
}

function s7Login(data: TenantData[]): void {
  const pwd = encodepassword(model.pwd || '');
  const params = { 
    username: model.user, 
    password: pwd, 
    vali: model.vali 
  };

  fetch(`/uniwim/ump/uniLogin?url=${data[0].url}/login.json`,{
    method: 'POST',
    data: JSON.stringify(params)
  }).then((rep:any)=>{
    if (rep.Code === 0) {
        location.replace(`${data[0].url}/sys/app/bulletin?utoken=${rep.Response._id}`);
      } else {
        onRefreshCode();
        model.err_info = rep.Message;
      }
  }).catch((ex: any) => {
      console.log('登录失败', ex);
      onRefreshCode();
      model.err_info = '登录失败，请联系管理员';
  });
}

const onLogin = async()=>{
  model.err_info = null;
  ;
  // 表单验证
  const isValid = await formRef.value.validate((valid, fields) => {
      if (valid) {
      } else {
        model.err_info = '请检查必填项';
      }
  });
  
  if (!isValid) {
    model.err_info = '请检查必填项';
    return;
  }

  if (model.tenantName) {
    try {
      const res = await systemApi.getDetailByName(model.tenantName);
      const data = res || [];
      if (data.length) {
        model.tenantId = data[0].id;
        const isS7 = data[0].cloud === '3' && data[0].sysType === 's7';
        if (isS7) {
          s7Login(data);
        } else {
          saasLogin();
        }
      } else {
        saasLogin();
      }
    } catch (err) {
      console.error('获取租户信息失败', err);
      model.err_info = '获取租户信息失败，请重试';
    }
  } else {
    model.tenantId = null;
    saasLogin();
  }
}

async function saasLogin(): Promise<void> {
  try {
    await getEncryptKey();
    const encryptStr = new JSEncrypt();
    encryptStr.setPublicKey(publicKey.value);
    const encryptPwd = encryptStr.encrypt(model.pwd || '');
    const pwd = HD.base64.encode(encryptPwd || '');

    const params = {
      username: model.user,
      password: pwd,
      vali: model.vali,
      tenantId: model.tenantId
    };

    const body = {
      data: HD.base64.encode(JSON.stringify(params))
    };

    loading.value = true;
    const res = await systemApi.saasLogin(body, {
      meta: { isData: false }
    });

    loading.value = false;
    if (!res) {
      model.err_info = '登录失败，请重试！';
      onRefreshCode();
      return;
    }

    if (res.Code === 0 && res.Response) {
      // 处理双因素认证
      ;
      if (res.Response.twoFactorAuth) {
        setLoginStip('code');
        // 等待DOM更新后设置属性
        setTimeout(() => {
          if (loginValidRef.value) {
            loginValidRef.value.mobile = res.Response.mobile || '';
            loginValidRef.value.tenantId = res.Response.tenantId || '';
          }
        }, 0);
        return;
      }

      // 存储登录信息
      window.localStorage.setItem('UniWimAuthorization', res.Response.token);
      window.localStorage.setItem('UniWimExpire', res.Response.expire);
      window.localStorage.setItem('UniWimRefreshToken', res.Response.refreshToken);
      window.localStorage.setItem('UniWimTenantId', res.Response.tenantId);

      // 处理强制修改密码
      if (res.Response.changePwd) {
        model.err_info = null;
        visible.value = true;
      } else {
        // 处理记住密码
        if (model.remember) {
          HD.Cookie.Set('uniwim_username', encodeURIComponent(params.username || ''), 10);
          HD.Cookie.Set('uniwim_password', pwd, 10); // 原代码中pwdForRemember未定义，保持原逻辑
        } else {
          HD.Cookie.Set('uniwim_username', '', -1);
          HD.Cookie.Set('uniwim_password', '', -1);
        }

        // 登录成功跳转
        if (props.loginPop) {
          // await $store.dispatch('UPDTAINFO');
          let userInfo = await systemApi.initUserInfo();
          ;
          if(userInfo){
            userStore.setUserInfo(userInfo)
            loginStore.LOGIN_POP_VISIBLE(false)
          }
        } else {
          location.replace(`${import.meta.env.BASE_URL}index.html`);
        }
      }
    } else {
      model.err_info = res.Message || '登录失败';
      onRefreshCode();
    }
  } catch (err) {
    model.err_info = err instanceof Error ? err.message : '登录异常';
    onRefreshCode();
    loading.value = false;
  }
}

function getEncryptKey(): Promise<void> {
  return new Promise((resolve, reject) => {
    systemApi.getEncryptKey().then((res) => {
      publicKey.value = res.publicKey || '';
      if (res.publicKey) {
        sessionStorage.setItem('publicKey', res.publicKey);
        resolve();
      } else {
        reject(new Error('获取加密密钥失败'));
      }
    }).catch(reject);
  });
}

function loginChange(): void {
  emit('onSwitchLoginType', -1);
}

function setLoginStip(val: 'first' | 'code'): void {
  loginStip.value = val;
}

// 定义事件
const emit = defineEmits<{
  (e: 'onSwitchLoginType', type: number): void;
}>();

// 声明全局变量类型
declare global {
  interface Window {
    HD: {
      Cookie: {
        Get: (key: string) => string;
        Set: (key: string, value: string, days: number) => void;
      };
      base64: {
        encode: (str: string) => string;
        decode: (str: string) => string;
      };
    };
    $: {
      ajax: (options: any) => void;
    };
  }
}
</script>
<style lang="less">
.app-popover{
  padding: 0!important;
  .el-popper__arrow{
    display: block;
  }
}
  .img-part {
    display: flex;

    img {
      width: 200px;
      height: 200px;
      margin-right: 10px;
    }
  }
</style>
<style scoped lang="less">
::v-deep.dly-pwd-box {
  padding: 27px 28px 30px 32px;
  background: transparent;

  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-size: 14px;
      color: #333333;
      line-height: 20px;

      &::before {
        display: none;
      }
    }

    .el-form-item__content {
      .el-select {
        width: 100%;
      }
    }
  }

  .dly-frame-toolbar {
    margin-top: -10px;

    .dly-frame-toolbar__left {
      .el-checkbox__input + .el-checkbox__label {
        font-size: 12px;
      }

      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #666666;
      }

      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #029199;
        border-color: #029199;
      }
    }

    .select-part {
      display: flex;
      justify-content: space-between;

      .el-checkbox__label {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
      }
    }
  }

  .el-input__wrapper {
    height: 44px;
    line-height: 44px;
    // border: 1px solid #dadada;
    border-radius: 2px;
    // background: #fff !important;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      color: #bfbfbf;
      letter-spacing: 0;
    }

    &:hover {
      border: 1px solid #0862ea !important;
    }
  }

  .dly-frame-button {
    width: 340px;
    height: 43px;
    margin-top: 29px;

    .el-button {
      font-size: 18px;
      font-weight: 400;
      width: 100%;
      height: 100%;
      border-radius: 2px;
      overflow: hidden;
      background: #0054d2;
      border-color: #0054d2;
    }
  }

  .dly-button {
    margin-top: 10px;
    display: flex;
    width: 100%;
    justify-content: space-between;

    .register-item {
      text-align: right;

      span {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
        line-height: 17px;
      }

      .register-btn {
        cursor: pointer;
        color: #0054d2;
      }
    }
  }

  .dly-error-info {
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 28px;
    background: #fcddd9;
    border: 1px solid #db3b35;
    border-radius: 2px;
    font-weight: 400;
    font-size: 14px;
    color: #db3b35;
    letter-spacing: 0;
    padding: 8px 15px;
    box-sizing: border-box;
    margin-bottom: 20px;

    i {
      margin-right: 13px;
    }
  }

  .identify-box {
    .el-input {
      width: calc(100% - 117px);
    }
  }

  .identify {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
  }

  // app获取
  .app-part {
    text-align: center;
    cursor: pointer;
    font-size: 12px;
    color: #333333;
  }


  .title {
    padding: 0 10px;
    font-size: 20px;
    font-weight: 700;
    user-select: none;
    text-align: center;
  }

  .isHidden {
    visibility: hidden;
  }
}
</style>