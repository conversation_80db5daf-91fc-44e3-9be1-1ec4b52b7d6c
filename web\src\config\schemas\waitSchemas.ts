/**
 * 等待组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const waitSchema: ComponentConfigSchema = {
  componentType: 'wait',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '让流程等待一段时间',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    // {
    //   id: 'advanced',
    //   label: '高级选项',
    //   description: '等待行为的高级配置',
    //   icon: 'Setting',
    //   order: 2,
    //   collapsible: true,
    //   collapsed: true,
    //   advanced: true,
    // },
  ],

  fields: {
    duration: {
      type: 'number',
      label: '等待时长（秒）',
      description: '暂停执行的时间长度',
      placeholder: '5',
      required: true,
      group: 'basic',
      order: 1,
      min: 1,
      max: 3600,
      step: 1,
      precision: 0,
      validation: [
        {
          type: 'required',
          message: '等待时长不能为空',
        },
        {
          type: 'min',
          value: 1,
          message: '等待时长不能小于1秒',
        },
      ],
    },

    // unit: {
    //   type: 'radio',
    //   label: '时间单位',
    //   description: '等待时长的单位',
    //   group: 'basic',
    //   order: 2,
    //   default: 'seconds',
    //   options: [
    //     { label: '秒', value: 'seconds', description: '以秒为单位' },
    //     { label: '毫秒', value: 'milliseconds', description: '以毫秒为单位' },
    //     { label: '分钟', value: 'minutes', description: '以分钟为单位' },
    //   ],
    // },

    reason: {
      type: 'string',
      label: '等待原因',
      description: '记录等待的原因，便于调试',
      placeholder: '等待页面加载完成',
      group: 'basic',
      order: 1,
    },

    log_wait: {
      type: 'boolean',
      label: '记录等待日志',
      description: '是否在日志中记录等待信息',
      group: 'advanced',
      order: 2,
      default: true,
    },
  },

  presets: {
    short: {
      label: '短暂等待',
      description: '短暂的等待时间，适合快速操作',
      config: {
        duration: 1,
        unit: 'seconds',
        log_wait: false,
      },
    },

    medium: {
      label: '中等等待',
      description: '中等的等待时间，适合一般操作',
      config: {
        duration: 3,
        unit: 'seconds',
        log_wait: true,
      },
    },

    long: {
      label: '长时间等待',
      description: '较长的等待时间，适合复杂操作',
      config: {
        duration: 10,
        unit: 'seconds',
        log_wait: true,
        reason: '等待复杂操作完成',
      },
    },
  },

  examples: [
    {
      title: '等待页面加载',
      description: '等待3秒让页面完全加载',
      config: {
        duration: 3,
        unit: 'seconds',
        reason: '等待页面加载完成',
      },
    },
    {
      title: '等待动画完成',
      description: '等待500毫秒让动画播放完成',
      config: {
        duration: 500,
        unit: 'milliseconds',
        reason: '等待动画完成',
      },
    },
  ],
}


