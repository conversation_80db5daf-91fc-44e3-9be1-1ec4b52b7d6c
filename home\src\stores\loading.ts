import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoadingStore = defineStore('loading', () => {
  const isLoading = ref(false)
  const isLatest = ref(true)
  const isFirst = ref(true)
  const publishType = ref(0)
  const publishResponse = ref({})
  const currentVersion = ref('')
  const updateStatus = ref('success')

  function startLoading() {
    isLoading.value = true
  }

  function finishLoading() {
    isLoading.value = false
  }

  function setLatest(latest: boolean) {
    isLatest.value = latest
  }

  function setFirst(first: boolean) {
    isFirst.value = first
  }

  function setPublishType(type: number) {
    publishType.value = type
  }

  function setPublishResponse(data: any) {
    publishResponse.value = data
  }

  function setCurrentVersion(data: any) {
    currentVersion.value = data
  }

  function setUpdateStatus(status: string) {
    updateStatus.value = status
  }

  return { isLoading, isLatest,isFirst, publishType,publishResponse,currentVersion,updateStatus,setUpdateStatus,setCurrentVersion,setPublishResponse,setPublishType, startLoading, finishLoading,setLatest,setFirst }
})