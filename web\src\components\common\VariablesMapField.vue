<template>
  <div class="variables-map">
    <div class="variables-list">
      <div
        class="variables-group"
        v-for="(variableGroup, variableIndex) in modelValue"
        :key="variableIndex"
      >
        <div class="variables-label">
          <div class="variable-row">
            <el-form-item
              class="t1"
              :class="{ 'error-info': hasValidationError(variableGroup.variable) }"
              :key="variableIndex"
              :prop="`extract_variable[${variableIndex}].variable`"
            >
              <el-input
                v-model="variableGroup.variable"
                placeholder="变量名，如：code"
                @input="validateVariableName"
                clearable
                style="width: 130px"
              />
            </el-form-item>
            <el-select
              class="t2 el-select-small"
              v-model="variableGroup.type"
              placeholder="选择类型"
              @change="changeType(variableGroup)"
              style="width: 80px"
            >
              <el-option label="对象" value="json" />
              <el-option label="列表" value="list" />
              <el-option label="字符" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
            </el-select>
            <el-input
              class="t3"
              v-model="variableGroup.desc"
              placeholder="变量描述"
              clearable
              style="flex: 1"
            />
          </div>
          <div class="variable-row" v-if="!hideRealKey">
            <el-form-item
              class="t4"
              :class="{
                'right-radius-0': variableGroup.type === 'json' || variableGroup.type === 'list',
              }"
              style="width: 100%"
              :prop="`extract_variable[${variableIndex}].realkey`"
            >
              <el-input
                v-model="variableGroup.realkey"
                placeholder="提取规则，如：Response.title"
                clearable
              />
            </el-form-item>
            <el-tooltip
              content="配置示例数据，用于excel/word模板绑定"
              placement="top"
              effect="light"
              v-if="variableGroup.type === 'json' || variableGroup.type === 'list'"
            >
              <el-button class="t5" @click="openDemoDialog(variableGroup, variableIndex)"
                >示例
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="variables-action">
          <el-icon
            @click="removeVariableGroup(variableIndex)"
            :size="20"
            color="var(--el-color-danger)"
            title="删除"
            v-if="modelValue?.length > 1"
          >
            <RemoveFilled />
          </el-icon>
<!--          <el-icon-->
<!--            @click="addVariableGroup"-->
<!--            :size="20"-->
<!--            color="var(&#45;&#45;el-color-primary)"-->
<!--            v-if="variableIndex === modelValue.length - 1"-->
<!--            title="添加"-->
<!--          >-->
<!--            <CirclePlusFilled />-->
<!--          </el-icon>-->
        </div>
      </div>
      <div v-if="!modelValue?.length" class="no-variables-map-data" @click="addVariableGroup">
        <el-icon :size="20" color="var(--el-color-primary)" title="添加">
          <CirclePlusFilled />
        </el-icon>
        <span>添加变量</span>
      </div>
    </div>
    <el-dialog
      v-model="showDemoDialog"
      title="配置示例数据"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
        <div style="color: #666">用于excel/word模板绑定</div>
        <el-button @click="formatJson" :icon="Document" class="format-button" size="small">
          格式化
        </el-button>
      </div>
      <div>
        <el-input
          type="textarea"
          :rows="10"
          v-model="demoContent"
          placeholder="请输入JSON格式的Demo数据"
        />
      </div>
      <template #footer>
        <el-button @click="showDemoDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDemoData">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, nextTick, ref } from 'vue'
import type { ValidationResult } from '@/types/config.ts'
import { Document, VideoPause, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: Array<{
    variable?: string
    type?: 'json' | 'list' | 'string' | 'number' | 'boolean'
    desc?: string
    realkey?: string
    example?: object | null
  }>
  validationErrors: ValidationResult
  hideRealKey: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const addVariableGroup = () => {
  const newGroup = {
    variable: '',
    type: 'string',
    desc: '',
    realkey: '',
    example: null,
  }
  emit('update:modelValue', [...(props.modelValue || []), newGroup])
}

const removeVariableGroup = (groupIndex: number) => {
  const newGroups = [...props.modelValue]
  newGroups.splice(groupIndex, 1)

  emit('update:modelValue', newGroups)
}

const changeType = (variableGroup: any) => {
  variableGroup.example = null
  emit('update:modelValue', [...props.modelValue])
}

const validateVariableName = () => {
  if (props.modelValue) {
    props.modelValue.forEach((item) => {
      if (item.variable) {
        // 实时过滤非法字符，只能包含字母、数字、横线、下划线，且不能以数字开头
        let filteredValue = item.variable.replace(/[^a-zA-Z0-9_-]/g, '')
        // 如果以数字开头，则移除开头的数字
        if (/^[0-9]/.test(filteredValue)) {
          filteredValue = filteredValue.replace(/^[0-9]+/, '')
        }
        item.variable = filteredValue
      }
    })
  }
}

const hasValidationError = (variable: string) => {
  return (props.validationErrors[0]?.variables || []).some((v: string) => v === variable)
}

const showDemoDialog = ref(false)
const demoContent = ref('')
const demoIndex = ref(-1)
const openDemoDialog = (variableGroup: any, variableIndex: number) => {
  demoContent.value = variableGroup.example?.demo || ''
  // todo key-value map配置回显

  demoIndex.value = variableIndex
  showDemoDialog.value = true
}
const formatJson = () => {
  try {
    const data = props.modelValue[demoIndex.value].type === 'json' ? '{}' : '[]'
    const parsed = JSON.parse(demoContent.value || data)
    demoContent.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

const saveDemoData = () => {
  try {
    // 验证JSON格式
    if (demoContent.value) {
      JSON.parse(demoContent.value)
    }
    const newGroups = props.modelValue
    newGroups[demoIndex.value].example = {
      demo: demoContent.value,
      // todo key-value map配置保存

    }
    emit('update:modelValue', newGroups)
    showDemoDialog.value = false
  } catch (error) {
    ElMessage.error('请输入有效的JSON格式数据')
  }
}

onMounted(async () => {
  // 历史数据处理：将字符串数组格式转换为对象格式
  if (props.modelValue && Array.isArray(props.modelValue)) {
    const hasStringItems = props.modelValue.some((item) => typeof item === 'string')

    if (hasStringItems) {
      const convertedValue = props.modelValue.map((item) => {
        if (typeof item === 'string') {
          return {
            variable: item,
            type: 'string',
            desc: '',
            realkey: '',
            example: null,
          }
        }
        return item
      })

      await nextTick()
      emit('update:modelValue', convertedValue)
    }
  }
})

defineExpose({
  addVariableGroup,
})
</script>

<style scoped lang="scss">
.variables-map {
  width: 100%;
}

.variables-list {
  display: flex;
  flex-direction: column;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.variables-group {
  display: flex;
  flex-flow: row nowrap;
  position: relative;
  padding: 8px;
  transition: background 0.2s;

  &:hover,
  &:focus-within {
    background: #fafafa;
  }

  & ~ .variables-group {
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -8px;
      right: 0;
      height: 1px;
      background-color: #e1e1e1;
    }
  }

  .variables-label {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .variable-row {
      display: flex;
      align-items: flex-start;

      .el-form-item {
        margin-bottom: 2px;
      }

      &:last-child {
        .el-form-item {
          margin-bottom: 0;
        }
      }

      :deep(.t1) {
        z-index: 1;

        .el-input__wrapper {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          box-shadow: 0 0 0 1px #e1e1e1 inset;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
          }
        }

        &.error-info {
          .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--el-color-danger) inset;
          }
        }
      }

      :deep(.t2) {
        margin-left: -1px;

        &:hover,
        &:focus-within {
          z-index: 2;
        }

        .el-select__wrapper {
          border-radius: 0;
          box-shadow: 0 0 0 1px #e1e1e1 inset;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
          }

          &.is-focused {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset;
          }
        }
      }

      :deep(.t3) {
        z-index: 1;
        margin-left: -1px;

        .el-input__wrapper {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          box-shadow: 0 0 0 1px #e1e1e1 inset;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
          }
        }
      }

      :deep(.t4) {
        .el-input__wrapper {
          box-shadow: 0 0 0 1px #e1e1e1 inset;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
          }
        }
      }

      :deep(.t5) {
        &.el-button {
          font-size: var(--el-font-size-extra-small);
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          margin-left: -1px;

          &:hover,
          &:active {
            z-index: 2;
          }
        }
      }
    }
  }

  .variables-action {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    padding-left: 4px;

    .el-icon {
      cursor: pointer;

      & + .el-icon {
        margin-top: 8px;
      }
    }
  }
}

.no-variables-map-data {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 0;
  color: var(--el-color-primary);
}
</style>
