<template>
  <div class="list-container">
    <Searchbar @search="onSearch"/>

    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" ref="wrapper">
      <van-empty v-if="finished && !loading && !list.length" description="无任务数据" />
      <van-list
        v-model:loading="loading"
        :finished="finished"
        loading-text=" "
        finished-text=""
        @load="onLoad"
      >
        <div class="task-list">
          <div class="task-item" v-for="row in list" :key="row">
            <div class="task-name">
              {{ row.missionName }}
            </div>
            <div class="task-state">
              <span class="st tag" :class="row.taskState">
                  {{ formatTaskState(row.taskState) }}
              </span>
              <span class="time">
                {{ formatCronToText(row.timeScheduled) }}
              </span>
            </div>
            <div class="task-run">
              <div class="run1">
                <img :src="runCountIcon" alt=""/>
                运行次数：
                <template v-if="Number(row.executeCount) + Number(row.failCount) > 0">
                  <span class="r1">{{ Number(row.executeCount) || 0 }}</span>/<span
                  class="r2">{{ Number(row.failCount) || 0 }}</span>
                </template>
                <template v-else>
                  <span class="">{{ Number(row.executeCount) + Number(row.failCount) }}</span>
                </template>
              </div>
              <div class="run1">
                <img :src="runSuccessIcon" alt=""/>
                成功率：<span class="r4">{{
                  (Number(row.executeCount) + Number(row.failCount) === 0 ? 0 : Math.round((Number(row.executeCount) / (Number(row.executeCount) + Number(row.failCount)) * 100)))
                }}%</span>
              </div>
            </div>
            <div class="task-run">
              <div class="run1">
                <span class="r3">最近执行：</span>
                {{ row.lastExecuteTime ? formatTime(row.lastExecuteTime, 'yyyy-MM-DD HH:mm') : '/' }}
              </div>
            </div>
            <div class="task-action">
              <div class="b1" @click="onHandleMonitor(row)">监控</div>
              <div class="b1" @click="onRowActiveChange(row)" :disabled="row.activeLoading">
                <van-loading type="spinner" size="14px" v-if="row.activeLoading"/>
                {{ row.active ? '停用' : '启用' }}
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import {ref, onActivated} from 'vue';
import {useRouter, useRoute, onBeforeRouteLeave} from 'vue-router'

const route = useRoute()
const router = useRouter()

import {api} from '@/api';
import utils from '@/utils/utils.ts'

// 开始：—————————————— 滚动条相关 ——————————————
const wrapper = ref<HTMLDivElement>();
const scrollTop = ref(0);
// 页面重新打开的时候，滚动到顶部
onActivated(() => {
  if (scrollTop.value) {
    wrapper.value.$el.scrollTop = scrollTop.value;
  }
})
onBeforeRouteLeave((to, from, next)=>{
  scrollTop.value = wrapper.value?.$el?.scrollTop
  next()
})
// 结束：—————————————— 滚动条相关 ——————————————


// 开始：—————————————— 运行图标 ——————————————
// 运行次数图标
import runCountIcon from '@/assets/images/run_count.png'
// 成功率图标
import runSuccessIcon from '@/assets/images/run_success_rate.png'
// 结束：—————————————— 运行图标 ——————————————


// 开始：—————————————— 定义props ——————————————
const props = defineProps({
  taskState: {
    type: String,
    default: '',
  },
})
// 结束：—————————————— 定义props ——————————————

// 开始：—————————————— 搜索相关 ——————————————
const searchValue = ref<string>('');
const onSearch = async (search: string) => {
  wrapper.value.$el.scrollTop = 0
  searchValue.value = search
  refreshing.value = true;
  onRefresh()
};
// 结束：—————————————— 搜索相关 ——————————————


// 开始：—————————————— 数据加载相关 ——————————————
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const pageNo = ref(1);
const pageSize = ref(30);

interface Condition {
  Field: string;
  Group: number;
  Operate: string;
  Relation: string;
  Value: string;
}

interface QueryParams {
  conditions: Condition[];
  data: Record<string, any>;
  index: number;
  size: number;
}

// 获取列表数据
const onLoad = async () => {
  const params: QueryParams = {
    conditions: [],
    data: {},
    index: pageNo.value,
    size: pageSize.value
  };
  if (searchValue.value) {
    params.conditions.push({
      Field: "missionName",
      Group: 1,
      Operate: "like",
      Relation: "and",
      Value: searchValue.value
    })
  }
  if (props.taskState !== 'all') {
    params.data.taskState = props.taskState;
  }
  try {
    const res = await api.AIAgentMissionQuery(params)
    if (res.current <= res.pages && res.rows.length > 0) {
      if (pageNo.value === 1) {
        list.value = res.rows
      } else {
        list.value = list.value.concat(res.rows)
      }
      pageNo.value++
    }
    else {
      if(!res.rows.length && res.current === 1) {
        list.value = []
      }
      finished.value = true;
    }
    loading.value = false;
  } catch (e) {
    loading.value = false;
    finished.value = true;
  }
  refreshing.value = false;
};

// 刷新
const onRefresh = () => {
  finished.value = false;
  pageNo.value = 1;
  loading.value = true;
  onLoad();
}

// 监控任务
const onHandleMonitor = (row: object) => {
  // 路由切换到监控页面
  router.push({
    path: '/monitor',
    query: {
      missionId: row.id,
      missionName: row.missionName,
    }
  })
}

// 停用启用任务
const onRowActiveChange = async (row: object) => {
  row.activeLoading = true
  const params = {
    ...row,
    active: row.active === 0 ? 1 : 0,
    missionStartTime: row.missionEndTime ? moment(row.missionStartTime).valueOf() : null,
    missionEndTime: row.missionEndTime ? moment(row.missionEndTime).valueOf() : null,
  }
  let toast = showLoadingToast({
    message: row.active === 0 ? '任务开启中...' : '任务关闭中...',
    duration: 0,
    forbidClick: true,
  });
  if (params.active) {
    const res = await api.AIAgentMissionDetail({id: params.id})
    params.configContent = res?.configContent || ''
  } else {
    delete params.configContent
  }
  try {
    const result = await api.AIAgentMissionUpdateActive(params)
    if (result?.Code === 0) {
      row.active = params.active
      toast = showSuccessToast({
        message: params.active === 0 ? '任务已关闭' : '任务已开启',
        forbidClick: true,
      })
    } else {
      toast = showFailToast({
        message: params.active === 0 ? '任务关闭失败' : '任务开启失败',
        forbidClick: true,
      })
    }
    setTimeout(() => {
      toast.close()
    }, 500)
  } catch (e) {
    toast = showFailToast({
      message: params.active === 0 ? '任务关闭失败' : '任务开启失败',
      forbidClick: true,
    })
    setTimeout(() => {
      toast.close()
    }, 500)
  }
  row.activeLoading = false
}
// 结束：—————————————— 数据加载相关 ——————————————


// 格式化taskState
const formatTaskState = (state: string) => {
  switch (state) {
    case 'wait':
      return '待执行'
    case 'running':
      return '执行中'
    case 'end':
      return '已结束'
    case 'unable':
      return '已停用'
    default:
      return '未知'
  }
}

// 格式化执行间隔
const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const formatCronToText = (timeScheduled?: string) => {
  const parts = (timeScheduled || '').split(',')
  switch (parts[0]) {
    case '1':
      return `间隔${parts[1]}分钟`;
    case '2':
      return `间隔${parts[1]}小时`;
    case '3':
      return `间隔${parts[1]}天`;
    case '4':
      const week = parts.slice(1).sort((a: string, b: string) => Number(a) - Number(b)).map((we: string) => weeks[Number(we) - 1]).join(',');
      return `${week}`;
    case '5':
      return `${utils.formatTime(Number(parts[1]))}`;
  }
  return timeScheduled
}

// 格式化时间
const formatTime = (data: string, format: string) => {
  if (!data) return ''
  return moment(data).format(format)
}

// 格式化state名称
const formatState = (state: number) => {
  switch (state) {
    case -1:
      return '失败'
    case 0:
      return '执行中'
    case 1:
      return '成功'
    default:
      return '未知'
  }
}
// 格式化state类型
const formatStateType = (state: number) => {
  switch (state) {
    case -1:
      return 'danger'
    case 0:
      return 'primary'
    case 1:
      return 'success'
    default:
      return 'warning'
  }
}


</script>

<style scoped lang="scss">
.list-container {
  height: 100%;
  background: #F1F3F5;
}

.van-pull-refresh {
  height: calc(100% - 54px);
  overflow-y: auto;
}

.task-list {
  display: flex;
  flex-direction: column;

  .task-item {
    background: #ffffff;
    margin: 12px;
    padding: 12px;
    box-sizing: border-box;
    border-radius: 4px;
    //box-shadow: 0 1px 6px rgba(100, 100, 100, 0.15);
    font-size: 14px;
    display: flex;
    flex-direction: column;

    & ~ .task-item {
      margin-top: 0;
    }

    .task-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      position: relative;
      padding-left: 8px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eeeeee;
      font-weight: bold;
      font-size: 14px;
      line-height: 14px;

      &:before {
        content: '';
        width: 2px;
        height: 14px;
        background: #3B94E6;
        border-radius: 1px;
        position: absolute;
        top: 0;
        left: 0;
      }

    }

    .task-state {
      display: flex;
      align-items: center;
      height: 22px;
      line-height: 22px;
      margin: 10px 0 0;

      .st {
        width: 52px;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 12px;
        border-radius: 11px;
        box-sizing: border-box;

        &.running {
          background: #F0F5FF;
          border: 1px solid #BED2FF;
          color: #1E39C3;
        }

        &.wait {
          background: #FFFDDF;
          border: 1px solid #E9DE9A;
          color: #9E7E00;
        }

        &.unable, &.end {
          background: #F7F7F9;
          border: 1px solid #E6E7E9;
          color: #666666;
        }
      }

      .time {
        margin-left: 12px;
        font-size: 12px;
        line-height: 1;
        color: #999999;
      }
    }

    .task-run {
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      color: #5C5F66;
      margin: 10px 0;

      & ~ .task-run {
        margin-top: 0;
      }

      .run1 {
        display: flex;
        align-items: center;
        white-space: nowrap;

        img {
          width: 12px;
          height: 12px;
          object-fit: contain;
          display: block;
          margin-right: 8px;
        }
      }

      .r1 {
        color: var(--van-primary-color);
      }

      .r2 {
        color: var(--van-danger-color);
      }

      .r3 {
        color: #BCBFC3;
      }

      .r4 {
        color: #5ACD90;
      }
    }

    .task-action {
      display: flex;
      justify-content: space-around;
      padding: 16px 0 6px;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: #f1f1f1;
      }

      .b1 {
        color: #222222;
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.action {
          color: var(--van-primary-color);
        }

        &[disabled]:not([disabled='false']) {
          opacity: 0.4;
          pointer-events: none;
        }

        &:not(:first-child):before {
          content: '';
          position: absolute;
          top: -4px;
          left: 0;
          width: 1px;
          height: 24px;
          background: #f1f1f1;
        }
      }
    }
  }
}
</style>
