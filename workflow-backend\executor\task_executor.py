"""
任务执行器 - 负责执行Robot Framework代码（适用于PyInstaller打包）
"""

import json
import os
import shutil
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
import threading

import robot.run
from loguru import logger
from robot import run
import psutil
from robot.result import ResultVisitor

from config import settings  # 绝对导入

from robot.libraries.BuiltIn import BuiltIn
from robot.api import get_model, TestSuite
from robot.reporting import ResultWriter
from robot.result.executionresult import Result

from executor.lifecycle import NodeStatus

# 本地模块导入
from models.workflow import (
    ExecutionResult,
    ExecutionStatus,
    ExecutionOptions,
    ExecutionLog,
    LogLevel,
    create_execution_id,
)

from listener.execution_monitor import ExecutionMonitor, cache

from utils.wimtask_server_api import (
    save_history,
    send_msg,
    upload_task_state,
    send_msg_new,
)
from concurrent.futures import ProcessPoolExecutor

process_pool: Optional[ProcessPoolExecutor] = None


class ExecuteParam:
    def __init__(
        self,
        robot_code: str,
        task_id: str,
        token: str,
        node_num: int = None,
        options: Optional[Dict[str, Any]] = None,
    ):
        self.robot_code = robot_code
        self.task_id = task_id
        self.options = options
        self.node_num = node_num
        self.token = token
        self.step_end_callback = None
        self.execution_id = None


class ErrorCollector(ResultVisitor):
    """访问测试结果并收集错误信息"""

    def __init__(self):
        self.errors = []

    def visit_test(self, test):
        """处理测试用例结果"""
        if test.status == "FAIL":
            error_info = {
                "test_name": test.name,
                "status": test.status,
                "message": test.message,
                "start_time": test.starttime,
                "end_time": test.endtime,
                "elapsed_time": test.elapsedtime,
            }
            self.errors.append(error_info)


class TaskExecutor:
    """任务执行器"""

    def __init__(self, if_send_msg=True, if_save_history=True):
        self.executions: Dict[str, ExecutionResult] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.output_dir = Path("outputs")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.listeners = {}  # 存储监听器实例，便于后续访问输出等信息
        self.if_send_msg = if_send_msg
        self.if_save_history = if_save_history

        logging.info("任务执行器初始化完成")

    async def stop_current_execute_in_queue(self):
        await task_manager.stop_current()

    async def execute_in_queue(self, execute_param: ExecuteParam) -> Dict[str, str]:

        if execute_param.execution_id:
            execution_id = execute_param.execution_id
        else:
            execution_id = create_execution_id()

        err = await task_manager.submit(self, execute_param)

        if err is not None:
            return {"execution_id": execution_id, "error": err}

        return {"execution_id": execution_id}

    async def execute(self, execute_param: ExecuteParam) -> str:
        """异步执行Robot Framework代码"""
        if execute_param.execution_id:
            execution_id = execute_param.execution_id
        else:
            execution_id = create_execution_id()

        # 创建执行结果记录
        execution_result = ExecutionResult(
            execution_id=execution_id,
            status=ExecutionStatus.PENDING,
            start_time=datetime.now(),
            robot_code=execute_param.robot_code,
        )

        self.executions[execution_id] = execution_result
        monitor = ExecutionMonitor(
            token=execute_param.token,
            task_id=execute_param.task_id,
            history_id=execution_id,
            node_num=execute_param.node_num,
            if_send_msg=self.if_send_msg,
        )
        monitor.callback = execute_param.step_end_callback
        self.listeners[execution_id] = monitor

        await self._execute_robot_code(execution_id, execute_param)
        return execution_id

    async def _execute_robot_code(self, execution_id: str, execute_param: ExecuteParam):
        """执行Robot Framework代码的内部方法"""

        robot_code = execute_param.robot_code

        execution_result = self.executions.get(execution_id, {})

        errs = []

        try:
            node_path = os.environ["PLAYWRIGHT_NODEJS_PATH"]
            logger.info(f"Playwright node 路径是{node_path}")
            execution_result.status = ExecutionStatus.RUNNING
            upload_task_state(execute_param.token, execute_param.task_id, 1)
            self._add_log(
                execution_result, LogLevel.INFO, "开始执行Robot Framework代码"
            )
            # 执行时间
            exc_param = {
                "exc_time": int(datetime.now().timestamp()),
                "history_id": execution_id,
                "task_id": execute_param.task_id,
                "token": execute_param.token,
            }

            # 同时保存到输出目录用于调试
            execution_output_dir = self.output_dir / execution_id
            execution_output_dir.mkdir(exist_ok=True)
            debug_robot_path = execution_output_dir / "workflow.robot"
            debug_robot_path.write_text(execute_param.robot_code, encoding="utf-8")
            # 获取当前项目路径
            project_path = str(Path(__file__).parent.parent)
            # 创建监听器

            options = execute_param.options

            output_dir = Path(options.get("outputdir", str(execution_output_dir)))
            # 构建执行参数
            run_kwargs = {
                "outputdir": output_dir,
                "loglevel": options.get("log_level", "DEBUG"),
                "variable": options.get("variables", [f"execution_id:{execution_id}"]),
                "include": options.get("include_tags", []),
                "exclude": options.get("exclude_tags", []),
                "dryrun": options.get("dry_run", False),
                # "output":'output_e.xml',
                # "log": "log_2.html" ,
                # "report": 'report_2.html',
                "listener": [self.listeners[execution_id]],
                "console": "verbose",
            }

            # 模拟PID
            pid = f"{os.getpid()}-{threading.get_ident()}"
            execution_result.pid = pid
            self._add_log(execution_result, LogLevel.INFO, f"启动执行器线程，PID={pid}")

            result = await asyncio.to_thread(
                self._run_robot_code, robot_code, run_kwargs
            )

            error_collector = ErrorCollector()
            result.visit(error_collector)
            errs = error_collector.errors

            # 配置要生成的报告
            writer_config = {
                "report": output_dir / "report.html",
                "log": output_dir / "log.html",
            }
            # 生成报告
            status = ResultWriter(result).write_results(**writer_config)

            # 设置执行状态
            execution_result.end_time = datetime.now()
            execution_result.duration = (
                execution_result.end_time - execution_result.start_time
            ).total_seconds()

            if status == 0:
                execution_result.status = ExecutionStatus.SUCCESS
                upload_task_state(execute_param.token, execute_param.task_id, 3)
                # 单节点执行记录返回结果
                execution_result.return_obj = cache.get_item(execute_param.task_id)
                self._add_log(execution_result, LogLevel.INFO, "执行成功完成")
            else:
                execution_result.status = ExecutionStatus.FAILED
                upload_task_state(execute_param.token, execute_param.task_id, -1)
                self._add_log(
                    execution_result, LogLevel.ERROR, f"执行失败，返回码: {result}"
                )
            exc_param["status"] = status

        except asyncio.CancelledError:
            execution_result.status = ExecutionStatus.CANCELLED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.WARN, "执行被取消")

            upload_task_state(execute_param.token, execute_param.task_id, 3)

        except Exception as e:
            execution_result.status = ExecutionStatus.FAILED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.ERROR, f"执行异常: {str(e)}")
            logging.exception(f"执行 {execution_id} 时发生异常")
            execution_result.exception = str(e)
            upload_task_state(execute_param.token, execute_param.task_id, -1)

        finally:
            # 清理浏览器资源
            try:
                BuiltIn().run_keyword("Close All Browsers")
            except Exception as e:
                logging.warning(f"关闭浏览器失败: {str(e)}")

            # # 杀死子进程
            # self.kill_child_processes(os.getpid())
            # 清理运行任务记录
            if execution_id in self.running_tasks:
                del self.running_tasks[execution_id]
            # 通知流程结束
            if execute_param.step_end_callback:

                data = {
                    "end": "1",
                    "missionId": execute_param.task_id,
                    "historyId": execution_id,
                    "execution_id": execution_id,
                    "state": "suite." + execution_result.status,
                }

                if "exception" in execution_result:
                    data["exception"] = execution_result.exception

                if len(errs) > 0:
                    data["errors"] = errs

                execute_param.step_end_callback(data)
            try:
                if self.if_save_history:
                    # 记录执行状态
                    save_history(exc_param)

                if self.if_send_msg:
                    # 发送消息
                    send_msg_new(
                        exc_param, execute_param.task_id, self.listeners[execution_id]
                    )
                    # send_msg(exc_param, execute_param.task_id, self.listeners[execution_id])
            except Exception as e:
                logging.warning(f"发送消息或历史记录失败: {str(e)}")

            # 清理监听器
            if execution_id in self.listeners:
                if execution_id in self.running_tasks:
                    del self.listeners[execution_id]
            # 清除变量缓存
            cache.del_item(execute_param.task_id)

    def _run_robot(self, robot_file: str, run_kwargs: dict):
        """实际运行 Robot Framework 的方法"""
        return run(robot_file, **run_kwargs)

    def _run_robot_code(self, robot_code: str, run_kwargs: dict) -> Result:
        model = get_model(robot_code)
        suite = TestSuite.from_model(model)
        result = suite.run(**run_kwargs)
        return result

    async def get_execution_status(self, execution_id: str) -> ExecutionResult:
        """获取执行状态"""
        if execution_id not in self.executions:
            raise ValueError(f"执行记录不存在: {execution_id}")
        return self.executions[execution_id]

    async def get_monitor(self, execution_id: str) -> ExecutionResult:
        """获取执行状态"""
        if execution_id not in self.listeners:
            raise ValueError(f"监听器不存在: {execution_id}")
        return self.listeners[execution_id]

    async def stop_execution(self, execution_id: str) -> bool:
        """停止执行"""
        if execution_id in self.listeners:
            self.listeners[execution_id].set_stop_flag(True)

        if execution_id not in self.running_tasks:
            return False

        # 取消任务协程
        if execution_id in self.running_tasks:
            task = self.running_tasks[execution_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # 更新执行状态
        if execution_id in self.executions:
            execution_result = self.executions[execution_id]
            execution_result.status = ExecutionStatus.CANCELLED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.WARN, "执行被用户停止")

        # 移除任务记录
        del self.running_tasks[execution_id]

        logging.info(f"执行 {execution_id} 已停止")
        return True

    async def list_executions(self) -> List[ExecutionResult]:
        """列出所有执行记录"""
        return list(self.executions.values())

    async def cleanup(self):
        """清理资源"""
        logging.info("正在清理任务执行器资源...")

        # 停止所有正在运行的进程
        for execution_id in list(self.running_tasks.keys()):
            await self.stop_execution(execution_id)

        logging.info("任务执行器资源清理完成")

    def _add_log(
        self,
        execution_result: ExecutionResult,
        level: LogLevel,
        message: str,
        source: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        """添加执行日志"""
        log_entry = ExecutionLog(
            timestamp=datetime.now(),
            level=level,
            message=message,
            source=source,
            details=details,
        )
        execution_result.logs.append(log_entry)

    def get_execution_output_dir(self, execution_id: str) -> Optional[Path]:
        """获取执行输出目录"""
        output_dir = self.output_dir / execution_id
        return output_dir if output_dir.exists() else None

    async def _copy_output_files(self, execution_id: str, source_dir: Path):
        """复制输出文件到永久目录"""
        execution_output_dir = self.output_dir / execution_id
        execution_output_dir.mkdir(exist_ok=True)

        try:
            # 复制所有输出文件
            for file_path in source_dir.iterdir():
                if file_path.is_file():
                    dest_path = execution_output_dir / file_path.name
                    shutil.copy2(file_path, dest_path)

                    # 记录输出文件
                    execution_result = self.executions[execution_id]
                    execution_result.output_files.append(str(dest_path))

            logging.info(f"输出文件已复制到: {execution_output_dir}")

        except Exception as e:
            logging.error(f"复制输出文件失败: {e}")

    def cleanup_old_executions(self, days: int = 7):
        """清理旧的执行记录"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)

        to_remove = []
        for execution_id, execution_result in self.executions.items():
            if execution_result.start_time.timestamp() < cutoff_time:
                to_remove.append(execution_id)

        for execution_id in to_remove:
            # 删除执行记录
            del self.executions[execution_id]

            # 删除输出文件
            output_dir = self.output_dir / execution_id
            if output_dir.exists():
                shutil.rmtree(output_dir)

        logging.info(f"清理了 {len(to_remove)} 个旧的执行记录")

    def kill_child_processes(self, pid):
        try:
            parent = psutil.Process(pid)
            children = parent.children(recursive=True)
            for child in children:
                try:
                    child.terminate()
                    child.wait(timeout=1.0)
                except psutil.NoSuchProcess:
                    pass
        except psutil.NoSuchProcess:
            pass


class QueuedTaskManager:
    def __init__(self):
        self._task_queue = asyncio.Queue(100)
        self._in_queue = set()
        self._state = "new"
        self._current_task = None
        self._current_execution_id = None

    async def start(self):
        self._state = "start"
        logger.info("排队任务执行器启动")
        while self._state == "start":
            try:
                param = self._task_queue.get_nowait()

                if param.executor is not None:
                    try:
                        task = asyncio.create_task(
                            param.executor.execute(param),
                            name="task_" + param.execution_id,
                        )
                        self._current_task = task
                        self._current_execution_id = param.execution_id

                        def callback(t):
                            if t.cancelled():
                                logger.info(f"任务被取消{param.execution_id}")
                            elif t.exception():
                                logger.info(f"任务出错：{t.exception()}")
                            else:
                                logger.info(f"任务结果：{t.result()}")

                        task.add_done_callback(callback)
                        await task
                    finally:
                        self._in_queue.discard(param.task_id)
                        self._current_task = None
                        self._current_execution_id = None
                else:
                    logger.warning(
                        f"运行参数中没有指定执行器{param.task_id} {param.execution_id}"
                    )
            except asyncio.QueueEmpty:
                await asyncio.sleep(2)
            except Exception as e:
                logger.error(f"任务管理器主循环异常{e}", exc_info=True)

    async def stop_current(self):
        if self._current_task is not None:
            self._current_task.cancel()
            logger.info(f"发送cancel信号{self._current_task.get_name()}")

    async def submit(
        self, executor: TaskExecutor, param: ExecuteParam
    ) -> Optional[str]:
        if param.task_id not in self._in_queue:
            param.executor = executor
            await self._task_queue.put(param)
            if param.task_id not in self._in_queue:
                self._in_queue.add(param.task_id)
            return None
        else:
            return f"任务{param.task_id}已经在队列里面等待执行"

    async def exit(self):
        await self.stop_current()
        self._state = "exit"

    async def qsize(self) -> int:
        return self._task_queue.qsize()

    async def clear_queue(self):
        count = 0
        while not self._task_queue.empty():
            try:
                param = self._task_queue.get_nowait()
                self._task_queue.task_done()  # 修复队列计数
                self._in_queue.discard(param.task_id)  # 移除跟踪
                count += 1
            except asyncio.QueueEmpty:
                break
        logger.info(f"已清空队列，共移除 {count} 个任务")


task_manager = QueuedTaskManager()
