/**
 * 拍平后的字段信息接口
 */
interface FlattenedKeyInfo {
  key: string;
  path: string;
  value: any;
  meaning?: string;
}

/**
 * 递归提取对象或数组中所有的键，并将结果拍平处理
 * @param data 要处理的数据，可以是任意类型
 * @param currentPath 当前路径，用于构建层级
 * @param meanings 键的含义映射，格式为 { "路径": "含义" }
 * @returns 拍平后的所有键信息数组
 */
function extractAndFlattenKeys(
  data: any,
  currentPath: string = '',
  meanings: Record<string, string> = {}
): FlattenedKeyInfo[] {
  const result: FlattenedKeyInfo[] = [];

  // 处理数组
  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      const itemPath = currentPath ? `${currentPath}[${index}]` : `[${index}]`;

      // 如果数组项是对象或数组，递归处理并合并结果
      if (typeof item === 'object' && item !== null) {
        const childResults = extractAndFlattenKeys(item, itemPath, meanings);
        result.push(...childResults);
      } else {
        // 基本类型值，直接添加到结果
        result.push({
          key: `[${index}]`,
          path: itemPath,
          value: item,
          meaning: meanings[itemPath]
        });
      }
    });
  }
  // 处理对象
  else if (typeof data === 'object' && data !== null) {
    Object.entries(data).forEach(([key, value]) => {
      const itemPath = currentPath ? `${currentPath}.${key}` : key;

      // 如果值是对象或数组，递归处理并合并结果
      if (typeof value === 'object' && value !== null) {
        const childResults = extractAndFlattenKeys(value, itemPath, meanings);
        result.push(...childResults);
      } else {
        // 基本类型值，直接添加到结果
        result.push({
          key,
          path: itemPath,
          value: value,
          meaning: meanings[itemPath]
        });
      }
    });
  }

  return result;
}

// 示例用法
const exampleData = {
  user: {
    name: "张三",
    age: 30,
    address: {
      city: "北京",
      street: "中关村大街"
    }
  },
  hobbies: ["读书", "运动", { type: "户外", frequency: "每周" }]
};

// 定义各字段的含义
const keyMeanings = {
  "user.name": "用户姓名",
  "user.age": "用户年龄",
  "user.address.city": "所在城市",
  "user.address.street": "街道信息",
  "hobbies[0]": "第一个爱好",
  "hobbies[1]": "第二个爱好",
  "hobbies[2].type": "爱好类型",
  "hobbies[2].frequency": "进行频率"
};

// 提取并拍平所有键
const flattenedKeys = extractAndFlattenKeys(exampleData, '', keyMeanings);

// 打印结果
console.log("拍平后的所有键信息：");
console.log(JSON.stringify(flattenedKeys, null, 2));
