/* just override what you need */
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      // 自定义颜色覆盖
      'base': #0054D2,
      // 'base': #1E39C3,
      // 'light-8': #BED2FF,
      // 'light-9': #F0F5FF
    ),
    // 'warning': (
    //   'base': #9E7E00,
    //   'light-8': #E9DE9A,
    //   'light-9': #FFFDDF
    // ),
    // 'info': (
    //   'base': #666666,
    //   'light-8': #E6E7E9,
    //   'light-9': #F7F7F9
    // ),
  ),
  $text-color: (
    'regular': #222,
    'secondary': #5C5F66
  ),
  $font-size: (
    //'large': 16px,
    //'medium': 14px,
    //'base': 12px,
    //'small': 11px,
    //'extra-small': 10px,
  ),
  // $bg-color: (
  //   '': #F7F7F9,
  // ),
  $border-color: (
    'lighter': #ffffff
  ),
  $messagebox: (
    'font-size': '16px',
  )
);
