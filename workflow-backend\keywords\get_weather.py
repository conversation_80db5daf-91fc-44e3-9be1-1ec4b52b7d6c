#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
<AUTHOR>   zxf 
@Version :   1.0
@Time    :   2025/06/26 11:06:46
'''
import json
import requests

from config import globals
from config.env_config import get_config_item,DLY_URL,ALGORITHM_PLATFORM_URL

def get_weather(city, days):
    days = int(days)
    if days == 0:
        days = days + 1
    elif days == 1:
        days = days + 1
    elif days >= 16:
        days = 15
    data = {"ty": "days",
            "city": city,
            "days": days}

    headers = {
                "Content-Type": "application/json",
                "Authorization": globals.token
             }
    url = f"{get_config_item(DLY_URL)}/hdl/uniwater/v1.0/weather/gaofen.json"
    try:
        response = requests.post(url, 
                                json=data, headers=headers)
        
        weather = response.json()["Response"]["weather"]["result"]["daily_fcsts"]

        if days == 2:
            temp = weather[1:]
            return weather[1:]
        else:
            return weather
    except Exception as e:
        return f"weather api error:{str(e)}"

import json

ENDPOINTS = {
    "压力": "/LimCalP",
    "流量": "/LimCalF"
}

def get_limit_interval(data_type, his_win, day_count=30.0, percent=1.5, timeout=30):
    if isinstance(his_win, str):
        try:
            his_win = json.loads(his_win)
        except Exception as e:
            print(f"❌ his_win 转换失败: {str(e)}")
            return None

    if not isinstance(his_win, list) or not all(isinstance(i, (int, float)) for i in his_win):
        print("❌ his_win 必须为 float 数组")
        return None

    if data_type not in ENDPOINTS:
        print(f"❌ 不支持的数据类型: {data_type}")
        return None

    url = f"{get_config_item(ALGORITHM_PLATFORM_URL)}/algoztdata_v10{ENDPOINTS[data_type]}"
    payload = {"his_win": his_win, "day_count": day_count, "percent": percent}
    headers = {"Content-Type": "application/json"}

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=timeout)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None