(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-df06b104"],{3967:function(e,t,a){"use strict";a.r(t);a("0643"),a("2382");var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mapLegend",class:{white:!e.$route.query.bigScreenNew},style:{right:e.right+24+"px"},attrs:{id:"oneMapLegend"}},["simulationEmulation"==e.$store.state.currentShowPanel||"valveClosingSimulation"==e.$store.state.currentShowPanel||"pipeFlushingSimulation"==e.$store.state.currentShowPanel||"waterShutdownSimulation"==e.$store.state.currentShowPanel?t("div",{staticClass:"paramSetLegend"},[t("div",{staticClass:"paramSetLegendHeader"},[t("div",{staticClass:"name"},[e._v(" "+e._s(e.currentTtile)+" ")])]),t("div",{staticClass:"split"}),t("el-select",{staticStyle:{"margin-bottom":"4px",width:"140px"},attrs:{placeholder:""},on:{change:e.attributeChange},model:{value:e.paramSetLegend.attribute,callback:function(t){e.$set(e.paramSetLegend,"attribute",t)},expression:"paramSetLegend.attribute"}},e._l(e.attributeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1),e._l(e.paramSetLegendList1,(function(a){return t("div",{directives:[{name:"show",rawName:"v-show",value:"DirectionChange"!=e.paramSetLegend.attribute,expression:"paramSetLegend.attribute != 'DirectionChange'"}],key:a.value,staticStyle:{"line-height":"28px","padding-left":"4px"}},[t("div",{staticClass:"legendIcon",staticStyle:{display:"inline-block","margin-right":"8px","border-radius":"0"},style:{background:a.color}}),t("span",[e._v(e._s(`${a.min} - ${a.max}`))])])})),t("div",{staticClass:"split"}),"DirectionChange"==e.paramSetLegend.attribute?t("div",e._l(e.paramSetLegendList2,(function(a){return t("div",{key:a.value,staticStyle:{"line-height":"28px","padding-left":"4px"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:"rect"==a.type,expression:"da.type == 'rect'"}],staticClass:"legendIcon",staticStyle:{display:"inline-block","margin-right":"8px"},style:{background:a.color,width:"30px",height:a.size}}),t("span",[e._v(e._s(""+a.name))])])})),0):e._e()],2):t("div",[e.mvtLayerData?t("div",{staticClass:"tab-box not-el-checkbox"},[t("el-tabs",{staticClass:"legend-tab",attrs:{stretch:!1},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{staticClass:"py-2",attrs:{label:"图例",name:"legend"}},[e.mvtLayerLegend.length>0?t("el-checkbox",{attrs:{indeterminate:e.isCheckedMvtAll,checked:""},on:{change:e.handleCheckMvtLegendAllChange},model:{value:e.checkMvtLegendAll,callback:function(t){e.checkMvtLegendAll=t},expression:"checkMvtLegendAll"}},[e._v(" "+e._s("全选")+" ")]):e._e(),t("el-checkbox-group",{on:{change:e.handleCheckedMvtLegendChange},model:{value:e.checkMvtLegend,callback:function(t){e.checkMvtLegend=t},expression:"checkMvtLegend"}},e._l(e.mvtLayerLegend,(function(a){return t("el-checkbox",{key:a.value,attrs:{label:a.value}},[t("div",{staticClass:"circle",staticStyle:{display:"inline-block","margin-right":"8px","border-radius":"0"},style:{background:a.color}}),t("span",[e._v(e._s(""+(a.value||a.max)))])])})),1)],1),t("el-tab-pane",{attrs:{label:"状态",name:"alarm"}},[e.legendData.length>0?t("el-checkbox",{attrs:{indeterminate:e.isCheckedAll,checked:""},on:{change:e.handleCheckLegendAllChange},model:{value:e.checkLegendAll,callback:function(t){e.checkLegendAll=t},expression:"checkLegendAll"}},[e._v(" "+e._s(`全选(${this.gridTreePoi.filter(e=>e.x&&e.y).length})`)+" ")]):e._e(),t("el-checkbox-group",{on:{change:e.handleCheckedLegendChange},model:{value:e.checkLegend,callback:function(t){e.checkLegend=t},expression:"checkLegend"}},e._l(e.legendData,(function(a){return t("el-checkbox",{key:a.id,attrs:{label:a.code}},[a.icon?t("div",{staticClass:"legend-icon"},[t("img",{attrs:{src:a.icon,alt:""}})]):a.color?t("div",{staticClass:"circle-border",style:{borderColor:a.color+"66"}},[t("div",{staticClass:"circle",style:{background:a.color,borderColor:a.color+"66"}})]):e._e(),t("span",{attrs:{title:a.name}},[e._v(e._s(a.name))]),t("span",[e._v(e._s(`(${a.value})`))])])})),1)],1)],1)],1):t("div",{staticStyle:{padding:"2px 0"},on:{mouseleave:function(t){e.activeOption=""}}},[t("el-tabs",{directives:[{name:"show",rawName:"v-show",value:!e.isCollapse,expression:"!isCollapse"}],staticClass:"legend-tab",attrs:{stretch:!1},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{staticClass:"py-2",attrs:{label:"图例",name:"legend"}},[t("div",{staticClass:"legendTools"},[t("div",{staticClass:"item-group"},[t("div",{staticClass:"tool-item legend-item",class:{active:!e.isLabel},on:{click:function(t){return e.handleClickLegendOption("icon")}}},[t("i",{staticClass:"iconfont icon-kapianshi"}),t("div",{staticClass:"text"},[e._v("图标")])]),t("i",{staticClass:"jiantou",class:"icon"==e.activeOption?"el-icon-arrow-up active":"el-icon-arrow-down",on:{click:function(t){return e.handleLegendOptionCollapse("icon")}}})]),t("div",{staticClass:"item-group"},[t("div",{staticClass:"tool-item legend-item",class:{active:e.isLabel},on:{click:function(t){return e.handleClickLegendOption("label")}}},[t("i",{staticClass:"iconfont icon-zhandianbiaoqian1"}),t("div",{staticClass:"text"},[e._v("数据标签")])]),t("i",{staticClass:"jiantou",class:"label"==e.activeOption?"el-icon-arrow-up active":"el-icon-arrow-down",on:{click:function(t){return e.handleLegendOptionCollapse("label")}}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:"icon"==e.activeOption,expression:"activeOption=='icon'"}],staticClass:"icon-option",class:{disabled:e.isLabel}},[t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendIconOption()}},model:{value:e.iconOption_.name.checked,callback:function(t){e.$set(e.iconOption_.name,"checked",t)},expression:"iconOption_.name.checked"}}),t("span",[e._v(e._s(e.iconOption_.name.label))])],1),t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendIconOption()}},model:{value:e.iconOption_.icon.checked,callback:function(t){e.$set(e.iconOption_.icon,"checked",t)},expression:"iconOption_.icon.checked"}}),t("span",[e._v(e._s(e.iconOption_.icon.label))])],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:"label"==e.activeOption,expression:"activeOption=='label'"}],staticClass:"icon-option",class:{disabled:!e.isLabel}},[t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendLabelOption()}},model:{value:e.labelOption_.name.checked,callback:function(t){e.$set(e.labelOption_.name,"checked",t)},expression:"labelOption_.name.checked"}}),t("span",[e._v(e._s(e.labelOption_.name.label))])],1),t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendLabelOption()}},model:{value:e.labelOption_.unit.checked,callback:function(t){e.$set(e.labelOption_.unit,"checked",t)},expression:"labelOption_.unit.checked"}}),t("span",[e._v(e._s(e.labelOption_.unit.label))])],1),t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendLabelOption()}},model:{value:e.labelOption_.value.checked,callback:function(t){e.$set(e.labelOption_.value,"checked",t)},expression:"labelOption_.value.checked"}}),t("span",[e._v(e._s(e.labelOption_.value.label))])],1),t("div",{staticClass:"legend-item"},[t("el-checkbox",{on:{change:function(t){return e.changeLegendLabelOption()}},model:{value:e.labelOption_.curve.checked,callback:function(t){e.$set(e.labelOption_.curve,"checked",t)},expression:"labelOption_.curve.checked"}}),t("span",[e._v(e._s(e.labelOption_.curve.label))])],1),t("div",{staticClass:"legend-item legend-item-input"},[t("span",[e._v(e._s(e.labelOption_.count.label))]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.labelOption_.count.number,expression:"labelOption_.count.number"}],attrs:{min:1,max:10,label:e.labelOption_.count.label},domProps:{value:e.labelOption_.count.number},on:{change:e.changeLegendLabelOption,input:function(t){t.target.composing||e.$set(e.labelOption_.count,"number",t.target.value)}}})])]),t("div",[e.placeLegend.length>0?t("el-checkbox",{attrs:{checked:!1},on:{change:e.handlePlaceLegendCheckAllChange},model:{value:e.placeLegendCheckAll,callback:function(t){e.placeLegendCheckAll=t},expression:"placeLegendCheckAll"}},[e._v(" "+e._s("全选")+" ")]):e._e(),t("div",{staticClass:"legend-main"},e._l(e.placeLegend,(function(a){return t("div",{key:a.code,staticClass:"legend-item",attrs:{label:a.code}},[t("el-checkbox",{attrs:{checked:!1},on:{change:function(t){return e.legendCheckChange(a)}},model:{value:a.checked,callback:function(t){e.$set(a,"checked",t)},expression:"da.checked"}}),a.icon&&"2"==a.type?t("img",{staticClass:"mr-1",attrs:{height:"20px",width:"20px",src:a.icon,alt:""}}):t("div",{staticClass:"legend-icon",style:a.style},[e._v(e._s(e.formatText(a.text)))]),t("span",[e._v(e._s(a.name))]),t("span",[e._v("（"+e._s(a.count)+"）")])],1)})),0)],1)]),t("el-tab-pane",{attrs:{label:"数据",name:"value"}},[e.dtypes.length>0?t("el-checkbox",{attrs:{checked:!1},on:{change:e.handleDatatypesAllChange},model:{value:e.datatypesCheckAll,callback:function(t){e.datatypesCheckAll=t},expression:"datatypesCheckAll"}},[e._v(" "+e._s("全选")+" ")]):e._e(),t("div",{staticClass:"dtype-box"},[t("el-checkbox-group",{model:{value:e.datatypesCheckList,callback:function(t){e.datatypesCheckList=t},expression:"datatypesCheckList"}},e._l(e.dtypes,(function(a){return t("el-checkbox",{key:a.Value,attrs:{label:a.Name,checked:!1},on:{change:e.changeDtype}},[t("span",[e._v(e._s(a.Name))])])})),1)],1)],1),t("el-tab-pane",{staticClass:"tab-alarm not-el-checkbox",attrs:{label:"状态",name:"alarm"}},[t("el-checkbox-group",{on:{change:e.handleCheckedLegendChange},model:{value:e.checkLegend,callback:function(t){e.checkLegend=t},expression:"checkLegend"}},e._l(e.legendData,(function(a){return t("el-checkbox",{key:a.id,attrs:{label:a.code}},[a.icon?t("div",{staticClass:"legend-icon"},[t("img",{attrs:{src:a.icon,alt:""}})]):a.color?t("div",{staticClass:"circle-border",style:{borderColor:a.color+"66"}},[t("div",{staticClass:"circle",style:{background:a.color,borderColor:a.color+"66"}})]):e._e(),t("span",{attrs:{title:a.name}},[e._v(e._s(a.name))]),t("span",[e._v(e._s(`(${a.value})`))])])})),1)],1)],1),t("div",{staticClass:"Collapse",on:{click:e.collapseClick}},[t("i",{class:{"el-icon-arrow-down":!e.isCollapse,"el-icon-arrow-up":e.isCollapse}})])],1)])])},l=[],i=(a("14d9"),a("4e3e"),a("a573"),a("2a49")),s=a("2f62"),c=a("1f3e"),o=a("a27e"),d=a("4360"),h=a("687c"),r={props:{right:{type:Number,default:480}},watch:{checkLegend:{handler:function(e,t){this.$store.commit("setState",{checkLegend:e}),"oneMap"==this.$store.state.mapType?this.$mapControls.addOneMapPoints(this.gridTreePoi):this.$mapControls.addGridTreePoi(this.gridTreePoi),this.$mapControls.legendMapPoi()}},mvtLayerData:{handler:function(e,t){e?(this.$store.state.mvtLayerAllLegend[e.value]?this.mvtLayerLegend=this.$store.state.mvtLayerAllLegend[e.value]:this.checkMvtLegend=this.mvtLayerLegend.map(e=>e.value),this.activeName="legend"):this.activeName="legend"}},oneMapPlaceData:{handler:function(e,t){this.placeLegend=e.map(e=>{var t;return{code:e.code,name:e.name,count:e.count,checked:!(null===(t=window)||void 0===t||null===(t=t.toolbarVue)||void 0===t||null===(t=t.placeModels)||void 0===t||!t.includes(e.code))}}),window.toolbarVue.placeCheckNodes=this.placeLegend,this.placeLegend.forEach(e=>{let t=this.modelBaseStyle[e.code]?this.modelBaseStyle[e.code].obj:null;if(t){var n;let a=JSON.parse(t).point||{};e.style=Object(i["a"])(this.defaultStyle),e.text=a.text||(null===e||void 0===e||null===(n=e.name)||void 0===n?void 0:n.substring(0,1))||"",e.style.background=a.color||e.style.background,e.style.borderColor=a.borderColor||e.style.borderColor;let l=a.width||e.style.width;l&&(l.includes("px")||(l+="px")),e.style.width=l,e.style.borderWidth=a.borderSize||e.style.borderWidth,e.icon=a.icon||"",e.type=a.type}else e.style=Object(i["a"])(this.defaultStyle),e.icon=a("ae09"),e.type="2"})}},labelOption:{handler:function(e,t){e&&(this.labelOption_={...e})}},iconOption:{handler:function(e,t){e&&(this.iconOption_={...e})}},"$store.state.currentShowPanel":{handler(e){document.getElementById("oneMapLegend").style.height="auto",document.getElementById("oneMapLegend").style.width="auto",document.getElementById("oneMapLegend").style.minWidth="143px",document.getElementById("oneMapLegend").style.minHeight="143px",this.attributeChange(this.paramSetLegend.attribute)}},"$store.state.simulationEmulationCalculationData":{deep:!0,handler(e,t){e&&(this.paramSetLegendRange.MinPressure=e.MinPressure||0,this.paramSetLegendRange.MaxPressure=e.MaxPressure||1,this.paramSetLegendRange.MinFlow=e.MinFlow||0,this.paramSetLegendRange.MaxFlow=e.MaxFlow||1,this.paramSetLegendRange.MinVelocity=e.MinVelocity||0,this.paramSetLegendRange.MaxVelocity=e.MaxVelocity||1,this.attributeChange(this.paramSetLegend.attribute))}}},data(){return{isCollapse:!1,activeName:"legend",isCheckedAll:!1,checkLegendAll:!1,checkLegend:[1,2,3],placeLegend:[],defaultStyle:{width:"20px",height:"20px",background:"#034316",borderWidth:"2px",borderStyle:"solid",borderColor:"#A2FFB5",borderRadius:"50%",textAlign:"center",lineHeight:"17px",fontSize:"10px",color:"#A2FFB5"},visible:!1,isCheckedMvtAll:!1,checkMvtLegendAll:!1,checkMvtLegend:[],mvtLayerLegend:[],mvtLayerLegendDialog:[],paramSetLegendExpand:!1,paramSetLegendRange:{MinPressure:0,MaxPressure:0,MinFlow:0,MaxFlow:0,MinVelocity:0,MaxVelocity:0},attributeOptions:[{name:"压力变化(Mpa)",value:"PressureChange"},{name:"流量变化(m³/h)",value:"FlowChange"},{name:"流速变化(m/s)",value:"VelocityChange"},{name:"流向变化",value:"DirectionChange"}],paramSetLegend:{checkAll:!1,attribute:"PressureChange",checkList1:[],checkList2:["add","reduce"]},paramSetLegendList1:[{color:"#f1d61f",min:"0.22",max:"0.32",value:"1"},{color:"#e0a310",min:"0.32",max:"0.43",value:"2"},{color:"#d57107",min:"0.43",max:"0.53",value:"3"},{color:"#ce4200",min:"0.53",max:"0.64",value:"4"},{color:"#9f1b00",min:"0.64",max:"0.74",value:"5"}],paramSetLegendList2:[],currentTtile:"",datatypesCheckList:[],datatypesCheckAll:!1,placeLegendCheckAll:!1,iconOption_:{name:{label:"名称",checked:!0},icon:{label:"图标",checked:!0}},labelOption_:{name:{label:"名称",checked:!0},unit:{label:"数据单位",checked:!0},value:{label:"数据值",checked:!0},curve:{label:"数据曲线",checked:!0},count:{label:"数据行",number:2}},activeModel:"",activeOption:"",first:!0}},mounted(){window.mapLegendVue=this,setInterval(()=>{var e,t;if(this.isLabel&&null!==(e=this.datatype)&&void 0!==e&&e.length&&!this.labelListPostId){var a,n;if(null!==(t=window.map3d)&&void 0!==t&&t.active)if(null===(a=window.map3d)||void 0===a||null===(a=a.map)||void 0===a||null===(a=a.viewer)||void 0===a||null===(a=a.controls)||void 0===a||!a.autoRotate||null===(n=window)||void 0===n||null===(n=n.map3d)||void 0===n||!n.visibilityState)return;this.updateLable(!0)}},9e4)},computed:{...Object(s["d"])(["legendData","modelBaseStyle","oneMapPlaceData","gridTreePoi","projectId","mvtLayerData","useGis","paramData","datatype","isLabel","labelOption","iconOption"]),dtypes(){let e=[];return e=(this.paramData||[]).map(e=>({Name:e.name,Value:e.id,kind:e.kind})),e}},methods:{updateLabelOption(){window.oneMapVue.$store.commit("setState",{labelOption:{...this.labelOption_}})},handleLegendOptionCollapse(e){this.activeOption==e?this.activeOption="":this.activeOption=e},handleClickLegendOption(e){let t;"label"!=e||this.isLabel||(t=!0),window.oneMapVue.$store.commit("setState",{isLabel:t}),this.updateLable(t)},changeLegendIconOption(){var e;this.isLabel||(window.oneMapVue.$store.commit("setState",{iconOption:{...this.iconOption_}}),null===(e=window.map3d)||void 0===e||e.setIcon({isName:this.iconOption_.name.checked,isIcon:this.iconOption_.icon.checked}))},changeLegendLabelOption(){this.updateLabelOption()},showCurve(e,t){e.stopPropagation()},changeDtype(){const e=[],t=this.dtypes;for(let a=0;a<t.length;a++)this.datatypesCheckList.includes(t[a].Name)&&e.push(t[a].Value);this.datatypesCheckList.length&&this.datatypesCheckList[0]&&this.datatypesCheckList.length===t.length?this.datatypesCheckAll=!0:this.datatypesCheckAll=!1,this.updateDtype(e)},updateDtype(e){var t;(this.$store.commit("setState",{datatype:e}),d["a"].state.mapDataParams.dataType=e,this.isLabel)&&(e.length?this.updateLable(!0):null===(t=window.map3d)||void 0===t||t.showLabel(!1,{notShowPoint:!0}))},handleDatatypesAllChange(e){const t=this.dtypes,a=e?t.map(e=>e.Value):[];this.datatypesCheckList=e?t.map(e=>e.Name):[],this.updateDtype(a)},updateDatatypesCheckList(e){if(null!==e&&void 0!==e&&e.length){const t=this.dtypes;this.datatypesCheckList=t.map(t=>{if(e.includes(t.Value))return t.Name}),this.datatypesCheckList.length&&this.datatypesCheckList[0]&&this.datatypesCheckList.length===t.length?this.datatypesCheckAll=!0:this.datatypesCheckAll=!1}else this.datatypesCheckAll=!0,this.handleDatatypesAllChange(!0)},legendCheckChange(e){console.log({placeCheckNode:e});let t=[];for(let a=0;a<this.placeLegend.length;a++)this.placeLegend[a].checked&&t.push(this.placeLegend[a].code);this.updatePlaceLegend(t,e),e.checked&&window.postMessage({action:"MAP-EVENT",params:{method:"modelChange",args:e}},"*")},handlePlaceLegendCheckAllChange(e){let t=[];for(let a=0;a<this.placeLegend.length;a++)e?(this.placeLegend[a].checked=!0,t.push(this.placeLegend[a].code)):this.placeLegend[a].checked=!1;window.toolbarVue.placeModels=t,console.log({checkNodes:t}),this.updatePlaceLegend(t)},updatePlaceLegend(e,t){const a=this;e||(e=window.toolbarVue.placeModels),window.toolbarVue.placeModels=e,d["a"].state.mapDataParams.placeModels=e;for(let c in null===(n=window.map3d)||void 0===n?void 0:n.markerLayerCollection){var n;const t=window.map3d.markerLayerCollection[c];null!==t&&void 0!==t&&t.modelId&&(e.includes(null===t||void 0===t?void 0:t.modelId)?t.show():t.hide())}var l,i;if(this.isLabel)if(null!==(l=e)&&void 0!==l&&l.length){if(!t||null!==t&&void 0!==t&&t.checked)setTimeout(()=>{a.updateLable(!0)},a.first?1e3:100);else if(t){var s;null===(s=window.map3d.markerLayerCollection[t.name])||void 0===s||s.showLabel(!1,{notShowPoint:!1})}}else null===(i=window.map3d)||void 0===i||i.showLabel(!1,{notShowPoint:!1});for(let c=0;c<this.placeLegend.length;c++)e.includes(this.placeLegend[c].code)?this.placeLegend[c].checked||(this.placeLegend[c].checked=!0):this.placeLegend[c].checked&&(this.placeLegend[c].checked=!1);this.placeLegend.length&&this.placeLegend[0]&&this.placeLegend.length===e.length?this.placeLegendCheckAll=!0:this.placeLegendCheckAll=!1},collapseClick(){this.isCollapse=!this.isCollapse,this.isCollapse?(document.getElementById("oneMapLegend").style.height="36px",document.getElementById("oneMapLegend").style.width="36px",document.getElementById("oneMapLegend").style.minWidth="36px",document.getElementById("oneMapLegend").style.minHeight="36px"):(document.getElementById("oneMapLegend").style.height="auto",document.getElementById("oneMapLegend").style.width="auto",document.getElementById("oneMapLegend").style.minWidth="143px",document.getElementById("oneMapLegend").style.minHeight="143px")},updateLable(e){if(e){var t;null!==(t=window.map3d)&&void 0!==t&&t.active||(window.oneMapVue.closeAllInfoWindow(),window.map.mapCollections.point.hide(),window.map.mapCollections.label.hide(),window.map.mapCollections.allLabel.hide()),this.$store.commit("setState",{analysisLoading:!0});const e=this.labelListPostId=Object(h["d"])();Object(o["e"])("/imb/toolbar/label/list",d["a"].state.mapDataParams).then(t=>{if(e!==this.labelListPostId)return;this.labelListPostId=null,this.$store.commit("setState",{analysisLoading:!1});const a={};t.forEach(e=>{var t;null!==(t=window)&&void 0!==t&&null!==(t=t.toolbarVue)&&void 0!==t&&null!==(t=t.placeModels)&&void 0!==t&&t.includes(null===e||void 0===e?void 0:e.modelId)&&(a[e.id]=e)}),this.$store.commit("setState",{labelInfoWindowData:a}),window.oneMapVue.openAllInfoWindow("label"),window.map3d.showLabel(!0)}).catch(e=>{this.$store.commit("setState",{analysisLoading:!1})})}else{var a;if(null!==(a=window.map3d)&&void 0!==a&&a.active)window.map3d.showLabel(!1),this.changeLegendIconOption();else{var n;const e=null===(n=window.map)||void 0===n||null===(n=n.mapCollections)||void 0===n?void 0:n.label;isLabel&&(e.show(),window.map.mapCollections.allLabel.show())}}},formatText(e){let t=String(e);if(1==t.length)return t;if(t.length>=2){let e=t.slice(0,2);return this.$utils.getLen(e)>1?e.slice(0,1):e}},handleCheckLegendAllChange(e){this.checkLegend=e?this.legendData.map(e=>e.code):[],this.isCheckedAll=!1},handleCheckedLegendChange(e){this.checkLegendAll=e.length===this.legendData.length,this.isCheckedAll=e.length>0&&e.length<this.legendData.length},handleCheckMvtLegendAllChange(e){this.checkMvtLegend=e?this.mvtLayerLegend.map(e=>e.value):[],this.isCheckedMvtAll=!1,this.showHideMvt()},handleCheckedMvtLegendChange(e){this.checkLegendMvtAll=e.length===this.mvtLayerLegend.length,this.isCheckedMvtAll=e.length>0&&e.length<this.mvtLayerLegend.length,this.showHideMvt()},showHideMvt(){var e;if(null!==(e=this.mvtLayerData)&&void 0!==e&&e.id){let e=Object(i["a"])(this.$store.state.mvtLayerAllLegend[this.mvtLayerData.value]);e.forEach(e=>{this.checkMvtLegend.includes(e.value)?e.opacity=1:e.opacity=0}),this.$store.state.mvtLayerAllLegend[this.mvtLayerData.value]=e;const t=this.$mapControls.handleMvtStyle(e);this.$mapControls.updateStyleForMVTLayer(this.mvtLayerData,t)}},handleData(e,t){const a=this.mvtLayerLegendDialog.length;e.operator&&(1==t&&e.min&&(this.mvtLayerLegendDialog[0].max=e.min),t==a-2&&e.max&&(this.mvtLayerLegendDialog[a-1].max=e.max))},delLegend(e){this.mvtLayerLegendDialog.splice(e,1)},openDialog(){this.visible=!0,this.mvtLayerLegendDialog=Object(i["a"])(this.mvtLayerLegend)},submitLegendToProject(){const e={id:this.projectId,tuglie:JSON.stringify(Object(i["a"])(this.$store.state.mvtLayerAllLegend))};Object(c["c"])(e).then(e=>{this.$message.success("保存成功")})},paramSetLegendChackAll(e){e?(this.paramSetLegend.checkList1=this.paramSetLegendList1.map(e=>e.value),this.paramSetLegend.checkList2=this.paramSetLegendList2.map(e=>e.value)):(this.paramSetLegend.checkList1=[],this.paramSetLegend.checkList2=[])},checkList2Change(){this.$store.commit("setState",{paramSetLegendChecked:this.paramSetLegend.checkList2})},onExpand(){this.paramSetLegendExpand=!this.paramSetLegendExpand},attributeChange(e){this.paramSetLegend.checkList2=["add","reduce"],this.handlerParamSetLegendData(e);let t={attribute:this.paramSetLegend.attribute,legendList:this.paramSetLegendList1};this.$store.commit("setState",{paramSetLegendInfo:t})},handlerParamSetLegendData(e){let t=0,a=1,n=[];"DirectionChange"!==e?("PressureChange"===e&&(n=["#51D4FE","#48f65f","#ffba12","#ff7532","#C54B64"],t=this.paramSetLegendRange.MinPressure,a=this.paramSetLegendRange.MaxPressure||1),"FlowChange"===e&&(n=["#df1614","#ffb35f","#fdc98e","#a8dced","#277aba"],t=this.paramSetLegendRange.MinFlow,a=this.paramSetLegendRange.MaxFlow||1,this.paramSetLegendList2=[{color:"red",size:"12px",type:"rect",name:"流量增加的管线",value:"add"},{color:"red",size:"6px",type:"rect",name:"流量减小的管线",value:"reduce"}]),"VelocityChange"===e&&(n=["#df1614","#ffb35f","#fdc98e","#a8dced","#277aba"],t=this.paramSetLegendRange.MinVelocity,a=this.paramSetLegendRange.MaxVelocity||1,this.paramSetLegendList2=[{color:"red",size:"12px",type:"rect",name:"流速增加的管线",value:"add"},{color:"red",size:"6px",type:"rect",name:"流速减小的管线",value:"reduce"}]),this.paramSetLegendList1=this.createLegends(t,a,5,n)):this.paramSetLegendList2=[{color:"#FF0000",size:"8px",type:"rect",name:"流向变化的管线",value:"add"},{color:"#6fb3ff",size:"8px",type:"rect",name:"流向未变化的管线",value:"reduce"}]},createLegends(e,t,a,n){if(!a||0===a)return[];let l=[];e==t&&(a=1);let i=parseFloat(((t-e)/a).toFixed(3));for(let s=1;s<=a;s++){let c=e+i*(s-1),o=s==a?t:e+i*s;const d={id:s,min:c.toFixed(3),max:o.toFixed(3),color:n[s-1]};l.push(d)}return l}}},p=r,g=(a("c864"),a("8f2c"),a("2877")),u=Object(g["a"])(p,n,l,!1,null,"76d95374",null);t["default"]=u.exports},"8f2c":function(e,t,a){"use strict";a("c3c8")},c3c8:function(e,t,a){},c864:function(e,t,a){"use strict";a("e049")},e049:function(e,t,a){}}]);