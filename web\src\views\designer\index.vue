<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <!-- 左侧：Logo、应用名称和工作流信息 -->
      <div class="toolbar-brand">
        <el-icon class="back-page-icon" @click="onRouteBack">
          <Back />
        </el-icon>
        <div class="workflow-info">
          <span class="title">{{ workflowName }}<el-icon><EditPen /></el-icon></span>
          <el-input
            v-model="workflowName"
            placeholder="工作流名称"
            size="small"
            class="workflow-name-input"
            @blur="updateWorkflowName"
            @keyup.enter="updateWorkflowName"
          />
        </div>
        <el-tooltip
          :content="workflowStore.hasUnsavedChanges ? '工作流有未保存的更改' : '工作流已保存'"
          placement="bottom"
        >
          <div class="save-status" :class="{ 'has-changes': workflowStore.hasUnsavedChanges }">
            <el-icon v-if="workflowStore.hasUnsavedChanges" :size="14" color="#f56c6c">
              <Warning />
            </el-icon>
            <el-icon v-else :size="14" color="#BCBFC3">
              <CircleCheck />
            </el-icon>
            <span class="status-text">
              {{ workflowStore.hasUnsavedChanges ? '未保存' : '已保存' }}
            </span>
            <span v-if="lastAutoSaveTime && !workflowStore.hasUnsavedChanges" class="auto-save-text">
              自动保存 {{ lastAutoSaveTime }}
            </span>
          </div>
        </el-tooltip>
      </div>

      <!-- 中间：操作按钮 -->
      <div class="toolbar-actions"></div>

      <!-- 右侧：设置按钮 -->
      <div class="toolbar-right">
        <el-tooltip :content="reportUrl ? '说明' : '暂无说明'" placement="bottom">
          <el-button
            style="margin-right: 12px;"
            @click="openReportUrl"
            :disabled="!reportUrl"
          >
            <template #icon>
              <i class="action-iconfont icon-zhihangrenwu" style="font-size: 12px;"></i>
            </template>
            说明
          </el-button>
        </el-tooltip>
        <el-tooltip content="问题" placement="bottom">
          <invalid-nodes-panel @select="triggerFocus" v-model="showInvalidNodes"/>
        </el-tooltip>
        <el-tooltip content="智能录制 - 一键开始录制浏览器操作" placement="bottom">
          <el-button
            @click="startIntelligentRecording"
            :icon="VideoCamera"
            :loading="isRecording"
            :disabled="isExecuting"
          >
            {{ isRecording ? '智能录制中...' : '智能录制' }}
          </el-button>
        </el-tooltip>
        <el-tooltip content="暂停/恢复录制" placement="bottom" v-if="isRecording">
          <el-button
            @click="toggleRecording"
            :icon="recordingPaused ? VideoPlay : VideoPause"
            :disabled="!isRecording"
          >
            {{ recordingPaused ? '恢复' : '暂停' }}
          </el-button>
        </el-tooltip>
        <el-tooltip content="停止录制并生成工作流" placement="bottom" v-if="isRecording">
          <el-button
            @click="stopIntelligentRecording"
            :icon="TurnOff"
            :disabled="!isRecording"
          >
            停止录制
          </el-button>
        </el-tooltip>
        <el-tooltip content="调试工作流 (Ctrl+F5)" placement="bottom">
          <el-button
            @click="executeWorkflow"
            :icon="VideoPlay"
            :loading="isExecuting"
          >
            {{ isExecuting ? '执行中...' : '调试' }}
          </el-button>
        </el-tooltip>
        <el-tooltip content="停止执行" placement="bottom">
          <el-button
            @click="stopExecution"
            :icon="VideoPause"
            plain
            :disabled="!isExecuting"
            :type="isExecuting ? 'danger' : ''"
            >停止
          </el-button>
        </el-tooltip>
        <el-dropdown trigger="hover">
          <el-button style="margin-left: 12px;">更多<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
          <template #dropdown>
            <div class="template-designer-title" v-if="false">更多功能</div>
            <el-dropdown-menu>
              <!-- <el-dropdown-item @click="openSysetemSetting"><i class="action-menu-icon action-iconfont icon-shezhi"></i>系统设置</el-dropdown-item> -->
              <el-dropdown-item @click="openLogPanel"><i class="action-menu-icon action-iconfont icon-zhihangrenwu"></i>执行记录</el-dropdown-item>
              <el-dropdown-item @click="openLocalVariables"><i class="action-menu-icon action-iconfont icon-bianliangguanli"></i>变量管理</el-dropdown-item>
              <el-dropdown-item @click="openReportDesigner('excel')"><i class="action-menu-icon action-iconfont icon-Excelwenjian"></i>Excel 模板库</el-dropdown-item>
              <el-dropdown-item @click="openReportDesigner('word')"><i class="action-menu-icon action-iconfont icon-Wordwenjian"></i>Word 模板库</el-dropdown-item>
              <el-dropdown-item @click="openWorkflow"><i class="action-menu-icon action-iconfont icon-wenjiandaoru"></i>导入工作流</el-dropdown-item>
              <el-dropdown-item @click="exportWorkflow"><i class="action-menu-icon action-iconfont icon-wenjiandaochu"></i>导出工作流</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-tooltip v-if="missionData.id" content="保存工作流 (Ctrl+S)" placement="bottom">
          <el-button @click="saveWorkflow" :icon="Document" type="primary" style="margin-left: 12px;">保存</el-button>
        </el-tooltip>
        <!--          <el-button @click="showSettings" :icon="QuestionFilled">帮助</el-button>-->
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧组件面板 -->
      <div class="left-panel" :class="{'collapsed': isCollapsed}">
        <el-segmented class="panel-component-tabs" v-model="activeComponentPanel" :options="componentPanelTabs" block size="large">
          <template #default="scope">
            <div class="scope-label">
              <i class="action-iconfont" :class="scope.item.icon"></i>
              <div>{{ scope.item.label }}</div>
            </div>
          </template>
        </el-segmented>
        <div class="panel-content-tab" v-show="activeComponentPanel === 'components'">
          <ComponentPanel />
        </div>
        <div class="panel-content-tab" v-show="activeComponentPanel === 'packages'">
          <PackagePanel @imported-template="handleImportedTemplate" @template-saved="onTemplateSaved"/>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <WorkflowCanvas ref="workflowCanvas"/>
      </div>

      <!-- 右侧属性面板 / 右侧日志面板 -->
      <div class="right-panel" v-if="selectedNode">
        <SmartPropertyPanel />
      </div>
      <div class="right-panel" v-show="logNewStore.logPanelVisible">
        <LogRightPanel />
      </div>
    </div>
    <!-- 局部变量组件 -->
    <variable-panel v-model="isLocalVariablesVisible" />

    <!-- 模板设计器组件 -->
    <report-designer :type="reportType" :template-id="reportId" v-model="isReportDesignerVisible" :is-from-node="isFromNode" @template-saved="onTemplateSaved" @template-deleted="onTemplateDeleted" @template-closed="onTemlateClosed"/>
    <!-- 系统设置组件 -->
    <div class="system-setting" v-show="showSystemSetting">
      <system-setting v-if="showSystemSetting" v-model="showSystemSetting" @close="showSystemSetting=false"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import moment from "moment";
import {
  Document,
  VideoPlay,
  VideoPause,
  VideoCamera,
  TurnOff,
  Warning,
} from '@element-plus/icons-vue'

import ComponentPanel from '@/components/panels/ComponentPanel.vue'
import WorkflowCanvas from '@/components/canvas/WorkflowCanvas.vue'
import SmartPropertyPanel from '@/components/panels/SmartPropertyPanel.vue'
import VariablePanel from '@/components/panels/VariablePanel.vue'
import ReportDesigner from '@/components/panels/ReportDesigner.vue'
import InvalidNodesPanel from '@/components/panels/InvalidNodesPanel.vue'
import LogRightPanel from '@/components/panels/LogRightPanel.vue'
import systemSetting from './systemSetting.vue'
import PackagePanel from '@/components/panels/PackagePanel.vue'

import {useWorkflowStore, type WorkflowData} from '@/stores/workflow'
import { useLogNewStore } from '@/stores/logNew'
import { usePanelStore } from '@/stores/panel'
import { useTargetSourceStore } from '@/stores/targetSource'

import { NotificationService } from '@/utils/notificationService'

// @ts-ignore
import saasApi from '@/api/index'
// @ts-ignore
import utils from '@/utils/utils'

import { useRoute, useRouter } from 'vue-router'
import {
  componentCategories,
  type ComponentCategory,
  type ComponentDefinition
} from "@/utils/componentCategories.ts";

const route = useRoute()
const router = useRouter()

const workflowStore = useWorkflowStore()
const logNewStore = useLogNewStore()
const panelStore = usePanelStore()
const targetSourceStore = useTargetSourceStore()

// 是否执行状态
const isExecuting = computed(()=>{
  return logNewStore.isExecuting
})
const reportType = ref('excel')
const reportId = ref('')
const isRecording = ref(false)
const recordingPaused = ref(false)
const showSystemSetting = ref(false)
const workflowName = ref('')
const workflowCanvas = ref(null);

// 左侧面板
const componentPanelTabs = ref([
  { label: '指令库', value: 'components', icon: 'icon-zujian' },
  { label: '服务包', value: 'packages', icon: 'icon-gouwuche' }
])
const activeComponentPanel = ref('components')

// 新增折叠状态
const isCollapsed = computed(() => {
  return panelStore.isCollapsed
})

// 监听左侧面板切换
watch(
  () => activeComponentPanel.value,
  (newValue) => {
    if (newValue === 'packages') {
      panelStore.setCollapsed(false)
    }
  }
)

// 任务信息
const missionData = ref({ id: null })

// 是否显示检查清单
const showInvalidNodes = ref(false)

// 智能录制相关状态
const intelligentRecordingId = ref<string | null>(null)
const recordingStats = ref({
  operation_count: 0,
  duration: 0,
  nodes_generated: 0,
})
const realTimeOperations = ref<any[]>([])
const recordingStatusTimer = ref<number | null>(null)

// 获取选定组件
const selectedNode = computed(() => workflowStore.selectedNode)

// 获取token
const authorization = utils.GetAuthorization()

// 定义局部变量组件是否显示
const isLocalVariablesVisible = ref(false)
const openLocalVariables = () => {
  isLocalVariablesVisible.value = true
}

// 打开执行日志面板
const openLogPanel = () => {
  logNewStore.setLogPanel(true)
}

//
const openSysetemSetting = () => {
  showSystemSetting.value = true;
}

// 定义模板设计器是否显示
const isReportDesignerVisible = ref(false)
// 定义是否来源自节点的创建模板
const isFromNode = ref(false)
const openReportDesigner = (type: string, id: string | '') => {
  reportType.value = type
  reportId.value = id
  isReportDesignerVisible.value = true
  // 取消选中节点
  // setTimeout(()=>{
  //   workflowStore.clearSelection()
  // }, 2000)
}

// 监听来自配置面板的模板设计器打开事件
const handleOpenReportDesigner = (event: CustomEvent) => {
  console.log('收到打开模板设计器事件:', event.detail)
  isFromNode.value = true
  const template_type = event.detail?.template_type
  const template_id = event.detail?.template_id
  openReportDesigner(template_type || 'excel', template_id)
}

// 在组件挂载时添加事件监听器
onMounted(() => {
  document.addEventListener('open-report-designer', handleOpenReportDesigner as EventListener)
})

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('open-report-designer', handleOpenReportDesigner as EventListener)
})

//定位节点
const triggerFocus = async (nodeId: string) => {
  await nextTick();
  if (workflowCanvas) {
    workflowCanvas.value.focusOnNode(nodeId);
  }
};

// 处理模板保存
const onTemplateSaved = (template: any) => {
  // 可以在这里刷新Excel组件的模板选项
  // 或者触发其他相关的更新操作

  // 通过事件总线或者直接调用父组件的方法来打开模板设计器
  // 这里我们使用一个简单的方法：触发一个自定义事件
  const event = new CustomEvent('refresh-template-select', {
    bubbles: true,
    detail: { source: 'designer' }
  })
  document.dispatchEvent(event)

  // 导入后打开模板设计界面并加载模板
  const event1 = new CustomEvent('open-report-designer', {
    bubbles: true,
    detail: { source: 'template-selector', template_type: template.templateType, template_id: template.id }
  })
  document.dispatchEvent(event1)
}
// 处理模板删除
const onTemplateDeleted = (id: string) => {
  // 通过事件总线或者直接调用父组件的方法来打开模板设计器
  // 这里我们使用一个简单的方法：触发一个自定义事件
  const event = new CustomEvent('refresh-template-select', {
    bubbles: true,
    detail: { source: 'designer' }
  })
  document.dispatchEvent(event)
}

// 处理模板关闭
const onTemlateClosed = () => {
  isFromNode.value = false
}

// 监听工作流元数据变化，同步工作流名称
watch(
  () => workflowStore.metadata.name,
  (newName) => {
    workflowName.value = newName
  },
  { immediate: true },
)
// 页面销毁时执行终止录制
onUnmounted(() => {
  stopIntelligentRecording(false)
})

// 打开任务介绍
const openReportUrl = () => {
  if (reportUrl.value) {
    route.query.uniwim === 'pc'
      ? window.open(reportUrl.value, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
      : window.open(reportUrl.value)
  }
}

// 使用服务包模板导入工作流
const handleImportedTemplate = (template: WorkflowData) => {
  try {
    // 导入工作流不处理任务名称
    if(workflowName.value){
      template.metadata.name = workflowName.value
    }
    workflowStore.loadWorkflow(template)
    workflowStore.markAsChanged()

    ElMessage.success({ message: '工作流加载成功', showClose: true })
  } catch (error) {
    ElMessage.error({ message: '文件格式错误', showClose: true })
  }
}

// 新建工作流
const newWorkflow = async () => {
  if (workflowStore.hasUnsavedChanges) {
    try {
      await ElMessageBox.confirm('当前工作流有未保存的更改，是否继续？', '确认', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning',
      })
    } catch {
      return
    }
  }

  workflowStore.newWorkflow()
  ElMessage.success({ message: '已创建新工作流', showClose: true })
}

// 打开工作流
const openWorkflow = async () => {
  // 1. 添加确认提示框
  await ElMessageBox.confirm(
    `此操作将重置当前工作流，请确认是否继续？`,
    '导入工作流',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  // Web环境：使用文件输入
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      try {
        const text = await file.text()
        const workflowData = JSON.parse(text)
        // 导入工作流不处理任务名称
        if(workflowName.value){
          workflowData.metadata.name = workflowName.value
        }
        workflowStore.loadWorkflow(workflowData)
        workflowStore.markAsChanged()
        ElMessage.success({ message: '工作流加载成功', showClose: true })
      } catch (error) {
        ElMessage.error({ message: '文件格式错误', showClose: true })
      }
    }
  }
  input.click()
}

// 保存工作流前对数据处理钩子
const transWorkFlowData = (workflowData: WorkflowData) => {
  if (workflowData.nodes) {
    workflowData.nodes.forEach((item) => {
      // 处理变量赋值数据
      if (item.data?.componentType === 'variable_assignment') {
        if (item.data.config.variables && typeof item.data.config.variables === 'object') {
          item.data.config.variables = JSON.stringify(item.data.config.variables)
        }
      }
      // http 请求的提取变量保存前过滤掉没有填写variable变量名的值
      else if (item.data?.componentType === 'http_request') {
        if (item.data.config?.extract_variable?.length) {
          item.data.config.extract_variable = item.data.config.extract_variable.filter((f: any) => !!f.variable.trim())
        }
      }
    })
  }
  return workflowData
}

// 保存工作流
const saveWorkflow = async () => {
  if (workflowStore.invalidNodes.length > 0) {
    showInvalidNodes.value = true
    // ElMessage.error({
    //   message: `存在 ${workflowStore.invalidNodes.length} 个节点的配置未通过校验，请修正后再保存。具体请查看‘检查清单’`,
    //   showClose: true
    // });
    // return;
  }

  // 存在任务信息则保存的时候走接口。否则保存到本地
  if (missionData.value.id) {
    const loading = ElLoading.service({
      lock: true,
      fullscreen: true,
    })
    try {
      const workflowData = transWorkFlowData(workflowStore.getWorkflowData())
      console.warn({ workflowData })
      const params = { ...missionData.value, configContent: JSON.stringify(workflowData) }
      // 替换任务名称
      params.missionName = workflowData.metadata.name
      // 保存接口
      saasApi
        .AIAgentMissionUpdateRobot(params)
        .then((res) => {
          if (res.Code === 0) {
            workflowStore.markAsSaved()
            lastAutoSaveTime.value = ''  // 清除自动保存时间标识
            ElMessage.success({ message: '工作流已保存', showClose: true })
          } else {
            throw Error(res)
          }
        })
        .catch(() => {
          ElMessage.error({ message: '保存失败', showClose: true })
        })
        .finally(() => {
          loading.close()
        })


      // 开始节点修改任务数据逻辑
      const editMissionData = workflowStore.missionData

      if (JSON.stringify(missionData.value) !== JSON.stringify(editMissionData)) {
        // 处理周期数据
        if (editMissionData.specify) {
          editMissionData.specify = moment(editMissionData.specify).valueOf()
        }
        editMissionData.timeScheduled = onModelToCron(editMissionData)

        // 同步missionData数据
        missionData.value = {
          ...editMissionData
        }

        const params2 = {
          ...editMissionData,
          missionName: workflowData.metadata.name,
          missionStartTime: moment(editMissionData.missionStartTime).valueOf(),
          missionEndTime: editMissionData.missionEndTime ? moment(editMissionData.missionEndTime).valueOf() : null,
        }
        // 保存任务信息
        saasApi
          .AIAgentMissionUpdate(params2)
          .then((res) => {
            if (res.Code === 0) {
              console.log('任务信息已保存');
            } else {
              throw Error(res)
            }
          })
          .finally(() => {
          })
      }
    } catch (error) {
      ElMessage.error({ message: '保存失败', showClose: true })
      loading && loading.close()
    }
  }
  else {
    ElMessage.error({ message: '不存在任务ID，无法保存', showClose: true })
  }
}
// 保存任务数据时model转成cron 格式转换
const onModelToCron = (model) => {
  switch (model.execution) {
    // 按分钟
    case 1:
      return `1,${model.minutes}`
    // 按小时
    case 2:
      return `2,${model.hours}`
    // 按天
    case 3:
      return `3,${model.days}`
    // 按周
    case 4:
      return `4,${model.weeks.join(',')}`
    // 指定时间
    case 5:
      return `5,${model.specify}`
  }
}
// 导出工作流
const exportWorkflow = async () => {
  // Web环境：下载文件
  try {
    const workflowData = workflowStore.getWorkflowData()
    const dataStr = JSON.stringify(workflowData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${workflowName.value || 'workflow'}-${moment().format("YYYYMMDDHHmmss")}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    workflowStore.markAsSaved()
    ElMessage.success({ message: '工作流已下载', showClose: true })
  } catch (error) {
    ElMessage.error({ message: '保存失败', showClose: true })
  }
}

// 执行工作流
const executeWorkflow = async () => {
  // 节点数量检测
  if (workflowStore.nodes.length === 0) {
    ElMessage.warning({ message: '请先添加一些节点到工作流中', showClose: true })
    return
  }
  // 问题清单检测
  if (workflowStore.invalidNodes.length > 0) {
    showInvalidNodes.value = true
    return;
  }
  // 清除当前选中面板
  if(!logNewStore.logPanelVisible){
    workflowStore.clearSelection()
  }
  // 打开日志面板
  logNewStore.setLogPanel(true)
  // 设置日志执行状态
  logNewStore.setLogStoped(false)
  logNewStore.setLogExecuting(true)
  // 清除日志记录
  logNewStore.clearLogs()

  // 检查WebSocket连接
  window.checkWebSocketConnection();

  try {
    const workflowData = workflowStore.getWorkflowData()

    // 处理变量赋值数据
    if (workflowData.nodes) {
      workflowData.nodes.forEach((item) => {
        if (item.data?.componentType === 'variable_assignment') {
          if (item.data.config.variables && typeof item.data.config.variables === 'object') {
            item.data.config.variables = JSON.stringify(item.data.config.variables)
          }
        }
      })
    }

    // 检查是否有用户交互组件，如果有则先处理用户输入
    const { UserInteractionHandler } = await import('@/utils/userInteraction')

    if (UserInteractionHandler.hasUserInteractions(workflowData.nodes)) {

      try {
        const userInputs = await UserInteractionHandler.handleUserInteractions(workflowData.nodes)
        // 将用户输入注入到工作流数据中
        workflowData.nodes = UserInteractionHandler.injectUserInputs(workflowData.nodes, userInputs)
      } catch (error) {
        ElMessage.error({ message: '用户输入处理失败', showClose: true })
        return
      }
    }

    const result = await executeWorkflowViaAPI(workflowData)
    if (result) {
      // 设置执行返回数据到store (history,taskId)
      logNewStore.setLogHistoryInfo(result.historyId, result.taskId)
      // 添加一次性监听事件，监听isExecuting为false的时候弹出执行成功消息
      const unWatch = watch(
        () => logNewStore.isExecuting,
        (newVal) => {
          if (!newVal && !logNewStore.isStopped) {
            const suiteLogFailed = logNewStore.logData.find((f) => f?.node_id === '' && f?.state?.includes('suite.failed'))
            // 查找结束节点并显示通知
            const endNode = workflowStore.nodes.find((node) => node.type === 'end')
            if (endNode?.data?.config && !suiteLogFailed) {
              // NotificationService.showWorkflowEndNotification(endNode.data.config)
              NotificationService.showSuccess('执行完成', '工作流执行完成')
            } else {
              // 默认成功通知
              if(suiteLogFailed){
                NotificationService.showError('执行失败', '工作流执行失败')
              }
              else{
                NotificationService.showSuccess('执行完成', '工作流执行完成')
              }
            }
            unWatch()
          }
        })
    }
  } catch (error) {
    // 更新执行状态
    logNewStore.setLogExecuting(false)
    setTimeout(()=>{
      logNewStore.setLogStoped(true)
    }, 100)
    if(error?.name === 'AbortError'){
      return
    }
    ElMessage.error({ message: '执行异常', showClose: true })
  }
}

// 深度清理对象，移除所有不可序列化的属性
const deepCleanObject = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'function' || typeof obj === 'symbol') {
    return undefined
  }

  if (obj instanceof Date) {
    return obj.toISOString()
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepCleanObject(item)).filter((item) => item !== undefined)
  }

  if (typeof obj === 'object') {
    const cleaned: any = {}
    for (const [key, value] of Object.entries(obj)) {
      // 跳过Vue内部属性和不可序列化的属性
      if (
        key.startsWith('$') ||
        key.startsWith('_') ||
        key === '__v_isRef' ||
        key === '__v_isReactive'
      ) {
        continue
      }

      const cleanedValue = deepCleanObject(value)
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue
      }
    }
    return cleaned
  }

  return obj
}

// 通过HTTP API执行工作流
const executeWorkflowViaAPI = async (workflowData: any) => {
  try {

    // 使用深度清理函数
    const cleanWorkflowData = deepCleanObject(workflowData)

    const requestData = {
      workflow: cleanWorkflowData,
      options: {},
      taskId: route.query.id, // 添加任务id
    }

    let jsonString
    try {
      jsonString = JSON.stringify(requestData, null, 2)
    } catch (serializeError) {
      throw new Error(`数据序列化失败: ${serializeError}`)
    }
    const response = await fetch('http://localhost:39876/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization,
      },
      body: jsonString,
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('无法连接到Python引擎，请确保后端服务正在运行 (http://localhost:39876)')
    }
    throw error
  }
}


// 停止执行
const stopExecution = async () => {
  try {
    const response = await fetch(`http://localhost:39876/execution/${logNewStore.logHistoryInfo.historyId}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 更新执行状态
    logNewStore.setLogStoped(true)
    logNewStore.setLogExecuting(false)
    ElMessage.info({ message: '已停止执行', showClose: true })
  } catch (error) {
    console.error({error})
    ElMessage.info({ message: '停止执行失败', showClose: true })
  }
}


// 显示设置
const showSettings = () => {
  // TODO: 实现设置对话框
  ElMessage.info({ message: '设置功能开发中...', showClose: true })
}

// 更新工作流名称
const updateWorkflowName = () => {
  if (workflowName.value.trim()) {
    workflowStore.metadata.name = workflowName.value.trim()
    workflowStore.markAsChanged()
  } else {
    workflowName.value = workflowStore.metadata.name
  }
}

// ==================== 自动保存功能 ====================
// 添加自动保存相关状态
const lastAutoSaveTime = ref<string>('')
const autoSaveInterval = ref<NodeJS.Timeout | null>(null)

// 自动保存方法
const autoSaveWorkflow = async () => {
  if (!missionData.value.id || !workflowStore.hasUnsavedChanges) return

  try {
    const workflowData = transWorkFlowData(workflowStore.getWorkflowData())
    const params = { ...missionData.value, configContent: JSON.stringify(workflowData) }
    params.missionName = workflowData.metadata.name

    await saasApi.AIAgentMissionUpdateRobot(params)
    workflowStore.markAsSaved()
    lastAutoSaveTime.value = new Date().toLocaleTimeString()
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

// 启动自动保存定时器
const startAutoSave = () => {
  if (autoSaveInterval.value) clearInterval(autoSaveInterval.value)
  autoSaveInterval.value = setInterval(() => {
    if (workflowStore.hasUnsavedChanges) {
      autoSaveWorkflow()
    }
  }, 30000) // 30秒检查一次
}

// 停止自动保存
const stopAutoSave = () => {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value)
    autoSaveInterval.value = null
  }
}


// ==================== 智能录制功能 ====================

// 开始智能录制
const startIntelligentRecording = async () => {
  try {
    isRecording.value = true
    recordingPaused.value = false
    // logStore.addLog('info', '正在启动智能录制器...')

    // 智能录制配置
    const config = {
      browser: 'chromium',
      mode: 'intelligent',
      auto_screenshot: true,
      smart_recognition: true,
    }

    // logStore.addLog('info', `智能录制配置: ${JSON.stringify(config)}`)

    // 调用智能录制API
    const response = await fetch('http://localhost:39876/intelligent-recording/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization,
      },
      body: JSON.stringify(config),
    })

    if (!response.ok) {
      const errorText = await response.text()
      // logStore.addLog('error', `启动智能录制失败: ${response.status} ${response.statusText}`)
      // logStore.addLog('error', `错误详情: ${errorText}`)
      throw new Error(`启动智能录制失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    // logStore.addLog('info', `智能录制API响应: ${JSON.stringify(result)}`)

    if (result.success) {
      intelligentRecordingId.value = result.session_id || result.recording_id
      // logStore.addLog('success', result.message || '智能录制器已启动，请在浏览器中进行操作')
      ElMessage.success({
        message: '智能录制已开始！浏览器将自动打开，请在其中进行操作',
        type: 'success',
        duration: 3000,
        showClose: true,
      })

      // 启动状态监控
      startRecordingStatusMonitor()

      // 显示录制特性
      if (result.features) {
        // logStore.addLog('info', `录制特性: ${JSON.stringify(result.features)}`)
      }
    } else {
      isRecording.value = false
      // logStore.addLog('error', `启动智能录制失败: ${result.error || result.message}`)
      ElMessage.error({
        message: `启动智能录制失败: ${result.error || result.message}`,
        showClose: true,
      })
    }
  } catch (error) {
    isRecording.value = false
    // logStore.addLog('error', `启动智能录制异常: ${error}`)
    ElMessage.error({
      message: `启动智能录制失败: ${error?.message}`,
      showClose: true,
    })
  }
}

// 停止智能录制
const stopIntelligentRecording = async (notice: boolean = true) => {
  try {
    // logStore.addLog('info', '正在停止智能录制...')

    // 停止状态监控
    stopRecordingStatusMonitor()

    // 调用智能录制停止API
    const response = await fetch('http://localhost:39876/intelligent-recording/stop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization,
      },
      body: JSON.stringify({
        session_id: intelligentRecordingId.value,
        recording_id: intelligentRecordingId.value,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      // logStore.addLog('error', `停止智能录制失败: ${response.status} ${response.statusText}`)
      // logStore.addLog('error', `错误详情: ${errorText}`)
      if (notice) {
        throw new Error(`停止智能录制失败: ${response.status} ${response.statusText}`)
      }
    }

    const result = await response.json()
    // logStore.addLog('info', `停止智能录制API响应: ${JSON.stringify(result)}`)

    if (result.success && result.workflow_nodes && result.workflow_nodes.length > 0) {
      // 将录制的操作转换为工作流节点
      await convertRecordingToWorkflow(result.workflow_nodes)

      // 显示录制统计
      if (result.stats) {
        // logStore.addLog('success', `智能录制完成！`)

        // 安全地处理duration字段，避免undefined错误
        const duration = result.stats.duration || 0
        const totalOperations = result.stats.total_operations || 0
        const nodesGenerated = result.stats.nodes_generated || 0

        // logStore.addLog('info', `录制统计: 操作数量 ${totalOperations}, 耗时 ${duration.toFixed(1)}秒, 生成节点 ${nodesGenerated}个`,)

        ElMessage.success({
          message: `智能录制完成！生成了 ${result.workflow_nodes.length} 个操作节点`,
          type: 'success',
          duration: 5000,
          showClose: true,
        })
      } else {
        // logStore.addLog('success', `智能录制已停止，生成了 ${result.workflow_nodes.length} 个操作节点`,)
        ElMessage.success({
          message: `智能录制完成，工作流已自动生成 ${result.workflow_nodes.length} 个操作`,
          showClose: true,
        })
      }
    } else if (result.success) {
      // logStore.addLog('warning', '智能录制已停止，但未生成有效的工作流')
      ElMessage.warning({ message: '录制已停止，但未检测到有效操作', showClose: true })
    } else {
      // logStore.addLog('error', `停止智能录制失败: ${result.error || result.message}`)
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      notice &&
        ElMessage.error({
          message: `停止智能录制失败: ${result.error || result.message}`,
          showClose: true,
        })
    }
  } catch (error) {
    // logStore.addLog('error', `停止智能录制异常: ${error}`)
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    notice &&
      ElMessage.error({
        message: `停止智能录制失败: ${error.message}`,
        showClose: true,
      })
  } finally {
    isRecording.value = false
    recordingPaused.value = false
    intelligentRecordingId.value = null
    recordingStats.value = {
      operation_count: 0,
      duration: 0,
      nodes_generated: 0,
    }
    realTimeOperations.value = []
  }
}

// 暂停/恢复录制
const toggleRecording = async () => {
  try {
    if (recordingPaused.value) {
      // 恢复录制
      const response = await fetch('http://localhost:39876/intelligent-recording/resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          recordingPaused.value = false
          // logStore.addLog('info', '智能录制已恢复')
          ElMessage.info({ message: '录制已恢复', showClose: true })
        }
      }
    } else {
      // 暂停录制
      const response = await fetch('http://localhost:39876/intelligent-recording/pause', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          recordingPaused.value = true
          // logStore.addLog('info', '智能录制已暂停')
          ElMessage.info({ message: '录制已暂停', showClose: true })
        }
      }
    }
  } catch (error) {
    // logStore.addLog('error', `切换录制状态失败: ${error}`)
    ElMessage.error({ message: '操作失败', showClose: true })
  }
}

// 启动录制状态监控
const startRecordingStatusMonitor = () => {
  if (recordingStatusTimer.value) {
    clearInterval(recordingStatusTimer.value)
  }

  recordingStatusTimer.value = setInterval(async () => {
    try {
      // 获取录制状态
      const statusResponse = await fetch('http://localhost:39876/intelligent-recording/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      })
      if (statusResponse.ok) {
        const statusResult = await statusResponse.json()
        if (statusResult.success && statusResult.stats) {
          recordingStats.value = statusResult.stats
        }
      }

      // 获取实时操作
      const opsResponse = await fetch('http://localhost:39876/intelligent-recording/operations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      })
      if (opsResponse.ok) {
        const opsResult = await opsResponse.json()
        if (opsResult.success) {
          realTimeOperations.value = opsResult.operations || []

          // 如果有新操作，显示在日志中
          if (opsResult.operations && opsResult.operations.length > 0) {
            const latestOp = opsResult.operations[opsResult.operations.length - 1]
            if (latestOp && latestOp.timestamp) {
              const opTime = new Date(latestOp.timestamp)
              const now = new Date()
              if (now.getTime() - opTime.getTime() < 3000) {
                // 3秒内的操作
                // logStore.addLog('info', `捕获操作: ${latestOp.type} - ${JSON.stringify(latestOp.element_info)}`,)
              }
            }
          }
        }
      }
    } catch (error) {
      // 静默处理监控错误
    }
  }, 2000) // 每2秒更新一次
}

// 停止录制状态监控
const stopRecordingStatusMonitor = () => {
  if (recordingStatusTimer.value) {
    clearInterval(recordingStatusTimer.value)
    recordingStatusTimer.value = null
  }
}

// 根据组件类型获取图标和颜色
const getNodeStyle = (componentType: string, category: string) => {
  const data = componentCategories.find((component: ComponentCategory) => component.name === category)
  if(data) {
    const component = data.components.find((component: ComponentDefinition) => component.type === componentType)
    if(component) {
      return {
        icon: component.icon,
        color: data.color,
      }
    }else {
      return {
        icon: 'action-iconfont icon-zhihangrenwu',
        color: data.color,
      }
    }
  }
  else {
    return {
      icon: 'action-iconfont icon-zhihangrenwu',
      color: '#875AF7',
    }
  }
}

// 将录制结果转换为工作流节点
const convertRecordingToWorkflow = async (recordedNodes: any[]) => {
  try {
    // 获取现有的开始和结束节点
    const startNode = workflowStore.nodes.find((node) => node.type === 'start')
    const endNode = workflowStore.nodes.find((node) => node.type === 'end')

    // 清除开始和结束节点之间的所有连接和中间节点
    const nodesToRemove = workflowStore.nodes.filter(
      (node) => node.type !== 'start' && node.type !== 'end',
    )

    // 移除中间节点
    nodesToRemove.forEach((node) => {
      workflowStore.removeNode(node.id)
    })

    // 移除开始和结束节点之间的直接连接（如果存在）
    const directEdge = workflowStore.edges.find(
      (edge) => edge.source === startNode?.id && edge.target === endNode?.id,
    )
    if (directEdge) {
      workflowStore.removeEdge(directEdge.id)
    }

    // 确保开始和结束节点存在
    if (!startNode || !endNode) {
      // logStore.addLog('error', '缺少开始或结束节点，无法插入录制的操作')
      return
    }

    // 布局参数调整
    const NODE_WIDTH = 250
    const NODE_HEIGHT = 100
    const HORIZONTAL_SPACING = 300
    const VERTICAL_SPACING = 200
    const MAX_NODES_PER_ROW = 4

    // 计算开始节点位置
    const startX = startNode.position?.x || 100
    const startY = startNode.position?.y || 200

    // 添加录制的节点
    let lastNodeId = startNode.id
    let lastNodeX = startX
    let lastNodeY = startY

    for (const [index, recordedNode] of recordedNodes.entries()) {
      const nodeId = `recorded_${index + 1}`
      const row = Math.floor(index / MAX_NODES_PER_ROW)
      const col = index % MAX_NODES_PER_ROW

      // 计算节点位置
      const nodeX = startX + col * (HORIZONTAL_SPACING + NODE_WIDTH)
      const nodeY = startY + (row + 1) * VERTICAL_SPACING

      // 记录最后一个节点的位置
      lastNodeX = nodeX
      lastNodeY = nodeY

      // 获取节点样式
      const nodeStyle = getNodeStyle(recordedNode.type, recordedNode.category)

      // 配置节点
      recordedNode.config = {
        ...recordedNode.config,
        tabIdx: recordedNode['tab_idx'],
        tabChanged: recordedNode['tab_changed'],
        error_handle: 'stop',
        retry_delay: 3,
        retry_times: 0
      }

      const newNode = {
        id: nodeId,
        type: 'workflow',
        position: { x: nodeX, y: nodeY },
        data: {
          componentType: recordedNode.type,
          label: recordedNode.label,
          category: recordedNode.category,
          icon: nodeStyle.icon,
          color: nodeStyle.color,
          config: recordedNode.config,
          inputs: recordedNode.inputs || [],
          outputs: recordedNode.outputs || [],
        },
      }

      workflowStore.addNode(newNode)

      // 连接节点
      if (lastNodeId) {
        const edgeId = `edge_${lastNodeId}_${nodeId}`
        const edge = {
          id: edgeId,
          source: lastNodeId,
          target: nodeId,
          type: 'workflow',
          animated: true,
          style: { stroke: '#409eff', strokeWidth: 2 },
          data: { type: 'control' },
        }
        workflowStore.addEdge(edge)
      }

      // 如果是行尾节点，连接到下一行的第一个节点
      if (col === MAX_NODES_PER_ROW - 1 && index < recordedNodes.length - 1) {
        lastNodeId = nodeId
      }
      else if (index === recordedNodes.length - 1) {
        // 最后一个节点连接到结束节点
        const finalEdgeId = `edge_${nodeId}_${endNode.id}`
        const finalEdge = {
          id: finalEdgeId,
          source: nodeId,
          target: endNode.id,
          type: 'workflow',
          animated: true,
          style: { stroke: '#409eff', strokeWidth: 2 },
          data: { type: 'control' },
        }
        workflowStore.addEdge(finalEdge)
      } else {
        lastNodeId = nodeId
      }
    }

    // 计算结束节点位置 - 放在最后一个节点的右侧
    const endX = lastNodeX + HORIZONTAL_SPACING + NODE_WIDTH
    const endY = lastNodeY

    // 更新结束节点位置
    workflowStore.updateNodePosition(endNode.id, { x: endX, y: endY })

    // 连接最后一个节点到结束节点
    if (recordedNodes.length > 0) {
      const lastNodeId = `recorded_${recordedNodes.length}`
      const finalEdgeId = `edge_${lastNodeId}_${endNode.id}`
      const finalEdge = {
        id: finalEdgeId,
        source: lastNodeId,
        target: endNode.id,
        type: 'workflow',
        animated: true,
        style: { stroke: '#409eff', strokeWidth: 2 },
        data: { type: 'control' },
      }
      workflowStore.addEdge(finalEdge)
    }

    // logStore.addLog('success', `成功转换录制结果，生成了 ${recordedNodes.length} 个节点并自动连接`)
    workflowStore.markAsChanged()
  } catch (error) {
    // logStore.addLog('error', `转换录制结果失败: ${error}`)
    throw error
  }
}

// 快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // 检查是否在输入元素中，如果是则不处理快捷键
  const target = event.target as HTMLElement
  const isInputElement =
    target.tagName === 'INPUT' ||
    target.tagName === 'TEXTAREA' ||
    target.contentEditable === 'true' ||
    target.closest('.el-input') ||
    target.closest('.el-textarea') ||
    target.closest('.el-select') ||
    target.closest('.property-panel')

  if (isInputElement) {
    return // 在输入元素中时不处理快捷键
  }

  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveWorkflow()
  }
  // Ctrl+N 新建
  else if (event.ctrlKey && event.key === 'n') {
    event.preventDefault()
    newWorkflow()
  }
  // Ctrl+O 打开
  else if (event.ctrlKey && event.key === 'o') {
    event.preventDefault()
    openWorkflow()
  }
  // Ctrl+F5 运行
  else if (event.ctrlKey && event.key === 'F5') {
    event.preventDefault()
    executeWorkflow()
  }
  // Ctrl+E 导出
  else if (event.ctrlKey && event.key === 'e') {
    event.preventDefault()
    exportWorkflow()
  }
}

// 页面返回
const onRouteBack = () => {
  // 检查是否有未保存的更改
  if (workflowStore.hasUnsavedChanges) {
    ElMessageBox.confirm('当前工作流有未保存的更改，确定要离开吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        workflowStore.clearMissionData()
        router.back()
      })
      .catch(() => {
        // 用户取消操作
      })
  } else {
    workflowStore.clearMissionData()
    router.back()
  }
}


// 获取左侧指令、服务包权限
const GetGetAuthTargetResource = () => {
  targetSourceStore.initQueryStatus(false)
  const params = {
    url: '/wimai/api/task/authTarget/getTargetResource',
    body_param: {},
    method: 'get',
  }
  saasApi
    .AIDtemplateCrud(params)
    .then((res: any) => {
      if(res?.resources){
        targetSourceStore.initData(res.resources)
      }
    })
    .finally(() => {
      targetSourceStore.initQueryStatus(true)
    })
}

const reportUrl = ref('')

// 组件挂载时添加快捷键监听
onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)
  // 获取指令、服务包权限
  GetGetAuthTargetResource()
  // 检查URL中的id参数，如果存在id，则通过id获取任务详情数据并加载编排流程配置
  if (route.query.id) {
    // 清空画布
    await nextTick()
    workflowStore.loadWorkflow({})
    const loading = ElLoading.service({
      lock: true,
      fullscreen: true,
    })
    const workflowId = route.query.id.toString()
    // logStore.addLog('info', `正在加载工作流ID: ${workflowId}`)
    saasApi
      .AIAgentMissionDetail({ id: workflowId })
      .then((response: any) => {
        if (!response) {
          // logStore.addLog('error', `工作流加载失败: ${response.error || '未知错误'}`)
          workflowStore.createDefaultNodes() // 加载失败时创建默认节点
        } else {
          let data = null
          try {
            data = JSON.parse(response.configContent)
            // 处理变量赋值数据
            data.nodes.forEach(item => {
              if (item.data?.componentType === 'variable_assignment') {
                if (item.data.config.variables && typeof item.data.config.variables === 'string') {
                  item.data.config.variables = JSON.parse(item.data.config.variables)
                }
              }
            })
          } catch (e) {}
          if (data) {
            workflowStore.loadWorkflow(data)
          } else {
            workflowStore.createDefaultNodes()
          }
          reportUrl.value = response?.reportUrl || ''
          // 将任务名称更新到编排中
          response.missionName && workflowStore.updateWorkflowName(response.missionName)
          // 将任务数据保存到临时对象中
          missionData.value = response
          workflowStore.updateMissionData(response)
          // logStore.addLog('success', `工作流ID ${workflowId} 加载成功`)

          // 强制设置初始 viewport
          nextTick(() => {
            workflowStore.updateViewport({ x: 0, y: 0, zoom: 1 })
          })

          // 开始自动保存
          startAutoSave()
        }
      })
      .catch((error) => {
        // logStore.addLog('error', `工作流加载异常: ${error}`)
        workflowStore.createDefaultNodes() // 异常时创建默认节点
      })
      .finally(() => {
        loading.close()
      })
  }
  else {
    // 如果工作流为空，则创建默认的开始和结束节点
    if (workflowStore.nodes.length === 0) {
      workflowStore.createDefaultNodes()
    }
    // 强制设置初始 viewport
    nextTick(() => {
      workflowStore.updateViewport({ x: 0, y: 0, zoom: 1 })
    })
  }
})

// 组件卸载时移除快捷键监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 清理录制状态监控
  stopRecordingStatusMonitor()
  // 停止自动保存
  stopAutoSave()
})
</script>

<style scoped lang="scss">
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f4f8;
}

.toolbar {
  height: 54px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
}

.toolbar-brand {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-page-icon {
  cursor: pointer;
}

.workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;

  .workflow-name-input {
    display: none;
  }

  .title {
    color: var(--font-color-1);
    font-size: 14px;
    font-weight: bold;
    //max-width: 160px;
    min-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .el-icon{
      margin-left: 8px;
      color: #BCBFC3;
    }
  }

  &:hover,
  &:focus-within {
    .workflow-name-input {
      display: block;
      position: absolute;
      left: 0;
    }

    .title {
      visibility: hidden;
    }
  }
}

.workflow-name-input {
  //width: 160px;
  width: 100%;
}

.workflow-name-input :deep(.el-input__wrapper) {
  border: 1px solid transparent;
  border-radius: 6px;
  width: 100%;
  height: 28px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  box-shadow: none;
}

.workflow-name-input :deep(.el-input__wrapper:hover) {
  border: 1px solid #c0c4cc;
  background: rgba(255, 255, 255, 0.9);
}

.workflow-name-input :deep(.el-input__wrapper.is-focus) {
  border: 1px solid #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15);
  background: white;
}

.workflow-name-input :deep(.el-input__inner) {
  color: var(--font-color-1);
  font-size: 14px;
  font-weight: bold;
  font-family: auto;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #BCBFC3;
  transition: all 0.3s ease;
  cursor: default;
  flex-shrink: 0;
}

.save-status.has-changes {
  color: #f56c6c;
  animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.toolbar-actions {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  min-width: 300px;
  justify-content: flex-end;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: unset;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 1;
  transition: transform 0.2s ease, margin-left 0.2s ease; // 添加过渡效果

  &.collapsed{
    transform: translateX(-100%);
    &+.canvas-area{
      margin-left: -250px; // 补偿左侧面板的宽度
    }
  }

  .panel-content-tab{
    flex: 1;
    overflow: hidden;
  }
  .panel-component-tabs{
    background: none;
    padding: 0 16px;
    margin-top: 16px;
    box-sizing: border-box;
    &.collapsed{
      padding: 0 6px;
      margin-bottom: 8px;
      :deep(.el-segmented__group){
        border: none;
        .el-segmented__item-selected{
          display: none !important;
        }
      }
    }
    :deep(.el-segmented__group){
      border-bottom: 1px solid #eeeeee;
      .el-segmented__item{
        font-size: 14px;
        padding: 0;
        &.is-selected{
          color: var(--el-color-primary);
          font-weight: bold;
        }
        &:not(.is-disabled):not(.is-selected):hover{
          background: none;
        }
      }
      .el-segmented__item-selected{
        height: 2px !important;
        top: auto;
        bottom: -1px;
        width: 72px !important;
        left: 20px !important;
      }
    }

    .scope-label{
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: center;
      gap: 8px;
      .action-iconfont{
        font-size: 14px;
        font-weight: normal;
      }
      .num-total{
        font-weight: normal;
        font-size: 12px;
        color: #999999;
      }
    }
  }
}

.canvas-area {
  flex: 1;
  background: #f2f4f8;
  position: relative;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.2s ease; // 给右侧区域也添加过渡效果
}

.right-panel {
  width: 375px;
  flex-shrink: 0;
  background: white;
  overflow-y: auto;
  //border-left: 1px solid #e4e7ed;
  box-shadow: 0 0 4px 0 #00000014;
  margin: 10px 10px 10px 1px;
  border-radius: 4px;
}

.canvas-area-top-panel {
  flex: 1;
  position: relative;
}

.canvas-area-bottom-panel {
  height: 320px;
  background: white;
  border-top: 1px solid #e4e7ed;
  transition: height 0.3s ease;
}

.canvas-area-bottom-panel.collapsed {
  height: 40px;
}

.canvas-area-bottom-panel.fullscreen {
  height: calc(100vh - 240px) !important;
}

.panel-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  user-select: none;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #303133;
}

.group-icon {
  .el-icon ~ .el-icon {
    margin-left: 16px;
  }
}

.collapse-icon {
  transition: transform 0.3s ease;
}

.collapse-icon.rotated {
  transform: rotate(180deg);
}

.panel-content {
  height: calc(100% - 40px);
  overflow: hidden;
}

.template-designer-title{
  padding: 8px 16px 4px;
  color: #999;
  font-size: 12px;
}

.action-menu-icon{
  font-size: 12px;
  margin-right: 8px;
}

/* 工作流通知样式 */
:deep(.workflow-notification) {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.workflow-notification .el-notification__title) {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
}

:deep(.workflow-notification .el-notification__content) {
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-line;
}

:deep(.workflow-notification.el-notification--success) {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.95) 0%, rgba(103, 194, 58, 0.85) 100%);
  color: white;
}

:deep(.workflow-notification.el-notification--error) {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.95) 0%, rgba(245, 108, 108, 0.85) 100%);
  color: white;
}

:deep(.workflow-notification.el-notification--warning) {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.95) 0%, rgba(230, 162, 60, 0.85) 100%);
  color: white;
}

:deep(.workflow-notification.el-notification--info) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.95) 0%, rgba(64, 158, 255, 0.85) 100%);
  color: white;
}

:deep(.workflow-notification .el-notification__closeBtn) {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

:deep(.workflow-notification .el-notification__closeBtn:hover) {
  color: white;
}
</style>
