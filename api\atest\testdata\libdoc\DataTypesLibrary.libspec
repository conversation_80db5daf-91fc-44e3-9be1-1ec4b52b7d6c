<?xml version="1.0" encoding="UTF-8"?>
<keywordspec name="DataTypesLibrary" type="LIBRARY" format="HTML" scope="TEST" generated="2023-12-08T12:59:08+00:00" specversion="6" source="/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py" lineno="88">
<version/>
<doc>&lt;p&gt;This Library has Data Types.&lt;/p&gt;
&lt;p&gt;It has some in &lt;code&gt;__init__&lt;/code&gt; and others in the &lt;a href="#Keywords" class="name"&gt;Keywords&lt;/a&gt;.&lt;/p&gt;
&lt;p&gt;The DataTypes are the following that should be linked. &lt;span class="name"&gt;HttpCredentials&lt;/span&gt; , &lt;a href="#type-GeoLocation" class="name"&gt;GeoLocation&lt;/a&gt; , &lt;a href="#type-Small" class="name"&gt;Small&lt;/a&gt; and &lt;a href="#type-AssertionOperator" class="name"&gt;AssertionOperator&lt;/a&gt;.&lt;/p&gt;</doc>
<tags>
</tags>
<inits>
<init name="__init__" lineno="97">
<arguments repr="credentials: Small = one">
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="credentials: Small = one">
<name>credentials</name>
<type name="Small" typedoc="Small"/>
<default>one</default>
</arg>
</arguments>
<doc>&lt;p&gt;This is the init Docs.&lt;/p&gt;
&lt;p&gt;It links to &lt;a href="#Set%20Location" class="name"&gt;Set Location&lt;/a&gt; keyword and to &lt;a href="#type-GeoLocation" class="name"&gt;GeoLocation&lt;/a&gt; data type.&lt;/p&gt;</doc>
<shortdoc>This is the init Docs.</shortdoc>
</init>
</inits>
<keywords>
<kw name="Assert Something" lineno="107">
<arguments repr="value, operator: AssertionOperator | None = None, exp: str = something?">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="value">
<name>value</name>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="operator: AssertionOperator | None = None">
<name>operator</name>
<type name="Union" union="true">
<type name="AssertionOperator" typedoc="AssertionOperator"/>
<type name="None" typedoc="None"/>
</type>
<default>None</default>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="exp: str = something?">
<name>exp</name>
<type name="str" typedoc="string"/>
<default>something?</default>
</arg>
</arguments>
<doc>&lt;p&gt;This links to &lt;a href="#type-AssertionOperator" class="name"&gt;AssertionOperator&lt;/a&gt; .&lt;/p&gt;
&lt;p&gt;This is the next Line that links to &lt;a href="#Set%20Location" class="name"&gt;Set Location&lt;/a&gt; .&lt;/p&gt;</doc>
<shortdoc>This links to `AssertionOperator` .</shortdoc>
</kw>
<kw name="Custom" lineno="134">
<arguments repr="arg: CustomType, arg2: CustomType2, arg3: CustomType, arg4: Unknown">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg: CustomType">
<name>arg</name>
<type name="CustomType" typedoc="CustomType"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg2: CustomType2">
<name>arg2</name>
<type name="CustomType2" typedoc="CustomType2"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg3: CustomType">
<name>arg3</name>
<type name="CustomType" typedoc="CustomType"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg4: Unknown">
<name>arg4</name>
<type name="Unknown"/>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
<kw name="Funny Unions" lineno="114">
<arguments repr="funny: bool | int | float | str | AssertionOperator | Small | GeoLocation | None = equal">
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="funny: bool | int | float | str | AssertionOperator | Small | GeoLocation | None = equal">
<name>funny</name>
<type name="Union" union="true">
<type name="bool" typedoc="boolean"/>
<type name="int" typedoc="integer"/>
<type name="float" typedoc="float"/>
<type name="str" typedoc="string"/>
<type name="AssertionOperator" typedoc="AssertionOperator"/>
<type name="Small" typedoc="Small"/>
<type name="GeoLocation" typedoc="GeoLocation"/>
<type name="None" typedoc="None"/>
</type>
<default>equal</default>
</arg>
</arguments>
<returntype name="Union" union="true">
<type name="int" typedoc="integer"/>
<type name="List" typedoc="list">
<type name="int" typedoc="integer"/>
</type>
</returntype>
<doc/>
<shortdoc/>
</kw>
<kw name="Set Location" lineno="104">
<arguments repr="location: GeoLocation">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="location: GeoLocation">
<name>location</name>
<type name="GeoLocation" typedoc="GeoLocation"/>
</arg>
</arguments>
<returntype name="bool" typedoc="boolean"/>
<doc/>
<shortdoc/>
</kw>
<kw name="Typing Types" lineno="128">
<arguments repr="list_of_str: List[str], dict_str_int: Dict[str, int], whatever: Any, *args: List[Any]">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="list_of_str: List[str]">
<name>list_of_str</name>
<type name="List" typedoc="list">
<type name="str" typedoc="string"/>
</type>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="dict_str_int: Dict[str, int]">
<name>dict_str_int</name>
<type name="Dict" typedoc="dictionary">
<type name="str" typedoc="string"/>
<type name="int" typedoc="integer"/>
</type>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="whatever: Any">
<name>whatever</name>
<type name="Any" typedoc="Any"/>
</arg>
<arg kind="VAR_POSITIONAL" required="false" repr="*args: List[Any]">
<name>args</name>
<type name="List" typedoc="list">
<type name="Any" typedoc="Any"/>
</type>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
<kw name="X Literal" lineno="131">
<arguments repr="arg: Literal[1, 'xxx', b'yyy', True, None, one]">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg: Literal[1, 'xxx', b'yyy', True, None, one]">
<name>arg</name>
<type name="Literal" typedoc="Literal">
<type name="1"/>
<type name="'xxx'"/>
<type name="b'yyy'"/>
<type name="True"/>
<type name="None"/>
<type name="one"/>
</type>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
</keywords>
<typedocs>
<type name="Any" type="Standard">
<doc>&lt;p&gt;Any value is accepted. No conversion is done.&lt;/p&gt;</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>Typing Types</usage>
</usages>
</type>
<type name="AssertionOperator" type="Enum">
<doc>&lt;p&gt;This is some Doc&lt;/p&gt;
&lt;p&gt;This has was defined by assigning to __doc__.&lt;/p&gt;</doc>
<accepts>
<type>string</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
</usages>
<members>
<member name="equal" value="=="/>
<member name="==" value="=="/>
<member name="&lt;" value="&lt;"/>
<member name="&gt;" value="&gt;"/>
<member name="&lt;=" value="&lt;="/>
<member name="&gt;=" value="&gt;="/>
</members>
</type>
<type name="boolean" type="Standard">
<doc>&lt;p&gt;Strings &lt;code&gt;TRUE&lt;/code&gt;, &lt;code&gt;YES&lt;/code&gt;, &lt;code&gt;ON&lt;/code&gt; and &lt;code&gt;1&lt;/code&gt; are converted to Boolean &lt;code&gt;True&lt;/code&gt;, the empty string as well as strings &lt;code&gt;FALSE&lt;/code&gt;, &lt;code&gt;NO&lt;/code&gt;, &lt;code&gt;OFF&lt;/code&gt; and &lt;code&gt;0&lt;/code&gt; are converted to Boolean &lt;code&gt;False&lt;/code&gt;, and the string &lt;code&gt;NONE&lt;/code&gt; is converted to the Python &lt;code&gt;None&lt;/code&gt; object. Other strings and other accepted values are passed as-is, allowing keywords to handle them specially if needed. All string comparisons are case-insensitive.&lt;/p&gt;
&lt;p&gt;Examples: &lt;code&gt;TRUE&lt;/code&gt; (converted to &lt;code&gt;True&lt;/code&gt;), &lt;code&gt;off&lt;/code&gt; (converted to &lt;code&gt;False&lt;/code&gt;), &lt;code&gt;example&lt;/code&gt; (used as-is)&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>integer</type>
<type>float</type>
<type>None</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Set Location</usage>
</usages>
</type>
<type name="CustomType" type="Custom">
<doc>&lt;p&gt;Converter method doc is used when defined.&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>integer</type>
</accepts>
<usages>
<usage>Custom</usage>
</usages>
</type>
<type name="CustomType2" type="Custom">
<doc>&lt;p&gt;Class doc is used when converter method has no doc.&lt;/p&gt;</doc>
<accepts>
</accepts>
<usages>
<usage>Custom</usage>
</usages>
</type>
<type name="dictionary" type="Standard">
<doc>&lt;p&gt;Strings must be Python &lt;a href="https://docs.python.org/library/stdtypes.html#dict"&gt;dictionary&lt;/a&gt; literals. They are converted to actual dictionaries using the &lt;a href="https://docs.python.org/library/ast.html#ast.literal_eval"&gt;ast.literal_eval&lt;/a&gt; function. They can contain any values &lt;code&gt;ast.literal_eval&lt;/code&gt; supports, including dictionaries and other containers.&lt;/p&gt;
&lt;p&gt;If the type has nested types like &lt;code&gt;dict[str, int]&lt;/code&gt;, items are converted to those types automatically. This in new in Robot Framework 6.0.&lt;/p&gt;
&lt;p&gt;Examples: &lt;code&gt;{'a': 1, 'b': 2}&lt;/code&gt;, &lt;code&gt;{'key': 1, 'nested': {'key': 2}}&lt;/code&gt;&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>Mapping</type>
</accepts>
<usages>
<usage>Typing Types</usage>
</usages>
</type>
<type name="float" type="Standard">
<doc>&lt;p&gt;Conversion is done using Python's &lt;a href="https://docs.python.org/library/functions.html#float"&gt;float&lt;/a&gt; built-in function.&lt;/p&gt;
&lt;p&gt;Starting from RF 4.1, spaces and underscores can be used as visual separators for digit grouping purposes.&lt;/p&gt;
&lt;p&gt;Examples: &lt;code&gt;3.14&lt;/code&gt;, &lt;code&gt;2.9979e8&lt;/code&gt;, &lt;code&gt;10 000.000 01&lt;/code&gt;&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>Real</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
</usages>
</type>
<type name="GeoLocation" type="TypedDict">
<doc>&lt;p&gt;Defines the geolocation.&lt;/p&gt;
&lt;ul&gt;
&lt;li&gt;&lt;code&gt;latitude&lt;/code&gt; Latitude between -90 and 90.&lt;/li&gt;
&lt;li&gt;&lt;code&gt;longitude&lt;/code&gt; Longitude between -180 and 180.&lt;/li&gt;
&lt;li&gt;&lt;code&gt;accuracy&lt;/code&gt; &lt;b&gt;Optional&lt;/b&gt; Non-negative accuracy value. Defaults to 0.&lt;/li&gt;
&lt;/ul&gt;
&lt;p&gt;Example usage: &lt;code&gt;{'latitude': 59.95, 'longitude': 30.31667}&lt;/code&gt;&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>Mapping</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Set Location</usage>
</usages>
<items>
<item key="longitude" type="float" required="true"/>
<item key="latitude" type="float" required="true"/>
<item key="accuracy" type="float" required="false"/>
</items>
</type>
<type name="integer" type="Standard">
<doc>&lt;p&gt;Conversion is done using Python's &lt;a href="https://docs.python.org/library/functions.html#int"&gt;int&lt;/a&gt; built-in function. Floating point numbers are accepted only if they can be represented as integers exactly. For example, &lt;code&gt;1.0&lt;/code&gt; is accepted and &lt;code&gt;1.1&lt;/code&gt; is not.&lt;/p&gt;
&lt;p&gt;Starting from RF 4.1, it is possible to use hexadecimal, octal and binary numbers by prefixing values with &lt;code&gt;0x&lt;/code&gt;, &lt;code&gt;0o&lt;/code&gt; and &lt;code&gt;0b&lt;/code&gt;, respectively.&lt;/p&gt;
&lt;p&gt;Starting from RF 4.1, spaces and underscores can be used as visual separators for digit grouping purposes.&lt;/p&gt;
&lt;p&gt;Examples: &lt;code&gt;42&lt;/code&gt;, &lt;code&gt;-1&lt;/code&gt;, &lt;code&gt;0b1010&lt;/code&gt;, &lt;code&gt;10 000 000&lt;/code&gt;, &lt;code&gt;0xBAD_C0FFEE&lt;/code&gt;&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>float</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
<type name="list" type="Standard">
<doc>&lt;p&gt;Strings must be Python &lt;a href="https://docs.python.org/library/stdtypes.html#list"&gt;list&lt;/a&gt; literals. They are converted to actual lists using the &lt;a href="https://docs.python.org/library/ast.html#ast.literal_eval"&gt;ast.literal_eval&lt;/a&gt; function. They can contain any values &lt;code&gt;ast.literal_eval&lt;/code&gt; supports, including lists and other containers.&lt;/p&gt;
&lt;p&gt;If the type has nested types like &lt;code&gt;list[int]&lt;/code&gt;, items are converted to those types automatically. This in new in Robot Framework 6.0.&lt;/p&gt;
&lt;p&gt;Examples: &lt;code&gt;['one', 'two']&lt;/code&gt;, &lt;code&gt;[('one', 1), ('two', 2)]&lt;/code&gt;&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>Sequence</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
<type name="Literal" type="Standard">
<doc>&lt;p&gt;Only specified values are accepted. Values can be strings, integers, bytes, Booleans, enums and None, and used arguments are converted using the value type specific conversion logic.&lt;/p&gt;
&lt;p&gt;Strings are case, space, underscore and hyphen insensitive, but exact matches have precedence over normalized matches.&lt;/p&gt;</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>X Literal</usage>
</usages>
</type>
<type name="None" type="Standard">
<doc>&lt;p&gt;String &lt;code&gt;NONE&lt;/code&gt; (case-insensitive) is converted to Python &lt;code&gt;None&lt;/code&gt; object. Other values cause an error.&lt;/p&gt;</doc>
<accepts>
<type>string</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
</usages>
</type>
<type name="Small" type="Enum">
<doc>&lt;p&gt;This is the Documentation.&lt;/p&gt;
&lt;p&gt;This was defined within the class definition.&lt;/p&gt;</doc>
<accepts>
<type>string</type>
<type>integer</type>
</accepts>
<usages>
<usage>__init__</usage>
<usage>Funny Unions</usage>
</usages>
<members>
<member name="one" value="1"/>
<member name="two" value="2"/>
<member name="three" value="3"/>
<member name="four" value="4"/>
</members>
</type>
<type name="string" type="Standard">
<doc>&lt;p&gt;All arguments are converted to Unicode strings.&lt;/p&gt;</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
</typedocs>
</keywordspec>
