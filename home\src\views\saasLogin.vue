<template>
  <div class="dly-box">
    <div 
      class="dly-login-box" 
      v-if="loginType && ['1-1', '1-3'].indexOf(loginType) !== -1 && (showAccPwdLogin || showSmsLogin)"
    >
      <div 
        class="qrCode-item" 
        v-if="showQrLogin"
      >
        <img 
          src="../assets/images/login/popQrCode.png" 
          alt="" 
          @click="onSwitchLoginType('1-2')"
        />
      </div>
      <div class="dly-login-header" v-if="loginStip === 'first'">
        <div 
          :class="[loginType === '1-1' ? 'title-item active-item' : 'title-item']" 
          v-if="showAccPwdLogin"
        >
          <div class="head-title" @click="onSwitchLoginType('1-1')">账号登录</div>
          <div class="line-part"><span></span></div>
        </div>
        <div 
          :class="[loginType === '1-3' ? 'title-item active-item' : 'title-item']" 
          @click="onSwitchLoginType('1-3')" 
          v-if="showSmsLogin"
        >
          <div class="head-title">手机号登录</div>
          <div class="line-part"><span></span></div>
        </div>
      </div>
    </div>
    <!-- 账号密码登录 -->
    <pwdLogin 
      ref="pwdlogin" 
      v-if="loginType === '1-1'" 
      :configs="configs" 
      :cloud="cloud" 
      :loginPop="loginPop" 
      @onSwitchLoginType="onSwitchLoginType(-1)" 
    />

    <!-- 一诺扫码登录 -->
    <LoginQrCode  
      v-else-if="loginType === '1-2'" 
      :configs="configs" 
      :cloud="cloud" 
      :loginPop="loginPop" 
      @onSwitchLoginType="onSwitchLoginType" 
    />

    <!-- 手机号登录 -->
    <loginSms 
      v-else-if="loginType === '1-3'" 
      :configs="configs" 
      :pwsIsShow="true" 
      :loginPop="loginPop" 
      @onSwitchLoginType="onSwitchLoginType" 
    />

    <register 
      :configs="configs" 
      :loginPop="loginPop" 
      @loginChange="loginChange" 
      @onSwitchLoginType="onSwitchLoginType" 
      v-else-if="loginType === -1" 
    />

    <!-- 到期未改强制修改密码 -->
    <el-dialog 
      :title="'修改密码'" 
      v-model="visible" 
      z-index="1000" 
      :append-to-body="true" 
      custom-class="hd-form-dialog lcs-password" 
      :close-on-click-modal="false" 
      width="350px"
    >
      <el-form 
        :model="crow" 
        :rules="rules" 
        ref="crowForm" 
        label-width="80px"
      >
        <el-form-item label="原始密码" prop="opwd" style="margin-bottom: 28px !important">
          <el-input v-model="crow.opwd" type="password" />
        </el-form-item>
        <el-form-item label="新密码" prop="npwd" style="margin-bottom: 28px !important">
          <el-tooltip class="item" effect="dark" placement="right-end">
            <template #content>
              <div v-html="pwdRuleTips"></div>
            </template>
            <el-input v-model="crow.npwd" type="password" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="确认密码" prop="rpwd" style="margin-bottom: 28px !important">
          <el-tooltip class="item" effect="dark" placement="right-end">
            <template #content>
              <div v-html="pwdRuleTips"></div>
            </template>
            <el-input v-model="crow.rpwd" type="password" />
          </el-tooltip>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="mini" type="primary" @click="save('crowForm')">确定</el-button>
        <el-button size="mini" @click="visible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, inject } from 'vue';
import utils from '@/utils/utils';
import JSEncrypt from 'jsencrypt';
import pwdLogin from '@/components/loginType/pwd.vue';
import register from '@/components/loginType/register.vue';
import LoginQrCode from '@/components/loginType/LoginQrCode.vue';
import LoginSms from '@/components/loginType/LoginSms.vue';
import systemApi from "@/api/system";
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";

const userStore = useUserStore()
const loginStore = useLoginStore()

// 类型定义
interface Configs {
  logo: string;
  loginBackground: string;
  loginLeftBackground: string;
  isRedirect?: string;
  showVctCode?: number;
  loginAccountType?: string[];
  pwdRuleType?: string;
  pwdRule?: string;
  pwdMinLength?: number;
  weakPasswordSetting?: string;
  rememberPwd?: string;
}

interface OtherType {
  type: string;
  visible: boolean;
}

interface Props {
  configs?: Configs;
  cid?: string | null;
  cloud?: string;
  otherType?: OtherType;
  loginPop?: boolean;
}

interface ModelType {
  user: string | null;
  pwd: string | null;
  vali: string | null;
  tenantId: string | null;
  validShow: boolean;
  err_info: string | null;
  remember: boolean;
  cid: string | null;
  tenantName?: string;
}

interface CrowType {
  opwd: string;
  npwd: string;
  rpwd: string;
}

interface RulesType {
  opwd: {
    required: boolean;
    message: string;
    trigger: string;
  }[];
  npwd: {
    required: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: string, callback: (error?: Error) => void) => void;
  }[];
  rpwd: {
    required: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: string, callback: (error?: Error) => void) => void;
  }[];
}

interface TenantData {
  id: string;
  url: string;
  cloud: string;
  sysType: string;
}

// 外部依赖注入类型
interface DlyApi {
  getLoginCode: (timeVal: string) => string;
  getDetailByName: (name: string) => Promise<TenantData[]>;
  saasLogin: (params: any, options?: any) => Promise<any>;
  getEncryptKey: () => Promise<{ publicKey: string }>;
}

interface Store {
  dispatch: (action: string) => Promise<void>;
}

// 接收Props
const props = withDefaults(defineProps<Props>(), {
  configs: () => ({
    logo: require('@/assets/images/login/cloud-logo.png'),
    loginBackground: require('@/assets/images/login/uniwim-cloud.jpg'),
    loginLeftBackground: require('@/assets/images/login/uniwim-cloud-logo2.png')
  }),
  cid: null,
  cloud: '',
  otherType: () => ({
    type: '1-1',
    visible: false
  }),
  loginPop: false
});

// 注入外部依赖


// 响应式状态
const pwdlogin = ref<InstanceType<typeof pwdLogin> | null>(null);
const crowForm = ref<any>(null);
const identifyCode = ref<string | null>(null);
const visible = ref<boolean>(false);
const publicKey = ref<string>('');
const loginType = ref<string | number>('1-1');
const loginStip = ref<string>('first');

// 复杂对象状态
const model = reactive<ModelType>({
  user: null,
  pwd: null,
  vali: null,
  tenantId: null,
  validShow: false,
  err_info: null,
  remember: false,
  cid: null
});

const crow = reactive<CrowType>({
  opwd: '',
  npwd: '',
  rpwd: ''
});

// 表单验证规则
const rules = reactive<RulesType>({
  opwd: [
    {
      required: true,
      message: '原始密码不能为空',
      trigger: 'blur'
    }
  ],
  npwd: [
    {
      required: true,
      message: '新密码不能为空',
      trigger: 'blur'
    },
    { validator: checkPassWord, trigger: 'blur' }
  ],
  rpwd: [
    {
      required: true,
      message: '确认密码不能为空',
      trigger: 'blur'
    },
    { validator: validateEq, trigger: 'blur' }
  ]
});

// 验证函数
function checkPassWord(rule: any, value: string, callback: (error?: Error) => void) {
  if (crow.opwd === value) {
    callback(new Error('原始密码与新密码必须不同'));
    return;
  }
  
  if (props.configs.pwdRuleType === '4' && props.configs.pwdRule) {
    if (!new RegExp(props.configs.pwdRule).test(value)) {
      callback(new Error('密码验证规则不通过'));
    } else {
      callback();
    }
    return;
  }
  callback();
}

function validateEq(rule: any, value: string, callback: (error?: Error) => void) {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== crow.npwd) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
}

// 计算属性
const pwdRuleTips = computed<string>(() => {
  let str = `新密码需要符合以下规则：<br/>1.`;
  switch (props.configs.pwdRuleType) {
    case '1':
      str += '字母大写+字母小写+数字组合';
      break;
    case '2':
      str += '字母+数字+特殊字符组合';
      break;
    case '3':
      str += '字母+数字组合';
      break;
    case '4':
      str += props.configs.pwdRule 
        ? `满足正则策略：${props.configs.pwdRule}` 
        : '新密码不能为空';
      break;
    case '5':
      str += '字母大写+字母小写+特殊字符+数字';
      break;
    default:
      str += '新密码不能为空';
      break;
  }
  str += `<br/>2.密码长度不能小于${props.configs.pwdMinLength || 6}`;
  
  if (props.configs.weakPasswordSetting) {
    str += `<br/>3.不能设置的密码名单有：${props.configs.weakPasswordSetting}`;
  }
  return str;
});

const showAccPwdLogin = computed<boolean>(() => {
  return props.configs.loginAccountType 
    ? props.configs.loginAccountType.includes('1-1') 
    : true;
});

const showQrLogin = computed<boolean>(() => {
  return props.configs.loginAccountType 
    ? props.configs.loginAccountType.includes('1-2') 
    : false;
});

const showSmsLogin = computed<boolean>(() => {
  return props.configs.loginAccountType 
    ? props.configs.loginAccountType.includes('1-3') 
    : false;
});

// 监听配置变化
watch(
  () => props.configs,
  (v) => {
    if (v && v.isRedirect === '1' && utils.isMobileSys()) {
      location.replace(`${window.top.location.origin}/mobile/index.html`);
    } else {
      if (v && v.showVctCode === 1) {
        onRefreshCode();
      }
      loginType.value = v?.loginAccountType?.length 
        ? v.loginAccountType[0] 
        : '1-1';
    }
  },
  { deep: true, immediate: true }
);

// 生命周期
onMounted(() => {
  if (props.configs.rememberPwd !== '1') return;
  
  model.user = HD.Cookie.Get('uniwim_username') || getCookieValue('uniwim_username') || null;
  if (!model.user) return;
  
  model.pwd = HD.base64.decode(HD.Cookie.Get('uniwim_password') || getCookieValue('uniwim_password') || '');
  model.remember = true;
});

// 方法定义
function getCookieValue(cookieName: string): string | null {
  const cookies = document.cookie.split('; ');
  for (const cookie of cookies) {
    const [name, value] = cookie.split('=');
    if (name === cookieName) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

function onRefreshCode(): void {
  const timestamp = Date.now().toString();
  const randomNumber = Math.floor(1000 + Math.random() * 9000).toString();
  const timeVal = timestamp + randomNumber;
  identifyCode.value = systemApi.getLoginCode(timeVal);
}

function encodepassword(input: string): string {
  const _keyStr = 'NjCG7lX9WbVtnaA1TxzEY5OpuJ8Pr4oZF3s-SKdkchv2mqyLiD0efwRIBH_=6UgMQ';
  let output = '';
  let chr1: number, chr2: number, chr3: number, enc1: number, enc2: number, enc3: number, enc4: number;
  let i = 0;

  const _utf8_encode = (str: string): string => {
    str = str.replace(/\r\n/g, '\n');
    let utftext = '';
    for (let n = 0; n < str.length; n++) {
      const c = str.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if (c > 127 && c < 2048) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  };

  input = _utf8_encode(input);
  while (i < input.length) {
    chr1 = input.charCodeAt(i++);
    chr2 = i < input.length ? input.charCodeAt(i++) : NaN;
    chr3 = i < input.length ? input.charCodeAt(i++) : NaN;

    enc1 = chr1 >> 2;
    enc2 = ((chr1 & 3) << 4) | (chr2 ? (chr2 >> 4) : 0);
    enc3 = isNaN(chr2) ? 64 : ((chr2 & 15) << 2) | (chr3 ? (chr3 >> 6) : 0);
    enc4 = isNaN(chr3) ? 64 : chr3 & 63;

    output += _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
  }
  return output;
}

function s7Login(data: TenantData[]): void {
  const pwd = encodepassword(model.pwd || '');
  const params = { 
    username: model.user, 
    password: pwd, 
    vali: model.vali 
  };

  $.ajax({
    type: 'POST',
    contentType: 'application/json',
    dataType: 'json',
    data: JSON.stringify(params),
    url: `/uniwim/ump/uniLogin?url=${data[0].url}/login.json`,
    success: (rep: any) => {
      if (rep.Code === 0) {
        location.replace(`${data[0].url}/sys/app/bulletin?utoken=${rep.Response._id}`);
      } else {
        onRefreshCode();
        model.err_info = rep.Message;
      }
    },
    error: (ex: any) => {
      console.log('登录失败', ex);
      onRefreshCode();
      model.err_info = '登录失败，请联系管理员';
    }
  });
}

function onLogin(): void {
  model.err_info = null;
  // @ts-ignore - 需根据实际表单引用类型调整
  crowForm.value.validate((res: boolean) => {
    if (!res) {
      model.err_info = '请检查必填项';
      return;
    }

    if (model.tenantName) {
      systemAPi.getDetailByName(model.tenantName).then((res) => {
        const data = res || [];
        if (data.length) {
          model.tenantId = data[0].id;
          const isS7 = data[0].cloud === '3' && data[0].sysType === 's7';
          if (isS7) {
            s7Login(data);
          } else {
            saasLogin();
          }
        } else {
          saasLogin();
        }
      });
    } else {
      model.tenantId = null;
      saasLogin();
    }
  });
}

async function saasLogin(): Promise<void> {
  try {
    await getEncryptKey();
    const encryptStr = new JSEncrypt();
    encryptStr.setPublicKey(publicKey.value);
    const encryptPwd = encryptStr.encrypt(model.pwd || '');
    const pwd = HD.base64.encode(encryptPwd || '');

    const params = {
      username: model.user,
      password: pwd,
      vali: model.vali,
      tenantId: model.tenantId
    };

    model.err_info = '登录中...';
    const body = {
      data: HD.base64.encode(JSON.stringify(params))
    };

    const res = await systemApi.saasLogin(body, {
      meta: { isData: false }
    });

    if (!res) {
      model.err_info = '登录失败，请重试！';
      onRefreshCode();
      return;
    }

    if (res.Code === 0 && res.Response.token) {
      utils.setLocalStorageInfo(
        res.Response.token,
        res.Response.expire,
        res.Response.refreshToken,
        res.Response.tenantId
      );

      if (res.Response.changePwd) {
        model.err_info = null;
        visible.value = true;
      } else {
        if (model.remember) {
          HD.Cookie.Set('uniwim_username', params.username || '', 10);
          HD.Cookie.Set('uniwim_password', params.pwd, 10); // 注意：原代码中pwdForRemember未定义，此处保留原逻辑
        } else {
          HD.Cookie.Set('uniwim_username', '', -1);
          HD.Cookie.Set('uniwim_password', '', -1);
        }

        if (props.loginPop) {
          // await $store.dispatch('UPDTAINFO');
          let userInfo = await systemApi.initUserInfo();
          ;
          if(userInfo){
            userStore.setUserInfo(userInfo)
            loginStore.LOGIN_POP_VISIBLE(false)
          }
        } else {
          location.replace(`${import.meta.env.BASE_URL}index.html`);
        }
      }
    } else {
      model.err_info = res.Message;
      onRefreshCode();
    }
  } catch (err) {
    model.err_info = err as string;
    onRefreshCode();
  }
}

function getEncryptKey(): Promise<void> {
  return new Promise((resolve, reject) => {
    systemAPi.getEncryptKey().then((res) => {
      publicKey.value = res.publicKey || '';
      if (res.publicKey) {
        sessionStorage.setItem('publicKey', res.publicKey);
        resolve();
      } else {
        reject();
      }
    });
  });
}

function onSwitchLoginType(name: string | number): void {
  loginType.value = name;
}

function loginChange(type: any): void {
  model.user = null;
  model.pwd = null;
  model.vali = null;
  model.err_info = '';
}

function save(formName: string): void {
  // @ts-ignore
  crowForm.value.validate((valid: boolean) => {
    if (valid) {
      // 此处添加密码修改提交逻辑
      visible.value = false;
    }
  });
}

// 声明全局变量类型
declare global {
  interface Window {
    HD: {
      Cookie: {
        Get: (key: string) => string;
        Set: (key: string, value: string, days: number) => void;
      };
      base64: {
        encode: (str: string) => string;
        decode: (str: string) => string;
      };
    };
    $: {
      ajax: (options: any) => void;
    };
  }
}
</script>

<style scoped lang="less">
.dly-box {
  position: relative;

  .el-input__wrapper {
    box-shadow: none;
  }

  .dly-login-box {
    padding: 38px 28px 0 32px;
    background: transparent;

    .qrCode-item {
      position: absolute;
      right: 8px;
      top: 8px;

      img {
        width: 60px;
        height: 60px;
        cursor: pointer;
      }
    }

    .dly-login-header {
      width: 100%;
      height: 46px;
      display: flex;
      align-items: center;

      .title-item {
        margin-right: 32px;
        cursor: pointer;

        .head-title {
          font-weight: 400;
          font-size: 16px;
          color: #666666;
          height: 36px;
          line-height: 36px;
        }

        &.active-item {
          .head-title {
            font-weight: 400;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
          }

          .line-part {
            height: 2px;
            line-height: 2px;
            text-align: center;
            margin-top: 5px;

            span {
              width: 20px;
              height: 2px;
              background: #0054d2;
              border-radius: 1px;
              display: inline-block;
            }
          }
        }
      }
    }
  }
}
</style>