"""
节点数据API
用于获取工作流节点的执行结果和示例数据
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional, List
import json
import os
import asyncio
import aiohttp
import sqlite3
import pandas as pd
from pathlib import Path
from datetime import datetime

router = APIRouter(prefix="/api/nodes", tags=["nodes"])

# 节点执行结果缓存目录
EXECUTION_CACHE_DIR = Path("data/execution_cache")
EXECUTION_CACHE_DIR.mkdir(parents=True, exist_ok=True)

print(f"缓存目录: {EXECUTION_CACHE_DIR.absolute()}")
print(f"缓存目录存在: {EXECUTION_CACHE_DIR.exists()}")

@router.get("/{node_id}/sample-data")
async def get_node_sample_data(node_id: str) -> Dict[str, Any]:
    """
    获取节点的示例数据
    优先级：
    1. 最近的执行结果
    2. 缓存的示例数据
    3. 返回空结果
    """
    try:
        # 1. 尝试获取最近的执行结果
        execution_result = await get_latest_execution_result(node_id)
        if execution_result:
            return execution_result

        # 2. 尝试获取缓存的示例数据
        cached_data = await get_cached_sample_data(node_id)
        if cached_data:
            return cached_data

        # 3. 返回空结果
        return {
            "error": "no_data",
            "message": f"节点 {node_id} 没有可用的示例数据",
            "suggestion": "请先执行该节点或在节点配置中提供示例数据"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取节点示例数据失败: {str(e)}")


@router.post("/{node_id}/sample-data")
async def save_node_sample_data(node_id: str, sample_data: Dict[str, Any]) -> Dict[str, str]:
    """
    保存节点的示例数据
    """
    try:
        cache_file = EXECUTION_CACHE_DIR / f"{node_id}_sample.json"

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)

        return {
            "status": "success",
            "message": f"节点 {node_id} 的示例数据已保存"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存节点示例数据失败: {str(e)}")


@router.post("/{node_id}/fields")
async def get_node_fields(node_id: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取节点的字段结构，不执行节点
    支持多种策略：
    1. 从执行历史获取
    2. 从缓存的示例数据获取
    3. 直接发出真实请求获取数据
    4. 根据节点类型和配置推断
    5. 返回默认字段模板
    """
    try:
        nodes = request_data.get("nodes", [])
        edges = request_data.get("edges", [])
        force_real_request = request_data.get("force_real_request", False)  # 强制真实请求

        # 找到目标节点
        target_node = None
        for node in nodes:
            if node["id"] == node_id:
                target_node = node
                break

        if not target_node:
            raise HTTPException(status_code=404, detail=f"未找到节点: {node_id}")

        node_data = target_node.get("data", {})
        component_type = node_data.get("componentType")
        config = node_data.get("config", {})

        print(f"获取节点字段: {node_id}, 类型: {component_type}")
        print(f"节点配置: {config}")
        print(f"强制真实请求: {force_real_request}")

        # 如果强制真实请求，直接跳到策略3
        if force_real_request:
            print("强制执行真实请求...")
            real_data = await execute_real_request(component_type, config)
            print(f"真实请求数据: {real_data is not None}")
            if real_data:
                fields = analyze_data_structure(real_data)
                print(f"从真实请求解析到 {len(fields)} 个字段")
                if fields:
                    # 保存到缓存供下次使用
                    await save_cached_sample_data(node_id, real_data)
                    return {
                        "success": True,
                        "nodeId": node_id,
                        "fields": fields,
                        "source": "real_request",
                        "message": "通过真实请求获取字段结构",
                        "raw_data": real_data,  # 返回完整的原始数据
                    "request_info": {  # 返回请求信息
                        "url": config.get("url"),
                        "method": component_type,
                        "headers": config.get("headers"),
                        "data": config.get("data"),
                        "json_data": config.get("json_data"),
                        "actual_data_used": config.get("json_data") or config.get("data")
                    }
                    }

            # 真实请求失败，返回错误
            return {
                "success": False,
                "nodeId": node_id,
                "fields": [],
                "source": "real_request_failed",
                "message": "真实请求失败，请检查节点配置",
                "suggestion": "check_config"
            }

        # 策略1: 从执行历史获取
        print("尝试策略1: 从执行历史获取")
        historical_data = await get_latest_execution_result(node_id)
        print(f"执行历史数据: {historical_data is not None}")
        if historical_data:
            fields = analyze_data_structure(historical_data)
            print(f"从执行历史解析到 {len(fields)} 个字段")
            if fields:
                return {
                    "success": True,
                    "nodeId": node_id,
                    "fields": fields,
                    "source": "execution_history",
                    "message": "从执行历史获取字段结构",
                    "raw_data": historical_data
                }

        # 策略2: 从缓存的示例数据获取
        print("尝试策略2: 从缓存的示例数据获取")
        cached_data = await get_cached_sample_data(node_id)
        print(f"缓存数据: {cached_data is not None}")
        if cached_data:
            fields = analyze_data_structure(cached_data)
            print(f"从缓存数据解析到 {len(fields)} 个字段")
            if fields:
                return {
                    "success": True,
                    "nodeId": node_id,
                    "fields": fields,
                    "source": "cached_sample",
                    "message": "从缓存示例数据获取字段结构",
                    "raw_data": cached_data
                }

        # 策略3: 直接发出真实请求获取数据
        print("尝试策略3: 直接发出真实请求获取数据")
        real_data = await execute_real_request(component_type, config)
        print(f"真实请求数据: {real_data is not None}")
        if real_data:
            fields = analyze_data_structure(real_data)
            print(f"从真实请求解析到 {len(fields)} 个字段")
            if fields:
                # 保存到缓存供下次使用
                await save_cached_sample_data(node_id, real_data)
                return {
                    "success": True,
                    "nodeId": node_id,
                    "fields": fields,
                    "source": "real_request",
                    "message": "通过真实请求获取字段结构",
                    "raw_data": real_data
                }

        # 没有真实数据，返回空结果
        return {
            "success": False,
            "nodeId": node_id,
            "fields": [],
            "source": "no_data",
            "message": "无法获取字段数据，请检查节点配置",
            "suggestion": "check_config"
        }

    except Exception as e:
        print(f"获取节点字段失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "fields": []
        }


@router.get("/{node_id}/execution-history")
async def get_node_execution_history(node_id: str, limit: int = 10) -> Dict[str, Any]:
    """
    获取节点的执行历史
    """
    try:
        history_file = EXECUTION_CACHE_DIR / f"{node_id}_history.json"

        if not history_file.exists():
            return {
                "executions": [],
                "total": 0
            }

        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)

        # 按时间倒序排列，返回最近的记录
        executions = sorted(history.get("executions", []),
                          key=lambda x: x.get("timestamp", ""),
                          reverse=True)[:limit]

        return {
            "executions": executions,
            "total": len(history.get("executions", []))
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取节点执行历史失败: {str(e)}")


async def get_latest_execution_result(node_id: str) -> Optional[Dict[str, Any]]:
    """
    获取节点最近的执行结果
    """
    try:
        history_file = EXECUTION_CACHE_DIR / f"{node_id}_history.json"

        if not history_file.exists():
            return None

        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)

        executions = history.get("executions", [])
        if not executions:
            return None

        # 获取最近的成功执行结果
        for execution in sorted(executions, key=lambda x: x.get("timestamp", ""), reverse=True):
            if execution.get("status") == "success" and execution.get("output_data"):
                return execution["output_data"]

        return None

    except Exception as e:
        print(f"获取最近执行结果失败: {e}")
        return None


async def get_cached_sample_data(node_id: str) -> Optional[Dict[str, Any]]:
    """
    获取缓存的示例数据
    """
    try:
        cache_file = EXECUTION_CACHE_DIR / f"{node_id}_sample.json"

        if not cache_file.exists():
            return None

        with open(cache_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    except Exception as e:
        print(f"获取缓存示例数据失败: {e}")
        return None


async def save_execution_result(node_id: str, execution_data: Dict[str, Any]) -> None:
    """
    保存节点执行结果到历史记录
    """
    try:
        history_file = EXECUTION_CACHE_DIR / f"{node_id}_history.json"

        # 读取现有历史
        history = {"executions": []}
        if history_file.exists():
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)

        # 添加新的执行记录
        history["executions"].append(execution_data)

        # 保持最近100条记录
        if len(history["executions"]) > 100:
            history["executions"] = history["executions"][-100:]

        # 保存到文件
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"保存执行结果失败: {e}")


@router.delete("/{node_id}/cache")
async def clear_node_cache(node_id: str) -> Dict[str, str]:
    """
    清除节点的缓存数据
    """
    try:
        cache_file = EXECUTION_CACHE_DIR / f"{node_id}_sample.json"
        history_file = EXECUTION_CACHE_DIR / f"{node_id}_history.json"

        deleted_files = []

        if cache_file.exists():
            cache_file.unlink()
            deleted_files.append("sample_data")

        if history_file.exists():
            history_file.unlink()
            deleted_files.append("execution_history")

        return {
            "status": "success",
            "message": f"已清除节点 {node_id} 的缓存",
            "deleted": deleted_files
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除节点缓存失败: {str(e)}")


@router.get("/cache/stats")
async def get_cache_stats() -> Dict[str, Any]:
    """
    获取缓存统计信息
    """
    try:
        cache_files = list(EXECUTION_CACHE_DIR.glob("*_sample.json"))
        history_files = list(EXECUTION_CACHE_DIR.glob("*_history.json"))

        # 统计缓存大小
        total_size = sum(f.stat().st_size for f in cache_files + history_files)

        return {
            "sample_data_files": len(cache_files),
            "history_files": len(history_files),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "cache_directory": str(EXECUTION_CACHE_DIR)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


def analyze_data_structure(data: Any, path: str = '', label: str = '') -> List[Dict[str, Any]]:
    """
    分析数据结构，返回字段列表
    """
    if not data or not isinstance(data, (dict, list)):
        return []

    fields = []

    if isinstance(data, list):
        # 处理数组
        if len(data) > 0:
            item_fields = analyze_data_structure(data[0], f"{path}[]", f"{label}[项目]")
            fields.append({
                "path": f"{path}[]" if path else "[]",
                "type": "array",
                "label": f"{label} (数组)" if label else "数组",
                "isArray": True,
                "arrayItemType": get_field_type(data[0]),
                "children": item_fields,
                "sample": f"{len(data)}项"
            })
    else:
        # 处理对象
        for key, value in data.items():
            field_path = f"{path}.{key}" if path else key
            field_label = key
            field_type = get_field_type(value)

            field_info = {
                "path": field_path,
                "type": field_type,
                "label": field_label,
                "sample": get_sample_value(value)
            }

            if field_type == "object" and isinstance(value, dict):
                field_info["children"] = analyze_data_structure(value, field_path, field_label)
            elif field_type == "array" and isinstance(value, list):
                field_info["isArray"] = True
                field_info["arrayLength"] = len(value)  # 添加数组长度信息
                if len(value) > 0:
                    field_info["arrayItemType"] = get_field_type(value[0])
                    field_info["children"] = analyze_data_structure(value[0], f"{field_path}[]", f"{field_label}[项目]")
                    field_info["sample"] = f"{len(value)}项 (显示第1项结构)"
                    field_info["label"] = f"{field_label} (数组，共{len(value)}项)"
                else:
                    field_info["arrayItemType"] = "unknown"
                    field_info["children"] = []
                    field_info["sample"] = "空数组"
                    field_info["label"] = f"{field_label} (空数组)"

            fields.append(field_info)

    return fields


def get_field_type(value: Any) -> str:
    """
    获取字段类型
    """
    if value is None:
        return "null"
    elif isinstance(value, bool):
        return "boolean"
    elif isinstance(value, int):
        return "number"
    elif isinstance(value, float):
        return "number"
    elif isinstance(value, str):
        return "string"
    elif isinstance(value, list):
        return "array"
    elif isinstance(value, dict):
        return "object"
    else:
        return "unknown"


def get_sample_value(value: Any) -> Any:
    """
    获取示例值
    """
    if isinstance(value, str) and len(value) > 50:
        return value[:50] + "..."
    elif isinstance(value, list):
        return f"{len(value)}项"
    elif isinstance(value, dict):
        return f"{len(value)}个字段"
    else:
        return value


def infer_fields_from_node_config(component_type: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    根据节点类型和配置推断字段结构
    """
    if component_type in ["http_post", "http_get"]:
        return infer_http_fields(config)
    elif component_type == "db_query":
        return infer_db_query_fields(config)
    elif component_type == "excel_read":
        return infer_excel_fields(config)
    elif component_type == "csv_read":
        return infer_csv_fields(config)
    elif component_type == "python_execute":
        return infer_python_fields(config)
    elif component_type == "javascript_execute":
        return infer_javascript_fields(config)
    else:
        return []


def infer_http_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断HTTP请求节点的字段结构
    """
    # 基于常见的API响应结构
    return [
        {
            "path": "id",
            "type": "string",
            "label": "id",
            "sample": "response-id-123"
        },
        {
            "path": "object",
            "type": "string",
            "label": "object",
            "sample": "chat.completion"
        },
        {
            "path": "created",
            "type": "number",
            "label": "created",
            "sample": 1234567890
        },
        {
            "path": "model",
            "type": "string",
            "label": "model",
            "sample": "deepseek-chat"
        },
        {
            "path": "choices",
            "type": "array",
            "label": "choices",
            "isArray": True,
            "arrayItemType": "object",
            "sample": "1项",
            "children": [
                {
                    "path": "choices[].index",
                    "type": "number",
                    "label": "index",
                    "sample": 0
                },
                {
                    "path": "choices[].message",
                    "type": "object",
                    "label": "message",
                    "sample": "2个字段",
                    "children": [
                        {
                            "path": "choices[].message.role",
                            "type": "string",
                            "label": "role",
                            "sample": "assistant"
                        },
                        {
                            "path": "choices[].message.content",
                            "type": "string",
                            "label": "content",
                            "sample": "响应内容..."
                        }
                    ]
                },
                {
                    "path": "choices[].finish_reason",
                    "type": "string",
                    "label": "finish_reason",
                    "sample": "stop"
                }
            ]
        },
        {
            "path": "usage",
            "type": "object",
            "label": "usage",
            "sample": "3个字段",
            "children": [
                {
                    "path": "usage.prompt_tokens",
                    "type": "number",
                    "label": "prompt_tokens",
                    "sample": 10
                },
                {
                    "path": "usage.completion_tokens",
                    "type": "number",
                    "label": "completion_tokens",
                    "sample": 20
                },
                {
                    "path": "usage.total_tokens",
                    "type": "number",
                    "label": "total_tokens",
                    "sample": 30
                }
            ]
        }
    ]


def infer_db_query_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断数据库查询节点的字段结构
    """
    # 基于常见的数据库查询结果结构
    return [
        {
            "path": "[]",
            "type": "array",
            "label": "查询结果 (数组)",
            "isArray": True,
            "arrayItemType": "object",
            "sample": "多项",
            "children": [
                {
                    "path": "[].id",
                    "type": "number",
                    "label": "id",
                    "sample": 1
                },
                {
                    "path": "[].name",
                    "type": "string",
                    "label": "name",
                    "sample": "记录名称"
                },
                {
                    "path": "[].value",
                    "type": "string",
                    "label": "value",
                    "sample": "记录值"
                },
                {
                    "path": "[].create_time",
                    "type": "string",
                    "label": "create_time",
                    "sample": "2024-01-01 10:00:00"
                }
            ]
        }
    ]


def infer_excel_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断Excel读取节点的字段结构
    """
    return [
        {
            "path": "[]",
            "type": "array",
            "label": "Excel数据 (数组)",
            "isArray": True,
            "arrayItemType": "object",
            "sample": "多项",
            "children": [
                {
                    "path": "[].column1",
                    "type": "string",
                    "label": "column1",
                    "sample": "列1数据"
                },
                {
                    "path": "[].column2",
                    "type": "string",
                    "label": "column2",
                    "sample": "列2数据"
                },
                {
                    "path": "[].column3",
                    "type": "string",
                    "label": "column3",
                    "sample": "列3数据"
                }
            ]
        }
    ]


def infer_csv_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断CSV读取节点的字段结构
    """
    return [
        {
            "path": "[]",
            "type": "array",
            "label": "CSV数据 (数组)",
            "isArray": True,
            "arrayItemType": "object",
            "sample": "多项",
            "children": [
                {
                    "path": "[].column1",
                    "type": "string",
                    "label": "column1",
                    "sample": "值1"
                },
                {
                    "path": "[].column2",
                    "type": "string",
                    "label": "column2",
                    "sample": "值2"
                },
                {
                    "path": "[].column3",
                    "type": "string",
                    "label": "column3",
                    "sample": "值3"
                }
            ]
        }
    ]


def infer_python_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断Python执行节点的字段结构
    """
    fields = []

    # 根据配置的输出变量推断字段
    if config.get("output_variable"):
        fields.append({
            "path": "output",
            "type": "string",
            "label": "输出结果",
            "sample": "Python执行输出"
        })

    if config.get("error_variable"):
        fields.append({
            "path": "error",
            "type": "string",
            "label": "错误信息",
            "sample": "错误详情"
        })

    if config.get("return_code_variable"):
        fields.append({
            "path": "return_code",
            "type": "number",
            "label": "返回码",
            "sample": 0
        })

    return fields if fields else [
        {
            "path": "result",
            "type": "string",
            "label": "执行结果",
            "sample": "Python代码执行结果"
        }
    ]


def infer_javascript_fields(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    推断JavaScript执行节点的字段结构
    """
    fields = []

    # 根据配置的输出变量推断字段
    if config.get("output_variable"):
        fields.append({
            "path": "output",
            "type": "string",
            "label": "输出结果",
            "sample": "JavaScript执行输出"
        })

    if config.get("error_variable"):
        fields.append({
            "path": "error",
            "type": "string",
            "label": "错误信息",
            "sample": "错误详情"
        })

    if config.get("return_code_variable"):
        fields.append({
            "path": "return_code",
            "type": "number",
            "label": "返回码",
            "sample": 0
        })

    return fields if fields else [
        {
            "path": "result",
            "type": "string",
            "label": "执行结果",
            "sample": "JavaScript代码执行结果"
        }
    ]


def get_default_fields_template(component_type: str) -> List[Dict[str, Any]]:
    """
    获取默认字段模板
    """
    templates = {
        "http_post": [
            {
                "path": "data",
                "type": "object",
                "label": "响应数据",
                "sample": "API响应内容"
            }
        ],
        "http_get": [
            {
                "path": "data",
                "type": "object",
                "label": "响应数据",
                "sample": "API响应内容"
            }
        ],
        "db_query": [
            {
                "path": "results",
                "type": "array",
                "label": "查询结果",
                "isArray": True,
                "arrayItemType": "object",
                "sample": "数据库查询结果"
            }
        ],
        "excel_read": [
            {
                "path": "data",
                "type": "array",
                "label": "Excel数据",
                "isArray": True,
                "arrayItemType": "object",
                "sample": "Excel表格数据"
            }
        ],
        "csv_read": [
            {
                "path": "data",
                "type": "array",
                "label": "CSV数据",
                "isArray": True,
                "arrayItemType": "object",
                "sample": "CSV文件数据"
            }
        ],
        "python_execute": [
            {
                "path": "output",
                "type": "string",
                "label": "执行输出",
                "sample": "Python代码执行结果"
            }
        ],
        "javascript_execute": [
            {
                "path": "output",
                "type": "string",
                "label": "执行输出",
                "sample": "JavaScript代码执行结果"
            }
        ]
    }

    return templates.get(component_type, [
        {
            "path": "data",
            "type": "object",
            "label": "数据",
            "sample": "节点输出数据"
        }
    ])


async def execute_real_request(component_type: str, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    根据节点类型和配置执行真实请求获取数据
    """
    try:
        if component_type in ["http_post", "http_get", "http_request"]:
            return await execute_http_request(component_type, config)
        elif component_type == "db_query":
            return await execute_db_query(config)
        elif component_type == "excel_read":
            return await read_excel_file(config)
        elif component_type == "csv_read":
            return await read_csv_file(config)
        else:
            print(f"不支持的节点类型: {component_type}")
            return None
    except Exception as e:
        print(f"执行真实请求失败: {e}")
        return None


async def execute_http_request(method: str, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    执行HTTP请求
    """
    try:
        url = config.get("url")
        url_method = config.get("url_method", "POST")
        if not url:
            print("HTTP请求缺少URL配置")
            return None

        headers = {}

        # 处理请求头
        if config.get("headers"):
            try:
                if isinstance(config["headers"], str):
                    headers = json.loads(config["headers"])
                elif isinstance(config["headers"], dict):
                    headers = config["headers"]
            except:
                pass

        # 设置默认Content-Type
        if "content-type" not in headers and "Content-Type" not in headers:
            headers["Content-Type"] = "application/json"

        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时

        async with aiohttp.ClientSession(timeout=timeout) as session:
            if method == "http_post" or url_method == "POST":
                # POST请求 - 优先使用json_data，然后是data
                data = config.get("json_data") or config.get("data", {})
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                    except:
                        pass

                print(f"POST请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 如果没有配置数据，尝试发送空的POST请求
                if not data:
                    print("警告: 没有配置POST数据，将发送空请求体")

                async with session.post(url, json=data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"HTTP POST请求成功: {url}")
                        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        return result
                    else:
                        error_text = await response.text()
                        print(f"HTTP POST请求失败: {response.status}")
                        print(f"请求URL: {url}")
                        print(f"请求头: {headers}")
                        print(f"请求数据: {data}")
                        print(f"错误响应: {error_text}")

                        # 如果是400错误，尝试使用更简单的请求
                        if response.status == 400:
                            print("尝试使用简化的请求参数...")
                            simple_data = {"message": "test"}
                            simple_headers = {"Content-Type": "application/json"}

                            async with session.post(url, json=simple_data, headers=simple_headers) as retry_response:
                                if retry_response.status == 200:
                                    result = await retry_response.json()
                                    print(f"HTTP POST重试请求成功: {url}")
                                    return result
                                else:
                                    retry_error = await retry_response.text()
                                    print(f"HTTP POST重试也失败: {retry_response.status} - {retry_error}")

                        return None
            else:
                # GET请求
                params = config.get("params", {})
                if isinstance(params, str):
                    try:
                        params = json.loads(params)
                    except:
                        params = {}

                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"HTTP GET请求成功: {url}")
                        return result
                    else:
                        error_text = await response.text()
                        print(f"HTTP GET请求失败: {response.status}")
                        print(f"请求URL: {url}")
                        print(f"请求头: {headers}")
                        print(f"请求参数: {params}")
                        print(f"错误响应: {error_text}")
                        return None

    except asyncio.TimeoutError:
        print(f"HTTP请求超时: {url}")
        return None
    except Exception as e:
        print(f"HTTP请求异常: {e}")
        return None


async def execute_db_query(config: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
    """
    执行数据库查询
    """
    try:
        db_type = config.get("db_type", "sqlite")
        query = config.get("query")

        if not query:
            print("数据库查询缺少SQL语句")
            return None

        if db_type == "sqlite":
            db_path = config.get("db_path")
            if not db_path:
                print("SQLite查询缺少数据库路径")
                return None

            # 检查文件是否存在
            if not os.path.exists(db_path):
                print(f"数据库文件不存在: {db_path}")
                return None

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row  # 返回字典格式
            cursor = conn.cursor()

            # 限制查询结果数量，避免返回过多数据
            limited_query = query
            if "LIMIT" not in query.upper():
                limited_query += " LIMIT 100"

            cursor.execute(limited_query)
            rows = cursor.fetchall()

            # 转换为字典列表
            result = [dict(row) for row in rows]

            conn.close()
            print(f"SQLite查询成功，返回 {len(result)} 条记录")
            return result

        else:
            print(f"暂不支持的数据库类型: {db_type}")
            return None

    except Exception as e:
        print(f"数据库查询异常: {e}")
        return None


async def read_excel_file(config: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
    """
    读取Excel文件
    """
    try:
        file_path = config.get("file_path")
        if not file_path:
            print("Excel读取缺少文件路径")
            return None

        if not os.path.exists(file_path):
            print(f"Excel文件不存在: {file_path}")
            return None

        sheet_name = config.get("sheet_name", 0)  # 默认第一个sheet
        header_row = config.get("header_row", 0)  # 默认第一行为表头

        # 读取Excel文件，限制行数
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row, nrows=100)

        # 转换为字典列表
        result = df.to_dict('records')

        print(f"Excel读取成功，返回 {len(result)} 条记录")
        return result

    except Exception as e:
        print(f"Excel读取异常: {e}")
        return None


async def read_csv_file(config: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
    """
    读取CSV文件
    """
    try:
        file_path = config.get("file_path")
        if not file_path:
            print("CSV读取缺少文件路径")
            return None

        if not os.path.exists(file_path):
            print(f"CSV文件不存在: {file_path}")
            return None

        encoding = config.get("encoding", "utf-8")
        delimiter = config.get("delimiter", ",")

        # 读取CSV文件，限制行数
        df = pd.read_csv(file_path, encoding=encoding, delimiter=delimiter, nrows=100)

        # 转换为字典列表
        result = df.to_dict('records')

        print(f"CSV读取成功，返回 {len(result)} 条记录")
        return result

    except Exception as e:
        print(f"CSV读取异常: {e}")
        return None


async def save_cached_sample_data(node_id: str, data: Any) -> None:
    """
    保存数据到缓存
    """
    try:
        cache_file = EXECUTION_CACHE_DIR / f"{node_id}_sample.json"

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"数据已缓存到: {cache_file}")

    except Exception as e:
        print(f"保存缓存数据失败: {e}")