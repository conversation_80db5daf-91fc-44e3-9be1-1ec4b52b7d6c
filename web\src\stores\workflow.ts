import { defineStore } from 'pinia'
import { ref, computed,nextTick } from 'vue'
import { useVueFlow } from '@vue-flow/core';
import type { Node, Edge, Connection} from '@vue-flow/core'
import { configSchemaManager } from "@/utils/configSchema.ts";

export interface WorkflowNode extends Node {
  type: string
  data: {
    label: string
    icon?: string
    config: Record<string, any>
    category: string
    description?: string
    componentType?: string
    inputs?: string[]
    outputs?: string[]
    isSystemNode?: boolean // 标识系统节点（开始/结束），不可删除
  }
}

export interface WorkflowEdge extends Edge {
  id: string
  source: string
  target: string
  sourceHandle?: string | null
  targetHandle?: string | null
  type?: string
  animated?: boolean
  data?: {
    type: 'control' | 'data'
  }
}

export interface WorkflowData {
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
  metadata: {
    name: string
    description: string
    version: string
    createdAt: string
    updatedAt: string
  }
  variables: variableType[]
}

export interface ComponentDefinition {
  type: string; // 组件类型标识
  label: string; // 显示名称
  description?: string; // 描述信息
  icon: string; // 图标名称
  category: string; // 分类
  disabled?: boolean; // 是否禁用
  config: Record<string, any>; // 组件配置
  inputs?: string[]; // 输入参数
  outputs?: string[]; // 输出参数
}

export interface variableType {
    name: string
    value: any
    type: string
    scope: string
    description?: string
    sourceNode?: string
    sourceNodeId?: string
    isSystem?: boolean // 是否内置变量
}


export const useWorkflowStore = defineStore('workflow', () => {
  // 状态
  const nodes = ref<WorkflowNode[]>([])
  const edges = ref<WorkflowEdge[]>([])
  const selectedNodeId = ref<string | null>(null)
  const selectedEdgeId = ref<string | null>(null)
  const viewport = ref({ x: 0, y: 0, zoom: 1 })
  const hasUnsavedChanges = ref(false)
  const metadata = ref({
    name: '未命名工作流',
    description: '',
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  })
  // 局部变量
  const variables = ref<variableType[]>([])

  // 计算属性
  const selectedNode = computed(() => {
    return selectedNodeId.value
      ? nodes.value.find((node) => node.id === selectedNodeId.value)
      : null
  })

  const selectedEdge = computed(() => {
    return selectedEdgeId.value
      ? edges.value.find((edge) => edge.id === selectedEdgeId.value)
      : null
  })

  const nodeCount = computed(() => nodes.value.length)
  const edgeCount = computed(() => edges.value.length)

  // 获取数据源节点
  const dataSourceNodes = computed(() => {
    return nodes.value.filter(node => {
      const componentType = node.data.componentType
      return componentType && [
        'http_post',
        'http_get',
        'db_query',
        'excel_read',
        'csv_read'
      ].includes(componentType)
    })
  })

  // 新增计算属性：获取未通过校验的节点
  const invalidNodes = computed(() => {
    const invalidNodesList: Array<{
      node: WorkflowNode;
      result: {
        isValid: boolean;
        errors?: Array<{
          message: string;
        }>;
      };
    }> = [];

    // 1. 先验证节点配置
    nodes.value.forEach(node => {
      if (node.data?.config) {
        const res = configSchemaManager.validateConfig(<string>node.data.componentType, node.data.config)
        if(!res.isValid){
          invalidNodesList.push({
            node,
            result: res
          })
        }
      }
    });

    // 2. 验证节点连接情况
    const connectionResult = validateAllNodesConnected({
      ignoreTypes: ['start', 'end'],
      requireBothDirections: true // 要求每个节点都有入边和出边
    })

    if(connectionResult && !connectionResult.isValid) {
      connectionResult.unconnectedNodes.forEach(node => {
        // 检查该节点是否已经在invalidNodesList中
        const existingIndex = invalidNodesList.findIndex(item => item.node.id === node.id)

        if(existingIndex >= 0) {
          // 如果已存在，添加连接错误到现有错误列表
          if(!invalidNodesList[existingIndex].result.errors) {
            invalidNodesList[existingIndex].result.errors = []
          }
          invalidNodesList[existingIndex].result.errors!.push({
            message: "节点未完成连接"
          })
        } else {
          // 如果不存在，添加新条目
          invalidNodesList.push({
            node,
            result: {
              isValid: false,
              errors: [{
                message: "节点未完成连接"
              }]
            }
          })
        }
      })
    }

    // 3. 验证没有edge边的情况下，需要校验开始结束节点
    if (!edges.value.length && nodes.value[0]) {
      invalidNodesList.push({
        node: nodes.value[0],
        result: {
          isValid: false,
          errors: [{
            message: "节点未完成连接"
          }]
        }
      })
    }

    return invalidNodesList;
  });

  // 方法定位到指定节点
  const focusOnNode = (nodeId: string) => {
    const { fitView} = useVueFlow()
    const node = nodes.value.find(n => n.id === nodeId);
    if (node) {
      fitView({
        nodes: [nodeId], // 要定位的节点 ID
        padding: 0.2, // 节点周围的边距比例
        duration: 500, // 动画过渡时间（毫秒）
      });
    }
  };

  //方法 验证节点连线情况
  const validateAllNodesConnected = (options = {}) => {
    // 可配置选项
    const {
      ignoreTypes = [], // 要忽略的节点类型
      requireBothDirections = false // 是否要求节点同时有入边和出边
    } = options

    // 如果没有边，直接返回所有非忽略节点
    if (!edges.value.length) {
      return {
        isValid: false,
        unconnectedNodes: nodes.value.filter(node => !ignoreTypes.includes(node.type))
      }
    }

    // 创建边索引以提高查找效率
    const edgeSourceMap = new Map<string, boolean>()
    const edgeTargetMap = new Map<string, boolean>()

    edges.value.forEach(edge => {
      edgeSourceMap.set(edge.source, true)
      edgeTargetMap.set(edge.target, true)
    })

    const unconnectedNodes = nodes.value.filter(node => {
      // 跳过忽略的节点类型
      if (ignoreTypes.includes(node.type)) {
        return false
      }

      const hasIncomingEdge = edgeTargetMap.has(node.id)
      const hasOutgoingEdge = edgeSourceMap.has(node.id)

      // 根据配置决定节点是否未连接
      return requireBothDirections
        ? !(hasIncomingEdge && hasOutgoingEdge) // 需要同时有入边和出边
        : !(hasIncomingEdge || hasOutgoingEdge) // 只需有任意一种连接
    })

    return {
      isValid: unconnectedNodes.length === 0,
      unconnectedNodes
    }
  }

    // 更新节点位置
  const updateNodePosition = (nodeId: string, position: { x: number; y: number }) => {
    const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
    if (nodeIndex !== -1) {
      // 创建新节点对象以触发响应式更新
      const updatedNode = {
        ...nodes.value[nodeIndex],
        position: {
          ...nodes.value[nodeIndex].position,
          ...position
        }
      }
      nodes.value.splice(nodeIndex, 1, updatedNode)
      markAsChanged()
    } else {
      console.warn(`未找到ID为 ${nodeId} 的节点`)
    }
  }

  // 方法
  const addNode = (node: Omit<WorkflowNode, 'id'> | WorkflowNode) => {
    const newNode: WorkflowNode = {
      ...node,
      id: (node as WorkflowNode).id || generateNodeId(),
      position: node.position || { x: 100, y: 100 },
    }
    nodes.value.push(newNode)
    markAsChanged()
    return newNode
  }

  const removeNode = (nodeId: string) => {
    const node = nodes.value.find((n) => n.id === nodeId)

    // 检查是否为系统节点（开始/结束节点），如果是则不允许删除
    if (node?.data.isSystemNode) {
      console.warn('系统节点不能删除')
      return false
    }

    const index = nodes.value.findIndex((node) => node.id === nodeId)
    if (index > -1) {
      nodes.value.splice(index, 1)
      // 同时删除相关的边
      edges.value = edges.value.filter((edge) => edge.source !== nodeId && edge.target !== nodeId)
      if (selectedNodeId.value === nodeId) {
        selectedNodeId.value = null
      }
      markAsChanged()
      return true
    }
    return false
  }

  const updateNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    const node = nodes.value.find((n) => n.id === nodeId)
    if (node) {
      Object.assign(node, updates)
      markAsChanged()
    }
  }

  const updateNodeConfig = (nodeId: string, config: Record<string, any>) => {
    const nodeIndex = nodes.value.findIndex((n) => n.id === nodeId)
    if (nodeIndex !== -1) {
      const node = nodes.value[nodeIndex]
      // Create a completely new config object to ensure Vue reactivity
      const newConfig = { ...node.data.config, ...config }

      // Create a new node object to trigger Vue reactivity
      const updatedNode = {
        ...node,
        data: {
          ...node.data,
          config: newConfig,
        },
      }

      // Replace the node in the array to trigger reactivity
      nodes.value.splice(nodeIndex, 1, updatedNode)
      markAsChanged()
    }
  }

  const updateNodeData = (nodeId: string, data: Record<string, any>) => {
    const nodeIndex = nodes.value.findIndex((n) => n.id === nodeId)
    if (nodeIndex !== -1) {
      const node = nodes.value[nodeIndex]

      // Create a new node object to trigger Vue reactivity
      const updatedNode = {
        ...node,
        data: { ...node.data, ...data },
      }

      // Replace the node in the array to trigger reactivity
      nodes.value.splice(nodeIndex, 1, updatedNode)
      markAsChanged()
    }
  }

  const addEdge = (edge: any) => {
    const newEdge: WorkflowEdge = {
      ...edge,
      id: edge.id || generateEdgeId(),
      type: edge.type || 'smoothstep',
    }
    edges.value.push(newEdge)
    markAsChanged()
    return newEdge
  }

  const removeEdge = (edgeId: string) => {
    const index = edges.value.findIndex((edge) => edge.id === edgeId)
    if (index > -1) {
      edges.value.splice(index, 1)
      if (selectedEdgeId.value === edgeId) {
        selectedEdgeId.value = null
      }
      markAsChanged()
    }
  }

  const onConnect = (connection: Connection) => {
    const edge: Omit<WorkflowEdge, 'id'> = {
      source: connection.source,
      target: connection.target,
      sourceHandle: connection.sourceHandle,
      targetHandle: connection.targetHandle,
      type: 'workflow',
      animated: true,
      data: {
        type: 'control',
      },
    }
    addEdge(edge)
  }

  const selectNode = (nodeId: string | null) => {
    if(nodeId!='end_node'){
    // if(nodeId!='start_node'&&nodeId!='end_node'){
      selectedNodeId.value = nodeId
    }else{
      selectedNodeId.value = null
    }
    selectedEdgeId.value = null

  }

  const selectEdge = (edgeId: string | null) => {
    selectedEdgeId.value = null
    selectedNodeId.value = null
    nextTick(()=>{
      selectedEdgeId.value = edgeId
    })
  }

  const clearSelection = () => {
    selectedNodeId.value = null
    selectedEdgeId.value = null
  }

  const updateViewport = (newViewport: { x: number; y: number; zoom: number }) => {
    viewport.value = newViewport
  }

  const newWorkflow = () => {
    nodes.value = []
    edges.value = []
    selectedNodeId.value = null
    selectedEdgeId.value = null
    viewport.value = { x: 0, y: 0, zoom: 1 }
    hasUnsavedChanges.value = false

    metadata.value = {
      name: '未命名工作流',
      description: '',
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    variables.value = []

    // 自动添加开始和结束节点
    createDefaultNodes()
  }

  const createDefaultNodes = () => {
    // 创建开始节点
    const startNode: WorkflowNode = {
      id: 'start_node',
      type: 'start',
      position: { x: 100, y: 200 },
      data: {
        label: '开始',
        config: {
          workflow_name: '',
          description: '',
          execution_mode: 'immediate',
          schedule_type: 'once',
          schedule_time: '',
          daily_time: '09:00',
          error_strategy: 'stop',
          max_retries: 3,
          retry_delay: 5,
          log_level: 'INFO',
          confirm_start: false,
          timeout: 300,
        },
        category: 'control',
        description: '工作流的开始节点，标识流程入口点',
        componentType: 'workflow_start',
        inputs: [],
        outputs: ['workflow_context'],
        isSystemNode: true,
      },
    }

    // 创建结束节点
    const endNode: WorkflowNode = {
      id: 'end_node',
      type: 'end',
      position: { x: 900, y: 200 },
      data: {
        label: '结束',
        config: {
          status: 'success',
          message: '',
          return_data: '',
          notify_completion: true,
          notification_title: '工作流执行完成',
          notification_message: '',
          notification_type: 'success',
          notification_duration: 5,
          cleanup: true,
          log_summary: true,
        },
        category: 'control',
        description: '工作流的结束节点，标识流程完成',
        componentType: 'workflow_end',
        inputs: ['workflow_context'],
        outputs: [],
        isSystemNode: true,
      },
    }

    // 添加节点到工作流
    nodes.value.push(startNode, endNode)

    // 添加默认变量
    addDefaultVariables()

    // 不自动连接开始和结束节点，让用户手动连接
  }

  // 新增函数：添加默认变量
  const addDefaultVariables = () => {
    // 定义所有内置变量
    const defaultVariables = [
      {
        name: 'innerCurrentTime',
        value: '%Y-%m-%d %H:%M:%S',
        type: 'currentTime',
        scope: 'local',
        description: '获取当前时间',
        isSystem: true,
      },
      {
        name: 'token',
        value: '',
        type: 'string',
        scope: 'local',
        description: '获取当前用户token信息',
        isSystem: true,
      }
      // 可以在这里添加更多内置变量
    ]

    // 确保variables数组存在
    variables.value = variables.value || []

    // 添加缺失的内置变量
    defaultVariables.forEach(defaultVar => {
      const exists = variables.value.some(v => v.name === defaultVar.name && defaultVar.isSystem)
      if (!exists) {
        // 添加到数组最前面
        variables.value.unshift(defaultVar)
        markAsChanged()
      }
    })
  }

  const loadWorkflow = (data: WorkflowData) => {
    nodes.value = data.nodes || []
    edges.value = data.edges || []
    viewport.value = data.viewport || { x: 0, y: 0, zoom: 1 }
    metadata.value = data.metadata || metadata.value
    variables.value = data.variables || []
    selectedNodeId.value = null
    selectedEdgeId.value = null
    hasUnsavedChanges.value = false

    // 确保有默认变量
    addDefaultVariables()
  }

  const getWorkflowData = (): WorkflowData => {
    // 深度清理函数
    const deepClean = (obj: any): any => {
      if (obj === null || obj === undefined) return obj
      if (typeof obj === 'function' || typeof obj === 'symbol') return undefined
      if (obj instanceof Date) return obj.toISOString()

      if (Array.isArray(obj)) {
        return obj.map((item) => deepClean(item)).filter((item) => item !== undefined)
      }

      if (typeof obj === 'object') {
        const cleaned: any = {}
        for (const [key, value] of Object.entries(obj)) {
          // 跳过Vue Flow内部属性
          if (
            [
              'computedPosition',
              'handleBounds',
              'selected',
              'dimensions',
              'isParent',
              'resizing',
              'dragging',
              'events',
              'sourceNode',
              'targetNode',
              '__v_isRef',
              '__v_isReactive',
            ].includes(key)
          ) {
            continue
          }

          // 跳过以$或_开头的属性
          if (typeof key === 'string' && (key.startsWith('$') || key.startsWith('_'))) {
            continue
          }

          const cleanedValue = deepClean(value)
          if (cleanedValue !== undefined) {
            cleaned[key] = cleanedValue
          }
        }
        return cleaned
      }

      return obj
    }
    // 清理节点和边数据
    const cleanNodes = deepClean(nodes.value)
    const cleanEdges = deepClean(edges.value)

    return {
      nodes: cleanNodes,
      edges: cleanEdges,
      viewport: {
        x: viewport.value.x,
        y: viewport.value.y,
        zoom: viewport.value.zoom,
      },
      metadata: {
        name: metadata.value.name,
        description: metadata.value.description,
        version: metadata.value.version,
        createdAt: metadata.value.createdAt,
        updatedAt: new Date().toISOString(),
      },
      variables: variables.value,
    }
  }

  const markAsChanged = () => {
    hasUnsavedChanges.value = true
    metadata.value.updatedAt = new Date().toISOString()
  }

  const markAsSaved = () => {
    hasUnsavedChanges.value = false
  }

  // 工具函数
  const generateNodeId = () => {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const generateEdgeId = () => {
    return `edge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 修改工作流名称
  const updateWorkflowName = (newName: string) => {
    if (newName.trim()) {
      metadata.value.name = newName.trim()
      // markAsChanged()
    }
  }

  // 获取全局变量
  const getLocalVariables = () => {
    return variables.value
  }
  // 设置全局变量
  const setLocalVariables = (newVariables: variableType[]) => {
    variables.value = newVariables
  }
  // 添加全局变量
  const addLocalVariable = (newVariable: variableType) => {
    if (variables.value) {
      variables.value.push(newVariable)
    } else {
      variables.value = [newVariable]
    }
  }
  // 编辑指定索引的全局变量
  const editLocalVariable = (index: number, variable: variableType) => {
    if (index > -1) {
      variables.value[index] = variable
    }
  }
  // 删除全局变量
  const removeLocalVariableByIndex = (index: number) => {
    if (index > -1) {
      variables.value.splice(index, 1)
    }
  }

  // 获取指定类型的节点
  const getNodesByType = (componentType: string) => {
    return nodes.value.filter(node => node.data.componentType === componentType)
  }

  // 获取节点的输出变量
  const getNodeOutputVariables = (nodeId: string) => {
    const node = nodes.value.find(n => n.id === nodeId)
    if (!node) return []

    const componentType = node.data.componentType
    const config = node.data.config || {}

    switch (componentType) {
      case 'http_post':
      case 'http_get':
        return [config.response_content_variable || 'response']

      case 'db_query':
        return [config.result_variable || 'query_result']

      case 'excel_read':
        return node.data.outputs || ['data']

      case 'csv_read':
        return node.data.outputs || ['csv_data']

      default:
        return node.data.outputs || []
    }
  }

  // 获取指定节点
  const getNodeById = (nodeId: string) => {
    return nodes.value?.find(node => node.id === nodeId)
  }


  // 存储任务详情数据
  const missionData = ref<any>({})
  // 更新任务详情数据
  const updateMissionData = (data: any) => {
    missionData.value = data
  }
  // 清空任务详情数据
  const clearMissionData = () => {
    missionData.value = {}
  }

  return {
    // 状态
    nodes,
    edges,
    selectedNodeId,
    selectedEdgeId,
    viewport,
    hasUnsavedChanges,
    metadata,

    // 局部变量
    variables,

    // 计算属性
    selectedNode,
    selectedEdge,
    nodeCount,
    edgeCount,
    dataSourceNodes,
    invalidNodes,

    missionData,
    // 更新任务详情数据
    updateMissionData,
    // 清空任务详情数据
    clearMissionData,

    // 方法
    focusOnNode,
    updateNodePosition,
    addNode,
    removeNode,
    updateNode,
    updateNodeConfig,
    updateNodeData,
    addEdge,
    removeEdge,
    onConnect,
    selectNode,
    selectEdge,
    clearSelection,
    updateViewport,
    newWorkflow,
    loadWorkflow,
    getWorkflowData,
    markAsChanged,
    markAsSaved,
    createDefaultNodes,
    updateWorkflowName,
    getNodeById,
    // 局部变量方法
    getLocalVariables,
    setLocalVariables,
    addLocalVariable,
    editLocalVariable,
    removeLocalVariableByIndex,
    // 数据源相关方法
    getNodesByType,
    getNodeOutputVariables,
  }
})
