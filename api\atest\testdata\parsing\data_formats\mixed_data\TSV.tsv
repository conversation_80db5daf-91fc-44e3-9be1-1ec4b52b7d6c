*Setting
Documentation	Test suite in TSV file		
Resource	../resources/txt_resource.txt			

*Variable
${msg}	*ERROR*

*Test Cases	
TSV Passing	No operation
TSV Failing	[Documentation]	FAIL	**ERROR**
	Failing	*${msg}*
TXT Resource	Keyword from TXT resource		
	Keyword from TXT resource 2		
	Should Be Equal	${txt_resource_var}	TXT Resource Variable
	Should Be Equal	${txt_resource_var2}	TXT Resource Variable From Recursive Resource


*Keyword
Failing	[Arguments]	${msg}
	Fail	${msg}
