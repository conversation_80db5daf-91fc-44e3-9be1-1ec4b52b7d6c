Demonstrations
==============

There are several demo projects that introduce Robot Framework and help getting
started with it.

`Quick Start Guide <https://github.com/robotframework/QuickStartGuide/blob/master/QuickStart.rst>`__
    Introduces the most important features of Robot Framework and acts as
    an executable demo.

`Robot Framework demo <https://github.com/robotframework/RobotDemo>`__
    Simple example test cases. Demonstrates also creating custom test libraries.

`Web testing demo <https://github.com/robotframework/WebDemo>`__
    Demonstrates how to create tests and higher level keywords. The system
    under test is a simple web page that is tested using SeleniumLibrary_.

`ATDD with Robot Framework <https://code.google.com/p/atdd-with-robot-framework>`__
    Demonstrates how to use Robot Framework when following
    Acceptance Test Driven Development (ATDD) process.
