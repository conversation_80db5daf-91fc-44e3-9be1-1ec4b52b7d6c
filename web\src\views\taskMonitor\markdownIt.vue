<template>
  <div v-html="compiledMarkdown"></div>
</template>
 
<script>
import { defineComponent, computed } from 'vue';
import markdownIt from 'markdown-it';
 
export default defineComponent({
  name: 'Markdown',
  props: {
    content: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const md = markdownIt();
    const compiledMarkdown = computed(() => {
      return md.render(props.content);
    });
 
    return { compiledMarkdown };
  },
});
</script>