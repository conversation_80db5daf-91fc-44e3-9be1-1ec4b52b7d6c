<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :before-close="handleClose"
    append-to-body
    :show-close="false"
  >
    <template #header>
      <div class="el-dialog__custom_title">
        <div class="el-dialog__title">{{ title }}</div>
        <div class="el-dialog__tools">
          <el-button link type="primary" @click="showAddVariableDialog = true" :icon="Plus">
            添加变量
          </el-button>
          <!-- 关闭 -->
          <el-button link @click="dialogVisible = false">
            <el-icon size="20">
              <Close />
            </el-icon>
          </el-button>
        </div>
      </div>
    </template>
    <div class="variable-panel">
      <div class="panel-content">
        <!-- 变量搜索 -->
        <div class="variable-search">
          <el-input
            v-model="searchText"
            placeholder="搜索变量..."
            :prefix-icon="Search"
            clearable
          />
        </div>

        <!-- 变量分类标签 -->
        <!--      <div class="variable-tabs">-->
        <!--        <el-radio-group v-model="activeTab" size="small">-->
        <!--          <el-radio-button label="all">全部</el-radio-button>-->
        <!--          <el-radio-button label="local">局部</el-radio-button>-->
        <!--          <el-radio-button label="global">全局</el-radio-button>-->
        <!--          <el-radio-button label="workflow">工作流</el-radio-button>-->
        <!--        </el-radio-group>-->
        <!--      </div>-->

        <!-- 变量列表 -->
        <div class="variable-list">
          <div v-if="filteredVariables.length === 0" class="empty-state">
            <el-empty description="暂无变量" :image-size="80" />
          </div>

          <div
            v-for="variable in filteredVariables"
            :key="variable.name"
            class="variable-item"
            @click="selectVariable(variable)"
            @dblclick="editVariable(variable)"
            :class="{ active: selectedVariable?.name === variable.name }"
          >
            <div class="variable-header">
              <div class="variable-name">
                <el-icon class="variable-icon">
                  <component :is="getVariableIcon(variable.type)" />
                </el-icon>
                <span class="name">{{ variable.name }}</span>
                <el-tag :type="getScopeTagType(variable.scope)" size="small" class="scope-tag">
                  {{ getScopeLabel(variable.scope) }}
                </el-tag>
                <el-tag type="primary" size="small" class="scope-tag" v-if="variable.isSystem">
                  内置
                </el-tag>
              </div>
              <div class="variable-actions">
                <el-button link @click.stop="editVariable(variable)" :icon="Edit" title="编辑"
                  >编辑
                </el-button>
                <el-button
                  v-if="!variable.isSystem"
                  link
                  type="danger"
                  @click.stop="deleteVariable(variable)"
                  :icon="Delete"
                  title="删除"
                  >删除
                </el-button>
              </div>
            </div>

            <div class="variable-value" v-if="!variable.isSystem && variable.name !== 'token'">
              <span class="value-label">值:</span>
              <span class="value-content">{{ formatVariableValue(variable.value, variable.type) }}</span>
            </div>

            <div v-if="variable.description" class="variable-description">
              描述: {{ variable.description }}
            </div>

            <div class="variable-meta">
              <span class="type">类型: {{ getTypeLabel(variable.type) }}</span>
              <!--            <span v-if="variable.sourceNodeId" class="source">-->
              <!--              来源: {{ getNodeLabel(variable.sourceNodeId) }}-->
              <!--            </span>-->
            </div>
          </div>
        </div>
      </div>

      <!-- 变量详情面板 -->
      <div v-if="selectedVariable && false" class="variable-details">
        <div class="details-header">
          <h4>变量详情</h4>
          <el-button size="small" text @click="selectedVariable = null" :icon="Close" />
        </div>

        <div class="details-content">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="名称">{{ selectedVariable.name }}</el-descriptions-item>
            <el-descriptions-item label="类型"
              >{{ getTypeLabel(selectedVariable.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="作用域"
              >{{ getScopeLabel(selectedVariable.scope) }}
            </el-descriptions-item>
            <el-descriptions-item label="值" v-if="!selectedVariable.isSystem && selectedVariable.name !== 'token'">
              <pre class="variable-value-display">{{
                formatVariableValueDetailed(selectedVariable.value, selectedVariable.type)
              }}</pre>
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedVariable.description" label="描述">
              {{ selectedVariable.description }}
            </el-descriptions-item>
            <!--          <el-descriptions-item v-if="selectedVariable.sourceNodeId" label="来源节点">-->
            <!--            {{ getNodeLabel(selectedVariable.sourceNodeId) }}-->
            <!--          </el-descriptions-item>-->
          </el-descriptions>
        </div>
      </div>

      <!-- 添加/编辑变量对话框 -->
      <el-dialog
        v-model="showAddVariableDialog"
        :title="editingVariable ? '编辑变量' : '添加变量'"
        width="500px"
        @closed="onDialogClosed"
      >
        <el-form
          :model="variableForm"
          :rules="variableRules"
          ref="variableFormRef"
          label-width="80px"
        >
          <el-form-item label="作用域" prop="scope" v-if="false">
            <el-select v-model="variableForm.scope" placeholder="选择作用域">
              <el-option label="局部变量" value="local" />
              <!--            <el-option label="工作流变量" value="workflow" />-->
              <!--            <el-option label="全局变量" value="global" />-->
            </el-select>
          </el-form-item>

          <el-form-item label="变量名" prop="name">
            <el-input v-model="variableForm.name" :disabled="!!variableForm.isSystem" placeholder="输入变量名" />
          </el-form-item>

          <el-form-item label="类型" prop="type">
            <el-select v-model="variableForm.type" :disabled="!!variableForm.isSystem" placeholder="选择类型">
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔值" value="boolean" />
              <el-option label="JSON" value="json" />
              <el-option label="列表" value="list" />
              <el-option label="当前时间" value="currentTime" />
            </el-select>
          </el-form-item>

          <el-form-item label="值" prop="value" v-if="variableForm.type !== 'currentTime' && !(variableForm.isSystem && variableForm.name === 'token')">
            <el-input
              v-model="variableForm.value"
              type="textarea"
              :rows="4"
              placeholder="输入变量值"
            />
          </el-form-item>

          <el-form-item label="时间格式" prop="value" v-else-if="variableForm.type === 'currentTime'">
            <el-select v-model="variableForm.value" placeholder="选择时间格式">
              <el-option label="年-月-日 时:分:秒 (YYYY-MM-DD HH:mm:ss)" value="%Y-%m-%d %H:%M:%S" />
              <el-option label="年-月-日 (YYYY-MM-DD)" value="%Y-%m-%d" />
              <el-option label="月/日/年 (YYYY/MM/DD)" value="%m/%d/%Y" />
              <el-option label="时:分:秒 (HH:mm:ss)" value="%H:%M:%S" />
              <el-option label="年 (YYYY)" value="%Y" />
              <el-option label="月 (MM)" value="%m" />
              <el-option label="日 (DD)" value="%d" />
              <el-option label="时 (HH)" value="%H" />
              <el-option label="分 (mm)" value="%M" />
              <el-option label="秒 (ss)" value="%S" />
            </el-select>
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="variableForm.description"
              type="textarea"
              :rows="2"
              :maxlength="50"
              show-word-limit
              placeholder="变量描述（可选）"
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <el-button @click="showAddVariableDialog = false">取消</el-button>
          <el-button type="primary" @click="saveVariable">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Plus,
  Refresh,
  Search,
  Edit,
  Delete,
  Close,
  Setting,
  Document,
  Clock,
  CirclePlus,
  Check,
  Tickets,
  List,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWorkflowStore } from '@/stores/workflow'
import type { variableType } from '@/stores/workflow'
import {getAvailableVariables} from "@/utils/availableVariables.ts";

// props传参
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '变量管理',
  },
})

// 响应式数据
const workflowStore = useWorkflowStore()
const searchText = ref('')
const activeTab = ref('all')
const selectedVariable = ref<variableType | null>(null)
const showAddVariableDialog = ref(false)
const editingVariable = ref<variableType | null>(null)

// 表单数据
const variableForm = ref({
  name: '',
  type: 'string',
  scope: 'local',
  value: '',
  description: '',
  isSystem: false,
})
const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)

// 从工作流中收集可用变量
const availableVariables = computed(() => getAvailableVariables())

// 监听props变化
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
  },
)

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 监听表单值类型变化
watch(
  () => variableForm.value.type,
  (newType, oldType) => {
    if(showAddVariableDialog.value) return
    // 当从普通类型切换到当前时间，或从当前时间切换到普通类型时
    if ((oldType === 'currentTime' && newType !== 'currentTime') ||
        (oldType !== 'currentTime' && newType === 'currentTime')) {
      variableForm.value.value = ''
    }
  }
)

const handleClose = () => {
  dialogVisible.value = false
}

// 表单验证规则
const variableRules = {
  name: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '变量名格式不正确', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback()
          return
        }

        // 如果是编辑模式且变量名未改变，跳过检查
        if (editingVariable.value && editingVariable.value.name === value) {
          callback()
          return
        }

        // 检查变量名是否已存在
        const exists = availableVariables.value.some(v => v.name === value)
        if (exists) {
          callback(new Error('变量名已存在，请使用唯一名称'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  scope: [{ required: true, message: '请选择作用域', trigger: 'change' }],
  value: [{ required: true, message: '请输入变量值', trigger: 'blur' }],
}

const variableFormRef = ref()

// 计算属性
const filteredVariables = computed(() => {
  let filtered = workflowStore.variables

  // 按作用域过滤
  if (activeTab.value !== 'all') {
    filtered = filtered.filter((v) => v.scope === activeTab.value)
  }

  // 按搜索文本过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(
      (v) =>
        v.name.toLowerCase().includes(search) ||
        v.description?.toLowerCase().includes(search) ||
        String(v.value).toLowerCase().includes(search),
    )
  }

  return filtered
})

const selectVariable = (variable: variableType) => {
  selectedVariable.value = variable
}

const editVariable = (variable: variableType) => {
  editingVariable.value = variable
  showAddVariableDialog.value = true
  variableForm.value = {
    name: variable.name,
    type: variable.type,
    scope: variable.scope,
    value:
      typeof variable.value === 'object'
        ? JSON.stringify(variable.value, null, 2)
        : String(variable.value),
    description: variable.description || '',
    isSystem: variable.isSystem || false,
  }
}

const deleteVariable = async (variable: variableType) => {
  try {
    await ElMessageBox.confirm(`确定要删除变量 "${variable.name}" 吗？`, '确认删除', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const index = workflowStore.variables.findIndex((v) => v.name === variable.name)
    if (index > -1) {
      workflowStore.removeLocalVariableByIndex(index)
      if (selectedVariable.value?.name === variable.name) {
        selectedVariable.value = null
      }
      ElMessage.success('变量已删除')
    }
  } catch {
    // 用户取消删除
  }
}

const saveVariable = async () => {
  if (!variableFormRef.value) return

  try {
    await variableFormRef.value.validate()

    const newVariable: variableType = {
      name: variableForm.value.name,
      type: variableForm.value.type,
      scope: variableForm.value.scope,
      value: parseVariableValue(variableForm.value.value, variableForm.value.type),
      description: variableForm.value.description,
    }

    if (editingVariable.value) {
      // 编辑现有变量
      const index = workflowStore.variables.findIndex((v) => v.name === editingVariable.value!.name)
      if (index > -1) {
        workflowStore.editLocalVariable(index, {
          ...workflowStore.variables[index],
          ...newVariable,
        })
      }
      ElMessage.success('变量已更新')
    } else {
      // 添加新变量
      workflowStore.addLocalVariable(newVariable)
      ElMessage.success('变量已添加')
    }

    showAddVariableDialog.value = false
    editingVariable.value = null
    resetForm()
  } catch (error) {
    console.error('保存变量失败:', error)
  }
}

const resetForm = () => {
  variableForm.value = {
    name: '',
    type: 'string',
    scope: 'local',
    value: '',
    description: '',
    isSystem: false,
  }
}

const onDialogClosed = () => {
  resetForm()
  editingVariable.value = null
}

const parseVariableValue = (value: string, type: string) => {
  try {
    switch (type) {
      case 'number':
        return Number(value)
      case 'boolean':
        return value.toLowerCase() === 'true'
      case 'json':
      case 'list':
        return JSON.parse(value)
      default:
        return value
    }
  } catch {
    return value
  }
}

const formatVariableValue = (value: any, type?: string) => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  // 如果是当前时间类型且值是时间格式字符串
  if (type === 'currentTime' && typeof value === 'string') {
    // 将 % 格式转换为 YYYY-MM-DD HH:mm:ss 格式
    return value
      .replace(/%Y/g, 'YYYY')
      .replace(/%m/g, 'MM')
      .replace(/%d/g, 'DD')
      .replace(/%H/g, 'HH')
      .replace(/%M/g, 'mm')
      .replace(/%S/g, 'ss')
  }
  return String(value)
}

const formatVariableValueDetailed = (value: any, type?: string) => {
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2)
  }
  // 如果是当前时间类型且值是时间格式字符串
  if (type === 'currentTime' && typeof value === 'string') {
    // 将 % 格式转换为 YYYY-MM-DD HH:mm:ss 格式
    return value
      .replace(/%Y/g, 'YYYY')
      .replace(/%m/g, 'MM')
      .replace(/%d/g, 'DD')
      .replace(/%H/g, 'HH')
      .replace(/%M/g, 'mm')
      .replace(/%S/g, 'ss')
  }
  return String(value)
}

const getVariableIcon = (type: string) => {
  switch (type) {
    case 'string':
      return Document
    case 'number':
      return CirclePlus
    case 'boolean':
      return Check
    case 'json':
      return Tickets
    case 'list':
      return List
    case 'currentTime':
      return Clock
    default:
      return Document
  }
}

const getScopeTagType = (scope: string) => {
  switch (scope) {
    case 'global':
      return 'danger'
    case 'workflow':
      return 'warning'
    case 'local':
      return 'info'
    default:
      return 'info'
  }
}

const getScopeLabel = (scope: string) => {
  switch (scope) {
    case 'global':
      return '全局'
    case 'workflow':
      return '工作流'
    case 'local':
      return '局部'
    default:
      return scope
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'string':
      return '字符串'
    case 'number':
      return '数字'
    case 'boolean':
      return '布尔值'
    case 'json':
      return 'JSON'
    case 'list':
      return '列表'
    case 'currentTime':
      return '当前时间'
    default:
      return type
  }
}

const getNodeLabel = (nodeId: string) => {
  const node = workflowStore.nodes.find((n) => n.id === nodeId)
  return node?.data?.label || nodeId
}
</script>

<style scoped>
.variable-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.variable-search {
  margin-bottom: 16px;
}

.variable-tabs {
  margin-bottom: 16px;
}

.variable-list {
  flex: 1;
  overflow-y: auto;
  max-height: 450px;
}

.variable-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.variable-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.variable-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.variable-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.variable-icon {
  color: #909399;
}

.name {
  font-weight: 600;
  color: #303133;
}

.scope-tag {
  margin-left: 8px;
}

.variable-actions {
  display: flex;
  gap: 4px;
}

.variable-value {
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
}

.value-label {
  font-weight: 500;
}

.value-content {
  margin-left: 4px;
  font-family: 'Courier New', monospace;
}

.variable-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.variable-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #c0c4cc;
}

.variable-details {
  border-top: 1px solid #e4e7ed;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.details-header h4 {
  margin: 0;
  color: #303133;
}

.variable-value-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.el-dialog__custom_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-dialog__tools {
}
</style>
