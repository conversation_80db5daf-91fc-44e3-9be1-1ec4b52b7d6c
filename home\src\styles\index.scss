// import dark theme
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

:root {
  --font-color-1: #222222;
  --font-color-2: #666666;
  --font-color-3: #999999;
  --font-color-4: #cccccc;
  --font-color-5: #ffffff;
}

body {
  font-family: Inter, system-ui, Avenir, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

.default-confirm-class.el-message-box {
  border-radius: 12px !important;
  width: 500px !important;
  padding-bottom: 0 !important;
  padding-top: 0 !important;

  .el-message-box__header {
    padding: 24px 24px 4px;

    .el-message-box__headerbtn {
      width: 24px;
      height: 24px;
      right: 24px;
      top: 23px;
      padding: 3px;
      color: rgba(13, 13, 13, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        border-radius: 4px;
        background: rgba(13, 13, 13, 0.06);
      }

      &:active {
        border-radius: 4px;
        background: rgba(13, 13, 13, 0.1);
      }
    }

    .el-message-box__title {
      line-height: 24px;
      font-weight: 600;
      font-size: 16px;
      color: rgba(13, 13, 13, 0.9);
    }

    .el-message-box__close {
      font-size: 20px;
      color: #000;
    }
  }

  .el-message-box__content {
    padding: 18px 24px 0px;
  }

  .el-message-box__btns {
    padding: 24px 12px;

    .el-button {
      height: 32px !important;;
      padding: 6px 16px !important;
      font-size: 14px !important;
      font-weight: 400;
      border-radius: 4px !important;
    }
  }
}

.el-dialog {
  border-radius: 4px !important;
  .user-picker-dialog-header {
    height: 40px;
    box-sizing: border-box;
    .el-dialog__title {
      font-size: 14px;
    }
    .el-dialog__headerbtn {
      top: 7px;
    }
  }
  .picker-user-container {
    width: 100%;
    height: 400px;
  }
  .picker-user-mainbody {
    height: 100%;
    display: flex;

    .picker__tree {
      flex: 1;
      max-width: 280px;
      .el-tree-async {
        height: 100%;
        width: 100%;
        position: relative;
        border: solid 1px #dddddd;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        .el-tree-async-text {
          height: 30px;
          box-sizing: border-box;
          background-color: #f5f7fa;
          line-height: 30px;
          padding: 0 15px;
          border-bottom: solid 1px #dddddd;
        }
        .el-tree-async-search {
          width: calc(100% - 20px);
          margin: 6px auto;
          display: block;
        }
        .custom-tree-node {
          font-size: 14px;
          color: #888888;
          i {
            margin-right: 4px;
          }
        }
      }
    }

    .picker__handle {
      flex: 0 0 80px;
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      justify-content: center;
      box-sizing: border-box;

      .el-button {
        height: 28px;
        margin-left: 0;
        margin-bottom: 8px;
        border: 1px solid #f1f2f4;
        background-color: #f1f2f4;
      }
      .el-button:hover {
        color: #235fdf;
        border-color: #dbe5fd;
      }
    }

    .picker__table {
      flex: 1;
      width: 0;
      overflow-x: auto;
      position: relative;

      .el-table-box {
        min-width: 300px;
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table-header {
          width: 100%;
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          padding: 6px 6px 0 6px;
          box-sizing: border-box;
          align-items: flex-start;
          flex-shrink: 0;
          flex-grow: 0;
          min-height: 40px;
          .el-table-header-title {
            flex-shrink: 0;
            line-height: 20px;
            height: 20px;
            font-size: 16px;
            margin: 3px 10px 5px 0;
            border-left: 2px solid #235fdf;
            padding-left: 5px;
          }
          .el-table-header-left {
            display: inline-flex;
            flex-grow: 1;
            margin-left: 5px;
            .el-table-header-input {
              height: 28px;
              line-height: 28px;
            }
          }
          .el-table-header-right {
            display: inline-flex;
            padding-left: 20px;
            padding-bottom: 6px;
            flex-shrink: 0;
            .header__text {
              font-size: 14px;
              line-height: 20px;
              height: 20px;
              margin: 3px 0 5px 0;
            }
          }
        }
        .el-table-body {
          display: inline-flex;
          box-sizing: border-box;
          flex: 1;
          height: 100%;
          position: relative;
          overflow: hidden;
        }
      }

      .header__text {
        font-size: 14px;
        line-height: 20px;
        height: 20px;
        margin: 3px 0 5px 0;

        > span {
          color: #235fdf;
        }
      }

      .filter-text {
        position: absolute;
        width: 22%;
        top: 5px;
        left: 70px;
        z-index: 5;
      }
    }
  }
}

.el-popper__arrow {
  display: none;
}

.el-dropdown-menu__item {
  //padding: 0 20px !important;
  .el-dropdown-item-title {
    height: 28px !important;
    line-height: 28px !important;
  }
  &:hover, &:focus {
    background: #f0f1f3 !important;
    color: #222 !important;
  }
}
.el-dropdown-menu__item.selected {
  color: #0054D2;
  font-weight: 700;
  &:hover, &:focus {
    color: #0054D2 !important;
  }
}

.el-table {
  th.el-table__cell {
    background: #F7F7F9 !important;
  }
}


// 标准表格样式
.crud-table thead {
  color: #5C5F66 !important;
}
.mask-table thead th {
  font-weight: 400 !important;
}
.crud-table thead .el-table__cell {
  border-right: 1px solid #EEEEEE !important;
}
.crud-tag-style {
  .el-tag--primary {
    --el-tag-text-color: #1E39C3 !important;
    --el-tag-bg-color: #F0F5FF !important;
    --el-tag-border-color: #BED2FF !important;
    --el-tag-hover-color: #1E39C3 !important;
  }
  .el-tag--warning {
    --el-tag-text-color: #9E7E00 !important;
    --el-tag-bg-color: #FFFDDF !important;
    --el-tag-border-color: #E9DE9A !important;
    --el-tag-hover-color: #9E7E00 !important;
  }
  .el-tag--info {
    --el-tag-text-color: #666666 !important;
    --el-tag-bg-color: #F7F7F9 !important;
    --el-tag-border-color: #E6E7E9 !important;
    --el-tag-hover-color: #666666 !important;
  }
}
.mask-table thead {
  color: #999999 !important;
}
.mask-table thead th {
  font-weight: 500 !important;
}
.mask-table th.el-table__cell.is-leaf, .mask-table td.el-table__cell {
  border-bottom: 1px solid #EBEEF5 !important;
}
.mask-table .el-table__cell {
  border-right: 1px solid #EBEEF5 !important;
}
.mask-table .el-table__border-left-patch {
  background-color: #EBEEF5;
}
.mask-table::after {
  background-color: #EBEEF5 !important;
}

.header-tools-drop-menu .el-dropdown-menu__item {
  min-width: 100px;
  height: 32px;
  padding: 2px 16px !important;
  .header-tools-drop-iconfont {
    font-size: 12px;
    margin-right: 8px;
  }
}


// 服务包管理页面form-item表单从单行调整为上下结构
.service-package-form {
  max-height: 720px;
  overflow: hidden;
  overflow-y: auto;
  padding: 0 50px;
  padding-top: 16px;
  .el-form-item {
    margin-bottom: 22px;
    .el-form-item__label {
      color: #5C5F66;
    }
    .el-form-item__error {
      width: 100%;
      background: #ffffff;
    }
    .form-tip {
      font-weight: 400;
      font-size: 12px;
      color: #BCBFC3;
      font-size: 12px;
      line-height: 1;
      padding-top: 2px;
      position: absolute;
      top: 100%;
      left: 0;
    }
  }
  .form-item-flex-column {
    flex-direction: column;
    .el-form-item__label {
      display: block;
      text-align: left;
    }
  }
  .el-upload__text {
    font-size: 12px !important;
    .el-upload__tip {
      font-weight: 400;
      font-size: 12px;
      color: #BCBFC3;
    }
  }
  .el-upload__list {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #FAFAFA;
    .icon-preview-item {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 64px;
      }
      .icon-preview-item-tool {
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        display: none;
        .el-icon {
          font-size: 20px;
          color: #ffffff;
          cursor: pointer;
        }
      }
      &:hover {
        .icon-preview-item-tool {
          display: flex;
        }
      }
    }
  }
  .icon-preview {
    width: 100%;
    height: 128px;
    &.small-height {
      // height: 96px;
      // height: 128px;
      height:48px;
    }
    .icon-preview-item {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      border: 1px solid #E6E7E9;
      img {
        max-width: 100%;
        max-height: 100%;
      }
      .icon-preview-item-tool {
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        display: none;
        .el-icon {
          color: #ffffff;
          cursor: pointer;
        }
      }
      &:hover {
        .icon-preview-item-tool {
          display: flex;
        }
      }
    }
  }
}
.service-package-template-form {
  // padding-left: 30px;
  padding-left: 10px;
  padding-right: 50px;
  max-height: 600px;
  .service-package-template-form-upload {
    .el-upload-dragger {
      background: #FAFAFA;
    }
  }
}
.table-selected {
  display: flex;
  >div {
    display: inline-block;
    height: 260px;
  }
  .table-selected-title {
    height: 20px;
    line-height: 20px;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    margin-bottom: 8px;
  }
  .table-selected-talbe {
    width: calc(60% - 16px);

    margin-right: 16px;
  }
  .table-selected-selected {
    width: 40%;
    height: 260px;
    .table-selected-cards {
      height: calc(100% - 28px);
      padding: 10px;
      box-sizing: border-box;
      background: #F2F4F8;
      overflow-y: auto;
      .table-selected-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #FFFFFF;
        border: 1px solid #E6E7E9;
        box-shadow: 0 2px 10px 0 #00000014;
        border-radius: 4px;
        padding: 16px;
        box-sizing: border-box;
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
        .card-left {
          height: 100%;
          >div {
            margin-bottom: 8px;
            &:last-child {
              margin-bottom: 0;
            }
          }
          .card-left-title {
            font-weight: 500;
            font-size: 14px;
            color: #222222e6;
          }
          .card-left-text {
            font-weight: 400;
            font-size: 12px;
            color: #BCBFC3;
          }
        }
        .card-right {
          height: 100%;
        }
      }
    }
  }
}
.upload-item {
  display: flex;
  flex-direction: column;
  .upload-item-img {
    width: 64px;
  }
}
.upload-item-text {
  height: 18px;
  line-height: 18px;
  font-weight: 400;
  font-size: 12px;
  color: #BCBFC3;
  margin-top: 8px;
}
.servicePackage-dialog {
  .el-dialog__title {
    font-size: 14px;
    font-weight: bold;
  }
  .el-dialog__headerbtn {
    top: 5px;
  }
  .icon-box {
    height: 300px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    padding: 8px;
    .icon-item {
      width: 100%;
      padding: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &:hover {
        background: #F6F6F6;
      }
      &.active {
        background: #F6F6F6;
      }
      img {
        width: 64px;
        height: 64px;
      }
    }
    .icon-item:nth-child(5n) {
      margin-right: 0;
    }
  }
  .image {
    grid-template-columns: repeat(3, 1fr);
    .icon-item {
      img {
        width: 280px;
        height: auto;
      }
    }
    .icon-item:nth-child(3n) {
      margin-right: 0;
    }
  }
}
.version-dialog-box {
  .dialog-box-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialog-box-title-text {
      display: flex;
      font-weight: 500;
      font-size: 14px;
      color: #222222;
      .text-tip {
        height: 18px;
        line-height: 18px;
        font-weight: 400;
        font-size: 12px;
        color: #BCBFC3;
      }
    }
  }
  .dialog-box-table {
    height: 240px;
    margin-top: 16px;
    .task-link {
      margin-right: 16px;
      &.is-disabled {
        color: #BCBFC3;
      }
    }
  }
}

.el-pagination > *.is-first {
  font-weight: 400;
  color: #5C5F66;
}
.el-pager li {
  background: #FFFFFF !important;
  border: 1px solid #E6E7E9 !important;
  border-radius: 4px !important;
  margin: 0 4px;
  &:hover {
    border: 1px solid #0054D9 !important;
    background: rgba(0, 84, 217, 0.08) !important;
  }
}
.el-pager li.is-active {
  border: 1px solid #0054D9 !important;
  background: rgba(0, 84, 217, 0.08) !important;
}


// vue-quill-editor插件汉化
.ql-snow .ql-tooltip[data-mode=link]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-right: 0px;
    content: '保存';
    padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode=video]::before {
    content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: '10px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: '32px';
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: '标题1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: '标题2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: '标题3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: '标题4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: '标题5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: '标题6';
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: '衬线字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: '等宽字体';
}






// 自定义插槽内的select字体大小
.el-select-small{
  .el-select__wrapper{
    font-size: 12px;
    line-height: 28px;
  }
}
.right-radius-0{
  .el-select__wrapper{
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
  .el-input__wrapper{
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
}

