import ast
from datetime import datetime, time

from loguru import logger
from robot.api.deco import keyword

from utils.time import hms_str_to_time, str_to_datetime


class ConditionTools:

    def _compare_datetime(self, first: datetime, second: datetime, opt):
        if not (
            isinstance(first, (datetime, time)) and isinstance(second, (datetime, time))
        ):
            raise TypeError("first和second必须是datetime或time对象")
        if type(first) is not type(second):
            raise TypeError(
                f"两个参数类型必须一致，first是{type(first).__name__}，second是{type(second).__name__}"
            )
        match opt.lower():
            # 大于
            case ">" | "gt" | "greater" | "greaterthan" | "大于":
                return first > second
            case "<" | "lt" | "less" | "lessthan" | "小于":
                return first < second
            case ">=" | "ge" | "greaterequal" | "greaterthanorequal" | "大于等于":
                return first >= second
            case "<=" | "le" | "lessthanorequal" | "小于等于":
                return first <= second
            case "=" | "eq" | "equals" | "等于" | "相等":
                return first == second
            case "!=" | "<>" | "ne" | "notequals" | "不等于" | "不相等":
                return first != second
            case _:
                raise ValueError(f"不支持的运算符：{opt}")

    @keyword("Condition Test")
    def condition_test(self, *args):
        if len(args) == 3:
            try:
                return self.test_one(args[0], args[1], args[2])
            except Exception as e:
                logger.info(f"比较条件{args}有问题，返回false{e}")
                return False
        elif (len(args) - 3) % 4 == 0:
            index = 0
            length = len(args)
            current = None
            while index < length:
                group = args[index : index + 3]
                if group:
                    try:
                        r = self.test_one(group[0], group[1], group[2])
                    except Exception as e:
                        logger.info(f"比较条件{group}有问题，返回false{e}")
                        r = False
                    index += 3  # 移动到下一组起始位置
                    relation = args[index]
                    if current is None:
                        current = r
                    elif str(relation).lower() == "and":
                        current = current and r
                    else:
                        current = current or r
                    index += 1  # 跳过间隔元素
            return current
        else:
            raise ValueError(f"参数数量有问题必须是3或者3的倍数-1 {args}")

    def test_one(self, field, value, opt):

        field = str(field)
        value = str(value)

        data_type = self.determine_data_type(field, value, opt)
        match data_type.lower():
            case "bool":
                return self.test_bool(field, value, opt)
            case "float":
                return self.test_float(field, value, opt)
            case "datetime" | "time":
                return self.test_datetime(field, value, opt)
            case "list":
                return self.test_list(field, value, opt)
            case "str":
                return self.test_str(field, value, opt)
            case _:
                raise Exception(f"不能确定数据类型{data_type}")

    def determine_data_type(self, field: str, value: str, opt: str) -> str:
        type_checkers = [
            ("list", self.is_list),
            ("datetime", self.is_datetime),
            ("time", self.is_time),
            ("bool", self.is_bool),
            ("float", self.is_float),
            ("str", lambda x: True),
        ]

        for type_name, checker in type_checkers:
            if checker(field) and checker(value):
                return type_name
        return "error"

    def is_list(self, s: str) -> bool:
        s = s.strip()
        if not (s.startswith("[") and s.endswith("]")):
            return False
        try:
            result = ast.literal_eval(s)
            return isinstance(result, list)
        except (SyntaxError, ValueError, TypeError):
            return False

    def is_datetime(self, s: str) -> bool:
        s = s.strip()
        datetime_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y/%m/%d %H:%M:%S",
            "%Y-%m-%d %H:%M",
            "%Y/%m/%d %H:%M",
            "%Y/%m/%d",
            "%Y-%m-%d",
        ]
        for fmt in datetime_formats:
            try:
                datetime.strptime(s, fmt)
                return True
            except ValueError:
                continue
        return False

    def is_time(self, s: str) -> bool:
        s = s.strip()
        time_formats = [
            "%H:%M:%S",
            "%H:%M",
            "%I:%M:%S %p",
            "%I:%M %p",
        ]
        for fmt in time_formats:
            try:
                datetime.strptime(s, fmt).time()
                return True
            except ValueError:
                continue
        return False

    def is_bool(self, s: str) -> bool:
        s = s.strip().lower()
        return s in ("true", "false")

    def is_float(self, s: str) -> bool:
        s = s.strip()
        try:
            float(s)
            return True
        except ValueError:
            return False

    def test_float(self, field, value, opt):

        first, second = float(field), float(value)

        match opt.lower():
            case ">" | "gt" | "greater" | "greaterthan" | "大于":
                return first > second
            case "<" | "lt" | "less" | "lessthan" | "小于":
                return first < second
            case ">=" | "ge" | "greaterequal" | "greaterthanorequal" | "大于等于":
                return first >= second
            case "<=" | "le" | "lessthanorequal" | "小于等于":
                return first <= second
            case "=" | "eq" | "equals" | "等于" | "相等":
                return first == second
            case "!=" | "<>" | "ne" | "notequals" | "不等于" | "不相等":
                return first != second
            case _:
                raise ValueError(
                    f"判断为数字不支持选择的操作 '{field}' '{value}' {opt}"
                )

    def test_bool(self, field, value, opt):
        first, second = bool(field), bool(value)

        match opt.lower():
            case "=" | "eq" | "equals" | "等于" | "相等":
                return first == second
            case "!=" | "<>" | "ne" | "notequals" | "不等于" | "不相等":
                return first != second
            case _:
                raise ValueError(
                    f"判断为bool不支持选择的操作 '{field}' '{value}' {opt}"
                )

    def test_datetime(self, field, value, opt):
        try:
            ft = str_to_datetime(field)
            st = str_to_datetime(value)
            return self._compare_datetime(ft, st, opt)
        except ValueError:
            ft = hms_str_to_time(field)
            st = hms_str_to_time(value)
            return self._compare_datetime(ft, st, opt)

    def test_list(self, field, value, opt):
        first = ast.literal_eval(field)
        second = ast.literal_eval(value)

        match opt.lower():
            case "contains" | "in":
                return all(item in first for item in second)
            case "not_contains" | "not_in" | "notcontains" | "notin":
                return any(item not in first for item in second)
            case "=" | "eq" | "equals" | "等于" | "相等":
                if len(first) != len(second):
                    return False
                return all(a == b for a, b in zip(first, second))
            case "!=" | "<>" | "ne" | "notequals" | "不等于" | "不相等":
                if len(first) != len(second):
                    return True
                return not all(a == b for a, b in zip(first, second))
            case _:
                raise ValueError(
                    f"判断为list不支持选择的操作 '{field}' '{value}' {opt}"
                )

    def test_str(self, field, value, opt):

        first, second = str(field), str(value)

        match opt.lower():
            case "=" | "eq" | "equals" | "等于" | "相等":
                return first == second
            case "!=" | "<>" | "ne" | "notequals" | "不等于" | "不相等":
                return first != second
            case "contains" | "in":
                return second in first
            case "not_contains" | "not_in" | "notcontains" | "notin":
                return second not in first
            case "startswith" | "prefix":
                return first.startswith(second)
            case "endswith" | "suffix":
                return first.endswith(second)

            case _:
                raise ValueError(f"判断为str不支持选择的操作 '{field}' '{value}' {opt}")
