from typing import Optional, List

from pydantic import BaseModel

from core.executor import ActionManager
from models.workflow import WorkflowData, WorkflowEdge, WorkflowNode, ValidationResult


class ValidationError(BaseModel):
    """验证错误"""

    node_id: Optional[str] = None
    edge_id: Optional[str] = None
    field: Optional[str] = None
    message: str
    severity: str = "error"  # error, warning, info


class WorkflowValidator:

    @staticmethod
    def _has_circular_dependency(
        nodes: List[WorkflowNode], edges: List[WorkflowEdge]
    ) -> bool:
        """检查是否存在循环依赖"""
        graph = {node.id: [] for node in nodes}

        for edge in edges:
            if edge.type in ["control", "workflow"]:
                graph[edge.source].append(edge.target)

        # 使用DFS检测循环
        visited = set()
        rec_stack = set()

        def has_cycle(node_id: str) -> bool:
            visited.add(node_id)
            rec_stack.add(node_id)

            for neighbor in graph[node_id]:
                if neighbor not in visited:
                    if has_cycle(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True

            rec_stack.remove(node_id)
            return False

        for node in nodes:
            if node.id not in visited:
                if has_cycle(node.id):
                    return True

        return False

    @staticmethod
    def _find_isolated_nodes(
        nodes: List[WorkflowNode], edges: List[WorkflowEdge]
    ) -> List[str]:
        """查找孤立节点"""
        connected_nodes = set()

        for edge in edges:
            connected_nodes.add(edge.source)
            connected_nodes.add(edge.target)

        isolated = []
        for node in nodes:
            if node.id not in connected_nodes and len(nodes) > 1:
                isolated.append(node.id)

        return isolated

    @staticmethod
    def _validate_node(
        actions: ActionManager, node: WorkflowNode
    ) -> List[ValidationError]:
        """验证单个节点"""
        errors = []

        # 检查组件类型是否存在
        component_type = node.data.componentType

        # 获取动作
        action = actions.get(component_type)

        if action is None:
            errors.append(
                ValidationError(
                    node_id=node.id,
                    message=f"未知的组件类型: {component_type}",
                    severity="error",
                )
            )
            return errors

        # 验证必需的配置项
        for field_name, field_config in action.config_schema.items():
            if field_config.get("required", False):
                if field_name not in node.data.config:
                    errors.append(
                        ValidationError(
                            node_id=node.id,
                            field=field_name,
                            message=f"缺少必需的配置项: {field_name}",
                            severity="error",
                        )
                    )
                elif (
                    node.data.config[field_name] == ""
                    or node.data.config[field_name] is None
                ):
                    errors.append(
                        ValidationError(
                            node_id=node.id,
                            field=field_name,
                            message=f"必需的配置项不能为空: {field_name}",
                            severity="error",
                        )
                    )

        return errors

    @staticmethod
    def validate(actions: ActionManager, workflow: WorkflowData) -> ValidationResult:
        """验证工作流"""
        errors = []
        warnings = []

        # 检查是否有节点
        if not workflow.nodes:
            errors.append(
                ValidationError(message="工作流中没有任何节点", severity="error")
            )
            return ValidationResult(is_valid=False, errors=errors)

        # 验证每个节点
        for node in workflow.nodes:
            node_errors = WorkflowValidator._validate_node(actions, node)
            errors.extend(node_errors)

        # 检查循环依赖
        if WorkflowValidator._has_circular_dependency(workflow.nodes, workflow.edges):
            errors.append(
                ValidationError(message="工作流中存在循环依赖", severity="error")
            )

        # 检查开始和结束节点
        start_nodes = [
            node
            for node in workflow.nodes
            if node.data.componentType == "workflow_start"
        ]
        end_nodes = [
            node for node in workflow.nodes if node.data.componentType == "workflow_end"
        ]

        # 检查开始节点
        if len(start_nodes) == 0:
            errors.append(
                ValidationError(message="工作流必须包含一个开始节点", severity="error")
            )
        elif len(start_nodes) > 1:
            errors.append(
                ValidationError(message="工作流只能包含一个开始节点", severity="error")
            )

        # 检查结束节点
        if len(end_nodes) == 0:
            warnings.append(
                ValidationError(message="建议为工作流添加结束节点", severity="warning")
            )

        # 检查开始节点不应有输入连接
        for start_node in start_nodes:
            input_edges = [
                edge for edge in workflow.edges if edge.target == start_node.id
            ]
            if input_edges:
                errors.append(
                    ValidationError(
                        node_id=start_node.id,
                        message="开始节点不能有输入连接",
                        severity="error",
                    )
                )

        # 检查结束节点不应有输出连接
        for end_node in end_nodes:
            output_edges = [
                edge for edge in workflow.edges if edge.source == end_node.id
            ]
            if output_edges:
                errors.append(
                    ValidationError(
                        node_id=end_node.id,
                        message="结束节点不能有输出连接",
                        severity="error",
                    )
                )

        # 检查孤立节点（排除开始和结束节点的特殊情况）
        isolated_nodes = WorkflowValidator._find_isolated_nodes(
            workflow.nodes, workflow.edges
        )
        for node_id in isolated_nodes:
            node = next((n for n in workflow.nodes if n.id == node_id), None)
            if node and node.data.componentType not in [
                "workflow_start",
                "workflow_end",
            ]:
                warnings.append(
                    ValidationError(
                        node_id=node_id,
                        message=f"节点 {node_id} 没有连接到其他节点",
                        severity="warning",
                    )
                )

        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)
