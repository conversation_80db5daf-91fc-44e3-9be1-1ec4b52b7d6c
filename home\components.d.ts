/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Catalogue: typeof import('./src/components/catalogue/index.vue')['default']
    ComponentPanel: typeof import('./src/components/panels/ComponentPanel.vue')['default']
    ConditionsField: typeof import('./src/components/common/ConditionsField.vue')['default']
    ConfigInput: typeof import('./src/components/panels/config/ConfigInput.vue')['default']
    ConfigNumber: typeof import('./src/components/panels/config/ConfigNumber.vue')['default']
    ConfigSelect: typeof import('./src/components/panels/config/ConfigSelect.vue')['default']
    ConfigSwitch: typeof import('./src/components/panels/config/ConfigSwitch.vue')['default']
    ContextMenu: typeof import('./src/components/canvas/ContextMenu.vue')['default']
    copy: typeof import('./src/components/catalogue/TocItem copy.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElCountdown: typeof import('element-plus/es')['ElCountdown']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScroll: typeof import('element-plus/es')['ElScroll']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EndNode: typeof import('./src/components/nodes/EndNode.vue')['default']
    ForgetPwd: typeof import('./src/components/loginType/ForgetPwd.vue')['default']
    Identify: typeof import('./src/components/loginType/Identify.vue')['default']
    InvalidNodesPanel: typeof import('./src/components/panels/InvalidNodesPanel.vue')['default']
    LoginQrCode: typeof import('./src/components/loginType/LoginQrCode.vue')['default']
    LoginSms: typeof import('./src/components/loginType/LoginSms.vue')['default']
    LoginValid: typeof import('./src/components/loginType/loginValid.vue')['default']
    LogRightPanel: typeof import('./src/components/panels/LogRightPanel.vue')['default']
    MapFrame: typeof import('./src/components/mapFrame.vue')['default']
    MissionEdit: typeof import('./src/components/missionEdit.vue')['default']
    MultiLevelToc: typeof import('./src/components/catalogue/MultiLevelToc.vue')['default']
    PackagePanel: typeof import('./src/components/panels/PackagePanel.vue')['default']
    PageLoading: typeof import('./src/components/pageLoading/index.vue')['default']
    ProcessFormField: typeof import('./src/components/common/ProcessFormField.vue')['default']
    Pwd: typeof import('./src/components/loginType/pwd.vue')['default']
    Register: typeof import('./src/components/loginType/register.vue')['default']
    ReportDesigner: typeof import('./src/components/panels/ReportDesigner.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SmartConfigField: typeof import('./src/components/panels/config/SmartConfigField.vue')['default']
    SmartPropertyPanel: typeof import('./src/components/panels/SmartPropertyPanel.vue')['default']
    StartNode: typeof import('./src/components/nodes/StartNode.vue')['default']
    TenantList: typeof import('./src/components/layout/tenantList.vue')['default']
    TennatList: typeof import('./src/components/layout/tennatList.vue')['default']
    TocItem: typeof import('./src/components/catalogue/TocItem.vue')['default']
    TreePickerUser: typeof import('./src/components/treePickerUser.vue')['default']
    Userset: typeof import('./src/components/layout/userset.vue')['default']
    UsersetV1: typeof import('./src/components/layout/usersetV1.vue')['default']
    VariableInputField: typeof import('./src/components/common/VariableInputField.vue')['default']
    VariablePanel: typeof import('./src/components/panels/VariablePanel.vue')['default']
    VariablesField: typeof import('./src/components/common/VariablesField.vue')['default']
    VariablesMapField: typeof import('./src/components/common/VariablesMapField.vue')['default']
    WordDesigner: typeof import('./src/components/panels/WordDesigner.vue')['default']
    WordEditor: typeof import('./src/components/doc/wordEditor.vue')['default']
    WorkflowCanvas: typeof import('./src/components/canvas/WorkflowCanvas.vue')['default']
    WorkflowEdge: typeof import('./src/components/canvas/WorkflowEdge.vue')['default']
    WorkflowNode: typeof import('./src/components/canvas/WorkflowNode.vue')['default']
  }
  export interface GlobalDirectives {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
