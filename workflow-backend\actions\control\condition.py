from typing import Dict, Optional

from actions.control import control_group
from core.executor import ExecutionContext
from utils.condtion import test_one


@control_group.action(
    type="condition",
    label="条件判断",
    description="根据条件执行不同的分支",
    category="control",
)
def test_condition(context: ExecutionContext, config: Dict, options: Dict):

    result = None

    conditions = config.get("conditions", [])

    if len(conditions) == 0:
        context.failed(f"没有配置任何条件")
        return

    for condition in conditions:
        # 条件id
        condition_key = condition.get("condition_id", "")

        if condition_key == "":
            context.failed(f"有条件含有无效的condition{condition}")

        relation = condition.get("relation", "and")
        result: Optional[bool]

        for cond in condition.get("conditions", []):
            field = cond.get("field", "")
            value = cond.get("value", "")
            operator = cond.get("operator", "equals")
            r = test_one(field, value, operator)
            if result is None:
                result = r
            else:
                if relation.lower() == "or":
                    if r or result:
                        result = True
                        break
                else:
                    if not r and result:
                        result = False
                        break

        if result:
            context.action_success(
                condition_key=condition_key, msg=f"条件 {condition} 满足"
            )

    context.action_success(condition_key="node_end")
