from typing import Dict, Optional, Any, List

from pydantic.dataclasses import dataclass

from actions.control import group
from core.executor import ExecutionContext, ActionContext
from core.model import BaseActionParams
from utils.condtion import test_one


class ConditionParams(BaseActionParams):
    conditions: List[Dict[str, Any]]


@group.action(
    type="condition",
    label="条件判断",
    description="根据条件执行不同的分支",
    category="control",
    params=ConditionParams,
)
def test_condition(context: ActionContext, params: ConditionParams):

    result = None

    conditions = params.conditions

    if len(conditions) == 0:
        context.get_agent_context().failed(f"没有配置任何条件")
        return

    for condition in conditions:
        # 条件id
        condition_key = condition.get("condition_id", "")

        if condition_key == "":
            context.get_agent_context().failed(f"有条件含有无效的condition{condition}")

        relation = condition.get("relation", "and")
        result: Optional[bool]

        for cond in condition.get("conditions", []):
            field = cond.get("field", "")
            value = cond.get("value", "")
            operator = cond.get("operator", "equals")
            r = test_one(field, value, operator)
            if result is None:
                result = r
            else:
                if relation.lower() == "or":
                    if r or result:
                        result = True
                        break
                else:
                    if not r and result:
                        result = False
                        break

        if result:
            context.success(condition_key=condition_key, msg=f"条件 {condition} 满足")

    context.success(condition_key="node_end")
