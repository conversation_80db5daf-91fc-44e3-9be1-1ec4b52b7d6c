"""
Workflow-Use 录制服务
基于workflow-use的确定性录制和执行服务
完全替换原有的实验性录制实现
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

# 导入workflow-use集成
try:
    from experimental.workflow_use.integration import workflow_use_integration
    WORKFLOW_USE_AVAILABLE = True
except ImportError as e:
    WORKFLOW_USE_AVAILABLE = False
    workflow_use_integration = None

logger = logging.getLogger(__name__)


class WorkflowUseRecordingService:
    """
    基于Workflow-Use的录制服务
    提供确定性、快速、可靠的工作流录制和执行
    """

    def __init__(self):
        """初始化录制服务"""
        if WORKFLOW_USE_AVAILABLE:
            self.integration = workflow_use_integration
            # 启用workflow-use集成
            self.integration.enable()
            logger.info("WorkflowUse录制服务已初始化")
        else:
            self.integration = None
            logger.error("WorkflowUse录制服务初始化失败，workflow-use不可用")
        
        # 状态管理
        self.recording_status = "idle"  # idle, recording, completed
        self.current_session_id = None

    async def start_recording_session(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动录制会话
        
        Args:
            config: 录制配置，包含browser、start_url等
            
        Returns:
            录制启动结果
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'success': False,
                'error': 'Workflow-Use录制引擎不可用，请检查安装'
            }
        
        try:
            logger.info(f"启动Workflow-Use录制会话，配置: {config}")
            
            # 更新状态
            self.recording_status = "recording"
            
            # 使用workflow-use启动录制
            result = await self.integration.start_recording_session(config)
            
            if result['success']:
                self.current_session_id = result.get('session_id')
                logger.info(f"录制会话启动成功: {self.current_session_id}")
                
                # 添加workflow-use特有的功能标识
                result['features'] = {
                    'deterministic_execution': True,
                    'fast_replay': True,
                    'ai_fallback': False,  # 默认禁用
                    'smart_selectors': True,
                    'element_highlighting': True,
                    'recording_type': 'workflow-use'
                }
            else:
                self.recording_status = "idle"
                logger.error(f"录制会话启动失败: {result.get('error')}")
                
            return result
            
        except Exception as e:
            logger.error(f"启动录制会话异常: {e}")
            self.recording_status = "idle"
            return {
                'success': False,
                'error': f'启动录制失败: {str(e)}'
            }

    async def stop_recording_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        停止录制会话
        
        Args:
            session_id: 会话ID（可选）
            
        Returns:
            录制停止结果，包含生成的工作流节点
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'success': False,
                'error': 'Workflow-Use录制引擎不可用'
            }
        
        try:
            logger.info(f"停止Workflow-Use录制会话: {session_id or self.current_session_id}")
            
            # 使用workflow-use停止录制
            result = await self.integration.stop_recording_session(session_id)
            
            # 更新状态
            self.recording_status = "idle"
            self.current_session_id = None
            
            if result['success']:
                logger.info(f"录制会话停止成功，生成了{len(result.get('workflow_nodes', []))}个节点")
                
                # 添加统计信息
                result['stats'] = {
                    'recording_type': 'workflow-use',
                    'total_operations': len(result.get('workflow_nodes', [])),
                    'nodes_generated': len(result.get('workflow_nodes', [])),
                    'deterministic': True
                }
            else:
                logger.error(f"录制会话停止失败: {result.get('error')}")
                
            return result
            
        except Exception as e:
            logger.error(f"停止录制会话异常: {e}")
            self.recording_status = "idle"
            self.current_session_id = None
            return {
                'success': False,
                'error': f'停止录制失败: {str(e)}'
            }

    def get_recording_status(self) -> Dict[str, Any]:
        """
        获取录制状态
        
        Returns:
            当前录制状态信息
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'success': False,
                'status': 'unavailable',
                'message': 'Workflow-Use录制引擎不可用'
            }
        
        try:
            # 获取workflow-use状态
            status = self.integration.get_recording_status()
            
            # 添加服务层状态信息
            status.update({
                'recording_status': self.recording_status,
                'current_session_id': self.current_session_id,
                'engine': 'workflow-use',
                'capabilities': self.get_capabilities()
            })
            
            return status
            
        except Exception as e:
            logger.error(f"获取录制状态异常: {e}")
            return {
                'success': False,
                'status': 'error',
                'message': f'获取状态失败: {str(e)}'
            }

    def get_real_time_operations(self) -> Dict[str, Any]:
        """
        获取实时操作列表
        注意：workflow-use使用不同的录制机制，不提供实时操作列表
        
        Returns:
            操作列表信息
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'success': False,
                'operations': [],
                'total_count': 0,
                'message': 'Workflow-Use录制引擎不可用'
            }
        
        # workflow-use不提供实时操作列表，返回状态信息
        return {
            'success': True,
            'operations': [],
            'total_count': 0,
            'recording_type': 'workflow-use',
            'message': 'Workflow-Use使用确定性录制，完成后一次性生成所有节点',
            'status': self.recording_status
        }

    async def pause_recording(self) -> Dict[str, Any]:
        """
        暂停录制
        注意：workflow-use当前不支持暂停/恢复功能
        
        Returns:
            暂停结果
        """
        return {
            'success': False,
            'error': 'Workflow-Use当前不支持暂停功能，请使用停止录制',
            'suggestion': '建议完成当前录制后重新开始新的录制会话'
        }

    async def resume_recording(self) -> Dict[str, Any]:
        """
        恢复录制
        注意：workflow-use当前不支持暂停/恢复功能
        
        Returns:
            恢复结果
        """
        return {
            'success': False,
            'error': 'Workflow-Use当前不支持恢复功能，请重新开始录制',
            'suggestion': '建议开始新的录制会话'
        }

    def get_capabilities(self) -> Dict[str, Any]:
        """
        获取录制服务能力
        
        Returns:
            服务能力信息
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'available': False,
                'engine': 'workflow-use',
                'error': 'Workflow-Use不可用'
            }
        
        return {
            'available': True,
            'engine': 'workflow-use',
            'features': {
                'deterministic_execution': True,
                'fast_replay': True,
                'ai_fallback': False,  # 可配置
                'smart_selectors': True,
                'element_highlighting': True,
                'pause_resume': False,  # 当前不支持
                'real_time_operations': False,  # 不同的录制机制
                'format_conversion': True,
                'non_ai_execution': True
            },
            'supported_browsers': ['chromium', 'firefox', 'webkit'],
            'recording_type': 'browser_extension'
        }

    async def execute_workflow(self, workflow_definition: Dict[str, Any], 
                             inputs: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行工作流（新增功能）
        
        Args:
            workflow_definition: 工作流定义
            inputs: 输入参数
            
        Returns:
            执行结果
        """
        if not WORKFLOW_USE_AVAILABLE or not self.integration:
            return {
                'success': False,
                'error': 'Workflow-Use执行引擎不可用'
            }
        
        try:
            logger.info("开始执行Workflow-Use工作流")
            result = await self.integration.execute_workflow(workflow_definition, inputs)
            
            if result['success']:
                logger.info("工作流执行完成")
            else:
                logger.error(f"工作流执行失败: {result.get('error')}")
                
            return result
            
        except Exception as e:
            logger.error(f"执行工作流异常: {e}")
            return {
                'success': False,
                'error': f'执行工作流失败: {str(e)}'
            }

    async def cleanup(self):
        """清理资源"""
        try:
            if self.integration:
                await self.integration.cleanup()
            
            self.recording_status = "idle"
            self.current_session_id = None
            
            logger.info("WorkflowUse录制服务已清理")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 全局服务实例
workflow_use_recording_service = WorkflowUseRecordingService()
