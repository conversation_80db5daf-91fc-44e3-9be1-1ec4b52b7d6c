
import prism from 'prismjs'
import mermaid from 'mermaid';
// import Editor, { ElementType, getElementListByHTML,BlockType } from "@hufe921/canvas-editor";
import Editor, { ElementType, getElementListByHTML,BlockType } from "../../../public/static/canvas-editor/canvas-editor.es.js";
import docxPlugin from '@hufe921/canvas-editor-plugin-docx';
import { debounce, nextTick, splitText, formatPrismToken } from './index.js'
import { Dialog } from './dialog/Dialog'
import { Signature } from './signature/Signature'
let modeIndex = 0; // 切换模式变量
export function Init(IElement,CommentList,props) {
  const isApple =
    typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent);

  // 使用组件特定的选择器
  const componentId = IElement.id || '';
  const editorElement = document.querySelector(`.editor${componentId}`);

  if (!editorElement) {
    console.error(`Element with class .editor${componentId} not found.`);
    return;
  }

  // 获取组件容器，用于限制DOM查询范围
  const componentContainer = componentId ? document.querySelector(`#${componentId}`) : document;

  // 辅助函数：安全的DOM选择器
  const safeQuerySelector = (selector) => {
    return componentContainer ? componentContainer.querySelector(selector) : null;
  };

  const RowFlex = {
    CENTER: 'center',
    LEFT: 'left',
    RIGHT: 'right'
  };

  const commentList = CommentList || []

  let options = {
    margins: [50, 50, 70, 50],
    watermark: {
      data: '',
      size: 120
    }, // 水印
    pageNumber: {
      format: '第{pageNo}页/共{pageCount}页'
    },
    placeholder: {
      data: '请输入正文'
    },
    zone: {
      tipDisabled: false
    },
    maskMargin: [60, 0, 30, 0] // 菜单栏高度60，底部工具栏30为遮盖层
  }
  const instance = new Editor(
    editorElement,
    IElement.data, // 数据
    {
      ...options,
      ...(IElement.options || {})
    }
  );

  // 集成官方Word插件
  instance.use(docxPlugin)

  Reflect.set(window, 'editor', instance);

  nextTick(() => {
    updateComment();
  });
  // 1.菜单弹窗销毁
  window.addEventListener('click', function (evt) {
    const visibleDom = componentContainer.querySelector('.visible');
    if (!visibleDom || visibleDom.contains(evt.target)) return;
    visibleDom.classList.remove('visible');
  }, {
    capture: true
  });

  // 2. | 撤销 | 重做 | 格式刷 | 清除格式 |
  const undoDom = componentContainer.querySelector('.menu-item__undo');
  if (undoDom) {
    undoDom.title = `撤销(${isApple ? '⌘' : 'Ctrl'}+Z)`;
    undoDom.onclick = function () {
      console.log('undo');
      instance.command.executeUndo();
    };
  }

  const redoDom = componentContainer.querySelector('.menu-item__redo');
  if (redoDom) {
    redoDom.title = `重做(${isApple ? '⌘' : 'Ctrl'}+Y)`;
    redoDom.onclick = function () {
      console.log('redo');
      instance.command.executeRedo();
    };
  }

  const painterDom = componentContainer.querySelector('.menu-item__painter');

  if (painterDom) {
    let isFirstClick = true;
    let painterTimeout;
    painterDom.onclick = function () {
      if (isFirstClick) {
        isFirstClick = false;
        painterTimeout = window.setTimeout(() => {
          console.log('painter-click');
          isFirstClick = true;
          instance.command.executePainter({
            isDblclick: false
          });
        }, 200);
      } else {
        window.clearTimeout(painterTimeout);
      }
    };

    painterDom.ondblclick = function () {
      console.log('painter-dblclick');
      isFirstClick = true;
      window.clearTimeout(painterTimeout);
      instance.command.executePainter({
        isDblclick: true
      });
    };
  }

  const formatDom = componentContainer.querySelector('.menu-item__format');
  if (formatDom) {
    formatDom.onclick = function () {
      console.log('format');
      instance.command.executeFormat();
    };
  }

  //3. | 字体 | 字体变大 | 字体变小 | 加粗 | 斜体 | 下划线 | 删除线 | 上标 | 下标 | 字体颜色 | 背景色 |
  // 字体
  const fontDom = componentContainer.querySelector('.menu-item__font');
  const fontSelectDom = fontDom?.querySelector('.select');
  const fontOptionDom = fontDom?.querySelector('.options');
  if (fontDom && fontSelectDom && fontOptionDom) {
    fontDom.onclick = function () {
      console.log('font');
      fontOptionDom.classList.toggle('visible');
    };
    fontOptionDom.onclick = function (evt) {
      const li = evt.target;
      instance.command.executeFont(li.dataset.family);
    };
  }

  // 字号设置
  const sizeSetDom = componentContainer.querySelector('.menu-item__size');
  const sizeSelectDom = sizeSetDom?.querySelector('.select');
  const sizeOptionDom = sizeSetDom?.querySelector('.options');
  if (sizeSetDom && sizeSelectDom && sizeOptionDom) {
    sizeSetDom.title = `设置字号`;
    sizeSetDom.onclick = function () {
      console.log('size');
      sizeOptionDom.classList.toggle('visible');
    };
    sizeOptionDom.onclick = function (evt) {
      const li = evt.target;
      instance.command.executeSize(Number(li.dataset.size));
    };
  }

  // 增大字号
  const sizeAddDom = componentContainer.querySelector('.menu-item__size-add');
  if (sizeAddDom) {
    sizeAddDom.title = `增大字号(${isApple ? '⌘' : 'Ctrl'}+[)`;
    sizeAddDom.onclick = function () {
      console.log('size-add');
      instance.command.executeSizeAdd();
    };
  }

  // 减小字号
  const sizeMinusDom = componentContainer.querySelector('.menu-item__size-minus');
  if (sizeMinusDom) {
    sizeMinusDom.title = `减小字号(${isApple ? '⌘' : 'Ctrl'}+])`;
    sizeMinusDom.onclick = function () {
      console.log('size-minus');
      instance.command.executeSizeMinus();
    };
  }


  // 加粗
  const boldDom = componentContainer.querySelector('.menu-item__bold');
  if (boldDom) {
    boldDom.title = `加粗(${isApple ? '⌘' : 'Ctrl'}+B)`;
    boldDom.onclick = function () {
      console.log('bold');
      instance.command.executeBold();
    };
  }

  // 斜体
  const italicDom = componentContainer.querySelector('.menu-item__italic');
  if (italicDom) {
    italicDom.title = `斜体(${isApple ? '⌘' : 'Ctrl'}+I)`;
    italicDom.onclick = function () {
      console.log('italic');
      instance.command.executeItalic();
    };
  }

  // 下划线
  const underlineDom = componentContainer.querySelector('.menu-item__underline');
  underlineDom.title = `下划线(${isApple ? '⌘' : 'Ctrl'}+U)`;
  const underlineOptionDom = underlineDom.querySelector('.options');
  if (underlineDom && underlineOptionDom) {
    underlineDom.querySelector('.select').onclick = function () {
      
      underlineOptionDom.classList.toggle('visible');
    };
    underlineDom.querySelector('i').onclick = function () {
      console.log('underline');
      instance.command.executeUnderline();
      underlineOptionDom.classList.remove('visible');
    };
    underlineDom.querySelector('ul').onmousedown = function (evt) {
      const li = evt.target;
      const decorationStyle = li.dataset.decorationStyle;
      instance.command.executeUnderline({
        style: decorationStyle
      });
    };
  }
  

  // 删除线
  const strikeoutDom = componentContainer.querySelector('.menu-item__strikeout');
  if (strikeoutDom) {
    strikeoutDom.onclick = function () {
      console.log('strikeout');
      instance.command.executeStrikeout();
    };
  }

  // 上标
  const superscriptDom = componentContainer.querySelector('.menu-item__superscript');
  superscriptDom.title = `上标(${isApple ? '⌘' : 'Ctrl'}+Shift+,)`;
  if (superscriptDom) {
    superscriptDom.onclick = function () {
      console.log('superscript');
      instance.command.executeSuperscript();
      
    };
  }

  // 下标
  const subscriptDom = componentContainer.querySelector('.menu-item__subscript');
  subscriptDom.title = `下标(${isApple ? '⌘' : 'Ctrl'}+Shift+.)`;
  if (subscriptDom) {
    subscriptDom.onclick = function () {
      console.log('subscript');
      instance.command.executeSubscript();
    };
  }

  // 字体颜色
  const colorControlDom = componentContainer.querySelector('#color');
  const colorDom = componentContainer.querySelector('.menu-item__color');
  const colorSpanDom = colorDom?.querySelector('span');
  if (colorControlDom && colorDom) {
    colorControlDom.oninput = function () {
      instance.command.executeColor(colorControlDom.value);
    };
    colorDom.onclick = function () {
      console.log('color');
      colorControlDom.click();
    };
  }

  // 背景色
  const highlightControlDom = componentContainer.querySelector('#highlight');
  const highlightDom = componentContainer.querySelector('.menu-item__highlight');
  const highlightSpanDom = highlightDom?.querySelector('span');
  if (highlightControlDom && highlightDom) {
    highlightControlDom.oninput = function () {
      instance.command.executeHighlight(highlightControlDom.value);
    };
    highlightDom.onclick = function () {
      console.log('highlight');
      highlightControlDom?.click();
    };
  }

  // 填充颜色
  const backgroundControlDom = componentContainer.querySelector('#background');
  const backgroundDom = componentContainer.querySelector('.menu-item__background');
  backgroundDom.onclick = function () {
    console.log('background');
    backgroundControlDom?.click();
  };
  // backgroundDom.style.display = 'none'
  const backgroundSpanDom = backgroundDom.querySelector('span');
  backgroundControlDom.oninput = function () {
    instance.command.executeTableTdBackgroundColor(backgroundControlDom.value);
    backgroundSpanDom.style.backgroundColor = backgroundControlDom.value
  };

  // 标题设置
  const titleDom = componentContainer.querySelector('.menu-item__title');
  const titleSelectDom = titleDom.querySelector('.select');
  const titleOptionDom = titleDom.querySelector('.options');
  titleOptionDom.querySelectorAll('li').forEach((li, index) => {
    li.title = `Ctrl+${isApple ? 'Option' : 'Alt'}+${index}`;
  });
  titleDom.onclick = function () {
    console.log('title');
    titleOptionDom.classList.toggle('visible');
  };
  titleOptionDom.onclick = function (evt) {
    const li = evt.target;
    const level = li.dataset.level;
    instance.command.executeTitle(level || null);
  };

  // 文本对齐
  const leftDom = componentContainer.querySelector('.menu-item__left');
  leftDom.title = `左对齐(${isApple ? '⌘' : 'Ctrl'}+L)`;
  leftDom.onclick = function () {
    console.log('left');
    instance.command.executeRowFlex(RowFlex.LEFT);
  };

  const centerDom = componentContainer.querySelector('.menu-item__center');
  centerDom.title = `居中对齐(${isApple ? '⌘' : 'Ctrl'}+E)`;
  centerDom.onclick = function () {
    console.log('center');
    instance.command.executeRowFlex(RowFlex.CENTER);
  };

  const rightDom = componentContainer.querySelector('.menu-item__right');
  rightDom.title = `右对齐(${isApple ? '⌘' : 'Ctrl'}+R)`;
  rightDom.onclick = function () {
    console.log('right');
    instance.command.executeRowFlex(RowFlex.RIGHT);
  };

  const alignmentDom = componentContainer.querySelector('.menu-item__alignment');
  alignmentDom.title = `两端对齐(${isApple ? '⌘' : 'Ctrl'}+J)`;
  alignmentDom.onclick = function () {
    console.log('alignment');
    instance.command.executeRowFlex(RowFlex.ALIGNMENT);
  };

  const justifyDom = componentContainer.querySelector('.menu-item__justify');
  justifyDom.title = `分散对齐(${isApple ? '⌘' : 'Ctrl'}+Shift+J)`;
  justifyDom.onclick = function () {
    console.log('justify');
    instance.command.executeRowFlex('justify');
  };

  // 行间距
  const rowMarginDom = componentContainer.querySelector('.menu-item__row-margin');
  const rowOptionDom = rowMarginDom.querySelector('.options');
  rowMarginDom.onclick = function () {
    console.log('row-margin');
    rowOptionDom.classList.toggle('visible');
  };
  rowOptionDom.onclick = function (evt) {
    const li = evt.target;
    instance.command.executeRowMargin(Number(li.dataset.rowmargin));
  };

  // 列表
  const listDom = componentContainer.querySelector('.menu-item__list');
  listDom.title = `列表(${isApple ? '⌘' : 'Ctrl'}+Shift+U)`;
  const listOptionDom = listDom.querySelector('.options');
  listDom.onclick = function () {
    console.log('list');
    listOptionDom.classList.toggle('visible');
  };
  listOptionDom.onclick = function (evt) {
    const li = evt.target;
    const listType = li.dataset.listType || null;
    const listStyle = li.dataset.listStyle;
    instance.command.executeList(listType, listStyle);
  };

  // 4. | 表格 | 图片 | 超链接 | 分割线 | 水印 | 代码块 | 分隔符 | 控件 | 复选框 | LaTeX | 日期选择器
  const tableDom = componentContainer.querySelector('.menu-item__table');
  const tablePanelContainer = componentContainer.querySelector('.menu-item__table__collapse');
  const tableClose = componentContainer.querySelector('.table-close');
  const tableTitle = componentContainer.querySelector('.table-select');
  const tablePanel = componentContainer.querySelector('.table-panel');

  // Draw rows and columns
  const tableCellList = [];
  for (let i = 0; i < 10; i++) {
    const tr = document.createElement('tr');
    tr.classList.add('table-row');
    const trCellList = [];
    for (let j = 0; j < 10; j++) {
      const td = document.createElement('td');
      td.classList.add('table-cel');
      tr.appendChild(td);
      trCellList.push(td);
    }
    if(tablePanel)tablePanel.appendChild(tr);
    tableCellList.push(trCellList);
  }

  let colIndex = 0;
  let rowIndex = 0;

  // Remove all table cell selections
  function removeAllTableCellSelect() {
    tableCellList.forEach(tr => {
      tr.forEach(td => td.classList.remove('active'));
    });
  }

  // Set table title content
  function setTableTitle(payload) {
    tableTitle.innerText = payload;
  }

  // Restore initial state
  function recoveryTable() {
    removeAllTableCellSelect();
    setTableTitle('插入');
    colIndex = 0;
    rowIndex = 0;
    if(tablePanelContainer)tablePanelContainer.style.display = 'none';
  }
  if(tableDom){
    tableDom.onclick = function () {
      console.log('table');
      if(tablePanelContainer)tablePanelContainer.style.display = 'block';
    };
  }
  
  if(tablePanel){
    tablePanel.onmousemove = function (evt) {
      const celSize = 16;
      const rowMarginTop = 10;
      const celMarginRight = 6;
      const { offsetX, offsetY } = evt;
      removeAllTableCellSelect();
      colIndex = Math.ceil(offsetX / (celSize + celMarginRight)) || 1;
      rowIndex = Math.ceil(offsetY / (celSize + rowMarginTop)) || 1;
      tableCellList.forEach((tr, trIndex) => {
        tr.forEach((td, tdIndex) => {
          if (tdIndex < colIndex && trIndex < rowIndex) {
            td.classList.add('active');
          }
        });
      });
      setTableTitle(`${rowIndex}×${colIndex}`);
    };
  }
  
  if(tableClose){
    tableClose.onclick = function () {
      recoveryTable();
    };
  }

  function generateTableHtml(rowIndex, colIndex, options = {}) {
    // 参数验证
    if (!Number.isInteger(rowIndex) || rowIndex <= 0) {
        throw new Error('行数必须是正整数');
    }
    if (!Number.isInteger(colIndex) || colIndex <= 0) {
        throw new Error('列数必须是正整数');
    }

    // 合并默认样式和用户自定义样式
    const {
        tableClass = 'border-collapse w-full',
        headerClass = 'border border-gray-300 bg-gray-100 px-4 py-2 text-left',
        cellClass = 'border border-gray-300 px-4 py-2',
        oddRowClass = '',
        evenRowClass = 'bg-gray-50'
    } = options;

    // 构建表头
    let thead = '<thead><tr>';
    // 第一列作为行索引标题
    thead += `<th class="${headerClass}"></th>`;
    // 生成列标题（列1、列2...）
    for (let col = 1; col <= colIndex; col++) {
        thead += `<th class="${headerClass}"></th>`;
    }
    thead += '</tr></thead>';

    // 构建表格主体
    let tbody = '<tbody>';
    for (let row = 1; row <= rowIndex; row++) {
        // 为奇数行和偶数行添加不同样式
        const rowClass = row % 2 === 0 ? evenRowClass : oddRowClass;
        tbody += `<tr class="${rowClass}">`;
        // 第一列显示行索引（行1、行2...）
        tbody += `<td class="${headerClass} font-medium"></td>`;
        // 生成单元格内容（显示坐标如(1,1)）
        for (let col = 1; col <= colIndex; col++) {
            tbody += `<td class="${cellClass}"></td>`;
        }
        tbody += '</tr>';
    }
    tbody += '</tbody>';

    // 组合成完整表格
    return `<table class="${tableClass}">${thead}${tbody}</table>`;
}
  if(tablePanel){
    const options = instance.command.getOptions()
    const margins = options.margins
    const direction = options.paperDirection == 'horizontal' ? 1 : 0
    const innerWidth = (direction === 0 ? options.width : options.height) - margins[1] - margins[3]
    tablePanel.onclick = function () {
      let html = generateTableHtml(rowIndex,colIndex)
      // instance.command.executeInsertTable(rowIndex, colIndex);
      let element = getElementListByHTML(html,{innerWidth})
      debugger;
      let time = new Date().getTime()
      element[0].conceptId ="table"+time
      element[0].id ="table"+time
      instance.command.executeInsertElementList(element)
      recoveryTable();
    };
  }

  const imageDom = componentContainer.querySelector('.menu-item__image');
  const imageFileDom = componentContainer.querySelector('#image');
  if (imageDom && imageFileDom) {
    imageDom.onclick = function () {
      imageFileDom.click();
    };
    imageFileDom.onchange = function () {
    const file = imageFileDom.files[0];
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = function () {
      const image = new Image();
      const value = fileReader.result;
      image.src = value;
      image.onload = function () {
        // 处理图片宽高,宽不能超过800,高不能超过900,等比例改变图片大小
        const options = instance.command.getOptions()
        const margins = options.margins;
        const width = options.width - margins[0] - margins[2];
        const height = options.height - margins[1] - margins[3] - 100;
        let oldWidth = image.width;
        let oldHeight = image.height;
        if (oldWidth > width) {
          image.width = width;
          image.height = oldHeight * (image.width / oldWidth);
          oldWidth = image.width;
          oldHeight = image.height;
        }
        if (oldHeight > height) {
          image.height = height;
          image.width = oldWidth * (image.height / oldHeight);
        }
        instance.command.executeImage({
          value,
          width: image.width,
          height: image.height
        });
        imageFileDom.value = '';
      };
    };
  }
  }
  const imageVarDom = componentContainer.querySelector('.menu-item__imageVar');
  imageVarDom.onclick = function () {
    const allFields = props.allFields
    debugger;
    const selections = allFields.map((field) => {
      return {
        label: field.path,
        value: `#{${field.path}}`
      }
    })
    selections.unshift({
      label: 'pic',
      value: `#{pic}`
    })
    new Dialog({
      title: '图片占位符',
      data: [
        {
          type: 'text',
          label: '宽度',
          name: 'width',
          required: true,
          value: '',
          placeholder: '请输入宽度'
        },
        {
          type: 'text',
          label: '高度',
          name: 'height',
          required: true,
          value: '',
          placeholder: '请输入高度'
        },
        // {
        //   type: 'text',
        //   label: '占位符',
        //   name: 'value',
        //   required: true,
        //   value: '',
        //   placeholder: '请输入占位符'
        // }
        {
          type: 'select',
          label: '占位符',
          name: 'value',
          value: '',
          required: true,
          options:selections
        }
      ],
      onConfirm: payload => {
        const width = payload.find(p => p.name === 'width')?.value
        if (!width) return
        const height = payload.find(p => p.name === 'height')?.value
        if (!height) return
        const value = payload.find(p => p.name === 'value')?.value
        if (!value) return
        const default_pic = `data:image/jpeg;base64,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`;
        debugger;
        instance.command.executeImage({
          value:default_pic,
          width:width,
          height:height,
          conceptId:JSON.stringify({variable:value||'#{pic}'})
        })
      }
    })
    
  };
  

 const codeblockDom = componentContainer.querySelector(
    '.menu-item__codeblock'
  );
  codeblockDom.onclick = function () {
    new Dialog({
      title: '代码块',
      data: [{
        type: 'textarea',
        name: 'codeblock',
        placeholder: '请输入代码',
        width: 500,
        height: 300
      }],
      onConfirm: payload => {
        const codeblock = payload.find(p => p.name === 'codeblock')?.value
        if (!codeblock) return
        const tokenList = prism.tokenize(codeblock, prism.languages.javascript)
        const formatTokenList = formatPrismToken(tokenList)
        const elementList = [];
        for (let i = 0; i < formatTokenList.length; i++) {
          const formatToken = formatTokenList[i]
          const tokenStringList = splitText(formatToken.content)
          for (let j = 0; j < tokenStringList.length; j++) {
            const value = tokenStringList[j]
            const element = {
              value
            }
            if (formatToken.color) {
              element.color = formatToken.color
            }
            if (formatToken.bold) {
              element.bold = true
            }
            if (formatToken.italic) {
              element.italic = true
            }
            elementList.push(element)
          }
        }
        elementList.unshift({
          value: '\n'
        })
        instance.command.executeInsertElementList(elementList)
      }
    })
  }


  // 超链接
  const hyperlinkDom = componentContainer.querySelector(
    '.menu-item__hyperlink'
  )
  hyperlinkDom.onclick = function () {
    console.log('hyperlink')
    new Dialog({
      title: '超链接',
      data: [
        {
          type: 'text',
          label: '文本',
          name: 'name',
          required: true,
          placeholder: '请输入文本',
          value: instance.command.getRangeText()
        },
        {
          type: 'text',
          label: '链接',
          name: 'url',
          required: true,
          placeholder: '请输入链接'
        }
      ],
      onConfirm: payload => {
        const name = payload.find(p => p.name === 'name')?.value
        if (!name) return
        const url = payload.find(p => p.name === 'url')?.value
        if (!url) return
        const context = instance.command.getRangeContext()
        const endElement = context?.endElement || {
          size:16
        }
        instance.command.executeHyperlink({
          type: ElementType.HYPERLINK,
          value: '',
          url,
          valueList: splitText(name).map(n => ({
            value: n,
            size: endElement.size || 16,
            font: endElement.font,
            bold: endElement.bold,
            italic: endElement.italic,
            underline: endElement.underline,
            strikeout: endElement.strikeout
          }))
        })
      }
    })
  }

  

  const separatorDom = componentContainer.querySelector('.menu-item__separator');
  const separatorOptionDom = separatorDom.querySelector('.options');
  separatorDom.onclick = function () {
    console.log('separator');
    separatorOptionDom.classList.toggle('visible');
  };
  separatorOptionDom.onmousedown = function (evt) {
    let payload = [];
    const li = evt.target;
    const separatorDash = li.dataset.separator?.split(',').map(Number);
    if (separatorDash) {
      const isSingleLine = separatorDash.every(d => d === 0);
      if (!isSingleLine) {
        payload = separatorDash;
      }
    }
    instance.command.executeSeparator(payload);
  };

  const pageBreakDom = componentContainer.querySelector('.menu-item__page-break');
  pageBreakDom.onclick = function () {
    console.log('pageBreak');
    instance.command.executePageBreak();
  };

  // 水印
  // const watermarkDom = componentContainer.querySelector(
  //   '.menu-item__watermark'
  // );
  // const watermarkOptionDom =
  //   watermarkDom.querySelector('.options')
  // watermarkDom.onclick = function () {
  //   console.log('watermark')
  //   watermarkOptionDom.classList.toggle('visible')
  // }
  // watermarkOptionDom.onmousedown = function (evt) {
  //   const li = evt.target;
  //   const menu = li.dataset.menu || null;
  //   watermarkOptionDom.classList.toggle('visible')
  //   if (menu === 'add') {
  //     new Dialog({
  //       title: '水印',
  //       data: [
  //         {
  //           type: 'text',
  //           label: '内容',
  //           name: 'data',
  //           required: true,
  //           placeholder: '请输入内容'
  //         },
  //         {
  //           type: 'color',
  //           label: '颜色',
  //           name: 'color',
  //           required: true,
  //           value: '#AEB5C0'
  //         },
  //         {
  //           type: 'number',
  //           label: '字体大小',
  //           name: 'size',
  //           required: true,
  //           value: '120'
  //         },
  //         {
  //           type: 'number',
  //           label: '透明度',
  //           name: 'opacity',
  //           required: true,
  //           value: '0.3'
  //         },
  //         {
  //           type: 'select',
  //           label: '重复',
  //           name: 'repeat',
  //           value: '0',
  //           required: false,
  //           options: [
  //             {
  //               label: '不重复',
  //               value: '0'
  //             },
  //             {
  //               label: '重复',
  //               value: '1'
  //             }
  //           ]
  //         },
  //         {
  //           type: 'number',
  //           label: '水平间隔',
  //           name: 'horizontalGap',
  //           required: false,
  //           value: '10'
  //         },
  //         {
  //           type: 'number',
  //           label: '垂直间隔',
  //           name: 'verticalGap',
  //           required: false,
  //           value: '10'
  //         }
  //       ],
  //       onConfirm: payload => {
  //         const nullableIndex = payload.findIndex(p => !p.value)
  //         if (~nullableIndex) return
  //         const watermark = payload.reduce((pre, cur) => {
  //           pre[cur.name] = cur.value
  //           return pre
  //         }, {})
  //         const repeat = watermark.repeat === '1'
  //         instance.command.executeAddWatermark({
  //           data: watermark.data,
  //           color: watermark.color,
  //           size: Number(watermark.size),
  //           opacity: Number(watermark.opacity),
  //           repeat,
  //           gap:
  //             repeat && watermark.horizontalGap && watermark.verticalGap
  //               ? [
  //                   Number(watermark.horizontalGap),
  //                   Number(watermark.verticalGap)
  //                 ]
  //               : undefined
  //         })
  //       }
  //     })
  //   } else {
  //     instance.command.executeDeleteWatermark()
  //   }
  // }


  const controlDom = componentContainer.querySelector(
    '.menu-item__control'
  );
  const controlOptionDom = controlDom.querySelector('.options');
  controlDom.onclick = function () {
    console.log('control')
    controlOptionDom.classList.toggle('visible')
  }
  controlOptionDom.onmousedown = function (evt) {
    controlOptionDom.classList.toggle('visible')
    const li = evt.target;
    const type = li.dataset.control || null
    switch (type) {
      case 'text':
        new Dialog({
          title: '文本控件',
          data: [
            {
              type: 'text',
              label: '占位符',
              name: 'placeholder',
              required: true,
              placeholder: '请输入占位符'
            },
            {
              type: 'text',
              label: '默认值',
              name: 'value',
              placeholder: '请输入默认值'
            }
          ],
          onConfirm: payload => {
            const placeholder = payload.find(
              p => p.name === 'placeholder'
            )?.value
            if (!placeholder) return
            const value = payload.find(p => p.name === 'value')?.value || ''
            instance.command.executeInsertElementList([
              {
                type: ElementType.CONTROL,
                value: '',
                control: {
                  type,
                  value: value
                    ? [
                        {
                          value
                        }
                      ]
                    : null,
                  placeholder
                }
              }
            ])
          }
        })
        break
      case 'select':
        new Dialog({
          title: '列举控件',
          data: [
            {
              type: 'text',
              label: '占位符',
              name: 'placeholder',
              required: true,
              placeholder: '请输入占位符'
            },
            {
              type: 'text',
              label: '默认值',
              name: 'code',
              placeholder: '请输入默认值'
            },
            {
              type: 'textarea',
              label: '值集',
              name: 'valueSets',
              required: true,
              height: 100,
              placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
            }
          ],
          onConfirm: payload => {
            const placeholder = payload.find(
              p => p.name === 'placeholder'
            )?.value
            if (!placeholder) return
            const valueSets = payload.find(p => p.name === 'valueSets')?.value
            if (!valueSets) return
            const code = payload.find(p => p.name === 'code')?.value
            instance.command.executeInsertElementList([
              {
                type: ElementType.CONTROL,
                value: '',
                control: {
                  type,
                  code,
                  value: null,
                  placeholder,
                  valueSets: JSON.parse(valueSets)
                }
              }
            ])
          }
        })
        break
      case 'checkbox':
        new Dialog({
          title: '复选框控件',
          data: [
            {
              type: 'text',
              label: '默认值',
              name: 'code',
              placeholder: '请输入默认值，多个值以英文逗号分割'
            },
            {
              type: 'textarea',
              label: '值集',
              name: 'valueSets',
              required: true,
              height: 100,
              placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
            }
          ],
          onConfirm: payload => {
            const valueSets = payload.find(p => p.name === 'valueSets')?.value
            if (!valueSets) return
            const code = payload.find(p => p.name === 'code')?.value
            instance.command.executeInsertElementList([
              {
                type: ElementType.CONTROL,
                value: '',
                control: {
                  type,
                  code,
                  value: null,
                  valueSets: JSON.parse(valueSets)
                }
              }
            ])
          }
        })
        break
      case 'radio':
        new Dialog({
          title: '单选框控件',
          data: [
            {
              type: 'text',
              label: '默认值',
              name: 'code',
              placeholder: '请输入默认值'
            },
            {
              type: 'textarea',
              label: '值集',
              name: 'valueSets',
              required: true,
              height: 100,
              placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
            }
          ],
          onConfirm: payload => {
            const valueSets = payload.find(p => p.name === 'valueSets')?.value
            if (!valueSets) return
            const code = payload.find(p => p.name === 'code')?.value
            instance.command.executeInsertElementList([
              {
                type: ElementType.CONTROL,
                value: '',
                control: {
                  type,
                  code,
                  value: null,
                  valueSets: JSON.parse(valueSets)
                }
              }
            ])
          }
        })
        break
      case 'date':
        new Dialog({
          title: '日期控件',
          data: [
            {
              type: 'text',
              label: '占位符',
              name: 'placeholder',
              required: true,
              placeholder: '请输入占位符'
            },
            {
              type: 'text',
              label: '默认值',
              name: 'value',
              placeholder: '请输入默认值'
            },
            {
              type: 'text',
              label: '默认值',
              name: 'value',
              placeholder: '请输入默认值'
            },
            {
              type: 'select',
              label: '日期格式',
              name: 'dateFormat',
              value: 'yyyy-MM-dd hh:mm:ss',
              required: true,
              options: [
                {
                  label: 'yyyy-MM-dd hh:mm:ss',
                  value: 'yyyy-MM-dd hh:mm:ss'
                },
                {
                  label: 'yyyy-MM-dd',
                  value: 'yyyy-MM-dd'
                }
              ]
            }
          ],
          onConfirm: payload => {
            const placeholder = payload.find(
              p => p.name === 'placeholder'
            )?.value
            if (!placeholder) return
            const value = payload.find(p => p.name === 'value')?.value || ''
            const dateFormat =
              payload.find(p => p.name === 'dateFormat')?.value || ''
            instance.command.executeInsertElementList([
              {
                type: ElementType.CONTROL,
                value: '',
                control: {
                  type,
                  dateFormat,
                  value: value
                    ? [
                        {
                          value
                        }
                      ]
                    : null,
                  placeholder
                }
              }
            ])
          }
        })
        break
      default:
        break
    }
  }

  const checkboxDom = componentContainer.querySelector('.menu-item__checkbox');
  checkboxDom.onclick = function () {
    console.log('checkbox');
    instance.command.executeInsertElementList([
      {
        type: ElementType.CHECKBOX,
        checkbox: {
          value: false
        },
        value: ''
      }
    ]);
  };
  
  const chartDom = componentContainer.querySelector('.menu-item__chart');
  chartDom.onclick = function () {
    const allFields = props.allFields
    const selections = allFields.map((field) => {
      return {
        label: field.path,
        value: `#{${field.path}}`
      }
    })
    selections.unshift({
      label: 'chart',
        value: `#{chart}`
    })
    let tableHtml = `<table>
    <thead>
        <tr>
            <th>产品名称</th>
            <th>类别</th>
            <th>价格（元）</th>
            <th>库存数量</th>
            <th>月销量</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>无线耳机</td>
            <td>数码产品</td>
            <td>299</td>
            <td>156</td>
            <td>89</td>
        </tr>
        <tr>
            <td>机械键盘</td>
            <td>电脑配件</td>
            <td>499</td>
            <td>78</td>
            <td>45</td>
        </tr>
        <tr>
            <td>智能手表</td>
            <td>智能设备</td>
            <td>1299</td>
            <td>45</td>
            <td>23</td>
        </tr>
        <tr>
            <td>蓝牙音箱</td>
            <td>音频设备</td>
            <td>399</td>
            <td>102</td>
            <td>56</td>
        </tr>
        <tr>
            <td>游戏手柄</td>
            <td>游戏配件</td>
            <td>199</td>
            <td>63</td>
            <td>32</td>
        </tr>
    </tbody>
</table>`
//     debugger;
//     const options = instance.command.getOptions()
//     const margins = options.margins
//     const direction = options.paperDirection == 'horizontal' ? 1 : 0
  
//     const innerWidth = (direction === 0 ? options.width : options.height) - margins[1] - margins[3]
//     instance.command.executeInsertElementList(convertHtml(tableHtml,{
//       innerWidth,
//     }))
 instance.command.executeSetHTML(tableHtml)
    console.log('chart');
    new Dialog({
      title: '图表',
      data: [
        {
          type: 'text',
          label: '宽度',
          name: 'width',
          required: true,
          value: '',
          placeholder: '请输入宽度'
        },
        {
          type: 'text',
          label: '高度',
          name: 'height',
          required: true,
          value: '',
          placeholder: '请输入高度'
        },
        // {
        //   type: 'text',
        //   label: '占位符',
        //   name: 'value',
        //   required: true,
        //   value: '',
        //   placeholder: '请输入占位符'
        // }
        {
          type: 'select',
          label: '占位符',
          name: 'value',
          value: '',
          required: true,
          options:selections
        }
      ],
      onConfirm: payload => {
        const width = payload.find(p => p.name === 'width')?.value
        if (!width) return
        const height = payload.find(p => p.name === 'height')?.value
        if (!height) return
        const value = payload.find(p => p.name === 'value')?.value
        if (!value) return
        const elementList = []
        elementList.push({
            type: 'chart',
            width:width,
            height:height,
            value: value||'#{chart}'
          })
        instance.command.executeInsertElementList(elementList)
      }
    })
    
  };


  const radioDom = componentContainer.querySelector('.menu-item__radio');
  radioDom.onclick = function () {
    console.log('radio');
    instance.command.executeInsertElementList([
      {
        type: ElementType.RADIO,
        checkbox: {
          value: false
        },
        value: ''
      }
    ]);
  };

  // const latexDom = componentContainer.querySelector('.menu-item__latex');
  // latexDomlatexDom.onclick = function () {
  //   console.log('LaTeX')
  //   new Dialog({
  //     title: 'LaTeX',
  //     data: [
  //       {
  //         type: 'textarea',
  //         height: 100,
  //         name: 'value',
  //         placeholder: '请输入LaTeX文本'
  //       }
  //     ],
  //     onConfirm: payload => {
  //       const value = payload.find(p => p.name === 'value')?.value
  //       if (!value) return
  //       instance.command.executeInsertElementList([
  //         {
  //           type: ElementType.LATEX,
  //           value
  //         }
  //       ])
  //     }
  //   })
  // }

  const dateDom = componentContainer.querySelector('.menu-item__date');
  const dateDomOptionDom = dateDom.querySelector('.options');
  dateDom.onclick = function () {
    console.log('date');
    dateDomOptionDom.classList.toggle('visible');
    // Adjust position
    const bodyRect = document.body.getBoundingClientRect();
    const dateDomOptionRect = dateDomOptionDom.getBoundingClientRect();
    if (dateDomOptionRect.left + dateDomOptionRect.width > bodyRect.width) {
      dateDomOptionDom.style.right = '0px';
      dateDomOptionDom.style.left = 'unset';
    } else {
      dateDomOptionDom.style.right = 'unset';
      dateDomOptionDom.style.left = '0px';
    }
    // Current date
    const date = new Date();
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    const dateTimeString = `${dateString} ${hour}:${minute}:${second}`;
    dateDomOptionDom.querySelector('li:first-child').innerText = dateString;
    dateDomOptionDom.querySelector('li:last-child').innerText = dateTimeString;
  };
  dateDomOptionDom.onmousedown = function (evt) {
    const li = evt.target;
    const dateFormat = li.dataset.format;
    dateDomOptionDom.classList.toggle('visible');
    instance.command.executeInsertElementList([
      {
        type: ElementType.DATE,
        value: '',
        dateFormat,
        valueList: [
          {
            value: li.innerText.trim()
          }
        ]
      }
    ]);
  };

  // 5. | 搜索&替换 | 打印 |
  const searchCollapseDom = componentContainer.querySelector('.menu-item__search__collapse');
  const searchInputDom = componentContainer.querySelector('.menu-item__search__collapse__search input');
  const replaceInputDom = componentContainer.querySelector('.menu-item__search__collapse__replace input');
  const searchDom = componentContainer.querySelector('.menu-item__search');
  searchDom.title = `搜索与替换(${isApple ? '⌘' : 'Ctrl'}+F)`;
  const searchResultDom = searchCollapseDom.querySelector('.search-result');

  function setSearchResult() {
    const result = instance.command.getSearchNavigateInfo();
    if (result) {
      const { index, count } = result;
      searchResultDom.innerText = `${index}/${count}`;
      if (index == 0) {
        instance.command.executeSearchNavigateNext();
        setSearchResult();
      }
    } else {
      searchResultDom.innerText = '';
    }
  }

  searchDom.onclick = function () {
    console.log('search');
    searchCollapseDom.style.display = 'block';
    const bodyRect = document.body.getBoundingClientRect();
    const searchRect = searchDom.getBoundingClientRect();
    const searchCollapseRect = searchCollapseDom.getBoundingClientRect();
    if (searchRect.left + searchCollapseRect.width > bodyRect.width) {
      searchCollapseDom.style.right = '0px';
      searchCollapseDom.style.left = 'unset';
    } else {
      searchCollapseDom.style.right = 'unset';
    }
    searchInputDom.focus();
  }

  searchCollapseDom.querySelector('span').onclick = function () {
    searchCollapseDom.style.display = 'none';
    searchInputDom.value = '';
    replaceInputDom.value = '';
    instance.command.executeSearch(null);
    setSearchResult();
  }

  searchInputDom.oninput = function () {
    instance.command.executeSearch(searchInputDom.value || null);
    setSearchResult();
  }

  searchInputDom.onkeydown = function (evt) {
    if (evt.key === 'Enter') {
      instance.command.executeSearch(searchInputDom.value || null);
      setSearchResult();
    }
  }

  searchCollapseDom.querySelector('button').onclick = function () {
    const searchValue = searchInputDom.value;
    const replaceValue = replaceInputDom.value;
    if (searchValue && replaceValue && searchValue !== replaceValue) {
      instance.command.executeReplace(replaceValue);
    }
  }

  searchCollapseDom.querySelector('.arrow-left').onclick = function () {
    instance.command.executeSearchNavigatePre();
    setSearchResult();
  }

  searchCollapseDom.querySelector('.arrow-right').onclick = function () {
    instance.command.executeSearchNavigateNext();
    setSearchResult();
  }

  const printDom = componentContainer.querySelector('.menu-item__print');
  printDom.title = `打印(${isApple ? '⌘' : 'Ctrl'}+P)`;
  printDom.onclick = function () {
    console.log('print');
    instance.command.executePrint();
  }


  // 6. 目录显隐 | 页面模式 | 纸张缩放 | 纸张大小 | 纸张方向 | 页边距 | 全屏 | 设置
  const editorOptionDom =
    componentContainer.querySelector('.editor-option')
  editorOptionDom.onclick = function () {
    const options = instance.command.getOptions()
    new Dialog({
      title: '编辑器配置',
      data: [
        {
          type: 'textarea',
          name: 'option',
          width: 350,
          height: 300,
          required: true,
          value: JSON.stringify(options, null, 2),
          placeholder: '请输入编辑器配置'
        }
      ],
      onConfirm: payload => {
        const newOptionValue = payload.find(p => p.name === 'option')?.value
        if (!newOptionValue) return
        const newOption = JSON.parse(newOptionValue)
        instance.command.executeUpdateOptions(newOption)
      }
    })
  }

  async function updateCatalog() {
    const catalog = await instance.command.getCatalog();
    const catalogMainDom = componentContainer.querySelector('.catalog__main');
    if (!catalogMainDom) return
    catalogMainDom.innerHTML = '';
    if (catalog) {
      const appendCatalog = (parent, catalogItems) => {
        for (let c = 0; c < catalogItems.length; c++) {
          const catalogItem = catalogItems[c];
          const catalogItemDom = document.createElement('div');
          catalogItemDom.classList.add('catalog-item');

          // Render
          const catalogItemContentDom = document.createElement('div');
          catalogItemContentDom.classList.add('catalog-item__content');
          const catalogItemContentSpanDom = document.createElement('span');
          catalogItemContentSpanDom.innerText = catalogItem.name;
          catalogItemContentDom.append(catalogItemContentSpanDom);

          // Location
          catalogItemContentDom.onclick = () => {
            instance.command.executeLocationCatalog(catalogItem.id);
          };
          catalogItemDom.append(catalogItemContentDom);

          if (catalogItem.subCatalog && catalogItem.subCatalog.length) {
            appendCatalog(catalogItemDom, catalogItem.subCatalog);
          }

          // Append
          parent.append(catalogItemDom);
        }
      };
      appendCatalog(catalogMainDom, catalog);
    }
  }

  let isCatalogShow = true;
  const catalogDom = componentContainer.querySelector('.catalog');
  const catalogModeDom = componentContainer.querySelector('.catalog-mode');
  const catalogHeaderCloseDom = componentContainer.querySelector('.catalog__header__close');
  const switchCatalog = () => {
    isCatalogShow = !isCatalogShow;
    if (isCatalogShow) {
      catalogDom.style.display = 'block';
      updateCatalog();
    } else {
      catalogDom.style.display = 'none';
    }
  };
  catalogModeDom.onclick = switchCatalog;
  catalogHeaderCloseDom.onclick = switchCatalog;

  const pageModeDom = componentContainer.querySelector('.page-mode');
  const pageModeOptionsDom = pageModeDom?.querySelector('.options');
  if (pageModeDom && pageModeOptionsDom) {
    pageModeDom.onclick = function () {
      pageModeOptionsDom.classList.toggle('visible');
    };
    pageModeOptionsDom.onclick = function (evt) {
      const li = evt.target;
      instance.command.executePageMode(li.dataset.pageMode);
    };
  }

  const pageScalePercentageDom = componentContainer.querySelector('.page-scale-percentage');
  if (pageScalePercentageDom) {
    pageScalePercentageDom.onclick = function () {
      console.log('page-scale-recovery');
      instance.command.executePageScaleRecovery();
    };
  }

  const pageScaleMinusDom = componentContainer.querySelector('.page-scale-minus');
  if (pageScaleMinusDom) {
    pageScaleMinusDom.onclick = function () {
      console.log('page-scale-minus');
      instance.command.executePageScaleMinus();
    };
  }

  const pageScaleAddDom = componentContainer.querySelector('.page-scale-add');
  if (pageScaleAddDom) {
    pageScaleAddDom.onclick = function () {
      console.log('page-scale-add');
      instance.command.executePageScaleAdd();
    };
  }

  // Paper Size
  const paperSizeDom = componentContainer.querySelector('.paper-size');
  const paperSizeDomOptionsDom = paperSizeDom.querySelector('.options');
  paperSizeDom.onclick = function () {
    paperSizeDomOptionsDom.classList.toggle('visible');
  };
  paperSizeDomOptionsDom.onclick = function (evt) {
    const li = evt.target;
    const paperType = li.dataset.paperSize;
    const [width, height] = paperType.split('*').map(Number);
    instance.command.executePaperSize(width, height);

    // Paper status echo
    paperSizeDomOptionsDom.querySelectorAll('li').forEach(child => child.classList.remove('active'));
    li.classList.add('active');
  };

  // 页面边距
  const paperMarginDom =
    componentContainer.querySelector('.paper-margin')
  paperMarginDom.onclick = function () {
    const [topMargin, rightMargin, bottomMargin, leftMargin] =
      instance.command.getPaperMargin()
    new Dialog({
      title: '页边距',
      data: [
        {
          type: 'text',
          label: '上边距',
          name: 'top',
          required: true,
          value: `${topMargin}`,
          placeholder: '请输入上边距'
        },
        {
          type: 'text',
          label: '下边距',
          name: 'bottom',
          required: true,
          value: `${bottomMargin}`,
          placeholder: '请输入下边距'
        },
        {
          type: 'text',
          label: '左边距',
          name: 'left',
          required: true,
          value: `${leftMargin}`,
          placeholder: '请输入左边距'
        },
        {
          type: 'text',
          label: '右边距',
          name: 'right',
          required: true,
          value: `${rightMargin}`,
          placeholder: '请输入右边距'
        }
      ],
      onConfirm: payload => {
        const top = payload.find(p => p.name === 'top')?.value
        if (!top) return
        const bottom = payload.find(p => p.name === 'bottom')?.value
        if (!bottom) return
        const left = payload.find(p => p.name === 'left')?.value
        if (!left) return
        const right = payload.find(p => p.name === 'right')?.value
        if (!right) return
        instance.command.executeSetPaperMargin([
          Number(top),
          Number(right),
          Number(bottom),
          Number(left)
        ])
      }
    })
  }

  // 纸张方向
  const paperDirectionDom = componentContainer.querySelector('.paper-direction');
  const paperDirectionDomOptionsDom = paperDirectionDom.querySelector('.options');
  paperDirectionDom.onclick = function () {
    paperDirectionDomOptionsDom.classList.toggle('visible');
  };
  paperDirectionDomOptionsDom.onclick = function (evt) {
    const li = evt.target;
    const paperDirection = li.dataset.paperDirection;
    instance.command.executePaperDirection(paperDirection);
    // 纸张方向状态回显
    paperDirectionDomOptionsDom.querySelectorAll('li').forEach(child => child.classList.remove('active'));
    li.classList.add('active');
  };


  // 全屏
  const fullscreenDom = componentContainer.querySelector('.fullscreen');
  fullscreenDom.onclick = toggleFullscreen;
  window.addEventListener('keydown', evt => {
    if (evt.key === 'F11') {
      toggleFullscreen();
      evt.preventDefault();
    }
  });
  document.addEventListener('fullscreenchange', () => {
    fullscreenDom.classList.toggle('exist');
  });

  function toggleFullscreen() {
    console.log('fullscreen');
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  // 7.编辑器使用模式
  const modeList = [
    {
      mode: 'edit', // EditorMode.EDIT
      name: '编辑模式'
    },
    // {
    //   mode: 'clean', // EditorMode.CLEAN
    //   name: '清洁模式'
    // },
    {
      mode: 'readonly', // EditorMode.READONLY
      name: '只读模式'
    },
    // {
    //   mode: 'form', // EditorMode.FORM
    //   name: '表单模式'
    // },
    // {
    //   mode: 'print', // EditorMode.PRINT
    //   name: '打印模式'
    // }
  ];
  // 如果传入默认模式
  if (IElement.mode) modeIndex = modeList.findIndex(it => it.mode == IElement.mode)
  const modeElement = componentContainer.querySelector('.editor-mode');
  if (modeElement) {
    modeElement.onclick = function () {
      // 模式选择循环
      modeIndex === modeList.length - 1 ? (modeIndex = 0) : modeIndex++;
      // 设置模式
      const { name, mode } = modeList[modeIndex];
      modeElement.innerText = name;
      instance.command.executeMode(mode);
      // 设置菜单栏权限视觉反馈
      const isReadonly = mode === 'readonly';
      const enableMenuList = ['search', 'print', 'import'];
      componentContainer.querySelectorAll('.menu-item>div').forEach(dom => {
        const menu = dom.dataset.menu;
        isReadonly && (!menu || !enableMenuList.includes(menu))
          ? dom.classList.add('disable')
          : dom.classList.remove('disable');
      });
    };
  }

  // 模拟批注
  const commentDom = componentContainer.querySelector('.comment')
  async function updateComment() {
    if (!commentDom) return;
    const groupIds = await instance.command.getGroupIds()
    let list = commentList&&commentList.length?commentList:instance.commentList||[];
    for (const comment of commentList){
      const activeCommentDom = commentDom.querySelector(
        `.comment-item[data-id='${comment.id}']`
      )
      // 编辑器是否存在对应成组id
      if (groupIds.includes(comment.id)) {
        // 当前dom是否存在-不存在则追加
        if (!activeCommentDom) {
          const commentItem = document.createElement('div')
          commentItem.classList.add('comment-item')
          commentItem.setAttribute('data-id', comment.id)
          commentItem.onclick = () => {
            instance.command.executeLocationGroup(comment.id)
          }
          commentDom.append(commentItem)
          // 选区信息
          const commentItemTitle = document.createElement('div')
          commentItemTitle.classList.add('comment-item__title')
          commentItemTitle.append(document.createElement('span'))
          const commentItemTitleContent = document.createElement('span')
          commentItemTitleContent.innerText = comment.rangeText
          commentItemTitle.append(commentItemTitleContent)
          const closeDom = document.createElement('i')
          closeDom.onclick = () => {
            instance.command.executeDeleteGroup(comment.id)
          }
          commentItemTitle.append(closeDom)
          commentItem.append(commentItemTitle)
          // 基础信息
          const commentItemInfo = document.createElement('div')
          commentItemInfo.classList.add('comment-item__info')
          const commentItemInfoName = document.createElement('span')
          commentItemInfoName.innerText = comment.userName
          const commentItemInfoDate = document.createElement('span')
          commentItemInfoDate.innerText = comment.createdDate
          commentItemInfo.append(commentItemInfoName)
          commentItemInfo.append(commentItemInfoDate)
          commentItem.append(commentItemInfo)
          // 详细评论
          const commentItemContent = document.createElement('div')
          commentItemContent.classList.add('comment-item__content')
          commentItemContent.innerText = comment.content
          commentItem.append(commentItemContent)
          commentDom.append(commentItem)
        }
      } else {
        // 编辑器内不存在对应成组id则dom则移除
        activeCommentDom?.remove()
      }
    }
  }

  // 8.内部事件监听
  instance.listener.rangeStyleChange = function (payload) {
    // 控件类型
    payload.type === 'subscript'
      ? subscriptDom.classList.add('active')
      : subscriptDom.classList.remove('active');
    payload.type === 'superscript'
      ? superscriptDom.classList.add('active')
      : superscriptDom.classList.remove('active');
    payload.type === 'separator'
      ? separatorDom.classList.add('active')
      : separatorDom.classList.remove('active');

    separatorOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    if (payload.type === 'separator') {
      const separator = payload.dashArray.join(',') || '0,0';
      const curSeparatorDom = separatorOptionDom.querySelector(`[data-separator='${separator}']`);
      if (curSeparatorDom) {
        curSeparatorDom.classList.add('active');
      }
    }

    // 富文本
    fontOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    const curFontDom = fontOptionDom.querySelector(`[data-family='${payload.font}']`);
    if (curFontDom) {
      fontSelectDom.innerText = curFontDom.innerText;
      fontSelectDom.style.fontFamily = payload.font;
      curFontDom.classList.add('active');
    }

    sizeOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    const curSizeDom = sizeOptionDom.querySelector(`[data-size='${payload.size}']`);
    if (curSizeDom) {
      sizeSelectDom.innerText = curSizeDom.innerText;
      curSizeDom.classList.add('active');
    } else {
      sizeSelectDom.innerText = `${payload.size}`;
    }

    payload.bold
      ? boldDom.classList.add('active')
      : boldDom.classList.remove('active');
    payload.italic
      ? italicDom.classList.add('active')
      : italicDom.classList.remove('active');
    payload.underline
      ? underlineDom.classList.add('active')
      : underlineDom.classList.remove('active');
    payload.strikeout
      ? strikeoutDom.classList.add('active')
      : strikeoutDom.classList.remove('active');

    if (payload.color) {
      colorDom.classList.add('active');
      colorControlDom.value = payload.color;
      colorSpanDom.style.backgroundColor = payload.color;
    } else {
      colorDom.classList.remove('active');
      colorControlDom.value = '#000000';
      colorSpanDom.style.backgroundColor = '#000000';
    }

    if (payload.highlight) {
      highlightDom.classList.add('active');
      highlightControlDom.value = payload.highlight;
      highlightSpanDom.style.backgroundColor = payload.highlight;
    } else {
      highlightDom.classList.remove('active');
      highlightControlDom.value = '#ffff00';
      highlightSpanDom.style.backgroundColor = '#ffff00';
    }

    // 行布局
    leftDom.classList.remove('active');
    centerDom.classList.remove('active');
    rightDom.classList.remove('active');
    alignmentDom.classList.remove('active');
    justifyDom.classList.remove('active');

    if (payload.rowFlex && payload.rowFlex === 'right') {
      rightDom.classList.add('active');
    } else if (payload.rowFlex && payload.rowFlex === 'center') {
      centerDom.classList.add('active');
    } else if (payload.rowFlex && payload.rowFlex === 'alignment') {
      alignmentDom.classList.add('active');
    } else if (payload.rowFlex && payload.rowFlex === 'justify') {
      justifyDom.classList.add('active');
    } else {
      leftDom.classList.add('active');
    }

    // 行间距
    rowOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    const curRowMarginDom = rowOptionDom.querySelector(`[data-rowmargin='${payload.rowMargin}']`);
    curRowMarginDom.classList.add('active');

    // 功能
    payload.undo
      ? undoDom.classList.remove('no-allow')
      : undoDom.classList.add('no-allow');
    payload.redo
      ? redoDom.classList.remove('no-allow')
      : redoDom.classList.add('no-allow');
    payload.painter
      ? painterDom.classList.add('active')
      : painterDom.classList.remove('active');

    // 标题
    titleOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    if (payload.level) {
      const curTitleDom = titleOptionDom.querySelector(`[data-level='${payload.level}']`);
      titleSelectDom.innerText = curTitleDom.innerText;
      curTitleDom.classList.add('active');
    } else {
      titleSelectDom.innerText = '正文';
      titleOptionDom.querySelector('li:first-child').classList.add('active');
    }

    // 列表
    listOptionDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    if (payload.listType) {
      listDom.classList.add('active');
      const listType = payload.listType === 'OL' ? 'DECIMAL' : payload.listType;
      const curListDom = listOptionDom.querySelector(`[data-list-type='${listType}'][data-list-style='${listType}']`);
      if (curListDom) {
        curListDom.classList.add('active');
      }
    } else {
      listDom.classList.remove('active');
    }

    // 批注
    if (commentDom) {
      commentDom
        .querySelectorAll('.comment-item')
        .forEach(commentItemDom => {
          commentItemDom.classList.remove('active')
        })
      if (payload.groupIds) {
        const [id] = payload.groupIds
        const activeCommentDom = commentDom.querySelector(
          `.comment-item[data-id='${id}']`
        )
        if (activeCommentDom) {
          activeCommentDom.classList.add('active')
          // scrollIntoView(commentDom, activeCommentDom)
        }
      }
    }

  }

  // 可见页码
  instance.listener.visiblePageNoListChange = function (payload) {
    const text = payload.map(i => i + 1).join('、')
    const dom = componentContainer.querySelector('.page-no-list')
    if (dom) dom.innerText = text
  }

  // word总页数变更
  instance.listener.pageSizeChange = function (payload) {
    const dom = componentContainer.querySelector('.page-size')
    if (dom) dom.innerText = `${payload}`
  }

  // 页码
  instance.listener.intersectionPageNoChange = function (payload) {
    const dom = componentContainer.querySelector('.page-no')
    if (dom) dom.innerText = `${
      payload + 1
    }`
  }

  // 放大缩小百分比数字变更
  instance.listener.pageScaleChange = function (payload) {
    let text = componentContainer.querySelector(
      '.page-scale-percentage'
    )
    if (text) text.innerText = `${Math.floor(payload * 10 * 10)}%`
  }

  // 控件变更监听
  instance.listener.controlChange = function (payload) {
    const disableMenusInControlContext = [
      'table',
      'hyperlink',
      'separator',
      'page-break',
      'control'
    ];
    // 菜单操作权限
    disableMenusInControlContext.forEach(menu => {
      const menuDom = componentContainer.querySelector(`.menu-item__${menu}`);
      if (menuDom) {
        payload
          ? menuDom.classList.add('disable')
          : menuDom.classList.remove('disable');
      }
    });
  };

  // 页面模式变更监听
  instance.listener.pageModeChange = function (payload) {
    const activeMode = pageModeOptionsDom.querySelector(`[data-page-mode='${payload}']`);
    if (activeMode) {
      pageModeOptionsDom.querySelectorAll('li').forEach(li => li.classList.remove('active'));
      activeMode.classList.add('active');
    }
  };

  // 内容变更处理函数
  const handleContentChange = async function () {
    // 字数
    const wordCount = await instance.command.getWordCount();
    const wordCountDom = componentContainer.querySelector('.word-count');
    if (wordCountDom) {
      wordCountDom.innerText = `${wordCount || 0}`;
    }

    // 目录
    if (isCatalogShow) {
      nextTick(() => {
        updateCatalog();
      });
    }

    // 批注
    nextTick(() => {
      updateComment();
    });
  };

  // 内容变更监听，使用防抖函数
  instance.listener.contentChange = debounce(handleContentChange, 200);
  handleContentChange();

  // 保存监听
  instance.listener.saved = function (payload) {
    console.log('elementList: ', payload);
  };

  // 右键菜单注册
  instance.register.contextMenuList([
    {
      name: '批注',
      when: payload => {
        return (
          !payload.isReadonly &&
          payload.editorHasSelection &&
          payload.zone === 'main'
        )
      },
      callback: (command) => {
        new Dialog({
          title: '批注',
          data: [
            {
              type: 'textarea',
              label: '批注',
              height: 100,
              name: 'value',
              required: true,
              placeholder: '请输入批注'
            }
          ],
          onConfirm: payload => {
            const value = payload.find(p => p.name === 'value')?.value
            if (!value) return
            const groupId = command.executeSetGroup()
            if (!groupId) return
            if (commentList) commentList.push({
              id: groupId,
              content: value,
              userName: 'Hufe',
              rangeText: command.getRangeText(),
              createdDate: new Date().toLocaleString()
            })
          }
        })
      }
    },
    {
      name: '签名',
      icon: 'signature',
      when: payload => {
        return !payload.isReadonly && payload.editorTextFocus
      },
      callback: (command) => {
        new Signature({
          onConfirm(payload) {
            if (!payload) return
            const { value, width, height } = payload
            if (!value || !width || !height) return
            command.executeInsertElementList([
              {
                value,
                width,
                height,
                type: ElementType.IMAGE
              }
            ])
          }
        })
      }
    },
    {
      name: '格式整理',
      icon: 'word-tool',
      when: payload => {
        // return !payload.isReadonly
        return false
      },
      callback: (command) => {
        command.executeWordTool()
      }
    },
    // {
    //   name: '百度搜索：%s',
    //   when: payload => {
    //     return payload.editorHasSelection
    //   },
    //   callback: (command) => {
    //     const text = command.getRangeText()
    //     if (text) {
    //       window.open(`https://www.baidu.com/s?ie=UTF-8&wd=${text}`, '_blank')
    //     }
    //   }
    // }
  ])

  // 快捷键注册
  instance.register.shortcutList([
    {
      key: 'P',
      mod: true,
      isGlobal: true,
      callback: (command) => {
        command.executePrint();
      }
    },
    {
      key: 'F',
      mod: true,
      isGlobal: true,
      callback: (command) => {
        const text = command.getRangeText();
        searchDom.click();
        if (text) {
          searchInputDom.value = text;
          instance.command.executeSearch(text);
          setSearchResult();
        }
      }
    },
    {
      key: 'Minus',
      ctrl: true,
      isGlobal: true,
      callback: (command) => {
        command.executePageScaleMinus();
      }
    },
    {
      key: 'Equal',
      ctrl: true,
      isGlobal: true,
      callback: (command) => {
        command.executePageScaleAdd();
      }
    },
    {
      key: 'Zero',
      ctrl: true,
      isGlobal: true,
      callback: (command) => {
        command.executePageScaleRecovery();
      }
    }
  ]);

  return instance;
}


// 修改变量
export function changeModeIndex(value) {
  modeIndex = value || 0;
}

// 将html转化为ElementList
export function convertHtml(html, options) {
  return getElementListByHTML(html, options);
}

// splitText
export function splitTextFnc(text) {
  return splitText(text);
}