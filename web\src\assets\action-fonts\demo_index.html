<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4726857" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf7;</span>
                <div class="name">云端下载</div>
                <div class="code-name">&amp;#xeaf7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf4;</span>
                <div class="name">数据运行</div>
                <div class="code-name">&amp;#xeaf4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf5;</span>
                <div class="name">节点流程</div>
                <div class="code-name">&amp;#xeaf5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf6;</span>
                <div class="name">数据指标</div>
                <div class="code-name">&amp;#xeaf6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf2;</span>
                <div class="name">置信区间</div>
                <div class="code-name">&amp;#xeaf2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf3;</span>
                <div class="name">数据清洗</div>
                <div class="code-name">&amp;#xeaf3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf0;</span>
                <div class="name">变量管理</div>
                <div class="code-name">&amp;#xeaf0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaf1;</span>
                <div class="name">模版</div>
                <div class="code-name">&amp;#xeaf1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaee;</span>
                <div class="name">全部导出</div>
                <div class="code-name">&amp;#xeaee;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaef;</span>
                <div class="name">文件导出</div>
                <div class="code-name">&amp;#xeaef;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaed;</span>
                <div class="name">文件导入</div>
                <div class="code-name">&amp;#xeaed;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaec;</span>
                <div class="name">录制</div>
                <div class="code-name">&amp;#xeaec;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaea;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xeaea;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaeb;</span>
                <div class="name">购物车</div>
                <div class="code-name">&amp;#xeaeb;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae0;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xeae0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae2;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xeae2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae3;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xeae3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae4;</span>
                <div class="name">行高高</div>
                <div class="code-name">&amp;#xeae4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae5;</span>
                <div class="name">搜索放大镜</div>
                <div class="code-name">&amp;#xeae5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae6;</span>
                <div class="name">行高低</div>
                <div class="code-name">&amp;#xeae6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae7;</span>
                <div class="name">加号新增添加</div>
                <div class="code-name">&amp;#xeae7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae8;</span>
                <div class="name">刷新重置</div>
                <div class="code-name">&amp;#xeae8;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae9;</span>
                <div class="name">列定义</div>
                <div class="code-name">&amp;#xeae9;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeadd;</span>
                <div class="name">原型</div>
                <div class="code-name">&amp;#xeadd;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeade;</span>
                <div class="name">详设</div>
                <div class="code-name">&amp;#xeade;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeadf;</span>
                <div class="name">规划</div>
                <div class="code-name">&amp;#xeadf;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeae1;</span>
                <div class="name">行高中</div>
                <div class="code-name">&amp;#xeae1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead5;</span>
                <div class="name">复选框</div>
                <div class="code-name">&amp;#xead5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead6;</span>
                <div class="name">获取元素属性</div>
                <div class="code-name">&amp;#xead6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead7;</span>
                <div class="name">鼠标滚动</div>
                <div class="code-name">&amp;#xead7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead8;</span>
                <div class="name">导航</div>
                <div class="code-name">&amp;#xead8;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead9;</span>
                <div class="name">下拉框</div>
                <div class="code-name">&amp;#xead9;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeada;</span>
                <div class="name">文件上传</div>
                <div class="code-name">&amp;#xeada;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeadb;</span>
                <div class="name">弹窗</div>
                <div class="code-name">&amp;#xeadb;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeadc;</span>
                <div class="name">框架</div>
                <div class="code-name">&amp;#xeadc;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead3;</span>
                <div class="name">晴</div>
                <div class="code-name">&amp;#xead3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead4;</span>
                <div class="name">其他</div>
                <div class="code-name">&amp;#xead4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead2;</span>
                <div class="name">监测数据</div>
                <div class="code-name">&amp;#xead2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead1;</span>
                <div class="name">信息模型</div>
                <div class="code-name">&amp;#xead1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xead0;</span>
                <div class="name">工单流程</div>
                <div class="code-name">&amp;#xead0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeace;</span>
                <div class="name">执行任务</div>
                <div class="code-name">&amp;#xeace;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeacf;</span>
                <div class="name">流程</div>
                <div class="code-name">&amp;#xeacf;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac1;</span>
                <div class="name">鼠标位置</div>
                <div class="code-name">&amp;#xeac1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac2;</span>
                <div class="name">鼠标键盘</div>
                <div class="code-name">&amp;#xeac2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac3;</span>
                <div class="name">代码执行</div>
                <div class="code-name">&amp;#xeac3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac4;</span>
                <div class="name">移动鼠标</div>
                <div class="code-name">&amp;#xeac4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac5;</span>
                <div class="name">键盘输入</div>
                <div class="code-name">&amp;#xeac5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac6;</span>
                <div class="name">鼠标悬停</div>
                <div class="code-name">&amp;#xeac6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac7;</span>
                <div class="name">解压</div>
                <div class="code-name">&amp;#xeac7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac8;</span>
                <div class="name">JavaScript代码执行</div>
                <div class="code-name">&amp;#xeac8;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac9;</span>
                <div class="name">Word文件</div>
                <div class="code-name">&amp;#xeac9;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaca;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xeaca;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeacb;</span>
                <div class="name">操作维修</div>
                <div class="code-name">&amp;#xeacb;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeacc;</span>
                <div class="name">压缩</div>
                <div class="code-name">&amp;#xeacc;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeacd;</span>
                <div class="name">压缩包</div>
                <div class="code-name">&amp;#xeacd;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeabc;</span>
                <div class="name">鼠标滚轮</div>
                <div class="code-name">&amp;#xeabc;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeabd;</span>
                <div class="name">Python3代码执行</div>
                <div class="code-name">&amp;#xeabd;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeabe;</span>
                <div class="name">PDF文件</div>
                <div class="code-name">&amp;#xeabe;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeabf;</span>
                <div class="name">鼠标点击</div>
                <div class="code-name">&amp;#xeabf;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeac0;</span>
                <div class="name">Excel文件</div>
                <div class="code-name">&amp;#xeac0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaba;</span>
                <div class="name">截屏</div>
                <div class="code-name">&amp;#xeaba;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeabb;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xeabb;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab1;</span>
                <div class="name">运行DOS命令</div>
                <div class="code-name">&amp;#xeab1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab2;</span>
                <div class="name">语音通知</div>
                <div class="code-name">&amp;#xeab2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab3;</span>
                <div class="name">消息通知</div>
                <div class="code-name">&amp;#xeab3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab4;</span>
                <div class="name">确认框</div>
                <div class="code-name">&amp;#xeab4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab5;</span>
                <div class="name">终止进程</div>
                <div class="code-name">&amp;#xeab5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab6;</span>
                <div class="name">操作系统</div>
                <div class="code-name">&amp;#xeab6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab7;</span>
                <div class="name">屏幕解锁</div>
                <div class="code-name">&amp;#xeab7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab8;</span>
                <div class="name">屏幕锁屏</div>
                <div class="code-name">&amp;#xeab8;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab9;</span>
                <div class="name">运行</div>
                <div class="code-name">&amp;#xeab9;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaac;</span>
                <div class="name">HTTPGET请求</div>
                <div class="code-name">&amp;#xeaac;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaad;</span>
                <div class="name">连接数据库</div>
                <div class="code-name">&amp;#xeaad;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaae;</span>
                <div class="name">设备上发数据查询</div>
                <div class="code-name">&amp;#xeaae;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaaf;</span>
                <div class="name">HTTPPOST请求</div>
                <div class="code-name">&amp;#xeaaf;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeab0;</span>
                <div class="name">数据库连接</div>
                <div class="code-name">&amp;#xeab0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa5;</span>
                <div class="name">数据处理</div>
                <div class="code-name">&amp;#xeaa5;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa6;</span>
                <div class="name">文本替换</div>
                <div class="code-name">&amp;#xeaa6;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa7;</span>
                <div class="name">文本转日期</div>
                <div class="code-name">&amp;#xeaa7;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa8;</span>
                <div class="name">时间戳转日期</div>
                <div class="code-name">&amp;#xeaa8;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa9;</span>
                <div class="name">获取当前时间</div>
                <div class="code-name">&amp;#xeaa9;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaaa;</span>
                <div class="name">信息模型数据查询</div>
                <div class="code-name">&amp;#xeaaa;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaab;</span>
                <div class="name">编组</div>
                <div class="code-name">&amp;#xeaab;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9b;</span>
                <div class="name">字母大小写转换</div>
                <div class="code-name">&amp;#xea9b;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9c;</span>
                <div class="name">Base64解码</div>
                <div class="code-name">&amp;#xea9c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9d;</span>
                <div class="name">产生随机数</div>
                <div class="code-name">&amp;#xea9d;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9e;</span>
                <div class="name">Base64编码</div>
                <div class="code-name">&amp;#xea9e;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9f;</span>
                <div class="name">获取时间间隔</div>
                <div class="code-name">&amp;#xea9f;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa0;</span>
                <div class="name">减少时间</div>
                <div class="code-name">&amp;#xeaa0;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa1;</span>
                <div class="name">获取时间详细信息</div>
                <div class="code-name">&amp;#xeaa1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa2;</span>
                <div class="name">日期转时间戳</div>
                <div class="code-name">&amp;#xeaa2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa3;</span>
                <div class="name">日期转文本</div>
                <div class="code-name">&amp;#xeaa3;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeaa4;</span>
                <div class="name">从文本中提取内容</div>
                <div class="code-name">&amp;#xeaa4;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea96;</span>
                <div class="name">人工智能AI</div>
                <div class="code-name">&amp;#xea96;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea97;</span>
                <div class="name">AI分析</div>
                <div class="code-name">&amp;#xea97;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea98;</span>
                <div class="name">OCR识别</div>
                <div class="code-name">&amp;#xea98;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea99;</span>
                <div class="name">语音识别</div>
                <div class="code-name">&amp;#xea99;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea9a;</span>
                <div class="name">图片识别</div>
                <div class="code-name">&amp;#xea9a;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea85;</span>
                <div class="name">关闭网页</div>
                <div class="code-name">&amp;#xea85;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea86;</span>
                <div class="name">刷新网页</div>
                <div class="code-name">&amp;#xea86;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea87;</span>
                <div class="name">打开网页</div>
                <div class="code-name">&amp;#xea87;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea88;</span>
                <div class="name">网页截图</div>
                <div class="code-name">&amp;#xea88;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea89;</span>
                <div class="name">等待窗口</div>
                <div class="code-name">&amp;#xea89;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8a;</span>
                <div class="name">点击元素</div>
                <div class="code-name">&amp;#xea8a;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8b;</span>
                <div class="name">网页</div>
                <div class="code-name">&amp;#xea8b;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8c;</span>
                <div class="name">等待元素</div>
                <div class="code-name">&amp;#xea8c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8d;</span>
                <div class="name">鼠标滚动网页</div>
                <div class="code-name">&amp;#xea8d;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8e;</span>
                <div class="name">填写输入框</div>
                <div class="code-name">&amp;#xea8e;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea8f;</span>
                <div class="name">网页数据抓取</div>
                <div class="code-name">&amp;#xea8f;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea82;</span>
                <div class="name">For循环</div>
                <div class="code-name">&amp;#xea82;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea81;</span>
                <div class="name">IF条件判断</div>
                <div class="code-name">&amp;#xea81;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea83;</span>
                <div class="name">循环</div>
                <div class="code-name">&amp;#xea83;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea84;</span>
                <div class="name">等待</div>
                <div class="code-name">&amp;#xea84;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xec72;</span>
                <div class="name">告警实心</div>
                <div class="code-name">&amp;#xec72;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xeb80;</span>
                <div class="name">警告</div>
                <div class="code-name">&amp;#xeb80;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe64f;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea94;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xea94;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea95;</span>
                <div class="name">多选</div>
                <div class="code-name">&amp;#xea95;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea90;</span>
                <div class="name">共享</div>
                <div class="code-name">&amp;#xea90;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea92;</span>
                <div class="name">企业文档</div>
                <div class="code-name">&amp;#xea92;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea93;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xea93;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea5c;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xea5c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea55;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xea55;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea54;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xea54;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea56;</span>
                <div class="name">回收站删除</div>
                <div class="code-name">&amp;#xea56;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea57;</span>
                <div class="name">收藏五角星</div>
                <div class="code-name">&amp;#xea57;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea58;</span>
                <div class="name">蓝色文件夹</div>
                <div class="code-name">&amp;#xea58;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea5a;</span>
                <div class="name">企业</div>
                <div class="code-name">&amp;#xea5a;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea5b;</span>
                <div class="name">共享</div>
                <div class="code-name">&amp;#xea5b;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe617;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe898;</span>
                <div class="name">操作记录</div>
                <div class="code-name">&amp;#xe898;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe63c;</span>
                <div class="name">speed-快进</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe652;</span>
                <div class="name">rew-快退</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe984;</span>
                <div class="name">播放选中</div>
                <div class="code-name">&amp;#xe984;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe985;</span>
                <div class="name">暂停选中</div>
                <div class="code-name">&amp;#xe985;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea59;</span>
                <div class="name">设备比选</div>
                <div class="code-name">&amp;#xea59;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe9b1;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe9b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe67f;</span>
                <div class="name">问号</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xea4c;</span>
                <div class="name">筛选降</div>
                <div class="code-name">&amp;#xea4c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe894;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe894;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe62c;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe629;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe774;</span>
                <div class="name">业务管理</div>
                <div class="code-name">&amp;#xe774;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe6e2;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe974;</span>
                <div class="name">物资</div>
                <div class="code-name">&amp;#xe974;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe975;</span>
                <div class="name">组织</div>
                <div class="code-name">&amp;#xe975;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe655;</span>
                <div class="name">人员管理</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon action-iconfont">&#xe726;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'action-iconfont';
  src: url('iconfont.woff2?t=1754010467788') format('woff2'),
       url('iconfont.woff?t=1754010467788') format('woff'),
       url('iconfont.ttf?t=1754010467788') format('truetype'),
       url('iconfont.svg?t=1754010467788#action-iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.action-iconfont {
  font-family: "action-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="action-iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"action-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon action-iconfont icon-yunduanxiazai"></span>
            <div class="name">
              云端下载
            </div>
            <div class="code-name">.icon-yunduanxiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shujuyunhang"></span>
            <div class="name">
              数据运行
            </div>
            <div class="code-name">.icon-shujuyunhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jiedianliucheng"></span>
            <div class="name">
              节点流程
            </div>
            <div class="code-name">.icon-jiedianliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shujuzhibiao"></span>
            <div class="name">
              数据指标
            </div>
            <div class="code-name">.icon-shujuzhibiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zhixinqujian"></span>
            <div class="name">
              置信区间
            </div>
            <div class="code-name">.icon-zhixinqujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shujuqingxi"></span>
            <div class="name">
              数据清洗
            </div>
            <div class="code-name">.icon-shujuqingxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-bianliangguanli"></span>
            <div class="name">
              变量管理
            </div>
            <div class="code-name">.icon-bianliangguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-moban"></span>
            <div class="name">
              模版
            </div>
            <div class="code-name">.icon-moban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-quanbudaochu"></span>
            <div class="name">
              全部导出
            </div>
            <div class="code-name">.icon-quanbudaochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenjiandaochu"></span>
            <div class="name">
              文件导出
            </div>
            <div class="code-name">.icon-wenjiandaochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenjiandaoru"></span>
            <div class="name">
              文件导入
            </div>
            <div class="code-name">.icon-wenjiandaoru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-luzhi"></span>
            <div class="name">
              录制
            </div>
            <div class="code-name">.icon-luzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zujian"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.icon-zujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-gouwuche"></span>
            <div class="name">
              购物车
            </div>
            <div class="code-name">.icon-gouwuche
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-hanggaogao"></span>
            <div class="name">
              行高高
            </div>
            <div class="code-name">.icon-hanggaogao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-sousuofangdajing"></span>
            <div class="name">
              搜索放大镜
            </div>
            <div class="code-name">.icon-sousuofangdajing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-hanggaodi"></span>
            <div class="name">
              行高低
            </div>
            <div class="code-name">.icon-hanggaodi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jiahaoxinzengtianjia"></span>
            <div class="name">
              加号新增添加
            </div>
            <div class="code-name">.icon-jiahaoxinzengtianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shuaxinzhongzhi"></span>
            <div class="name">
              刷新重置
            </div>
            <div class="code-name">.icon-shuaxinzhongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-liedingyi"></span>
            <div class="name">
              列定义
            </div>
            <div class="code-name">.icon-liedingyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yuanxing"></span>
            <div class="name">
              原型
            </div>
            <div class="code-name">.icon-yuanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xiangshe"></span>
            <div class="name">
              详设
            </div>
            <div class="code-name">.icon-xiangshe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-guihua"></span>
            <div class="name">
              规划
            </div>
            <div class="code-name">.icon-guihua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-hanggaozhong"></span>
            <div class="name">
              行高中
            </div>
            <div class="code-name">.icon-hanggaozhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-fuxuankuang"></span>
            <div class="name">
              复选框
            </div>
            <div class="code-name">.icon-fuxuankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-huoquyuansushuxing"></span>
            <div class="name">
              获取元素属性
            </div>
            <div class="code-name">.icon-huoquyuansushuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaogundong"></span>
            <div class="name">
              鼠标滚动
            </div>
            <div class="code-name">.icon-shubiaogundong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-daohang"></span>
            <div class="name">
              导航
            </div>
            <div class="code-name">.icon-daohang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xialakuang"></span>
            <div class="name">
              下拉框
            </div>
            <div class="code-name">.icon-xialakuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenjianshangchuan"></span>
            <div class="name">
              文件上传
            </div>
            <div class="code-name">.icon-wenjianshangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-tanchuang"></span>
            <div class="name">
              弹窗
            </div>
            <div class="code-name">.icon-tanchuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-kuangjia"></span>
            <div class="name">
              框架
            </div>
            <div class="code-name">.icon-kuangjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-qing"></span>
            <div class="name">
              晴
            </div>
            <div class="code-name">.icon-qing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-qita"></span>
            <div class="name">
              其他
            </div>
            <div class="code-name">.icon-qita
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jianceshuju"></span>
            <div class="name">
              监测数据
            </div>
            <div class="code-name">.icon-jianceshuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xinximoxing"></span>
            <div class="name">
              信息模型
            </div>
            <div class="code-name">.icon-xinximoxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-gongdanliucheng"></span>
            <div class="name">
              工单流程
            </div>
            <div class="code-name">.icon-gongdanliucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zhihangrenwu"></span>
            <div class="name">
              执行任务
            </div>
            <div class="code-name">.icon-zhihangrenwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-liucheng"></span>
            <div class="name">
              流程
            </div>
            <div class="code-name">.icon-liucheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaoweizhi"></span>
            <div class="name">
              鼠标位置
            </div>
            <div class="code-name">.icon-shubiaoweizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaojianpan"></span>
            <div class="name">
              鼠标键盘
            </div>
            <div class="code-name">.icon-shubiaojianpan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-daimazhihang"></span>
            <div class="name">
              代码执行
            </div>
            <div class="code-name">.icon-daimazhihang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yidongshubiao"></span>
            <div class="name">
              移动鼠标
            </div>
            <div class="code-name">.icon-yidongshubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jianpanshuru"></span>
            <div class="name">
              键盘输入
            </div>
            <div class="code-name">.icon-jianpanshuru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaoxuanting"></span>
            <div class="name">
              鼠标悬停
            </div>
            <div class="code-name">.icon-shubiaoxuanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jieya"></span>
            <div class="name">
              解压
            </div>
            <div class="code-name">.icon-jieya
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-JavaScriptdaimazhihang"></span>
            <div class="name">
              JavaScript代码执行
            </div>
            <div class="code-name">.icon-JavaScriptdaimazhihang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Wordwenjian"></span>
            <div class="name">
              Word文件
            </div>
            <div class="code-name">.icon-Wordwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenjian"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.icon-wenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-caozuoweixiu"></span>
            <div class="name">
              操作维修
            </div>
            <div class="code-name">.icon-caozuoweixiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yasuo"></span>
            <div class="name">
              压缩
            </div>
            <div class="code-name">.icon-yasuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yasuobao"></span>
            <div class="name">
              压缩包
            </div>
            <div class="code-name">.icon-yasuobao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaogunlun"></span>
            <div class="name">
              鼠标滚轮
            </div>
            <div class="code-name">.icon-shubiaogunlun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Python3daimazhihang"></span>
            <div class="name">
              Python3代码执行
            </div>
            <div class="code-name">.icon-Python3daimazhihang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-PDFwenjian"></span>
            <div class="name">
              PDF文件
            </div>
            <div class="code-name">.icon-PDFwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaodianji"></span>
            <div class="name">
              鼠标点击
            </div>
            <div class="code-name">.icon-shubiaodianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Excelwenjian"></span>
            <div class="name">
              Excel文件
            </div>
            <div class="code-name">.icon-Excelwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jieping"></span>
            <div class="name">
              截屏
            </div>
            <div class="code-name">.icon-jieping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yunhangDOSmingling"></span>
            <div class="name">
              运行DOS命令
            </div>
            <div class="code-name">.icon-yunhangDOSmingling
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yuyintongzhi"></span>
            <div class="name">
              语音通知
            </div>
            <div class="code-name">.icon-yuyintongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xiaoxitongzhi"></span>
            <div class="name">
              消息通知
            </div>
            <div class="code-name">.icon-xiaoxitongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-querenkuang"></span>
            <div class="name">
              确认框
            </div>
            <div class="code-name">.icon-querenkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zhongzhijincheng"></span>
            <div class="name">
              终止进程
            </div>
            <div class="code-name">.icon-zhongzhijincheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-caozuoxitong"></span>
            <div class="name">
              操作系统
            </div>
            <div class="code-name">.icon-caozuoxitong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-pingmujiesuo"></span>
            <div class="name">
              屏幕解锁
            </div>
            <div class="code-name">.icon-pingmujiesuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-pingmusuoping"></span>
            <div class="name">
              屏幕锁屏
            </div>
            <div class="code-name">.icon-pingmusuoping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yunhang"></span>
            <div class="name">
              运行
            </div>
            <div class="code-name">.icon-yunhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-HTTPGETqingqiu"></span>
            <div class="name">
              HTTPGET请求
            </div>
            <div class="code-name">.icon-HTTPGETqingqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-lianjieshujuku"></span>
            <div class="name">
              连接数据库
            </div>
            <div class="code-name">.icon-lianjieshujuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shebeishangfashujuchaxun"></span>
            <div class="name">
              设备上发数据查询
            </div>
            <div class="code-name">.icon-shebeishangfashujuchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-HTTPPOSTqingqiu"></span>
            <div class="name">
              HTTPPOST请求
            </div>
            <div class="code-name">.icon-HTTPPOSTqingqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shujukulianjie"></span>
            <div class="name">
              数据库连接
            </div>
            <div class="code-name">.icon-shujukulianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shujuchuli"></span>
            <div class="name">
              数据处理
            </div>
            <div class="code-name">.icon-shujuchuli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenbentihuan"></span>
            <div class="name">
              文本替换
            </div>
            <div class="code-name">.icon-wenbentihuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenbenzhuanriqi"></span>
            <div class="name">
              文本转日期
            </div>
            <div class="code-name">.icon-wenbenzhuanriqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shijianchuozhuanriqi"></span>
            <div class="name">
              时间戳转日期
            </div>
            <div class="code-name">.icon-shijianchuozhuanriqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-huoqudangqianshijian"></span>
            <div class="name">
              获取当前时间
            </div>
            <div class="code-name">.icon-huoqudangqianshijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xinximoxingshujuchaxun"></span>
            <div class="name">
              信息模型数据查询
            </div>
            <div class="code-name">.icon-xinximoxingshujuchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-bianzu"></span>
            <div class="name">
              编组
            </div>
            <div class="code-name">.icon-bianzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zimudaxiaoxiezhuanhuan"></span>
            <div class="name">
              字母大小写转换
            </div>
            <div class="code-name">.icon-zimudaxiaoxiezhuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Base64jiema"></span>
            <div class="name">
              Base64解码
            </div>
            <div class="code-name">.icon-Base64jiema
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-chanshengsuijishu"></span>
            <div class="name">
              产生随机数
            </div>
            <div class="code-name">.icon-chanshengsuijishu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Base64bianma"></span>
            <div class="name">
              Base64编码
            </div>
            <div class="code-name">.icon-Base64bianma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-huoqushijianjiange"></span>
            <div class="name">
              获取时间间隔
            </div>
            <div class="code-name">.icon-huoqushijianjiange
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jianshaoshijian"></span>
            <div class="name">
              减少时间
            </div>
            <div class="code-name">.icon-jianshaoshijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-huoqushijianxiangxixinxi"></span>
            <div class="name">
              获取时间详细信息
            </div>
            <div class="code-name">.icon-huoqushijianxiangxixinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-riqizhuanshijianchuo"></span>
            <div class="name">
              日期转时间戳
            </div>
            <div class="code-name">.icon-riqizhuanshijianchuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-riqizhuanwenben"></span>
            <div class="name">
              日期转文本
            </div>
            <div class="code-name">.icon-riqizhuanwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-congwenbenzhongtiquneirong"></span>
            <div class="name">
              从文本中提取内容
            </div>
            <div class="code-name">.icon-congwenbenzhongtiquneirong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-rengongzhinengAI"></span>
            <div class="name">
              人工智能AI
            </div>
            <div class="code-name">.icon-rengongzhinengAI
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-AIfenxi"></span>
            <div class="name">
              AI分析
            </div>
            <div class="code-name">.icon-AIfenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-OCRshibie"></span>
            <div class="name">
              OCR识别
            </div>
            <div class="code-name">.icon-OCRshibie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yuyinshibie"></span>
            <div class="name">
              语音识别
            </div>
            <div class="code-name">.icon-yuyinshibie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-tupianshibie"></span>
            <div class="name">
              图片识别
            </div>
            <div class="code-name">.icon-tupianshibie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-guanbiwangye"></span>
            <div class="name">
              关闭网页
            </div>
            <div class="code-name">.icon-guanbiwangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shuaxinwangye"></span>
            <div class="name">
              刷新网页
            </div>
            <div class="code-name">.icon-shuaxinwangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-dakaiwangye"></span>
            <div class="name">
              打开网页
            </div>
            <div class="code-name">.icon-dakaiwangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wangyejietu"></span>
            <div class="name">
              网页截图
            </div>
            <div class="code-name">.icon-wangyejietu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-dengdaichuangkou"></span>
            <div class="name">
              等待窗口
            </div>
            <div class="code-name">.icon-dengdaichuangkou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-dianjiyuansu"></span>
            <div class="name">
              点击元素
            </div>
            <div class="code-name">.icon-dianjiyuansu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wangye"></span>
            <div class="name">
              网页
            </div>
            <div class="code-name">.icon-wangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-dengdaiyuansu"></span>
            <div class="name">
              等待元素
            </div>
            <div class="code-name">.icon-dengdaiyuansu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shubiaogundongwangye"></span>
            <div class="name">
              鼠标滚动网页
            </div>
            <div class="code-name">.icon-shubiaogundongwangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-tianxieshurukuang"></span>
            <div class="name">
              填写输入框
            </div>
            <div class="code-name">.icon-tianxieshurukuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wangyeshujuzhuaqu"></span>
            <div class="name">
              网页数据抓取
            </div>
            <div class="code-name">.icon-wangyeshujuzhuaqu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-Forxunhuan"></span>
            <div class="name">
              For循环
            </div>
            <div class="code-name">.icon-Forxunhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-IFtiaojianpanduan"></span>
            <div class="name">
              IF条件判断
            </div>
            <div class="code-name">.icon-IFtiaojianpanduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-xunhuan"></span>
            <div class="name">
              循环
            </div>
            <div class="code-name">.icon-xunhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-dengdai"></span>
            <div class="name">
              等待
            </div>
            <div class="code-name">.icon-dengdai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-tixingshixin"></span>
            <div class="name">
              告警实心
            </div>
            <div class="code-name">.icon-tixingshixin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-jinggao"></span>
            <div class="name">
              警告
            </div>
            <div class="code-name">.icon-jinggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-fanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.icon-fanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-fenxiang1"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-duoxuan"></span>
            <div class="name">
              多选
            </div>
            <div class="code-name">.icon-duoxuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-gongxiang1"></span>
            <div class="name">
              共享
            </div>
            <div class="code-name">.icon-gongxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-qiyewendang"></span>
            <div class="name">
              企业文档
            </div>
            <div class="code-name">.icon-qiyewendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-fenxiang"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon-wenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-huishouzhanshanchu"></span>
            <div class="name">
              回收站删除
            </div>
            <div class="code-name">.icon-huishouzhanshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shoucangwujiaoxing"></span>
            <div class="name">
              收藏五角星
            </div>
            <div class="code-name">.icon-shoucangwujiaoxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-lansewenjianjia"></span>
            <div class="name">
              蓝色文件夹
            </div>
            <div class="code-name">.icon-lansewenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-qiye"></span>
            <div class="name">
              企业
            </div>
            <div class="code-name">.icon-qiye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-gongxiang"></span>
            <div class="name">
              共享
            </div>
            <div class="code-name">.icon-gongxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shouye1"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-caozuojilu"></span>
            <div class="name">
              操作记录
            </div>
            <div class="code-name">.icon-caozuojilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-speedkuaijin"></span>
            <div class="name">
              speed-快进
            </div>
            <div class="code-name">.icon-speedkuaijin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-rewkuaitui"></span>
            <div class="name">
              rew-快退
            </div>
            <div class="code-name">.icon-rewkuaitui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-bofangxuanzhong"></span>
            <div class="name">
              播放选中
            </div>
            <div class="code-name">.icon-bofangxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-zantingxuanzhong"></span>
            <div class="name">
              暂停选中
            </div>
            <div class="code-name">.icon-zantingxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shebeibixuan"></span>
            <div class="name">
              设备比选
            </div>
            <div class="code-name">.icon-shebeibixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wenhao"></span>
            <div class="name">
              问号
            </div>
            <div class="code-name">.icon-wenhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shaixuanjiang"></span>
            <div class="name">
              筛选降
            </div>
            <div class="code-name">.icon-shaixuanjiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shaixuan"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.icon-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-icon-"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.icon-icon-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-user"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.icon-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-yewuguanli"></span>
            <div class="name">
              业务管理
            </div>
            <div class="code-name">.icon-yewuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-shuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.icon-shuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-wuzi"></span>
            <div class="name">
              物资
            </div>
            <div class="code-name">.icon-wuzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-icon_xinyong_xianxing_jijin-284"></span>
            <div class="name">
              组织
            </div>
            <div class="code-name">.icon-icon_xinyong_xianxing_jijin-284
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-renyuanguanli"></span>
            <div class="name">
              人员管理
            </div>
            <div class="code-name">.icon-renyuanguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon action-iconfont icon-ayizhangtu"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-ayizhangtu
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="action-iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            action-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunduanxiazai"></use>
                </svg>
                <div class="name">云端下载</div>
                <div class="code-name">#icon-yunduanxiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuyunhang"></use>
                </svg>
                <div class="name">数据运行</div>
                <div class="code-name">#icon-shujuyunhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiedianliucheng"></use>
                </svg>
                <div class="name">节点流程</div>
                <div class="code-name">#icon-jiedianliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuzhibiao"></use>
                </svg>
                <div class="name">数据指标</div>
                <div class="code-name">#icon-shujuzhibiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhixinqujian"></use>
                </svg>
                <div class="name">置信区间</div>
                <div class="code-name">#icon-zhixinqujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuqingxi"></use>
                </svg>
                <div class="name">数据清洗</div>
                <div class="code-name">#icon-shujuqingxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianliangguanli"></use>
                </svg>
                <div class="name">变量管理</div>
                <div class="code-name">#icon-bianliangguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-moban"></use>
                </svg>
                <div class="name">模版</div>
                <div class="code-name">#icon-moban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanbudaochu"></use>
                </svg>
                <div class="name">全部导出</div>
                <div class="code-name">#icon-quanbudaochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjiandaochu"></use>
                </svg>
                <div class="name">文件导出</div>
                <div class="code-name">#icon-wenjiandaochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjiandaoru"></use>
                </svg>
                <div class="name">文件导入</div>
                <div class="code-name">#icon-wenjiandaoru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-luzhi"></use>
                </svg>
                <div class="name">录制</div>
                <div class="code-name">#icon-luzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zujian"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#icon-zujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gouwuche"></use>
                </svg>
                <div class="name">购物车</div>
                <div class="code-name">#icon-gouwuche</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hanggaogao"></use>
                </svg>
                <div class="name">行高高</div>
                <div class="code-name">#icon-hanggaogao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuofangdajing"></use>
                </svg>
                <div class="name">搜索放大镜</div>
                <div class="code-name">#icon-sousuofangdajing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hanggaodi"></use>
                </svg>
                <div class="name">行高低</div>
                <div class="code-name">#icon-hanggaodi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiahaoxinzengtianjia"></use>
                </svg>
                <div class="name">加号新增添加</div>
                <div class="code-name">#icon-jiahaoxinzengtianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxinzhongzhi"></use>
                </svg>
                <div class="name">刷新重置</div>
                <div class="code-name">#icon-shuaxinzhongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liedingyi"></use>
                </svg>
                <div class="name">列定义</div>
                <div class="code-name">#icon-liedingyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuanxing"></use>
                </svg>
                <div class="name">原型</div>
                <div class="code-name">#icon-yuanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshe"></use>
                </svg>
                <div class="name">详设</div>
                <div class="code-name">#icon-xiangshe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guihua"></use>
                </svg>
                <div class="name">规划</div>
                <div class="code-name">#icon-guihua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hanggaozhong"></use>
                </svg>
                <div class="name">行高中</div>
                <div class="code-name">#icon-hanggaozhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuxuankuang"></use>
                </svg>
                <div class="name">复选框</div>
                <div class="code-name">#icon-fuxuankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huoquyuansushuxing"></use>
                </svg>
                <div class="name">获取元素属性</div>
                <div class="code-name">#icon-huoquyuansushuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaogundong"></use>
                </svg>
                <div class="name">鼠标滚动</div>
                <div class="code-name">#icon-shubiaogundong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daohang"></use>
                </svg>
                <div class="name">导航</div>
                <div class="code-name">#icon-daohang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xialakuang"></use>
                </svg>
                <div class="name">下拉框</div>
                <div class="code-name">#icon-xialakuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianshangchuan"></use>
                </svg>
                <div class="name">文件上传</div>
                <div class="code-name">#icon-wenjianshangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tanchuang"></use>
                </svg>
                <div class="name">弹窗</div>
                <div class="code-name">#icon-tanchuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kuangjia"></use>
                </svg>
                <div class="name">框架</div>
                <div class="code-name">#icon-kuangjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qing"></use>
                </svg>
                <div class="name">晴</div>
                <div class="code-name">#icon-qing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qita"></use>
                </svg>
                <div class="name">其他</div>
                <div class="code-name">#icon-qita</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianceshuju"></use>
                </svg>
                <div class="name">监测数据</div>
                <div class="code-name">#icon-jianceshuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinximoxing"></use>
                </svg>
                <div class="name">信息模型</div>
                <div class="code-name">#icon-xinximoxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongdanliucheng"></use>
                </svg>
                <div class="name">工单流程</div>
                <div class="code-name">#icon-gongdanliucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhihangrenwu"></use>
                </svg>
                <div class="name">执行任务</div>
                <div class="code-name">#icon-zhihangrenwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liucheng"></use>
                </svg>
                <div class="name">流程</div>
                <div class="code-name">#icon-liucheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaoweizhi"></use>
                </svg>
                <div class="name">鼠标位置</div>
                <div class="code-name">#icon-shubiaoweizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaojianpan"></use>
                </svg>
                <div class="name">鼠标键盘</div>
                <div class="code-name">#icon-shubiaojianpan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daimazhihang"></use>
                </svg>
                <div class="name">代码执行</div>
                <div class="code-name">#icon-daimazhihang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yidongshubiao"></use>
                </svg>
                <div class="name">移动鼠标</div>
                <div class="code-name">#icon-yidongshubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianpanshuru"></use>
                </svg>
                <div class="name">键盘输入</div>
                <div class="code-name">#icon-jianpanshuru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaoxuanting"></use>
                </svg>
                <div class="name">鼠标悬停</div>
                <div class="code-name">#icon-shubiaoxuanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieya"></use>
                </svg>
                <div class="name">解压</div>
                <div class="code-name">#icon-jieya</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-JavaScriptdaimazhihang"></use>
                </svg>
                <div class="name">JavaScript代码执行</div>
                <div class="code-name">#icon-JavaScriptdaimazhihang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Wordwenjian"></use>
                </svg>
                <div class="name">Word文件</div>
                <div class="code-name">#icon-Wordwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjian"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#icon-wenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caozuoweixiu"></use>
                </svg>
                <div class="name">操作维修</div>
                <div class="code-name">#icon-caozuoweixiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yasuo"></use>
                </svg>
                <div class="name">压缩</div>
                <div class="code-name">#icon-yasuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yasuobao"></use>
                </svg>
                <div class="name">压缩包</div>
                <div class="code-name">#icon-yasuobao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaogunlun"></use>
                </svg>
                <div class="name">鼠标滚轮</div>
                <div class="code-name">#icon-shubiaogunlun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Python3daimazhihang"></use>
                </svg>
                <div class="name">Python3代码执行</div>
                <div class="code-name">#icon-Python3daimazhihang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-PDFwenjian"></use>
                </svg>
                <div class="name">PDF文件</div>
                <div class="code-name">#icon-PDFwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaodianji"></use>
                </svg>
                <div class="name">鼠标点击</div>
                <div class="code-name">#icon-shubiaodianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Excelwenjian"></use>
                </svg>
                <div class="name">Excel文件</div>
                <div class="code-name">#icon-Excelwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieping"></use>
                </svg>
                <div class="name">截屏</div>
                <div class="code-name">#icon-jieping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunhangDOSmingling"></use>
                </svg>
                <div class="name">运行DOS命令</div>
                <div class="code-name">#icon-yunhangDOSmingling</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyintongzhi"></use>
                </svg>
                <div class="name">语音通知</div>
                <div class="code-name">#icon-yuyintongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxitongzhi"></use>
                </svg>
                <div class="name">消息通知</div>
                <div class="code-name">#icon-xiaoxitongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-querenkuang"></use>
                </svg>
                <div class="name">确认框</div>
                <div class="code-name">#icon-querenkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongzhijincheng"></use>
                </svg>
                <div class="name">终止进程</div>
                <div class="code-name">#icon-zhongzhijincheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caozuoxitong"></use>
                </svg>
                <div class="name">操作系统</div>
                <div class="code-name">#icon-caozuoxitong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingmujiesuo"></use>
                </svg>
                <div class="name">屏幕解锁</div>
                <div class="code-name">#icon-pingmujiesuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingmusuoping"></use>
                </svg>
                <div class="name">屏幕锁屏</div>
                <div class="code-name">#icon-pingmusuoping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunhang"></use>
                </svg>
                <div class="name">运行</div>
                <div class="code-name">#icon-yunhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-HTTPGETqingqiu"></use>
                </svg>
                <div class="name">HTTPGET请求</div>
                <div class="code-name">#icon-HTTPGETqingqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjieshujuku"></use>
                </svg>
                <div class="name">连接数据库</div>
                <div class="code-name">#icon-lianjieshujuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shebeishangfashujuchaxun"></use>
                </svg>
                <div class="name">设备上发数据查询</div>
                <div class="code-name">#icon-shebeishangfashujuchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-HTTPPOSTqingqiu"></use>
                </svg>
                <div class="name">HTTPPOST请求</div>
                <div class="code-name">#icon-HTTPPOSTqingqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujukulianjie"></use>
                </svg>
                <div class="name">数据库连接</div>
                <div class="code-name">#icon-shujukulianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuchuli"></use>
                </svg>
                <div class="name">数据处理</div>
                <div class="code-name">#icon-shujuchuli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbentihuan"></use>
                </svg>
                <div class="name">文本替换</div>
                <div class="code-name">#icon-wenbentihuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenzhuanriqi"></use>
                </svg>
                <div class="name">文本转日期</div>
                <div class="code-name">#icon-wenbenzhuanriqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijianchuozhuanriqi"></use>
                </svg>
                <div class="name">时间戳转日期</div>
                <div class="code-name">#icon-shijianchuozhuanriqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huoqudangqianshijian"></use>
                </svg>
                <div class="name">获取当前时间</div>
                <div class="code-name">#icon-huoqudangqianshijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinximoxingshujuchaxun"></use>
                </svg>
                <div class="name">信息模型数据查询</div>
                <div class="code-name">#icon-xinximoxingshujuchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzu"></use>
                </svg>
                <div class="name">编组</div>
                <div class="code-name">#icon-bianzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zimudaxiaoxiezhuanhuan"></use>
                </svg>
                <div class="name">字母大小写转换</div>
                <div class="code-name">#icon-zimudaxiaoxiezhuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Base64jiema"></use>
                </svg>
                <div class="name">Base64解码</div>
                <div class="code-name">#icon-Base64jiema</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chanshengsuijishu"></use>
                </svg>
                <div class="name">产生随机数</div>
                <div class="code-name">#icon-chanshengsuijishu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Base64bianma"></use>
                </svg>
                <div class="name">Base64编码</div>
                <div class="code-name">#icon-Base64bianma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huoqushijianjiange"></use>
                </svg>
                <div class="name">获取时间间隔</div>
                <div class="code-name">#icon-huoqushijianjiange</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianshaoshijian"></use>
                </svg>
                <div class="name">减少时间</div>
                <div class="code-name">#icon-jianshaoshijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huoqushijianxiangxixinxi"></use>
                </svg>
                <div class="name">获取时间详细信息</div>
                <div class="code-name">#icon-huoqushijianxiangxixinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-riqizhuanshijianchuo"></use>
                </svg>
                <div class="name">日期转时间戳</div>
                <div class="code-name">#icon-riqizhuanshijianchuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-riqizhuanwenben"></use>
                </svg>
                <div class="name">日期转文本</div>
                <div class="code-name">#icon-riqizhuanwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-congwenbenzhongtiquneirong"></use>
                </svg>
                <div class="name">从文本中提取内容</div>
                <div class="code-name">#icon-congwenbenzhongtiquneirong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rengongzhinengAI"></use>
                </svg>
                <div class="name">人工智能AI</div>
                <div class="code-name">#icon-rengongzhinengAI</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-AIfenxi"></use>
                </svg>
                <div class="name">AI分析</div>
                <div class="code-name">#icon-AIfenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-OCRshibie"></use>
                </svg>
                <div class="name">OCR识别</div>
                <div class="code-name">#icon-OCRshibie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyinshibie"></use>
                </svg>
                <div class="name">语音识别</div>
                <div class="code-name">#icon-yuyinshibie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupianshibie"></use>
                </svg>
                <div class="name">图片识别</div>
                <div class="code-name">#icon-tupianshibie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbiwangye"></use>
                </svg>
                <div class="name">关闭网页</div>
                <div class="code-name">#icon-guanbiwangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxinwangye"></use>
                </svg>
                <div class="name">刷新网页</div>
                <div class="code-name">#icon-shuaxinwangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dakaiwangye"></use>
                </svg>
                <div class="name">打开网页</div>
                <div class="code-name">#icon-dakaiwangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangyejietu"></use>
                </svg>
                <div class="name">网页截图</div>
                <div class="code-name">#icon-wangyejietu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengdaichuangkou"></use>
                </svg>
                <div class="name">等待窗口</div>
                <div class="code-name">#icon-dengdaichuangkou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianjiyuansu"></use>
                </svg>
                <div class="name">点击元素</div>
                <div class="code-name">#icon-dianjiyuansu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangye"></use>
                </svg>
                <div class="name">网页</div>
                <div class="code-name">#icon-wangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengdaiyuansu"></use>
                </svg>
                <div class="name">等待元素</div>
                <div class="code-name">#icon-dengdaiyuansu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shubiaogundongwangye"></use>
                </svg>
                <div class="name">鼠标滚动网页</div>
                <div class="code-name">#icon-shubiaogundongwangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianxieshurukuang"></use>
                </svg>
                <div class="name">填写输入框</div>
                <div class="code-name">#icon-tianxieshurukuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangyeshujuzhuaqu"></use>
                </svg>
                <div class="name">网页数据抓取</div>
                <div class="code-name">#icon-wangyeshujuzhuaqu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Forxunhuan"></use>
                </svg>
                <div class="name">For循环</div>
                <div class="code-name">#icon-Forxunhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-IFtiaojianpanduan"></use>
                </svg>
                <div class="name">IF条件判断</div>
                <div class="code-name">#icon-IFtiaojianpanduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xunhuan"></use>
                </svg>
                <div class="name">循环</div>
                <div class="code-name">#icon-xunhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengdai"></use>
                </svg>
                <div class="name">等待</div>
                <div class="code-name">#icon-dengdai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tixingshixin"></use>
                </svg>
                <div class="name">告警实心</div>
                <div class="code-name">#icon-tixingshixin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinggao"></use>
                </svg>
                <div class="name">警告</div>
                <div class="code-name">#icon-jinggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#icon-fanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang1"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duoxuan"></use>
                </svg>
                <div class="name">多选</div>
                <div class="code-name">#icon-duoxuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongxiang1"></use>
                </svg>
                <div class="name">共享</div>
                <div class="code-name">#icon-gongxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiyewendang"></use>
                </svg>
                <div class="name">企业文档</div>
                <div class="code-name">#icon-qiyewendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon-wenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huishouzhanshanchu"></use>
                </svg>
                <div class="name">回收站删除</div>
                <div class="code-name">#icon-huishouzhanshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucangwujiaoxing"></use>
                </svg>
                <div class="name">收藏五角星</div>
                <div class="code-name">#icon-shoucangwujiaoxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lansewenjianjia"></use>
                </svg>
                <div class="name">蓝色文件夹</div>
                <div class="code-name">#icon-lansewenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiye"></use>
                </svg>
                <div class="name">企业</div>
                <div class="code-name">#icon-qiye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongxiang"></use>
                </svg>
                <div class="name">共享</div>
                <div class="code-name">#icon-gongxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye1"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caozuojilu"></use>
                </svg>
                <div class="name">操作记录</div>
                <div class="code-name">#icon-caozuojilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-speedkuaijin"></use>
                </svg>
                <div class="name">speed-快进</div>
                <div class="code-name">#icon-speedkuaijin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rewkuaitui"></use>
                </svg>
                <div class="name">rew-快退</div>
                <div class="code-name">#icon-rewkuaitui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bofangxuanzhong"></use>
                </svg>
                <div class="name">播放选中</div>
                <div class="code-name">#icon-bofangxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zantingxuanzhong"></use>
                </svg>
                <div class="name">暂停选中</div>
                <div class="code-name">#icon-zantingxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shebeibixuan"></use>
                </svg>
                <div class="name">设备比选</div>
                <div class="code-name">#icon-shebeibixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenhao"></use>
                </svg>
                <div class="name">问号</div>
                <div class="code-name">#icon-wenhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuanjiang"></use>
                </svg>
                <div class="name">筛选降</div>
                <div class="code-name">#icon-shaixuanjiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#icon-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#icon-icon-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#icon-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yewuguanli"></use>
                </svg>
                <div class="name">业务管理</div>
                <div class="code-name">#icon-yewuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#icon-shuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wuzi"></use>
                </svg>
                <div class="name">物资</div>
                <div class="code-name">#icon-wuzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_xinyong_xianxing_jijin-284"></use>
                </svg>
                <div class="name">组织</div>
                <div class="code-name">#icon-icon_xinyong_xianxing_jijin-284</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renyuanguanli"></use>
                </svg>
                <div class="name">人员管理</div>
                <div class="code-name">#icon-renyuanguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ayizhangtu"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-ayizhangtu</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
