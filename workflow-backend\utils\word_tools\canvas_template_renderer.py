import re
from jinja2 import Template
from typing import Any

def normalize_path(path: str) -> str:
    # 去除所有 []
    return path.replace('[]', '')

def convert_text_with_jinja(text: str, context: dict) -> str:
    def replacer(m):
        raw_path = m.group(1).strip()
        norm_path = normalize_path(raw_path)
        return f"{{{{ {norm_path} }}}}"
    tpl = re.sub(r'#\{(.*?)\}', replacer, text)
    return Template(tpl).render(context)

def render_canvas_json_with_context(obj: Any, context: dict) -> Any:
    if isinstance(obj, dict):
        return {k: render_canvas_json_with_context(v, context) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [render_canvas_json_with_context(i, context) for i in obj]
    elif isinstance(obj, str) and '#{' in obj:
        return convert_text_with_jinja(obj, context)
    else:
        return obj
