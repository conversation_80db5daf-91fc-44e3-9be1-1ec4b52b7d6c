<template>
  <div class="tree-picker-user">
    <el-input
      class="tree-picker-user-input"
      v-model="inputValue"
      readonly
      clearable
      placeholder=""
      @click.native="openDialog(true)"
    >
      <template #suffix>
        <div class="icon-clear" v-if="inputValue" @click.stop="onClear">
          <el-icon><CircleClose /></el-icon>
        </div>
      </template>
      <template #append>
        <div class="icon-box" @click="openDialog(true)">
          <el-icon><UserFilled /></el-icon>
        </div>
      </template>
    </el-input>
    <el-dialog v-model="dlgVisible" :close-on-click-modal="false" append-to-body header-class="user-picker-dialog-header" body-class="user-picker-dialog-body" :width="'820px'" :title="title" @open="onOpend" @closed="onClosed">
      <div class="picker-user-container">
        <div class="picker-user-mainbody">
          <div class="picker__tree" v-if="ready">
            <div class="el-tree-async">
              <div class="el-tree-async-text">可选项</div>
              <div class="el-tree-async-search">
                <el-autocomplete
                  ref="autoRef"
                  v-model="filterText"
                  :fetch-suggestions="querySearch"
                  clearable
                  class="inline-input w-50"
                  placeholder="搜索"
                  :prefix-icon="Search"
                  value-key="showText"
                  @select="handleSelect"
                />
              </div>
              <el-scrollbar class="el-tree-async-content" v-loading="loading">
                <!-- :load="loadNode" -->
                <!-- lazy -->
                <el-tree
                  ref="treeRef"
                  style="max-width: 600px;"
                  :data="treeData"
                  :props="treeProps"
                  node-key="id"
                  show-checkbox
                  :default-checked-keys="checkedKeys"
                  @check-change="handleCheckChange"
                >
                  <template #default="{ node, data }">
                    <div class="custom-tree-node">
                      <i class="iconfont icon-biaodanbiaoge" v-if="data.type === 'user'"></i>
                      <i class="iconfont icon-wenjianjia1" v-else></i>
                      <span>{{ node.label }}</span>
                    </div>
                  </template>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>

          <div class="picker__handle">
            <el-button icon="ArrowRight" @click="addUser"></el-button>
            <el-button icon="ArrowLeft" @click="removeUser"></el-button>
            <el-button icon="DArrowLeft" @click="removeAll"></el-button>
          </div>

          <div class="picker__table">
            <div class="el-table-box">
              <div class="el-table-header">
                <div class="el-table-header-title">已选择：</div>
                <div class="el-table-header-left">
                  <el-input
                    class="el-table-header-input"
                    v-model="userName"
                    clearable
                    size="small"
                    placeholder="请输入姓名"
                  ></el-input>
                </div>
                <div class="el-table-header-right">
                  <div class="header__text">
                    本次新增 <span>{{ Number(addUserNum) - Number(nowUserNum) > 0 ? Number(addUserNum) - Number(nowUserNum) : 0 }}</span> 人， 共 <span>{{ addUserNum }}</span> 人 
                  </div>
                </div>
              </div>
              <div class="el-table-body">
                <el-table
                  ref="multipleTableRef"
                  :data="filterTableData"
                  row-key="id"
                  style="width: 100%;height: 100%;"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="50" align="center" />
                  <el-table-column type="index" label="#" width="50" align="center" />
                  <el-table-column property="name" label="姓名" width="80" align="center" />
                  <el-table-column property="sn" label="工号" width="80" align="center" />
                  <el-table-column property="orgName" label="部门" show-overflow-tooltip align="center" />
                  <template #empty>
                    <el-empty description="暂无数据" :image-size="120" />
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button size="default" @click="openDialog(false)">取消</el-button>
        <el-button size="default" type="primary" @click="onConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import type { FilterNodeMethodFunction, TreeInstance, LoadFunction, AutocompleteInstance } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
// @ts-ignore
import { request } from '@/utils/axios';
// @ts-ignore
import { utils } from '@/utils/utils';

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  title?: string
  // variables?: Variable[]
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入值或选择变量...',
  variables: () => [],
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string],
  'change': [nameString: string] // 添加 change 事件类型
}>()

// 响应式数据
const inputValue = ref('')
const dlgVisible = ref(false)
const ready = ref(false)
const loading = ref(false)


const onClear = () => {
  emit('update:modelValue', '')
  emit('change', '')
}

const onOpend = () => {
  if (treeData.value.length === 0) initTree()
  ready.value = true;
  initValue();
}
const onClosed = () => {
  ready.value = false;
}
const openDialog = (isopen: boolean) => {
  dlgVisible.value = isopen
}
const onConfirm = () => {
  const nameIds = tableData.value.map(it => it.id).join(',') || ''
  const nameString = tableData.value.map(it => it.name).join(',') || ''
  emit('update:modelValue', nameIds)
  emit('change', nameString)
  openDialog(false)
}


onMounted(() => {
  
})

onUnmounted(() => {
  
})


// el-tree方法
interface Tree {
  children?: Tree[]
  id: string
  name: string
  orderBy?: number
  orgName?: string
  parentId?: string
  pid?: string
  type: string
  weight?: number
}

const autoRef = ref<AutocompleteInstance>()

const querySearch = (queryString: string, cb: any) => {
  if (!queryString) {
    return cb([])
  }
  const userParams = {
    name: queryString
  }
  request.post('/sys/user/query.json', userParams)
    .then(res => {
      const results = (res || []).map(item => {
        return {
          ...item,
          showText: `${item.name} [${item.groupName || ' '} - ${item.sn || ' '}]`
        }
      })
      cb(results)
    })
    .catch(err => {
      cb([])
    })
}

const handleSelect = (item: any) => {
  const thisData = tableData.value.find(it => it.id == item.id)
  if (!thisData) {
    tableData.value.push({
      ...item,
      type: 'user',
      orgName: item.groupName || ''
    })
  }
  if (!checkedKeys.value.includes(item.id)) {
    checkedKeys.value.push(item.id)
    treeRef.value?.setChecked(item.id, true)
  }
  filterText.value = ''
}

const treeProps = {
  label: 'name',
  children: 'children',
  isLeaf: (node: Tree) => {
    return node.type === 'user'
  },
  disabled: (node: Tree) => {
    const tableIds = tableData.value.map(it => it.id)
    return tableIds.includes(node.id)
  },
}
let checkedKeys = ref<string[]>([])
let treeData = ref<Tree[]>([])

// 默认获取同步树数据
const initTree = () => {
  loading.value = true
  const params = {
    permissionGroup: 0
  }
  request.post('/sys/user/groupOfUser.json', params)
  .then(res => {
    if (res?.type) {
      treeData.value = res?.children || []
    }
    loading.value = false
  })
  .catch(err => {
    treeData.value = []
    loading.value = false
  })
}

const initValue = () => {
  const ids = props.modelValue?.split(',') || []
  if (!ids.length) {
    tableData.value = []
    checkedKeys.value = []
    nowUserNum.value = '0'
    inputValue.value = ''
    return
  }
  const userParams = {
    ids
  }
  request.post('/sys/user/query.json', userParams)
    .then(res => {
      tableData.value = (res || []).map(it => {
        return {
          ...it,
          type: 'user',
          orgName: it.groupName || ''
        }
      })
      checkedKeys.value = (res || []).map(it => it.id)
      nowUserNum.value = checkedKeys.value.length.toString() || '0'
      inputValue.value = (res || []).map(it => it.name).join(',')
    })
    .catch(err => {
      inputValue.value = ''
    })
}

const handleCheckChange = async (
  data: Tree,
  checked: boolean,
  indeterminate: boolean
) => {
  const node = treeRef.value?.getNode(data.id)
  if(checked && !node?.isLeaf) { //节点被选中
    await asyncCheckNodes(node);
  }
}

// 异步节点选中时默认展开
const asyncCheckNodes = async (node: any) => {
  if(node.expanded && !node.isLeaf) {
    //节点已加载，递归节点
    if((node.childNodes || []).length) {
      for(let child of node.childNodes) {
        await asyncCheckNodes(child);
      }
    }
  } else {
    if (node.isLeaf) return
    //节点没有进行过异步加载，强行异步递归加载数据并选中
    let nodes = await reAsyncChildNodesPromise(node)
    if((nodes || []).length) {
      for(let child of nodes) {
        if(!child.isLeaf) {
          await asyncCheckNodes(child);
        }
      }
    }
  }
}

const reAsyncChildNodesPromise = (parentNode: any) => {
  var promise = new Promise(function (resolve, reject) {
    try {
      parentNode.expand(() => {
        resolve(parentNode.childNodes || [])
      })
    } catch (e) {
      reject(e);
    }
  });
  return promise;
}

const filterText = ref('')
const treeRef = ref<TreeInstance>()

// const loadNode: LoadFunction = async(node, resolve) => {
//   if (node.level === 0) {
//     const data = [
//       {
//         "id": "0",
//         "name": "全部",
//         "type": "group",
//         "children": []
//     }]
//     return resolve(data)
//   } else if (node.level === 1) {
//     return resolve(treeData.value)
//   } else if (node.data?.id) {
//     const params = {
//       pid: node.data.id
//     }
//     request.post('/sys/user/groupOfUser.json', params)
//     .then(res => {
//       let data = []
//       if (typeof res === 'object' && res?.length > 0) {
//         data = res
//       }
//       return resolve(data)
//     })
//     .catch(err => {
//       return resolve([])
//     })
//   } else {
//     return resolve([])
//   }
// }


// el-table方法
const userName = ref('')
const multipleSelection = ref<Tree[]>([])
const nowUserNum = ref('0')
const addUserNum = ref('0')

const tableData = ref<Tree[]>([])

const handleSelectionChange = (val: Tree[]) => {
  multipleSelection.value = val
}

// 增加输入框过滤后的tableData
const filterTableData = computed(() => {
  return tableData.value.filter(it => it.name.includes(userName.value))
})

const addUser = () => {
  const checkedNodes = treeRef.value?.getCheckedNodes() || []
  if (checkedNodes.length === 0) {
    return
  }
  const ids = tableData.value.map(it => it.id)
  checkedNodes.forEach(node => {
    if (!ids.includes(node.id)) { //不存在时才去新增或替换
      tableData.value.push(node);
    }
  })
  tableData.value = tableData.value.filter(it => it.type === 'user')
}
const removeUser = () => {
  if (!multipleSelection.value.length) {
    return
  }
  const tableIds = multipleSelection.value.map(it => it.id)
  tableData.value = tableData.value.filter(item => !tableIds.includes(item.id))
  // tree去掉默认选中项
  checkedKeys.value.forEach(it => {
    if (tableIds.includes(it)) {
      checkedKeys.value = checkedKeys.value.filter(key => key !== it)
    }
  })
  // nowUserNum.value = checkedKeys.value.length.toString() || '0'
  if(!tableData.value.length){
    treeRef.value?.setCheckedNodes([])
  } else {
    const tableCheckIds = tableData.value.map(it => it.id)
    treeRef.value?.setCheckedKeys(tableCheckIds, true)
  }
}
const removeAll = () => {
  multipleSelection.value = []
  tableData.value = []
  treeRef.value?.setCheckedNodes([])
  checkedKeys.value = []
  // nowUserNum.value = '0'
}



// 监听 props 变化
import { watch } from 'vue'

watch(
  () => props.modelValue,
  (newValue) => {
    initValue()
  },
  { immediate: true }
)
watch(
  () => tableData.value,
  (newValue) => {
    addUserNum.value = newValue.length.toString() || '0'
  },
)
</script>

<style scoped lang="scss">
.tree-picker-user {
  width: 100%;
  ::v-deep(.tree-picker-user-input) {
    .el-input__wrapper {
      padding-right: 0;
      .icon-clear {
        width: 25px;
        height: 100%;
        padding: 0 10px;
        color: #666666;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .icon-box {
        width: 25px;
        height: 100%;
        padding: 0 10px;
        color: #666666;
        background-color: #F5F7FA;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
    .el-input-group__append {
      padding: 0 10px;
    }
  }
}
</style>
