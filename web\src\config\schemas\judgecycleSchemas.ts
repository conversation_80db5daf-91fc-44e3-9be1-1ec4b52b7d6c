/**
 * 判断/循环组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const conditionSchema: ComponentConfigSchema = {
  componentType: 'condition',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '条件判断的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
  ],

  fields: {
    conditions: {
      type: 'conditions',
      label: '条件分支',
      description: '',
      group: 'basic',
      order: 1,
    },
  },

  presets: {
    simple_condition: {
      label: '简单条件',
      description: '基本条件判断配置',
      config: {
        condition: 'value > 10',
        log_condition: true,
      },
    },

    branch_condition: {
      label: '分支条件',
      description: '带分支的条件判断',
      config: {
        condition: 'status === "success"',
        true_action: 'success_action',
        false_action: 'failure_action',
      },
    },
  },

  examples: [
    {
      title: '数值比较',
      description: '比较数值大小并执行不同分支',
      config: {
        condition: 'score >= 60',
        true_action: 'pass_action',
        false_action: 'fail_action',
      },
    },
    {
      title: '字符串匹配',
      description: '检查字符串匹配并执行不同操作',
      config: {
        condition: 'status === "completed"',
        true_action: 'next_step',
        false_action: 'retry_step',
      },
    },
  ],
}

// ... existing conditionSchema ...

export const forLoopSchema: ComponentConfigSchema = {
  componentType: 'for_loop',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '循环控制的基本参数',
      icon: 'Refresh',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '循环控制的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    loop_type: {
      type: 'select',
      label: '循环类型',
      description: '选择循环的方式',
      group: 'basic',
      order: 1,
      default: 'count',
      options: [
        { label: '固定次数', value: 'count', description: '执行固定次数的循环' },
        { label: '遍历数组', value: 'array', description: '遍历数组中的每个元素' },
        { label: '条件循环', value: 'while', description: '满足条件时持续循环' },
      ],
    },

    loop_count: {
      type: 'number',
      label: '循环次数',
      description: '循环执行的次数',
      placeholder: '10',
      group: 'basic',
      order: 2,
      min: 1,
      max: 10000,
      dependsOn: {
        field: 'loop_type',
        value: 'count',
      },
    },

    loop_array: {
      type: 'textarea',
      label: '遍历数组',
      description: '输入要遍历的数组变量名或JSON数组',
      placeholder: '["item1", "item2"] 或 array_variable',
      group: 'basic',
      order: 3,
      rows: 3,
      dependsOn: {
        field: 'loop_type',
        value: 'array',
      },
    },

    loop_condition: {
      type: 'textarea',
      label: '循环条件',
      description: '循环继续执行的条件表达式',
      placeholder: 'i < 10 或 status === "running"',
      group: 'basic',
      order: 4,
      rows: 3,
      dependsOn: {
        field: 'loop_type',
        value: 'while',
      },
    },

    loop_variable: {
      type: 'string',
      label: '循环变量名',
      description: '存储当前循环索引或元素的变量名',
      placeholder: 'i 或 item',
      group: 'basic',
      order: 5,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    break_condition: {
      type: 'textarea',
      label: '中断条件',
      description: '提前退出循环的条件表达式',
      placeholder: 'i > 5 或 error_occurred',
      group: 'advanced',
      order: 1,
      rows: 2,
    },

    parallel_execution: {
      type: 'boolean',
      label: '并行执行',
      description: '是否并行执行循环体内的操作',
      group: 'advanced',
      order: 2,
      default: false,
    },
  },

  presets: {
    simple_loop: {
      label: '简单循环',
      description: '执行10次的标准循环',
      config: {
        loop_type: 'count',
        loop_count: 10,
        loop_variable: 'i',
      },
    },
    array_loop: {
      label: '数组遍历',
      description: '遍历数组中的每个元素',
      config: {
        loop_type: 'array',
        loop_array: 'items',
        loop_variable: 'item',
      },
    },
  },

  examples: [
    {
      title: '固定次数循环',
      description: '循环执行5次操作',
      config: {
        loop_type: 'count',
        loop_count: 5,
        loop_variable: 'index',
      },
    },
    {
      title: '数组遍历',
      description: '处理数组中的每个元素',
      config: {
        loop_type: 'array',
        loop_array: '["apple", "banana", "orange"]',
        loop_variable: 'fruit',
      },
    },
    {
      title: '条件循环',
      description: '当条件满足时持续循环',
      config: {
        loop_type: 'while',
        loop_condition: 'status === "processing"',
        loop_variable: 'counter',
        break_condition: 'counter > 100',
      },
    },
  ],
}
