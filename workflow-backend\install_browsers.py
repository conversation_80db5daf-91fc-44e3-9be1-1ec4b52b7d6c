#!/usr/bin/env python3
"""
后端浏览器安装脚本
安装浏览器到 workflow-backend/plugins/browsers/ 目录，便于后端打包
"""

import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("开始安装后端浏览器...")
    logger.info("="*60)
    
    try:
        # 导入浏览器管理器
        from utils.browser_manager import install_project_browsers, verify_browser_installation, get_browser_status
        
        # 1. 安装浏览器
        logger.info("安装浏览器到 workflow-backend/plugins/browsers/...")
        if not install_project_browsers():
            logger.error("浏览器安装失败")
            return False
        
        # 2. 验证安装
        logger.info("验证浏览器安装...")
        if not verify_browser_installation():
            logger.error("浏览器验证失败")
            return False
        
        # 3. 显示安装信息
        logger.info("获取安装信息...")
        info = get_browser_status()
        
        logger.info("="*60)
        logger.info("后端浏览器安装完成！")
        logger.info(f"浏览器路径: {info['browsers_path']}")
        logger.info(f"安装状态: {'已安装' if info['installed'] else '未安装'}")
        
        total_size = sum(browser['size_mb'] for browser in info['browsers'])
        logger.info(f"总大小: {total_size:.1f} MB")
        
        logger.info("已安装的浏览器:")
        for browser in info['browsers']:
            logger.info(f"   - {browser['name']}: {browser['size_mb']} MB")
        
        logger.info("现在录制和执行将使用同一个浏览器！")
        logger.info("打包时请包含 workflow-backend/plugins/ 目录")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入浏览器管理器失败: {e}")
        logger.error("请确保在 workflow-backend 目录运行此脚本")
        return False
    except Exception as e:
        logger.error(f"安装过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
