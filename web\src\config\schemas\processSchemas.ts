/**
 * 流程组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const startWorkflowSchema: ComponentConfigSchema = {
  componentType: 'start_workflow',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '流程选择',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'form',
      label: '表单配置',
      description: '填写表单数据的配置',
      icon: 'Edit',
      order: 1,
      collapsible: false,
    },
  ],

  fields: {
    procCode: {
      type: 'select',
      label: '流程选择',
      description: '',
      placeholder: '请选择需要发起的流程',
      required: true,
      group: 'basic',
      order: 1,
      async: true,
      optionsLoader: async () => {
        try {
          // 使用动态导入来避免循环依赖
          // const { request } = await import('@/utils/axios')
          // const options = await request.get('/report-templates/options/list')
          // if (options && typeof options === 'object' && Array.isArray(options)) {
          //   return [ ...options]
          // }
          // return []
          return [
            { label: '流程1', value: 'process1' },
            { label: '流程2', value: 'process2' },
            { label: '流程3', value: 'process3' }
          ]
        } catch (error) {
          console.error('获取模板选项失败:', error)
        }
        return []
      },
      changeEvent: (e, options, emit) => {
        if(!e){
          emit('update:value', 'procName', null)
        }else{
          const selectedOption = options.find(option => option.value === e)
          emit('update:value', 'procName', selectedOption?.label)
        }
      }
    },
    params: {
      type: 'processForm',
      label: '流程表单',
      required: true,
      labelHidden: true,
      group: 'form',
      order: 2,
      default: {},
    }
  },

  presets: {

  },
  examples: [

  ],
}
