*** Settings ***
Documentation    单元测试
...              Generated by WimTask RPA Designer


# RPA Framework 核心库
{#Library          RPA.Browser.Playwright  # 现代Web自动化 (基于Playwright)#}
Library          RPA.Desktop             # 桌面自动化
Library          RPA.Excel.Files         # Excel处理
Library          RPA.Email.ImapSmtp      # 邮件功能
Library          RPA.HTTP                # HTTP/API调用
Library          RPA.Tables              # 数据表处理
Library          RPA.Database            # 数据库操作
Library          RPA.FileSystem          # 文件系统操作

# Robot Framework 内置库
Library          Collections
Library          String
Library          DateTime
Library          OperatingSystem
Library          Process

# excel 相关库
Library    RPA.Excel.Files
Library    openpyxl

# 自定义库
Library          keywords.data_utils
Library          keywords.decode_exec
Library          keywords.sand_box_actuator
Library          keywords.ai_analyze_exec
Library          keywords.text_to_speech
Library          keywords.notifier_send
Library          keywords.get_weather
Library          keywords.image_recognition
Library          keywords.data_forecast
Library          keywords.data_wash
Library          keywords.water_shutoff_valve
Library          keywords.json_extract

# HTTP请求库
Library          RequestsLibrary

# 自定义库
Library          listener.execution_monitor.CustomiseLibrary

*** Variables ***
# Workflow Variables
${TASK_ID}              {{ task_id }}
${ENABLE_NOTIFY}        False

# Default Timeouts
${DEFAULT_TIMEOUT}      30s
${DEFAULT_WAIT}         1s

# Variable Management
&{WORKFLOW_VARIABLES}   # Dictionary to store workflow variables
&{GLOBAL_VARIABLES}     # Dictionary to store global variables
&{LOCAL_VARIABLES}     # Dictionary to store LOCAL variables

*** Test Cases ***

Execute Node
    TRY
        # 处理自定义变量
        {% for idx in range(nodes | length) %}
            {% for key, value in nodes[idx].params.items() %}
                {% if value is string %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t')|replace('#', '\\#') }}
                {% elif value is number %}
            ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value }}
                {% elif value is sameas true or value is sameas false %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|lower }}
                {% elif key == 'json_data' or key == 'headers' %}
        # Handle JSON data and headers as dictionaries
            ${{ '{' }}{{ key }}{{ '}' }}=    Evaluate    {{ value|tojson|replace('false', 'False')|replace('true', 'True')|replace('null', 'None') }}
                {% else %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|string|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t') }}
                {% endif %}
            {% endfor %}
         # 处理内置变量
            {% for key, value in nodes[idx].node.data.config.items() %}
            {% if value is string %}
            {% if key == 'code' and nodes[idx].node.data.componentType in ['python_execute', 'javascript_execute'] %}
        # Python code - use Robot Framework multiline syntax with proper variable handling
        ${{ '{' }}{{ key }}{{ '}' }}=    Catenate    SEPARATOR=\n
            {% for line in value.split('\n') %}
        ...    {{ line|replace('${re}', '"""${re}"""') }}
            {% endfor %}
            {% else %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t')|replace('#', '\\#') }}
            {% endif %}
            {% elif value is number %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value }}
            {% elif value is sameas true or value is sameas false %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|lower }}
            {% elif key == 'json_data' or key == 'headers' %}
        # Handle JSON data and headers as dictionaries
        ${{ '{' }}{{ key }}{{ '}' }}=    Evaluate    {{ value|tojson|replace('false', 'False')|replace('true', 'True')|replace('null', 'None') }}
            {% else %}
        ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|string|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t') }}
            {% endif %}
            {% endfor %}

        # Execute component logic
    {{ processed_components[idx].robot_template | indent(8, True) }}
        TRY

            {{  "${out_vals_list}" }}    Create Dictionary
            {% for out in nodes[idx].node.data.outputs %}
                ...     "{{ out }}"=${{ "{" }}{{ out }}{{ "}" }}
            {% endfor %}
            record_output       ${TASK_ID}          Execute Node         {{  "${out_vals_list}" }}
            {{  "${input_vals_list}" }}    Create Dictionary
            Log    Step completed: {{ nodes[idx].node.data.label }}    INFO
        EXCEPT    AS    ${error}
            Log    Step failed: {{ nodes[idx].node.data.label }} - ${error}    ERROR
            # Handle error based on configuration
            Handle Step Error    {{ nodes[idx].node.data.label }}    ${error}
        END

    {% endfor %}
    EXCEPT    AS    ${error}
        Log    Workflow failed with error: ${error}    ERROR
        Fail    Workflow execution failed: ${error}
    FINALLY
        # Cleanup
        Cleanup Resources
    END
*** Keywords ***


Handle Step Error
    [Documentation]    Handle errors that occur during step execution
    [Arguments]        ${step_name}    ${error_message}

    Log    Error in step ${step_name}: ${error_message}    ERROR

    # You can customize error handling here
    # For now, we'll re-raise the error to stop execution
    Fail    Step ${step_name} failed: ${error_message}

Cleanup Resources
    [Documentation]    Clean up any resources used during workflow execution

    Log    Cleaning up resources...    INFO

    # Close any open browsers
    TRY
        Close Browser    ALL
    EXCEPT
        Log    No browsers to close    DEBUG
    END

    TRY
        Disconnect From Database
        Log    数据库连接已关闭    INFO
    EXCEPT
        Log    数据库未连接或关闭失败，已忽略    DEBUG
    END

    # Add other cleanup tasks as needed
    Log    Cleanup completed    INFO

# Utility Keywords
Wait For Element
    [Documentation]    Wait for an element to be visible with custom timeout
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait Until Element Is Visible    ${locator}    timeout=${timeout}

Safe Click Element
    [Documentation]    Click element with error handling
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    Click Element    ${locator}
    Sleep    ${DEFAULT_WAIT}

Safe Input Text
    [Documentation]    Input text with error handling
    [Arguments]        ${locator}    ${text}    ${clear}=${True}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    Run Keyword If    ${clear}    Clear Element Text    ${locator}
    Input Text    ${locator}    ${text}

Get Element Text Safe
    [Documentation]    Get element text with error handling
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    ${text}=    Get Text    ${locator}
    RETURN    ${text}

Substitute Variables In Text
    [Documentation]    Replace variable references in text with actual values
    [Arguments]        ${text}

    ${result}=    Set Variable    ${text}

    # Get all variables and substitute them
    FOR    ${name}    ${value}    IN    &{WORKFLOW_VARIABLES}
        ${pattern}=    Set Variable    \${${name}}
        ${result}=    Replace String    ${result}    ${pattern}    ${value}
    END

    FOR    ${name}    ${value}    IN    &{GLOBAL_VARIABLES}
        ${pattern}=    Set Variable    \${${name}}
        ${result}=    Replace String    ${result}    ${pattern}    ${value}
    END

    RETURN    ${result}


