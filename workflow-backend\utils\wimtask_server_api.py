
import jwt
import requests
import time
import logging
from listener.execution_monitor import cache
from datetime import datetime
from config.env_config import (get_config_item,DLY_URL,YN_URL,TaskMonitor_Url)
from loguru import logger

# 本地模块导入
from models.workflow import (
    create_execution_id,
)

current_token = None

def forward_get(url,authorization,params):
    current_token = authorization
    headers = {
        "Authorization": authorization
    }
    try:
        response = requests.get(get_config_item(YN_URL)+url,
                                params=params, headers=headers)
        if response.status_code != 200:
            logger.error(f"url:{url},response:{response.text}")
        return response.json()
    except Exception as e:
        logger.error(f"{url},请求失败")
        logger.error("message:{}".str(e))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return None

def forward_post(url,authorization,params):
    global current_token
    current_token = authorization
    headers = {
        "Content-Type": "application/json",
        "Authorization": authorization
    }

    try:
        response = requests.post(get_config_item(YN_URL)+url,
                                json=params, headers=headers)
        if response.status_code != 200:
            logger.error(f"url:{url},response:{response.text}")
        return response.json()
    except Exception as e:
        logger.error(f"{url},请求失败")
        logger.error("message:{}".str(e))
        # return HTTPException(status_code=500, detail=f"config detail异常: {str(e)}")
        return None

# 根据id 查询任务信息
def get_wimtask_by_id(task_id:str,token:str) -> dict:
    return forward_get(f"/wimai/api/task/detail?id={task_id}", token, {})["Response"]

# 根据token 分页查询所有任务
def take_task(token):
    response = forward_post("/wimai/api/task/query",token,{})
    if response:
        return response["Response"]['rows']
    return response


# 修改任务时间
def first_upload_task_update(authorization: str, task_id: str, next_start_time: int):
    data = {
        "id": task_id,
        "nextStartTime": next_start_time
    }
    resp =  forward_post("/wimai/api/task/update", authorization, data)
    if not resp :
        return {"success": False, "error": "失败"}
    return resp



# 修改任务时间
def upload_task_update(authorization: str, task_id: str, next_start_time: int = None,
                       last_execute_time: int = None):
    data = {"id": task_id}
    if next_start_time is not None:
        data["nextStartTime"] = next_start_time
    if last_execute_time is not None:
        data["lastExecuteTime"] = last_execute_time

    resp =  forward_post("/wimai/api/task/update", authorization, data)
    if not resp :
        return {"success": False, "error": "失败"}
    return resp



# 修改任务状态
def upload_task_state(authorization: str, task_id: str, state: int = None):
    data = {"id": task_id,"state":state}
    resp = forward_post("/wimai/api/task/updateState", authorization, data)
    if not resp:
        return {"success": False, "error": "失败"}
    return resp


# 保存任务记录
def save_history(exc_param):
    user = jwt.decode(exc_param["token"], options={"verify_signature": False})
    history_boyd = {
        "id": exc_param["history_id"],
        "missionId": exc_param["task_id"],
        "result": exc_param["status"],
        "executeUser": user["name"],
        "executeTime": exc_param["exc_time"],
        "endTime": int(datetime.now().timestamp()),
        "executeState": 1 if exc_param["status"] == 0 else -1
    }

    resp = forward_post("/wimai/api/taskHistory/history/saveLog", exc_param["token"], history_boyd)


# 发生消息
def send_msg(exc_param:dict,task_id:str,monitor):
    wimtask = get_wimtask_by_id(task_id=task_id,token=exc_param["token"])

    state = 1 if exc_param["status"] == 0 else -1

    if not wimtask:
        return
    #userIds = [wimtask["creatorId"]]
    userIds = []
    if wimtask["reportUser"]:
        report_users = wimtask["reportUser"].split(",")
        if report_users:
            userIds.extend(report_users)
    param = {
        "missionId": task_id,
        "missionExecId": exc_param["history_id"],
        "content": "点击查看执行情况",
        "userIds": userIds,

    }
    if state == 1 :
        param["content"] = "点击查看执行情况"
        param["title"] = f'{wimtask["missionName"]}执行完成'
        param["link"] = f'{get_config_item(YN_URL)}/WimTask/index.html#/taskMonitor?historyId={exc_param["history_id"]}&rightTitle={wimtask["missionName"]}'
    else:
        param["title"] = f'{wimtask["missionName"]}执行失败'
        param["content"] = cache.get_item(f"{task_id}_log")

    headers = {
        "Content-Type": "application/json",
        "Authorization": exc_param["token"]  # "Bearer empty" # 如果需要授权，tokens 从params 获取
    }
    if monitor :
        param["extend"] = monitor.step_history
    else:
        param["extend"] = [{
                "title": "结束",
                "name": "结束",
                "start_time": int(datetime.now().timestamp()),
                "time": int(datetime.now().timestamp()),
                "end": 1,
                "content": "工作流结束节点",
                "status": "failed",
            }]
    resp = requests.post(f"{get_config_item(DLY_URL)}/wimai/api/tool/msgSend", json=param, headers=headers)

def get_online_template(id:str) -> dict:
    return forward_get(f"/wimai/api/office/template/detail?id={id}", current_token, {})

# 发生消息
def send_msg_new(exc_param:dict,task_id:str,monitor):
    wimtask = get_wimtask_by_id(task_id=task_id,token=exc_param["token"])

    state = 1 if exc_param["status"] == 0 else -1

    if not wimtask:
        return
    #userIds = [wimtask["creatorId"]]
    userIds = []
    if wimtask["reportUser"]:
        report_users = wimtask["reportUser"].split(",")
        if report_users:
            userIds.extend(report_users)
    title = ""
    content = ""
    link = ""
    if state == 1 :
        title = f'{wimtask["missionName"]}执行完成'
        content = "点击查看执行情况"
        link = f'{get_config_item(TaskMonitor_Url)}?historyId={exc_param["history_id"]}&missionId={task_id}'
    else:
        title = f'{wimtask["missionName"]}执行失败'
        content = cache.get_item(f"{task_id}_log")
        link = ""

    notifier_send(
        title,
        content,
        userIds,
        "sys",
        link,
        exc_param["token"]
    )


def make_message_request(title, content, users, method, link="",token=""):
    headers = {
        "Authorization": token,
        "content-type": "application/json",
    }

    base_url = f"{get_config_item(DLY_URL)}/uniwater/event/push.json"
    data = make_message_data(title, content, users, method, link)

    return {"url": base_url, "headers": headers, "data": data}

def make_message_data(title, content, users, method, link=""):
    timestamp = time.time()
    data = {
        "sourceid": "WimAI_" + create_execution_id(),
        "type": "WimAI_Task",
        "push": method,
        "title": title,
        "content": content,
        "begin": int(timestamp),
        "popupType": "pop",
        "users": users,
        "link": link
    }
    return data

def notifier_send(title, content, users, method, link, token):
    method = method
    request = make_message_request(title, content, users, method, link, token)
    response = requests.post(
        url=request["url"], headers=request["headers"], json=request["data"]
    )
    if response.status_code == 200:
        logging.info(f"发送消息成功")
        return "ok"
    else:
        logging.info(f"发送消息失败{response.status_code}")
        return response.status_code