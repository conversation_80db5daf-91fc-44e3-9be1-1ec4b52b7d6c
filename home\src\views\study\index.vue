<template>
  <div class="study-page">
    <div class="study-content clearfix">
      <div class="study-content-inner">
        <div class="study-left-catalogue">
          <el-input v-model="keyword" @focus="handleFocus" @blur="handleBlur" :class="{ 'input-focused': isFocused }" class="custom-input" placeholder="搜索" :prefix-icon="Search" />
          <el-scrollbar style="height: calc(100% - 56px)">
            <div class="study-left-catalogue-items">
              <el-tree
                ref="treeRef"
                class="filter-tree"
                :data="treeData"
                highlight-current
                node-key="id"
                @node-click="nodeClick"
                :current-node-key="currentNodeKey"
                :default-expanded-keys="defaultExpandKeys"
                :props="defaultProps"
                :filter-node-method="filterNode"
              />
            </div>
          </el-scrollbar>
        </div>
        <div class="study-description">
          <el-scrollbar style="height:100%;padding:24px;"  ref="contentScrollRef">
            <p class="content-title">{{currentNode?currentNode.title:''}}</p>
            <p class="updated">{{currentNode?moment(currentNode.updated).format('YYYY-MM-DD')+' 更新':''}}</p>
            <div class="ql-editor" v-html="description"></div>
          </el-scrollbar>
        </div>
        <div class="study-right-catalogue">
          <catalogue
              :htmlContent="description"
              :offset="0"
              @scroll-to="contentScrollTo"
              title="目录"
              :container="container"
            ></catalogue>
        </div>
      </div>
    </div>
    <!-- 底部信息模块 -->
    <footer class="footer-section">
        <div class="container">
            <!-- <div class="footer-main">
                <div class="footer-left">
                    <div class="footer-left-title">联系我们</div>
                    <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                    <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                    <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                </div>
                <div class="footer-middle">
                    <p>一诺数字助理</p>
                    <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                        class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                    <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                        class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                </div>
                <div class="footer-right">
                    <div class="qrcode-box">
                        <p>一诺APP</p>
                        <div class="qrcode">
                            <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                        </div>
                    </div>
                    <div class="qrcode-box">
                        <p>度量公众号</p>
                        <div class="qrcode">
                            <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                        </div>
                    </div>
                </div>
            </div> -->
            <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
        </div>

    </footer>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import catalogue from '@/components/catalogue/index.vue'
import utils from '@/utils/utils'
import saasApi from '@/api/index'
import { ref, reactive, computed, onMounted,watch,nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElTree,ElScrollbar  } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const keyword = ref('')
const isFixed = ref(true)
let container = ref('')
let classify = ref({})
let treeData = ref([])
let currentNodeKey = ref('')
let currentNode = ref(null)
let defaultExpandKeys = ref([])

const contentScrollRef = ref<InstanceType<typeof ElScrollbar>>()
interface Tree {
  [key: string]: any
}

const isFocused = ref(false);

const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

watch(keyword, (val) => {
  treeRef.value!.filter(val)
})

const nodeClick = (data: Tree) => {
  console.log(data)
  if(!data.children){
    currentNode.value = data
    description.value = data.content
  }
  
}
const filterNode = (value: string, data: Tree) => {
  debugger;
  if (!value) return true
  return data.label.includes(value)
}

const description = ref()
// 定义计算属性，和 Vue2 中 computed 配置项里的函数作用一致
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc' || utils.GetQueryString('uniwim') == 'pc'
})

const isFromPackage = computed(() => {
  return route.query.from === 'package' || utils.GetQueryString('from') == 'package'
})

const token = computed(() => {
  return utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
})
const activeName = ref('study')


const addImageWidthAttribute = (htmlString:string) => {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlString;
    
    // 获取所有img标签
    const images = tempDiv.getElementsByTagName('img');
    
    // 遍历每个img标签
    for (let i = 0; i < images.length; i++) {
        const img = images[i];
        // 检查是否已经有width属性
        if (!img.hasAttribute('width')) {
            // 如果没有，添加width="100%"
            img.setAttribute('width', '100%');
        }
    }
    
    // 返回处理后的HTML字符串
    return tempDiv.innerHTML;
}

const contentScrollTo = (top:number) => {
  ;
  nextTick(()=>{
    // contentScrollRef.value.setScrollTop(top)
    contentScrollRef.value.scrollTo({
        top: top,
        behavior: 'smooth',
      }
    )
  })
  
}

const handleScroll = (event: any) => {
  if (event.target.scrollTop == 0) {
    isFixed.value = true
  } else {
    isFixed.value = false
  }
}

const handleTab = (type: string) => {
  let path = '/homepage'
  if (type.paneName === 'product') {
    path = '/homepage'
  } else if (type.paneName === 'market') {
    path = '/serviceMarket'
  } else if (type.paneName === 'study') {
    path = '/study'
  }
  router.push({
    path: path,
    query: route.query,
  })
}
const jumpToPage = (path: string) => {
  // router.push({
  //     name: path,
  //     query: route.query
  // })
  let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
  if(token){
    route.query.uniwater_utoken = token;
  }
  let url = utils.generateUrlWithQuery(
    'https://www.dlmeasure.com/extends/WimTask/index.html#/manager',
    route.query,
  )
  // window.open(url)
  window.location.href = url
}

const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {
          status:1
        },
        order:[{
          "Field":"sort",
          "Type":1
        }],
        size:Infinity,
        index:1
    }
    saasApi.AIAgentLearningCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
          res.rows.forEach(row=>{
            classify.value[row.id] = row
          })
        } else {
            classify.value = {}
        }
        getContentData()
    })
    .catch((err: any) => {
        classify.value = {}
    })
    .finally(() => {
    })

}
/**
 * 按指定字段对数据进行分组
 * @param {Array} data - 需要分组的原始数据数组
 * @param {String|Function} key - 分组依据的字段名，或返回分组键的函数
 * @param {Boolean} [preserveKeys=false] - 是否保留原始键结构（仅当key为函数时有效）
 * @returns {Object} 分组后的对象，键为分组依据的值，值为对应组的数据数组
 */
function groupBy(data, key, preserveKeys = false) {
  // 校验输入数据
  if (!Array.isArray(data)) {
    throw new TypeError('第一个参数必须是数组');
  }
  
  return data.reduce((result, item) => {
    // 确定分组键
    let groupKey;
    if (typeof key === 'function') {
      // 如果是函数，调用函数获取分组键
      groupKey = key(item);
    } else if (typeof key === 'string') {
      // 如果是字符串，直接使用该字段的值作为分组键
      groupKey = item[key];
    } else {
      throw new TypeError('第二个参数必须是字符串或函数');
    }
    
    // 处理特殊键值（如undefined、null）
    const safeKey = groupKey ?? 'undefined';
    
    // 初始化分组数组（如果不存在）
    if (!result[safeKey]) {
      result[safeKey] = [];
    }
    
    // 将当前项添加到对应分组
    result[safeKey].push(item);
    
    return result;
  }, {});
}
// 聚焦时添加样式标记
const handleFocus = () => {
  isFocused.value = true;
};

// 失焦时移除样式标记
const handleBlur = () => {
  isFocused.value = false;
};
const getContentData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {
          status:1
        },
        size:Infinity,
        index:1
    }
    saasApi.AIAgentLearningContentQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
          res.rows.forEach(row=>{
            row.categoryName = classify.value[row.categoryId].name
            row.content = addImageWidthAttribute(row.content);
            row.label = row.title
          })
          let result = groupBy(res.rows,'categoryName')
          ;
          let list = []
          for(let key in result){
            list.push({
              label:key,
              id:key,
              children:result[key]
            })
          }
          treeData.value = list
          console.log(treeData.value,'treeData')
          currentNodeKey.value = list[0].children[0].id
          // setTimeout(()=>{
            // treeRef.value!.setCurrentKey(list[0].children[0].id
            nodeClick(list[0].children[0])
          // },300)
          defaultExpandKeys.value = [list[0].id]
        } else {
          treeData.value = []
        }
    })
    .catch((err: any) => {
        treeData.value = []
    })
    .finally(() => {
    })

}
onMounted(()=>{
  getClassifyData();
  container.value = document.querySelector('.study-description')
})
</script>

<style scoped lang="less">
.banner-top {
        width:100%;
        // width:1400px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        background:#fff;
        // height: 80px;
        margin: 0 auto;
        position:fixed;
        top:0;
        z-index:10;
        transition: background-color 0.3s ease; // 添加过渡效果
        box-shadow: 0 1px 30px 0 #4a5c7a29;
        &.banner-top-fixed{
            background: transparent;
            box-shadow: none;
        }
        &.is-from-package{
            top:42px;
        }

        .banner-left{
            display:flex;
            // height:58px;
            // align-items: center;
            img{
                margin-top:5px;
            }
            .tabs-box{
                display:flex;
                align-items: center;
                height:58px;
            }
            .el-tabs{
                height:40px;
                margin-left:68px;
            }
            /deep/.el-tabs__header{
                margin:0;
            }
            /deep/.el-tabs__nav-wrap::after{
                background:transparent;
            }
            /deep/.el-tabs__item{
                font-size: 16px;
            }
            /deep/.el-tabs__item.is-active{
                font-weight: 700;
                font-size: 16px;
            }
        }
        .banner-right {
            display: flex;
            font-size: 16px;
            align-items: center;
            .logo-tel{
                display:flex;
                align-items: center;
            }
            .menus {
                display: flex;
                margin-left: 50px;

                .menu-item {
                    margin-right: 30px;
                    cursor: pointer;
                    &:last-of-type {
                        margin-right: 0;
                    }
                }
            }
        }

    }
    .banner {
        width: 100%;
        min-width: 1400px;
        height: 500px;
        background: url('../../assets/images/homepage/banner-bg.jpg') no-repeat center top;
        background-size: cover;
        .banner-inner{
            // width:1400px;
            margin:0 auto;
        }

        .banner-desc {
            // height: 132px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            margin: 0 auto;
            // padding-top: 148px;
            padding-top:100px;
            width: 1200px;
            color: #232425;
            display: flex;
            align-items: center;

            b {
                // height: 73px;
                font-family: YouSheBiaoTiHei;
                font-size: 56px;
                color: #222222;
                background: -webkit-linear-gradient(left, #ff7e5f, #feb47b);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-fill-color: transparent;
                text-shadow: 5px 8px 9px #0054d238;
            }
            img{
                // height:132px;
            }
        }

        .banner-button {
            width: 200px;
            height: 64px;
            box-sizing: border-box;
            background: url('../../assets/images/homepage/banner-button.png') no-repeat center;
            background-size: cover;
            margin: 0 auto;
            // margin-top: 97px;
            margin-top:80px;
            cursor: pointer;
            line-height: 64px;
            padding-left: 40px;
            font-size: 20px;
            color: #FFFFFF;
        }
    }
.study-page {
    width:100%;
    height:100%;
    background: #fff;
    .clearfix::after {
        content: "";
        display: table;
        clear: both;
    }
    :deep(.el-tree--highlight-current) .el-tree-node.is-current > .el-tree-node__content{
      background:transparent;
      .el-text{
        color:#0054D2;
      }
    }
    :deep(.el-tree-node > .el-tree-node__children),:deep(.el-tree-node__content){
      // height:32px;
      line-height: 32px;
      height:auto;
      // height:34px;
    }
    :deep(.td-quill-video-overlay){
      display:none
    }
    :deep(.ql-editor) .td-video{
      // display:flex;
    }
  
  .study-content{
    padding-top:60px;
    background: #f5f5f5;
    min-width:1440px;
    height:calc(100% - 48px);
    background: url('../../assets/images/homepage/detail-bg.png') #F5F7FA no-repeat top center;
    background-size: 1920px 577px;
    .study-content-inner{
      width:1400px;
      height:calc(100% - 20px);
      margin:0 auto;
      margin-top:20px;
    }
    :deep(.ql-editor){
      padding:0;
    }
    .study-left-catalogue{
      width: 240px;
      height: 100%;
      padding: 20px;
      // margin-right:20px;
      border-right:1px solid #eee;
      box-sizing: border-box;
      float:left;
      background: #fff;
      :deep(.el-tree-node__children) .el-tree-node {
          height: 32px;
          line-height: 32px;
          .el-tree-node__content{
            height:32px;
          }
      }
      :deep(.el-tree-node__label){
        font-weight:500
      }
      :deep(.el-tree-node__children) .el-text{
        // font-size:12px;
        color:#5C5F66;
        font-weight:normal;
      }
      .custom-input{
        margin-bottom: 16px;
        :deep(.el-input__inner){
          font-family: SourceHanSansSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #BCBFC3;
        }
        :deep(.el-input__wrapper) .el-input__prefix-inner .el-icon{
          font-size:12px;
        }
        :deep(.el-input__wrapper.is-focus){
          border-bottom:1px solid #0054D2;
        }
        :deep(.el-input__wrapper.is-focus) .el-input__prefix-inner .el-icon{
          color:#0054D2;
        }
        :deep(.el-input__wrapper){
          box-shadow: none;
          border-radius:0;
          border-bottom:1px solid #E6E7E9;
        }
      }
    }
    .study-description{
        width:calc(100% - 500px);
        float:left;
        height:100%;
        // padding:24px;
        overflow: hidden;
        overflow-y: auto;
        background: #fff;
        .content-title{
          font-family: SourceHanSansSC-Medium;
          font-weight: bold;
          font-size: 32px;
          color: #000000;
          letter-spacing: 0;
        }
        .updated{
          height: 18px;
          font-family: SourceHanSansSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #BCBFC3;
          margin:10px 0 24px 0;
        }
    }
    .study-right-catalogue{
      float:left;
      width: 240px;
      height: 100%;
      background: #f5f5f5;
      box-sizing: border-box;
    }
  }
  // 底部样式
  .footer-section {
      background-color: #f5f5f6;
      // padding: 20px 0;
      width: 100%;
      min-width: 1400px;
      .container {
          width: 1400px;
          margin: 0 auto;
          // display: flex;
          justify-content: space-between;

          .footer-left-title {
              width: 64px;
              height: 24px;
              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 16px;
              margin-bottom: 12px;
              color: #191919;
          }

          .footer-left {
              p {
                  font-family: SourceHanSansSC-Regular;
                  font-weight: 400;
                  font-size: 14px;
                  color: #595959;
                  line-height: 40px;
                  display: flex;
                  align-items: center;

                  &.address i {
                      background: url('../../assets/images/homepage/position.png') no-repeat;
                      background-size: cover;
                  }

                  &.tel i {
                      background: url('../../assets/images/homepage/telephone.png') no-repeat;
                      background-size: cover;
                  }

                  &.email i {
                      background: url('../../assets/images/homepage/email.png') no-repeat;
                      background-size: cover;
                  }
              }

              i {
                  display: block;
                  width: 14px;
                  height: 14px;
                  margin-right: 10px;

              }
          }

          >div {
              flex: 1;
              margin-right: 20px;

              &:last-child {
                  margin-right: 0;
              }


          }
      }

      .footer-main {
          width: 100%;
          display: flex;
          justify-content: space-between;
      }

      .footer-middle {
          height: 144px;

          p {
              height: 24px;
              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 16px;
              color: #191919;
              margin-bottom: 24px;
          }

          .download-btn {
              width: 149px;
              height: 38px;
              line-height: 38px;
              text-align: center;
              border: 1px solid #0054d2;
              border-radius: 4px;
              border-radius: 4px;
              text-decoration: none;
              display: flex;
              align-items: center;
              text-align: center;
              box-sizing: border-box;
              padding: 0 10px;
              // justify-content: center;
              margin-bottom: 20px;
              color: #0054d2;

              &:hover {
                  opacity: 0.8;
              }

              i {
                  display: block;
                  margin-right: 10px;
              }

              .ios-icon {
                  width: 14px;
                  height: 14px;
                  background: url("../../assets/images/homepage/ios.png") no-repeat;
                  background-size: cover;
              }

              .windows-icon {
                  width: 14px;
                  height: 14px;
                  background: url("../../assets/images/homepage/win.png") no-repeat;
                  background-size: cover;
              }
          }
      }

      .footer-right {
          height: 144px;
          font-weight: 400;
          font-size: 16px;
          color: #191919;
          display: flex;
          gap: 30px;
      }

      .qrcode-box {
          p {
              font-weight: 400;
              font-size: 16px;
              color: #191919;
              height: 24px;
              margin-bottom: 15px;
              text-align: center;
          }
      }

      .qrcode {
          width: 118px;
          height: 118px;
          background: #ffffff00;
          border: 1px solid #DEDEDE;
          border-radius: 2px;
          text-align: center;

          img {
              width: 100px;
              height: 100px;
              display: block;
              margin-top: 9px;
              margin-left: 9px;
          }
      }

      .copyright {
          width: 100%;
          height:48px;
          line-height:48px;
          text-align: center;
          // margin-top: 75px;
          color: #898e96;
          font-size: 12px;
          position: inherit;
          a{
            color: #898e96;
            text-decoration: none;
          }
      }
  }
}
</style>