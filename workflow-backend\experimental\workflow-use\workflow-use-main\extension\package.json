{"name": "browser-use-workflow-recorder", "description": "record and enchance workflows for browser use", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@headlessui/react": "^2.2.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/vite": "^4.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.507.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rrweb": "^2.0.0-alpha.4", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@types/chrome": "^0.0.318", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@wxt-dev/module-react": "^1.1.3", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3", "wxt": "^0.20.6"}}