<template>
  <form action="/" class="search-form">
    <div class="search-input">
      <van-search
        v-model="searchValue"
        left-icon=""
        show-action
        placeholder="请输入关键词"
        @search="onSearch"
      >
        <template #action>
          <div @click="onClickButton">搜索</div>
        </template>
      </van-search>
    </div>
  </form>
</template>

<script setup lang="ts">
import {ref, emit} from 'vue'

const emit = defineEmits(['search'])


// 搜索相关
const searchValue = ref<string>('');
const onSearch = () => {
  emit('search', searchValue.value);
};
const onClickButton = () => {
  onSearch()
}

</script>

<style scoped lang="scss">
.search-form {
  border-bottom: 1px solid #F1F3F5;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 1px 12px rgba(239,239,239,0.96);
  z-index: 1;
  position: relative;

  .search-input{
    margin: 0 16px;
    padding: 10px 0;
  }
  :deep(.van-search) {
    padding: 0;
    border: 1px solid #0054D2;
    border-radius: 8px;
    .van-search__content{
      background: none;
    }
    .van-field__control{
      &::placeholder {
        color: #999999;
      }
    }
    .van-search__action{
      background: #0054D2;
      border-radius: 4px;
      color: #fff;
      margin: 3px;
      line-height: calc(var(--van-search-input-height) - 6px);
      height: calc(var(--van-search-input-height) - 6px);
    }
  }
}
</style>
