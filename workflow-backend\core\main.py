import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional

from loguru import logger
from nanoid import generate
from pydantic import BaseModel

from actions import manager

from core.executor import (
    ExecutionContext,
    ActionGroup,
    ActionManager,
    AgentExecutor,
    ActionContext,
)
from core.workflow_transfer import WorkflowTransfer
from models.workflow import WorkflowData

group = ActionGroup()

group2 = ActionGroup()


@group.action(type="xxx", label="xxx")
def test(context: ActionContext, config: Dict, options: Dict):
    logger.info(f"xxx已经执行 {config} {options}")


@group2.action(type="xx", label="xx")
def test(context: ActionContext, config: Dict, options: Dict):
    context.send_data({"value": "xx"})
    logger.info(f"xx已经执行 {config} {options}")


def sinple_test():
    actions = ActionManager()

    actions.include(group)
    actions.include(group2)

    ctx = ExecutionContext()

    actions.get("xx").run(ctx, {"name": "testaaa"}, {"value": 1})

    actions.get("xxx").run(ctx, {"name": "testxxx"}, {"value": 3})

    logger.info("here")


def read_json_from_file(p: str) -> str:
    try:
        with open(p, "r", encoding="utf-8") as f:
            # 解析JSON文件内容为字典
            return f.read()
    except FileNotFoundError:
        raise FileNotFoundError(f"JSON文件不存在: {p}")
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"JSON格式错误 in {p}: {str(e)}", e.doc, e.pos)
    except IOError as e:
        raise IOError(f"读取文件失败 {p}: {str(e)}")


class ExecuteWorkflowRequest(BaseModel):
    workflow: WorkflowData
    taskId: str
    options: Optional[Dict[str, Any]] = None


if __name__ == "__main__":

    json_str = read_json_from_file("./test_json2.json")

    req = ExecuteWorkflowRequest.model_validate_json(json_data=json_str)

    executor = AgentExecutor(manager.get)

    execution_id = generate()

    agent = WorkflowTransfer.transfer(manager, req.workflow, req.taskId)

    async def run():
        await executor.execute(agent, execution_id)

    asyncio.run(run())

    logging.info("完成")

    time.sleep(5)
