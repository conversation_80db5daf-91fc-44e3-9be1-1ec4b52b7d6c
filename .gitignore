web/components.d.ts
/web/node_modules/
/home/<USER>/
/node_modules/
/WimTask/
/yarn.lock
workflow-backend/**/__pycache__/
workflow-backend/api/__pycache__/
workflow-backend/api/utils/__pycache__/
workflow-backend/services/__pycache__/
workflow-backend/utils/__pycache__/
.idea/
workflow-backend/logs/
workflow-backend/outputs/
workflow-backend/build/
workflow-backend/dist/
workflow-backend/data/
workflow-backend/package/
workflow-backend/listener.execution_monitor/
workflow-backend/main.spec
workflow-backend/config.yaml
*.log
workflow-backend/listener.execution_monitor/**
workflow-backend/plugins/browsers/
workflow-backend/plugins/browser_data/
workflow-backend/plugins/user_data_dir/
workflow-backend/plugins/node/
workflow-backend/config/version.json
workflow-backend/build_and_pack.bat
package-lock.json
workflow-backend/experimental/workflow_use/config.json
WimTask
wimtask
node.exe
workflow-backend/web