import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { ElMessageBox } from 'element-plus'

import App from './App.vue'
import router from './router'

// 自定义scss样式
import '@/styles/index.scss'
// 自定义iconfont样式
import '@/assets/fonts/iconfont.css';
// 自定义panel-iconfont样式 左侧面板指令图标
import '@/assets/action-fonts/iconfont.css';
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// Vue Flow styles
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'
import '@vue-flow/node-resizer/dist/style.css'

// 注册配置Schema
import { registerAllSchemas } from './config/schemaRegistry'

import moment from 'moment';
window.moment = moment;

// @ts-ignore
import systemApi from "@/api/system";
// @ts-ignore
import saasApi from '@/api/index';

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)

app.use(ElementPlus, { zIndex: 3000, locale: zhCn })

// 注册配置Schema
registerAllSchemas()

window.addEventListener('unhandledrejection', event => {
  // 捕获动态导入失败的错误

  if (event && event.reason && event.reason.message.includes('Failed to fetch dynamically imported module')) {
    ElMessageBox.confirm(
      `应用已更新，请刷新后继续操作。`,
      `提示`,
      {
        closeOnClickModal:false,
        closeOnPressEscape:false,
        showCancelButton:false,
        showClose:false,
        confirmButtonText: '确定',
        type: 'warning',
      },
    ).then(res=>{
      location.reload()
    })
    // 防止默认处理（如控制台输出）
    event.preventDefault()
  }
})

import { useUserStore } from "@/stores/user";
import { useWebsocketStore } from "@/stores/websocket";
// 初始化SharedWorker 页面全局分发消息
const initWorker = () => {
  if (window.socketWorker) {
    return
  }
  const worker = new SharedWorker(new URL('./workers/socketWorker.ts', import.meta.url), {
    type: 'module',
    name: 'WimTaskSocketWorker' // 添加name确保所有页面使用同一个worker
  });
  worker.port.start();
  // 存储worker引用
  window.socketWorker = worker;
  // 消息监听
  window.socketWorker.port.onmessage = (event) => {
    switch (event.data.type) {
      // 监听websocket消息
      case "WEBSOCKET_MESSAGE":
        // if (sessionStorage.getItem('监听websocket消息') === '1') {
          console.log(event.data.data)
        // }
        break;
      // 获取数据并保存到 websocket 中用于全局使用
      case "REFRESH_WEBSOCKET_STORE":
        useWebsocketStore().initData(event.data.data.data)
        break;
      // 将数据保存到localStorage中。便于下次刷新页面重新获取数据
      case "SAVE_TO_STORAGE":
        localStorage.setItem('WimTaskWebsocketMessages', JSON.stringify(event.data.data))
        break;
    }
  };
  // 页面加载时恢复数据
  const storedData = localStorage.getItem('WimTaskWebsocketMessages')
  if (storedData) {
    worker.port.postMessage({
      type: 'RESTORE_FROM_STORAGE',
      data: JSON.parse(storedData)
    })
  }
  // 处理页面刷新/关闭时的worker清理
  window.addEventListener('beforeunload', () => {
    if (window.socketWorker && Object.keys(window.socketWorker).length === 0) {
      window.socketWorker.port.close();
    }
  });
}

// 新增方法：检查并重新连接WebSocket
const initCheckWebSocketConnection = () => {
  if(window.checkWebSocketConnection) return
  if(!window.socketWorker) return;
  window.checkWebSocketConnection = () => {
    window.socketWorker.port.postMessage({
      type: 'CHECK_WEBSOCKET_CONNECTION'
    });
  }
}

const InitReady = () => {
  let configList = []
  return new Promise(resolve => {
    Promise.all([
      // saasApi.AITaskConfigList(),
      systemApi.initUserInfo(),
    ]).then(([configs]) => {
      if(configs && configs.length){
        configList = configs
        useUserStore().setConfigs(configList);
      }

      // 初始化SharedWorker
      initWorker()
      // 初始化检查并重新连接WebSocket
      initCheckWebSocketConnection()
    }).finally(async ()=>{
      resolve(true)
    })
  })
};

InitReady().then(()=>{
  app.mount('#app')
})
