import multiprocessing
from multiprocessing.queues import Queue

from robot.api import get_model, TestSuite

result_queue = Queue()


def run_task(robot_code: str, run_kwargs: dict):
    p = multiprocessing.Process(target=worker, args=(robot_code, run_kwargs))
    return p


def worker(robot_code: str, run_kwargs: dict) -> None:
    model = get_model(robot_code)
    suite = TestSuite.from_model(model)
    result = suite.run(**run_kwargs)
    result_queue.put(result)
