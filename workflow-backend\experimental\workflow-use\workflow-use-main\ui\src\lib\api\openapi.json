{"openapi": "3.1.0", "info": {"title": "Workflow Execution Service", "version": "0.1.0"}, "paths": {"/api/workflows": {"get": {"summary": "List Workflows", "operationId": "list_workflows_api_workflows_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowListResponse"}}}}}}}, "/api/workflows/{name}": {"get": {"summary": "Get Workflow", "operationId": "get_workflow_api_workflows__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "title": "Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Get Workflow Api Workflows  Name  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/update": {"post": {"summary": "Update Workflow", "operationId": "update_workflow_api_workflows_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/update-metadata": {"post": {"summary": "Update Workflow Metadata", "operationId": "update_workflow_metadata_api_workflows_update_metadata_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowMetadataUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/execute": {"post": {"summary": "Execute Workflow", "operationId": "execute_workflow_api_workflows_execute_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecuteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecuteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/logs/{task_id}": {"get": {"summary": "Get Logs", "operationId": "get_logs_api_workflows_logs__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "position", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Position"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowLogsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/tasks/{task_id}/status": {"get": {"summary": "Get Task Status", "operationId": "get_task_status_api_workflows_tasks__task_id__status_get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflows/tasks/{task_id}/cancel": {"post": {"summary": "Cancel Workflow", "operationId": "cancel_workflow_api_workflows_tasks__task_id__cancel_post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowCancelResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WorkflowCancelResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["success", "message"], "title": "WorkflowCancelResponse"}, "WorkflowExecuteRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "inputs": {"type": "object", "title": "Inputs"}}, "type": "object", "required": ["name", "inputs"], "title": "WorkflowExecuteRequest"}, "WorkflowExecuteResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "task_id": {"type": "string", "title": "Task Id"}, "workflow": {"type": "string", "title": "Workflow"}, "log_position": {"type": "integer", "title": "Log Position"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["success", "task_id", "workflow", "log_position", "message"], "title": "WorkflowExecuteResponse"}, "WorkflowListResponse": {"properties": {"workflows": {"items": {"type": "string"}, "type": "array", "title": "Workflows"}}, "type": "object", "required": ["workflows"], "title": "WorkflowListResponse"}, "WorkflowLogsResponse": {"properties": {"logs": {"items": {"type": "string"}, "type": "array", "title": "Logs"}, "position": {"type": "integer", "title": "Position"}, "log_position": {"type": "integer", "title": "Log Position"}, "status": {"type": "string", "title": "Status"}, "result": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Result"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["logs", "position", "log_position", "status"], "title": "WorkflowLogsResponse"}, "WorkflowMetadataUpdateRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["name", "metadata"], "title": "WorkflowMetadataUpdateRequest"}, "WorkflowResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["success"], "title": "WorkflowResponse"}, "WorkflowStatusResponse": {"properties": {"task_id": {"type": "string", "title": "Task Id"}, "status": {"type": "string", "title": "Status"}, "workflow": {"type": "string", "title": "Workflow"}, "result": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Result"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["task_id", "status", "workflow"], "title": "WorkflowStatusResponse"}, "WorkflowUpdateRequest": {"properties": {"filename": {"type": "string", "title": "Filename"}, "nodeId": {"type": "integer", "title": "Nodeid"}, "stepData": {"type": "object", "title": "Stepdata"}}, "type": "object", "required": ["filename", "nodeId", "stepData"], "title": "WorkflowUpdateRequest"}}}}