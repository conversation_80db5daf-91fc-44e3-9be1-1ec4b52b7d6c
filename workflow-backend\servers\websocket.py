import json
import multiprocessing
import asyncio
import socket
import sys
from pathlib import Path

import websockets
import time
from typing import Optional, Dict, Tuple
import uuid
import threading

from loguru import logger


def setup_subprocess_logger():
    logger.remove()

    if sys.stdout is not None:  # 检查stdout是否存在
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
        )

    # 添加文件输出
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)

    logger.add(
        log_dir / "Websocket.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="20 MB",
        retention="1 days",
        compression="zip",
    )

    return logger


class WebSocketServerWithClients:
    def __init__(self, host: str = "127.0.0.1", port: int = 8765):
        self.host = host
        self.port = port
        self.process: Optional[multiprocessing.Process] = None

        # 进程间通信队列
        self.cmd_queue = multiprocessing.Queue()  # 主进程 -> 子进程（命令）
        self.status_queue = multiprocessing.Queue()  # 子进程 -> 主进程（状态通知）

    def _subprocess_server(self):
        sub_logger = setup_subprocess_logger()
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        sub_logger.info(f"websocket子进程 {multiprocessing.current_process().pid} 启动")

        # 客户端连接管理（子进程内维护）
        # 格式：client_id -> WebSocket连接对象
        clients: Dict[str, websockets.ServerConnection] = {}
        # 客户端元数据：client_id -> (ip, port, 连接时间)
        client_metadata: Dict[str, Tuple[str, int, float]] = {}

        # 向所有客户端广播消息
        async def broadcast(message: str, exclude_client_id: str = None):
            """发送消息到所有连接的客户端，可排除特定客户端"""
            if not clients:
                return

            if len(message) > 1024 * 1024:
                logger.info(
                    f"广播数据大于1MB {multiprocessing.current_process().pid} {len(message)}"
                )

            # 复制客户端列表以避免迭代中修改
            for client_id, websocket in list(clients.items()):
                if client_id == exclude_client_id:
                    continue
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    sub_logger.info(f"客户端 {client_id} 已断开，广播失败")
                    if client_id in clients:
                        del clients[client_id]
                    if client_id in client_metadata:
                        del client_metadata[client_id]
                except websockets.exceptions.PayloadTooBig:
                    sub_logger.info(f"发送数据太大 {client_id} {len(message)}")
                except Exception as e:
                    sub_logger.info(f"发送数据错误  {client_id} {e}")

        # 处理客户端连接
        async def handle_client(websocket: websockets.ServerConnection):

            # 生成唯一客户端ID
            client_id = str(uuid.uuid4())[:8]
            ip, port = websocket.remote_address
            connect_time = time.time()

            # 记录客户端
            clients[client_id] = websocket
            client_metadata[client_id] = (ip, port, connect_time)
            sub_logger.info(f"新客户端连接：{client_id} ({ip}:{port})")
            # 获取底层 TCP 套接字
            sock = websocket.transport.get_extra_info("socket")
            actual_size = sock.getsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF)
            sub_logger.info(f"新客户端TCP设置前发送缓冲区大小：{actual_size/1024} KB")

            # 调整发送缓冲区为 5MB
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 512 * 1024)
            # 验证设置后的缓冲区大小
            actual_size = sock.getsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF)
            sub_logger.info(f"新客户端TCP设置后发送缓冲区大小：{actual_size/1024} KB")

            if actual_size < 512 * 1024:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 512 * 1024)
            actual_size = sock.getsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF)
            if actual_size < 512 * 1024:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 256 * 1024)
                actual_size = sock.getsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF)
                sub_logger.info(
                    f"新客户端再次设置TCP后发送缓冲区大小：{actual_size/1024} KB"
                )

            # 通知主进程有新客户端连接
            self.status_queue.put(
                {
                    "type": "connected",
                    "client_id": client_id,
                    "ip": ip,
                    "port": port,
                    "time": connect_time,
                }
            )

            try:
                # 发送欢迎消息
                await websocket.send(f"欢迎连接！您的客户端ID：{client_id}")
                # 广播新客户端加入
                await broadcast(
                    f"客户端 {client_id} 已加入", exclude_client_id=client_id
                )

                # 消息处理循环
                async for message in websocket:
                    sub_logger.info(f"收到 {client_id} 的消息：{message}")
                    # 示例：回复确认消息
                    await websocket.send(f"禁止乱发消息：{message}")

            except websockets.exceptions.ConnectionClosed:
                sub_logger.info(f"客户端 {client_id} 断开连接")
            finally:
                # 清理客户端记录
                if client_id in clients:
                    del clients[client_id]
                    del client_metadata[client_id]
                    # 通知主进程客户端断开
                    self.status_queue.put(
                        {
                            "type": "disconnected",
                            "client_id": client_id,
                            "time": time.time(),
                        }
                    )
                    # 广播客户端离开
                    await broadcast(f"客户端 {client_id} 已离开")

        # 处理主进程命令
        async def process_commands():
            while True:
                if not self.cmd_queue.empty():
                    cmd = self.cmd_queue.get()

                    # 发送消息给指定客户端
                    if cmd["type"] == "send_to_client":
                        client_id = cmd["client_id"]
                        message = cmd["message"]
                        if client_id in clients:
                            try:
                                await clients[client_id].send(message)
                                self.status_queue.put(
                                    {"type": "send_success", "client_id": client_id}
                                )
                            except Exception as e:
                                self.status_queue.put(
                                    {
                                        "type": "send_failed",
                                        "client_id": client_id,
                                        "error": str(e),
                                    }
                                )
                        else:
                            self.status_queue.put(
                                {"type": "client_not_found", "client_id": client_id}
                            )

                    # 发送消息给所有客户端
                    elif cmd["type"] == "broadcast":
                        message = cmd["message"]
                        try:
                            await broadcast(message)
                        except Exception as e:
                            sub_logger.error(f"广播消息失败{e}")
                        self.status_queue.put(
                            {"type": "broadcast_complete", "client_count": len(clients)}
                        )

                    # 获取当前客户端列表
                    elif cmd["type"] == "get_clients":
                        client_list = [
                            {
                                "client_id": cid,
                                "ip": meta[0],
                                "port": meta[1],
                                "connect_time": meta[2],
                            }
                            for cid, meta in client_metadata.items()
                        ]
                        self.status_queue.put(
                            {
                                "type": "client_list",
                                "clients": client_list,
                                "count": len(client_list),
                            }
                        )

                    # 关闭服务器命令
                    elif cmd["type"] == "shutdown":
                        sub_logger.info("收到关闭命令")
                        # 先向所有客户端发送关闭通知
                        await broadcast("服务器即将关闭，连接将终止")
                        return

                await asyncio.sleep(0.01)

        # 服务器主逻辑
        async def server_main():
            async with websockets.serve(
                handle_client,
                host=self.host,
                port=self.port,
                ping_interval=20,
                ping_timeout=10,
                max_size=10 * 1024 * 1024,
            ) as server:
                sub_logger.info(
                    f"websocket子进程 WebSocket服务器启动：ws://{self.host}:{self.port}"
                )
                # 同时运行服务器和命令处理
                await asyncio.gather(server.wait_closed(), process_commands())

        try:
            loop.run_until_complete(server_main())
        finally:
            loop.close()
            sub_logger.info(
                f"websocket子进程 {multiprocessing.current_process().pid} 退出"
            )

    def start(self):
        """启动子进程服务器"""
        if self.process and self.process.is_alive():
            logger.info("websocket子进程已在运行")
            return
        self.process = multiprocessing.Process(
            target=self._subprocess_server, name=f"WS-Server-{self.port}"
        )
        self.process.start()

    def stop(self, timeout: int = 10):
        """停止服务器"""
        if not (self.process and self.process.is_alive()):
            logger.info("websocket子进程未在运行")
            return

        # 发送关闭命令
        self.cmd_queue.put({"type": "shutdown"})
        logger.info(f"等待websocket子进程退出（最多 {timeout} 秒）")
        self.process.join(timeout)

        if self.process.is_alive():
            logger.info("websocket子进程退出超时，强制终止")
            self.process.terminate()
            self.process.join(2)

        self.process = None
        logger.info("websocket子进程已停止")

    # 主进程调用的API：向指定客户端发送消息
    def send_to_client(self, client_id: str, message: str):
        """主进程调用：向指定客户端发送消息"""
        if not (self.process and self.process.is_alive()):
            logger.info("websocket服务器未运行，无法发送消息")
            return

        self.cmd_queue.put(
            {"type": "send_to_client", "client_id": client_id, "message": message}
        )

    # 主进程调用的API：向所有客户端广播消息
    def broadcast(self, message: str):
        """主进程调用：向所有连接的客户端发送消息"""
        if not self.process:
            logger.info(f"websocket服务器未初始化，无法广播消息{message}")
            return
        elif not self.process.is_alive():
            logger.info(f"websocket服务器进程is not alive，无法广播消息{message}")
            return

        self.cmd_queue.put({"type": "broadcast", "message": message})

    # 主进程调用的API：获取当前客户端列表
    def get_clients(self) -> dict:
        """主进程调用：获取当前连接的客户端列表"""
        if not (self.process and self.process.is_alive()):
            return {"status": "error", "message": "服务器未运行"}

        # 发送获取客户端列表命令
        self.cmd_queue.put({"type": "get_clients"})

        # 等待响应（超时5秒）
        start_time = time.time()
        while time.time() - start_time < 5:
            if not self.status_queue.empty():
                response = self.status_queue.get()
                if response["type"] == "client_list":
                    return {
                        "status": "success",
                        "client_count": response["count"],
                        "clients": response["clients"],
                    }
            time.sleep(0.1)

        return {"status": "error", "message": "获取客户端列表超时"}

    # 可选：主进程监听状态变化（如客户端连接/断开）
    def start_status_listener(self):
        """启动状态监听线程，处理子进程发送的状态通知"""

        def listener():
            logger.info("websocket状态监听线程启动")
            while self.process and self.process.is_alive():
                if not self.status_queue.empty():
                    status = self.status_queue.get()
                    if status["type"] == "connected":
                        logger.info(
                            f"websocket客户端 {status['client_id']} 已连接 ({status['ip']}:{status['port']})"
                        )
                    elif status["type"] == "disconnected":
                        logger.info(f"websocket客户端 {status['client_id']} 已断开")
                    elif status["type"] == "broadcast_complete":
                        logger.info(
                            f"websocket广播完成，已发送给 {status['client_count']} 个客户端"
                        )
                time.sleep(0.1)
            logger.info("websocket状态监听线程停止")

        threading.Thread(target=listener, daemon=True).start()


_websocket_server = WebSocketServerWithClients(port=12765)


def start_ws_server():
    _websocket_server.start()


def stop_ws_server():
    _websocket_server.stop()


# 在子进程中需要重新设置broadcast实现
class WebSocketBroadcastProxy:

    def __init__(self):
        def _def_broadcast(msg: str):
            _websocket_server.broadcast(msg)

        self._broadcast_func = _def_broadcast

    def set_broadcast_func(self, func):
        self._broadcast_func = func

    def broadcast_data(self, data: Dict):
        msg = json.dumps(data, ensure_ascii=False)
        self._broadcast_func(msg)


ws_broadcast_proxy = WebSocketBroadcastProxy()
