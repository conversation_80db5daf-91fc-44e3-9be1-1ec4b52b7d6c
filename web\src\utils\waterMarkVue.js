const watermark = {}

/**
 *
 * @param {string} id
 * @param {string} strs
 * @param {DOM} container
 */
const setWatermark = (id, strs, container) => {
    if (container === undefined) {
        return
    }

    // 查看页面上有没有，如果有则删除
    if (document.getElementById(id) !== null) {
        const childelement = document.getElementById(id)
        childelement.parentNode.removeChild(childelement)
    }

    var containerWidth = container.offsetWidth // 获取父容器宽
    var containerHeight = container.offsetHeight // 获取父容器高
    container.style.position = 'relative'
    let svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

    // 创建SVG的宽度和高度
    svg.setAttribute('width', '100');
    svg.setAttribute('height', '100');
    svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    // 创建SVG的背景颜色
    let defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    let linearGradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    let stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('style', 'stop-color:rgb(255,0,0);stop-opacity:1');
    let stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    stop2.setAttribute('offset', '100%');
    stop2.setAttribute('style', 'stop-color:rgb(0,0,255);stop-opacity:1');
    linearGradient.appendChild(stop1);
    linearGradient.appendChild(stop2);
    defs.appendChild(linearGradient);
    svg.appendChild(defs);

    // 添加水印内容
    let text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50');
    text.setAttribute('y', '50');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('transform', 'rotate(-30 23 18)');
    text.setAttribute('font-size', '14px');
    text.setAttribute('font-weight', '400');
    text.textContent = strs;
    svg.appendChild(text);


    // 创建一个div元素
    const div = document.createElement('div')
    div.id = id // 设置id
    div.style.pointerEvents = 'none' // 取消所有事件
    div.style.top = '0px'
    div.style.left = '0px'
    div.style.position = 'absolute'
    div.style.zIndex = '999'
    div.style.width = containerWidth + 'px'
    div.style.height = containerHeight + 'px'
    div.style.backgroundRepeat = 'repeat'
    div.style.backgroundPosition = '0 0'
    div.style.opacity = .06
    let url = `data:image/svg+xml,${encodeURIComponent(svg.outerHTML)}`
    div.style.backgroundImage = `url("${url}")`
    container.appendChild(div) // 追加到页面
}

watermark.set = (id, str, container) => {
    setWatermark(id, str, container)
}
// 移除水印方法
watermark.remove = (id,container) => {
    if (document.getElementById(id) !== null) {
       container.removeChild(document.getElementById(id))
    }
}
export default watermark