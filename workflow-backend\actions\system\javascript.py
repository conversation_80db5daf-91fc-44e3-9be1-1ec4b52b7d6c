import os
from typing import Dict, Optional, Tu<PERSON>, Any

import js2py

from actions.system import system_group, logger
from core.executor import ExecutionContext


@system_group.action(
    type="javascript_execute",
    label="执行JavaScript代码",
    description="通过 Node.js 执行 JavaScript 代码片段或脚本文件",
    category="system",
    icon="Code",
    config_schema={
        "code": {"type": "string", "required": False},  # JS 代码片段
        "script_file": {"type": "string", "required": False},  # JS 脚本路径
        "node_path": {"type": "string", "default": "node"},  # Node.js 执行路径
        "timeout": {"type": "number", "default": 60},  # 超时时间（秒）
        "capture_output": {"type": "boolean", "default": True},  # 是否记录控制台输出
        "working_directory": {"type": "string", "required": False},  # 执行目录
        "arguments": {"type": "string", "required": False},  # 传递给脚本的参数
        "output_variable": {"type": "string", "required": False},  # 保存标准输出变量
        "error_variable": {"type": "string", "required": False},  # 保存错误输出变量
        "return_code_variable": {"type": "string", "required": False},  # 保存返回码变量
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
    outputs=["js_output", "return_code", "error_output"],
)
def javascript_execute(context: ExecutionContext, config: Dict, options: Dict):

    code = config.get("code", "")
    script_file = config.get("script_file", "")

    (js_output, stderr, return_code) = _run_javascript(
        code=code, script_file=script_file
    )

    if stderr is None or stderr != "":
        context.action_failed(stderr)
        return

    context.set_variable("js_output", js_output)
    context.set_variable("return_code", return_code)
    context.set_variable("stderr", stderr)

    output_variable = config.get("output_variable", "")
    if output_variable != "":
        context.set_variable(output_variable, js_output)


def _run_javascript(
    code: Optional[str] = None,
    script_file: Optional[str] = None,
    timeout: int = 30,
    capture_output: bool = True,
    working_directory: Optional[str] = None,
    arguments: Optional[list] = None,
    output_value: str = None,
) -> Tuple[str, str, int, Dict[str, Any]]:

    result_vars = {}
    logger.info("开始执行 JavaScript 代码")

    # 参数校验
    code_provided = bool(code and code.strip() and code != "None")
    file_provided = bool(
        script_file and script_file != "None" and os.path.exists(script_file)
    )

    if code_provided and file_provided:
        logger.error("不能同时指定JavaScript代码和脚本文件")
        raise ValueError("不能同时指定JavaScript代码和脚本文件")

    if not code_provided and not file_provided:
        logger.error("必须提供JavaScript代码或脚本文件")
        raise ValueError("必须提供JavaScript代码或脚本文件")

    # 读取JS代码
    js_code = ""
    if file_provided:
        with open(script_file, "r", encoding="utf-8") as f:
            js_code = f.read()
    else:
        js_code = code

    # 执行JS代码
    stdout = ""
    stderr = ""
    return_code = 0

    try:

        # 创建JS执行环境
        context = js2py.EvalJs()

        # 执行代码（使用超时控制）
        js_output = context.eval(js_code)  # 超时单位为毫秒

        logger.info(f"js2py {js_output} 返回码: {return_code}")

        if capture_output:
            logger.info(f"JavaScript 输出: {stdout}")
            if stderr:
                logger.warning(f"JavaScript 错误输出: {stderr}")

        if return_code != 0:
            logger.error(f"JavaScript 执行失败，返回码: {return_code}")
            if stderr:
                logger.error(f"错误信息: {stderr}")
        else:
            logger.info("JavaScript 执行成功")

    except js2py.PyJsException as e:
        error_msg = f"JavaScript 执行错误: {str(e)}"
        logger.error(error_msg)
        stderr = error_msg
        return_code = 1
        raise RuntimeError(error_msg)
    except Exception as e:
        error_msg = f"执行过程出错: {str(e)}"
        logger.error(error_msg)
        stderr = error_msg
        return_code = 1
        raise
    finally:
        pass

    return (js_output, stderr, return_code)
