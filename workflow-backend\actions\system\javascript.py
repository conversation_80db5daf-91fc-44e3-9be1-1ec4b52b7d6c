import os
from typing import Dict, Optional, Tu<PERSON>, Any

import js2py

from actions.system import group, logger
from core.executor import ExecutionContext, ActionContext


@group.action(
    type="javascript_execute",
    label="执行JavaScript代码",
    description="通过 Node.js 执行 JavaScript 代码片段或脚本文件",
    category="system",
    icon="Code",
    config_schema={
        "code": {"type": "string", "required": False},  # JS 代码片段
        "script_file": {"type": "string", "required": False},  # JS 脚本路径
        "node_path": {"type": "string", "default": "node"},  # Node.js 执行路径
        "timeout": {"type": "number", "default": 60},  # 超时时间（秒）
        "capture_output": {"type": "boolean", "default": True},  # 是否记录控制台输出
        "working_directory": {"type": "string", "required": False},  # 执行目录
        "arguments": {"type": "string", "required": False},  # 传递给脚本的参数
        "output_variable": {"type": "string", "required": False},  # 保存标准输出变量
        "error_variable": {"type": "string", "required": False},  # 保存错误输出变量
        "return_code_variable": {"type": "string", "required": False},  # 保存返回码变量
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
    outputs=["js_output", "return_code", "error_output"],
)
def javascript_execute(context: ActionContext, config: Dict):

    code = config.get("code", "")

    (js_output, stderr, return_code) = _run_javascript(code=code)

    if stderr is None or stderr != "":
        context.failed(stderr)
        return

    context.set_variable("js_output", js_output)
    context.set_variable("return_code", return_code)
    context.set_variable("stderr", stderr)

    output_variable = config.get("output_variable", "")
    if output_variable != "":
        context.set_variable(output_variable, js_output)


def _run_javascript(
    code: Optional[str] = None,
) -> Tuple[str, str, int, Dict[str, Any]]:

    result_vars = {}
    logger.info("开始执行 JavaScript 代码")

    js_code = code

    # 执行JS代码
    stdout = ""
    stderr = ""
    return_code = 0

    try:

        # 创建JS执行环境
        context = js2py.EvalJs()

        # 执行代码（使用超时控制）
        js_output = context.eval(js_code)  # 超时单位为毫秒

    except js2py.PyJsException as e:
        error_msg = f"JavaScript 执行错误: {str(e)}"
        logger.error(error_msg)
        stderr = error_msg
        return_code = 1

    except Exception as e:
        error_msg = f"执行过程出错: {str(e)}"
        logger.error(error_msg)
        stderr = error_msg
        return_code = 1
    finally:
        pass

    return (js_output, stderr, return_code)
