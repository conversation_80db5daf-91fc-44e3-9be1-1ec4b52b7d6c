import matplotlib.pyplot as plt
import numpy as np
import base64
from io import BytesIO


def generate_line_chart(data, labels=None, title="折线图", xlabel="X轴", ylabel="Y轴",
                        grid=True, figsize=(10, 6), dpi=100, output_file=None):
    """
    生成折线图并转换为 Base64 编码

    参数:
        data: 数据列表，可以是一维列表（单条折线）或二维列表（多条折线）
        labels: 每条折线的标签列表
        title: 图表标题
        xlabel: X轴标签
        ylabel: Y轴标签
        grid: 是否显示网格
        figsize: 图表尺寸
        dpi: 图像分辨率
        output_file: 可选的输出文件路径，如果提供则保存图像到文件

    返回:
        图像的 Base64 编码字符串
    """
    # 设置中文字体，确保中文正常显示
    plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=figsize, dpi=dpi)

    # 处理单条折线数据
    if isinstance(data[0], (int, float)):
        x = np.arange(len(data))
        ax.plot(x, data, label=labels[0] if labels else None)

    # 处理多条折线数据
    else:
        for i, line_data in enumerate(data):
            x = np.arange(len(line_data))
            label = labels[i] if labels and i < len(labels) else f"数据{i + 1}"
            ax.plot(x, line_data, label=label)

    # 设置图表标题和轴标签
    ax.set_title(title, fontsize=14)
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)

    # 设置网格
    if grid:
        ax.grid(True, linestyle='--', alpha=0.7)

    # 设置图例
    if labels or (isinstance(data[0], list) and len(data) > 1):
        ax.legend()

    # 自动调整布局
    plt.tight_layout()

    # 如果提供了输出文件路径，则保存图像
    if output_file:
        plt.savefig(output_file, format='png')

    # 将图像转换为 Base64
    buffer = BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_data = base64.b64encode(buffer.read()).decode('utf-8')

    # 关闭图形以释放资源
    plt.close(fig)

    return image_data


def main():
    """示例用法"""
    # 示例1：单条折线数据
    single_line_data = [120, 122, 124, 125, 128, 130, 135, 140, 145, 150]

    # 示例2：多条折线数据
    multi_line_data = [
        [120, 122, 124, 125, 128, 130, 135, 140, 145, 150],
        [140, 138, 135, 130, 128, 125, 122, 120, 118, 115]
    ]

    # 生成单条折线图的 Base64
    single_line_base64 = generate_line_chart(
        data=single_line_data,
        title="单条折线示例",
        xlabel="时间（分钟）",
        ylabel="值",
        labels=["数据"]
    )

    # 生成多条折线图的 Base64
    multi_line_base64 = generate_line_chart(
        data=multi_line_data,
        title="多条折线对比示例",
        xlabel="时间（分钟）",
        ylabel="值",
        labels=["上升趋势", "下降趋势"]
    )

    # 打印 Base64 编码的前100个字符（完整输出会很长）
    print(f"单条折线图 Base64（前100个字符）:")
    print(single_line_base64[:100] + "...")

    print(f"\n多条折线图 Base64（前100个字符）:")
    print(multi_line_base64[:100] + "...")

    # 创建 HTML 示例文件
    with open("chart_example.html", "w") as f:
        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <title>折线图示例</title>
</head>
<body>
    <h1>单条折线图</h1>
    <img src="data:image/png;base64,{single_line_base64}" alt="单条折线图">

    <h1>多条折线图</h1>
    <img src="data:image/png;base64,{multi_line_base64}" alt="多条折线图">
</body>
</html>
""")
    print("\n已生成 HTML 示例文件: chart_example.html")


if __name__ == "__main__":
    main()