"""
Workflow-Use 集成接口
提供与现有WimTask系统的无缝集成
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any

from loguru import logger

from .service import WorkflowUseService


class WorkflowUseIntegration:
    """
    Workflow-Use 集成接口
    提供与现有浏览器录制服务兼容的接口
    """

    def __init__(self, enable_ai_fallback: bool = False):
        """
        初始化集成接口

        Args:
            enable_ai_fallback: 是否启用AI回退功能
        """
        self.service = WorkflowUseService(enable_ai_fallback=enable_ai_fallback)
        self.enabled = True

        logger.info("WorkflowUse集成接口初始化完成")

    def is_enabled(self) -> bool:
        """检查集成是否启用"""
        return self.enabled and self.service.adapter.is_available()

    def enable(self):
        """启用集成"""
        self.enabled = True
        logger.info("WorkflowUse集成已启用")

    def disable(self):
        """禁用集成"""
        self.enabled = False
        logger.info("WorkflowUse集成已禁用")

    async def start_recording_session(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动录制会话
        兼容原有的browser_recording_service接口
        """
        if not self.is_enabled():
            return {"success": False, "error": "WorkflowUse集成未启用或不可用"}

        return await self.service.start_recording_session(config)

    async def stop_recording_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        停止录制会话
        兼容原有的browser_recording_service接口
        """
        if not self.is_enabled():
            return {"success": False, "error": "WorkflowUse集成未启用"}

        return await self.service.stop_recording_session(session_id)

    def get_recording_status(self) -> Dict[str, Any]:
        """
        获取录制状态
        兼容原有的browser_recording_service接口
        """
        if not self.is_enabled():
            return {
                "success": False,
                "status": "disabled",
                "message": "WorkflowUse集成未启用",
            }

        return self.service.get_recording_status()

    def get_real_time_operations(self) -> Dict[str, Any]:
        """
        获取实时操作列表
        兼容原有的browser_recording_service接口
        """
        if not self.is_enabled():
            return {"success": False, "operations": [], "total_count": 0}

        # workflow-use使用不同的录制机制，这里返回状态信息
        status = self.service.get_recording_status()
        return {
            "success": True,
            "operations": [],  # workflow-use不提供实时操作列表
            "total_count": 0,
            "recording_type": "workflow-use",
            "status": status.get("status", "unknown"),
        }

    async def pause_recording(self) -> Dict[str, Any]:
        """
        暂停录制
        兼容原有的browser_recording_service接口
        """
        # workflow-use目前不支持暂停/恢复，返回不支持的消息
        return {"success": False, "error": "WorkflowUse暂不支持暂停/恢复功能"}

    async def resume_recording(self) -> Dict[str, Any]:
        """
        恢复录制
        兼容原有的browser_recording_service接口
        """
        # workflow-use目前不支持暂停/恢复，返回不支持的消息
        return {"success": False, "error": "WorkflowUse暂不支持暂停/恢复功能"}

    async def execute_workflow(
        self, workflow_definition: Dict[str, Any], inputs: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行工作流
        新增功能，利用workflow-use的确定性执行能力
        """
        if not self.is_enabled():
            return {"success": False, "error": "WorkflowUse集成未启用"}

        return await self.service.execute_workflow(workflow_definition, inputs)

    def convert_wimtask_workflow(self, wimtask_nodes: List[Dict]) -> Dict[str, Any]:
        """
        将WimTask工作流转换为workflow-use格式
        新增功能，用于格式转换
        """
        if not self.is_enabled():
            return {"success": False, "error": "WorkflowUse集成未启用"}

        return self.service.convert_wimtask_workflow(wimtask_nodes)

    def get_capabilities(self) -> Dict[str, Any]:
        """
        获取集成能力信息
        新增功能，用于前端显示
        """
        return {
            "enabled": self.is_enabled(),
            "features": {
                "deterministic_execution": True,
                "fast_replay": True,
                "ai_fallback": self.service.adapter.enable_ai_fallback,
                "self_healing": False,  # 未来版本可能支持
                "pause_resume": False,  # 当前不支持
                "real_time_operations": False,  # 不同的录制机制
                "format_conversion": True,  # 支持格式转换
                "non_ai_execution": True,  # 支持非AI执行
            },
            "status": self.service.get_recording_status(),
            "adapter_status": self.service.adapter.get_status(),
        }

    async def cleanup(self):
        """清理资源"""
        await self.service.cleanup()
        logger.info("WorkflowUse集成接口已清理")


# 全局集成实例
workflow_use_integration = WorkflowUseIntegration(enable_ai_fallback=True)
