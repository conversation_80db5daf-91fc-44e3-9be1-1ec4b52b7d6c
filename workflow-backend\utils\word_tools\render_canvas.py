import json
import re
import ast
from docx import Document
from docx.shared import Pt, RGBColor, Cm, Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn
from utils.word_tools.canvas_template_renderer import render_canvas_json_with_context



def load_canvas_json(json_path):
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def parse_rgb_color(color_str):
    if not color_str or not color_str.startswith("rgb"):
        return None
    nums = re.findall(r"\d+", color_str)
    if len(nums) == 3:
        r, g, b = map(int, nums)
        return RGBColor(r, g, b)
    return None


def apply_text_style(run, style, default_size=16, default_font="Microsoft YaHei"):
    if not style:
        return
    if style.get("bold"):
        run.bold = True
    if style.get("italic"):
        run.italic = True
    if style.get("underline"):
        run.underline = True
    # 映射表：canvas size (px) → Word pt
    canvas_size_to_pt = {
        56: 42, 48: 36, 34: 26, 32: 24, 29: 22, 24: 18, 21: 16,
        20: 15, 18: 14, 16: 12, 14: 10.5, 12: 9, 10: 7.5,
        8: 6.5, 7: 5.5, 6: 5
    }
    raw_size = style.get("size", default_size)
    size_pt = canvas_size_to_pt.get(int(raw_size), default_size)
    run.font.size = Pt(size_pt)

    run.font.name = default_font
    run._element.rPr.rFonts.set(qn('w:eastAsia'), default_font)

    color = parse_rgb_color(style.get("color", ""))
    if color:
        run.font.color.rgb = color


def add_table_to_doc(doc, block):
    if isinstance(block.get("value"), dict) and "headers" in block["value"]:
        _add_simple_table(doc, block)
    else:
        _add_rich_table(doc, block)


def _add_rich_table(doc, block):
    tr_list = block.get("trList", [])
    colgroup = block.get("colgroup", [])
    col_count = len(colgroup) if colgroup else max(len(tr.get("tdList", [])) for tr in tr_list)

    table = doc.add_table(rows=len(tr_list), cols=col_count)
    table.style = 'Table Grid'

    for row in table.rows:
        for cell in row.cells:
            cell.text = ""

    merged_cells = set()

    for r_idx, tr in enumerate(tr_list):
        td_list = tr.get("tdList", [])
        c_idx = 0
        for td in td_list:
            while (r_idx, c_idx) in merged_cells:
                c_idx += 1

            colspan = td.get("colspan", 1)
            rowspan = td.get("rowspan", 1)
            values = td.get("value", [])

            cell = table.cell(r_idx, c_idx)
            para = cell.paragraphs[0]

            for run_data in values:
                text = run_data.get("value", "")
                run_obj = para.add_run(text)
                apply_text_style(run_obj, run_data)

            if colspan > 1:
                end_cell = table.cell(r_idx, c_idx + colspan - 1)
                cell.merge(end_cell)
                for cc in range(c_idx + 1, c_idx + colspan):
                    merged_cells.add((r_idx, cc))
            if rowspan > 1:
                end_cell = table.cell(r_idx + rowspan - 1, c_idx)
                cell.merge(end_cell)
                for rr in range(r_idx + 1, r_idx + rowspan):
                    merged_cells.add((rr, c_idx))

            c_idx += colspan


def _add_simple_table(doc, block):
    value = block["value"]
    headers = value.get("headers", [])
    rows = value.get("rows", [])

    row_count = len(rows) + 1  # 包括标题行
    col_count = len(headers)

    table = doc.add_table(rows=row_count, cols=col_count)
    table.style = 'Table Grid'

    # 写入表头
    for c, header in enumerate(headers):
        cell = table.cell(0, c)
        cell.text = header

    # 写入内容
    for r, row_data in enumerate(rows, start=1):
        for c, cell_text in enumerate(row_data):
            table.cell(r, c).text = str(cell_text)

def render_title_block(doc, block, default_size=20, default_font="Microsoft YaHei"):
    value_list = block.get("valueList", [])
    para = doc.add_paragraph()

    para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    for span in value_list:
        run = para.add_run(span.get("value", ""))
        apply_text_style(run, span, default_size, default_font)

    para.paragraph_format.space_after = Pt(12)

def render_text_block(doc, block, default_size=16, default_font="Microsoft YaHei"):
    text = block.get("value", "")
    style = block.get("style", {})

    # 按换行拆段落
    lines = text.split('\n')
    for line in lines:
        p = doc.add_paragraph()
        p.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

        # 简单识别加粗 **...**
        parts = re.split(r'(\*\*.+?\*\*)', line)
        for part in parts:
            if part.startswith('**') and part.endswith('**'):
                run = p.add_run(part[2:-2])
                run.bold = True
            else:
                run = p.add_run(part)

            # 应用字体、大小和颜色（如果你想支持）
            run.font.name = default_font
            run.font.size = Pt(default_size)

        p.paragraph_format.space_after = Pt(6)

def render_list_block(doc, block, default_font="Microsoft YaHei", default_size=12):
    items = block.get("value", [])
    for item in items:
        lines = item.split('\n')
        for idx, line in enumerate(lines):
            p = doc.add_paragraph()
            p.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

            # 如果行是列表项（- 开头），缩进表示
            if line.strip().startswith('- '):
                p.paragraph_format.left_indent = Pt(20)
                text = line.strip()[2:].strip()
            else:
                text = line.strip()

            # 简单识别加粗 **...**
            parts = re.split(r'(\*\*.+?\*\*)', text)
            for part in parts:
                if part.startswith('**') and part.endswith('**'):
                    run = p.add_run(part[2:-2])
                    run.bold = True
                else:
                    run = p.add_run(part)
                run.font.name = default_font
                run.font.size = Pt(default_size)
            p.paragraph_format.space_after = Pt(4)

def split_runs_by_paragraph(runs):
    paragraphs = []
    current_paragraph = []

    for run in runs:
        text = run.get("value", "")
        style = {k: v for k, v in run.items() if k != "value"}

        # 用正则切分 \n 和 \n\n（保留空段）
        parts = re.split(r'(\n)', text)

        for part in parts:
            if part == "\n":
                # 一个换行表示：当前段落结束
                paragraphs.append(current_paragraph)
                current_paragraph = []
            elif part:
                current_paragraph.append({"value": part, **style})

    # 收尾处理
    if current_paragraph:
        paragraphs.append(current_paragraph)

    return paragraphs

def add_image_to_doc(doc, block, max_width_inches=6):
    """
    根据 image block 添加图片到 doc 中
    """
    path = block.get("value")
    if not path:
        return

    try:
        # 插入图片，限制最大宽度（如 6 英寸）
        doc.add_picture(path, width=Inches(max_width_inches))
    except Exception as e:
        print(f"[图片插入失败] {path}: {e}")

def add_runs_as_paragraph(doc, runs, default_size=16, default_font="Microsoft YaHei"):
    if not runs:
        return
    p = doc.add_paragraph()

    # 设置对齐（优先从第一个 run 中读取）
    align = None
    for run in runs:
        align_candidate = run.get("rowFlex", "").lower()
        if align_candidate in ("left", "center", "right"):
            align = align_candidate
            break
    if not align:
        align = "left"

    if align == "center":
        p.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    elif align == "right":
        p.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    else:
        p.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

    for run in runs:
        text = run.get("value", "")
        style = run

        # 段内 \n -> 换行
        subparts = text.split("\n")
        for i, subtext in enumerate(subparts):
            if subtext:
                run_obj = p.add_run(subtext)
                apply_text_style(run_obj, style, default_size, default_font)
            if i < len(subparts) - 1:
                p.add_run().add_break()

def create_word_document(content, output_path, title=None, font_size=12):
    doc = Document()
    # 添加标题
    if title:
        doc.add_heading(title, level=1)

    # 添加正文内容，并设置字体大小
    paragraph = doc.add_paragraph(content)
    for run in paragraph.runs:
        run.font.size = Pt(font_size)

    # 保存文档
    doc.save(output_path)


def render_canvas_to_docx(canvas_json, output_path):
    doc = Document()

    # 获取全局样式配置
    options = canvas_json.get("data", {}).get("options", {})
    default_size = options.get("defaultSize", 16)
    default_font = options.get("defaultFont", "Microsoft YaHei")
    margins = options.get("margins", [50, 50, 70, 50])  # 上右下左(px)

    # 设置页边距
    section = doc.sections[0]
    section.top_margin = Cm(margins[0] / 37.8)
    section.right_margin = Cm(margins[1] / 37.8)
    section.bottom_margin = Cm(margins[2] / 37.8)
    section.left_margin = Cm(margins[3] / 37.8)

    # 解析主内容
    main_blocks = canvas_json.get("data", {}).get("data", {}).get("main", [])

    all_runs = []
    for block in main_blocks:
        t = block.get("type")
        if t == "table":
            if all_runs:
                paragraphs = split_runs_by_paragraph(all_runs)
                for para_runs in paragraphs:
                    add_runs_as_paragraph(doc, para_runs, default_size, default_font)
                all_runs = []
            add_table_to_doc(doc, block)
        elif t == "title":
            if all_runs:
                paragraphs = split_runs_by_paragraph(all_runs)
                for para_runs in paragraphs:
                    add_runs_as_paragraph(doc, para_runs, default_size, default_font)
                all_runs = []

            render_title_block(doc, block, default_size, default_font)
        elif t == "text":
            if all_runs:
                paragraphs = split_runs_by_paragraph(all_runs)
                for para_runs in paragraphs:
                    add_runs_as_paragraph(doc, para_runs, default_size, default_font)
                all_runs = []
            render_text_block(doc, block, default_size, default_font)
        elif t == "list":
            if all_runs:
                paragraphs = split_runs_by_paragraph(all_runs)
                for para_runs in paragraphs:
                    add_runs_as_paragraph(doc, para_runs, default_size, default_font)
                all_runs = []
            render_list_block(doc, block, default_font, default_size)
        elif t == "tab":
            all_runs.append({**block, "value": "\t"})
        elif t == "image":
            if all_runs:
                paragraphs = split_runs_by_paragraph(all_runs)
                for para_runs in paragraphs:
                    add_runs_as_paragraph(doc, para_runs, default_size, default_font)
                all_runs = []
            add_image_to_doc(doc, block)

        # elif t == "chart":
        #     if all_runs:
        #         paragraphs = split_runs_by_paragraph(all_runs)
        #         for para_runs in paragraphs:
        #             add_runs_as_paragraph(doc, para_runs)
        #         all_runs = []
        #
        #     chart_config = block.get("value")
        #     if isinstance(chart_config, str):
        #         try:
        #             chart_config = ast.literal_eval(chart_config)
        #             # chart_config = json.loads(chart_config)
        #         except:
        #             print("警告：图表配置JSON解析失败，跳过该图表")
        #             continue
        #     try:
        #         img_path = draw_chart_from_echarts_config(chart_config)
        #         p = doc.add_paragraph()
        #         r = p.add_run()
        #         r.add_picture(img_path, width=Inches(6))
        #         p.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        #     except Exception as e:
        #         print(f"图表生成失败: {e}")
        else:
            val = block.get("value")
            style = block
            if isinstance(val, list):
                all_runs.extend(val)
            elif isinstance(val, str):
                all_runs.append({"value": val, **style})

    # 渲染最后收尾段落
    if all_runs:
        paragraphs = split_runs_by_paragraph(all_runs)
        for para_runs in paragraphs:
            add_runs_as_paragraph(doc, para_runs, default_size, default_font)

    doc.save(output_path)


if __name__ == "__main__":
    # 加载 canvas JSON 模板 和数据
    canvas_template = load_canvas_json("canvas_template.json")
    context1 = load_canvas_json("data.json")
    context2 = load_canvas_json("data_chart.json")

    context_data = {**context1, **context2}
    # 替换所有 #{...} 变量
    rendered_canvas = render_canvas_json_with_context(canvas_template, context_data)

    # 渲染 docx
    render_canvas_to_docx(rendered_canvas, "output.docx")
