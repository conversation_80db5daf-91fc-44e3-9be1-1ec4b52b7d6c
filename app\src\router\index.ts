import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/workspace',
    },
    {
      path: '/workspace',
      name: 'workspace',
      component: () => import('@/views/workspace/index.vue'),
      meta: {
        title: '首页',
        keepAlive: true,
        scrollTop: 0
      },
    },
    {
      path: '/monitor',
      name: 'monitor',
      component: () => import('@/views/monitor/index.vue'),
      meta: {
        title: '监控',
      },
    }
  ],
})

export default router

