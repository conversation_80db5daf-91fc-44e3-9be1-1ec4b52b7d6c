<template>
  <teleport to="body">
    <div v-if="visible" class="context-menu" :style="{ left: x + 'px', top: y + 'px' }" @click.stop>
      <div class="context-menu-content">
        <div
          v-for="item in items"
          :key="item.action"
          class="context-menu-item"
          :class="{ disabled: item.disabled }"
          @click="handleItemClick(item)"
        >
          <el-icon v-if="item.icon" class="menu-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="menu-label">{{ item.label }}</span>
          <span v-if="item.shortcut" class="menu-shortcut">{{ item.shortcut }}</span>
        </div>

        <div v-if="hasDivider" class="context-menu-divider"></div>

        <div class="context-menu-item" @click="handleItemClick({ action: 'cancel' })">
          <el-icon class="menu-icon">
            <Close />
          </el-icon>
          <span class="menu-label">取消</span>
        </div>
      </div>
    </div>

    <!-- 背景遮罩 -->
    <div v-if="visible" class="context-menu-overlay" @click="handleClose"></div>
  </teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { Close } from '@element-plus/icons-vue'

interface MenuItem {
  label: string
  action: string
  icon?: string
  shortcut?: string
  disabled?: boolean
}

interface Props {
  visible?: boolean
  x: number
  y: number
  items: MenuItem[]
}

interface Emits {
  (e: 'select', action: string): void

  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<Emits>()

const hasDivider = computed(() => props.items.length > 0)

const handleItemClick = (item: MenuItem) => {
  if (item.disabled) return

  if (item.action === 'cancel') {
    emit('close')
  } else {
    emit('select', item.action)
  }
}

const handleClose = () => {
  emit('close')
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.context-menu {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  padding: 4px 0;
  font-size: 14px;
}

.context-menu-content {
  max-height: 300px;
  overflow-y: auto;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
}

.context-menu-item:hover:not(.disabled) {
  background-color: #f5f7fa;
}

.context-menu-item.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.menu-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.menu-label {
  flex: 1;
  white-space: nowrap;
}

.menu-shortcut {
  flex-shrink: 0;
  font-size: 12px;
  color: #909399;
}

.context-menu-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 4px 0;
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

/* 滚动条样式 */
.context-menu-content::-webkit-scrollbar {
  width: 4px;
}

.context-menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.context-menu-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.context-menu-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
