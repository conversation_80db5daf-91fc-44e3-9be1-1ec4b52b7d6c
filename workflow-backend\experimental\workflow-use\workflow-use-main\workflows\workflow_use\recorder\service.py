import os
os.environ['BROWSER_USE_DISABLE_UBLOCK'] = '1'
os.environ['BROWSER_USE_OFFLINE'] = '1'

import warnings
# 更全面的警告过滤
warnings.filterwarnings("ignore", message=".*Failed to setup uBlock Origin.*")
warnings.filterwarnings("ignore", module=".*browser_use.*")
# 在文件顶部添加
from unittest.mock import patch
import urllib.request
import urllib.error
import asyncio
import json
import pathlib
from typing import Optional
from fastapi import Request

import uvicorn
from browser_use import Browser
from browser_use.browser.profile import BrowserProfile
from fastapi import FastAPI
from loguru import logger
from patchright.async_api import async_playwright as patchright_async_playwright
from patchright.async_api import async_playwright as patchright_async_playwright

# Assuming views.py is correctly located for this import path
from workflow_use.recorder.views import (
    HttpRecordingStoppedEvent,
    HttpWorkflowUpdateEvent,
    RecorderEvent,
    WorkflowDefinitionSchema,  # This is the expected output type
)

from utils.path import get_resource_path

# Path Configuration (should be identical to recorder.py if run from the same context)
SCRIPT_DIR = pathlib.Path(__file__).resolve().parent
# 检查扩展目录，优先使用chrome-mv3-dev（开发版本）
EXT_DIR_DEV = (
    SCRIPT_DIR.parent.parent.parent / "extension" / ".output" / "chrome-mv3-dev"
)
EXT_DIR_PROD = SCRIPT_DIR.parent.parent.parent / "extension" / ".output" / "chrome-mv3"
# EXT_DIR = EXT_DIR_DEV if EXT_DIR_DEV.exists() else EXT_DIR_PROD
EXT_DIR = pathlib.Path(get_resource_path("plugins/chrome-mv3"))
# USER_DATA_DIR = SCRIPT_DIR / "user_data_dir"
USER_DATA_DIR = pathlib.Path(get_resource_path("plugins/wimtask_user_data_dir"))


class RecordingService:
    def __init__(self):
        self.event_queue: asyncio.Queue[RecorderEvent] = asyncio.Queue()
        self.last_workflow_update_event: Optional[HttpWorkflowUpdateEvent] = None
        self.browser: Optional[Browser] = None  # 初始化为None

        self.final_workflow_output: Optional[WorkflowDefinitionSchema] = None
        self.recording_complete_event = asyncio.Event()
        self.final_workflow_processed_lock = asyncio.Lock()
        self.final_workflow_processed_flag = False

        self.app = FastAPI(title="Temporary Recording Event Server")
        self.app.add_api_route(
            "/event", self._handle_event_post, methods=["POST"], status_code=202
        )

        # -- DEBUGGING --
        # Turn this on to debug requests
        # @self.app.middleware("http")
        # async def log_requests(request: Request, call_next):
        #     logger.info(f"[Debug] Incoming request: {request.method} {request.url}")
        #     try:
        #         # Read request body
        #         body = await request.body()
        #         logger.info(
        #             f"[Debug] Request body: {body.decode('utf-8', errors='replace')}"
        #         )
        #         response = await call_next(request)
        #         logger.info(f"[Debug] Response status: {response.status_code}")
        #         return response
        #     except Exception as e:
        #         logger.info(f"[Error] Error processing request: {str(e)}")

        self.uvicorn_server_instance: Optional[uvicorn.Server] = None
        self.server_task: Optional[asyncio.Task] = None
        self.browser_task: Optional[asyncio.Task] = None
        self.event_processor_task: Optional[asyncio.Task] = None

    async def _handle_event_post(self, event_data: RecorderEvent):
        if isinstance(event_data, HttpWorkflowUpdateEvent):
            self.last_workflow_update_event = event_data
        await self.event_queue.put(event_data)
        return {"status": "accepted", "message": "Event queued for processing"}

    async def _process_event_queue(self):
        logger.info("[Service] Event processing task started.")
        try:
            while True:
                event = await self.event_queue.get()
                logger.info(f"[Service] Event Received: {event.type}")
                if isinstance(event, HttpWorkflowUpdateEvent):
                    # self.last_workflow_update_event is already updated in _handle_event_post
                    pass
                elif isinstance(event, HttpRecordingStoppedEvent):
                    logger.info(
                        "[Service] RecordingStoppedEvent received, processing final workflow..."
                    )
                    await self._capture_and_signal_final_workflow(
                        "RecordingStoppedEvent"
                    )
                self.event_queue.task_done()
        except asyncio.CancelledError:
            logger.info("[Service] Event processing task cancelled.")
        except Exception as e:
            logger.info(f"[Service] Error in event processing task: {e}")

    async def _capture_and_signal_final_workflow(self, trigger_reason: str):
        processed_this_call = False
        async with self.final_workflow_processed_lock:
            if (
                not self.final_workflow_processed_flag
                and self.last_workflow_update_event
            ):
                logger.info(
                    f"[Service] Capturing final workflow (Trigger: {trigger_reason})."
                )
                self.final_workflow_output = self.last_workflow_update_event.payload
                self.final_workflow_processed_flag = True
                processed_this_call = True

        if processed_this_call:
            logger.info(
                "[Service] Final workflow captured. Setting recording_complete_event."
            )
            self.recording_complete_event.set()  # Signal completion to the main method

            # If processing was due to RecordingStoppedEvent, also try to close the browser
            if trigger_reason == "RecordingStoppedEvent" and self.browser:
                logger.info(
                    "[Service] Attempting to close browser due to RecordingStoppedEvent..."
                )
                try:
                    await self.browser.close()
                    logger.info("[Service] Browser close command issued.")
                except Exception as e_close:
                    logger.info(
                        f"[Service] Error closing browser on recording stop: {e_close}"
                    )

    def mock_urlopen(*args, **kwargs):
        # 检查是否是 uBlock Origin 相关的请求
        if len(args) > 0 and 'ublock' in str(args[0]).lower():
            raise urllib.error.URLError("Network access disabled for uBlock Origin")
        # 对于其他请求，可以允许或阻止
        raise urllib.error.URLError("Network access disabled")

    async def _launch_browser_and_wait(self):
        logger.info(f"BROWSER_USE_DISABLE_UBLOCK = {os.environ.get('BROWSER_USE_DISABLE_UBLOCK', 'Not set')}")
        logger.info(f"BROWSER_USE_OFFLINE = {os.environ.get('BROWSER_USE_OFFLINE', 'Not set')}")
        logger.info(f"[Service] Attempting to load extension from: {EXT_DIR}")
        if not EXT_DIR.exists() or not EXT_DIR.is_dir():
            logger.info(f"[Service] ERROR: Extension directory not found: {EXT_DIR}")
            self.recording_complete_event.set()  # Signal failure
            return

        # Ensure user data dir exists
        USER_DATA_DIR.mkdir(parents=True, exist_ok=True)
        logger.info(f"[Service] Using browser user data directory: {USER_DATA_DIR}")

        # 创建模拟的 uBlock Origin 目录
        ublock_dir = USER_DATA_DIR / "ublock-origin"
        ublock_dir.mkdir(exist_ok=True)

        # 创建模拟的 manifest.json
        manifest_content = '''{
      "manifest_version": 2,
      "name": "uBlock Origin",
      "version": "1.0.0",
      "description": "Mock uBlock Origin extension"
    }'''

        manifest_file = ublock_dir / "manifest.json"
        if not manifest_file.exists():
            with open(manifest_file, 'w') as f:
                f.write(manifest_content)

        def mock_urlopen(*args, **kwargs):
            raise urllib.error.URLError("Network access completely disabled")

        # 使用 patch 包装整个 browser 初始化过程
        with patch('urllib.request.urlopen', side_effect=mock_urlopen):
            try:
                # Create browser profile with extension support
                profile = BrowserProfile(
                    headless=False,
                    user_data_dir=str(USER_DATA_DIR.resolve()),
                    args=[
                        f"--disable-extensions-except={str(EXT_DIR.resolve())}",
                        f"--load-extension={str(EXT_DIR.resolve())}",
                        "--no-default-browser-check",
                        "--no-first-run",
                        "--disable-background-networking",  # 禁用后台网络活动
                        "--disable-default-apps",  # 禁用默认应用
                        "--disable-extensions-http-throttling",  # 禁用扩展HTTP限制
                        "--disable-component-update",  # 禁用组件更新
                        "--disable-background-timer-throttling",  # 禁用后台定时器限制
                        "--disable-renderer-backgrounding",  # 禁用渲染器后台运行
                        "--disable-backgrounding-occluded-windows",  # 禁用后台窗口限制
                        "--disable-sync",  # 禁用同步功能
                        "--disable-features=TranslateUI",  # 禁用翻译功能
                        "--disable-ipc-flooding-protection",  # 禁用IPC洪水保护
                        "--enable-features=NetworkServiceInProcess",  # 启用进程内网络服务
                    ],
                    keep_alive=True,
                )

                # Create and configure browser
                playwright = await patchright_async_playwright().start()
                self.browser = Browser(
                    browser_profile=profile,
                    playwright=playwright
                )

                logger.info("[Service] Starting browser with extensions...")
                await self.browser.start()

                logger.info("[Service] Browser launched. Waiting for close or recording stop...")

                # Wait for browser to be closed manually or recording to stop
                while True:
                    try:
                        # Check if browser is still running by trying to get current page
                        await self.browser.get_current_page()
                        await asyncio.sleep(1)  # Poll every second
                    except Exception:
                        # Browser is likely closed
                        logger.info("[Service] Browser appears to be closed or inaccessible.")
                        break

            except asyncio.CancelledError:
                logger.info("[Service] Browser task cancelled.")
                if self.browser:
                    try:
                        await self.browser.close()
                    except:
                        pass  # Best effort
                raise  # Re-raise to be caught by gather
            except Exception as e:
                logger.info(f"[Service] Error in browser task: {e}")
                # 设置完成事件以避免无限等待
                self.recording_complete_event.set()
            finally:
                logger.info("[Service] Browser task finalization.")
                await self._capture_and_signal_final_workflow("BrowserTaskEnded")

    async def capture_workflow(self) -> Optional[WorkflowDefinitionSchema]:
        logger.info("[Service] Starting capture_workflow session...")
        # Reset state for this session
        self.last_workflow_update_event = None
        # logger.info("[Service] Starting capture_workflow session...  1")
        self.final_workflow_output = None
        # logger.info("[Service] Starting capture_workflow session...  2")
        self.recording_complete_event.clear()
        # logger.info("[Service] Starting capture_workflow session...  3")
        self.final_workflow_processed_flag = False
        # logger.info("[Service] Starting capture_workflow session...  4")

        # Start background tasks
        self.event_processor_task = asyncio.create_task(self._process_event_queue())
        # logger.info("[Service] Starting capture_workflow session...  5")
        self.browser_task = asyncio.create_task(self._launch_browser_and_wait())
        # logger.info("[Service] Starting capture_workflow session...  6")

        log_config = {  # 空日志配置，禁用所有输出
            "version": 1,
            "disable_existing_loggers": True,
            "handlers": {},
            "loggers": {},
        }

        # Configure and start Uvicorn server
        config = uvicorn.Config(
            self.app,
            host="127.0.0.1",
            port=7331,
            log_level="debug",
            loop="asyncio",
            log_config=log_config,
        )
        # logger.info("[Service] Starting capture_workflow session...  7")
        self.uvicorn_server_instance = uvicorn.Server(config)
        # logger.info("[Service] Starting capture_workflow session...  8")
        self.server_task = asyncio.create_task(self.uvicorn_server_instance.serve())
        logger.info("[Service] Uvicorn server task started.")

        try:
            logger.info("[Service] Waiting for recording to complete...")
            await self.recording_complete_event.wait()
            logger.info(
                "[Service] Recording complete event received. Proceeding to cleanup."
            )
        except asyncio.CancelledError:
            logger.info("[Service] capture_workflow task was cancelled externally.")
        finally:
            logger.info("[Service] Starting cleanup phase...")

            # 1. Stop Uvicorn server
            if (
                self.uvicorn_server_instance
                and self.server_task
                and not self.server_task.done()
            ):
                logger.info("[Service] Signaling Uvicorn server to shut down...")
                self.uvicorn_server_instance.should_exit = True
                try:
                    await asyncio.wait_for(
                        self.server_task, timeout=5
                    )  # Give server time to shut down
                except asyncio.TimeoutError:
                    logger.info(
                        "[Service] Uvicorn server shutdown timed out. Cancelling task."
                    )
                    self.server_task.cancel()
                except (
                    asyncio.CancelledError
                ):  # If capture_workflow itself was cancelled
                    pass
                except Exception as e_server_shutdown:
                    logger.info(
                        f"[Service] Error during Uvicorn server shutdown: {e_server_shutdown}"
                    )

            # 2. Stop browser task (and ensure browser is closed)
            if self.browser_task and not self.browser_task.done():
                logger.info("[Service] Cancelling browser task...")
                self.browser_task.cancel()
                try:
                    await self.browser_task
                except asyncio.CancelledError:
                    pass
                except Exception as e_browser_cancel:
                    logger.info(
                        f"[Service] Error awaiting cancelled browser task: {e_browser_cancel}"
                    )

            if self.browser:  # Final check to close browser if still open
                logger.info("[Service] Ensuring browser is closed in cleanup...")
                try:
                    self.browser.browser_profile.keep_alive = False
                    await self.browser.close()
                except Exception as e_browser_close:
                    logger.info(
                        f"[Service] Error closing browser in final cleanup: {e_browser_close}"
                    )
                # self.browser = None

            # 3. Stop event processor task
            if self.event_processor_task and not self.event_processor_task.done():
                logger.info("[Service] Cancelling event processor task...")
                self.event_processor_task.cancel()
                try:
                    await self.event_processor_task
                except asyncio.CancelledError:
                    pass
                except Exception as e_ep_cancel:
                    logger.info(
                        f"[Service] Error awaiting cancelled event processor task: {e_ep_cancel}"
                    )

            logger.info("[Service] Cleanup phase complete.")

        if self.final_workflow_output:
            logger.info("[Service] Returning captured workflow.")
        else:
            logger.info("[Service] No workflow captured or an error occurred.")
        return self.final_workflow_output


async def main_service_runner():  # Example of how to run the service
    service = RecordingService()
    workflow_data = await service.capture_workflow()
    if workflow_data:
        logger.info("\n--- CAPTURED WORKFLOW DATA (from main_service_runner) ---")
        # Assuming WorkflowDefinitionSchema has model_dump_json or similar
        try:
            logger.info(workflow_data.model_dump_json(indent=2))
        except AttributeError:
            logger.info(
                json.dumps(workflow_data, indent=2)
            )  # Fallback for plain dicts if model_dump_json not present
        logger.info("-----------------------------------------------------")
    else:
        logger.info("No workflow data was captured by the service.")


if __name__ == "__main__":
    # This allows running service.py directly for testing
    try:
        asyncio.run(main_service_runner())
    except KeyboardInterrupt:
        logger.info("Service runner interrupted by user.")
