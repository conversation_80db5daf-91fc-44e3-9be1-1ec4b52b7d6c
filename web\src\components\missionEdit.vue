<template>
  <div class="mission-edit-box">
    <el-form :model="form" :show-message="false">
      <el-row :gutter="20">
        <!-- <el-col :span="24">
          <el-form-item prop="missionName" label="任务名称" required :label-line="true">
            <el-input v-model.trim="form.missionName" :maxlength="32" type="text" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item prop="execution" label="执行时间" required :label-line="true" label-width="100%">
            <el-radio-group v-model="form.execution" @change="onExecutionChange($event, form)" size="small">
              <el-radio :value="1">按分钟</el-radio>
              <el-radio :value="2">按小时</el-radio>
              <el-radio :value="3">按天</el-radio>
              <el-radio :value="4">按周</el-radio>
              <el-radio :value="5">指定时间</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item v-if="form.execution === 1" prop="minutes" required>
            <span class="flex-inline-form" style="padding-left: 0;">执行间隔</span>
            <span class="flex-inline-form">(分钟)</span>
            <el-input-number v-model="form.minutes" :min="1" :max="59" clearable />
          </el-form-item>
          <el-form-item v-if="form.execution === 2" prop="hours" required>
            <span class="flex-inline-form" style="padding-left: 0;">执行间隔</span>
            <span class="flex-inline-form">(小时)</span>
            <el-input-number v-model="form.hours" :min="1" :max="23" clearable />
          </el-form-item>
          <el-form-item v-if="form.execution === 3" prop="days" required>
            <span class="flex-inline-form" style="padding-left: 0;">执行间隔</span>
            <span class="flex-inline-form">(天)</span>
            <el-input-number v-model="form.days" :min="1" :max="31" clearable />
          </el-form-item>
          <el-form-item v-if="form.execution === 4" prop="weeks" required>
            <el-checkbox-group v-model="form.weeks" size="small">
              <el-checkbox v-for="(name, index) in weeks" :key="index" :value="String(index + 1)">{{ name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="form.execution === 5" prop="specify" label="选择时间" required
            :label-line="true">
            <el-date-picker style="width: 100%;" v-model="form.specify" type="datetime" format="YYYY-MM-DD HH:mm" placeholder=""
              :disabled-date="specifyTimeOptions" />
          </el-form-item>
        </el-col>
        <template v-if="[1, 2, 3, 4].includes(form.execution)">
          <el-col :span="24">
            <el-form-item prop="missionStartTime" label="开始时间" required
              :label-line="true">
              <el-date-picker style="width: 100%;" v-model="form.missionStartTime" format="YYYY-MM-DD HH:mm:ss" type="datetime" placeholder=""
                :disabled-date="startTimeOptions" @change="onStartTimeChange" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="missionEndTime" label="结束时间" :label-line="true">
              <template #label>
                <span class="flex-inline-form" style="padding-left: 0;">结束时间</span>
              </template>
              <el-date-picker style="width: 100%;" v-model="form.missionEndTime" format="YYYY-MM-DD HH:mm:ss" type="datetime" placeholder=""
                :disabled-date="endTimeOptions" />
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="24">
          <el-form-item prop="reportUser" label="通知对象" :label-line="true">
            <template #label>
              <span class="flex-inline-form" style="padding-left: 0;">通知对象</span>
            </template>
            <tree-picker-user v-model='form.reportUser' title="人员选择" @change="handleReportUserChange"></tree-picker-user>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.missionType === 'dify'">
          <el-form-item prop="appKey" label="密钥" :label-line="true">
            <el-input v-model="form.appKey" type="text" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import type { FilterNodeMethodFunction, TreeInstance, LoadFunction, AutocompleteInstance } from 'element-plus'
import TreePickerUser from '@/components/treePickerUser.vue'
// @ts-ignore
import { request } from '@/utils/axios';
// @ts-ignore
import { utils } from '@/utils/utils';
import moment from "moment";
import { useWorkflowStore } from '@/stores/workflow'

const workflowStore = useWorkflowStore()

const missionData = computed(() => workflowStore.missionData)
const form = ref<any>({})
const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const currentTime = moment(moment().add(-1, 'd').format("YYYY-MM-DD 00:00:00")).valueOf()

const formSet = async (model) => {
  const params = {
    countdown_hours: 0,
    countdown_minutes: 0,
    countdown_seconds: 0,
    days: 1,
    hours: 1,
    minutes: 1,
    weeks: [],
    specify: null,
    execution: 1,
    reportUser: '',
    reportUserName: '',
  }
  let data = JSON.parse(JSON.stringify(model))
  // 设置默认值
  Object.keys(params).forEach((key) => {
    if (!data[key]) {
      data[key] = params[key]
    }
  })
  if (data.missionEndTime === 0) {
    data.missionEndTime = null
  }
  // 返回解析后的cron表达式
  return onCronToModel(data)
}

// cron 格式反向解析
const onCronToModel = (model) => {
  const parts = (model.timeScheduled || '').split(',')
  switch (parts[0]) {
    // 按分钟
    case '1':
      model.execution = 1;
      if (!model.minutes) model.minutes = Number(parts[1])
      break;
    case '2':
      model.execution = 2;
      if (!model.hours) model.hours = Number(parts[1])
      break;
    case '3':
      model.execution = 3;
      if (!model.days) model.days = Number(parts[1])
      break;
    case '4':
      model.execution = 4;
      if (!model.weeks) model.weeks = parts.slice(1).map(week => week)
      break;
    case '5':
      model.execution = 5;
      if (!model.specify) model.specify = Number(parts[1])
      break;
    default:
      model.execution = 1;
      if (!model.minutes) model.minutes = 1
  }
  console.warn({ model })

  return model
}

// 类型切换
const onExecutionChange = (e, model) => {
  if (e === 5 && !model.specify && model.missionStartTime) {
    model.specify = model.missionStartTime
  }
}

const handleReportUserChange = (val) => {
  form.value.reportUserName = val;
}

const specifyTimeOptions = (time) => {
  return time.getTime() <= currentTime;
}
const startTimeOptions = (time) => {
  if (form.value.missionEndTime) {
    return time.getTime() > form.value.missionEndTime || time.getTime() <= currentTime;
  }
  return time.getTime() <= currentTime;
}
const endTimeOptions = (time) => {
  if (form.value.missionStartTime) {
    return time.getTime() <= form.value.missionStartTime || time.getTime() <= currentTime;
  }
  return time.getTime() <= currentTime;
}

const onStartTimeChange = (time) => {
  if (form.value.missionEndTime && time.getTime() > form.value.missionEndTime) {
    form.value.missionStartTime = form.value.missionEndTime
  }
}


onMounted(async() => {
  form.value = JSON.parse(JSON.stringify(await formSet(missionData.value)))
})

onUnmounted(() => {
  
})


// 监听 props 变化
import { watch } from 'vue'

watch(
  () => form.value,
  (newValue) => {
    workflowStore.updateMissionData(newValue)
  },
  {
    deep: true
  }
)
</script>

<style scoped lang="scss">
.mission-edit-box {
  width: 100%;
  .el-form-item {
    display: flex;
    flex-direction: column;
  }
  .flex-inline-form{
    font-size: 12px;
    font-weight: 500;
    color: #5C5F66;
    padding-top: 6px;
    padding-left: 10px;
  }
}
</style>
