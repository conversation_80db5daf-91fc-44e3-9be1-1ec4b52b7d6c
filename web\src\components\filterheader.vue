<template>
  <div class="cue-table-header__filter-container">
    <span class="cue-table-header__filter-container-title">{{ column.label }}</span>
    <el-popover ref="popper" popper-class="cue-table-header__filter-popover" trigger="click" :placement="placement"
      width="250" :visible="visible" @show="onOpen">
      <div class="cue-table-header__filter-popover-body">
        <div class="cue-table-header__filter-popover-sort">
          <el-radio-group v-model="sortType" @change="sortChange">
            <el-radio-button label="ascending">
              <img v-show="sortType != 'ascending'" src="../assets/images/cueImg/ascending.png" alt="">
              <img v-show="sortType == 'ascending'" src="../assets/images/cueImg/ascending2.png" alt="">
              升序
            </el-radio-button>
            <el-radio-button label="descending">
              <img v-show="sortType != 'descending'" src="../assets/images/cueImg/descending.png" alt="">
              <img v-show="sortType == 'descending'" src="../assets/images/cueImg/descending2.png" alt="">
              降序
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-tabs">
          <!-- <el-select v-model="ctype" class="filter-total" v-if="activeName == '1'" @change="ctypeChange">
            <el-option value="1" label='当前页' />
          </el-select> -->

          <el-tabs v-model="activeName" v-loading="loading" @tab-click="tabClick">
            <el-tab-pane label='按内容' name="1">
              <el-input placeholder='请输入搜索的关键字' @input="keyChange" v-model="searchKey">
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="cue-table-header__filter-popover-content">
                <div class="cue-table-header__filter-popover-content-header">
                  <el-checkbox v-model="checked.all" @change="checkAll">全部
                    ({{ getCount }})</el-checkbox>
                </div>
                <div class="cue-table-header__filter-popover-content-body">
                  <el-checkbox-group v-if="ctype == '1' && !virtualized" v-model="checked.checks" @change="checkChange">
                    <el-checkbox v-for="c in contentData" :label="c.Value" :key="c.Value">
                      <el-tooltip v-if="hasHtmlTags(c.Name)" effect="dark" placement="top-start">
                        <template #content>
                          <span v-html="c.Name"></span>
                        </template>
                        <span class="name"><span v-html="c.Name"></span></span>
                      </el-tooltip>
                      <span v-else class="name" :title="c.Name">{{ c.Name }}<span class="total">
                          ({{ c.total }})</span></span>
                    </el-checkbox>
                  </el-checkbox-group>
                  <div v-else ref="scroll" class="scroll-view" :style="scrollStyle" @scroll="handleScroll">
                    <div class="scroll-view-blank" :style="{ height: contentDataAll.length * itemHeight + 'px' }"></div>
                    <div ref="scrollContent" class="scroll-view-content">
                      <el-checkbox v-for="c in contentData" v-model="c.check" :label="c.Value" :key="c.Value"
                        @change="itemChange(c)">
                        <el-tooltip v-if="hasHtmlTags(c.Name)" effect="dark" placement="top-start">
                          <template #content>
                            <span v-html="c.Name"></span>
                          </template>
                          <span class="name"><span v-html="c.Name"></span></span>
                        </el-tooltip>
                        <span v-else class="name" :title="c.Name">{{ c.Name }}<span class="total">
                            ({{ c.total }})</span></span>
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label='按条件' name="2">
              <div class="cue-table-header__filter-popover-container">
                <!-- 条件关系 -->
                <div>
                  <div class="filter-popover-container-title">关系</div>
                  <el-select class="operate-group" v-model="value.Operate" @change="handelOperateChange($event, value)"
                    ref="operateGroup">
                    <template v-if="value.stype === 'date'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="等于" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value=">" v-if="needShowOperate('>', value)" label="大于" />
                      <el-option value="<" v-if="needShowOperate('<', value)" label="小于" />
                      <el-option value=">=" v-if="needShowOperate('>=', value)" label="大于等于" />
                      <el-option value="<=" v-if="needShowOperate('<=', value)" label="小于等于" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                      <el-option value="between" v-if="needShowOperate('between', value)" label="介于" />
                    </template>
                    <!-- <template v-else-if="value.stype === 'codeTree'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="=" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value="like" v-if="needShowOperate('like', value)" label="包含" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                      <el-option v-if="needShowOperate('in', value)" value="in" label="在列表" />
                    </template> -->
                    <!-- <template v-else-if="value.stype === 'codeUserTree'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="=" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                    </template> -->
                    <template v-else-if="value.stype === 'code'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="等于" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value=">" v-if="needShowOperate('>', value)" label="大于" />
                      <el-option value="<" v-if="needShowOperate('<', value)" label="小于" />
                      <el-option value=">=" v-if="needShowOperate('>=', value)" label="大于等于" />
                      <el-option value="<=" v-if="needShowOperate('<=', value)" label="小于等于" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                      <el-option value="between" label="介于"
                        v-if="value.dtype === 'number' && needShowOperate('between', value)" />
                      <el-option v-if="needShowOperate('in', value)" value="in" label="在列表" />
                    </template>
                    <template v-else-if="value.stype === 'number' || value.dtype === 'number'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="等于" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value=">" v-if="needShowOperate('>', value)" label="大于" />
                      <el-option value="<" v-if="needShowOperate('<', value)" label="小于" />
                      <el-option value=">=" v-if="needShowOperate('>=', value)" label="大于等于" />
                      <el-option value="<=" v-if="needShowOperate('<=', value)" label="小于等于" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                      <el-option v-if="value.stype === 'number' && needShowOperate('between', value)" value="between" label="介于" />
                    </template>
                    <template v-else-if="value.stype === 'codeMulti'">
                      <el-option value="=" v-if="needShowOperate('=', value)" label="等于" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value="like" v-if="needShowOperate('like', value)" label="包含" />
                      <el-option value="in" v-if="needShowOperate('in', value)" label="在列表" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                    </template>
                    <template v-else>
                      <el-option value="=" v-if="needShowOperate('=', value)" label="等于" />
                      <el-option value="!=" v-if="needShowOperate('!=', value)" label="不等于" />
                      <el-option value="like" v-if="needShowOperate('like', value)" label="包含" />
                      <el-option value="in" v-if="needShowOperate('in', value)" label="在列表" />
                      <el-option value="nil" v-if="needShowOperate('nil', value)" label="不存在" />
                      <el-option value="nnil" v-if="needShowOperate('nnil', value)" label="存在" />
                      <el-option value="blank" v-if="needShowOperate('blank', value)" label="为空" />
                    </template>
                  </el-select>
                </div>
                <!-- 数值 -->
                <div style="margin-top: 8px">
                  <div class="filter-popover-container-title">数值</div>
                  <div class="value-group" v-if="value.stype === 'date'" @click="handleDatePickerPos">
                    <el-date-picker v-if="value.Operate !== 'between'" clearable v-model="value.Value"
                      :format="GetFormat(value) || 'YYYY-MM-dd'" :type="DatePickerType(value)">
                    </el-date-picker>
                    <template v-else>
                      <div class="value-group-container date-group" style="width: 100%;">
                        <el-date-picker style="width: 100%;" v-if="DatePickerType(value) !== 'year'" start-placeholder="开始日期"
                          end-placeholder="结束日期" range-separator="至" clearable v-model="value.Value"
                          :format="GetFormat(value) || 'YYYY-MM-dd'" :type="DatePickerType(value)">
                        </el-date-picker>
                        <div v-else class="value-between-group">
                          <el-date-picker style="width: 48%;" start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至" clearable
                            v-model="value.Value[0]" :picker-options='DatePickerOption(value.Value[1], "1")'
                            :format="GetFormat(value) || 'YYYY-MM-dd'" :type="DatePickerType(value)">
                          </el-date-picker>
                          <span>-</span>
                          <el-date-picker style="width: 48%;" start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至" clearable
                            v-model="value.Value[1]" :picker-options='DatePickerOption(value.Value[0], "2")'
                            :format="GetFormat(value) || 'YYYY-MM-dd'" :type="DatePickerType(value)">
                          </el-date-picker>
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="value-group" v-else-if="value.stype === 'number'">
                    <div class="value-group-container number-group">
                      <el-input-number clearable v-if="value.Operate !== 'between'" v-model.trim="value.Value[0]" />
                      <div v-else class="value-between-group">
                        <el-input-number clearable v-model.trim="value.Value[0]" />
                        <span>-</span>
                        <el-input-number clearable v-model.trim="value.Value[1]" />
                      </div>
                    </div>
                  </div>
                  <div class="value-group" v-else-if="value.stype === 'code'">
                    <div class="value-group-container number-group">
                      <el-select v-if="value.Operate !== 'between'" clearable :array="value.Operate === 'in'"
                        :multiple="value.Operate === 'in'" v-model.trim="value.Value[0]">
                        <el-option v-for="item in selectSource(value)" :key="item.value" :label="item.label"
                          :value="item.value" />
                      </el-select>
                      <div v-else class="value-between-group">
                        <el-select clearable v-model.trim="value.Value[0]">
                          <el-option v-for="item in selectSource(value)" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                        <span>-</span>
                        <el-select clearable v-model.trim="value.Value[1]">
                          <el-option v-for="item in selectSource(value)" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="value-group" v-else-if="value.stype === 'codeTree'">
                    <div class="value-group-container">
                      <code-tree-select v-model.trim="value.Value" :visible="visible" :array="value.Operate === 'in'"
                        :multiple="value.Operate === 'in'" :data="selectSource(value)"></code-tree-select>
                    </div>
                  </div> -->
                  <!-- <div class="value-group" v-else-if="value.stype === 'codeUserTree'">
                    <div class="value-group-container">
                      <cue-ztree-picker-user v-model="value.Value" clearable :cid="cid" />
                    </div>
                  </div> -->
                  <div class="value-group" v-else-if="value.stype === 'codeMulti'">
                    <div class="value-group-container number-group">
                      <el-select clearable :array="false" :multiple="true" v-model.trim="value.Value">
                        <el-option v-for="item in selectSource(value)" :key="item.value" :label="item.label"
                          :value="item.value" />
                      </el-select>
                    </div>
                  </div>
                  <div class="value-group" v-else>
                    <el-select v-if="value.Operate === 'in'" array v-model.trim="value.Value" multiple filterable
                      allow-create default-first-option>
                      <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-input v-else type="text" v-model.trim="value.Value" clearable />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="cue-table-header__filter-popover-body-footer">
          <span class="header-filter-reset" @click="onReset"><i class="iconfont icon-shuaxin1"
              aria-hidden="true" />重置</span>
          <el-button @click="onConfirm" type="primary"
            :disabled="activeName == '1' && ctype == '2' && !checked.checks.length">确定</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
      <template #reference>
        <div ref="reference" class="cue-table-header__filter-btn" @click.stop.prevent="onFilter()"
          :class="{ 'active': condition || asort, 'hover': visible }">
          <i class="iconfont" :class="btnClass"></i>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import moment from "moment";
import utils from '@/utils/utils'

export default defineComponent({
  name: "FilterHeader",
  componentName: "CueCrudFilterHeader",
  components: {},
  props: {
    type: { type: String, default: "" },
    orderFormat: { type: String, default: "" },
    urlPrefix: { type: String, default: "" },
    bindings: { type: Object, default: null },
    data: { type: Object, default: () => { return {} } },
    UUID: { type: String, default: '' }, // crud唯一标识
    uid: { type: String, default: '' }, // 用户id
    cid: { type: String, default: '' }, // 客户id
    column: { type: Object, default: () => { return {} } },
    col: { type: Object, default: () => { return {} } },
    headerSorts: { type: Object, default: () => { return {} } },
    allRows: { type: Array, default: () => [] },
    crud: { type: Object, default: () => { return {} } },
    virtualized: { type: Boolean, default: false },
    defaultCtype: { type: String, default: '1' },
    placement: { type: String, default: 'bottom' }, // crud唯一标识
  },
  data() {
    return {
      visible: false,
      value: {},
      condition: null,
      sortType: '',
      activeName: '1',
      searchKey: '',
      checked: {
        all: true,
        checks: []
      },
      contentData: [],
      ctype: '1',
      asort: null,
      loading: false,
      itemHeight: 25,
      contentDataTotal: [],
      contentDataAll: [],
      codeTreeMap: {}
    }
  },
  computed: {
    btnClass() {
      if (this.condition) {
        if (this.asort) {
          if (this.asort.Type == 1) {
            return 'icon-shaixuansheng'
          } else {
            return 'icon-shaixuanjiang'
          }
        }
      } else {
        if (this.asort) {
          if (this.asort.Type == 1) {
            return 'icon-shangsheng'
          } else {
            return 'icon-xiajiang'
          }
        }
      }
      return 'icon-shaixuan3'
    },
    getCount() {
      let scount = 0
      let acount = 0
      if (this.ctype == '1' && !this.virtualized) {
        scount = this.contentData.filter(r => this.checked.checks.includes(r.Value)).reduce((p, n) => {
          return p + n.total
        }, 0)
        acount = this.contentData.reduce((p, n) => {
          return p + n.total
        }, 0)
      } else {
        this.contentDataAll.forEach(r => {
          if (r.check) {
            scount += r.total
          }
          acount += r.total
        })
      }
      return `${scount}/${acount}`
    },
    scrollStyle() {
      let h = this.contentDataAll.length * this.itemHeight
      return {
        height: (h > 200 ? 200 : h) + 'px'
      }
    },
    format() {
      return this.orderFormat || (this.$OPTIONS || {}).orderFormat;
    }
  },
  mounted() {
    // 事件总线 绑定接受关闭其他过滤框功能
    // EventBus.$on(`cue-filter-header-${this.UUID}`, ({ type, value }) => {
    //   if (type === 'hide' && value !== this.column.property) {
    //     this.visible = false
    //   } else if (type === 'reset') {
    //     this.onReset(false)
    //   } else if (type === 'refreshcondition') {
    //     if (value.property === this.column.property) {
    //       this.$set(this.data, value.property, value)
    //       this.activeName = value.activeName || this.activeName
    //       this.ctype = value.ctype || this.ctype
    //       this.initVal()
    //     }
    //   } else if (type === 'refreshsort') {

    //   } else if (type === 'resetCondition') {
    //     if (value.property === this.column.property) {
    //       this.onReset(false)
    //     }
    //   }
    // });
    if (this.defaultCtype == '2' && !this.col.hideHeaderFilterAll) {
      this.ctype = '2'
    }

    // document.addEventListener('click', this.handleDocumentClick, false)
  },
  beforeUnmount() {
    // EventBus.$off(`cue-filter-header-${this.UUID}`)

    // document.removeEventListener('click', this.handleDocumentClick, false)
  },
  watch: {
    allRows() {
      if (!this.virtualized) {
        let condition = this.data[this.column.property]
        if (condition && condition.activeName == '1' && condition.ctype == '1') {
          this.condition = null
          setTimeout(() => {
            this.$emit('changeTableData', {
              type: "resetcondition",
              uuid: this.UUID,
              property: this.column.property,
              value: {}
            })
            this.ctypeChange(undefined, true)
          }, 300)
        }
      }
    }
  },
  methods: {
    onOpen() {
      this.initVal()
      this.ctypeChange()
    },
    GetFormat(fmt) {
      if (this.type === 'hd-crud') {
        return fmt.colFormat
      }
      return fmt.colFormat || fmt.format
    },
    initVal() {
      if (this.data && this.data[this.column.property] && this.data[this.column.property].activeName == '2') {
        const _addRow = (data) => {
          this.value = data
        }
        this.condition = this.data[this.column.property].value
        let d = this.condition
        let row = {
          Field: this.col.data || this.col.prop,
          stype: this.col.dtype,
          format: this.col.format,
          colFormat: this.col.colFormat,
          dtype: this.col.filterable,
          Operate: this.condition.Operate,
          Value: this.condition.Value,
          Relation: "and",
          multiple: this.col.multiple,
          array: this.col.array,
          filteroperate: this.col.filteroperate,
          Disabled: this.col.Disabled
        }
        if ("nil" === d.Operate || "nnil" === d.Operate || "nBlank" === d.Operate) {
          if (row.dtype == 'number' || row.dtype == 'string' || row.dtype == true) {
            row.Value = []
          }
          _addRow(row);
          return;
        }
        if ("" === d.Value && "=" === d.Operate) {
          row.Value = null;
          row.Operate = "blank";
          // 特殊处理，code类型
          if (row.stype === "code") {
            row.Value = []
          }
          _addRow(row);
          return;
        }
        if (!d.Value && d.Value !== 0) {
          this.fillRowVal()
          return;
        }
        if (row.stype === "date") {
          if (row.Operate === "between") {
            row.Value = Array.isArray(d.Value) && d.Value.length === 2 ?
              (typeof d.Value[0] === "number" ? [
                new Date(d.Value[0] * 1000),
                new Date(d.Value[1] * 1000)
              ] : [d.Value[0], d.Value[1]]) : null;
          }
          else {
            row.Value = typeof d.Value === "number"
              ? new Date(d.Value * 1000) : d.Value;
          }
        }
        else if (row.stype === "code") {
          if (typeof d.Value === "string" || typeof d.Value === 'number' || row.Operate === 'in') {
            row.Value = d.Value || d.Value === 0 ? [d.Value] : []
          } else {
            row.Value = d.Value
          }
        } else if (row.stype === "codeTree" || row.stype === "codeUserTree") {
          row.Value = d.Value;
        } else if (row.stype === 'number') {
          if (typeof d.Value === "number") {
            row.Value = d.Value || d.Value === 0 ? [d.Value] : []
          } else {
            row.Value = d.Value
          }
        } else {
          row.Value = d.Value;
        }
        _addRow(row);
      } else if (!this.condition) {
        this.fillRowVal()
      }

      if (this.headerSorts[this.column.property]) {
        this.asort = this.headerSorts[this.column.property].value
        this.sortType = this.headerSorts[this.column.property].value.Type == 1 ? 'ascending' : 'descending'
      } else {
        this.sortType = ''
        this.asort = null
      }
    },
    DatePickerType(fmt) {
      let v = this.GetFormat(fmt) || "YYYY-MM-dd";
      if (v.indexOf("H") > 0 && fmt.Operate === "between") {
        return "datetimerange";
      }

      if (v.indexOf("d") > 0 && fmt.Operate === "between") {
        return "daterange";
      }
      if (v.indexOf("M") > 0 && fmt.Operate === "between") {
        return "monthrange";
      }
      if (v.indexOf("H") > 0) {
        return "datetime";
      }
      if (v.indexOf("d") > 0) {
        return "date";
      }
      if (v.indexOf("M") > 0) {
        return "month";
      }
      return "year";
    },
    DatePickerOption(date, type = '1') {
      if (!date) {
        return {}
      } else {
        return {
          disabledDate: (e) => {
            if (date) {
              if (type === '1')
                return date < e
              else
                return date > e
            } else {
              return false
            }
          },
        }
      }
    },
    selectSource(item) {
      const code = this.GetFormat(item)
      if ((this.bindings || {})[code]) {
        return this.bindings[code].map(it => {
          return {
            ...it,
            label: it.Name,
            value: it.Value
          }
        });
      }
      // if (Vue.filter("BINDINGS")) {
      //   return Vue.filter("BINDINGS")(code);
      // }
      return [];
    },
    // 初始化value
    fillRowVal() {
      let value = null
      if (this.col.dtype === 'code') {
        value = []
      }
      if (this.col.filterable && this.col.dtype === 'number') {
        value = []
      }
      if (this.col.filterable === 'number') {
        if (this.col.dtype === 'date' || !this.col.dtype) {
          value = null
        } else {
          value = []
        }
      }
      this.value = {
        Field: this.col.data || this.col.prop,
        stype: this.col.dtype,
        format: this.col.format,
        colFormat: this.col.colFormat,
        dtype: this.col.filterable,
        Operate: this.col.Operate,
        Value: value,
        Relation: "and",
        multiple: this.col.multiple,
        array: this.col.array,
        filteroperate: this.col.filteroperate,
        Disabled: this.col.Disabled
      }
      this.$nextTick(() => {
        if (this.$refs.operateGroup) {
          this.value.Operate = this.$refs.operateGroup.optionsArray[0].value
        }
      })
    },
    // 点击过滤按钮
    onFilter() {
      this.visible = !this.visible
      // EventBus.$emit(`cue-filter-header-${this.UUID}`, { type: "hide", value: this.column.property });
    },
    // 确认
    onConfirm() {
      this.condition = this.getCondition()
      if (!this.condition && !this.sortType) {
        this.onReset()
        return
      }
      let sortValue
      if (this.sortType) {
        sortValue = {
          type: "filter",
          activeName: this.activeName,
          ctype: this.ctype,
          uuid: this.UUID,
          property: this.column.property,
          value: { Field: this.column.property, Type: this.sortType == 'descending' ? -1 : 1 },
        }
      }
      // 发送给父组件查询信息
      this.$emit('changeTableData', {
        type: "filter",
        activeName: this.activeName,
        ctype: this.ctype,
        uuid: this.UUID,
        property: this.column.property,
        value: this.condition,
        sortValue: sortValue
      })
      this.visible = false
    },
    // 处理数据
    getCondition() {
      if (this.activeName == '1') {
        if (this.ctype == '1' && !this.virtualized) {
          if (this.checked.checks.length == this.contentDataAll.length) {
            return null
          }
        } else {
          if (this.checked.checks.length == this.contentData.length && this.contentDataTotal.length == this.contentData.length) {
            return null
          }
        }
        this.condition = { Field: this.col.data || this.col.prop, Operate: 'in', Relation: 'and', Value: this.checked.checks }
        return this.condition
      }
      const r = this.value
      if (!r.Field || !r.Operate) {
        return null;
      }
      let cond = {
        Field: r.Field, Operate: r.Operate, Relation: r.Relation, Value: null,
      };
      if ("nil" === r.Operate || "nnil" === r.Operate || "nBlank" === r.Operate) {
        return cond;
      }
      if ("blank" === r.Operate) {
        cond.Operate = "=";
        cond.Value = "";
        return cond;
      }
      // 空数组也不需要当成条件
      if ((!r.Value && r.Value !== 0) || JSON.stringify(r.Value) === '[]') {
        return null;
      }
      if (r.stype === "date") {
        if (Array.isArray(r.Value)) {
          if (typeof r.Value[0] != "object") {
            cond.Value = r.Value;
          }
          else {
            cond.Value = r.dtype === "number" ? [
              (r.Value[0].getTime() / 1000), (r.Value[1].getTime() / 1000)
            ] : [
              r.Value[0].FormatString(this.GetFormat(r) || "YYYY-MM-dd"),
              r.Value[1].FormatString(this.GetFormat(r) || "YYYY-MM-dd")
            ];

          }
        }
        else {
          if (typeof r.Value !== "object") {
            cond.Value = r.Value;
          }
          else {
            cond.Value = r.dtype === "number" ? (r.Value.getTime() / 1000)
              : r.Value.FormatString(this.GetFormat(r) || "YYYY-MM-dd");
          }
        }
      } else if (r.stype === "codeTree" || r.stype === "codeUserTree") {
        cond.Value = r.Value;
      } else {
        if (Array.isArray(r.Value) && r.Operate === 'in' && r.stype !== 'code') {
          cond.Value = r.Value;
        }
        else if (Array.isArray(r.Value) && r.Operate !== 'between') {
          cond.Value = r.Value[0];
          if (r.stype === 'code' && r.dtype === 'number') {
            if (!cond.Value) return null;
            cond.Value = Number(cond.Value)
          }
          if (cond.Value !== 0 && !cond.Value) {
            return null;
          }
        } else {
          cond.Value = r.Value;
          if (r.stype === 'code' && r.dtype === 'number') {
            if (r.Operate !== 'between') {
              if (!cond.Value) return null;
              cond.Value = Number(cond.Value)
            } else {
              cond.Value = cond.Value.map(v => Number(v))
            }
          }
          if (cond.Value !== 0 && !cond.Value) {
            return null;
          }
        }
      }
      return cond
    },
    // 取消
    onCancel() {
      this.visible = false
    },
    // 重置 send 是否发送消息给父级组件
    onReset(send = true) {
      this.condition = null
      this.fillRowVal()
      this.asort = null
      this.activeName = '1'
      this.ctype = '1'
      this.searchKey = ''
      if (!send) return
      // 发送给父组件重置信息
      this.$emit('changeTableData', {
        type: "reset",
        uuid: this.UUID,
        property: this.column.property,
        value: {}
      })
      this.visible = false
    },
    needShowOperate(operate, value) {
      if (!value || !value.filteroperate) {
        return true
      } else {
        return value.filteroperate.includes(operate)
      }
    },
    handelOperateChange(e, v) {
      // code兼容
      if (v.stype === 'code') {
        if (v.dtype === 'number') {
          if (v.Operate === 'between') {
            if (!Array.isArray(v.Value)) {
              v.Value = [v.Value, v.Value]
            }
          } else {
            if (Array.isArray(v.Value)) {
              v.Value = [v.Value[0] || v.Value[1] || null]
            } else {
              v.Value = []
            }
          }
        }
        if (Array.isArray(v.Value) && v.Value[0] && Array.isArray(v.Value[0]) && v.Operate !== 'in') {
          v.Value = [v.Value[0][0]]
        }
      }
      // 字符兼容
      if (v.dtype === 'string' && !v.stype) {
        if (v.Operate !== 'in' && v.Value && Array.isArray(v.Value)) {
          v.Value = v.Value.join(',')
        }
      }
      // 日期兼容
      if (v.stype === 'date') {
        if (v.Operate === 'between') {
          if (v.Value && !Array.isArray(v.Value)) {
            v.Value = [v.Value, v.Value]
          } else {
            v.Value = []
          }
        } else {
          if (Array.isArray(v.Value)) {
            v.Value = v.Value[0] || null
          }
        }
      }
    },
    handleDatePickerPos(e) {
      $('.popper__arrow').css({
        left: '150px'
      })
      let panelW = $('.el-picker-panel').width()
      let panelH = $('.el-picker-panel').height()
      let winW = $(window).width()
      let winH = $(window).height()
      let left = e.clientX - 160
      let top = e.clientY + 10
      if (panelW + e.clientX > winW) {
        left = left - (panelW + e.clientX - winW)
      }

      if (panelH + e.clientY > winH) {
        top = e.clientY - 10 - panelH
      }

      $('.el-picker-panel').css({
        top,
        left
      })
    },
    ctypeChange(val, rowschange) {
      if (val) {
        this.searchKey = ''
      }
      if (this.$refs.scroll) {
        this.$refs.scroll.scrollTop = 0
      }
      if (this.ctype == '1') {
        let countObj = {}
        //当前页根据其他字段的过滤条件,显示过滤数据
        let conditions = []
        for (let k in this.data) {
          if (k != this.column.property) {
            let c = this.data[k]
            if (c.activeName == '1' && c.ctype == '1') {
              conditions.push(c.value)
            }
          }
        }
        let allRows = this.allRows
        if (conditions.length) {
          allRows = this.allRows.filter(r => {
            let b = true
            conditions.forEach(v => {
              let rval = utils.objectGet(r, v.Field)
              b = b && v.Value.includes(!rval && rval !== 0 ? null : rval)
            })
            return b
          })
        }
        let rows = allRows.map(r => utils.objectGet(r, this.column.property))
        let data = []
        rows.forEach(row => {
          let r = !row && row !== 0 ? null : row
          if (countObj[r] == undefined) {
            countObj[r] = 1
            data.push(r)
          } else {
            countObj[r]++
          }
        })
        let dtype = this.col.dtype
        if (dtype == 'code') {
          let formatData = this.selectSource(this.col)
          data = data.map(d => {
            return {
              Value: d,
              Name: (formatData.find(f => f.Value == d) || { Name: d }).Name
            }
          })
        } else if (dtype == 'date') {
          data = data.map(d => {
            if (typeof d === 'number') {
              return {
                Name: moment(d).format(this.GetFormat(this.col)) || `/`, Value: d
              }
            } else {
              return {
                Name: d || `/`, Value: d
              }
            }
          })
        } else if (dtype == 'codeMulti') {
          let formatData = this.selectSource(this.col)
          data = data.map(d => {
            let dvals = d ? d.split(',') : null
            return {
              Name: dvals === null ? `(空内容)` : formatData.filter(f => dvals.includes(String(f.Value))).map(f => f.Name).join(','),
              Value: d
            }
          })
        } else if (dtype == 'codeTree') {
          let formatData = this.selectSource(this.col)
          const code = this.GetFormat(this.col)
          if (!this.codeTreeMap[code]) {
            let dict = {};
            let dictTree = (arr) => {
              arr.forEach(r => {
                dict[r.Value] = r.Name
                if (r.Children && r.Children.length) {
                  dictTree(r.Children)
                }
              })
            }
            dictTree(formatData)
            this.$set(this.codeTreeMap, code, dict);
          }
          data = data.map(d => {
            let dic = this.codeTreeMap[code] || {};
            return {
              Name: dic[d] === undefined ? d : dic[d],
              Value: d
            }
          })
        } else {
          data = data.map(d => {
            return {
              Name: d === null ? `(空内容)` : d, Value: d
            }
          })
        }
        data = data.map(d => {
          d.total = countObj[d.Value]
          return d
        })

        if (!this.virtualized) {
          this.contentDataAll = data
          this.contentData = data
          if (val != undefined) {
            this.checked.checks = this.contentData.map(c => c.Value)
          } else {
            let condition = this.data[this.column.property]
            if (condition && condition.activeName == '1' && !rowschange) {
              let checks = Array.isArray(condition.value.Value) ? condition.value.Value : [condition.value.Value]
              let allChecks = this.contentData.map(c => c.Value)
              this.checked.checks = checks.filter(c => allChecks.includes(c))
            } else {
              this.checked.checks = this.contentData.map(c => c.Value)
            }
          }
          this.checked.all = this.checked.checks.length == this.contentData.length
          if (this.searchKey) {
            this.keyChange(this.searchKey)
          }
        } else {
          data = data.map(d => {
            d.check = true
            return d
          })

          if (this.data && this.data[this.column.property]) {
            let condition = this.data[this.column.property]
            if (condition) {
              let values = condition.value.Value
              data.forEach(d => {
                d.check = values.includes(d.Value)
              })
            }
          }

          this.contentDataTotal = data
          this.checked.checks = data.filter(d => d.check).map(c => c.Value)
          this.contentDataAll = data
          this.checked.all = this.checked.checks.length == data.length
          if (this.searchKey) {
            this.keyChange(this.searchKey)
          }
          this.updateVisibleData(0)
        }
      }
    },
    deepTree(v, arr) {
      for (const item of arr) {
        if (item.Value === v) return item
        if (item.Children && item.Children.length) {
          const _item = this.deepTree(v, item.Children)
          if (_item) return _item
        }
      }
    },
    checkAll(val) {
      if (this.ctype == '1' && !this.virtualized) {
        val ? this.checked.checks = this.contentData.map(c => c.Value) : this.checked.checks = []
      } else {
        this.contentDataAll.forEach(d => {
          d.check = val
        })
        this.checked.checks = this.contentDataAll.filter(c => c.check).map(c => c.Value)
      }
    },
    checkChange(val) {
      this.checked.all = val.length == this.contentData.length
    },
    keyChange(str) {
      if (this.ctype == '1' && !this.virtualized) {
        this.contentData = this.contentDataAll.filter(d => {
          if (this.col.filterable === 'number') {
            return str === '' || d.Name == str
          } else {
            return d.Name && d.Name.indexOf(str) > -1
          }
        })
        this.checked.checks = this.contentData.map(c => c.Value)
        this.checked.all = true
      } else {
        this.contentDataAll = this.contentDataTotal.filter(d => {
          if (this.col.filterable === 'number') {
            return str === '' || d.Name == str
          } else {
            return d.Name && d.Name.indexOf(str) > -1
          }
        })
        this.checked.checks = this.contentDataAll.map(c => c.Value)
        this.checked.all = true
        this.updateVisibleData(0)
      }
    },
    itemChange() {
      this.checked.all = !this.contentDataAll.filter(c => !c.check).length
      this.checked.checks = this.contentDataAll.filter(c => c.check).map(c => c.Value)
    },
    sortChange(val) {
      this.visible = false
      this.asort = { Field: this.column.property, Type: val == 'descending' ? -1 : 1 }
      this.$emit('changeTableData', {
        type: "sort",
        activeName: this.activeName,
        ctype: this.ctype,
        uuid: this.UUID,
        property: this.column.property,
        value: this.asort
      })
      
    },
    handleScroll() {
      let scrollTop = this.$refs.scroll.scrollTop
      this.updateVisibleData(scrollTop)
    },
    updateVisibleData(scrollTop) {
      scrollTop = scrollTop || 0
      this.$nextTick(() => {
        const visibleCount = Math.ceil(this.$refs.scroll.clientHeight / this.itemHeight);
        const start = Math.floor(scrollTop / this.itemHeight);
        const end = start + visibleCount;
        this.contentData = this.contentDataAll.slice(start, end);
        this.$refs.scrollContent.style.webkitTransform = `translate3d(0, ${start * this.itemHeight}px, 0)`;
      })
    },
    //判断字符串里是否有html
    hasHtmlTags(str) {
      if (!str) {
        return false
      }
      const htmlTagsPattern = /<("[^"]*"|'[^']*'|[^'">])*>/;
      return htmlTagsPattern.test(str);
    },
    handleDocumentClick(e) {
      let reference = this.$refs.reference;
      const popper = this.$refs.popper;

      if (!this.$refs.popper || !reference || this.$refs.popper.$el.contains(e.target) || reference.contains(e.target) || !popper || e.target === document.body) return;
      this.$refs.popper.showPopper = false;
    },
    tabClick(tab) {
      if (tab.name == '1' && this.ctype == '2') {
        this.updateVisibleData(0)
      }
    }
  }
})

</script>

<style lang="less">
body .cue-table-header__filter.table-header-title__hidden {
  .cue-table-header__filter-container {
    .cue-table-header__filter-container-title {
      overflow: hidden;
    }
  }
}

body .cue-table-header__filter.table-header-title__wrap {
  .cue-table-header__filter-container {
    .cue-table-header__filter-container-title {
      white-space: break-spaces;
    }
  }
}

body .cue-table-header__filter.table-header-title__ellipsis {
  .cue-table-header__filter-container {
    .cue-table-header__filter-container-title {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

body .cue-table-header__filter-popover {
  .cue-table-header__filter-popover-body {
    // padding: 12px;

    .cue-table-header__filter-popover-sort {
      .el-radio-group {
        display: flex;
        width: 100%;

        .el-radio-button {
          flex: 1;

          .el-radio-button__inner {
            width: 100%;
            box-sizing: border-box;

            img {
              width: 14px;
              height: 14px;
              vertical-align: text-bottom;
            }
          }
        }
      }
    }

    .filter-tabs {
      position: relative;

      .el-tabs__header {
        margin-bottom: 12px;
      }

      .el-tabs__nav {
        height: 35px;
        margin-top: 4px;
      }

      .el-tabs__nav-wrap {
        &:after {
          display: none;
        }
      }

      .el-tabs__item {
        padding: 0 14px;
        height: 35px;

        &:nth-child(2) {
          padding-left: 0;
        }
      }

      .filter-total {
        position: absolute;
        right: 0;
        top: 8px;
        width: 90px;
        z-index: 10;
      }

      .el-tabs.en {
        .el-tabs__item {
          padding: 0 7px;
        }
      }
    }

    .cue-table-header__filter-popover-container {
      .filter-popover-container-title {
        height: 24px;
        line-height: 24px;
        color: #999999;
        position: relative;
      }
    }

    .cue-table-header__filter-popover-body-footer {
      margin-top: 12px;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .el-button {
        width: 60px;
      }

      .header-filter-reset {
        display: inline-flex;
        align-items: center;
        line-height: 1;
        white-space: nowrap;
        cursor: pointer;
        color: #222222;
        text-align: center;
        box-sizing: border-box;
        outline: none;
        margin: 0;
        transition: 0.1s;
        font-weight: 500;
        padding: 5px 12px;
        height: 28px;
        font-size: 12px;
        border-radius: 2px;
        margin-right: 10px;
      }
    }

    .cue-table-header__filter-popover-content {
      margin-top: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 2px;
      box-sizing: border-box;

      .cue-table-header__filter-popover-content-header {
        padding: 6px 0;
        border-bottom: 1px solid #e0e0e0;
        margin: 0 10px;
        width: calc(~"100% - 20px");
      }

      .cue-table-header__filter-popover-content-body {
        max-height: 200px;
        padding: 8px 0 8px 10px;
        overflow-y: auto;

        .el-checkbox-group {
          .el-checkbox {
            width: 100%;
            margin: 0 0 6px 0;
            display: flex;

            .el-checkbox__inner {
              // margin-top: 3px;
            }

            .el-checkbox__label {
              display: flex;
              width: calc(~"100% - 30px");

              .name {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .total {
                color: #666666;
              }
            }
          }
        }

        .scroll-view {
          height: 200px;
          overflow-y: auto;
          overflow-x: hidden;
          position: relative;

          .scroll-view-blank {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            z-index: -1;
          }

          .scroll-view-content {
            left: 0;
            right: 0;
            top: 0;
            position: absolute;

            .el-checkbox {
              width: 100%;
              margin: 0 0 6px 0;
              display: flex;
              align-items: center;

              .el-checkbox__label {
                display: flex;
                width: calc(~"100% - 30px");

                .name {
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style scoped lang="less">
.cue-table-header__filter-container {
  position: relative;
  display: inline-flex;
  width: 100%;

  .cue-table-header__filter-container-title {
    width: auto;
    display: inline-block;
    max-width: calc(100% - 18px);
  }

  .cue-table-header__filter-btn {
    height: 20px;
    color: #999999;
    cursor: pointer;
    text-align: center;
    z-index: 0;
    position: relative;

    .iconfont {
      font-size: 16px;
      font-weight: normal;
      z-index: 1;
      font-size: 18px;
    }

    .icon-shaixuan3 {
      font-size: 16px;
    }

    &:hover,
    &.active,
    &.hover {
      color: #999999;

      &:before {
        content: '';
        position: absolute;
        background: #e2e2e2;
        width: 100%;
        left: 0;
        top: 0;
        height: 24px;
        border-radius: 4px;
        z-index: -1;
      }
    }

    &.active {
      color: #0099CB;
    }

    .icon-asort {
      transform: scale(0.8);
      display: inline-block;
      margin-left: -3px;
      vertical-align: middle;
    }
  }
}

.cue-table-header__filter-container.left {
  justify-content: flex-start;
}

.cue-table-header__filter-container.center {
  justify-content: center;
}

.cue-table-header__filter-container.right {
  justify-content: flex-end;
}
</style>
