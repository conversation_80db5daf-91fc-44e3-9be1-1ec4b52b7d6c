import {request} from '@/utils/axios';
import {useUserStore} from "@/stores/user.ts";

let retryNum = 0
const retryTime = 1500
export const api = {
  // 获取配置列表
  AITaskConfigList() {
    return request.get('/wimai/api/task/config/list', {});
  },
  /*获取当前登录用户信息*/
  initUserInfo() {
    return new Promise((resolve) => {
      request
        .get(`/uniwim/ump/currUserInfo`, {})
        .then((result) => {
          if (result) {
            const userStore = useUserStore()
            // 登录信息存储
            userStore.setUserInfo(result);
            resolve(result);
          } else {
            resolve({tenantId: '5d89917712441d7a5073058c'}); // 默认和达科技租户，如果没有获取到信息的话
            if (retryNum < 10) {
              retryNum++
              setTimeout(async () => {
                await api.initUserInfo()
              }, retryTime);
            }
          }
        })
        .catch(() => {
          resolve({tenantId: '5d89917712441d7a5073058c'});
          if (retryNum < 10) {
            retryNum++
            setTimeout(async () => {
              await api.initUserInfo()
            }, retryTime);
          }
        });
    });
  },

  // 智能体执行任务查询
  AIAgentMissionQuery(params: object = {}) {
    return request.post('/wimai/api/task/query', params);
  },
  AIAgentMissionDetail({id}) {
    return request.get(`/wimai/api/task/detail?id=${id}`);
  },
  AIAgentMissionUpdateActive(params: object = {}) {
    return request.post('/wimai/api/task/updateActive', params);
  },
  // 智能体执行任务记录查询
  AIAgentMissionHistoryQuery(params: object = {}) {
    return request.post('/wimai/api/taskHistory/query', params);
  },
  // 智能体执行过程日志查询
  AIAgentMissionLogQuery(params: object = {}) {
    return request.post(`/wimai/api/task/excLogs/query`, params)
  },
  // 智能体执行过程日志对应文件查询
  AIAgentMissionExcFileFile(id: string) {
    return request.get(`/wimai/api/task/excFile/download?executionId=${id}`, {}, {
      responseType: 'arraybuffer'
    })
  },
  // 智能体查询服务端模板接口
  AIGetTaskTemplateList(taskId: string, opts: object = {}) {
    return request.get(`/wimai/api/office/template/getTaskTemplateList?taskId=${taskId}`, {}, opts)
  },

  // 统一转发crud接口
  AIDtemplateCrud(params: object) {
    return request.post('/wimai/api/template/crud', params)
  },

};
export default api;
