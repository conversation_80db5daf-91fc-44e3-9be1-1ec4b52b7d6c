<template>
    <div class="login-code">
        <div class="back" @click="goBack"><i class="el-icon-arrow-left"></i>返回</div>
        <h1>输入验证码</h1>
        <p class="tip1">
            请输入发送至<span>{{ mobile }}</span
            >的验证码，有效期 5 分钟
        </p>
        <div class="input-con">
            <el-input
                v-for="(item, index) in inputList"
                :class="{ isVal: item.value }"
                :key="index"
                v-model="item.value"
                :ref="(el) => { if (el) inputRefs[index] = el }"
                maxlength="1"
                @input="inputHanld(item, index)"
                @keydown.delete.native="
                    (e) => {
                        deleteVal(e, item, index);
                    }
                " />
        </div>
        <p class="tip2" v-if="!finished">
            <el-countdown ref="statistic" @finish="onFinish" format="ss" :value="deadline" time-indices></el-countdown>
            <span>秒后可以重新获取验证码</span>
        </p>
        <div v-if="finished" @click="retrieveGet">重新获取验证码</div>
        <div @click="goBack">重新登录</div>
        <p class="errMsg" v-if="err_info">{{ err_info }}</p>
        <el-button type="primary" block @click="loginPhone" :disabled="getCode.length != 6">登 录</el-button>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, withDefaults, defineEmits, onMounted } from 'vue';
import systemApi from "@/api/system";
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";

const userStore = useUserStore()
const loginStore = useLoginStore()

// 定义输入框项类型
interface InputItem {
  key: string;
  value: string;
}

// 定义API响应类型
interface ApiResponse<T = any> {
  Code: number;
  Message?: string;
  Response?: T;
}

// 登录响应数据类型
interface LoginResponse {
  token: string;
  expire: number;
  refreshToken: string;
  tenantId: string;
}

// 定义Props
const props = withDefaults(defineProps<{
  loginPop?: boolean;
}>(), {
  loginPop: false
});

// 定义Emits
const emit = defineEmits<{
  (e: 'setLoginStip', stage: string): void;
}>();

// 响应式变量
const deadline = ref<number>(Date.now() + 1 * 60 * 1000); // 倒计时截止时间
const finished = ref<boolean>(false); // 倒计时是否结束
const code = ref<string>(''); // 验证码（备用）
const mobile = ref<string>(''); // 手机号
const tenantId = ref<string>(''); // 租户ID
const err_info = ref<string>(''); // 错误信息
const inputList = ref<InputItem[]>([
  { key: 'input0', value: '' },
  { key: 'input1', value: '' },
  { key: 'input2', value: '' },
  { key: 'input3', value: '' },
  { key: 'input4', value: '' },
  { key: 'input5', value: '' }
]);

// 获取 refs
const inputRefs = ref([])

// 计算属性：拼接验证码
const getCode = computed<string>(() => {
  return inputList.value.reduce((code, item) => code + item.value, '');
});

// 返回上一步
const goBack = () => {
  emit('setLoginStip', 'first');
};

// 重新获取验证码
const retrieveGet = async () => {
  try {
    const res = await systemApi.verificationCode(
      {
        mobile: mobile.value,
        type: 'LOGIN_CODE'
      },
      {
        meta: { isData: false }
      }
    ) as ApiResponse;

    if (res.Code === 0) {
      err_info.value = '';
      finished.value = false;
      deadline.value = Date.now() + 1 * 60 * 1000; // 重置倒计时
    } else {
      err_info.value = res.Message || '获取验证码失败';
    }
  } catch (err: any) {
    err_info.value = err.message || err.Message || '获取验证码失败，请重试';
  }
};

// 验证码登录
const loginPhone = async () => {
  const params = {
    mobile: mobile.value,
    tenantId: tenantId.value,
    type: 'LOGIN_CODE',
    code: getCode.value
  };

  try {
    const res = await systemApi.verificationCodeLogin(params, {
      meta: { isData: false }
    }) as ApiResponse<LoginResponse>;

    if (!res) {
      err_info.value = '登录失败，请重试！';
      return;
    }

    if (res.Code === 0 && res.Response) {
      err_info.value = '';
      // 存储登录信息到localStorage
      window.localStorage.setItem('UniWimAuthorization', res.Response.token);
      window.localStorage.setItem('UniWimExpire', res.Response.expire.toString());
      window.localStorage.setItem('UniWimRefreshToken', res.Response.refreshToken);
      window.localStorage.setItem('UniWimTenantId', res.Response.tenantId);

      // 更新Vuex状态
    //   store.commit('InitIsTenant', true);

      if (props.loginPop) {
        // 刷新用户信息
        // await store.dispatch('UPDTAINFO');
        let userInfo = await systemApi.initUserInfo();
          ;
          if(userInfo){
            userStore.setUserInfo(userInfo)
            loginStore.LOGIN_POP_VISIBLE(false)
          }
      } else {
        // 跳转首页
        location.replace(`${import.meta.env.BASE_URL}index.html`);
      }
    } else {
      err_info.value = res.Message || '登录失败，请检查验证码';
    }
  } catch (err: any) {
    err_info.value = err.message || err.Message || '登录请求失败，请重试';
  }
};

// 倒计时结束回调
const onFinish = () => {
  console.log('倒计时结束');
  finished.value = true;
};

// 输入框输入处理（自动聚焦下一个）
const inputHanld = (item, index) => {
    ;
  console.log('Input at index:', index, 'New value:', item.value);

  // 示例：输入后自动聚焦下一个输入框
  if (item.value && index < inputRefs.value.length - 1) {
    inputRefs.value[index + 1].focus();
  }
};

// 删除键处理（自动聚焦上一个）
const deleteVal = (e: KeyboardEvent, item: InputItem, index: number) => {
  e.preventDefault(); // 阻止默认删除行为
  if (!item.value && index > 0) {
     inputRefs.value[index - 1].focus();
  }
  // 清空当前输入框值
  item.value = '';
};

// 组件挂载时初始化（可选）
onMounted(() => {
  // 自动聚焦第一个输入框
  inputRefs.value[0]?.focus();
});
</script>

<style lang="less" scoped>
.login-code {
    padding: 0;
    .back {
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        letter-spacing: 0;
        margin-top: 0;
        display: inline-block;
        cursor: pointer;
        i {
            padding-right: 6px;
            color: #999;
        }
    }
    h1 {
        font-weight: 500;
        font-size: 18px;
        color: #222222;
        letter-spacing: 0.4px;
        margin-top: 63px;
    }
    .tip1 {
        margin-top: 15px;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        letter-spacing: 0;
        line-height: 16px;
        span {
            font-weight: bold;
            font-size: 12px;
            color: #222222;
            letter-spacing: 0;
            padding: 0 5px;
        }
    }
    /deep/.input-con {
        .el-input {
            width: 36px;
            .el-input__inner {
                line-height: 36px;
                height: 36px;
                border-radius: 4px;
            }
        }
        .isVal {
            .el-input__wrapper {
                border: 1px solid #0862ea !important;
            }
        }
    }
    .el-button {
        margin-top: 20px;
        font-size: 18px;
        font-weight: 400;
        width: 100%;
        height: 43px;
        border-radius: 2px;
        overflow: hidden;
        background: #0054d2;
        border-color: #0054d2;
    }
    .tip2 {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-top: 15px;
        .el-statistic {
            line-height: inherit;
            font-size: 16px;
        }
    }
    > div {
        font-size: 12px;
        color: #0061c8;
        margin-top: 20px;
        cursor: pointer;
    }
    .errMsg {
        color: red;
        padding-top: 8px;
        font-size: 12px;
    }
}
</style>
