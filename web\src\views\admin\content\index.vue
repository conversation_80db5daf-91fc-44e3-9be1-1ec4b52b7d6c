<template>
    <div class="content-manager" v-loading="loading">
        <div class="header">
            <div class="title-section">
                内容管理
                <div class="header-tools">
                    <el-button class="header-tools-item" type="circle" size="mini" @click="handleCateManage">
                        栏目管理
                    </el-button>
                    <el-divider direction="vertical" style="margin: 0 16px" />
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
                        <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                        刷新
                    </el-button>
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                        新增
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onEdit(null)">
                        <i class="action-iconfont icon-bianji"></i>
                        编辑
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null||(currentRow&&currentRow.status === 1)" size="mini"
                        @click="onDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu"></i>
                        删除
                    </el-button>
                </div>
            </div>
            <div class="condition-section">
                <el-form :inline="true" :model="params">
                    <el-form-item label="位置">
                        <el-select v-model="params.location" placeholder="" style="width: 160px" @change="changePosition" clearable>
                            <el-option label="全部类型" value="all" />
                            <el-option v-for="it in positionList" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="栏目">
                        <el-select v-model="params.categoryId" placeholder="" style="width: 160px" @change="changeType" clearable>
                            <el-option label="全部类型" value="all" />
                            <el-option v-for="it in classify" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="params.status" placeholder="请选择状态" @change="changeParamStatus" style="width: 160px" clearable>
                            <el-option v-for="it in [{'Value':'all','Name':'全部'},{'Value':1,'Name':'上架'},{'Value':0,'Name':'下架'}]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="params.title" placeholder="文章名称" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="table-section">
            <div class="table-content" v-show="tabType == 'table'">
                <!-- stripe -->
                <el-table class="service-table" ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height:calc(100% - 48px)"
                    @rowClick="handleRowClick">
                    <el-table-column type="index" label="序号"
                        :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                    <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'status'" #default="{ row }">
                            <el-tag type="info" round v-if="row.status === 0">
                                下架
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.status === 1">
                                上架
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'categoryId'" #default="{ row }">
                            {{ classify.find(it => it.Value === row.categoryId)?.Name || '' }}
                        </template>
                        <template v-else-if="it.scoped == 'location'" #default="{ row }">
                            {{ positionList.find(it => it.Value === row.location)?.Name || '' }}
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link>
                            <el-link type="primary" class="task-link" @click="changeStatus(row)">{{ row.status === 0 ? '上架' : '下架' }}</el-link>
                            <el-link type="danger" class="task-link" @click="onDelete(row)" :disabled="row.status === 1">删除</el-link>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 50vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                        :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total" @change="tableQuery(false)" />
                </div>
            </div>
        </div>
    </div>

    <!-- 新增内容弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="dialogFormVisible" :title="isNew?'新增':'编辑'" width="1000" style="border-radius: 4px;" :append-to-body="false" :destroy-on-close="true" top="10vh">
        <el-form class="content-form content-template-form" :model="form" :rules="rules" ref="ruleFormRef" label-width="110px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="location" label="展示位置" required :label-line="true">
                        <el-select v-model="form.location" filterable placeholder="请选择展示位置">
                            <el-option v-for="it in positionList" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="title" label="文章标题" required :label-line="true">
                        <el-input v-model.trim="form.title" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="categoryId" label="所属栏目" required :label-line="true">
                        <el-select v-model="form.categoryId" placeholder="请选择所属栏目">
                            <el-option v-for="it in classify" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="sort" label="排序" :label-line="true">
                        <el-input-number
                            style="width:100px;"
                            :controls="false"
                            v-model="form.sort"
                            :min="1"
                            :max="10"
                            controls-position="right"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="content" label="正文" required :label-line="true">
                        <div style="width: 100%;height: 450px;">
                            <!-- contentType="text"   默认delta  html  text -->
                            <QuillEditor v-if="dialogFormVisible" v-model:content="form.content" @ready="quillReady" :options="editorOptions" contentType="html" style="height: calc(100% - 66px);">
                            </QuillEditor>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="save()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理弹窗 -->
    <el-dialog class="servicePackage-dialog" :append-to-body="false" v-model="cateManageVisible" :title="'栏目管理'" width="1042" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <div class="title-section cate-header-tools">
            <span></span>
            <div class="header-tools">
                <el-button class="header-tools-item" type="circle" size="mini" @click="onResetCate">
                    <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                    刷新
                </el-button>
                <el-button class="header-tools-item" type="circle" size="mini" @click="onAddCate">
                    <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                    新增
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onEditCate(null)">
                    <i class="action-iconfont icon-bianji"></i>
                    编辑
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null||(currentCateRow&&(currentCateRow.status === 1||currentCateRow.contentCount))" size="mini"
                    @click="onDeleteCate(null)">
                    <i class="action-iconfont icon-huishouzhanshanchu"></i>
                    删除
                </el-button>
            </div>
        </div>
        <div class="condition-section">
                <el-form :inline="true" :model="cateParams">
                    <el-form-item label="栏目名称">
                        <el-input v-model="cateParams.name" placeholder="栏目名称" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="cateParams.status" placeholder="请选择状态" @change="changeCateParamStatus" style="width: 160px" clearable>
                            <el-option v-for="it in [{'Value':'all','Name':'全部'},{'Value':1,'Name':'启用'},{'Value':0,'Name':'停用'}]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="cateTableQuery">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        <el-table class="cate-table" ref="cateTableRef" :data="cateTableData" border :show-overflow-tooltip="true"
            :highlight-current-row="true" style="width: 100%;height: 545px"
            @rowClick="handleCateRowClick">
            <el-table-column type="index" label="序号"
                :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                <el-table-column v-for="it in cateTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                <template v-if="it.scoped == 'status'" #default="{ row }">
                    <el-tag type="danger" round v-if="row.status === 0">
                        停用
                    </el-tag>
                    <el-tag type="success" round v-else-if="row.status === 1">
                        启用
                    </el-tag>
                </template>
                <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                    <el-link type="primary" class="task-link" @click="changeCateStatus(row)" :disabled="row.contentCount||row.status === 1">{{ row.status === 0 ? '启用' : '停用' }}</el-link>
                    <el-link type="primary" class="task-link" @click="onEditCate(row)">编辑</el-link>
                    <el-link type="danger" class="task-link" style="margin-left:16px;" @click="onDeleteCate(row)" :disabled="row.contentCount||row.status === 1">删除</el-link>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" style="height: 50vh;" />
            </template>
        </el-table>
        <div class="table-content-pagination">
            <el-pagination v-model:current-page="catePagination.currentPage" v-model:page-size="catePagination.pageSize"
                :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                :total="catePagination.total" @change="cateTableQuery(false)" />
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cateManageVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理编辑弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="editCateVisible" :title="isCateNew?'新增':'编辑'" width="480" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <el-form class="content-form content-template-form" :model="cateForm" ref="cateFormRef" label-width="100px" :rules="cateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="栏目名称" required :label-line="true">
                        <el-input v-model.trim="cateForm.name" :maxlength="32" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="sort" label="排序" :label-line="true">
                        <el-input-number
                            style="width:100px;"
                            :controls="false"
                            v-model="cateForm.sort"
                            :min="1"
                            :max="10"
                            controls-position="right"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editCateVisible = false">取消</el-button>
                <el-button type="primary" @click="saveCate()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { Quill } from '@vueup/vue-quill'
import { VideoExtend, QuillVideoWatch } from 'quill-video-extend-module'
const customSizes = ['10px', '12px', '14px', '16px', '18px', '20px', '24px', '30px', '36px']
const allowedLineHeights = ['1', '1.5', '2', '2.5', '3'];
const fontNames = [
  'SourceHanSansSC-Regular',//思源黑体
  'Arial', 
  'SimSun', // 宋体
  'KaiTi', // 楷体
  'Microsoft YaHei', // 微软雅黑
];

const route = useRoute()
const userStore = useUserStore()

;
const videoUploadUrl = ref('/wimai/api/task/upload')
const token = utils.GetAuthorization()
const editorOptions = ref({
    // debug: 'info',
    modules: {
        toolbar: {
            container:[
                { header: [1,2,3,4,5,6,false] },
                "bold", "italic", "underline", "strike", "link", "image","video", 
                { 'size': customSizes }, // 4. 在工具栏中加入自定义的字体大小
                { list: "ordered" }, 
                { 'lineheight': allowedLineHeights }, // 使用我们注册的 'lineheight'
                { list: "bullet" },
                { 'script': 'sub'}, 
                { 'script': 'super' },
                { 'indent': '-1'}, 
                { 'indent': '+1' },
                { 'direction': 'rtl' },
                { 'color': [] }, { 'background': [] },
                { 'font': fontNames },
                { 'align': [] },
                'clean'
            ],
            handlers: {
              'video': function() {
                debugger;
                QuillVideoWatch.emit(this.quill.id)
              }
            }
        },
        // VideoResize:{
        //     displayStyles: {
        //         backgroundColor: "black",
        //         border: "none",
        //         color: "white"
        //     },
        //     parchment: Quill.import('parchment'),
        //     modules: [ 'Resize', 'DisplaySize', 'Toolbar' ]
        // },
        VideoExtend: {
            loading: true,
            name: 'file',
            action: videoUploadUrl,
            headers: (xhr: XMLHttpRequest) => {
              // set custom token(optional)
              xhr.setRequestHeader('token', token)
            },
            response: (res:any) => {
              // video uploaded path
              // custom your own
              debugger;
              return res.Response
            }
        },
        imageResize: {
            displayStyles: {
                backgroundColor: "black",
                border: "none",
                color: "white"
            },
            parchment: Quill.import('parchment'),
            modules: ["Resize", "DisplaySize", "Toolbar"]
        }
    },
    placeholder: '请输入详情',
    // readOnly: true,
    theme: 'snow'
})
const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
    "Authorization": Authorization.value,
    "FROM_CHANNEL": "web"
})
const iconId = ref(0)
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const catePagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const isNew = ref(true)
const form = ref({
    title: "",
    location: "",
    content:"",
    categoryId:"",
    sort:1
})
const dialogFormVisible = ref(false)
const types = ref([
    { Name: '场景', Value: 'scene' },
    { Name: 'MCP', Value: 'mcp' },
    { Name: '模板', Value: 'template' },
])
const classify = ref([])
const positionList = ref([
    {Name:'学习中心',Value:'learn'}
])
const status = ref([
    { Name: '全部状态', Value: 'all' },
    { Name: '已上架', Value: 1 },
    { Name: '已下架', Value: 0 }
])
const tabType = ref('table')
const params = ref({
    categoryId: 'all',
    location:'all',
    status: 'all',
    title: ""
})
const cateParams = ref({
    status: 'all',
    name: ""
})

// 定义 tableData 中元素的类型
interface TableDataItem {
    id: string;
    iconPath: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscriptionCount?: string;
    downloadCount?: number;
    formatType?: string;
    status: any;
}
const tableData = ref<TableDataItem[]>([])
const tableColumns = ref([
    { data: 'location', title: '展示位置', scoped: 'location', minWidth: 120, orderable: true, filterable: true },
    { data: 'categoryId', title: '栏目名称',scoped:'categoryId', minWidth: 120, orderable: true, filterable: true },
    { data: 'title', title: '文章名称',  orderable: true, filterable: true },
    { data: 'views', title: '浏览量',  minWidth: 100, orderable: true, filterable: true },
    { data: 'creator', title: '创建人', minWidth: 160, orderable: true, filterable: true },
    { data: 'created', title: '创建时间', minWidth: 160, orderable: true, filterable: true },
    { data: 'updater', title: '最后编辑人', minWidth: 120, orderable: true, filterable: true },
    { data: 'updated', title: '最后编辑时间', minWidth: 160, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 150, fixed: 'right' },
])
interface versionTableDataItem {
    id: string;
    name: string;
    versionNumber: string;
    downloadCount: number;
    status: any;
}
const versionTableData = ref<versionTableDataItem[]>([])
const versionTableColumns = ref([
    { data: 'versionNumber', title: '版本号', scoped: 'versionNumber', width: 150, orderable: true, filterable: true },
    { data: 'created', title: '发布日期', scoped: 'created', orderable: true, filterable: true },
    { data: 'description', title: '版本描述', width: 160, orderable: true, filterable: true },
    // { data: 'releaseStatus', title: '状态', scoped: 'releaseStatus', width: 100, orderable: true, filterable: true },
    { data: 'downloadCount', title: '使用量', width: 80, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 210, orderable: true, filterable: true },
])


const ruleFormRef = ref<FormInstance>()


//ref
const tableRef = ref<HTMLElement>()
const serviceTableRef = ref<HTMLElement>()


const versionLoading = ref(false)
const currentVersionRow = ref(null)

//分类管理
const cateManageVisible = ref(false)
const cateTableColumns = ref([
    { data: 'name', title: '栏目名称', minWidth: 150, orderable: true, filterable: true },
    { data: 'contentCount', title: '文章数', minWidth: 80, orderable: true, filterable: true },
    { data: 'created', title: '创建时间', minWidth: 200, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped:"status",minWidth: 80, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', minWidth: 150, fixed: 'right' }
])
const currentCateRow = ref(null)
const cateTableData = ref([
])
const isCateNew = ref(true)
const cateForm = ref({
    name: '',
    iconPath:"",
    status:1,
    sort:1
})
const editCateVisible = ref(false)

const cateFormRef = ref<FormInstance>()



//computed
// isUniwimPc
const isUniwimPc = computed(() => {
    return route.query.uniwim === 'pc'
})

const contentMap = {}
const contentTypeMap = {}

const currentUser = computed(() => {
    return userStore.userInfo
})

//分类管理方法

const quillReady = (quill:any)=>{
    debugger;
    quill.root.quill = quill;
}

const commandChange = (val:string)=>{
    let obj = contentList.value.find(it=>it.Value==val)
    debugger;
    if(obj){
        form.value.name = obj.Label
        form.value.commandType = obj.category
    }
}

const handleCateManage = () => {
   cateManageVisible.value = true;
   cateTableQuery();
}

const cateTableQuery = (noloading: boolean = false) => {
    ;
    if (!noloading) {
        loading.value = true
    }
    currentCateRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"sort",
                Type:1
            }
        ]

    }
    if (cateParams.value.status !== 'all') {
        query_params.data.status = cateParams.value.status
    }
    if (cateParams.value.name) {
        query_params.data.name = params.value.name
    }
    saasApi.AIAgentLearningCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach((row:any)=>{
                if(row.typeList){
                    row.applicableType = row.typeList.map((tp:any)=>tp.type)
                }
            })
            cateTableData.value = res.rows
        } else {
            cateTableData.value = []
        }
    })
    .catch((err: any) => {
        cateTableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}

const onResetCate = () => {
    cateParams.value.status = 'all'
    cateParams.value.name = ''

    catePagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isCateNew.value = true;
    currentCateRow.value = null
    cateTableData.value = []
    cateTableQuery()
}

const onAddCate = () => {
    isCateNew.value = true
    editCateVisible.value = true
    cateForm.value = {
        name: '',
        status:1,
        sort: cateTableData.value.length+1
    }
}
const onEditCate = async (row: any) => {
    if (row) currentCateRow.value = row
    isCateNew.value = false
    cateForm.value = JSON.parse(JSON.stringify(await formSet(currentCateRow.value)))
    editCateVisible.value = true
}
const onDeleteCate = async (row: any) => {
    await ElMessageBox.confirm('是否删除当前栏目？', '确认删除', {
        type: 'warning',
    })
    if (row) currentCateRow.value = row
    saasApi.AIAgentLearningContentDelete([currentCateRow.value.id]).then((res: any) => {
        if (res.Code === 0) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            cateTableQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {},
        size:Infinity,
        index:1
    }
    saasApi.AIAgentLearningCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            classify.value = res.rows.map(it => {
                return {
                    ...it,
                    Name: it.name,
                    Value: it.id
                }
            })
        } else {
            classify.value = []
        }
    })
    .catch((err: any) => {
        classify.value = []
    })
    .finally(() => {
    })

}

const rules = reactive<FormRules>({
    title: [
        { required: true, message: '请输入文章标题', trigger: 'change' }
    ],
    categoryId: [
        { required: true, message: '请选择栏目', trigger: 'change' }
    ],
    location: [
        { required: true, message: '请选择位置', trigger: 'change' }
    ],
    content: [
        { required: true, message: '请输入正文', trigger: 'change' }
    ],
    sort: [
        { required: true, message: '请输入排序', trigger: 'change' }
    ]
})

const cateRules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入分类名称', trigger: 'change' }
    ],
    sort: [
        { required: true, message: '请输入排序', trigger: 'change' }
    ]
})

const save = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return

    let update_params = {
        ...form.value
    }
    if (isNew.value) {
        tableInsert(update_params)
    } else {
        tableUpdate(update_params)
    }
}
const saveCate = async () => {
    let isValidate = await cateFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    if (isCateNew.value) {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            ...cateForm.value,
            typeList
            // type:params.value.type
        }
        delete update_params.applicableType;
        cateInsert(update_params)
    } else {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            id: currentCateRow.value.id,
            name: cateForm.value.name,
            description: cateForm.value.description,
            status: cateForm.value.status,
            typeList
            // type:params.value.type
        }
        cateUpdate(update_params)
    }
}


const handleRowClick = (row: any, show: boolean) => {
    debugger;
    currentRow.value = row
}
const handleCateRowClick = (row: any, show: boolean) => {
    currentCateRow.value = row
}


const tableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    currentRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"sort",
                Type:1
            }
        ],
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    if (params.value.location !== 'all') {
        query_params.data.location = params.value.location
    }
    if (params.value.categoryId !== 'all') {
        query_params.data.categoryId = params.value.categoryId
    }
    if (params.value.status !== 'all') {
        query_params.data.status = params.value.status
    }
    if (params.value.title) {
        query_params.data.title = params.value.title
    }
    saasApi.AIAgentLearningContentQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            pagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            tableData.value = res.rows
        } else {
            tableData.value = []
        }
    })
    .catch((err: any) => {
        tableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}
const tableInsert = (insert_params: any) => {
    saasApi.AIAgentLearningContentAdd(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const tableUpdate = (update_params: any, noQuery?: boolean) => {
    let input_params = {
        ...currentRow.value,
        ...update_params
    }
    saasApi.AIAgentLearningContentUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                if (!noQuery) tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}

const cateInsert = (insert_params: any) => {
    saasApi.AIAgentLearningCategoryAdd(insert_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const cateUpdate = (update_params: any) => {
    let input_params = {
        ...currentCateRow.value,
        ...update_params
    }
    saasApi.AIAgentLearningCategoryUpdate(input_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const changeType = (val:any) => {
    if (!val) {
        params.value.type = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}
const changePosition = (val:any) => {
    if (!val) {
        params.value.location = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

const changeParamStatus = (val:any) => {
    if (!val&&val!==0) {
        params.value.status = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

const changeCateParamStatus = (val:any) => {
    if (!val&&val!==0) {
        cateParams.value.status = 'all'
    }
    // params.value.name = ''
    catePagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    cateTableQuery()
}

changeType(null)

const onSubmit = () => {
    tableQuery()
}
const onReset = () => {
    params.value.categoryId = 'all'
    params.value.location = 'all'
    params.value.status = 'all'
    params.value.title = ''

    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isNew.value = true
    form.value = {
        title: "",
        location:"learn",
        categoryId: "",
        content:"",
        sort:tableData.value.length+1
    }
    currentRow.value = null
    tableData.value = []
    tableQuery()
}
const formSet = async (model: any) => {
    // const form_params: any = {
    //     name: "",
    //     describe: "",
    //     price: "",
    //     classify: '',
    //     icon: [],
    //     templates: []
    // }
    let data = JSON.parse(JSON.stringify(model))
    // // 设置默认值
    // Object.keys(form_params).forEach((key) => {
    //     if (!data[key]) {
    //         data[key] = form_params[key]
    //     }
    // })

    return data
}
const onAdd = async () => {
    isNew.value = true
    debugger;
    form.value = JSON.parse(JSON.stringify(await formSet( {
        title: "",
        location:"learn",
        categoryId: "",
        content:"",
        sort:tableData.value.length+1
    })))
    // tableQuery()
    dialogFormVisible.value = true
}
const onEdit = async (row: any) => {
    if (row) currentRow.value = row
    isNew.value = false
    form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
    // tableQuery()
    dialogFormVisible.value = true
}
const changeStatus = async(row: any) => {
    if(row.status!==0){
        await ElMessageBox.confirm(`是否确认下架文章？`, `提示`, {
            type: 'warning',
            customClass: 'default-confirm-class',
        })
    }

    currentRow.value = row
    row.status = row.status === 0 ? 1 : 0
    const update_params = {
        status: row.status
    }
    tableUpdate(update_params)
}
const changeCateStatus = async(row: any) => {
    if(row.status!==0){
        await ElMessageBox.confirm(`是否确认停用栏目？`, `确认${row.status === 0 ? '启用' : '停用'}`, {
            type: 'warning',
        })
    }
    currentCateRow.value = row;
    row.status = row.status === 0 ? 1 : 0
    const update_params = {
        status: row.status
    }
    cateUpdate(update_params)
}
const onDelete = (row: any) => {
    if (row) currentRow.value = row
    const text = '确认删除文章吗？'
    ElMessageBox.alert(text, '提示', {
        // autofocus: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: (action) => {
            if (action === 'confirm') {
                saasApi.AIAgentWimtaskCommandDelete([currentRow.value.id]).then((res: any) => {
                    if (res.Code === 0 || res === true) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        tableQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            }
        },
    })
}


onMounted(() => {
    getClassifyData();
    // tableQuery()
})
</script>
<style scoped lang="scss">
::v-deep.cate-table .el-table__cell {
    border-bottom: 1px solid #EEEEEE !important;
}
:deep(.service-table) {
    .el-table__cell {
        border-bottom: 1px solid #EEEEEE !important;
    }
}
.small-height {
    height: 64px;
    width:64px;
}
.icon_lists .icon-item{
    display:flex;
    flex-direction: column;
    align-items: center;
}
.icon_lists .icon {
  display: block;
  height: 100px;
  line-height: 100px;
  font-size: 42px;
  margin: 10px auto;
  color: #333;
  -webkit-transition: font-size 0.25s linear, width 0.25s linear;
  -moz-transition: font-size 0.25s linear, width 0.25s linear;
  transition: font-size 0.25s linear, width 0.25s linear;
}

.icon_lists .icon:hover {
//   font-size: 100px;
}

.icon_lists .svg-icon {
  /* 通过设置 font-size 来改变图标大小 */
  width: 1em;
  /* 图标和文字相邻时，垂直对齐 */
  vertical-align: -0.15em;
  /* 通过设置 color 来改变 SVG 的颜色/fill */
  fill: currentColor;
  /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
      normalize.css 中也包含这行 */
  overflow: hidden;
}

.icon_lists li .name,
.icon_lists li .code-name {
  color: #666;
}
:deep(.icon-preview-item) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
    width: 64px;
    }

    .icon-preview-item-tool {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
    .el-icon {
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
    }
    }
    &:hover {
    .icon-preview-item-tool {
        display: flex;
    }
    }
}
.content-manager {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px;
    box-sizing: border-box;
    background: #f7f7f9 !important;
    // display: flex;
    flex-direction: column;

    .header {
        background: #fff;
        width: 100%;
        height: 112px;
        box-sizing: border-box;
        //overflow: hidden;



        .condition-section {
            padding: 8px 16px;
            box-sizing: border-box;
            // border-top: solid 1px #e8ecf0;
            display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 16px;
            }

            .tab-list {
                .tab-list-item {
                    width: 80px;
                    height: 32px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    border: 1px solid #E6E7E9;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &.left {
                        border-radius: 4px 0 0 4px;
                    }

                    &.right {
                        border-radius: 0 4px 4px 0;
                    }

                    &.active {
                        color: #FFFFFF;
                        background: #0054D9;
                        border-color: #0054D9;
                    }
                }
            }
        }
    }

    .table-section {
        // flex: 1;
        height:calc(100% - 112px);
        background: #fff;
    }

    .table-content {
        height: 100%;

        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }

        .table-icon {
            width: 24px;
            height: 24px;
        }

        .table-content-pagination {
            height: 48px;
            padding: 0 12px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        ::v-deep(.el-scrollbar__view) {
            height: 100%;
        }
    }


}
.title-section {
    height: 64px;
    width: 100%;
    padding: 12px 16px 16px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSansSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #222222;

    .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
    }
}

    .header-tools {
    display: flex;
    align-items: center;

    .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
            margin-right: 4px;
            font-size: 14px;
        }

        span {
            margin-left: 6px;
            line-height: 17px;
        }

        &:hover {
            color: rgba(0, 84, 210, 0.8);
        }

        &:active {
            color: #0044A9;
        }

        &.is-disabled {
            color: #BCBFC3;
            cursor: not-allowed;
        }
    }
    }
    .cate-table{
        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }
        .scene-tag{
            height: 22px;
            background: #F0F5FF;
            border: 1px solid #BED2FF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #1E39C3;
            line-height: 22px;
            margin-right:8px;
        }
        .mcp-tag{
            height: 22px;
            background: #FFFDDF;
            border: 1px solid #E9DE9A;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #D7A710;
            line-height: 22px;
            margin-right:8px;
        }
        .template-tag{
            height: 22px;
            background: #F0FFFB;
            border: 1px solid #C0E7DF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #2B9196;
            line-height: 22px;
            margin-right:8px;
        }
    }

</style>
