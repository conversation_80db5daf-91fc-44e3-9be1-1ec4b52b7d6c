from typing import Dict, List, Optional, Any,Set

class InputItem:
    name: Optional[str] = None # 参数名称
    type: Optional[str] = None # 数据类型
    info: Optional[str] = None # 数据名称
    default_val: Optional[any] = None # 输入参数

    def __init__(self, name: str, type: str, info: str, default_val: any):
        self.name = name
        self.type = type
        self.info = info
        self.default_val = default_val

    # 方法 1：添加 to_dict 方法
    def to_dict(self):
        return {
            "name": self.name,
            "type": self.type,
            "info": self.info,
            "default_val": self.default_val,
        }