<template>
  <div
    class="end-node"
    :class="{
      selected: selected,
      error: hasError || isFailure,
      success: isSuccess,
      warning: isWarning,
      running: isRunning,
    }"
  >
    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 输入连接点 -->
      <Handle
        type="target"
        position="left"
        :connectable-start="false"
        :style="{ background: getHandleColor() }"
        class="input-handle"
        :class="{ connected: isInputConnected }"
      />

      <!-- 图标区域 -->
      <div class="node-icon">
        <el-icon :size="24">
          <component :is="getStatusIcon()" />
        </el-icon>
      </div>

      <!-- 内容区域 -->
      <div class="node-content">
        <div class="node-title">{{ data.label || '结束' }}</div>
        <div class="node-subtitle" v-if="statusText">{{ statusText }}</div>
        <div class="node-description" v-if="message">{{ message }}</div>
      </div>

      <!-- 状态指示器 -->
      <div class="node-status" v-if="showStatus">
        <el-icon v-if="isRunning" class="status-running">
          <Loading />
        </el-icon>
        <el-icon v-else-if="hasError" class="status-error">
          <CircleClose />
        </el-icon>
        <el-icon v-else-if="isCompleted" class="status-success">
          <CircleCheck />
        </el-icon>
      </div>

      <!-- 系统节点标识 -->
      <div class="system-node-badge">
        <el-icon :size="10">
          <Lock />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import {
  CircleClose,
  CircleCheck,
  Warning,
  Timer,
  Loading,
  SuccessFilled,
  CircleCloseFilled,
  WarningFilled,
  Lock,
} from '@element-plus/icons-vue'

interface Props {
  data: {
    label: string
    config: Record<string, any>
    status?: 'idle' | 'running' | 'completed' | 'error'
  }
  edges?: any[],
  selected?: boolean
}

const props = defineProps<Props>()

// 计算输入是否已连接
const isInputConnected = computed(() => {
  return props.edges?.some(edge => edge.target === 'end_node')
})

// 计算属性
const endStatus = computed(() => props.data.config?.status || 'success')
const message = computed(() => props.data.config?.message || '')
const isRunning = computed(() => props.data.status === 'running')
const isCompleted = computed(() => props.data.status === 'completed')
const hasError = computed(() => props.data.status === 'error')
const showStatus = computed(() => props.data.status && props.data.status !== 'idle')

const isSuccess = computed(() => endStatus.value === 'success')
const isFailure = computed(() => endStatus.value === 'failure')
const isWarning = computed(() => endStatus.value === 'cancelled' || endStatus.value === 'timeout')

const statusText = computed(() => {
  const statusMap = {
    success: '成功完成',
    failure: '执行失败',
    cancelled: '已取消',
    timeout: '执行超时',
  }
  return statusMap[endStatus.value as keyof typeof statusMap] || endStatus.value
})

const getStatusIcon = () => {
  if (isRunning.value) return Loading

  switch (endStatus.value) {
    case 'success':
      return SuccessFilled
    case 'failure':
      return CircleCloseFilled
    case 'cancelled':
      return WarningFilled
    case 'timeout':
      return Timer
    default:
      return CircleClose
  }
}

const getHandleColor = () => {
  if (isFailure.value) return '#f56c6c'
  if (isWarning.value) return '#e6a23c'
  return '#67c23a'
}
</script>

<style scoped>
.end-node {
  min-width: 180px;
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  border: 2px solid #f56c6c;
  border-radius: 14px;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  transition: all 0.3s ease;
  position: relative;
  //overflow: hidden;
}

.end-node.success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border-color: #67c23a;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.end-node.warning {
  background: linear-gradient(135deg, #e6a23c 0%, #eebe77 100%);
  border-color: #e6a23c;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.end-node:hover {
  transform: translateY(-2px);
}

.end-node.success:hover {
  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
}

.end-node.warning:hover {
  box-shadow: 0 6px 20px rgba(230, 162, 60, 0.4);
}

.end-node.error:hover {
  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
}

.end-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3);
}

.end-node.running {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(245, 108, 108, 0.6);
  }
  100% {
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  }
}

.node-body {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: white;
  position: relative;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.node-content {
  flex: 1;
  min-width: 0;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-subtitle {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-description {
  font-size: 11px;
  opacity: 0.8;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.node-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.status-running {
  animation: spin 1s linear infinite;
  color: #409eff;
}

.status-error {
  color: #f56c6c;
}

.status-success {
  color: #67c23a;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.input-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  background: #f56c6c !important;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.input-handle.connected {
  opacity: 1;
}

.input-handle:hover {
  width: 16px;
  height: 16px;
}

.system-node-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .end-node {
    min-width: 150px;
  }

  .node-body {
    padding: 10px 12px;
  }

  .node-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .node-title {
    font-size: 14px;
  }
}
</style>
