/**
 * 数据库操作组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'
import saasApi from '@/api/index'
import { ElMessage,ElLoading } from 'element-plus'
import { configSchemaManager } from "@/utils/configSchema.ts";
export const dbConnectSchema: ComponentConfigSchema = {
  componentType: 'db_connect',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      description: '',
      // icon: 'Search',
      icon: 'Connection',
      order: 1,
      collapsible: false,
      buttons:[
        {
          text:'测试连接',
          icon:'Connection',
          callback:(nodeConfig,selectedNode,variables)=>{
            ;
            const loading = ElLoading.service({
              lock: true,
              fullscreen: true,
            })
            const res = configSchemaManager.validateConfig('db_connect', nodeConfig.value.config)
           
            if(res&&res.errors&&res.errors.length){
              let errorStr = res.errors.map(err=>err.message).join('<br/>')
              ElMessage.error({dangerouslyUseHTMLString: true, message: errorStr, showClose: true })
              return
            }
            saasApi.AITestRun([{node:selectedNode.value,params:variables}])
            .then((res) => {
              if (res.Code === 0) {
                ElMessage.success({ message: '连接成功', showClose: true })
              } else {
                throw Error(res)
              }
            })
            .catch(() => {
              ElMessage.error({ message: '连接失败', showClose: true })
            })
            .finally(() => {
              loading.close()
            })
          }
        }
      ]
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    }
  ],

  fields: {
    // driver: {
    //   type: 'select',
    //   label: '数据库驱动',
    //   description: '数据库驱动类型，如: sqlite, mysql, pymysql, postgresql',
    //   required: true,
    //   default: 'sqlite',
    //   options: [
    //     { label: 'sqlite', value: 'sqlite', description: '' },
    //     { label: 'mysql', value: 'mysql', description: '' },
    //     { label: 'pymysql', value: 'pymysql', description: '' },
    //     { label: 'postgresql', value: 'postgresql', description: '' },
    //     { label: 'oracle', value: 'oracle', description: '' },
    //     { label: 'sqlserver', value: 'sqlserver', description: '' },
    //   ],
    //   group: 'basic',
    //   order: 2,
    // },
    host: {
      type: 'string',
      label: '数据库地址',
      description: '',
      required: true,
      placeholder: '示例：***********',
      group: 'basic',
      order: 1,
      prefix:{
        key:'driver',
        width:'110px',
        options:[
          // { label: 'sqlite', value: 'sqlite', description: '' },
          { label: 'mysql', value: 'pymysql', description: '' },
          // { label: 'pymysql', value: 'pymysql', description: '' },
          // { label: 'postgresql', value: 'postgresql', description: '' },
          // { label: 'oracle', value: 'oracle', description: '' },
          // { label: 'sqlserver', value: 'sqlserver', description: '' }
        ]
      }
    },
    port: {
      type: 'string',
      label: '端口',
      description: '数据库连接端口，如: 5173',
      required: true,
      placeholder: '',
      group: 'basic',
      order: 1,
    },
    database: {
      type: 'string',
      label: '数据库名',
      // description: '数据库名，如: /uniwim',
      required: true,
      placeholder: '',
      group: 'basic',
      order: 1,
    },
    user: {
      type: 'string',
      label: '连接账号',
      // description: '数据库连接账号',
      required: true,
      placeholder: '',
      group: 'basic',
      order: 1,
    },
    password: {
      type: 'password',
      label: '连接密码',
      // description: '数据库连接密码',
      required: true,
      placeholder: '',
      group: 'basic',
      order: 1,
    },
    timeout: {
      type: 'number',
      label: '超时时间',
      description: '',
      // required: true,
      group: 'other',
      order: 1,
      default: 30,
      min: 1,
      max: 3000,
      unit: '秒',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    }
  },

  presets: {
    sqlite: {
      label: 'SQLite默认配置',
      description: '连接到本地SQLite数据库',
      config: {
        host: 'sqlite:///database.db',
        port:'5173',
        user:'account',
        password:'123456',
        driver: 'sqlite',
      },
    },
    mysql: {
      label: 'MySQL默认配置',
      description: '连接到MySQL数据库',
      config: {
        host: 'mysql://user:password',
        port:'5173',
        user:'account',
        password:'123456',
        driver: 'mysql',
      },
    },
  },
  examples: [
    {
      title: '连接到本地SQLite数据库',
      description: '使用默认配置连接到SQLite数据库',
      config: {
        host: 'sqlite:///database.db',
        port:'5173',
        user:'account',
        password:'123456',
        driver: 'sqlite',
      },
    },
  ]
}

export const dbQuerySchema: ComponentConfigSchema = {
  componentType: 'db_query',
  version: '1.0.0',
  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'response',
      label: '指令输出',
      description: '',
      icon: 'Document',
      order: 2,
    },
    {
      id: 'other',
      label: '其他设置',
      description: '',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],
  fields: {
    query: {
      type: 'textarea',
      label: 'SQL查询语句',
      description: '要执行的SQL查询语句',
      required: true,
      group: 'input',
      order: 1,
    },
    // parameters: {
    //   type: 'textarea',
    //   label: '查询参数',
    //   description: 'JSON格式的查询参数',
    //   required: false,
    //   group: 'basic',
    //   order: 2,
    // },
    response_content_variable: {
      type: 'string',
      label: '响应内容变量',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'sql_result',
      group: 'response',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    timeout: {
      type: 'number',
      label: '超时时间',
      description: '',
      required: true,
      group: 'other',
      order: 1,
      default: 30,
      min: 1,
      max: 3000,
      unit: '秒',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      // required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
        // { label: '输出默认值', value: 'default' }
      ]
    },
  },
  presets: {
    simple_select: {
      label: '简单查询',
      description: '执行简单的SELECT查询',
      config: {
        query: 'SELECT * FROM table',
        parameters: '',
      },
    },
  },
  examples: [
    {
      title: '查询用户表',
      description: '获取所有用户数据',
      config: {
        query: 'SELECT * FROM users',
        parameters: '',
      },
    },
  ],
}

export const dbExecuteSchema: ComponentConfigSchema = {
  componentType: 'db_execute',
  version: '1.0.0',
  groups: [
    {
      id: 'basic',
      label: '执行数据库操作',
      description: '执行数据库插入、更新、删除操作',
      icon: 'EditPen',
      order: 1,
      collapsible: false,
    },
  ],
  fields: {
    statement: {
      type: 'textarea',
      label: 'SQL语句',
      description: '要执行的SQL语句(INSERT/UPDATE/DELETE等)',
      required: true,
      group: 'basic',
      order: 1,
    },
    parameters: {
      type: 'textarea',
      label: '参数',
      description: 'JSON格式的参数',
      required: false,
      group: 'basic',
      order: 2,
    },
  },
  presets: {
    insert: {
      label: '插入数据',
      description: '向表中插入新记录',
      config: {
        statement: 'INSERT INTO users (name, email) VALUES (?, ?)',
        parameters: '["张三", "<EMAIL>"]',
      },
    },
  },
  examples: [
    {
      title: '插入新用户',
      description: '向用户表添加新用户',
      config: {
        statement: 'INSERT INTO users (name, email) VALUES (?, ?)',
        parameters: '["张三", "<EMAIL>"]',
      },
    },
  ],
}
