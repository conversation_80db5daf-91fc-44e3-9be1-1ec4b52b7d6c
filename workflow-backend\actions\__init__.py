from actions.control import group as control_group
from actions.data_process import group as data_process_group
from actions.file import group as file_group
from actions.system import group as system_group
from actions.api import group as api_group
from actions.ai import group as ai_group
from core.executor import ActionManager

manager = ActionManager()

manager.include(control_group)

manager.include(system_group)

manager.include(data_process_group)

manager.include(file_group)

manager.include(api_group)

manager.include(ai_group)
