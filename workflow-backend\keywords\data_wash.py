# -*- coding: utf-8 -*-

#from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from robotlibcore import keyword
from typing import Dict, List, Optional, Any,Set
import logging
import json
import requests
import base64
import re
import logging
from config import settings  # 绝对导入
from config import globals
import requests
from datetime import datetime, timedelta
import statistics
from models import algorith_manalysis
from loguru import logger

class OutputItem:
    wash_win: Optional[list[float]] = None
    wash_time: Optional[list[float]] = None
    miss_count: Optional[float] = None
    invalid_count: Optional[float] = None
    valid_count: Optional[float] = None
    grade: Optional[float] = None

    def __init__(self,wash_win:List[float],wash_time:List[float],miss_count:float,invalid_count:float,valid_count:float,grade:float):
        self.wash_win = wash_win
        self.wash_time = wash_time
        self.miss_count = miss_count
        self.invalid_count = invalid_count
        self.valid_count = valid_count
        self.grade = grade


        # 方法 1：添加 to_dict 方法

    def to_dict(self):
        return {
            "wash_win": self.wash_win,
            "wash_time": self.wash_time,
            "miss_count": self.miss_count,
            "invalid_count": self.invalid_count,
            "valid_count": self.valid_count,
            "grade": self.grade,
        }


@keyword("Data Wash")
def data_wash(wash_type: str, wash_format: str,his_win:List[any],day_count:float,fill_in_data:str,set_step:float,allow_max_data:float=0,allow_min_data:float=0):
    try:
        # 请求地址
        url = settings.DlY_URl + "/algorithmanalysis/CallAlgorithm"

        #his_win_json = json.loads(his_win)

        his_win_array = []

        if wash_format == "时间序列":
            his_win_array = fill_missing_timestamps_wash(his_win,int(day_count))
        else:
            his_win_array = his_win

        his_win_str = json.dumps(his_win_array, ensure_ascii=False)

        fill_log = 0
        if fill_in_data=="填充":
            fill_log = 1

        url_name = ""
        if wash_type == "流量":
            url_name = "algoztdata_v10/WindowWashF"
            if allow_min_data==0 and allow_max_data==0:
                allow_min_data = -30000
                allow_max_data = 55000
        elif wash_type == "压力":
            url_name = "algoztdata_v10/WindowWashP"
            if allow_min_data == 0 and allow_max_data == 0:
                allow_min_data = 0
                allow_max_data = 3

        input_list: list[algorith_manalysis.InputItem] = [
            algorith_manalysis.InputItem(name="his_win", type="[]float64", info="历史窗口", default_val=his_win_str),
            algorith_manalysis.InputItem(name="day_count", type="float64", info="数据天数", default_val=day_count),
            algorith_manalysis.InputItem(name="fill_log", type="float64", info="填充模式", default_val=fill_log),
            algorith_manalysis.InputItem(name="set_step", type="float64", info="输出时间步长", default_val=set_step),
            algorith_manalysis.InputItem(name="val_max", type="float64", info="允许出现的最大值", default_val=allow_max_data),
            algorith_manalysis.InputItem(name="val_min", type="float64", info="允许出现的最小值", default_val=allow_min_data)
        ]

        # 请求头
        headers = {
            "Authorization": "Bearer " + globals.token,
            "Content-Type": "application/json"
        }

        # 方法 2：使用 default 参数
        json_str = json.dumps(input_list, default=custom_serializer, ensure_ascii=False)

        #logger.info("开始序列化清洗入参："+json_str)


        payload = {
            "API_URL": url_name,  # 根据你的实际模型名称调整
            "PARA_JSON": json_str,
            "TOKEN": globals.token,
            "VER_ID": "0dc68135-8891-44de-99ff-9e41b78fceb0"
        }

        #logger.info(payload)

        logger.info("开始请求")
        # 发起 POST 请求
        response = requests.post(url, headers=headers, json=payload)

        response_json_response = response.json().get("RESPONSE", {})

        response_json_response_json = json.loads(response_json_response)

        data_out = response_json_response_json["Data"]["data_out"]

        result = wash_result(his_win, data_out,int(day_count))

        #logger.info(data_out)

        out_item = {
            "wash_win": data_out,
            "wash_time": result["wash_time"],
            "miss_count": result["miss_count"],
            "invalid_count": result["invalid_count"],
            "valid_count": result["valid_count"],
            "grade": result["grade"]
        }
        logger.info("结果返回")
        logger.info(result["miss_count"])
        logger.info(result["invalid_count"])
        logger.info(result["valid_count"])
        logger.info(result["grade"])
        #logger.info(out_item)


        #json_str = json.dumps(out_item, default=custom_serializer_out_wash, ensure_ascii=False)
        # forecast = {
        #     "forecast_data":filter_data_by_step(data_out, set_step),
        #     "forecast_avg": statistics.mean(data_out),
        #     "forecast_sum": sum(data_out),
        #     "forecast_max": max(data_out),
        #     "forecast_min": min(data_out),
        #     "forecast_img": ""
        # }

        # forecast = OutputItem(forecast_data=filter_data_by_step(data_out, set_step),
        #                       forecast_avg=statistics.mean(data_out), forecast_sum=sum(data_out) / 60.0,
        #                       forecast_max=max(data_out), forecast_min=min(data_out), forecast_img="")
        #
        # print(forecast)

        return out_item
    except Exception as e:
        return f"data_wash error:{str(e)}"


def custom_serializer(obj):
    if isinstance(obj, algorith_manalysis.InputItem):
        return obj.to_dict()  # 或直接使用 obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")

def custom_serializer_out_wash(obj):
    if isinstance(obj, OutputItem):
        return obj.to_dict()  # 或直接使用 obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")


def fill_missing_timestamps_wash(data, days=30):
    """
    补全时间序列数据，确保每1分钟有一个数据点
    缺失的数据点填充为 -9999
    """

    data.sort(key=lambda x: x['timestamp'],reverse=False)
    # 将输入数据转换为字典，键为时间戳，值为数据点
    data_dict = {item["timestamp"]: item["value"] for item in data}

    # 计算最近30天的时间范围
    end_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
    start_timestamp = end_timestamp - days * 24 * 60 * 60  # 30天前

    # 生成完整的时间序列
    filled_data = []
    current_timestamp = start_timestamp

    while current_timestamp < end_timestamp:
        # 如果当前时间戳在原始数据中存在，则使用原始值
        if current_timestamp in data_dict:
            value = data_dict[current_timestamp]
        else:
            # 否则填充为 -9999
            value = -9999

        filled_data.append({
            "timestamp": current_timestamp,
            "value": value
        })

        # 每分钟增加一次
        current_timestamp += 60

    his_win = []
    for item in filled_data:
        his_win.append(item["value"])

    return his_win

def wash_result(his_win,data_out,days=30):
    # 计算最近30天的时间范围
    end_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
    start_timestamp = end_timestamp - days * 24 * 60 * 60  # 30天前
    current_timestamp = start_timestamp
    wash_time:List[float] = []
    while current_timestamp < end_timestamp:
        wash_time.append(current_timestamp)
        # 每分钟增加一次
        current_timestamp += 60

    miss_count = his_win.count(-9999)
    invalid_count =  len([
    1 for a, b in zip(his_win, data_out)
    if a != -9999 and a != b
])
    valid_count = len([
    1 for a, b in zip(his_win, data_out)
    if a != -9999 and a == b
])
    grade = round(valid_count/len(his_win) * 100,2)

    result = {
        "wash_time": wash_time,
        "miss_count": miss_count,
        "invalid_count": invalid_count,
        "valid_count": valid_count,
        "grade": grade
    }
    return result

def filter_data_by_step_wash(data: list, step: str) -> list:
    # 验证输入数据长度
    if len(data) != 1440:
        raise ValueError(f"输入数据长度必须为1440，但实际长度为{len(data)}")

    # 定义步长映射表（将时间字符串转换为分钟数）
    step_mapping = {
        "1分钟": 1,
        "5分钟": 5,
        "10分钟": 10,
        "15分钟": 15,
        "30分钟": 30,
        "1小时": 60,
        "2小时": 120,
        "4小时": 240,
        "1天": 1440
    }

    # 检查步长是否有效
    if step not in step_mapping:
        valid_steps = ", ".join(step_mapping.keys())
        raise ValueError(f"无效的时间步长。支持的选项有：{valid_steps}")

    # 计算步长对应的分钟数
    minutes = step_mapping[step]

    # 按步长过滤数据
    filtered_data = data[::minutes]

    return filtered_data
