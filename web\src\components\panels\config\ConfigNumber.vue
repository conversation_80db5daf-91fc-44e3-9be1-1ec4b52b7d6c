<template>
  <el-form-item :label="configMeta.label || configKey">
    <el-input-number
      :model-value="configValue"
      @update:model-value="handleUpdate"
      :min="configMeta.min"
      :max="configMeta.max"
      :step="configMeta.step || 1"
      :precision="configMeta.precision"
      :placeholder="configMeta.placeholder || `请输入${configMeta.label || configKey}`"
      controls-position="right"
      size="small"
      style="width: 100%"
    />

    <!-- 帮助文本 -->
    <div v-if="configMeta.help" class="config-help">
      <el-icon :size="12">
        <QuestionFilled />
      </el-icon>
      <span>{{ configMeta.help }}</span>
    </div>

    <!-- 范围提示 -->
    <div v-if="hasRange" class="config-range">
      <span>范围: {{ configMeta.min }} - {{ configMeta.max }}</span>
      <span v-if="configMeta.unit" class="unit">{{ configMeta.unit }}</span>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

interface Props {
  configKey: string
  configValue: any
  nodeType?: string
}

interface Emits {
  (e: 'update', key: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置元数据
const configMeta = computed(() => {
  return getConfigMeta(props.nodeType, props.configKey)
})

// 是否有范围限制
const hasRange = computed(() => {
  return configMeta.value.min !== undefined || configMeta.value.max !== undefined
})

// 更新处理
const handleUpdate = (value: number | null) => {
  emit('update', props.configKey, value)
}

// 获取配置元数据
function getConfigMeta(nodeType: string | undefined, key: string) {
  const configDefinitions: Record<string, Record<string, any>> = {
    new_browser: {
      timeout: {
        label: '超时时间',
        placeholder: '30',
        help: '浏览器启动的最大等待时间',
        min: 1,
        max: 300,
        step: 1,
        unit: '秒',
      },
    },
    click_element: {
      timeout: {
        label: '超时时间',
        placeholder: '10',
        help: '等待元素出现的最大时间',
        min: 1,
        max: 60,
        step: 1,
        unit: '秒',
      },
      wait_after: {
        label: '点击后等待',
        placeholder: '1',
        help: '点击完成后的等待时间',
        min: 0,
        max: 10,
        step: 0.1,
        precision: 1,
        unit: '秒',
      },
    },
    input_text: {
      timeout: {
        label: '超时时间',
        placeholder: '10',
        help: '等待输入框出现的最大时间',
        min: 1,
        max: 60,
        step: 1,
        unit: '秒',
      },
    },
    get_text: {
      timeout: {
        label: '超时时间',
        placeholder: '10',
        help: '等待元素出现的最大时间',
        min: 1,
        max: 60,
        step: 1,
        unit: '秒',
      },
    },
    wait: {
      duration: {
        label: '等待时长',
        placeholder: '1',
        help: '暂停执行的时间长度',
        min: 1,
        max: 300,
        step: 1,
        precision: 0,
        unit: '秒',
      },
    },
    navigate_to: {
      timeout: {
        label: '超时时间',
        placeholder: '30',
        help: '页面加载的最大等待时间',
        min: 1,
        max: 120,
        step: 1,
        unit: '秒',
      },
    },
  }

  const componentConfig = configDefinitions[nodeType || ''] || {}
  return (
    componentConfig[key] || {
      label: key,
      placeholder: `请输入${key}`,
      help: '',
      min: 0,
      max: 100,
      step: 1,
    }
  )
}
</script>

<style scoped>
.config-help {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 11px;
  color: #909399;
  line-height: 1.4;
}

.config-range {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 11px;
  color: #606266;
}

.unit {
  color: #909399;
  font-style: italic;
}
</style>
