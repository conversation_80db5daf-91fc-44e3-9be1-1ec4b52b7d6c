<template>
  <!-- 原有模板代码保持不变 -->
  <div class="preview-wrapper">
    <div class="table-container" v-html="tableHtml">
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'

// @ts-expect-error
import saasApi from '@/api/index'

const route = useRoute()
// 存储 HTML 格式的表格数据
const tableHtml = ref('')

// Props
interface Props {
  id?: string
}

const props = withDefaults(defineProps<Props>(), {})

// 发起请求获取文件流
const fetchExcelFile = async () => {
  try {
    if (!route.query.id && !props.id) {
      ElMessage.error('未获取到 id 参数')
      return
    }

    // 请求 AIAgentMissionLogFile 接口，设置 responseType 为 arraybuffer
    const responseData = await saasApi.AIAgentMissionLogFile(route.query.id || props.id)

    // 创建 Blob 对象
    const blob = new Blob([responseData], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 读取 Blob 对象
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        // 解析文件内容为 Excel
        const workbook = XLSX.read(e.target.result, { type: 'array' })
        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        // 使用 sheet_to_html 方法直接转换为 HTML 表格
        tableHtml.value = XLSX.utils.sheet_to_html(worksheet)
      }
    }
    reader.onerror = () => {
      ElMessage.error('读取文件失败')
    }
    reader.readAsArrayBuffer(blob)
  } catch (error) {
    ElMessage.error('获取 Excel 文件失败: ' + (error as Error).message)
  }
}

// 组件挂载后发起请求
onMounted(() => {
  fetchExcelFile()
})
</script>

<style scoped lang="scss">
/* 原有样式代码保持不变 */
.preview-wrapper {
  height: 100%;
  width: 100%;
}

:deep(.table-container) {
  // 设置容器的最大宽度和高度
  //max-width: 100vw;
  max-height: 100vh;
  // 超出部分显示滚动条
  overflow: auto;

  table {
    border-collapse: collapse;
    display: block;
    margin-bottom: 1rem;
    table-layout: fixed;
  }

  table thead tr {
    border-bottom: 2px solid #dadde1
  }

  table thead, table tr:nth-child(2n) {
    background-color: #00000008;
  }

  table tr {
    background-color: #0000;
    border-top: 1px solid #dadde1;
    height: 42px;
  }

  table td, table th {
    border: 1px solid #dadde1;
    padding: 0.75rem;
    min-width: 80px; /* 设置最小宽度为 80px */
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 溢出内容显示省略号 */
  }

  table th {
    background-color: inherit;
    color: inherit;
    font-weight: 700;
  }

  table td {

  }

}
</style>
