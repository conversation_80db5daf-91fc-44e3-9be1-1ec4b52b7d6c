import json
import re
import os
from robot.libraries.BuiltIn import BuiltIn
from robot.api.deco import keyword
from utils.wimtask_server_api import get_online_template
from utils.word_tools.render_canvas import render_canvas_to_docx
from utils.word_tools.render_canvas import create_word_document
from loguru import logger
from utils.file_util import generate_filename_with_extend_format
from utils.file_util import open_folder



@keyword("Create Smart Word")
def create_word_smart(
    content: str = None,
    use_template: bool = False,
    template_id: str = None,
    output_folder: str = None,
    output_filename: str = None,
    title: str = "",
    font_size: int = 12
):
    """
    根据 use_template 判断创建 Word 的逻辑：
    - use_template 为 true 时，从 template_id 获取 Canvas JSON 并渲染
    - 否则从 content 读取文本内容创建 Word
    """
    try:
        output_folder = output_folder.replace('\\', '/')
        # 文件路径和文件名拼接url 自己拼死 .docx

        # extend 文件扩展名拼接
        output_filename = generate_filename_with_extend_format(output_filename)

        word_path = os.path.join(output_folder, f"{output_filename}.docx")
        if use_template:
            template_data = get_online_template(template_id)
            # 这里在线是 请求格式的需要提取出 Response  config
            try:
                template_data = template_data["Response"]["config"]
            except Exception as e:
                logger.error("获取模板数据失败")
                raise Exception(f"获取模板数据失败: {str(e)}") from e

            # 替换robot里面定义的普通变量
            template_data = deep_replace_robot_variables(template_data)
            # 转换成 dict
            if isinstance(template_data, str):
                template_data = json.loads(template_data)

            # 处理${md.} 的变量
            template_data = process_markdown_in_main(template_data)

            # 处理${img.} 的变量
            template_data = process_img_in_main(template_data)


            render_canvas_to_docx(template_data, word_path)
        else:
            # 替换robot里面定义的普通变量
            template_data = deep_replace_robot_variables(content)
            # 直接用文本类容创建
            create_word_document(template_data, word_path,title, font_size)

        logger.info(f"Word文档创建成功：{word_path}")

        open_folder(output_folder)

        # 设置变量
        BuiltIn().set_suite_variable("${word_path}", word_path)
        BuiltIn().set_suite_variable("${file_path}", output_folder)
        BuiltIn().set_suite_variable("${file_name}", output_filename)
    except Exception as e:
        error_msg = f"创建Word文档失败：{str(e)}"
        logger.error(error_msg)
        raise Exception(f"模板文档生成失败: {str(e)}") from e


# 替换 ${var} 的逻辑
robot_var_pattern = re.compile(r"\$\{(.*?)\}")

def is_json_string(s: str) -> bool:
    try:
        json.loads(s)
        return True
    except:
        return False

def replace_robot_variables(text: str) -> str:
    def repl(match):
        var_name = match.group(1)
        try:
            value = BuiltIn().get_variable_value("${" + var_name + "}")
            if is_json_string(value):
                return match.group(0)
            return str(value) if value is not None else match.group(0)
        except Exception:
            return match.group(0)
    return robot_var_pattern.sub(repl, text)

# 对整个对象结构递归替换变量
def deep_replace_robot_variables(obj):
    if isinstance(obj, str):
        return replace_robot_variables(obj)
    elif isinstance(obj, dict):
        return {k: deep_replace_robot_variables(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [deep_replace_robot_variables(item) for item in obj]
    else:
        return obj

# pattern: ${md:变量名}
markdown_var_pattern = re.compile(r"\$\{md:([^\}]+)\}")
img_var_pattern = re.compile(r"\$\{img:([^\}]+)\}")


def markdown_to_canvas_blocks(md_text: str) -> list:
    """
    将 markdown 文本转换为 canvas main[] 可用的 text 块
    简单实现：每段或每行变成一个 text 块
    """
    blocks = []
    for para in md_text.strip().split("\n\n"):
        lines = para.strip().split("\n")
        for line in lines:
            line = line.strip()
            if line:
                blocks.append({
                    "value": line,
                    "size": 16,
                    "bold": False,
                    "color": "rgb(0, 0, 0)",
                    "italic": False,
                    "rowMargin": 1.5
                })
                blocks.append({
                    "value": "\n"
                })
    return blocks

def process_markdown_in_main(template_data):
    """
    找出 main[] 中的 ${md:变量}，并展开成多个 markdown 块
    """
    main_list = template_data.get("data", {}).get("data", {}).get("main", [])
    new_main = []

    for item in main_list:
        value = item.get("value", "")
        if isinstance(value, str):
            match = markdown_var_pattern.fullmatch(value.strip())
            if match:
                var_name = match.group(1)
                md_value = BuiltIn().get_variable_value(f"${{{var_name}}}")
                if md_value:
                    try:
                        blocks = markdown_to_canvas_blocks(md_value)
                        new_main.extend(blocks)
                        continue  # 不保留原来的 item
                    except Exception as e:
                        print(f"[Markdown 渲染失败] {var_name}: {e}")
        new_main.append(item)

    template_data["data"]["data"]["main"] = new_main
    return template_data


def process_img_in_main(template_data):
    """
    找出 main[] 中的 ${img:变量}，并替换成图片块
    """
    main_list = template_data.get("data", {}).get("data", {}).get("main", [])
    new_main = []
    for item in main_list:
        value = item.get("value", "")
        if isinstance(value, str):
            match = img_var_pattern.fullmatch(value.strip())
            if match:
                var_name = match.group(1)
                md_value = BuiltIn().get_variable_value(f"${{{var_name}}}")
                if md_value:
                    try:
                        blocks = img_path_to_canvas_blocks(md_value)
                        new_main.extend(blocks)
                        continue  # 不保留原来的 item
                    except Exception as e:
                        print(f"[Markdown 渲染失败] {var_name}: {e}")
        new_main.append(item)

    template_data["data"]["data"]["main"] = new_main
    return template_data

def img_path_to_canvas_blocks(img_path: str) -> list:
    """
        组一个图片的blocks
    """
    return [{
        "type": "image",
        "value": img_path
    }]