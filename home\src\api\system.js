import { request } from '@/utils/axios';
import { useUserStore } from "@/stores/user";
import { aesDecrypt } from '@/utils/sysCrypto.js';
import utils from '@/utils/utils'
let retryNum = 0
const retryTime = 1500
const systemApi = {
    /* 新增程序信息 */
    saveLog(params) {
        return request.post('/uniwim/ump/accessLog/saveLog', params);
    },
    /* 游客模式埋点 */
    saveAccessTouristLog(params) {
        return request.post('/uniwim/ump/accessTouristLog/saveLog', params);
    },
    /* 根据appkey查询最新包*/
    appPackage(params, config) {
        return request.post('/uniwim/package-api/appPackage/search', params, config);
    },
    /*加密key*/
    getEncryptKey() {
        return request.get('/uniwim/ump/key');
    },
    /*修改用户密码*/
    changePwd(params, config = { meta: { isMessage: true } }) {
        return request.post('/uniwim/ump/user/changePwd', params, config);
    },
    /*右上角修改用户密码*/
    changeUserPwd(params, config = { meta: { isMessage: true } }) {
        return request.post('/uniwim/dmp/user/changePwd', params, config);
    },
    /*刷新token*/
    refreshToken(params, config = {}) {
        return request.post('/uniwim/ump/refreshToken', params, config);
    },
    /*发送验证码*/
    verificationCode(params, config = {}) {
        return request.post('/uniwim/dmp/verificationCode', params, config);
    },
    /*验证码登录*/
    verificationCodeLogin(params, config = {}) {
        return request.post('/uniwim/dmp/verificationCodeLogin', params, config);
    },
    /*忘记密码*/
    forgetPwd(params, config = {}) {
        return request.post('/uniwim/dmp/forgetPwd', params, config);
    },
    /*手机验证码修改密码*/
    verificationChangePwd(params, config = { meta: { isMessage: true } }) {
        return request.post('/uniwim/dmp/verificationChangePwd', params, config);
    },
    /*获取验证码图片*/
    getLoginCode(timeVal) {
        return '/uniwim/ump/loginCode.png?time=' + timeVal;
    },
    /*获取扫码结果*/
    getScanQrcodeInterface(params, config = { meta: { isData: false } }) {
        return request.get('/uniwim/ump/scanQrcodeInterface', params, config);
    },
    /*查询全部租户列表*/
    getTenantList(params) {
        return request.get('/uniwim/dmp/getTenantUserOauth', params);
    },
    /*租户切换*/
    changeTenant(params) {
        return request.post('/uniwim/dmp/changeTenant', params);
    },
    /* 根据名称获取租户详情 */
    getDetailByName(name) {
        return request.get('/uniwim/dmp/tenant/detailByName?name=' + name);
    },
    /*sass登录*/
    saasLogin(params, config = {}) {
        return request.post('/uniwim/dmp/login', params, config);
    },
    /*注册*/
    register(params, config = {}) {
        return request.post('/uniwim/dmp/register', params, config);
    },
    /* S7登录*/
    uniLogin(url, params, config) {
        return request.post('/uniwim/ump/uniLogin?url=' + url, params, config);
    },
    /*获取系统配置*/
    initConfigs(type) {
        return new Promise((resolve, reject) => {
            let token = utils.getUniwaterUtoken();
            request
                .get(!token ? '/uniwim/ump/cfg-encrypt' : utils.GetQueryString('menuId') ? '/uniwim/dmp/tenant/sysConfig/get' : `/uniwim/ump/cfg`, {})
                .then((res) => {
                    let result;
                    if (!token) {
                        result = JSON.parse(aesDecrypt(res));
                    } else {
                        result = res;
                    }
                    const userStore = useUserStore()
                    // if (result.customCss) {
                    //     let css = result.customCss,
                    //         head = document.getElementsByTagName('head')[0],
                    //         style = document.createElement('style');
                    //     style.id = 'customStyle';
                    //     style.type = 'text/css';
                    //     if (style.styleSheet) {
                    //         style.styleSheet.cssText = css;
                    //     } else {
                    //         style.appendChild(document.createTextNode(css));
                    //     }
                    //     head.appendChild(style);
                    // }
                    result.displayItems = result && result.displayItems ? JSON.parse(result.displayItems) : []; //显示项
                    result.yinuoEntrancedisplay = result && result.yinuoEntrancedisplay ? JSON.parse(result.yinuoEntrancedisplay) : []; //一诺入口显示
                    result.copyrightPosition = result && result.copyrightPosition ? JSON.parse(result.copyrightPosition) : []; //备案信息
                    result.loginAccountType = result.loginAccountType ? result.loginAccountType.split(',') : [];
                    result.forcePasswordChange = result && result.forcePasswordChange ? JSON.parse(result.forcePasswordChange) : []; //强制修改密码
                    result.resourceType = result.resourceType ? JSON.parse(result.resourceType) : [];

                    result.monitoringCenterIsShow = result && result.monitoringCenterIsShow && result.monitoringCenterIsShow === '0' ? result.monitoringCenterIsShow : '1';
                    result.tagNum = result && result.tagNum && result.tagNum ? result.tagNum : '8';
                    result.themeConfiguration = result && result.themeConfiguration && result.themeConfiguration ? result.themeConfiguration : 'default';
                    if (result.lockingIsOpen == '1') {
                        // 锁定开启时
                        result.loginTime = Number(result.loginTime);
                        result.loginCountMax = Number(result.loginCountMax);
                        result.loginTimeSpan = Number(result.loginTimeSpan);
                    }
                    if (!result.globalSearch) {
                        result.globalSearch = '1';
                    }
                    result.appTemp = result.appTemp ? JSON.parse(result.appTemp) : [];
                    result.appBannerList = result.appBannerList ? JSON.stringify(result.appBannerList) : '[]'; //轮播图

                    userStore.setConfigs(result);
                    // 浏览器图标
                    const favicon = result.favicon ? result.favicon : null;
                    const icoElement = document.querySelector('#ico');
                    // 2. 修改href属性
                    if (icoElement&&favicon) { // 增加存在性判断，避免报错
                    // icoElement.href = favicon;
                    }
                    // 浏览器标题自定义
                    // if (utils.GetQueryString('source') === 'App') {
                    //     document.title = '一诺AI';
                    // } else {
                    //     if (utils.GetQueryString('menuId')) {
                    //         // 未配置浏览器标题，显示：系统名称；未配置系统名称，显示：租户名称
                    //         let titleName = result.title || result.name || result.tenantName;
                    //         document.title = titleName;
                    //     } else if (result.title && !utils.GetQueryString('preview') && !utils.GetQueryString('menuId')) {
                    //         document.title = result.title;
                    //     }
                    // }
                    resolve(result);
                })
                .catch((e) => {
                    reject(e);
                });
        });
    },
    /*获取当前登录用户信息*/
    initUserInfo() {
        return new Promise((resolve, reject) => {
            request
                .get(`/uniwim/ump/currUserInfo`, {})
                .then((result) => {
                    window.WIMAI_CurrentUser = result;
                    if (result) {
                        const userStore = useUserStore()
                        // 登录信息存储
                        userStore.setUserInfo(result);
                        resolve(result);
                    } else {
                        resolve({ tenantId: '5d89917712441d7a5073058c' }); // 默认和达科技租户，如果没有获取到信息的话
                        if (retryNum < 10) {
                            retryNum++
                            setTimeout(() => {
                                systemApi.initUserInfo()
                            }, retryTime);
                        }
                    }
                })
                .catch((err) => {
                    resolve({ tenantId: '5d89917712441d7a5073058c' });
                    if (retryNum < 10) {
                        retryNum++
                        setTimeout(() => {
                            systemApi.initUserInfo()
                        }, retryTime);
                    }
                });
        });
    },
};
export default systemApi;
