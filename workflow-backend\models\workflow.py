"""
工作流数据模型定义
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field


class NodePosition(BaseModel):
    """节点位置"""
    x: float
    y: float


class NodeData(BaseModel):
    """节点数据"""
    label: str
    config: Dict[str, Any] = Field(default_factory=dict)
    category: str
    description: Optional[str] = None
    componentType: Optional[str] = None
    inputs: List[str] = Field(default_factory=list)
    outputs: List[str] = Field(default_factory=list)


class WorkflowNode(BaseModel):
    """工作流节点"""
    id: str
    type: str
    position: NodePosition
    data: NodeData

class WorkflowEdge(BaseModel):
    """工作流边（连接）"""
    id: str
    source: str
    target: str
    sourceHandle: Optional[str] = None
    targetHandle: Optional[str] = None
    type: str = "control"  # control 或 data
    animated: bool = False

class WorkflowCondition:
    id:str
    condition_str:str
    next_node_id:str

    def __init__(self, idd: str, next_node_id: str,condition_str: str):
        self.id = idd  # 实例属性
        self.condition_str = condition_str
        self.next_node_id = next_node_id

class WorkflowNodeConditions:
    node_id: str
    conditions: List[WorkflowCondition]
    else_next_node_id: str

    def __init__(self, idd: str,conditions: List[WorkflowCondition]):
        self.node_id = idd  # 实例属性
        self.conditions =conditions # 实例属性
        self.else_next_node_id = ""


class Viewport(BaseModel):
    """视口信息"""
    x: float = 0
    y: float = 0
    zoom: float = 1


class WorkflowMetadata(BaseModel):
    """工作流元数据"""
    name: str = "未命名工作流"
    description: str = ""
    version: str = "1.0.0"
    createdAt: str = Field(default_factory=lambda: datetime.now().isoformat())
    updatedAt: str = Field(default_factory=lambda: datetime.now().isoformat())


class WorkflowData(BaseModel):
    """完整的工作流数据"""
    nodes: List[WorkflowNode]
    edges: List[WorkflowEdge]
    viewport: Viewport = Field(default_factory=Viewport)
    metadata: WorkflowMetadata = Field(default_factory=WorkflowMetadata)
    variables: List[Dict[str, Any]] = Field(default_factory=dict)


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(str, Enum):
    """日志级别枚举"""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"


class ExecutionLog(BaseModel):
    """执行日志"""
    timestamp: datetime
    level: LogLevel
    message: str
    source: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class NodeExecutionResult(BaseModel):
    """节点执行结果"""
    node_id: str
    status: ExecutionStatus
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None  # 执行时长（秒）
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    logs: List[ExecutionLog] = Field(default_factory=list)


class ExecutionResult(BaseModel):
    """完整的执行结果"""
    execution_id: str
    workflow_id: Optional[str] = None
    status: ExecutionStatus
    pid: str=""
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    node_results: List[NodeExecutionResult] = Field(default_factory=list)
    logs: List[ExecutionLog] = Field(default_factory=list)
    output_files: List[str] = Field(default_factory=list)  # 生成的文件路径
    robot_code: Optional[str] = None  # 生成的Robot Framework代码
    robot_output: Optional[str] = None  # Robot Framework执行输出
    return_obj: Any = None

class ComponentDefinition(BaseModel):
    """组件定义"""
    type: str
    label: str
    description: str
    category: str
    icon: str
    config_schema: Dict[str, Any] = Field(default_factory=dict)
    inputs: List[str] = Field(default_factory=list)
    outputs: List[str] = Field(default_factory=list)
    robot_template: str  # Robot Framework模板


class ExecutionOptions(BaseModel):
    """执行选项"""
    output_dir: Optional[str] = None
    log_level: LogLevel = LogLevel.INFO
    timeout: Optional[int] = None  # 超时时间（秒）
    variables: Dict[str, Any] = Field(default_factory=dict)
    include_tags: List[str] = Field(default_factory=list)
    exclude_tags: List[str] = Field(default_factory=list)
    dry_run: bool = False  # 是否为试运行
    parallel: bool = False  # 是否并行执行


class ScheduleConfig(BaseModel):
    """调度配置"""
    enabled: bool = False
    cron_expression: Optional[str] = None
    interval_seconds: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    max_instances: int = 1
    coalesce: bool = True  # 是否合并错过的执行
    misfire_grace_time: int = 300  # 错过执行的宽限时间（秒）


class WorkflowTemplate(BaseModel):
    """工作流模板"""
    id: str
    name: str
    description: str
    category: str
    tags: List[str] = Field(default_factory=list)
    workflow: WorkflowData
    preview_image: Optional[str] = None
    created_by: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    usage_count: int = 0


class ValidationError(BaseModel):
    """验证错误"""
    node_id: Optional[str] = None
    edge_id: Optional[str] = None
    field: Optional[str] = None
    message: str
    severity: str = "error"  # error, warning, info


class ValidationResult(BaseModel):
    """验证结果"""
    is_valid: bool
    errors: List[ValidationError] = Field(default_factory=list)
    warnings: List[ValidationError] = Field(default_factory=list)

class CustomKeyWordResult(BaseModel):
    """自定义关键字执行结果"""
    stdout: object = None
    stderr: str = ""
    rc: int = 0

class ExecutionNode(BaseModel):
    node: Optional[WorkflowNode] = None
    params: Optional[Dict] = {}

# 工具函数
def create_execution_id() -> str:
    """创建执行ID"""
    import uuid
    return f"exec_{uuid.uuid4().hex[:8]}"


def create_node_id() -> str:
    """创建节点ID"""
    import uuid
    return f"node_{uuid.uuid4().hex[:8]}"


def create_edge_id() -> str:
    """创建边ID"""
    import uuid
    return f"edge_{uuid.uuid4().hex[:8]}"
