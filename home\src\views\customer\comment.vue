<template>
    <div class="customer-link">
        <div class="content-inner">
            <div class="content-left">
                <div class="description-top">
                    <div class="description-top-text">
                        <div class="description-title">提交申请</div>
                        <p style="margin-bottom:25px;">我们将为您提供</p>
                        <h1>解读产品价值：</h1>
                        <p>全方位为您介绍WimTask的功能，服务与报价</p>
                    </div>
                    <div class="description-top-img">
                        <img width="330" height="232" src="@/assets/images/homepage/comment-icon.png" alt="">
                    </div>
                    
                </div>
                <div class="description-bottom">
                    <div class="description-item">
                        <h1>定制化演示：</h1>
                        <p>针对您的业务痛点，定制产品功能演示，让您直观地看到WimTask为您的工作流程带来的改变</p>
                    </div>
                    <div class="description-item">
                        <h1>专属解决方案：</h1>
                        <p>制定最适合您的企业的自动化解决方案，释放团队潜力，让员工专注于创造性的核心业务</p>
                    </div>
                    <div class="description-item">
                        <p>为了能及时与您沟通后续事宜，WimTask团队将尽快给您回电，请您保持电话畅通</p>
                    </div>
                    <div class="hotline-item">
                        <h1>热线：</h1>
                        <p>0573-82875638</p>
                    </div>
                    
                </div>
            </div>
            <div class="content-right">
                <div class="customer-form">
                    <div class="form-title">联系我们</div>
                    <div class="form-content">
                        <el-form ref="dlyModeRef" :show-message="true" :rules="rules" label-position="top" :model="dlyMode" label-width="80px">
                            <el-form-item label="姓名" prop="userName" required style="margin-bottom: 28px !important">
                                <el-input v-model="dlyMode.userName" placeholder="请输入姓名" class="name-item"></el-input>
                            </el-form-item>
                            <el-form-item label="手机号" prop="userPhone" required style="margin-bottom: 28px !important">
                                <el-input v-model="dlyMode.userPhone" placeholder="请输入手机号"  class="phone-item"></el-input>
                            </el-form-item>
                            <el-form-item label="公司名称" prop="companyName" style="margin-bottom: 28px !important">
                                <el-input v-model="dlyMode.companyName" placeholder="请输入公司名称" class="company-item"></el-input>
                            </el-form-item>
                            <el-form-item label="留言" prop="message" required style="margin-bottom: 28px !important">
                                <el-input
                                    v-model="dlyMode.message"
                                    :rows="6"
                                    type="textarea"
                                    placeholder="请将您的需求告知我们"
                                />
                            </el-form-item>
                        </el-form>
                        <el-form-item>
                            <el-button class="submit-btn" type="primary" @click="submitForm">提交申请</el-button>
                        </el-form-item>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部信息模块 -->
        <footer class="footer-section">
            <div class="container">
                <!-- <div class="footer-main">
                    <div class="footer-left">
                        <div class="footer-left-title">联系我们</div>
                        <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                        <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                        <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                    </div>
                    <div class="footer-middle">
                        <p>一诺数字助理</p>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                            class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                            class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                    </div>
                    <div class="footer-right">
                        <div class="qrcode-box">
                            <p>一诺APP</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                            </div>
                        </div>
                        <div class="qrcode-box">
                            <p>度量公众号</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                            </div>
                        </div>
                    </div>
                </div> -->
                <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
            </div>

        </footer>
    </div>
</template>

<script lang="ts" setup>
import {ref,reactive} from 'vue'
import saasApi from '@/api/index';
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const dlyMode = ref({
    companyName: '',
    userPhone: '',
    userName: '',
    message: ''
})

const dlyModeRef = ref(null)

const rules = reactive<FormRules>({
    userName: [
        { required: true, message: '请输入姓名', trigger: 'change', }
    ],
    userPhone: [
        { required: true, message: '请输入手机号', trigger: 'change' },
        { 
            pattern: /^1[3-9]\d{9}$/, 
            message: '请输入正确的手机号码格式', 
            trigger: 'blur' 
        }
    ],
    message: [
        { required: true, message: '请输入您的需求', trigger: 'change' }
    ],
})

const submitForm = async() => {
    console.log(dlyMode)
    let isValidate = await dlyModeRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
            // ElMessage.eoor9
        }
    })
    if (!isValidate) return
    let params = {
        ...dlyMode.value,
        source:route.query.from||'',
        servicePackageId:route.query.serviceId||''
    }
  saasApi.AIAgentWimtaskCustomerApplicationInsert(params).then((res:any) => {
    if (res.Code == 0) {
      ElMessage({
        message: '保存成功',
        type: 'success'
      })
      dlyMode.value = {
            companyName: '',
            userPhone: '',
            userName: '',
            message: ''
        }
        setTimeout(()=>{
            dlyModeRef.value.clearValidate();
        },100)
        
    }
  })
}

</script>

<style scoped lang="less">
.customer-link{
    width:100%;
    height:100%;
    padding-top:72px;
    background:url('@/assets/images/homepage/comment-bg.png') no-repeat center center;
    background-size: cover;
    .content-inner{
        width:1200px;
        height:calc(100% - 48px);
        margin:auto;
        display: flex;
        align-items:center;
        p{
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #797E86;
        }
        h1{
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 20px;
            color: #222222;
            line-height:30px;
            margin-bottom:10px;
        }
        .content-left{
            margin-right:57px;
            .description-top{
                display: flex;
                align-items: center;
                margin-bottom:52px;
                .description-top-img{
                    width: 330px;
                    height: 232px;
                    margin-left:30px;
                }

                .description-top-text{
                    .description-title{
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 40px;
                        color: #000000;
                        margin-bottom:54px;
                    }
                }
            }
            .description-bottom{
                width:592px;
                .description-item{
                    margin-bottom:28px;
                }
                p{
                    line-height:24px;
                    height:auto;
                }
                .hotline-item{
                    h1{
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 20px;
                        color: #222222;
                        line-height: 30px;
                    }
                    p{
                        font-family: ArialMT;
                        font-size: 20px;
                        color: #797E86;
                        line-height: 30px;

                    }

                }
            }

        }
        .content-right{
            .customer-form{
                width: 450px;
                height: 662px;
                background: #FFFFFF;
                box-shadow: 0 2px 20px 0 #001c371a;
                border-radius: 8px;
                padding:30px 40px;
                box-sizing: border-box;
                .form-title{
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 24px;
                    color: #222222;
                    letter-spacing: 0;
                    margin-bottom:30px;
                }
                .phone-item,.name-item,.company-item{
                    height: 44px;
                    background: #FFFFFF;
                    // border: 1px solid #DADADA;
                    border-radius: 4px;
                }
                .submit-btn{
                    width: 370px;
                    height: 44px;
                    background: #0054D2;
                    border-radius: 4px;
                    font-family: PingFangSC-Regular;
                    font-weight: 400;
                    font-size: 18px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    text-align: center;
                }
            }
        }
    }
}
// 底部样式
  .footer-section {
      background-color: #f5f5f6;
      // padding: 20px 0;
      width: 100%;
      min-width: 1400px;
      .container {
          width: 1400px;
          margin: 0 auto;
          // display: flex;
          justify-content: space-between;

          .footer-left-title {
              width: 64px;
              height: 24px;
              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 16px;
              margin-bottom: 12px;
              color: #191919;
          }

          .footer-left {
              p {
                  font-family: SourceHanSansSC-Regular;
                  font-weight: 400;
                  font-size: 14px;
                  color: #595959;
                  line-height: 40px;
                  display: flex;
                  align-items: center;

                  &.address i {
                      background: url('../../assets/images/homepage/position.png') no-repeat;
                      background-size: cover;
                  }

                  &.tel i {
                      background: url('../../assets/images/homepage/telephone.png') no-repeat;
                      background-size: cover;
                  }

                  &.email i {
                      background: url('../../assets/images/homepage/email.png') no-repeat;
                      background-size: cover;
                  }
              }

              i {
                  display: block;
                  width: 14px;
                  height: 14px;
                  margin-right: 10px;

              }
          }

          >div {
              flex: 1;
              margin-right: 20px;

              &:last-child {
                  margin-right: 0;
              }


          }
      }

      .footer-main {
          width: 100%;
          display: flex;
          justify-content: space-between;
      }

      .footer-middle {
          height: 144px;

          p {
              height: 24px;
              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 16px;
              color: #191919;
              margin-bottom: 24px;
          }

          .download-btn {
              width: 149px;
              height: 38px;
              line-height: 38px;
              text-align: center;
              border: 1px solid #0054d2;
              border-radius: 4px;
              border-radius: 4px;
              text-decoration: none;
              display: flex;
              align-items: center;
              text-align: center;
              box-sizing: border-box;
              padding: 0 10px;
              // justify-content: center;
              margin-bottom: 20px;
              color: #0054d2;

              &:hover {
                  opacity: 0.8;
              }

              i {
                  display: block;
                  margin-right: 10px;
              }

              .ios-icon {
                  width: 14px;
                  height: 14px;
                  background: url("../../assets/images/homepage/ios.png") no-repeat;
                  background-size: cover;
              }

              .windows-icon {
                  width: 14px;
                  height: 14px;
                  background: url("../../assets/images/homepage/win.png") no-repeat;
                  background-size: cover;
              }
          }
      }

      .footer-right {
          height: 144px;
          font-weight: 400;
          font-size: 16px;
          color: #191919;
          display: flex;
          gap: 30px;
      }

      .qrcode-box {
          p {
              font-weight: 400;
              font-size: 16px;
              color: #191919;
              height: 24px;
              margin-bottom: 15px;
              text-align: center;
          }
      }

      .qrcode {
          width: 118px;
          height: 118px;
          background: #ffffff00;
          border: 1px solid #DEDEDE;
          border-radius: 2px;
          text-align: center;

          img {
              width: 100px;
              height: 100px;
              display: block;
              margin-top: 9px;
              margin-left: 9px;
          }
      }

      .copyright {
          width: 100%;
          height:48px;
          line-height:48px;
          text-align: center;
          // margin-top: 75px;
          color: #898e96;
          font-size: 12px;
          position: inherit;
          a{
            color: #898e96;
            text-decoration: none;
          }
      }
  }
</style>