<template>
  <el-form-item :label="configMeta.label || configKey">
    <div class="switch-container">
      <el-switch
        :model-value="configValue"
        @update:model-value="handleUpdate"
        :active-text="configMeta.activeText"
        :inactive-text="configMeta.inactiveText"
        :active-value="configMeta.activeValue !== undefined ? configMeta.activeValue : true"
        :inactive-value="configMeta.inactiveValue !== undefined ? configMeta.inactiveValue : false"
        size="small"
      />

      <!-- 状态文本 -->
      <span v-if="statusText" class="status-text">
        {{ statusText }}
      </span>
    </div>

    <!-- 帮助文本 -->
    <div v-if="configMeta.help" class="config-help">
      <el-icon :size="12">
        <QuestionFilled />
      </el-icon>
      <span>{{ configMeta.help }}</span>
    </div>

    <!-- 详细说明 -->
    <div v-if="configMeta.description" class="config-description">
      {{ configMeta.description }}
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

interface Props {
  configKey: string
  configValue: any
  nodeType?: string
}

interface Emits {
  (e: 'update', key: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置元数据
const configMeta = computed(() => {
  return getConfigMeta(props.nodeType, props.configKey)
})

// 状态文本
const statusText = computed(() => {
  if (configMeta.value.showStatus) {
    return props.configValue
      ? configMeta.value.activeText || '开启'
      : configMeta.value.inactiveText || '关闭'
  }
  return ''
})

// 更新处理
const handleUpdate = (value: any) => {
  emit('update', props.configKey, value)
}

// 获取配置元数据
function getConfigMeta(nodeType: string | undefined, key: string) {
  const configDefinitions: Record<string, Record<string, any>> = {
    new_browser: {
      headless: {
        label: '无头模式',
        help: '启用后浏览器将在后台运行，不显示界面',
        description: '无头模式可以提高执行速度，但无法看到浏览器操作过程',
        activeText: '启用',
        inactiveText: '禁用',
        showStatus: true,
      },
    },
    input_text: {
      clear_first: {
        label: '先清空内容',
        help: '输入文本前是否先清空输入框的现有内容',
        description: '建议启用，避免与现有内容混合',
        activeText: '清空',
        inactiveText: '追加',
        showStatus: true,
      },
    },
    click_element: {
      double_click: {
        label: '双击',
        help: '是否执行双击操作而不是单击',
        activeText: '双击',
        inactiveText: '单击',
        showStatus: true,
      },
      right_click: {
        label: '右键点击',
        help: '是否执行右键点击操作',
        activeText: '右键',
        inactiveText: '左键',
        showStatus: true,
      },
    },
    get_text: {
      trim_whitespace: {
        label: '去除空白字符',
        help: '是否去除获取文本前后的空白字符',
        description: '包括空格、制表符、换行符等',
        activeText: '去除',
        inactiveText: '保留',
        showStatus: true,
      },
    },
    set_variable: {
      global_scope: {
        label: '全局变量',
        help: '是否将变量设置为全局作用域',
        description: '全局变量可以在整个工作流中使用',
        activeText: '全局',
        inactiveText: '局部',
        showStatus: true,
      },
    },
    wait: {
      skip_if_element_present: {
        label: '元素存在时跳过',
        help: '如果指定元素已存在则跳过等待',
        description: '可以提高执行效率，避免不必要的等待',
        activeText: '跳过',
        inactiveText: '等待',
        showStatus: true,
      },
    },
  }

  const componentConfig = configDefinitions[nodeType || ''] || {}
  return (
    componentConfig[key] || {
      label: key,
      help: '',
      activeText: '是',
      inactiveText: '否',
      showStatus: false,
    }
  )
}
</script>

<style scoped>
.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-text {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.config-help {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 11px;
  color: #909399;
  line-height: 1.4;
}

.config-description {
  margin-top: 4px;
  font-size: 11px;
  color: #606266;
  line-height: 1.4;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e4e7ed;
}

/* Element Plus 开关样式调整 */
:deep(.el-switch__label) {
  font-size: 12px;
  color: #606266;
}

:deep(.el-switch__label.is-active) {
  color: #409eff;
  font-weight: 500;
}
</style>
