import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { ElMessageBox } from 'element-plus'

import App from './App.vue'
import router from './router'

// 自定义scss样式
import '@/styles/index.scss'
// 自定义iconfont样式
import '@/assets/fonts/iconfont.css';
// 自定义panel-iconfont样式 左侧面板指令图标
import '@/assets/action-fonts/iconfont.css';
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import "@/assets/css/quill-video-resize.css";
import '@/utils/hd.js'


import moment from 'moment';
window.moment = moment;

window.addEventListener('unhandledrejection', event => {
  // 捕获动态导入失败的错误

  if (event && event.reason && event.reason.message.includes('Failed to fetch dynamically imported module')) {
    ElMessageBox.confirm(
      `应用已更新，请刷新后继续操作。`,
      `提示`,
      {
        closeOnClickModal:false,
        closeOnPressEscape:false,
        showCancelButton:false,
        showClose:false,
        confirmButtonText: '确定',
        type: 'warning',
      },
    ).then(res=>{
      location.reload()
    })
    // 防止默认处理（如控制台输出）
    event.preventDefault()
  }
})

// sessionStorage.setItem('UniWimAuthorization', `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTEzNTIzNDcsImlkIjoiMTc5NjQ4MjY3ODgyNzA0NDg2NSIsImp3dElkIjoiYjg2NDI3ZDg0YTI3NGNkYTliZWI2ZWZiODVhMzQwMDgiLCJ1aWQiOiIxNzk2NDgyNjc4ODI3MDQ0ODY1IiwidGVuYW50SWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJjaWQiOiI1ZDg5OTE3NzEyNDQxZDdhNTA3MzA1OGMiLCJtYWluSWQiOiIxNzkwNTgyMDk3ODY4NTMzNzYxIiwiYXZhdGFyIjoiaHR0cHM6Ly9oZGtqLmRsbWVhc3VyZS5jb20vdW5pd2ltL3VwbG9hZHMvMjAyNC81LzE3OTY0ODI2Nzg4MjcwNDQ4NjUucG5nIiwibmFtZSI6Iuefs-WBpeaWhyIsImFjY291bnQiOiJaVDAzMCIsIm1vYmlsZSI6IjE3ODA1ODU2NDEzIiwic24iOiJaVDAzMCIsImdyb3VwIjoiNjgiLCJ5aGxvTnVtIjoiMTAwMDIwMjUyIiwiaXNBZG1pbiI6ZmFsc2UsImNoYW5uZWwiOiJ3ZWIiLCJjb21wYW55Ijp7ImlkIjoiNjgifSwidG9rZW5mcm9tIjoidW5pd2ltIiwidXNlclR5cGUiOiJ1c2VyIiwiZXhwIjoxNzUxOTU3NDQ3fQ.CbFaAXpR1Xiesn41FRqLBd8WgoyEiDsD_Lw26DuHJmE`)

const InitReady = () => {
  let configList = []
  return new Promise(resolve => {
    resolve(true)
    // Promise.all([
    //   systemApi.initUserInfo(),
    //   saasApi.AITaskConfigList(),
    // ]).then(([userInfo, configs, template]) => {
    //   // useUserStore().setUserInfo(userInfo);
    //   if(configs && configs.length){
    //     configList = configs
    //     useUserStore().setConfigs(configList);
    //   }
    // }).finally(async ()=>{
    //   resolve(true)
    // })
  })
};

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)

app.use(ElementPlus, { zIndex: 3000, locale: zhCn })

InitReady().then(()=>{
  app.mount('#app')
})
