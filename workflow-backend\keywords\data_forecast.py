# -*- coding: utf-8 -*-

#from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from robotlibcore import keyword
from typing import Dict, List, Optional, Any,Set
import logging
import json
import requests
import base64
import re
import logging
from config import settings  # 绝对导入
from config import globals
import requests
from datetime import datetime, timedelta
import statistics
from models import algorith_manalysis

class OutputItem:
    his_win_data: Optional[list[float]] = None
    his_win_time: Optional[list[float]] = None
    forecast_data: Optional[list[float]] = None
    forecast_time: Optional[list[float]] = None
    forecast_img: Optional[str] = None
    forecast_avg: Optional[float] = None
    forecast_sum: Optional[float] = None
    forecast_max: Optional[float] = None
    forecast_min: Optional[float] = None

    def __init__(self,his_win_data:list[float],his_win_time:list[float],forecast_data:list[float],forecast_time:list[float],forecast_img: str,forecast_avg: float,forecast_sum: float,forecast_max: float,forecast_min: float):
        self.his_win_data = his_win_data
        self.his_win_time = his_win_time
        self.forecast_data = forecast_data
        self.forecast_time = forecast_time
        self.forecast_img = forecast_img
        self.forecast_avg = forecast_avg
        self.forecast_sum = forecast_sum
        self.forecast_max = forecast_max
        self.forecast_min = forecast_min

        # 方法 1：添加 to_dict 方法

    def to_dict(self):
        return {
            "his_win_data": self.his_win_data,
            "his_win_time": self.his_win_time,
            "forecast_data": self.forecast_data,
            "forecast_time": self.forecast_time,
            "forecast_img": self.forecast_img,
            "forecast_avg": self.forecast_avg,
            "forecast_sum": self.forecast_sum,
            "forecast_max": self.forecast_max,
            "forecast_min": self.forecast_min,
        }


@keyword("Data Forecast")
def data_forecast(his_win: list[any], day_count: float,forecast_type:str,forecast_format:str,set_step:float):
    try:
        # 请求地址
        url = settings.DlY_URl + "/algorithmanalysis/CallAlgorithm"

        days = int(day_count)

        end_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
        start_timestamp = end_timestamp - days * 24 * 60 * 60  # 30天前

        #his_win_json = json.loads(his_win)

        his_win_array = []
        print("历史数据")
        print(his_win)
        if forecast_format == "时间序列":
            his_win_array = fill_missing_timestamps_forecast(his_win, start_timestamp, end_timestamp)
        else:
            his_win_array = his_win

        his_win_str = json.dumps(his_win_array, ensure_ascii=False)

        input_list: list[algorith_manalysis.InputItem] = [
            algorith_manalysis.InputItem(name="his_win", type="[]float64", info="历史窗口", default_val=his_win_str),
            algorith_manalysis.InputItem(name="day_count", type="float64", info="历史窗口天数", default_val=day_count),
            algorith_manalysis.InputItem(name="set_step", type="float64", info="输出数据时间步长/min", default_val=set_step),
            algorith_manalysis.InputItem(name="day_min", type="float64", info="第一次选取最少天数", default_val=8),
            algorith_manalysis.InputItem(name="day_mean", type="float64", info="取多天数据的平均值", default_val=4),
            algorith_manalysis.InputItem(name="delt_max", type="float64", info="第一个时刻的最大偏移量", default_val=1000)
        ]

        # 请求头
        headers = {
            "Authorization": "Bearer " + globals.token,
            "Content-Type": "application/json"
        }

        # 方法 2：使用 default 参数
        json_str = json.dumps(input_list, default=custom_serializer, ensure_ascii=False)

        url_name = ""
        if forecast_type == "流量":
            url_name = "algoztdata_v10/DataForecastF"
        elif forecast_type == "压力":
            url_name = "algoztdata_v10/DataForecastP"
        else:
            url_name = "algoztdata_v10/DataForecastLevel"

        payload = {
            "API_URL": url_name,  # 根据你的实际模型名称调整
            "PARA_JSON": json_str,
            "TOKEN": globals.token,
            "VER_ID": "0dc68135-8891-44de-99ff-9e41b78fceb0"
        }
        # 发起 POST 请求
        response = requests.post(url, headers=headers, json=payload)

        response_json_response = response.json().get("RESPONSE", {})

        response_json_response_json = json.loads(response_json_response)

        data_out = response_json_response_json["Data"]["data_out"]

        out_item = OutputItem(his_win, get_his_win_time(data_out,start_timestamp),data_out,get_forecast_time(data_out,end_timestamp), "",statistics.mean(data_out),sum(data_out) / 60.0,max(data_out),min(data_out))



        json_str = json.dumps(out_item, default=custom_serializer_out_forecast, ensure_ascii=False)
        # forecast = {
        #     "forecast_data":filter_data_by_step(data_out, set_step),
        #     "forecast_avg": statistics.mean(data_out),
        #     "forecast_sum": sum(data_out),
        #     "forecast_max": max(data_out),
        #     "forecast_min": min(data_out),
        #     "forecast_img": ""
        # }

        # forecast = OutputItem(forecast_data=filter_data_by_step(data_out, set_step),
        #                       forecast_avg=statistics.mean(data_out), forecast_sum=sum(data_out) / 60.0,
        #                       forecast_max=max(data_out), forecast_min=min(data_out), forecast_img="")
        #
        # print(forecast)
        print(json_str)
        return json_str
    except Exception as e:
        return f"f_forecast error:{str(e)}"


def custom_serializer(obj):
    if isinstance(obj, algorith_manalysis.InputItem):
        return obj.to_dict()  # 或直接使用 obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")

def custom_serializer_out_forecast(obj):
    if isinstance(obj, OutputItem):
        return obj.to_dict()  # 或直接使用 obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")


def fill_missing_timestamps_forecast(data, start_timestamp, end_timestamp):
    """
    补全时间序列数据，确保每1分钟有一个数据点
    缺失的数据点填充为 -9999
    """
    #排序数据
    data.sort(key=lambda x: x['timestamp'],reverse=False)
    # 将输入数据转换为字典，键为时间戳，值为数据点
    data_dict = {item["timestamp"]: item["value"] for item in data}

    # 计算最近30天的时间范围
    # end_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
    # start_timestamp = end_timestamp - days * 24 * 60 * 60  # 30天前

    # 生成完整的时间序列
    filled_data = []
    current_timestamp = start_timestamp

    while current_timestamp < end_timestamp:
        # 如果当前时间戳在原始数据中存在，则使用原始值
        if current_timestamp in data_dict:
            value = data_dict[current_timestamp]
        else:
            # 否则填充为 -9999
            value = -9999

        filled_data.append({
            "timestamp": current_timestamp,
            "value": value
        })

        # 每分钟增加一次
        current_timestamp += 60

    his_win = []
    for item in filled_data:
        his_win.append(item["value"])

    return his_win


def filter_data_by_step_forecast(data: list, step: str) -> list:
    # 验证输入数据长度
    if len(data) != 1440:
        raise ValueError(f"输入数据长度必须为1440，但实际长度为{len(data)}")

    # 定义步长映射表（将时间字符串转换为分钟数）
    step_mapping = {
        "1分钟": 1,
        "5分钟": 5,
        "10分钟": 10,
        "15分钟": 15,
        "30分钟": 30,
        "1小时": 60,
        "2小时": 120,
        "4小时": 240,
        "1天": 1440
    }

    # 检查步长是否有效
    if step not in step_mapping:
        valid_steps = ", ".join(step_mapping.keys())
        raise ValueError(f"无效的时间步长。支持的选项有：{valid_steps}")

    # 计算步长对应的分钟数
    minutes = step_mapping[step]

    # 按步长过滤数据
    filtered_data = data[::minutes]

    return filtered_data

def get_forecast_time(data_out:list[float], start_timestamp):
    data_len = len(data_out)
    #start_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
    current_timestamp = start_timestamp
    forecast_time:list[float] = []
    for i in range(data_len):
        forecast_time.append(current_timestamp)
        current_timestamp += 60

    return forecast_time

def get_his_win_time(his_win:list[float], start_timestamp):
    his_len = len(his_win)
    #start_timestamp = int(datetime.now().replace(second=0, microsecond=0).timestamp())
    current_timestamp = start_timestamp
    forecast_time:list[float] = []
    for i in range(his_len):
        forecast_time.append(current_timestamp)
        current_timestamp += 60

    return forecast_time