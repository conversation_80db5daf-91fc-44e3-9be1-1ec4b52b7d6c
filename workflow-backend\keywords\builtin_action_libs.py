
from fastapi import Response

import requests
from loguru import logger
from config import globals
from config.env_config import get_config_item,AI_RUL,BPM_URL
# 内置动作执行库

# AI分析
def ai_analyze_exec(question: str='',prompt:str=''):
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer none"
    }
    items = {
        "model": "qwen3-32b",
        "messages": [
            {
                "role": "system",
                "content": prompt
            },
            {
                "role": "user",
                "content": question
            }
        ]
    }
    # todo 这里后面要换外网或支持定义模型
    url = f"{get_config_item(AI_RUL)}/v1/chat/completions"
    try:
        response = requests.post(url,json=items, headers=headers)
        content_after = ""
        if response.status_code == 200:
            content = response.json()["choices"][0]["message"]["content"]
            split_token = "</think>"
            if split_token in content:
                content_after = content.split(split_token, 1)[1].strip()
            else:
                content_after = content.strip()
        return content_after
    except Exception as e:
        logger.info("/wimai/api/task/config/query fail")
        logger.info("message:{}".format(str(e)))
        return f'AI分析异常${e}'


# 发起流程
def start_workflow(params: dict,procCode:str):
    headers = {
        "Content-Type": "application/json",
        "Authorization": globals.token
    }
    params["procCode"] = procCode
    #todo 发起流程
    # 发起流程
    url = f'{get_config_item(BPM_URL)}/bpm/customize-api/{procCode}/create-order2'
    try:
        response = requests.post(url,json=params, headers=headers)
        if response.status_code == 200:
            content = response.json()["Response"]
            # 先判断是否创建成功： Code == 0 或者 Success = Ture
            if content["Code"] == 0 or content["Success"]:
                # 获取business_key
                return content.get("business_key","")
            else:
                return f"发起流程失败,{content['Message']}"
    except Exception as e:
        logger.info("/bpm/customize-api/a2/create-order2 fail")
        logger.info("message:{}".format(str(e)))
        return f'发起流程失败${e}'
