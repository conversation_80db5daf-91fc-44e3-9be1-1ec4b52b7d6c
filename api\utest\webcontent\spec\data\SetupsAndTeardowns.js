window.setupsAndTeardownsOutput = {};

window.setupsAndTeardownsOutput["suite"] = [1,2,3,0,[],[1,0,28],[],[[4,0,0,[],[1,26,2],[[1,5,6,0,7,8,0,0,[1,26,0],[[27,2,8]]],[0,9,0,0,0,0,0,0,[1,27,1],[[0,10,6,0,11,0,0,0,[1,27,0],[]],[2,5,6,0,7,12,0,0,[1,27,0],[[27,2,12]]]]],[2,5,6,0,7,13,0,0,[1,28,0],[[28,2,13]]]]]],[[1,5,6,0,7,14,0,0,[1,26,0],[[26,2,14]]],[2,5,6,0,7,15,0,0,[1,28,0],[[28,2,15]]]],[1,1,0,0]];

window.setupsAndTeardownsOutput["strings"] = [];

window.setupsAndTeardownsOutput["strings"] = window.setupsAndTeardownsOutput["strings"].concat(["*","*SetupsAndTeardowns","*/home/<USER>/Devel/robotframework/utest/webcontent/spec/data/SetupsAndTeardowns.robot","*utest/webcontent/spec/data/SetupsAndTeardowns.robot","*Test","*Log","*BuiltIn","*<p>Logs the given message with the given level.\x3c/p>","*test setup","*Keyword with teardown","*No Operation","*<p>Does absolutely nothing.\x3c/p>","*keyword teardown","*test teardown","*suite setup","*suite teardown"]);

window.setupsAndTeardownsOutput["stats"] = [[{"elapsed":"00:00:00","fail":0,"label":"All Tests","pass":1,"skip":0}],[],[{"elapsed":"00:00:00","fail":0,"id":"s1","label":"SetupsAndTeardowns","name":"SetupsAndTeardowns","pass":1,"skip":0}]];

window.setupsAndTeardownsOutput["errors"] = [];

window.setupsAndTeardownsOutput["baseMillis"] = 1724172740155;

window.setupsAndTeardownsOutput["generated"] = 30;

window.setupsAndTeardownsOutput["expand_keywords"] = null;

window.settings = {"background":{"fail":"DeepPink"},"logURL":"log.html","reportURL":"report.html"};

