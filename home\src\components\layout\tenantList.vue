<template>
    <div class="tenantList-page-box">
        <el-popover placement="left-start" width="180" trigger="hover" :visible-arrow="false" popper-class="tenantList-popover">
            
            <template #reference>
              <div class="tenant-btn">
                <el-icon><OfficeBuilding /></el-icon>
                <span>{{ tenantName }}</span>
              </div>
            </template>
            <div class="tenantList-content">
                <div class="search-part">
                    <el-input class="search-ipt" size="mini" placeholder="请输入内容" v-model="search" @input="searchInput" clearable></el-input>
                </div>
                <div class="tenantList-box">
                    <div 
                        :class="[currentUser && currentUser.tenantId === item.id ? 'tenant-item active' : 'tenant-item']" 
                        v-for="(item, index) in tenantList" 
                        :key="'tenant' + index" 
                        :command="item.id" 
                        v-show="!item.show" 
                        @click="tenantClick(item.id)"
                    >
                        {{ item.abbName ? item.abbName : item.tenantName }}
                    </div>
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject } from 'vue';
import { request } from '@/utils/axios';
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";
import systemApi from "@/api/system";
import utils from '@/utils/utils'

const userStore = useUserStore()
const loginStore = useLoginStore()

// 类型定义
interface Tenant {
  id: string | number;
  abbName?: string;
  tenantName: string;
  show?: boolean;
  [key: string]: any;
}

// 定义类型
interface User {
  id?: string | number;
  avatar?: string;
  name?: string;
  isAdmin?: boolean;
  isDefaultPwd?: number;
}

interface Configs {
  [key: string]: any;
}

interface ChangeTenantResponse {
  token: string;
  refreshToken: string;
  tenantId: string | number;
  expire?: string;
  [key: string]: any;
}

interface MenuItem {
  Id: string | number;
  Val?: string;
  ValBuffer?: string;
  link?: string;
  [key: string]: any;
}

interface Utils {
  debounce: (delay: number, fn: (...args: any[]) => any) => (...args: any[]) => void;
  getQueryString: (key: string) => string | null;
  setLocalStorageInfo: (token: string, expire: string, refreshToken: string, tenantId: string | number) => void;
}



// 组件Props
const props = defineProps<{
  isMultiTenancy?: boolean;
}>();

// 响应式状态
const search = ref('');
const tenantList = ref<Tenant[]>([]);
const tenantName = ref('租户切换');
const wrapperDebounce = ref<((val: string) => void) | null>(null);

// 注入全局依赖

const currentUser = computed<User>(() => {
  return userStore.userInfo;
});

const configs = computed<Configs>(() => {
  return userStore.configs;
});

// 获取租户列表
const getTenantList = () => {
  systemApi
    .getTenantList({
      isAdmin: props.isMultiTenancy ? 1 : 0,
      name: search.value
    })
    .then((res) => {
      tenantList.value = res;
      if (tenantList.value.length) {
        ;
        const tenantItem = tenantList.value.filter((item) => {
          return currentUser.value && currentUser.value.tenantId === item.id;
        });
        
        if (tenantItem.length) {
          tenantName.value = tenantItem[0].abbName || tenantItem[0].tenantName;
        } else {
          tenantName.value = '租户切换';
        }
      } else {
        tenantName.value = '租户切换';
      }
    });
};

// 搜索租户
const searchInput = (val: string) => {
  debugger;
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    if (!wrapperDebounce.value) {
      wrapperDebounce.value = utils.debounce(500, getTenantList);
    }
    wrapperDebounce.value(val);
  } else {
    // 已退出登录
    // window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
    loginStore.LOGOUT()
    userStore.clearUser();
  }
};

// 设置值并跳转
const setVal = (res: ChangeTenantResponse) => {
  const tenantInfo = utils.GetQueryString('tenantInfo');
  
  if (tenantInfo) {
    const blankUrl = {
      uniwater_utoken: res.token,
      UniWimRefreshToken: res.refreshToken,
      uniwim_tenant_id: res.tenantId
    };
    const encodedUrl = 'admin.html?tenantInfo=' + window.HD.base64.encode(encodeURIComponent(JSON.stringify(blankUrl)));
    window.top.location.replace(encodedUrl);
  } else {
    utils.setLocalStorageInfo(res.token, res.expire || '', res.refreshToken, res.tenantId);
    // 模拟nextTick
    setTimeout(() => {
      location.reload();
    }, 0);
  }
};

// 租户点击事件
const tenantClick = (tenantId: string | number) => {
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    if (currentUser.value.tenantId === tenantId) {
      return;
    }
    
    systemApi
      .changeTenant({
        tenantId: tenantId,
        isAdmin: props.isMultiTenancy ? 1 : 0
      })
      .then((res) => {
        window.localStorage.removeItem('apiHost');
        const newTenantId = res.tenantId;
        const currentViewUniwim = sessionStorage.getItem('currentViewUniwim');
        const item: MenuItem | null = currentViewUniwim ? JSON.parse(currentViewUniwim) : null;
        
        // if (window.location.href.indexOf('admin.html') === -1) {
        //   // 前台切换判断
        //   if (item && item.Val && item.Val.includes('/saasWorkbench')) {
        //     nextStep(item, newTenantId, res);
        //     return;
        //   }
        // }
        
        // const urlStr = window.location.href.indexOf('admin.html') !== -1 
        //   ? '/ump/adminMenu/adminMenuTree' 
        //   : '/ump/menu/userMenuTree';
        
        window.sessionStorage.setItem('UniWimAuthorization', res.token);
        utils.setLocalStorageInfo(res.token, res.expire || '', res.refreshToken, res.tenantId);
        // 模拟nextTick
        setTimeout(() => {
          location.reload();
        }, 0);
        // request.get(urlStr, {}).then((result: any) => {
        //   window.sessionStorage.removeItem('UniWimAuthorization');
        //   const menus = result && result.length ? utils.FormatAllMenu(result) : [];
        //   const isHas = item ? utils.containsElement(menus, item.Id) : false;
          
        //   if (isHas && item) {
        //     nextStep(item, newTenantId, res);
        //   } else {
        //     const fm = utils.GetFirstMenu(menus);
        //     sessionStorage.setItem('currentViewUniwim', JSON.stringify(fm));
        //     setVal(res);
        //   }
        // });
      });
  } else {
    // 已退出登录
    // window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
    loginStore.LOGOUT()
    userStore.clearUser();
  }
};

// 下一步处理
const nextStep = (item: MenuItem, tenantId: string | number, res: ChangeTenantResponse) => {
  if (item.Val && item.Val.indexOf('uniwim_tenant_id') !== -1) {
    item.Val = replaceUrlParam(item.Val, 'uniwim_tenant_id', tenantId);
  }
  if (item.ValBuffer && item.ValBuffer.indexOf('uniwim_tenant_id') !== -1) {
    item.ValBuffer = replaceUrlParam(item.ValBuffer, 'uniwim_tenant_id', tenantId);
  }
  if (item.link && item.link.indexOf('uniwim_tenant_id') !== -1) {
    item.link = replaceUrlParam(item.link, 'uniwim_tenant_id', tenantId);
  }
  
  sessionStorage.setItem('currentViewUniwim', JSON.stringify(item));
  setVal(res);
};

// 替换URL参数
const replaceUrlParam = (url: string, paramName: string, paramValue: string | number) => {
  const pattern = new RegExp(`([?&])${paramName}=[^&]*`);
  const replaced = url.replace(pattern, `$1${paramName}=${paramValue}`);

  if (replaced === url) {
    return url + (url.indexOf('?') === -1 ? '?' : '&') + `${paramName}=${paramValue}`;
  }

  return replaced;
};

// 组件挂载时获取租户列表
onMounted(() => {
  getTenantList();
});
</script>

<style lang="less" scoped>
.tenantList-page-box {
    .tenant-btn {
        display: flex;
        i {
            float: left;
            height: 36px;
            width: 18px;
            display: block;
            font-size: 18px;
            line-height: 36px;
            margin: 0px 10px 0 8px;
            overflow: visible;
        }
        span {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
<style lang="less">
.tenantList-popover {
    z-index: 9999999999 !important;
    &.el-popover.el-popper{
      // width:110px;
      padding:0;
    }
    .tenantList-content {
        background: #ffffff;
        border: 1px solid #cbd5dd;
        box-shadow: 0 0 10px 0 #cccccc66;
        border-radius: 2px;
        padding: 10px;
        box-sizing: border-box;
        .search-part {
        }

        .tenantList-box {
            margin: 6px 0;
            max-height: 600px;
            overflow: auto;
            .tenant-item {
                font-weight: 400;
                font-size: 12px;
                color: #222222;
                line-height: 36px;
                padding: 0 21px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                cursor: pointer;
                &.active {
                    background: #f0f1f3;
                }
                &:hover {
                    background: rgba(240, 241, 243, 0.5);
                }
            }
        }
    }
}
</style>
