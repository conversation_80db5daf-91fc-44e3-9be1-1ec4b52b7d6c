<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="5">
    <xs:annotation>
        <xs:documentation xml:lang="en">
            = Robot Framework output.xml schema =

            Compatible with Robot Framework 7.0 and newer. For more details and
            for older versions, go to
            https://github.com/robotframework/robotframework/tree/master/doc/schema

            Due to XSD 1.1 not being widely adopted, this schema is XSD 1.0 compatible.
            If you can use XSD 1.1, you can replace `xs:choice` groups with `xs:all`
            groups to make the schema more strict.
        </xs:documentation>
    </xs:annotation>
    <xs:element name="robot" type="Robot" />
    <xs:complexType name="Robot">
        <xs:sequence>
            <!-- Executed suite structure. -->
            <xs:element name="suite" type="Suite" />
            <!-- Execution statistics. Not read by Robot Framework itself. -->
            <xs:element name="statistics" type="Statistics" minOccurs="0" />
            <!-- Possible execution errors and warnings. -->
            <xs:element name="errors" type="Errors" minOccurs="0" />
        </xs:sequence>
        <xs:attribute name="generator" type="xs:string" />
        <xs:attribute name="generated" type="xs:dateTime" />
        <!-- True when executing tasks, false (default) when executing tests. -->
        <xs:attribute name="rpa" type="xs:boolean" />
        <!-- Version of the schema output.xml is compatible with. Must match
             the `version` attribute of the `xs:schema` root element. -->
        <xs:attribute name="schemaversion" type="SpecVersion" />
    </xs:complexType>
    <xs:simpleType name="SpecVersion">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="3" />
            <xs:maxInclusive value="5" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Suite">
        <xs:choice maxOccurs="unbounded">
            <!-- These keywords are possible suite setup and teardown.
                 They must have the `type` attribute set accordingly. -->
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="2" />
            <xs:element name="suite" type="Suite" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="test" type="Test" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="doc" type="xs:string" minOccurs="0" />
            <xs:element name="meta" type="Metadata" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string" />
        <xs:attribute name="source" type="xs:string" />
        <xs:attribute name="id" type="xs:string" />
    </xs:complexType>
    <xs:complexType name="Metadata">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" type="xs:string" use="required" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="Error">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="value" type="xs:string" minOccurs="1" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Test">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="doc" type="xs:string" minOccurs="0" />
            <xs:element name="tag" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="timeout" type="Timeout" minOccurs="0" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string" />
        <xs:attribute name="id" type="xs:string" />
        <xs:attribute name="line" type="xs:integer" />
    </xs:complexType>
    <!-- Keywords can be regular keywords or setup and teardown keywords.
         If the latter, they must have the `type` attribute set accordingly. -->
    <xs:complexType name="Keyword">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="var" type="xs:string" minOccurs="0" maxOccurs="unbounded" />  <!-- Assignment -->
            <xs:element name="arg" type="xs:string" minOccurs="0" maxOccurs="unbounded" />  <!-- Arguments -->
            <xs:element name="doc" type="xs:string" minOccurs="0" />
            <xs:element name="tag" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="timeout" type="Timeout" minOccurs="0" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string" />
        <xs:attribute name="owner" type="xs:string" />        <!-- Library or resource file name. -->
        <xs:attribute name="source_name" type="xs:string" />  <!-- Original name with embedded arguments. Not used otherwise. -->
        <xs:attribute name="type" type="KeywordType" />       <!-- Only used with setups and teardowns. -->
    </xs:complexType>
    <xs:simpleType name="KeywordType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="KEYWORD" />    <!-- Default when the attribute is not set. -->
            <xs:enumeration value="SETUP" />
            <xs:enumeration value="TEARDOWN" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="For">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="var" type="xs:string" minOccurs="0" maxOccurs="unbounded" />      <!-- Loop variables -->
            <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="unbounded" />    <!-- Loop values -->
            <xs:element name="iter" type="ForIteration" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="flavor" type="ForFlavor" />
        <xs:attribute name="start" type="xs:string" />    <!-- Used if IN ENUMERATE has `start`. -->
        <xs:attribute name="mode" type="xs:string" />     <!-- Used if IN ZIP has `mode`. -->
        <xs:attribute name="fill" type="xs:string" />     <!-- Used if IN ZIP has `fill`. -->
    </xs:complexType>
    <xs:simpleType name="ForFlavor">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IN" />
            <xs:enumeration value="IN RANGE" />
            <xs:enumeration value="IN ENUMERATE" />
            <xs:enumeration value="IN ZIP" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ForIteration">
        <xs:choice maxOccurs="unbounded">
            <!-- Loop variables with iteration specific values. -->
            <xs:element name="var" type="ForIterationVariable" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="break" type="Break" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="continue" type="Continue" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ForIterationVariable">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" type="xs:string" use="required" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="If">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="branch" type="IfBranch" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="IfBranch">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="break" type="Break" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="continue" type="Continue" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="type" type="IfType" use="required" />
        <xs:attribute name="condition" type="xs:string" />
    </xs:complexType>
    <xs:simpleType name="IfType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IF" />
            <xs:enumeration value="ELSE IF" />
            <xs:enumeration value="ELSE" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Try">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="branch" type="TryBranch" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TryBranch">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="pattern" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="break" type="Break" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="continue" type="Continue" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="type" type="TryType" use="required" />
        <xs:attribute name="pattern_type" type="xs:string" />
        <xs:attribute name="assign" type="xs:string" />
    </xs:complexType>
    <xs:simpleType name="TryType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TRY" />
            <xs:enumeration value="EXCEPT" />
            <xs:enumeration value="ELSE" />
            <xs:enumeration value="FINALLY" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="While">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="iter" type="WhileIteration" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="condition" type="xs:string" />
        <xs:attribute name="limit" type="xs:string" />
        <xs:attribute name="on_limit" type="xs:string" />
        <xs:attribute name="on_limit_message" type="xs:string" />
    </xs:complexType>
    <xs:complexType name="WhileIteration">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="break" type="Break" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="continue" type="Continue" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Group">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="for" type="For" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="if" type="If" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="try" type="Try" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="while" type="While" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="group" type="Group" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="variable" type="Var" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="return" type="Return" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="break" type="Break" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="continue" type="Continue" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="error" type="Error" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string" />
    </xs:complexType>
    <xs:complexType name="Var">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="var" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string" />
        <xs:attribute name="scope" type="xs:string" />
        <xs:attribute name="separator" type="xs:string" />
    </xs:complexType>
    <xs:complexType name="Return">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Break">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Continue">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="kw" type="Keyword" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="status" type="Status" />
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Message">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="time" type="xs:dateTime" use="required" />
                <xs:attribute name="level" type="MessageLevel" use="required" />
                <xs:attribute name="html" type="xs:boolean" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="MessageLevel">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TRACE" />
            <xs:enumeration value="DEBUG" />
            <xs:enumeration value="INFO" />
            <xs:enumeration value="WARN" />
            <xs:enumeration value="ERROR" />
            <xs:enumeration value="FAIL" />
            <xs:enumeration value="SKIP" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Status">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="status" type="StatusValue" use="required" />
                <xs:attribute name="start" type="xs:dateTime" />
                <xs:attribute name="elapsed" type="xs:float" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="StatusValue">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PASS" />
            <xs:enumeration value="FAIL" />
            <xs:enumeration value="SKIP" />
            <xs:enumeration value="NOT RUN" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Timeout">
        <xs:attribute name="value" type="xs:string" use="required" />
    </xs:complexType>
    <xs:complexType name="Statistics">
        <xs:all>
            <xs:element name="total" type="TotalStatistics" />
            <xs:element name="tag" type="TagStatistics" />
            <xs:element name="suite" type="SuiteStatistics" />
        </xs:all>
    </xs:complexType>
    <xs:complexType name="TotalStatistics">
        <xs:sequence>
            <xs:element name="stat" type="TotalStat" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TagStatistics">
        <xs:sequence>
            <xs:element name="stat" type="TagStat" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SuiteStatistics">
        <xs:sequence>
            <xs:element name="stat" type="SuiteStat" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TotalStat">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="pass" type="xs:integer" />
                <xs:attribute name="fail" type="xs:integer" />
                <xs:attribute name="skip" type="xs:integer" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="TagStat">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="pass" type="xs:integer" />
                <xs:attribute name="fail" type="xs:integer" />
                <xs:attribute name="skip" type="xs:integer" />
                <xs:attribute name="doc" type="xs:string" />
                <xs:attribute name="links" type="xs:string" />
                <xs:attribute name="info" type="xs:string" />
                <xs:attribute name="combined" type="xs:string" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="SuiteStat">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="pass" type="xs:integer" />
                <xs:attribute name="fail" type="xs:integer" />
                <xs:attribute name="skip" type="xs:integer" />
                <xs:attribute name="id" type="xs:string" />
                <xs:attribute name="name" type="xs:string" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="Errors">
        <xs:sequence>
            <xs:element name="msg" type="Message" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>
</xs:schema>
