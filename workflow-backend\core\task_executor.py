"""
任务执行器 - 负责执行Robot Framework代码（适用于PyInstaller打包）
"""

import asyncio
import os
import shutil

from asyncio import Future
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import threading


from robot import run
import psutil

from robot.libraries.BuiltIn import BuiltIn
from robot.api import get_model, TestSuite
from robot.reporting import ResultWriter
from robot.result.executionresult import Result
from robot.api.deco import keyword
from core import logger

# 本地模块导入
from models.workflow import (
    ExecutionResult,
    ExecutionStatus,
    ExecutionLog,
    LogLevel,
    create_execution_id,
    ExecuteContext,
    ExecuteParam,
)
from .action_node import actions, ActionResult, ActionNode

from .execution_monitor import ExecutionMonitor, cache

from utils.wimtask_server_api import save_history, send_msg, upload_task_state
from .variables import VariableManager


class TaskExecutor:
    running_tasks: Dict[str, Future]
    output_dir: Path
    executor: ThreadPoolExecutor
    variables: VariableManager
    executions: Dict[str, ExecuteContext]

    def __init__(self, variables: VariableManager):
        self.running_tasks: Dict[str, Future] = {}
        self.output_dir = Path("outputs")
        self.output_dir.mkdir(exist_ok=True)
        self.executor = ThreadPoolExecutor(max_workers=5)  # 控制最大并发数
        self.listeners = {}  # 存储监听器实例，便于后续访问输出等信息
        self.variables = variables

        logger.info("任务执行器初始化完成")

    @keyword
    def action_node(self, execute_id: str, typ: str):
        ctx = self.executions.get(execute_id)
        action = actions.get(typ, None)
        self.variables.get_workflows(execute_id)
        if action is None:
            return ActionResult(error=[f"没有定义的动作类型[{typ}]"])
            # 可以实现retry

        r = action.run(ctx, {}, {})
        # 可以实现日志，node运行状态等
        return r

    async def execute_async(self, execute_param: ExecuteParam) -> str:
        """异步执行Robot Framework代码"""
        execution_id = create_execution_id()

        ctx = ExecuteContext(
            execution_id, execute_param.task_id, execute_param.robot_code
        )

        future: Future = self.executor.submit(
            self._execute_robot_code, execution_id, execute_param
        )
        self.running_tasks[execution_id] = future

        return execution_id

    async def _execute_robot_code(self, execution_id: str, execute_param: ExecuteParam):
        """执行Robot Framework代码的内部方法"""

        robot_code = execute_param.robot_code

        execution_result = self.executions.get(execution_id, {})

        try:
            execution_result.status = ExecutionStatus.RUNNING
            upload_task_state(execute_param.token, execute_param.task_id, 1)
            self._add_log(
                execution_result, LogLevel.INFO, "开始执行Robot Framework代码"
            )
            # 执行时间
            exc_param = {
                "exc_time": int(datetime.now().timestamp()),
                "history_id": execution_id,
                "task_id": execute_param.task_id,
                "token": execute_param.token,
            }

            # 同时保存到输出目录用于调试
            execution_output_dir = self.output_dir / execution_id
            execution_output_dir.mkdir(exist_ok=True)
            debug_robot_path = execution_output_dir / "workflow.robot"
            debug_robot_path.write_text(execute_param.robot_code, encoding="utf-8")
            # 获取当前项目路径
            project_path = str(Path(__file__).parent.parent)
            # 创建监听器
            monitor = ExecutionMonitor(
                token=execute_param.token,
                task_id=execute_param.task_id,
                history_id=execution_id,
                node_num=execute_param.node_num,
            )
            self.listeners[execution_id] = monitor

            options = execute_param.options

            output_dir = Path(options.get("outputdir", str(execution_output_dir)))
            # 构建执行参数
            run_kwargs = {
                "outputdir": output_dir,
                "loglevel": options.get("log_level", "INFO"),
                "variable": options.get("variables", {}),
                "include": options.get("include_tags", []),
                "exclude": options.get("exclude_tags", []),
                "dryrun": options.get("dry_run", False),
                # "output":'output_e.xml',
                # "log": "log_2.html" ,
                # "report": 'report_2.html',
                "listener": [monitor],
            }

            # 模拟PID
            pid = f"{os.getpid()}-{threading.get_ident()}"
            execution_result.pid = pid
            self._add_log(execution_result, LogLevel.INFO, f"启动执行器线程，PID={pid}")

            result = self._run_robot_code(robot_code, run_kwargs)

            # 配置要生成的报告
            writer_config = {
                "report": output_dir / "report.html",
                "log": output_dir / "log.html",
            }
            # 生成报告
            status = ResultWriter(result).write_results(**writer_config)

            # 设置执行状态
            execution_result.end_time = datetime.now()
            execution_result.duration = (
                execution_result.end_time - execution_result.start_time
            ).total_seconds()

            if status == 0:
                execution_result.status = ExecutionStatus.SUCCESS
                upload_task_state(execute_param.token, execute_param.task_id, 3)
                # 单节点执行记录返回结果
                execution_result.return_obj = cache.get_item(execute_param.task_id)
                self._add_log(execution_result, LogLevel.INFO, "执行成功完成")
            else:
                execution_result.status = ExecutionStatus.FAILED
                upload_task_state(execute_param.token, execute_param.task_id, -1)

                self._add_log(
                    execution_result, LogLevel.ERROR, f"执行失败，返回码: {result}"
                )
            exc_param["status"] = status

        except asyncio.CancelledError:
            execution_result.status = ExecutionStatus.CANCELLED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.WARN, "执行被取消")

            upload_task_state(execute_param.token, execute_param.task_id, 3)

        except Exception as e:
            execution_result.status = ExecutionStatus.FAILED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.ERROR, f"执行异常: {str(e)}")
            logger.exception(f"执行 {execution_id} 时发生异常")

            upload_task_state(execute_param.token, execute_param.task_id, -1)

        finally:
            # 清理浏览器资源
            try:
                BuiltIn().run_keyword("Close All Browsers")
            except Exception as e:
                logger.warning(f"关闭浏览器失败: {str(e)}")

            # # 杀死子进程
            # self.kill_child_processes(os.getpid())
            # 清理运行任务记录
            if execution_id in self.running_tasks:
                del self.running_tasks[execution_id]
            # 记录执行状态
            save_history(exc_param)

            send_msg(exc_param, execute_param.task_id, monitor)
            # 清除变量缓存
            cache.del_item(execute_param.task_id)
            # 清理监听器
            if execution_id in self.listeners:
                del self.listeners[execution_id]

    def _run_robot(self, robot_file: str, run_kwargs: dict):
        """实际运行 Robot Framework 的方法"""
        return run(robot_file, **run_kwargs)

    def _run_robot_code(self, robot_code: str, run_kwargs: dict) -> Result:
        model = get_model(robot_code)
        suite = TestSuite.from_model(model)
        result = suite.run(**run_kwargs)
        return result

    async def get_execution_status(self, execution_id: str) -> ExecutionResult:
        """获取执行状态"""
        if execution_id not in self.executions:
            raise ValueError(f"执行记录不存在: {execution_id}")
        return self.executions[execution_id]

    async def stop_execution(self, execution_id: str) -> bool:
        """停止执行"""
        if execution_id not in self.running_tasks:
            return False

        future = self.running_tasks[execution_id]
        future.cancel()

        try:
            await future
        except asyncio.CancelledError:
            pass

        # 更新执行状态
        if execution_id in self.executions:
            execution_result = self.executions[execution_id]
            execution_result.status = ExecutionStatus.CANCELLED
            execution_result.end_time = datetime.now()
            self._add_log(execution_result, LogLevel.WARN, "执行被用户停止")

        # 移除任务记录
        del self.running_tasks[execution_id]

        logger.info(f"执行 {execution_id} 已停止")
        return True

    async def list_executions(self) -> List[ExecutionResult]:
        """列出所有执行记录"""
        return list(self.executions.values())

    async def cleanup(self):
        """清理资源"""
        logger.info("正在清理任务执行器资源...")

        # 停止所有正在运行的进程
        for execution_id in list(self.running_tasks.keys()):
            await self.stop_execution(execution_id)

        logger.info("任务执行器资源清理完成")

    def _add_log(
        self,
        execution_result: ExecutionResult,
        level: LogLevel,
        message: str,
        source: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        """添加执行日志"""
        log_entry = ExecutionLog(
            timestamp=datetime.now(),
            level=level,
            message=message,
            source=source,
            details=details,
        )
        execution_result.logs.append(log_entry)

    def get_execution_output_dir(self, execution_id: str) -> Optional[Path]:
        """获取执行输出目录"""
        output_dir = self.output_dir / execution_id
        return output_dir if output_dir.exists() else None

    async def _copy_output_files(self, execution_id: str, source_dir: Path):
        """复制输出文件到永久目录"""
        execution_output_dir = self.output_dir / execution_id
        execution_output_dir.mkdir(exist_ok=True)

        try:
            # 复制所有输出文件
            for file_path in source_dir.iterdir():
                if file_path.is_file():
                    dest_path = execution_output_dir / file_path.name
                    shutil.copy2(file_path, dest_path)

                    # 记录输出文件
                    execution_result = self.executions[execution_id]
                    execution_result.output_files.append(str(dest_path))

            logger.info(f"输出文件已复制到: {execution_output_dir}")

        except Exception as e:
            logger.error(f"复制输出文件失败: {e}")

    def cleanup_old_executions(self, days: int = 7):
        """清理旧的执行记录"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)

        to_remove = []
        for execution_id, execution_result in self.executions.items():
            if execution_result.start_time.timestamp() < cutoff_time:
                to_remove.append(execution_id)

        for execution_id in to_remove:
            # 删除执行记录
            del self.executions[execution_id]

            # 删除输出文件
            output_dir = self.output_dir / execution_id
            if output_dir.exists():
                shutil.rmtree(output_dir)

        logger.info(f"清理了 {len(to_remove)} 个旧的执行记录")

    def kill_child_processes(self, pid):
        try:
            parent = psutil.Process(pid)
            children = parent.children(recursive=True)
            for child in children:
                try:
                    child.terminate()
                    child.wait(timeout=1.0)
                except psutil.NoSuchProcess:
                    pass
        except psutil.NoSuchProcess:
            pass
