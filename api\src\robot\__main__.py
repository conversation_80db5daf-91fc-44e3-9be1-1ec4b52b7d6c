#!/usr/bin/env python

#  Copyright 2008-2015 Nokia Networks
#  Copyright 2016-     Robot Framework Foundation
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

import sys
from listener import execution_monitor

if __name__ == "__main__" and "robot" not in sys.modules:
    from pythonpathsetter import set_pythonpath

    set_pythonpath()

from robot import run_cli
from robot import run

monitor =  execution_monitor.ExecutionMonitor("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTAyNDQwNTQsImlkIjoiNjMxNmM2ZWM1NmE3YjMxNmUwNTZmYWUwIiwiand0SWQiOiI4YmE3NmFmMzM5N2Q0ZDczOWMyYmU1YmNkZDJkNWYzMyIsInVpZCI6IjYzMTZjNmVjNTZhN2IzMTZlMDU2ZmFlMCIsInRlbmFudElkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwiY2lkIjoiNWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjIiwibWFpbklkIjoiMTY1MyIsImF2YXRhciI6Imh0dHBzOi8vaGRrai5kbG1lYXN1cmUuY29tL3VuaXdpbS91cGxvYWRzLzIwMjQvMS90aHVtYm5haWw2ZmVmZmZmZDAxZTY0MTNlYmY0OTM4YWFjM2Q4NWFiNi5qcGciLCJuYW1lIjoi6Z-p6IiS5piOIiwiYWNjb3VudCI6Ijg3OCIsIm1vYmlsZSI6IjE4NTcxNDU3NjMxIiwic24iOiI4NzgiLCJncm91cCI6IjYyNzRiM2MyNTZhN2IzMzhjNDNmYjMxNSIsImdyb3VwTmFtZSI6IjAxLuWfuuehgOW5s-WPsOS6p-e6vyIsInlobG9OdW0iOiI0Mjg0MCIsImlzQWRtaW4iOmZhbHNlLCJjaGFubmVsIjoiZGVzayIsInJvbGVzIjpbIjE4MjY3ODgwMjkxNTM0NTYxMjkiLCI2NDc5YWRiOTU2YTdiMzNkYmNjZTYxMGMiLCIxNzc1NDMzODkyMDY3NjU1NjgyIiwiMTc0OTYwMDE2NDM1OTc1NzgyNSIsIjE4MjQwMTQ0Nzk2OTA2MDA0NDkiLCIxOTE2NzkwNzU5NjY1NzU0MTEzIl0sImNvbXBhbnkiOnsiaWQiOiI2Mjc0YjNjMjU2YTdiMzM4YzQzZmIzMTUiLCJuYW1lIjoiMDEu5Z-656GA5bmz5Y-w5Lqn57q_IiwiY29kZSI6IjY0NjU5OTY5MiJ9LCJ0b2tlbmZyb20iOiJ1bml3aW0iLCJ1c2VyVHlwZSI6InVzZXIiLCJleHAiOjE3ODE3ODAzNTR9.ZIbFblcf4--Yv4qvdD8I-rKQOmLr3N7Ym9PyjtHh-tw","1",'2')
robot_args = [
    '--listener', monitor,   # 注意这里传递的是监听器对象
    r'C:\Users\<USER>\Desktop\workflow.robot'   # 测试目录或文件
]

run(r'C:\Users\<USER>\Desktop\workflow.robot',listener=monitor)
