"""
进程管理工具 - 处理端口冲突和进程隔离
"""

import socket
import psutil
import os
from typing import List, Optional
from loguru import logger


class ProcessManager:
    """进程管理器，处理端口冲突和进程隔离"""

    @staticmethod
    def check_port(port: int = 39876) -> int:
        """check端口是否可用，如果找不到则终止程序"""
        if not ProcessManager.is_port_available(port):
            error_msg = f"端口不可用: {port}"
            logger.info(error_msg)
            raise RuntimeError(error_msg)

        return port

    @staticmethod
    def find_available_port(start_port: int = 39876, max_attempts: int = 100) -> int:
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            if ProcessManager.is_port_available(port):
                logger.info(f"找到可用端口: {port}")
                return port

        # 如果找不到可用端口，使用随机端口
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.bind(("127.0.0.1", 0))
            port = sock.getsockname()[1]
            logger.info(f"使用随机端口: {port}")
            return port

    @staticmethod
    def is_port_available(port: int, host: str = "127.0.0.1") -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception:
            return False

    @staticmethod
    def get_processes_using_port(port: int) -> List[dict]:
        """获取占用指定端口的进程信息"""
        processes = []
        try:
            for proc in psutil.process_iter(["pid", "name", "cmdline"]):
                try:
                    for conn in proc.connections():
                        if conn.laddr.port == port:
                            processes.append(
                                {
                                    "pid": proc.info["pid"],
                                    "name": proc.info["name"],
                                    "cmdline": (
                                        " ".join(proc.info["cmdline"])
                                        if proc.info["cmdline"]
                                        else ""
                                    ),
                                }
                            )
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"获取端口占用进程失败: {e}")

        return processes

    @staticmethod
    def kill_processes_using_port(port: int) -> bool:
        """杀死占用指定端口的进程"""
        try:
            processes = ProcessManager.get_processes_using_port(port)
            for proc_info in processes:
                try:
                    proc = psutil.Process(proc_info["pid"])
                    proc.terminate()
                    logger.info(f"终止进程 {proc_info['pid']} ({proc_info['name']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.warning(f"无法终止进程 {proc_info['pid']}: {e}")
            return True
        except Exception as e:
            logger.error(f"杀死端口占用进程失败: {e}")
            return False

    @staticmethod
    def setup_subprocess_environment() -> dict:
        """设置子进程环境变量，避免端口冲突"""
        env = os.environ.copy()

        # 标记为子进程
        env["PHOENIX_SUBPROCESS"] = "true"
        env["PHOENIX_DISABLE_SERVER"] = "true"

        # 禁用可能导致端口冲突的服务
        env["ROBOT_SYSLOG_FILE"] = "NONE"
        env["ROBOT_SYSLOG_LEVEL"] = "NONE"

        # 设置浏览器路径避免下载冲突
        from pathlib import Path

        workflow_backend_root = Path(
            __file__
        ).parent.parent  # 指向 workflow-backend 目录
        env["PLAYWRIGHT_BROWSERS_PATH"] = str(
            workflow_backend_root / "plugins" / "browsers"
        )

        # 禁用一些可能启动服务的环境变量
        env["ROBOT_LISTENER"] = ""
        env["ROBOT_LIBRARY_SEARCH_ORDER"] = ""

        return env

    @staticmethod
    def is_subprocess() -> bool:
        """检查当前是否在子进程中运行"""
        return os.environ.get("PHOENIX_SUBPROCESS") == "true"

    @staticmethod
    def cleanup_zombie_processes():
        """清理僵尸进程"""
        try:
            current_pid = os.getpid()
            for proc in psutil.process_iter(["pid", "name", "cmdline", "status"]):
                try:
                    # 跳过当前进程
                    if proc.info["pid"] == current_pid:
                        continue

                    # 查找相关的僵尸进程
                    if proc.info["status"] == psutil.STATUS_ZOMBIE or (
                        proc.info["cmdline"]
                        and any(
                            "phoenix" in cmd.lower() for cmd in proc.info["cmdline"]
                        )
                    ):

                        logger.info(f"发现可能的僵尸进程: {proc.info}")
                        proc.terminate()

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"清理僵尸进程失败: {e}")
