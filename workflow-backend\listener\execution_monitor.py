import json
import logging
import os
from time import sleep

import requests
import time
import ast
import jwt
from datetime import datetime
from requests import Response
from robot.api.deco import keyword
import threading
from robot.libraries.BuiltIn import BuiltIn
from robot.running.context import EXECUTION_CONTEXTS

from config.env_config import YN_URL, get_config_item, DLY_URL,TaskMonitor_Url

from config import globals

# 本地模块导入
from models.workflow import (
    create_execution_id,
)


lock = threading.Lock()


class Cache:
    # 存储数据
    DATA_DICT = dict()
    # 存储时间
    TIME = []
    # 存储key
    DATA_KEY = []

    def __init__(self, maxsize, ttl):
        self.maxsize = maxsize
        self.ttl = ttl

    def put_item(self, key: str, value):
        expire = int(datetime.now().timestamp()) + self.ttl

        # 如果存在，就更新数据
        if key in self.DATA_DICT:
            idx = self.DATA_KEY.index(key)
            del self.TIME[idx]
            del self.DATA_KEY[idx]

        self.TIME.append(expire)
        self.DATA_KEY.append(key)

        self.DATA_DICT[key] = value

    def get_item(self, key: str):
        return self.DATA_DICT.get(key)

    def del_item(self, key):
        try:
            # 删除时间
            idx = self.DATA_KEY.index(key)
            del self.TIME[idx]
            self.DATA_KEY.remove(key)

            del self.DATA_DICT[key]
            if f"{key}_log" in self.DATA_DICT:
                del self.DATA_DICT[f"{key}_log"]
        except Exception as e:
            logging.info(f"删除错误: 索引 {key}，原因：{e}")

    def clear_cache(self, name):

        while True:
            with lock:
                now = int(datetime.now().timestamp())

                max_idx = -1
                keys = []
                for idx in range(len(self.TIME)):
                    if now > self.TIME[idx]:
                        max_idx = idx
                        keys.append(self.DATA_KEY[idx])
                    else:
                        break

                if max_idx >= 0:
                    for i in range(max_idx + 1):
                        idx = i - 1
                        try:
                            del self.TIME[idx]
                            del self.DATA_KEY[idx]

                            key = keys[idx]
                            if key:
                                del self.DATA_DICT[key]
                        finally:
                            pass

            sleep(600)


# 创建缓存：最大200个条目，每个条目最多存活600秒
cache = Cache(maxsize=200, ttl=600)


# 定时清理过期的缓存
def thread_task():
    cache.clear_cache("")


threading.Thread(target=thread_task).start()


def get_output(work_id, node_id):
    key = f"{node_id}_output"
    if key in cache.get_item(work_id).keys():
        return cache.get_item(work_id)[f"{node_id}_output"]
    return None


def get_input(work_id, node_id):
    key = f"{node_id}_input"
    if key in cache.get_item(work_id):
        return cache.get_item(work_id)[key]
    return None


def upload_file(history_id, node_id, file_path, token):
    # 如果文件存在，通过multipart/form-data，上传到后端服务器 url: http://saasbate.dlmeasure.com:1801/wimai/api/task/excFile/upload
    if os.path.exists(file_path):
        file_name = os.path.basename(file_path)
        url = f"{get_config_item(YN_URL)}/wimai/api/task/excFile/upload"
        # 根据文件扩展名设置正确的MIME类型
        if file_path.lower().endswith(".xlsx"):
            mime_type = (
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        elif file_path.lower().endswith(".docx"):
            mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        else:
            mime_type = "application/octet-stream"  # 通用二进制流
        headers = {"Authorization": token}
        try:
            # 关键修改1：预先读取文件内容到内存
            with open(file_path, "rb") as f:
                file_content = f.read()  # 先将文件内容完整读取到内存

            # 关键修改2：使用BytesIO创建文件对象
            from io import BytesIO

            file_obj = BytesIO(file_content)

            # 关键修改3：确保文件对象不会被提前关闭
            files = {"file": (file_name, file_obj, mime_type)}

            # 添加调试信息
            logging.info(f"准备上传文件: {file_name} ({len(file_content)} bytes)")

            # 关键修改4：使用data参数传递其他字段
            data = {"executionId": history_id, "nodeId": node_id if node_id else ""}

            response = requests.post(url, headers=headers, files=files, data=data)

            # 确保关闭文件对象
            file_obj.close()

            if response.status_code == 200:
                logging.info("文件上传成功")
                return True
            else:
                logging.info(f"上传失败: 状态码 {response.status_code}")
                logging.info(f"错误信息: {response.text}")
                return False

        except Exception as e:
            logging.info(f"文件上传过程中发生错误: {str(e)}")
            return False


class ExecutionMonitor:
    ROBOT_LISTENER_API_VERSION = 3

    _callback = None

    def __init__(self, token, task_id, history_id, node_num, if_send_msg=True):
        self.step_history = []
        self.token = token
        self.logs: object = None
        self.history_id = history_id
        self.task_id = task_id
        self.stop_flag = False
        self.builtin = BuiltIn()
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": self.token,  # "Bearer empty" # 如果需要授权，tokens 从params 获取
        }
        self.node_num = node_num
        self.if_send_msg = if_send_msg

        resp = requests.get(
            f"{get_config_item(YN_URL)}/wimai/api/task/detail?id={self.task_id}",
            headers=self.headers,
        )
        if resp.status_code != 200:
            logging.info(f">>> 获取 任务执行器失败: ,respons|e body{resp.text} <<<")
            return

        self.task = json.loads(resp.text)["Response"]
        self.userIds = []
        if self.task:
            # self.userIds.append(self.task["creatorId"])

            if self.task["reportUser"]:
                report_users = self.task["reportUser"].split(",")
                if report_users:
                    self.userIds.extend(report_users)

            if not self.node_num:
                self.node_num = self.task["nodeNum"]

        self.keyword_locals = {}


    def set_stop_flag(self, value=True):
        self.stop_flag = value
        self.builtin.set_global_variable('${stop_flag}', str(value))

    def start_keyword(self, context, attributes):
        """在关键字执行前触发"""
        name = context.name
        keyword_name = name.name if hasattr(name, "name") else str(name)
        if keyword_name.startswith("Execute Step") and attributes.status != "NOT RUN":

            step_name = (
                keyword_name.split(" - ", 1)[1]
                if " - " in keyword_name
                else keyword_name
            )
            time = self.convert_time_str(date_str=str(attributes.starttime))
            status = "running"
            end = 0
            hideChat = 0
            if "workflow_end" in attributes.tags:
                status = "passed"
                end = 1

            if (
                "workflow_end" not in attributes.tags
                and "workflow_start" not in attributes.tags
            ):
                hideChat = 1

            step = {
                "title": step_name,
                "name": step_name,
                "start_time": time,
                "time": time,
                "end": end,
                "content": attributes.doc,
                "status": status,
                "hideChat": hideChat,
            }
            self.step_history.append(step)
            ctx = EXECUTION_CONTEXTS.current
            if ctx:
                # 获取当前作用域（即该关键字的作用域）的局部变量
                current_scope = ctx.variables.current
                # 将变量字典存储，按关键字名称记录
                # self.keyword_locals[attributes.kwname] = current_scope.as_dict()
                # 记录日志

            if True or "workflow_start" in attributes.tags:

                params = self.get_exc_start(
                    "PASS",
                    step_name,
                    self.convert_time_long(attributes.starttime),
                    self.convert_time_long(attributes.endtime),
                    name.split(" - ", 1)[0] if " - " in name else name,
                    attributes.tags[0],
                    attributes.doc,
                )
                params["state"] = "progress"
                params["execution_id"] = self.history_id
                if self._callback:
                    logging.info(f"节点开始{step_name}  >>> {params}")
                    self.trigger_event(params)

            # 开始节点 发生消息通知
            # if 'workflow_start' in attributes.tags:
            if self.if_send_msg:
                # param = {
                #     "missionId": self.task_id,
                #     "missionExecId": self.history_id,
                #     "content": f"共{self.node_num}个节点",
                #     "title": self.task["missionName"] + "开始执行",
                #     "link":f'{get_config_item(TaskMonitor_Url)}?historyId={self.history_id}&missionId={self.task_id}',
                #     "userIds": self.userIds,
                #     "extend": self.step_history,
                # }
                # resp = requests.post(
                #     f"{get_config_item(DLY_URL)}/wimai/api/tool/msgSend",
                #     json=param,
                #     headers=self.headers,
                # )
                notifier_send(
                    self.task["missionName"] + "开始执行",
                    f"共{self.node_num}个节点",
                    self.userIds,
                    "sys",
                    f'{get_config_item(TaskMonitor_Url)}?historyId={self.history_id}&missionId={self.task_id}'
                )
            logging.info(f"\n>>> STEP STARTED: {step_name} <<<")

    def end_keyword(self, context, attributes):
        if self.stop_flag:
            BuiltIn().fatal_error("执行被中止")
        name = context.name
        ctx = EXECUTION_CONTEXTS.current
        if "Fail" in name:
            err = self.builtin.get_variable_value("${keyword_name}", "")
            pass
        """在关键字执行后触发"""
        if name.startswith("Execute Step") and attributes.status != "NOT RUN":
            step_name = name.split(" - ", 1)[1] if " - " in name else name
            duration = attributes.elapsedtime / 1000  # 转换为秒

            for step in self.step_history:
                if step["name"] == step_name and step["status"] == "running":
                    time = self.convert_time_str(date_str=str(attributes.endtime))
                    output = get_output(
                        self.task_id, name.split(" - ", 1)[0] if " - " in name else name
                    )
                    step.update(
                        {
                            "title": step_name,
                            "end_time": time,
                            "time": time,
                            "duration": duration,
                            "status": (
                                "passed" if attributes.status == "PASS" else "failed"
                            ),
                            "output": output,
                            "content": attributes.doc,
                        }
                    )
                # 记录日志

            params = self.save_exc_log(
                attributes.status,
                step_name,
                self.convert_time_long(attributes.starttime),
                self.convert_time_long(attributes.endtime),
                name.split(" - ", 1)[0] if " - " in name else name,
                attributes.tags[0],
                attributes.doc,
            )
            params["execution_id"] = self.history_id
            if True or "workflow_start" not in attributes.tags:
                if self._callback:
                    logging.info(f"节点结束{step_name} {duration:.2f}s) >>> {params}")
                    self.trigger_event(params)

            status_icon = "✅" if attributes.status == "PASS" else "❌"

            logging.info(
                f"\n<<< STEP FINISHED: {step_name} {status_icon} "
                f"({duration:.2f}s) >>>"
            )

    def end_test(self, data, result):
        if result.status == "FAIL":
            cache.put_item(f"{self.task_id}_log", result.message)

    @property
    def callback(self):
        return self._callback

    @callback.setter
    def callback(self, value):
        if not callable(value) and value is not None:
            raise TypeError("回调必须是可调用的！")
        self._callback = value

    def trigger_event(self, data):
        if self._callback:
            # 创建生成器对象
            self._callback(data)

    def get_step_history(self):
        """获取步骤执行历史"""
        return self.step_history

    def convert_time_long(self, date_str: str) -> str:
        try:
            # 解析输入字符串
            dt = datetime.strptime(date_str, "%Y%m%d %H:%M:%S.%f")
            # 格式化为目标格式
            return dt.timestamp()
        except ValueError as e:
            raise ValueError("Invalid date format or value") from e

    def convert_time_str(self, date_str: str) -> str:
        try:
            # 解析输入字符串

            dt = datetime.strptime(date_str, "%Y%m%d %H:%M:%S.%f")
            # 格式化为目标格式
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError as e:
            raise ValueError("Invalid date format or value") from e

    def save_exc_log(self, status, name, sdt, edt, node_id, tag, doc):

        output_data = get_output(self.task_id, node_id)
        output = json.dumps(output_data, ensure_ascii=False)
        input = json.dumps(get_input(self.task_id, node_id), ensure_ascii=False)
        exc_boyd = {
            "missionId": self.task_id,
            "historyId": self.history_id,
            "nodeId": node_id.replace("Execute Step ", ""),
            "describe": doc,
            "title": name,
            "state": "passed" if status == "PASS" else "failed",
            "output": output,
            "input": input,
            "excTime": int(sdt),
            "finishTime": int(edt),
            "nodeType": tag,
        }

        resp = requests.post(
            f"{get_config_item(YN_URL)}/wimai/api/task/excLogs/save",
            json=exc_boyd,
            headers=self.headers,
        )
        logging.info(json.dumps(exc_boyd, ensure_ascii=False))
        if resp.status_code != 200:
            logging.info(f">>> EXC LOG FAILED: {name},response body{resp.text} <<<")
        # todo 如果是执行的excel、word写入的节点，输出参数里有输出目录（file_path/file_name），读取文件传给后端
        if tag == "excel_create" or tag == "word_create":
            if output_data and "file_path" in output_data:
                file_path = output_data["file_path"]
                file_name = output_data["file_name"]
                file_path = file_path + "/" + file_name
                file_path = file_path.replace("\\", "/")
                file_path = file_path.replace("//", "/")
                file_path = file_path.replace("\\\\", "/")
                # excel后缀.xlsx  word后缀 .docx
                if not file_path.endswith(".xlsx") and not file_path.endswith(".docx"):
                    if "excel" in tag:
                        file_path = file_path + ".xlsx"
                    elif "word" in tag:
                        file_path = file_path + ".docx"
                upload_file(self.history_id, "", file_path, self.token)

        return exc_boyd

    def get_exc_start(self, status, name, sdt, edt, node_id, tag, doc):
        exc_boyd = {
            "missionId": self.task_id,
            "historyId": self.history_id,
            "nodeId": node_id.replace("Execute Step ", ""),
            "describe": doc,
            "title": name,
            "state": "passed" if status == "PASS" else "failed",
            "output": {},
            "input": {},
            "excTime": int(sdt),
            "finishTime": int(edt),
            "nodeType": tag,
        }
        return exc_boyd

    def __exit__(self):
        del cache[self.task_id]


class CustomiseLibrary:
    def __init__(self):
        pass

    @keyword
    def record_input(self, work_id, node_id, val):
        new_dict = self.create_new_dic(val)
        work_param = cache.get_item(work_id)

        if not work_param:
            work_param = {}
        work_param[f"{node_id}_input"] = new_dict
        cache.put_item(work_id, work_param)

    @keyword
    def record_output(self, work_id: str, node_id: str, val: dict):
        new_dict = self.create_new_dic(val)

        work_param = cache.get_item(work_id)

        if not work_param:
            work_param = {}
        work_param[f"{node_id}_output"] = new_dict
        cache.put_item(work_id, work_param)

    def create_new_dic(self, val: dict) -> dict:
        new_dict = {}
        for k, v in val.items():
            if type(v) == Response:
                v = v.text
            elif hasattr(v, "__class__") and "Workbook" in str(type(v)):
                # 处理Workbook对象，只保存文件路径信息
                v = f"<Workbook object: {getattr(v, 'path', 'in-memory')}>"
            elif not isinstance(v, (str, int, float, bool, list, dict, type(None))):
                # 处理其他不可序列化的对象
                v = str(v)
            k = k.replace('"', "")
            new_dict[k] = v
        return new_dict


def make_message_request(title, content, users, method, link=""):
    headers = {
        "Authorization": globals.token,
        "content-type": "application/json",
    }

    base_url = f"{get_config_item(DLY_URL)}/uniwater/event/push.json"
    data = make_message_data(title, content, users, method, link)

    return {"url": base_url, "headers": headers, "data": data}

def make_message_data(title, content, users, method, link=""):
    timestamp = time.time()
    data = {
        "sourceid": "WimAI_" + create_execution_id(),
        "type": "WimAI_Task",
        "push": method,
        "title": title,
        "content": content,
        "begin": int(timestamp),
        "popupType": "pop",
        "users": users,
        "link": link
    }
    return data

def notifier_send(title, content, users, method, link):
    method = method
    request = make_message_request(title, content, users, method, link)
    response = requests.post(
        url=request["url"], headers=request["headers"], json=request["data"]
    )
    if response.status_code == 200:
        logging.info(f"发送消息成功")
        return "ok"
    else:
        logging.info(f"发送消息失败{response.status_code}")
        return response.status_code