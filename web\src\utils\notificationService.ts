/**
 * 通知服务
 * 处理工作流完成后的桌面通知
 */

import { ElNotification } from 'element-plus'

export interface NotificationOptions {
  title: string
  message?: string
  type: 'success' | 'info' | 'warning' | 'error'
  duration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  showClose?: boolean
  onClick?: () => void
}

export class NotificationService {
  /**
   * 显示工作流完成通知
   */
  static showWorkflowCompletion(options: NotificationOptions) {
    const {
      title,
      message = '',
      type = 'success',
      duration = 5000,
      position = 'bottom-right',
      showClose = true,
      onClick,
    } = options

    // 根据类型设置图标和样式
    const iconMap = {
      success: '✅',
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
    }

    const icon = iconMap[type]
    const displayTitle = `${icon} ${title}`

    return ElNotification({
      title: displayTitle,
      message: message || this.getDefaultMessage(type),
      type,
      duration: duration === 0 ? 0 : duration * 1000, // 转换为毫秒
      position,
      showClose,
      onClick,
      customClass: 'workflow-notification',
      dangerouslyUseHTMLString: false,
    })
  }

  /**
   * 显示成功通知
   */
  static showSuccess(title: string, message?: string, duration = 5) {
    return this.showWorkflowCompletion({
      title,
      message,
      type: 'success',
      duration,
    })
  }

  /**
   * 显示错误通知
   */
  static showError(title: string, message?: string, duration = 10) {
    return this.showWorkflowCompletion({
      title,
      message,
      type: 'error',
      duration,
    })
  }

  /**
   * 显示警告通知
   */
  static showWarning(title: string, message?: string, duration = 8) {
    return this.showWorkflowCompletion({
      title,
      message,
      type: 'warning',
      duration,
    })
  }

  /**
   * 显示信息通知
   */
  static showInfo(title: string, message?: string, duration = 5) {
    return this.showWorkflowCompletion({
      title,
      message,
      type: 'info',
      duration,
    })
  }

  /**
   * 根据工作流结束状态显示通知
   */
  static showWorkflowEndNotification(endConfig: any) {
    if (!endConfig.notify_completion) {
      return
    }

    const title = endConfig.notification_title || '工作流执行完成'
    const message = endConfig.notification_message || ''
    const type = endConfig.notification_type || 'success'
    const duration = endConfig.notification_duration || 5

    return this.showWorkflowCompletion({
      title,
      message,
      type,
      duration,
      onClick: () => {
        // 点击通知时的处理，可以跳转到日志或结果页面
        console.log('Notification clicked')
      },
    })
  }

  /**
   * 获取默认消息
   */
  private static getDefaultMessage(type: string): string {
    const messageMap = {
      success: '工作流执行完成',
      info: '工作流执行完成',
      warning: '工作流完成，但存在警告',
      error: '工作流执行失败',
    }
    return messageMap[type as keyof typeof messageMap] || '工作流执行完成'
  }

  /**
   * 显示执行统计通知
   */
  static showExecutionStats(stats: {
    totalNodes: number
    successNodes: number
    failedNodes: number
    executionTime: number
  }) {
    const { totalNodes, successNodes, failedNodes, executionTime } = stats
    const successRate = Math.round((successNodes / totalNodes) * 100)

    let type: 'success' | 'warning' | 'error' = 'success'
    let title = '工作流执行完成'

    if (failedNodes > 0) {
      type = failedNodes === totalNodes ? 'error' : 'warning'
      title = failedNodes === totalNodes ? '工作流执行失败' : '工作流部分完成'
    }

    const message = `
执行统计：
• 总节点数：${totalNodes}
• 成功节点：${successNodes}
• 失败节点：${failedNodes}
• 成功率：${successRate}%
• 执行时间：${executionTime.toFixed(2)}秒
    `.trim()

    return this.showWorkflowCompletion({
      title,
      message,
      type,
      duration: 10,
    })
  }

  /**
   * 显示定时任务通知
   */
  static showScheduledTaskNotification(taskName: string, nextRun?: Date) {
    const message = nextRun ? `下次执行时间：${nextRun.toLocaleString()}` : '定时任务已设置'

    return this.showInfo(`定时任务已创建：${taskName}`, message, 8)
  }

  /**
   * 显示重试通知
   */
  static showRetryNotification(attempt: number, maxRetries: number, error?: string) {
    const title = `重试执行 (${attempt}/${maxRetries})`
    const message = error ? `错误：${error}` : '正在重试执行失败的步骤'

    return this.showWarning(title, message, 5)
  }
}
