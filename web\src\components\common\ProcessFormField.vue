<template>
  <div class="variable-input-field">
    <el-form :model="allValues" label-position="top">
      <el-form-item
        v-for="(option, index) in formConfigs"
        :label="option.title"
        :key="option.data"
        :required="option.required"
        :class="['smart-config-field', `field-type-${option.showType}`]"
      >
        <!-- 支持变量的字符串/文本输入 -->
        <VariableInputField
          ref="VariableInputFieldRef"
          :model-value="modelValue[option.data]"
          @update:model-value="(v) => handleUpdate(v, option.data)"
          :all-values="allValues"
          :field-name="option.data"
          :field-config="{
            type: option.showType === 'textarea' ? 'textarea' : 'string',
            variableSupport: true
          }"
          :disabled="option.readonly"
          :type="option.showType === 'textarea' ? 'textarea' : 'string'"
          :language="fieldConfig.language"
        />
      </el-form-item>
      <el-empty v-if="!formConfigs.length" description="未获取到表单配置" :image-size="80"/>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue'
import VariablesField from '@/components/common/VariablesField.vue'
import VariableInputField from '@/components/common/VariableInputField.vue'

// Props
interface Props {
  modelValue?: string
  fieldConfig: any
  disabled?: boolean
  allValues: Record<string, any>
  fieldName: string
  componentType: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})
// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 定义表单配置
const formConfigs = ref<any[]>([])

// 获取表单配置
const GetFormConfig = (procCode: string) => {
  // 调用接口获取表单配置
  // 这里需要根据 procCode 获取对应的表单配置
  const procConfig = {
    process1: [
      {
        dftActivity: '',
        parent: null,
        data: 'field_1751955192670',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '单行文本',
        required: 0,
        keepSpaces: 0,
        defaultType: 'static',
        showCondition: null,
        readonly: 0,
        width: '50%',
        isUrl: 0,
        camundaLable: '单行文本',
        showType: 'text',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 0,
      },
      {
        dftActivity: '',
        parent: null,
        data: 'field_1751955113765',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'Number',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '单行数字',
        required: 0,
        keepSpaces: 0,
        defaultType: 'static',
        showCondition: null,
        readonly: 0,
        width: '50%',
        isUrl: 0,
        camundaLable: '单行数字',
        showType: 'text',
        onlyStyleHide: 0,
        camundaType: 'long',
        order: 1,
      },
      {
        parent: null,
        data: 'field_1751955116289',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        rows: 3,
        title: '多行文本',
        required: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '多行文本',
        showType: 'textarea',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 2,
      },
      {
        gridLayout: 0,
        parent: null,
        data: 'field_1751955117861',
        defaultValue: null,
        showMode: 'dropdown',
        labelLine: 0,
        linkage: null,
        title: '下拉选择框',
        required: 0,
        readonly: 0,
        camundaLable: '下拉选择框',
        showType: 'combo',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 3,
        onlyAppDetailHide: 0,
        dataType: 'String',
        gridLayoutVal: 6,
        format: null,
        multiple: 0,
        keepDom: 0,
        searchable: false,
        showCondition: null,
        width: '50%',
        expandTags: false,
      },
      {
        parent: null,
        data: 'field_1751955119433',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '多选框组',
        required: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '多选框组',
        showType: 'checkbox',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 4,
      },
      {
        parent: null,
        data: 'field_1751955123410',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        keepDom: 0,
        labelLine: 0,
        title: '开关',
        required: 0,
        showCondition: null,
        record_color: null,
        readonly: 0,
        width: '50%',
        camundaLable: '开关',
        showType: 'switch',
        onlyStyleHide: 0,
        camundaType: 'string',
        verification: null,
        order: 5,
      },
      {
        parent: null,
        data: 'field_1751955124302',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '颜色选择器',
        required: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '颜色选择器',
        showType: 'color',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 6,
      },
      {
        parent: null,
        data: 'field_1751955125937',
        onlyAppDetailHide: 0,
        max: 5,
        defaultValue: null,
        keepDom: 0,
        labelLine: 0,
        title: '评分',
        required: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '评分',
        showType: 'rate',
        onlyStyleHide: 0,
        allowHalf: 0,
        camundaType: 'string',
        order: 7,
      },
      {
        dftActivity: '',
        parent: null,
        data: 'field_1751955126782',
        onlyAppDetailHide: 0,
        max: {
          unit: 'days',
          offset: 0,
          offsetType: 'static',
          value: '',
        },
        defaultValue: null,
        dataType: 'String',
        format: 'yyyy-MM-dd',
        keepDom: 0,
        labelLine: 0,
        title: '日期选择器',
        required: 0,
        defaultType: 'static',
        showCondition: null,
        min: {
          unit: 'days',
          offset: 0,
          offsetType: 'static',
          value: '',
        },
        readonly: 0,
        width: '50%',
        camundaLable: '日期选择器',
        showType: 'datetime',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 8,
      },
      {
        isDownload: 0,
        template: null,
        parent: null,
        data: 'field_1751955130331',
        onlyAppDetailHide: 0,
        defaultValue: null,
        disAccept: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '文件',
        required: 0,
        accept: null,
        showCondition: null,
        readonly: 0,
        width: '50%',
        openChunk: 0,
        camundaLable: '文件',
        showType: 'upload',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 9,
      },
      {
        isDownload: 0,
        template: null,
        parent: null,
        isComment: 0,
        data: 'field_1751955131296',
        defaultValue: null,
        labelLine: 0,
        title: '图片',
        required: 0,
        useThumb: 0,
        readonly: 0,
        limit: 5,
        camundaLable: '图片',
        showType: 'img-upload',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 10,
        onlyAppDetailHide: 0,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        quality: 0.8,
        showCondition: null,
        width: '50%',
      },
      {
        parent: null,
        data: 'field_1751955132577',
        onlyAppDetailHide: 0,
        defaultValue: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '图片视频',
        required: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        limit: 5,
        camundaLable: '图片视频',
        showType: 'img-video',
        onlyStyleHide: 0,
        camundaType: 'string',
        fileType: 'all',
        order: 11,
      },
      {
        parent: null,
        onlyLeaf: 0,
        data: 'field_1751955138138',
        defaultValue: null,
        autoType: 2,
        labelLine: 0,
        title: '树形选择器',
        required: 0,
        readonly: 0,
        camundaLable: '树形选择器',
        showType: 'ztree',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 12,
        dftActivity: '',
        wholePath: 0,
        onlyAppDetailHide: 0,
        max: null,
        dataType: 'String',
        multiple: 0,
        autoFields: [],
        keepDom: 0,
        checkRules: '',
        readonlyCdn: '',
        defaultType: 'static',
        isScan: 0,
        showCondition: null,
        sourceType: 'format',
        width: '50%',
      },
      {
        map_options: null,
        parent: null,
        data: 'field_1751955139172',
        onlyAppDetailHide: 0,
        defaultValue: null,
        format: null,
        autoFields: [],
        needNavi: 0,
        keepDom: 0,
        labelLine: 0,
        title: '经纬度',
        isShot: 0,
        required: 0,
        currentLocation: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '经纬度',
        showType: 'position',
        deptArea: 0,
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 13,
      },
      {
        parent: null,
        magicPoc: 0,
        data: '_next_assignee',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '人员选择器',
        required: 1,
        expandAll: 0,
        showCondition: null,
        readonly: 0,
        width: '50%',
        camundaLable: '人员选择器',
        showType: 'next-assignee',
        onlyStyleHide: 0,
        text: '',
        camundaType: 'string',
        order: 14,
      },
    ],
    process2: [
      {
        dftActivity: '',
        parent: null,
        data: 'a0',
        onlyAppDetailHide: 0,
        defaultValue: null,
        dataType: 'String',
        format: null,
        multiple: 0,
        keepDom: 0,
        labelLine: 0,
        title: '单行文本0',
        required: 0,
        keepSpaces: 0,
        defaultType: 'static',
        showCondition: null,
        readonly: 0,
        width: '50%',
        isUrl: 0,
        camundaLable: '单行文本0',
        showType: 'text',
        onlyStyleHide: 0,
        camundaType: 'string',
        order: 0,
      },
    ],
    process3: [],
  }
  return procConfig[procCode] || []
}

// 监听allValues.procCode 变化，获取最新的流程的表单配置
watch(
  () => props.allValues.procCode,
  (newVal) => {
    formConfigs.value = GetFormConfig(newVal)
  },
  {
    immediate: true,
  },
)

const handleUpdate = (newValue: any, field: string) => {
  props.modelValue[field] = newValue
}
</script>

<style scoped>
.variable-input-field {
  width: 100%;
}
</style>
