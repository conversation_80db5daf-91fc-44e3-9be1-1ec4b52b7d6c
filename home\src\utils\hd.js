var LocalStorage = {
    Get: function (Key, Default) {
        if (Key.length > 0 && window["localStorage"]) {
            var Result = Proxy.Json.ToObject(window["localStorage"].getItem(Key));
            if (Result && Result.Expiress > (new Date().getTime())) {
                return Result.Data;
            }
            else if (jQuery.isFunction(Default)) {
                return Default();
            }
            else {
                return Default;
            }
        }
        else {
            return null;
        }
    },
    Set: function (Key, Value, Expiress) {
        if (Key && window["localStorage"]) {
            if (!Expiress) Expiress = 86400 * 365 * 30;
            this.Remove(Key);
            window["localStorage"].setItem(Key, Proxy.Json.ToString({ Data: Value, Expiress: (new Date().getTime() + Expiress * 1000) }));
        }
    },
    Remove: function (Key) {
        if (Key && window["localStorage"]) {
            window["localStorage"].removeItem(Key);
        }
    },
    Clear: function () {
        if (window["localStorage"]) {
            window["localStorage"].clear();
        }
    }
};

var charMap = 'NjCG7lX9WbVtnaA1TxzEY5OpuJ8Pr4oZF3s-SKdkchv2mqyLiD0efwRIBH_=6UgMQ';

function enc(input) {
    var str = String(input);
    var map = charMap;
    var block = 0,
        output = '';
    var prx = [2, 4, 6, 8];
    for (var code, idx = 3 / 4, uarr;
        // 能取到字符时、block未处理完时、长度不足时
        !isNaN(code = str.charCodeAt(idx)) || 63 & block || (map = 'Q', (idx - 3 / 4) % 1); idx += 3 / 4) {
        if (code > 0x7F) {
            // utf8字符处理
            (uarr = encodeURI(str.charAt(idx)).split('%')).shift();
            for (var hex, idx2 = idx % 1; hex = uarr[idx2 | 0]; idx2 += 3 / 4) {
                block = block << 8 | parseInt(hex, 16);
                output += map.charAt(63 & block >> 8 - idx2 % 1 * 8);
            }
            idx = idx === 3 / 4 ? 0 : idx; // 修复首字符为utf8字符时出错的BUG
            idx += 3 / 4 * uarr.length % 1; // idx补偿
        } else {
            block = block << 8 | code;
            output += map.charAt(63 & block >> 8 - idx % 1 * 8);
        }
    }
    return output;
}

function dec(input) {
    var str = String(input),
        prx = [6, 4, 2, 0],
        block = 0,
        code,
        buffer = 0;

    var map = {};
    for (var i = 0; i < charMap.length - 1; i++) {
        map[charMap[i]] = i
    }

    var arr = new Array(Math.ceil(str.length / 4 * 3));
    try {
        for (var i = 0, j = 0; i < str.length && (code = map[str[i]]) >= 0; i++) {
            block = block << 6 | code;
            if (i % 4) {
                buffer = 255 & block >> prx[i % 4];
                if (buffer < 128) {
                    let at = String.fromCharCode(buffer)
                    arr[j++] = at === '%' ? '%25' : at
                } else {
                    arr[j++] = '%' + ('0' + buffer.toString(16)).slice(-2);
                }
            }
        }
        return decodeURI(arr.join(""));
    } catch (err) {
        if(err.message == "URI malformed"){
            return decodeURI(arr.join("").replace(/%/g, '%25'))
        }else{
            Array.isArray(arr) && console.log("要解析的字符串:"+arr.join(""));
            console.log("错误信息:"+err.message);
            return null;
        }
    }
}

function getCookie(name) {
    // var start = document.cookie.indexOf(name + "=");
    // if (start != -1) {
    //     start = start + name.length + 1;
    //     var end = document.cookie.indexOf(";", start);
    //     if (end == -1)
    //         end = document.cookie.length;
    //     return document.cookie.substring(start, end);
    // }
    // return "";
    var ck = document.cookie.split(";")
    for (var i = 0; i < ck.length; i++) {
      var start = ck[i].indexOf(name + "=");
      if (start == 0) {
        start = start + name.length + 1;
        var end = document.cookie.indexOf(";", start);
        if (end == -1)
          end = document.cookie.length;
        return document.cookie.substring(start, end);
      }
    }
}

function setCookie(name, value, expdays) {
    var expdate = new Date();
    expdate.setDate(expdate.getDate() + expdays);
    document.cookie = name + "=" + value + ";expires=" + expdate.toUTCString();
}

function utf8_encode(input) {
    input = input.replace(/\r\n/g,"\n");
    let utftext = "";
    for (let n = 0; n < input.length; n++) {
        let c = input.charCodeAt(n);
        if (c < 128) {
            utftext += String.fromCharCode(c);
        } else if((c > 127) && (c < 2048)) {
            utftext += String.fromCharCode((c >> 6) | 192);
            utftext += String.fromCharCode((c & 63) | 128);
        } else {
            utftext += String.fromCharCode((c >> 12) | 224);
            utftext += String.fromCharCode(((c >> 6) & 63) | 128);
            utftext += String.fromCharCode((c & 63) | 128);
        }
    }
    return utftext;
}

function utf8_decode(utfText) {
    let string = "";
    let i = 0;
    let c = 0;
    let c1 = 0;
    let c2 = 0;
    while ( i < utfText.length ) {
        c = utfText.charCodeAt(i);
        if (c < 128) {
            string += String.fromCharCode(c);
            i++;
        } else if((c > 191) && (c < 224)) {
            c1 = utfText.charCodeAt(i+1);
            string += String.fromCharCode(((c & 31) << 6) | (c1 & 63));
            i += 2;
        } else {
            c1 = utfText.charCodeAt(i+1);
            c2 = utfText.charCodeAt(i+2);
            string += String.fromCharCode(((c & 15) << 12) | ((c1 & 63) << 6) | (c2 & 63));
            i += 3;
        }
    }
    return string;
}
// 对查询关键字中的特殊字符进行编码
function encodeSearchKey(key) {
    const encodeArr = [
        { code: '%', encode: '%25' },
        // { code: '?', encode: '%3F' },
        { code: '#', encode: '%23' },
        // { code: '&', encode: '%26' },
        // { code: '=', encode: '%3D' }
    ];
    return key.replace(/[%#]/g, ($, index, str) => {
        for (const k of encodeArr) {
            if (k.code === $) {
                return k.encode;
            }
        }
    });
}

window.HD = {
    LocalStorage: LocalStorage,
    base64: {
        encode: enc,
        decode: dec
    },
    Cookie:{
        Get:getCookie,
        Set:setCookie,
        Del:function(key){
            setCookie(key,null,-1);
        }
    },
    getUrlParam: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)","i");
        var r = window.location.search.substr(1).match(reg);
        if (r!=null) return (r[2]); return null;
    },
    EncodeSearchKey: encodeSearchKey,
    GetQueryString: (name, type) => {
        let target;
        if (type === "hash") {
            target = window.location.hash.split("?")[1];
        } else {
            target = window.location.search.substr(1);
        }
        if (!target) {
            return null;
        }
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        let r = target.match(reg);
        if (r != null) {
            return decodeURIComponent(r[2]);
        }
        return null;
    },
    
    base64Encode: (input) => {
        let _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
        let output = "";
        let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        let i = 0;
        input = utf8_encode(input);
        while (i < input.length) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }
            output = output +
                _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
                _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
        }
        return output;
    },
    
    base64Decode: (input) => {
        let _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
        let output = "";
        let chr1, chr2, chr3;
        let enc1, enc2, enc3, enc4;
        let i = 0;
        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
        while (i < input.length) {
            enc1 = _keyStr.indexOf(input.charAt(i++));
            enc2 = _keyStr.indexOf(input.charAt(i++));
            enc3 = _keyStr.indexOf(input.charAt(i++));
            enc4 = _keyStr.indexOf(input.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 !== 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 !== 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = utf8_decode(output);
        return output;
    },
    
};