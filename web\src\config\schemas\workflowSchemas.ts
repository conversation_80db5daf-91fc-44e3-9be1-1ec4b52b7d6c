/**
 * 开始结束的配置Schema
 */


import type {ComponentConfigSchema} from "@/types/config.ts";

// export const workflowStartSchema: ComponentConfigSchema = {
//   componentType: 'workflow_start',
//   version: '1.0.0',

//   groups: [
//     {
//       id: 'basic',
//       label: '基本配置',
//       description: '工作流开始的基本参数',
//       icon: 'VideoPlay',
//       order: 1,
//       collapsible: false,
//     },
//     // {
//     //   id: 'execution',
//     //   label: '执行策略',
//     //   description: '定时执行和错误处理配置',
//     //   icon: 'Timer',
//     //   order: 2,
//     //   collapsible: true,
//     //   collapsed: false,
//     // },
//     {
//       id: 'advanced',
//       label: '高级选项',
//       description: '工作流开始的高级配置',
//       icon: 'Setting',
//       order: 3,
//       collapsible: true,
//       collapsed: true,
//       advanced: false,
//     },
//   ],

//   fields: {
//     workflow_name: {
//       type: 'string',
//       label: '工作流名称',
//       description: '为工作流指定一个描述性名称',
//       placeholder: '请输入工作流名称',
//       help: '建议使用有意义的名称，便于识别和管理',
//       group: 'basic',
//       order: 1,
//       required: false,
//       validation: [
//         {
//           type: 'maxLength',
//           value: 100,
//           message: '工作流名称不能超过100个字符',
//         },
//       ],
//     },
//     description: {
//       type: 'textarea',
//       label: '工作流描述',
//       description: '详细描述工作流的功能和用途',
//       placeholder: '请输入工作流描述，例如：自动登录系统并下载报表',
//       help: '详细的描述有助于团队协作和后期维护',
//       group: 'basic',
//       order: 2,
//       required: false,
//       rows: 3,
//       validation: [
//         {
//           type: 'maxLength',
//           value: 500,
//           message: '工作流描述不能超过500个字符',
//         },
//       ],
//     },
//     // execution_mode: {
//     //   type: 'select',
//     //   label: '执行模式',
//     //   description: '选择工作流的执行方式',
//     //   help: '立即执行：马上开始；定时执行：按计划执行；手动触发：需要手动启动',
//     //   group: 'execution',
//     //   order: 1,
//     //   required: true,
//     //   default: 'immediate',
//     //   options: [
//     //     {
//     //       label: '立即执行',
//     //       value: 'immediate',
//     //       description: '工作流将立即开始执行',
//     //     },
//     //     {
//     //       label: '定时执行',
//     //       value: 'scheduled',
//     //       description: '按照设定的时间计划执行',
//     //     },
//     //     {
//     //       label: '手动触发',
//     //       value: 'manual',
//     //       description: '需要手动点击才能开始执行',
//     //     },
//     //   ],
//     // },
//     // schedule_type: {
//     //   type: 'select',
//     //   label: '定时类型',
//     //   description: '选择定时执行的类型',
//     //   group: 'execution',
//     //   order: 2,
//     //   required: true,
//     //   default: 'once',
//     //   conditions: [
//     //     {
//     //       field: 'execution_mode',
//     //       operator: 'equals',
//     //       value: 'scheduled',
//     //     },
//     //   ],
//     //   options: [
//     //     {
//     //       label: '单次执行',
//     //       value: 'once',
//     //       description: '在指定时间执行一次',
//     //     },
//     //     {
//     //       label: '每日执行',
//     //       value: 'daily',
//     //       description: '每天在指定时间执行',
//     //     },
//     //     {
//     //       label: '每周执行',
//     //       value: 'weekly',
//     //       description: '每周在指定时间执行',
//     //     },
//     //     {
//     //       label: 'Cron表达式',
//     //       value: 'cron',
//     //       description: '使用Cron表达式自定义执行时间',
//     //     },
//     //   ],
//     // },
//     // schedule_time: {
//     //   type: 'datetime',
//     //   label: '执行时间',
//     //   description: '指定执行的日期和时间',
//     //   group: 'execution',
//     //   order: 3,
//     //   required: true,
//     //   conditions: [
//     //     {
//     //       field: 'schedule_type',
//     //       operator: 'equals',
//     //       value: 'once',
//     //     },
//     //   ],
//     // },
//     // daily_time: {
//     //   type: 'time',
//     //   label: '每日执行时间',
//     //   description: '每天执行的时间',
//     //   group: 'execution',
//     //   order: 4,
//     //   required: true,
//     //   default: '09:00',
//     //   conditions: [
//     //     {
//     //       field: 'schedule_type',
//     //       operator: 'equals',
//     //       value: 'daily',
//     //     },
//     //   ],
//     // },
//     // error_strategy: {
//     //   type: 'select',
//     //   label: '错误处理策略',
//     //   description: '遇到错误时的处理方式',
//     //   help: '选择合适的错误处理策略可以提高工作流的稳定性',
//     //   group: 'execution',
//     //   order: 5,
//     //   required: true,
//     //   default: 'stop',
//     //   options: [
//     //     {
//     //       label: '停止执行',
//     //       value: 'stop',
//     //       description: '遇到错误时立即停止工作流',
//     //     },
//     //     {
//     //       label: '继续执行',
//     //       value: 'continue',
//     //       description: '忽略错误，继续执行后续步骤',
//     //     },
//     //     {
//     //       label: '重试执行',
//     //       value: 'retry',
//     //       description: '自动重试失败的步骤',
//     //     },
//     //     {
//     //       label: '跳过错误',
//     //       value: 'skip',
//     //       description: '跳过出错的步骤，继续执行',
//     //     },
//     //   ],
//     // },
//     // max_retries: {
//     //   type: 'number',
//     //   label: '最大重试次数',
//     //   description: '失败时的最大重试次数',
//     //   group: 'execution',
//     //   order: 6,
//     //   required: true,
//     //   default: 3,
//     //   min: 1,
//     //   max: 10,
//     //   conditions: [
//     //     {
//     //       field: 'error_strategy',
//     //       operator: 'equals',
//     //       value: 'retry',
//     //     },
//     //   ],
//     // },
//     // retry_delay: {
//     //   type: 'number',
//     //   label: '重试间隔(秒)',
//     //   description: '重试之间的等待时间',
//     //   group: 'execution',
//     //   order: 7,
//     //   required: true,
//     //   default: 5,
//     //   min: 1,
//     //   max: 300,
//     //   conditions: [
//     //     {
//     //       field: 'error_strategy',
//     //       operator: 'equals',
//     //       value: 'retry',
//     //     },
//     //   ],
//     // },
//     log_level: {
//       type: 'select',
//       label: '日志级别',
//       description: '设置工作流执行时的日志输出级别',
//       help: 'DEBUG级别会输出详细的调试信息，生产环境建议使用INFO',
//       group: 'advanced',
//       order: 1,
//       required: true,
//       default: 'INFO',
//       options: [
//         {
//           label: '调试 (DEBUG)',
//           value: 'DEBUG',
//           description: '输出详细的调试信息，用于开发调试',
//         },
//         {
//           label: '信息 (INFO)',
//           value: 'INFO',
//           description: '输出一般信息，推荐用于生产环境',
//         },
//         {
//           label: '警告 (WARN)',
//           value: 'WARN',
//           description: '只输出警告和错误信息',
//         },
//         {
//           label: '错误 (ERROR)',
//           value: 'ERROR',
//           description: '只输出错误信息',
//         },
//       ],
//     },
//     confirm_start: {
//       type: 'boolean',
//       label: '启动前确认',
//       description: '是否在工作流开始前要求用户确认',
//       help: '启用后会在执行前弹出确认对话框',
//       group: 'advanced',
//       order: 2,
//       required: false,
//       default: false,
//     },
//     timeout: {
//       type: 'number',
//       label: '超时时间',
//       description: '工作流最大执行时间，超时后自动终止',
//       help: '设置合理的超时时间可以避免工作流无限期运行',
//       placeholder: '300',
//       group: 'advanced',
//       order: 3,
//       required: false,
//       default: 300,
//       min: 1,
//       max: 3600,
//       step: 1,
//       unit: '秒',
//       validation: [
//         {
//           type: 'min',
//           value: 1,
//           message: '超时时间不能少于1秒',
//         },
//         {
//           type: 'max',
//           value: 3600,
//           message: '超时时间不能超过3600秒（1小时）',
//         },
//       ],
//     },
//   },

//   presets: {
//     simple: {
//       label: '简单模式',
//       description: '基础配置，适合简单的工作流',
//       config: {
//         log_level: 'INFO',
//         confirm_start: false,
//         timeout: 300,
//       },
//     },
//     debug: {
//       label: '调试模式',
//       description: '详细日志输出，适合开发调试',
//       config: {
//         log_level: 'DEBUG',
//         confirm_start: true,
//         timeout: 600,
//       },
//     },
//     production: {
//       label: '生产模式',
//       description: '生产环境配置，稳定可靠',
//       config: {
//         log_level: 'INFO',
//         confirm_start: false,
//         timeout: 1800,
//       },
//     },
//   },
// }

// export const workflowEndSchema: ComponentConfigSchema = {
//   componentType: 'workflow_end',
//   version: '1.0.0',

//   groups: [
//     {
//       id: 'basic',
//       label: '基本配置',
//       description: '工作流结束的基本参数',
//       icon: 'CircleClose',
//       order: 1,
//       collapsible: false,
//     },
//     {
//       id: 'notification',
//       label: '通知设置',
//       description: '完成通知和消息配置',
//       icon: 'Bell',
//       order: 2,
//       collapsible: true,
//       collapsed: false,
//     },
//     {
//       id: 'advanced',
//       label: '高级选项',
//       description: '工作流结束的高级配置',
//       icon: 'Setting',
//       order: 3,
//       collapsible: true,
//       collapsed: true,
//       advanced: true,
//     },
//   ],

//   fields: {
//     status: {
//       type: 'select',
//       label: '结束状态',
//       description: '指定工作流的结束状态',
//       help: '不同状态会影响工作流的最终执行结果',
//       group: 'basic',
//       order: 1,
//       required: true,
//       default: 'success',
//       options: [
//         {
//           label: '成功 ✅',
//           value: 'success',
//           description: '工作流正常完成，所有任务执行成功',
//         },
//         {
//           label: '失败 ❌',
//           value: 'failure',
//           description: '工作流执行失败，会抛出错误',
//         },
//         {
//           label: '取消 ⚠️',
//           value: 'cancelled',
//           description: '工作流被用户或系统取消',
//         },
//         {
//           label: '超时 ⏰',
//           value: 'timeout',
//           description: '工作流执行超时',
//         },
//       ],
//     },
//     message: {
//       type: 'textarea',
//       label: '结束消息',
//       description: '描述工作流结束的原因或结果',
//       placeholder: '例如：数据处理完成，共处理了100条记录',
//       help: '详细的结束消息有助于了解工作流的执行情况',
//       group: 'basic',
//       order: 2,
//       required: false,
//       rows: 3,
//       validation: [
//         {
//           type: 'maxLength',
//           value: 500,
//           message: '结束消息不能超过500个字符',
//         },
//       ],
//     },
//     notify_completion: {
//       type: 'boolean',
//       label: '完成通知',
//       description: '工作流完成后是否显示右下角弹窗通知',
//       help: '启用后会在工作流完成时显示桌面通知',
//       group: 'notification',
//       order: 1,
//       required: false,
//       default: true,
//     },
//     notification_title: {
//       type: 'string',
//       label: '通知标题',
//       description: '右下角弹窗通知的标题',
//       placeholder: '工作流执行完成',
//       group: 'notification',
//       order: 2,
//       required: false,
//       default: '工作流执行完成',
//       conditions: [{
//         field: 'notify_completion',
//         operator: 'equals',
//         value: true,
//       }]
//     },
//     notification_message: {
//       type: 'textarea',
//       label: '通知内容',
//       description: '右下角弹窗通知的详细内容',
//       placeholder: '工作流已成功完成所有任务',
//       group: 'notification',
//       order: 3,
//       required: false,
//       rows: 2,
//       conditions: [{
//         field: 'notify_completion',
//         operator: 'equals',
//         value: true,
//       }]
//     },
//     notification_type: {
//       type: 'select',
//       label: '通知类型',
//       description: '通知的类型，影响图标和颜色',
//       group: 'notification',
//       order: 4,
//       required: false,
//       default: 'success',
//       conditions: [{
//         field: 'notify_completion',
//         operator: 'equals',
//         value: true,
//       }]
//       options: [
//         {
//           label: '成功 ✅',
//           value: 'success',
//           description: '绿色成功通知',
//         },
//         {
//           label: '信息 ℹ️',
//           value: 'info',
//           description: '蓝色信息通知',
//         },
//         {
//           label: '警告 ⚠️',
//           value: 'warning',
//           description: '橙色警告通知',
//         },
//         {
//           label: '错误 ❌',
//           value: 'error',
//           description: '红色错误通知',
//         },
//       ],
//     },
//     notification_duration: {
//       type: 'number',
//       label: '通知持续时间(秒)',
//       description: '通知显示的持续时间，0表示不自动关闭',
//       group: 'notification',
//       order: 5,
//       required: false,
//       default: 5,
//       min: 0,
//       max: 30,
//       conditions: [{
//         field: 'notify_completion',
//         operator: 'equals',
//         value: true,
//       }]
//     },
//     return_data: {
//       type: 'textarea',
//       label: '返回数据',
//       description: '工作流执行完成后返回的数据',
//       placeholder: '{"processed_count": 100, "success_rate": 0.95}',
//       help: '支持JSON格式的数据，可用于后续处理或报告',
//       group: 'advanced',
//       order: 1,
//       required: false,
//       rows: 4,
//       validation: [
//         {
//           type: 'maxLength',
//           value: 1000,
//           message: '返回数据不能超过1000个字符',
//         },
//       ],
//     },
//     cleanup: {
//       type: 'boolean',
//       label: '执行清理',
//       description: '是否在工作流结束时执行清理操作',
//       help: '清理操作包括关闭浏览器、删除临时文件等',
//       group: 'advanced',
//       order: 2,
//       required: false,
//       default: true,
//     },
//     log_summary: {
//       type: 'boolean',
//       label: '记录摘要',
//       description: '是否在工作流结束时记录执行摘要',
//       help: '摘要包括执行时间、状态、处理数量等统计信息',
//       group: 'advanced',
//       order: 3,
//       required: false,
//       default: true,
//     },
//     notify_completion_end: {
//       type: 'boolean',
//       label: '完成通知',
//       description: '是否在工作流完成时发送通知',
//       help: '可以通过邮件、消息等方式通知相关人员',
//       group: 'advanced',
//       order: 4,
//       required: false,
//       default: false,
//     },
//   },

//   presets: {
//     success: {
//       label: '成功结束',
//       description: '正常成功完成的配置',
//       config: {
//         status: 'success',
//         cleanup: true,
//         log_summary: true,
//         notify_completion: false,
//       },
//     },
//     failure: {
//       label: '失败结束',
//       description: '执行失败时的配置',
//       config: {
//         status: 'failure',
//         cleanup: true,
//         log_summary: true,
//         notify_completion: true,
//       },
//     },
//     silent: {
//       label: '静默结束',
//       description: '不记录详细信息的配置',
//       config: {
//         status: 'success',
//         cleanup: false,
//         log_summary: false,
//         notify_completion: false,
//       },
//     },
//   },
// }

//  开始节点配置
export const workflowStartSchema: ComponentConfigSchema = {
  componentType: 'workflow_start',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '',
      icon: 'Document',
      order: 1,
      collapsible: true,
      collapsed: false,
    }
  ],

  fields: {
    mission_data: {
      type: 'missionEdit',
      label: ' ',
      group: 'basic',
      order: 1,
      labelHidden: true
    },
  },

  presets: {

  },
}


// 监测数据节点配置
// export const workflowStartSchema: ComponentConfigSchema = {
//   componentType: 'workflow_start',
//   version: '1.0.0',

//   groups: [
//     {
//       id: 'input',
//       label: '指令输入',
//       description: '',
//       icon: 'Connection',
//       order: 1,
//       collapsible: true,
//       collapsed: false,
//     },
//     {
//       id: 'output',
//       label: '指令输出',
//       description: '',
//       icon: 'Document',
//       order: 2,
//     },
//     {
//       id: 'other',
//       label: '其他设置',
//       description: '',
//       icon: 'Setting',
//       order: 3,
//       collapsible: true,
//       collapsed: false,
//     },
//   ],

//   fields: {
//     imb_stations: {
//       type: 'usertree',
//       label: '选择站点',
//       description: '',
//       placeholder: ' ',
//       required: true,
//       group: 'input',
//       order: 1
//     },
//     data_type: {
//       type: 'radio',
//       label: '数据变量',
//       description: '',
//       placeholder: ' ',
//       required: true,
//       group: 'input',
//       order: 2,
//       options: [
//         { label: '实时数据', value: 'real_time' },
//         { label: '历史数据', value: 'history' }
//       ]
//     },
//     query_time: {
//       type: 'radio',
//       label: '查询时间',
//       description: '',
//       placeholder: ' ',
//       required: true,
//       group: 'input',
//       order: 3,
//       options: [
//         { label: '今天', value: '0' },
//         { label: '最近24小时', value: '1' },
//         { label: '最近3天', value: '2' }
//       ]
//     },
//     data_interval: {
//       type: 'select',
//       label: '数据间隔',
//       description: '',
//       placeholder: ' ',
//       required: true,
//       group: 'input',
//       order: 4,
//       options: [
//         { label: '1分钟', value: '0' },
//         { label: '5分钟', value: '1' },
//         { label: '10分钟', value: '2' },
//         { label: '30分钟', value: '3' },
//         { label: '1小时', value: '4' }
//       ]
//     },
//     recognition_result: {
//       type: 'string',
//       label: '保存数据变量',
//       description: '',
//       placeholder: 'recognition_result',
//       required: true,
//       group: 'output',
//       order: 1,
//       outputVariable: true,
//       validation: [
//         {
//           type: 'pattern',
//           value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
//           message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
//         },
//       ],
//     },

//     timeout: {
//       type: 'number',
//       label: '连接超时秒数',
//       description: '',
//       required: true,
//       group: 'other',
//       order: 1,
//       default: 30,
//       min: 1,
//       max: 3000,
//       // unit: '秒',
//     },
//     error: {
//       type: 'errorretry',
//       label: '失败时重试',
//       description: '',
//       placeholder: ' ',
//       group: 'other',
//       order: 2,
//       children:[
//         {
//           id:'retry_times',
//           label: '重试次数',
//           description: '',
//           placeholder: ' ',
//           required: true,
//           group: 'other',
//           order: 2,
//           default: 1,
//           min: 1,
//           max: 10,
//           suffixUnit: '次',
//         },
//         {
//           id:'retry_delay',
//           label: '重试间隔',
//           description: '',
//           placeholder: ' ',
//           required: true,
//           group: 'other',
//           order: 2,
//           default: 1,
//           min: 1,
//           max: 10,
//           suffixUnit: '秒',
//         }
//       ]
//     },
//     error_handle: {
//       type: 'radio',
//       label: '错误处理方式',
//       description: '',
//       placeholder: ' ',
//       required: true,
//       group: 'other',
//       order: 3,
//       options: [
//         { label: '终止流程', value: 'stop' },
//         { label: '忽略并继续执行', value: 'ignore' }
//       ]
//     },
//   },

//   presets: {

//   },
// }

export const workflowEndSchema: ComponentConfigSchema = {
  componentType: 'workflow_end',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '工作流结束的基本参数',
      icon: 'CircleClose',
      order: 1,
      collapsible: false,
    },
    {
      id: 'notification',
      label: '通知设置',
      description: '完成通知和消息配置',
      icon: 'Bell',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '工作流结束的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    status: {
      type: 'select',
      label: '结束状态',
      description: '指定工作流的结束状态',
      help: '不同状态会影响工作流的最终执行结果',
      group: 'basic',
      order: 1,
      required: true,
      default: 'success',
      options: [
        {
          label: '成功 ✅',
          value: 'success',
          description: '工作流正常完成，所有任务执行成功',
        },
        {
          label: '失败 ❌',
          value: 'failure',
          description: '工作流执行失败，会抛出错误',
        },
        {
          label: '取消 ⚠️',
          value: 'cancelled',
          description: '工作流被用户或系统取消',
        },
        {
          label: '超时 ⏰',
          value: 'timeout',
          description: '工作流执行超时',
        },
      ],
    },
    message: {
      type: 'textarea',
      label: '结束消息',
      description: '描述工作流结束的原因或结果',
      placeholder: '例如：数据处理完成，共处理了100条记录',
      help: '详细的结束消息有助于了解工作流的执行情况',
      group: 'basic',
      order: 2,
      required: false,
      rows: 3,
      validation: [
        {
          type: 'maxLength',
          value: 500,
          message: '结束消息不能超过500个字符',
        },
      ],
    },
    notify_completion: {
      type: 'boolean',
      label: '完成通知',
      description: '工作流完成后是否显示右下角弹窗通知',
      help: '启用后会在工作流完成时显示桌面通知',
      group: 'notification',
      order: 1,
      required: false,
      default: true,
    },
    notification_title: {
      type: 'string',
      label: '通知标题',
      description: '右下角弹窗通知的标题',
      placeholder: '工作流执行完成',
      group: 'notification',
      order: 2,
      required: false,
      default: '工作流执行完成',
      conditions: [{
        field: 'notify_completion',
        operator: 'equals',
        value: true,
      }]
    },
    notification_message: {
      type: 'textarea',
      label: '通知内容',
      description: '右下角弹窗通知的详细内容',
      placeholder: '工作流已成功完成所有任务',
      group: 'notification',
      order: 3,
      required: false,
      rows: 2,
      conditions: [{
        field: 'notify_completion',
        operator: 'equals',
        value: true,
      }]
    },
    notification_type: {
      type: 'select',
      label: '通知类型',
      description: '通知的类型，影响图标和颜色',
      group: 'notification',
      order: 4,
      required: false,
      default: 'success',
      conditions: [{
        field: 'notify_completion',
        operator: 'equals',
        value: true,
      }],
      options: [
        {
          label: '成功 ✅',
          value: 'success',
          description: '绿色成功通知',
        },
        {
          label: '信息 ℹ️',
          value: 'info',
          description: '蓝色信息通知',
        },
        {
          label: '警告 ⚠️',
          value: 'warning',
          description: '橙色警告通知',
        },
        {
          label: '错误 ❌',
          value: 'error',
          description: '红色错误通知',
        },
      ],
    },
    notification_duration: {
      type: 'number',
      label: '通知持续时间(秒)',
      description: '通知显示的持续时间，0表示不自动关闭',
      group: 'notification',
      order: 5,
      required: false,
      default: 5,
      min: 0,
      max: 30,
      conditions: [{
        field: 'notify_completion',
        operator: 'equals',
        value: true,
      }]
    },
    return_data: {
      type: 'textarea',
      label: '返回数据',
      description: '工作流执行完成后返回的数据',
      placeholder: '{"processed_count": 100, "success_rate": 0.95}',
      help: '支持JSON格式的数据，可用于后续处理或报告',
      group: 'advanced',
      order: 1,
      required: false,
      rows: 4,
      validation: [
        {
          type: 'maxLength',
          value: 1000,
          message: '返回数据不能超过1000个字符',
        },
      ],
    },
    cleanup: {
      type: 'boolean',
      label: '执行清理',
      description: '是否在工作流结束时执行清理操作',
      help: '清理操作包括关闭浏览器、删除临时文件等',
      group: 'advanced',
      order: 2,
      required: false,
      default: true,
    },
    log_summary: {
      type: 'boolean',
      label: '记录摘要',
      description: '是否在工作流结束时记录执行摘要',
      help: '摘要包括执行时间、状态、处理数量等统计信息',
      group: 'advanced',
      order: 3,
      required: false,
      default: true,
    },
    notify_completion_end: {
      type: 'boolean',
      label: '完成通知',
      description: '是否在工作流完成时发送通知',
      help: '可以通过邮件、消息等方式通知相关人员',
      group: 'advanced',
      order: 4,
      required: false,
      default: false,
    },
  },

  presets: {
    success: {
      label: '成功结束',
      description: '正常成功完成的配置',
      config: {
        status: 'success',
        cleanup: true,
        log_summary: true,
        notify_completion: false,
      },
    },
    failure: {
      label: '失败结束',
      description: '执行失败时的配置',
      config: {
        status: 'failure',
        cleanup: true,
        log_summary: true,
        notify_completion: true,
      },
    },
    silent: {
      label: '静默结束',
      description: '不记录详细信息的配置',
      config: {
        status: 'success',
        cleanup: false,
        log_summary: false,
        notify_completion: false,
      },
    },
  },
}
