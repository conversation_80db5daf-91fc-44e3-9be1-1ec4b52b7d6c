<template>
  <div class="conditions-field">
    <div class="condition-list">
      <div
        v-for="(conditionGroup, groupIndex) in modelValue"
        :key="groupIndex"
        :class="getGroupClass(groupIndex)"
        class="condition-group"
      >
        <div class="condition-header">
          <div class="condition-flag">
            <div>{{ groupIndex === 0 ? 'IF' : 'ELIF' }}</div>
            <div class="case">CASE {{ groupIndex + 1 }}</div>
          </div>
          <template v-if="conditionGroup?.conditions?.length > 1">
            <div class="condition-actions">
              <div class="relation-switch" @click="changeRelation(conditionGroup)">
                {{ conditionGroup.relation.toUpperCase() }}
                <el-icon>
                  <Refresh />
                </el-icon>
              </div>
            </div>
            <div class="condition-relation-line"></div>
          </template>
        </div>

        <div class="sub-conditions">
          <div
            v-for="(condition, conditionIndex) in conditionGroup.conditions"
            :key="conditionIndex"
            :class="getChildGroupClass(conditionIndex)"
            class="sub-condition-item"
          >
            <div class="sub-condition-content-group">
              <div class="sub-condition-content">
                <VariableInputField
                  class="sub-condition-input-field"
                  :model-value="condition.field"
                  @update:model-value="
                    (val) => {
                      condition.field = val
                    }
                  "
                  :field-config="{ type: 'string', variableSupport: true, placeholder: '执行代码' }"
                  type="string"
                />

                <el-select v-model="condition.operator" placeholder="关系" size="small"
                           @change="condition.value = onOperatorChange(condition.operator, condition.value)">
                  <el-option label="包含" value="contains" />
                  <el-option label="不包含" value="notContains" />
                  <el-option label="等于" value="equals" />
                  <el-option label="不等于" value="notEquals" />
                  <el-option label="大于" value="greaterThan" />
                  <el-option label="大于等于" value="greaterThanOrEqual" />
                  <el-option label="小于" value="lessThan" />
                  <el-option label="小于等于" value="lessThanOrEqual" />
                  <el-option label="为空" value="isEmpty" />
                  <el-option label="不为空" value="isNotEmpty" />
                </el-select>
              </div>
              <div v-if="!['isEmpty', 'isNotEmpty'].includes(condition.operator)" class="sub-condition-content">
                <el-input
                  v-model="condition.value"
                  placeholder="输入值"
                  size="small"
                />
              </div>
            </div>
            <el-button
              :type="getChildGroupClass(conditionIndex)['is-hovered'] ? 'danger' : ''"
              size="small"
              @click="removeSubCondition(groupIndex, conditionIndex)"
              @mouseenter="handleChildMouseEnter(conditionIndex)"
              @mouseleave="handleChildMouseLeave"
              link
            >
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </div>
          <div class="sub-conditions-actions">
            <el-button size="small" plain @click="addSubCondition(groupIndex)">
              <el-icon>
                <Plus />
              </el-icon>
              <span>添加条件</span>
            </el-button>
            <el-button
              size="small"
              :type="getGroupClass(groupIndex)['is-hovered'] ? 'danger' : ''"
              text
              @click="removeConditionGroup(groupIndex)"
              v-if="modelValue.length > 1"
              @mouseenter="handleMouseEnter(groupIndex)"
              @mouseleave="handleMouseLeave"
            >
              <el-icon>
                <Delete />
              </el-icon>
              <span>移除</span>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-button
      class="add-condition-button"
      color="#F2F4F7"
      size="default"
      @click="addConditionGroup"
      style="margin-top: 8px"
    >
      <el-icon color="#333333">
        <Plus />
      </el-icon>
      <span>ELIF</span>
    </el-button>
    <div class="condition-group no-border">
      <div class="condition-header">
        <div class="condition-flag">
          <div>ELSE</div>
        </div>
      </div>
    </div>
    <div class="field-description">用于定义当 if 条件不满足时应执行的逻辑。</div>
  </div>
</template>

<script setup lang="ts">
import { Delete, Plus } from '@element-plus/icons-vue'
import VariableInputField from './VariableInputField.vue'
import { ref } from 'vue'

import { useWorkflowStore } from '@/stores/workflow'
const workflowStore = useWorkflowStore()

// @ts-ignore
import SnowflakeId from 'snowflake-id'

const generator = new SnowflakeId({
  offset: (2011 - 1970) * 31536000 * 1000,
})

interface Props {
  modelValue: Array<{
    condition_id: string
    relation: 'and' | 'or'
    conditions: Array<{
      condition_id: string
      field: string | null
      operator: string
      value: string | null
      conditions?: Array<any>
    }>
  }>
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const addConditionGroup = () => {
  const newGroup = {
    condition_id: 'node_' + generator.generate(),
    relation: 'and',
    conditions: [],
  }
  emit('update:modelValue', [...(props.modelValue || []), newGroup])
}

const removeConditionGroup = (groupIndex: number) => {
  const currentNode = workflowStore.selectedNode
  const groupToRemove = props.modelValue[groupIndex];
  const newGroups = [...props.modelValue]

  newGroups.splice(groupIndex, 1)
  hoveredGroupIndex.value = null

  // 删除相关边节点
  const edgeNode = workflowStore.edges.find(edge =>
    edge.sourceHandle === groupToRemove.condition_id
    && edge.source === currentNode?.id
  )
  if(edgeNode) {
    workflowStore.removeEdge(edgeNode.id)
  }

  if (!newGroups.length) {
    newGroups.push({
      condition_id: 'node_' + generator.generate(),
      relation: 'and',
      conditions: [],
    })
  }
  emit('update:modelValue', newGroups)
}

const addSubCondition = (groupIndex: number) => {
  const newGroups = [...props.modelValue]
  if (!newGroups[groupIndex].conditions) {
    newGroups[groupIndex].conditions = []
  }
  newGroups[groupIndex].conditions.push({
    condition_id: 'node_' + generator.generate(),
    field: null,
    value: null,
    operator: 'contains',
  })
  emit('update:modelValue', newGroups)
}

const removeSubCondition = (groupIndex: number, conditionIndex: number) => {
  const newGroups = [...props.modelValue]
  newGroups[groupIndex].conditions.splice(conditionIndex, 1)
  hoveredChildGroupIndex.value = null

  if (!newGroups[groupIndex].conditions.length) {
    newGroups[groupIndex].relation = 'and'
  }

  emit('update:modelValue', newGroups)
}

const changeRelation = (conditionGroup: { relation: 'and' | 'or' }) => {
  conditionGroup.relation = conditionGroup.relation === 'and' ? 'or' : 'and'
  emit('update:modelValue', [...props.modelValue])
}

// 修改后
const onOperatorChange = (operator: string, value: string | null) => {
  if (['isEmpty', 'isNotEmpty'].includes(operator)) {
    // 直接返回null值，不需要修改对象属性
    return null
  }
  emit('update:modelValue', [...props.modelValue])
  return value
}

// 添加hover状态管理
const hoveredGroupIndex = ref<number | null>(null)
const handleMouseEnter = (index: number) => {
  hoveredGroupIndex.value = index
}
const handleMouseLeave = () => {
  hoveredGroupIndex.value = null
}
// 添加动态类名计算
const getGroupClass = (index: number) => {
  return {
    'is-hovered': hoveredGroupIndex.value === index,
  }
}
const hoveredChildGroupIndex = ref<number | null>(null)
const handleChildMouseEnter = (index: number) => {
  hoveredChildGroupIndex.value = index
}
const handleChildMouseLeave = () => {
  hoveredChildGroupIndex.value = null
}
const getChildGroupClass = (index: number) => {
  return {
    'is-hovered': hoveredChildGroupIndex.value === index,
  }
}
</script>

<style scoped>
.conditions-field {
  width: 100%;
}

.condition-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 6px;
}

.condition-group {
  border-bottom: 1px solid #ebeef5;
  padding: 12px 5px;
  position: relative;
  transition: background-color 0.2s ease;
    &.no-border{
        margin-top: 14px;
        border-top: 1px solid #ebeef5;
        border-bottom: none;
        padding: 12px 5px;
        margin-bottom: 12px;
    }
}

.condition-group.is-hovered {
  background-color: #fef3f2;
  border-radius: 8px;
}

.condition-header {
  font-size: 12px;
  font-weight: 500;
  width: 60px;
  position: absolute;
  height: calc(100% - 56px);

  .condition-flag {
    font-weight: bold;
    line-height: 1.2;

    .case {
      font-weight: normal;
      font-size: 11px;
      line-height: 1;
      color: #676f83;
    }
  }
}

.condition-actions {
  display: flex;
  align-items: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background: #ffffff;
  right: 1px;
  border: 4px solid #fff;
  border-radius: 24px;
}

.condition-relation-line {
  border: 1px solid #dddee1;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border-right-width: 0;
  position: absolute;
  height: calc(100% - 30px);
  top: 15px;
  width: 10px;
  z-index: 1;
  right: 2px;
}

.relation-switch {
  border: 2px solid #ebeef5;
  border-radius: 24px;
  line-height: 1;
  padding: 2px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--el-color-primary);
  font-weight: bold;
  font-size: 12px;
}

.sub-conditions {
  padding-left: 60px;
}

.sub-conditions-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sub-condition-item {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
  margin-bottom: 8px;
}

.sub-condition-item.is-hovered {
  .sub-condition-content-group {
    background: #fef3f2;
  }
}

.sub-condition-content-group {
  background: #f1f3f6;
  border-radius: 8px;

  :deep(.el-input__wrapper) {
    background: none;
    box-shadow: none;
  }
}

.sub-condition-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 4px 4px 4px 0;

  .el-select {
    width: 105px;
    border-left: 1px solid #e3e6ed;
  }

  :deep(.el-select__wrapper) {
    background: none;
    box-shadow: none;
  }

  & ~ .sub-condition-content {
    border-top: 1px solid #e3e6ed;
  }
}
:deep(.sub-condition-input-field){
  .el-input-group__append{
    background: none;
    border-radius: 4px;
    box-shadow: none;
    //box-shadow: 0 1px 0 0 var(--el-input-border-color) inset,
    //            0 -1px 0 0 var(--el-input-border-color) inset,
    //            1px 0 0 0 var(--el-input-border-color) inset,
    //            -1px 0 0 0 var(--el-input-border-color) inset;
  }
}

.field-description {
  margin-top: 4px;
  font-size: 11px;
  color: #606266;
  line-height: 1.4;
}

.add-condition-button {
  width: 100%;
  color: #333333;

  .el-icon {
    margin-right: 4px;
  }
}
</style>
