import json
from typing import Optional, Union, Dict, Any

import aiohttp
import requests

from core.executor import ActionManager, ActionContext
from actions.api import group, logger


@group.action(
    type="http_request",
    label="HTTP 请求",
    description="发送HTTP请求并支持变量替换和响应捕获,可以选择POST和GET",
    category="api",
    icon="Upload",
    config_schema={
        "url_method": {"type": "string", "required": True},
        "url": {"type": "string", "required": True},
        "json_data": {"type": "string", "required": False},
        "headers": {"type": "string", "required": False},
        "response_content_variable": {"type": "string", "required": False},
        "verify_ssl": {"type": "boolean", "default": True},
    },
    outputs=["response", "response_text"],
)
async def http_request(context: ActionContext, config: Dict[str, Any]):

    url_method = config.get("url_method", "get")

    url = config.get("url")

    json_data = config.get("json_data", "{}")

    headers = config.get("headers", "{}")

    verify_ssl = config.get("verify_ssl", True)

    try:
        result = await _send_http_request(
            context=context,
            url_method=url_method,
            url=url,
            json_data=json_data,
            headers=headers,
            verify_ssl=verify_ssl,
        )
        logger.info(f"POST请求结果: {result}")
    except Exception as e:
        logger.warning(f"POST请求失败: {e}")


# 转换任何形式的响应结果为markdown格式
def response_to_markdown(response):
    # 如果响应结果是一个字符串、数字、布尔值、则直接返回
    try:
        if isinstance(response, (str, int, float, bool)):
            return str(response)
        # 取响应内容,结果的key可能是response或Response或data或Data
        if "response" in response:
            content = response["response"]
        elif "Response" in response:
            content = response["Response"]
        elif "data" in response:
            content = response["data"]
        elif "Data" in response:
            content = response["Data"]
        else:
            return "无法识别的响应结果"
        items = []
        # 如果content是一个字典，放到一个空数组中
        if isinstance(content, dict):
            items.append(content)
        elif isinstance(content, list):
            items = content
        else:
            return "无法识别的响应结果"

        markdown_table = json_to_markdown_table(items)
        return markdown_table
    except:
        return "无法识别的响应结果"


async def _send_http_request(
    context: ActionContext,
    url_method,
    url,
    json_data=None,
    headers=None,
    error_handle=False,
    response_content_variable="http_response",
    timeout=30,
    verify_ssl=True,
):
    """
    发送 HTTP 请求 支持不同请求方式
    """
    response = None
    method = url_method.strip().upper()
    if method not in ["POST", "GET"]:
        raise ValueError(f"暂不支持的请求方法: {method}")
    try:
        headers_dict = headers
        payload = json_data

        if method == "POST":
            response = requests.post(
                url,
                json=payload,
                headers=headers_dict,
                timeout=timeout,
                verify=verify_ssl,
            )
        elif method == "GET":
            response = requests.get(
                url,
                params=payload,
                headers=headers_dict,
                timeout=timeout,
                verify=verify_ssl,
            )

        # 判断响应是否为 JSON
        content_type = response.headers.get("Content-Type", "")
        is_json = "application/json" in content_type.lower()

        if response.status_code == 200:
            context.set_variable("is_dict", is_json)
            if is_json:
                data = response.json()
                json_str = json.dumps(data, indent=2, ensure_ascii=False)
                context.set_input(response_content_variable, json_str)
                return data
            else:
                context.set_variable(response_content_variable, response.text)
                # 设置固定
                context.set_variable("response_text", response.text)
                return response.text
        else:
            context.failed(
                f"请求成功但是返回错误状态码: 状态码 {response.status_code}, 响应内容: {response.text}"
            )

    except requests.exceptions.RequestException as e:
        context.failed(f"请求失败: {e}")


def json_to_markdown_table(json_data):
    if not json_data:
        return ""
    markdown_list = []
    # 添加数据行
    for data in json_data:
        markdown = "```markdown\n"
        # 定义一个markdown表格的头
        markdown += "| 名称 | 值 |\n| --- | --- |\n"

        items = [{"key": k, "value": v} for k, v in data.items()]
        for item in items:
            if not isinstance(item["value"], (dict, list)):

                markdown += f"| {item['key']} | {item['value']} |\n"
        markdown += "```"
        markdown_list.append(markdown)

    return markdown_list
