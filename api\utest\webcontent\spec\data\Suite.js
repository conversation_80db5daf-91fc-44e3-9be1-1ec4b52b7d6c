window.suiteOutput = {};

window.suiteOutput["suite"] = [1,2,3,4,[5,6],[1,0,143],[],[[7,8,9,[10,11],[1,36,106],[[0,12,13,0,14,15,0,0,[1,37,101],[[137,2,16]]],[3,17,0,0,0,0,0,0,[1,138,4],[[4,18,0,0,0,0,0,0,[1,138,2],[[0,19,0,0,0,20,0,0,[1,138,1],[[0,21,13,0,22,23,0,0,[1,139,1],[[139,2,24]]]]]]],[4,25,0,0,0,0,0,0,[1,140,2],[[0,19,0,0,0,20,0,0,[1,140,1],[[0,21,13,0,22,23,0,0,[1,141,1],[[141,2,26]]]]]]]]]]]],[],[1,1,0,0]];

window.suiteOutput["strings"] = [];

window.suiteOutput["strings"] = window.suiteOutput["strings"].concat(["*","*Suite","*/home/<USER>/Devel/robotframework/utest/webcontent/spec/data/Suite.robot","*utest/webcontent/spec/data/Suite.robot","*<p>suite doc\x3c/p>","*meta","*<p>data\x3c/p>","*Test","*1 second","*<p>test doc\x3c/p>","*tag1","*tag2","*Sleep","*BuiltIn","*<p>Pauses the test executed for the given time.\x3c/p>","*0.1 seconds","*Slept 100 milliseconds.","*${i}    IN RANGE    2","*${i} = 0","*my keyword","*${i}","*Log","*<p>Logs the given message with the given level.\x3c/p>","*index is ${index}","*index is 0","*${i} = 1","*index is 1"]);

window.suiteOutput["stats"] = [[{"elapsed":"00:00:00","fail":0,"label":"All Tests","pass":1,"skip":0}],[{"elapsed":"00:00:00","fail":0,"label":"tag1","pass":1,"skip":0},{"elapsed":"00:00:00","fail":0,"label":"tag2","pass":1,"skip":0}],[{"elapsed":"00:00:00","fail":0,"id":"s1","label":"Suite","name":"Suite","pass":1,"skip":0}]];

window.suiteOutput["errors"] = [];

window.suiteOutput["baseMillis"] = 1724172739991;

window.suiteOutput["generated"] = 151;

window.suiteOutput["expand_keywords"] = null;

window.settings = {"background":{"fail":"DeepPink"},"logURL":"log.html","reportURL":"report.html"};

