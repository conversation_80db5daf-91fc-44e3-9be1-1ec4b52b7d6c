<template>
  <div class="component-panel">
    <div class="panel-header">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          :placeholder="'请输入关键字' + ` [${getNotDisabledTotalActionsLength(filteredCategories) }/${
            getTotalActionsLength(filteredCategories)
          }]`"
          :prefix-icon="Search"
          clearable
          class="search-input"
        />
        <el-popover
          class="box-item"
          title="过滤"
          placement="right-start"
          width="172"
          :offset="24"
        >
          <span style="margin-right: 6px">过滤不可用节点</span>
          <el-switch v-model="hideDisabledComponents" />
          <template #reference>
            <span class="filter-disabled" @click.stop="() => {}">
              <el-icon><Filter /></el-icon>
            </span>
          </template>
        </el-popover>
      </div>
    </div>

    <el-scrollbar class="panel-content">
      <div class="package-auth-loading" v-if="!isAuthLoaded" v-loading="!isAuthLoaded" element-loading-text="指令库权限验证中...请稍等"></div>
      <el-collapse v-model="activeCategories" expand-icon-position="left">
        <el-collapse-item
          v-for="category in filteredCategories"
          :key="category.name"
          :title="category.label"
          :name="category.name"
          :icon="CaretRight"
        >
          <template #title>
            <div class="category-title">
              <div class="category-icon" v-html="getSvgIcon(category.icon)" />
              <span>{{ category.label }}</span>
              <div class="category-badge">
                [{{ getNotDisabledComponentsLength(category.components) }}/{{
                  category.components.length
                }}]
              </div>
            </div>
          </template>

          <div class="component-list">
            <div
              v-for="component in category.components"
              :key="component.type"
              :draggable="!component.disabled"
              @dragstart="onDragStart($event, component)"
              @dragend="onDragEnd"
            >
              <el-popover
                class="box-item"
                :disabled="!!component.disabled"
                placement="left"
                :offset="36"
                :show-after="200"
                width="190px"
              >
                <template #reference>
                  <div class="component-item" :class="{ disabled: !!component.disabled }">
                    <div class="component-icon">
                      <i :class="component.icon" />
                    </div>
                    <div class="component-info">
                      <div class="component-name">{{ component.label }}</div>
                    </div>
                  </div>
                </template>
                <template #default>
                  <div class="component-popover">
                    <div class="component-popover-title">
                      <div class="icon" :style="{'--el-color-primary': category.color}">
                        <el-icon :size="16">
                          <i :class="component.icon" />
                        </el-icon>
                      </div>
                      <div class="title">{{ component.label }}</div>
                    </div>
                    <div class="component-popover-description">{{ component.description }}</div>
                  </div>
                </template>
              </el-popover>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div class="no-data" v-if="!getTotalActionsLength(filteredCategories)">
        <el-empty :description="!isAuthCommand.length ? '暂无指令' : '没有该指令'" />
      </div>
    </el-scrollbar>
  </div>
  <div class="copyright-panel">
    <span>Copyright ©浙江和达科技股份有限公司</span>
  </div>
  <div class="right-collapse-icon" :title="!isCollapsed ? '折叠' : '展开'" @click.stop="toggleCollapse">
    <el-icon v-if="isCollapsed" size="14" color="#999"><CaretRight/></el-icon>
    <el-icon v-else size="14" color="#999"><CaretLeft/></el-icon>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Search, CaretRight } from '@element-plus/icons-vue'
import { getSvgIcon } from '@/assets/svg-icon'
import { componentCategories } from "@/utils/componentCategories"
import type { ComponentCategory, ComponentDefinition } from "@/utils/componentCategories"

// @ts-ignore
import saasApi from '@/api/index'

import { usePanelStore } from '@/stores/panel'
import { useTargetSourceStore } from '@/stores/targetSource'

const panelStore = usePanelStore()

// 新增折叠状态
const isCollapsed = computed(() => {
  return panelStore.isCollapsed
})
const toggleCollapse = () => {
  panelStore.setCollapsed(!isCollapsed.value)
}


// 定义是否还在获取权限中
const targetSourceStore = useTargetSourceStore()
// 权限加载情况
const isAuthQueried = computed(() => targetSourceStore.isAuthQueried)
// 指令加载情况
const isAuthCommandQueried = computed(() => targetSourceStore.isAuthCommandQueried)
// 整体加载状态
const isAuthLoaded = computed(() => {
  return isAuthQueried.value && isAuthCommandQueried.value
})
// 有权限的指令
const isAuthCommand = computed(() => targetSourceStore.command)
// 获取服务端指令库配置数据
const GetComponentCategories = () => {
  targetSourceStore.initCommandQueryStatus(false)
  const params = {
    url: '/wimai/api/task/command/query',
    body_param: {},
    method: 'post',
  }
  saasApi
    .AIDtemplateCrud(params)
    .then((res: any) => {
      if(res?.rows){
        targetSourceStore.initCommandData(res.rows)
      }
    })
    .finally(() => {
      targetSourceStore.initCommandQueryStatus(true)
    })
}

onMounted(() => {
  GetComponentCategories()
})

// 定义搜索内容
const searchQuery = ref('')
const activeCategories = ref(['browser', 'database'])

/**
 * 为 components 数组中每个组件的 config 对象添加缺失的属性
 * @param categories 分类数组，每个分类包含 components 数组
 */
const addMissingConfigProperties = (categories: ComponentCategory[])=>{
  categories.forEach((category: ComponentCategory) => {
    category.components.forEach((component: ComponentDefinition) => {
      const { config, type} = component
      if(!['workflow_start','workflow_end','wait','condition'].includes(type)){
        if (!config.hasOwnProperty('retry_times')) {
          config.retry_times = 1;
        }
        if (!config.hasOwnProperty('timeout')) {
          config.timeout = 15;
        }
        if (!config.hasOwnProperty('retry_delay')) {
          config.retry_delay = 1;
        }
        if (!config.hasOwnProperty('error_handle')) {
          config.error_handle = 'stop';
        }
      }
    })
  });
  return categories;
}

// 是否隐藏禁用组件
const hideDisabledComponents = ref(true)

// 根据权限先获取所有指令
const componentCategoriesAuth = computed(() => {
  if (!isAuthLoaded.value) {
    return []
  }
  let categories = componentCategories
  categories = categories
    .map((category: ComponentCategory) => ({
      ...category,
      // 过滤有权限code的指令
      components: category.components
        .map((component: ComponentDefinition) => {
          const componentData = targetSourceStore.commandList.find((item: any) => item.code === component.type)
          if(componentData){
            return {
              ...component,
              description: componentData.description || component.description,
              icon: 'action-iconfont ' + (componentData.iconPath || component.icon),
              status: componentData.status,
              isFree: componentData.isFree,
            }
          }
          return component
        })
        // 过滤掉权限中没有的指令，和指令库中下架的指令
        .filter((component: ComponentDefinition) => {
          return targetSourceStore.command.includes(component.type) && component.status === 1
        })
    }))
    .filter((category: ComponentCategory) => category.components.length > 0)
  return categories
})

// 过滤后的分类
const filteredCategories = computed(() => {
  const transComponentCategories = addMissingConfigProperties([...componentCategoriesAuth.value])
  let filtered = [...transComponentCategories]
  // 如果启用了隐藏禁用组件选项
  if (hideDisabledComponents.value) {
    filtered = filtered
      .map((category) => ({
        ...category,
        components: category.components.filter(component => !component.disabled)
      }))
      .filter(com => com.components.length > 0)
  }

  if (!searchQuery.value.trim()) {
    return filtered
  }

  const query = searchQuery.value.toLowerCase()
  return filtered
    .map((category) => ({
      ...category,
      components: category.components.filter(
        (component) =>
          component.label.toLowerCase().includes(query) ||
          component.description.toLowerCase().includes(query) ||
          component.type.toLowerCase().includes(query),
      ),
    }))
    .filter((category) => category.components.length > 0)
})

// 监听过滤后的分类
// 监听工作流元数据变化，同步工作流名称
watch(
  () => filteredCategories.value,
  (newValue) => {
    if (newValue.length) {
      activeCategories.value = newValue.map((category) => category.name)
    } else {
      activeCategories.value = ['browser', 'database']
    }
  },
  { immediate: true },
)

// 获取所有指令数量
const getTotalActionsLength = (categories: ComponentCategory[]) => {
  return categories.reduce((total, category) => {
    return total + category.components.length
  }, 0)
}
// 获取所有未被禁用指令数量
const getNotDisabledTotalActionsLength = (categories: ComponentCategory[]) => {
  return categories.reduce((total, category) => {
    return total + category.components.filter((comp) => !comp.disabled).length
  }, 0)
}

// 获取未被禁用组内元素数量
const getNotDisabledComponentsLength = (components: ComponentDefinition[]) => {
  return components.filter((component) => !component.disabled).length
}


// 拖拽事件处理
const onDragStart = (event: DragEvent, component: ComponentDefinition) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'component',
        component: component,
      }),
    )
    event.dataTransfer.effectAllowed = 'copy'
  }
}

const onDragEnd = (event: DragEvent) => {
  // 拖拽结束后的清理工作
  if (event.dataTransfer) {
    event.dataTransfer.clearData()
  }
}
defineExpose({
  getTotalActionsLength,
  getNotDisabledTotalActionsLength,
  filteredCategories,
  isCollapsed,
})
</script>

<style scoped lang="scss">
.package-auth-loading{
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}
.component-panel {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  background: white;
  transition: width 0.3s ease; /* 添加过渡效果 */
}

/* 新增折叠样式 */
.collapse-panel-icon {
  position: absolute;
  top: 4px;
  right: 12px;
  cursor: pointer;
  color: #666;
  padding: 0 4px;
}

.component-panel.collapsed {
  width: 60px;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;

  .panel-header{
    padding: 16px 12px;
  }
  .panel-title {
    justify-content: center;

    .num-total, .collapse-expand-icon {
      display: none;
    }
  }

  :deep(.el-collapse-item__header){
    display: none;
    padding-left: 7px;
    .el-collapse-item__arrow{
      display: none;
    }
  }
  :deep(.el-collapse-item__wrap){
    border-top: 1px solid #e7e7e7;
  }
  .component-list{
    padding: 0;
  }
  .component-item{
    padding: 3px 6px;
  }
}

.panel-header {
  padding: 16px;
}

.panel-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  width: 100%;
  position: relative;
  cursor: pointer;

  .num-total {
    font-size: 12px;
    color: #999999;
    position: absolute;
    right: 26px;
    font-weight: normal;
  }
  .collapse-expand-icon{
    padding: 0 4px;
  }
}
.search-box{
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  gap: 8px;
}
.filter-disabled{
  color: #666666;
  cursor: pointer;
}

.search-input {
  width: 100%;
}

:deep(.panel-content) {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px 0;
  .el-scrollbar__wrap {
    position: relative;
  }
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  user-select: none;
}
.category-icon{
  display: flex;
  align-items: center;
}

.category-badge {
  margin-left: auto;
  margin-right: 4px;
  color: #BCBFC3;
}

.component-list {
  padding: 0 0 8px 0;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  padding: 3px 12px 3px 44px;

  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;

  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  & ~ .component-item {
    margin-top: 3px;
  }
}

.component-item:not(.disabled):hover {
  background: #f5f7fa;
}

.component-item:active {
  cursor: grabbing;
  background: #f5f7fa;
}

.component-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  .action-iconfont{
    font-size: 14px;
  }
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-name {
  font-size: var(--el-font-size-base);
  line-height: 1.4;
  font-weight: 500;
  color: #303133;
}

.component-description {
  font-size: var(--el-font-size-small);
  color: #909399;
  line-height: 1.4;
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 深度选择器修改Element Plus样式 */
:deep(.el-collapse) {
  border: none;
}

:deep(.el-collapse-item__header) {
  border: none;
  margin-bottom: 4px;
  height: 32px;
  line-height: 32px;
  font-weight: 500;
  box-sizing: border-box;
}

:deep(.el-collapse-item__wrap) {
  border: none;
  background: transparent;
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

:deep(.el-collapse-item__arrow) {
  color: #999999;
}

:deep(.el-badge__content) {
  background: #409eff;
  border: none;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
}

.custom-tree-node .level1icon {
  color: #999999;
}

.custom-tree-node span {
  margin-left: 8px;
}

.disabled {
}

.component-popover {
  padding: 4px;

  .component-popover-title {
    display: flex;
    flex: 1;
    align-items: center;
    white-space: nowrap;

    .icon {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--el-color-primary);
      color: white;
      border-radius: 6px;
      margin-right: 8px;
      .action-iconfont{
        font-size: 14px;
        font-weight: normal;
      }
    }

    .title {
      font-size: var(--el-font-size-base);
      line-height: 1.4;
      font-weight: bold;
      color: #222222;
    }
  }
}

.component-popover-description {
  font-size: var(--el-font-size-extra-small);
  color: #5c5f66;
  margin-top: 8px;
  letter-spacing: 0;
}
.copyright-panel{
  box-sizing: border-box;
  flex-shrink: 0;
  height: 40px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 40px;
  padding: 0 16px;
  position: relative;
  font-size: 12px;
  color: #BCBFC3;
  user-select: none;
  text-align: center;
  box-shadow: 0 -3px 8px rgb(148 148 148 / 16%);
  &:before{
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: #eee;
  }
}
.right-collapse-icon{
  position: absolute;
  right: -17px;
  top: 50%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  /* 新增梯形样式 */
  &::before {
    content: "";
    position: absolute;
    width: 18px;
    height: 45px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-left: none;
    transform: perspective(5px) rotateY(8deg);
    transform-origin: left;
    border-radius: 0 6px 6px 0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    transition: background .2s;
  }
  .el-icon{
    transition: color .2s;
    left: -3px;
  }
  &:hover{
    .el-icon{
      color: #666;
    }
    &::before{
      background: #eef4ff;
    }
  }
}
</style>
