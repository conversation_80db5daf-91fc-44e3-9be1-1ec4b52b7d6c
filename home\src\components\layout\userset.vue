<template>
    <div class="dlyUniwim-user-set-container" v-loading.fullscreen.lock="fullscreenLoading">
        <slot name="prefix"></slot>
        <div class="uniwim-user-set-panel">
            <el-popover 
                v-model="userSetVisible" 
                placement="bottom-end" 
                :popper-class="'usersetPop uniwim-user-set-panel-popper ' + popoverClass" 
                :width="popoverWidth" 
                trigger="hover" 
                ref="userPop" 
                @show="popShow" 
                @hide="popHide"
            >
                <template #reference>
                    <a class="userset-button" :class="{ hover: userSetVisible }">
                        <slot name="info" :user="currentUser">
                            <img v-if="currentUser.avatar" @error="defaultImg" :src="currentUser.avatar" alt="" class="userset-avatar" />
                            <img v-else-if="defaultAvatar" :src="defaultAvatar" alt="" class="userset-avatar default-avatar" />
                            <i v-else class="iconfont icon-renyuan"></i>
                            <span>{{ currentUser.name || '' }}</span>
                            <i v-if="!hideIcon" class="iconfont icon-arrow-spread"></i>
                        </slot>
                    </a>
                </template>
                <ul class="userset-options">
                    <!-- <li v-if="configs && currentUser.id">
                        <tenantList :isMultiTenancy="isMultiTenancy"></tenantList>
                    </li> -->
                    <li @click="jumpToAdminPge">
                        <el-icon><UserFilled /></el-icon>
                        <span>个人中心</span>
                    </li>
                    <li v-if="configs?.hidePasswordChange !== '1'" @click="onPassword">
                        <i class="iconfont icon-mima"></i>
                        <span>修改密码</span>
                    </li>
                    <li @click="onLogout">
                        <i class="iconfont icon-tuichu"></i>
                        <span>退出系统</span>
                    </li>
                </ul>
            </el-popover>
        </div>

        <slot name="suffix"></slot>

        <!---------------------------------------------------------------------- 度量云修改密码 ------------------------------------------------------------------------>
        <el-dialog 
            v-if="dlyPwdVisiable" 
            v-model="dlyPwdVisiable" 
            :close-on-click-modal="false" 
            append-to-body 
            width="380px" 
            title="修改密码" 
            class="hd-form-dialog dly-pad-dialog" 
            :show-close="false"
        >
            <el-form ref="dlyModeRef" v-model="dlyMode" label-width="80px">
                <el-form-item label="手机号" style="margin-bottom: 28px !important">
                    <div class="phone-part">
                        <el-input v-model="dlyMode.mobile" placeholder="请输入内容" class="phone-item"></el-input>
                        <button type="button" class="send-btn" @click="sendCode" :disabled="disabled || isSended">{{ text }}</button>
                    </div>
                </el-form-item>
                <el-form-item label="验证码" style="margin-bottom: 28px !important">
                    <el-input v-model="dlyMode.code" placeholder="验证码"></el-input>
                </el-form-item>
                <el-form-item label="新密码" style="margin-bottom: 28px !important">
                    <el-tooltip class="item" effect="dark" placement="right-end">
                        <template #content><div v-html="pwdRuleTips"></div></template>
                        <el-input v-model="dlyMode.npwd" type="password" autocomplete="off" show-password />
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="确认密码" prop="rpwd" style="margin-bottom: 28px !important">
                    <el-tooltip class="item" effect="dark" placement="right-end">
                        <template #content><div v-html="pwdRuleTips"></div></template>
                        <el-input v-model="dlyMode.rpwd" type="password" show-password autocomplete="off" />
                    </el-tooltip>
                </el-form-item>
            </el-form>
            <p class="error" style="height: 30px">{{ dlyMode.err_info }}</p>
            <template #footer>
                <el-button  type="primary" @click="onDlyPasswordConfirm">确定</el-button>
                <el-button  @click="cancelDlyDiag" v-if="!defaultPassword">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import JSEncrypt from 'jsencrypt';
import tenantList from './tenantList.vue';
import { checkIsPhone, timer } from '@/utils/validate';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";
import systemApi from "@/api/system";
import utils from '@/utils/utils'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const userStore = useUserStore()
const loginStore = useLoginStore()

// 定义类型
interface User {
  id?: string | number;
  avatar?: string;
  name?: string;
  isAdmin?: boolean;
  isDefaultPwd?: number;
}

interface Configs {
  forcePasswordChange?: string;
  hidePasswordChange?: string;
  pwdRuleType?: string;
  pwdRule?: string;
  pwdMinLength?: number;
  weakPasswordSetting?: string;
}

interface DlyMode {
  mobile: string | null;
  code: string | null;
  npwd: string;
  rpwd: string;
  err_info: string | null;
}

// 组件props
const props = defineProps({
  hideIcon: {
    type: Boolean,
    default: false
  },
  defaultAvatar: {
    type: String,
    default: ''
  },
  popoverClass: {
    type: String,
    default: ''
  },
  popoverWidth: {
    type: Number,
    default: 110
  },
  isShow: {
    type: Boolean,
    default: false
  },
  isMultiTenancy: {
    type: Boolean,
    default: false
  }
});

// 组件emits
const emit = defineEmits([]);

// 响应式数据
const userSetVisible = ref(false);
const dlyPwdVisiable = ref(false);
const publicKey = ref<string | null>(null);
const defaultPassword = ref(false);
const systemConfig = ref<any>(null);
const fullscreenLoading = ref(false);
const userPop = ref<HTMLElement | null>(null);

// 密码表单数据
const dlyMode = reactive<DlyMode>({
  mobile: null,
  code: null,
  npwd: '',
  rpwd: '',
  err_info: null
});

// 验证码相关状态
const disabled = ref(true);
const isSended = ref(false);
const time = ref(60);
const text = ref('获取验证码');

// 表单引用
const dlyModeRef = ref<Ref<any>>(null);

// 计算属性
const currentUser = computed<User>(() => {
  return userStore.userInfo;
});

const configs = computed<Configs>(() => {
  return userStore.configs;
});

const pwdRuleTips = computed<string>(() => {
  let str = `新密码需要符合以下规则：<br/>1.`;
  switch (configs.value?.pwdRuleType) {
    case '1':
      str += '字母大写+字母小写+数字组合';
      break;
    case '2':
      str += '字母+数字+特殊字符组合';
      break;
    case '3':
      str += '字母+数字组合';
      break;
    case '4':
      if (configs.value?.pwdRule) {
        str += `满足正则策略：${configs.value.pwdRule}`;
      } else {
        str += '新密码不能为空';
      }
      break;
    case '5':
      str += '字母大写+字母小写+特殊字符+数字';
      break;
    default:
      str += '新密码不能为空';
      break;
  }
  str += `<br/>2.密码长度不能小于${configs.value?.pwdMinLength || 6}`;
  
  if (configs.value?.weakPasswordSetting) {
    str += `<br/>3.不能设置的密码名单有：${configs.value.weakPasswordSetting}`;
  }
  return str;
});

const isAdmin = computed<boolean>(() => {
  return currentUser.value && currentUser.value.isAdmin;
});

// 监听手机号变化
watch(
  () => dlyMode.mobile,
  (v) => {
    disabled.value = !v || !checkIsPhone(v);
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  onDefaultPassword();
});

// 方法定义
const popShow = () => {
  const iframeElement = document.getElementsByClassName('uniwim-layout-frame-page-content-iframe active');
  let message: any = {};
  
  nextTick(() => {
    if (userPop.value) {
      const ele = userPop.value as any;
      const popper = ele.$refs?.popper;
      
      if (popper) {
        message = {
          action: 'cutWindow',
          params: {
            popoverRect: popper.getBoundingClientRect(),
            iframeRect: iframeElement.length ? (iframeElement[0] as HTMLElement).getBoundingClientRect() : null
          }
        };
        
        const iframeWin = iframeElement.length ? (iframeElement[0] as HTMLIFrameElement).contentWindow : null;
        console.log('iframeWin', iframeElement[0]);
        iframeWin && iframeWin.postMessage(message, '*');
      }
    }
  });
};

const popHide = () => {
  const iframeElement = document.getElementsByClassName('uniwim-layout-frame-page-content-iframe active');
  const message = {
    action: 'repairWindow',
    params: {}
  };
  
  console.warn({ message });
  const iframeWin = iframeElement.length ? (iframeElement[0] as HTMLIFrameElement).contentWindow : null;
  iframeWin && iframeWin.postMessage(message, '*');
};

const onLogout = () => {
  if (currentUser.value?.id && utils) {
    // store.dispatch('LOGOUT');
    loginStore.LOGOUT()
    userStore.clearUser();
    debugger;
  } else {
    // window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
  }
};

const onDefaultPassword = () => {
  if (configs.value?.forcePasswordChange && 
      configs.value.forcePasswordChange.indexOf('3') !== -1 && 
      currentUser.value?.isDefaultPwd === 1) {
    defaultPassword.value = true;
    onPassword();
  }
};

const defaultImg = () => {
  if (currentUser.value) {
    currentUser.value.avatar = null;
  }
};

const onPassword = () => {
    ;
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    // @ts-ignore
    systemApi
      .initConfigs('')
      .then((configsData: any) => {
        systemConfig.value = configsData;
      })
      .catch((e: Error) => {
        console.error(e);
      });
      
    getEncryptKey();
    
    // 重置表单
    dlyMode.mobile = null;
    dlyMode.code = null;
    dlyMode.npwd = '';
    dlyMode.rpwd = '';
    dlyMode.err_info = null;
    
    dlyPwdVisiable.value = true;
  } else {
    window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
  }
};

const cancelDlyDiag = () => {
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    dlyPwdVisiable.value = false;
    dlyMode.mobile = null;
    dlyMode.code = null;
    dlyMode.npwd = '';
    dlyMode.rpwd = '';
    dlyMode.err_info = null;
    disabled.value = true;
    isSended.value = false;
    time.value = 60;
    text.value = '获取验证码';
  } else {
    window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
  }
};

const onDlyPasswordConfirm = () => {
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    dlyMode.err_info = null;
    
    if (!dlyMode.mobile) {
      dlyMode.err_info = '请输入手机号';
      return;
    }
    
    const isRight = checkIsPhone(dlyMode.mobile);
    if (!isRight) {
      dlyMode.err_info = '手机号有误';
      return;
    }
    
    if (!dlyMode.code) {
      dlyMode.err_info = '请输入手机验证码';
      return;
    }
    
    if (!dlyMode.npwd) {
      dlyMode.err_info = '请输入新密码';
      return;
    }
    
    if (!dlyMode.rpwd) {
      dlyMode.err_info = '请输入确认密码';
      return;
    }
    
    if (dlyMode.rpwd !== dlyMode.npwd) {
      dlyMode.err_info = '两次输入密码不一致!';
      return;
    }
    
    // @ts-ignore
    // store.dispatch('CURRENTUSER_EDITPwd', 1);
    
    getEncryptKey().then(() => {
      if (!publicKey.value) return;
      
      const encryptStr = new JSEncrypt();
      encryptStr.setPublicKey(publicKey.value);
      const encryptPwd = encryptStr.encrypt(dlyMode.npwd);
      
      if (!encryptPwd) {
        ElMessage.error('密码加密失败');
        return;
      }
      
      // @ts-ignore
      const pwd = HD.base64.encode(encryptPwd);
      const params = {
        mobile: dlyMode.mobile,
        code: dlyMode.code,
        npwd: pwd
      };
      
      // @ts-ignore
    //   systemApi
    //     .verificationChangePwd(params, {
    //       meta: {
    //         isData: false
    //       }
    //     })
    //     .then(() => {
          dlyPwdVisiable.value = false;
          
          // @ts-ignore
          ElMessageBox.confirm('密码修改成功，请重新登录', '提示', {
            showClose: false,
            showCancelButton:false,
            confirmButtonText: '确定',
          }).then(res=>{
            loginStore.LOGOUT()
            userStore.clearUser()
          });
        // })
        // .catch((err: any) => {
        //   // @ts-ignore
        // //   store.dispatch('CURRENTUSER_EDITPwd', 0);
        //   ElMessage.error(err.Message || err.message || err || '操作失败，请联系管理员');
        // });
    });
  } else {
    window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
  }
};

const handleSendCode = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    dlyMode.err_info = null;
    
    if (!dlyMode.mobile) {
      reject('请输入手机号');
      return;
    }
    
    // @ts-ignore
    systemApi
      .verificationCode(
        {
          mobile: dlyMode.mobile,
          type: 'CHANGE_PWD_CODE'
        },
        {
          meta: {
            isData: false
          }
        }
      )
      .then((res: any) => {
        if (res.Code === 0) {
          resolve();
        } else {
          reject(res.Message);
        }
      })
      .catch((err: Error) => {
        reject(err.message);
      });
  });
};

const sendCode = () => {
    ;
  if (isSended.value) return;
  
  disabled.value = true;
  const promise = handleSendCode();
  
  promise
    .then(() => {
      ElMessage.success('验证码已发送，请注意查收');
      isSended.value = true;
      letTimer();
    })
    .catch((err: string) => {
      ElMessage.error(err || '操作失败，请联系管理员');
      disabled.value = false;
    });
};

const letTimer = () => {
  let times = time.value;
  
  timer(
    times,
    (s: number) => {
      text.value = `已发送，${s}秒`;
    },
    () => {
      const isRight = dlyMode.mobile ? checkIsPhone(dlyMode.mobile) : false;
      if (dlyMode.mobile && isRight) {
        disabled.value = false;
      }
      text.value = '重新获取';
      isSended.value = false;
    }
  );
};

const getEncryptKey = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (publicKey.value) {
      resolve();
      return;
    }
    
    // @ts-ignore
    systemApi.getEncryptKey().then((res: any) => {
      if (res.publicKey) {
        publicKey.value = res.publicKey;
        sessionStorage.setItem('publicKey', res.publicKey);
        resolve();
      } else {
        reject('获取加密密钥失败');
      }
    }).catch(() => {
      reject('获取加密密钥失败');
    });
  });
};

const jumpToAdminPge = () => {
  debugger;
  if (currentUser.value?.id && window.localStorage.getItem('UniWimAuthorization')) {
    // router.push(
    //   {
    //     path: '/mine/service',
    //     query: route.query
    //   }
    // )
    let pathname = window.location.pathname;
    let url = utils.generateUrlWithQuery(pathname+'#/mine/service',route.query)
    window.open(url)
    // store.dispatch('OPEN_TAG', {
    //   Name: '',
    //   Val: 'admin.html',
    //   link: 'admin.html',
    //   target: '_blank'
    // });
  } else {
    window.top.postMessage('*#hd#*' + JSON.stringify({ action: 'LogoutSys', params: {} }), '*');
  }
};
</script>

<style scoped lang="less">
.dlyUniwim-user-set-container {
    float: right;
    height: 100%;
    // color: @platform-header-color;
    display: flex;
    justify-content: center;
    align-items: center;
    
    :deep(.uniwim-user-set-panel) {
        .userset-button {
            cursor: pointer;
            height: 32px;
            box-sizing: border-box;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            color: #ffffff;

            .userset-avatar {
                width: 32px;
                height: 32px;
                display: block;
                color: #029199;
                border-radius: 32px;
                object-fit: cover;
                
                &.default-avatar {
                    object-fit: contain;
                }
            }

            .iconfont {
                width: 24px;
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #029199;
                background: #e2f1f3;
                border-radius: 24px;
                
                &.icon-arrow-spread {
                    font-size: 18px !important;
                    height: 14px;
                    line-height: 14px;
                    width: 14px;
                    color: #222222;
                    background: none;
                }
            }

            > span {
                float: left;
                line-height: 20px;
                font-size: 14px;
                height: 20px;
                margin: 0 8px;
                font-weight: 400;
                color: #222222;
            }
        }
    }
}



:deep(.uniwim-user-set-detail-drawer) {
    padding: 12px 16px;
    box-sizing: border-box;
    
    .el-drawer__header {
        font-size: 18px;
        margin-bottom: 12px;
        color: #333333;
    }
}
</style>

<style lang="less">
.uniwim-user-set-panel-popper {
    &.el-popover.el-popper{
        min-width:110px;
        padding:0;
    }  
    .el-popper__arrow{
      display:block;
    }
    & > ul.userset-options {
        width: 110px;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        
        > li {
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
            height: 36px;
            line-height: 36px;
            // color: @color-common;
            display: inline-block;
            cursor: pointer;

            &:hover {
                background: #f0f1f3;
            }

            > i {
                float: left;
                height: 36px;
                width: 18px;
                display: block;
                font-size: 18px;
                line-height: 36px;
                margin: 0px 10px 0 8px;
                overflow: visible;
            }

            > span {
                float: left;
                display: block;
                height: 36px;
                line-height: 36px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
.dly-pad-dialog {
    padding:0;
    .el-button, body .el-button.el-button--mini {
        padding: 0 12px;
        height: 28px;
    }
    &.el-dialog .el-dialog__footer {
        padding: 16px;
    }
    &.el-dialog .el-dialog__header {
        padding: 10px 16px 0 16px;
        height: 40px;
        border-bottom: 1px solid #cbd5dd;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        background: #fbfbfb;
    }
    .el-dialog__header>.el-dialog__title {
        color: #000;
        display: block;
        height: 24px;
        float: left;
        font-size: 12px;
    }
    .el-input__wrapper{
        height:36px;
        line-height: 36px;;
    }
    .el-dialog__body {
        padding: 16px 16px 16px 16px;
        .send-code {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            font-size: 14px;

            input {
                flex: 1;
                width: 0 !important;
                border: 0;
                padding: 0 10px;
                background: none;
                outline: none;
                position: relative !important;
                
                &::placeholder {
                    color: #bfbfbf;
                }
            }

            .send-btn {
                border: 0;
                height: 100%;
                flex: 0 1 auto;
                padding: 0 10px;
                background-color: #f4f6f8;
                color: #333333;
                position: relative;
                cursor: pointer;
                line-height: 44px;
                font-size:12px;
                
                &:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0px;
                    height: 28px;
                    width: 1px;
                    background: #bfbfbf;
                }
                
                &:disabled {
                    opacity: 0.3;
                }
            }
        }
        
        .input-box {
            position: relative;
            height: 44px;
            border: 1px solid #f4f6f8;
            margin-top: 15px;
            overflow: hidden;
            background-color: #f4f6f8;
            border-radius: 4px;
            
            .login-bg-icon {
                background-image: url('../../assets/images/login/login-input-bg.png');
                
                &.nameicon {
                    background-position: 8px -34px;
                }
                
                &.customericon {
                    background-position: 8px 4px;
                }
                
                &.passwordicon {
                    background-position: 8px -73px;
                }
            }
            
            input,
            select {
                outline: none;
                line-height: 44px;
                height: 44px;
                border: 0;
                background: none;
                width: calc(100% - 44px);
                font-size: 14px;
                position: absolute;
                top: -1px;
                right: 0;
                display: block;
                
                &::placeholder {
                    color: #bfbfbf;
                }
            }
            
            .el-input {
                height: 44px;
                
                input {
                    padding: 0 26px 0 0;
                    outline: none;
                    line-height: 44px;
                    height: 44px;
                    border: 0;
                    background: none;
                    width: calc(100% - 44px);
                    font-size: 14px;
                    position: absolute;
                    top: -1px;
                    right: 0;
                    display: block;
                }
                
                i.el-input__icon.el-input__clear {
                    width: 26px;
                    font-size: 16px;
                }
            }
            
            &.no-icon {
                .el-input__inner,
                > input,
                select {
                    width: 100%;
                    padding: 0 10px;
                    box-sizing: border-box;
                }
            }
        }
        
        .select-ck {
            position: absolute;
            transform: translateY(6px);
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            align-items: center;
            left: 34px;
            right: 34px;
            
            > .forget-pwd,
            .el-checkbox {
                display: flex;
                cursor: pointer;
                
                span {
                    padding-left: 4px;
                    color: #888888;
                    font-size: 12px;
                }
                
                .el-checkbox__inner {
                    margin-top: 3px;
                }
            }
        }
        
        .submit {
            cursor: pointer;
            background: rgb(0, 141, 223);
            color: #fff;
            position: relative;
            width: 75%;
            margin: 30px 12.5% 0 12.5%;
            height: 38px;
            border: none;
            outline: 0;
            border-radius: 38px;
        }
        
        .error {
            color: Red;
            text-align: center;
            font-size: 12px;
            line-height: 30px;
            transition: all 0.2s ease;
        }

        .phone-part {
            display: flex;
            border: solid 1px #d0d0d0;
            
            .phone-item {
                width: calc(100% - 100px);
                .el-input__wrapper{
                    box-shadow: none;
                }
                .el-input__inner {
                    border: 0;
                }
            }
            
            .send-btn {
                border: 0;
                height: 100%;
                flex: 0 1 auto;
                padding: 0 10px;
                background-color: #f4f6f8;
                color: #333333;
                position: relative;
                cursor: pointer;
                line-height: 36px;
                font-size:12px;
                
                &:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0px;
                    height: 100%;
                    width: 1px;
                    background: #bfbfbf;
                }
                
                &:disabled {
                    opacity: 0.3;
                }
            }
        }
    }
}

.usersetPop {
    z-index: 9999999999 !important;
}
</style>