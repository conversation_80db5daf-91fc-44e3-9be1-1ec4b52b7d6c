#!/usr/bin/env python3
"""
简化的打包脚本
"""

import os
import sys
import shutil
import site
import subprocess
from pathlib import Path
from config import version

# fmt: off
def main():
    print("🚀 Phoenix RPA Engine 打包工具")
    print("=" * 40)

    # 0. 检查浏览器是否已安装
    print("🔍 检查浏览器安装...")
    plugins_dir = Path("plugins")
    browsers_dir = plugins_dir / "browsers"
    wrapper_dir = wrapper_path()

    if not wrapper_dir.exists():
        print("⚠️  browser.wrapper 包没有找到")
    else:
        print(f"有browser.wrapper {wrapper_dir.absolute()}")

    if not browsers_dir.exists() or not any(browsers_dir.glob("chromium-*")):
        print("⚠️  浏览器未安装，正在安装...")
        try:
            subprocess.run([sys.executable, "install_browsers.py"], check=True)
            print("✅ 浏览器安装成功")
        except subprocess.CalledProcessError:
            print("❌ 浏览器安装失败，请手动运行: python install_browsers.py")
            return
    else:
        print("✅ 浏览器已安装")

    # 1. 安装 PyInstaller
    print("📦 安装 PyInstaller...")
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "pyinstaller"], check=True
        )
        print("✅ PyInstaller 安装成功")
    except:
        print("❌ PyInstaller 安装失败")
        return

    # 2. 清理旧文件
    print("🧹 清理旧文件...")
    for path in ["dist", "build", "package"]:
        if os.path.exists(path):
            shutil.rmtree(path)

    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()

    # 3. 构建前端项目
    print("🌐 合并前端项目...")
    # 检查后端是否已存在 web 目录
    backend_web_dir = Path("web")
    if not backend_web_dir.exists():
        print("❌ 未找到前端项目目录且后端无预构建文件")
        # return
    else:
        print("⚠️ 未找到前端源码目录，使用已存在的构建文件")
    # 4. 构建命令
    cmd = [
        sys.executable,
        "-m",
        "PyInstaller",
        # '--windowed',
        "--name",
        "main",
        "--noconsole",
        "--onefile",
        "--distpath",
        "dist",
        "--workpath",
        "build",
        "--workpath",
        "listener.execution_monitor",
        "--runtime-tmpdir", ".",
    ]

    # 添加数据文件
    data_dirs = [
        (wrapper_dir, "Browser/wrapper"),
        ("transpiler/templates", "transpiler/templates"),
        ("api", "api"),
        ("common", "common"),
        ("models", "models"),
        ("keywords", "keywords"),
        ("experimental", "experimental"),
        ("config", "config"),
        ("plugins", "plugins"),  # 包含浏览器和插件
        ("data/reports/templates", "data/reports/templates"),  # 报表模板目录
        ("web", "web"),  # 前端静态文件目录
    ]
    # 在 data_dirs 列表中添加 workflow-use 路径
    workflow_use_path = str(
        Path("experimental/workflow-use/workflow-use-main/workflows")
    )
    data_dirs.append((workflow_use_path, "workflow_use"))
    # 添加Robot Framework HTML模板文件
    try:
        import robot

        robot_path = Path(robot.__file__).parent
        htmldata_path = robot_path / "htmldata"
        if htmldata_path.exists():
            data_dirs.append((str(htmldata_path), "robot/htmldata"))
    except ImportError:
        print("⚠️ Robot Framework未安装，跳过HTML模板文件")

    for src, dst in data_dirs:
        if os.path.exists(src):
            cmd.extend(["--add-data", f"{src};{dst}"])

    try:
        pandas_libs_path = (
            site.getsitepackages()[0] + "\\Lib\\site-packages\\pandas.libs"
        )
        cmd.extend(["--add-data", f"{pandas_libs_path};pandas.libs"])
        cmd.extend(["--add-data", f"node.exe;."])
    except:
        print("⚠️ pandas.libs未安装，跳过文件")
    # 添加隐藏导入
    hidden_imports = [
        "nanoid",
        "fastapi", "uvicorn", "robot", "RPA", "playwright", "pygame", "pygame._sdl2.audio", "wave",
        "psutil", "loguru", "requests", "jinja2", "websockets","js2py",
        "docx", "python-docx",
        "transpiler.workflow_transpiler", "executor.task_executor", "scheduler.task_scheduler", "models.workflow",
        "services.intelligent_recording_service", "services.browser_recording_service", # 这两个是不是不要了
        "utils.process_manager", "utils.browser_manager", "api.crud", "listener.execution_monitor",
        "keywords.data_utils", "keywords.sand_box_actuator", # 这两个是不是也不需要的
        "robot.htmldata.rebot", "RequestsLibrary", "pymysql",
        "RPA.Browser.Playwright",
        "RPA.Tables", "RPA.Database", "RPA.FileSystem", "RPA.HTTP", "RPA.Desktop", "RPA.Excel.Files", "RPA.Excel.Files",  "RPA.Email.ImapSmtp",
        "docx", "docx.shared", "docx.oxml", "docx.document", "docx.paragraph", "docx.text.run", "docx.table", "msoffcrypto",
        "openai","httpx","markdown","bs4","beautifulsoup4","sounddevice"
        # 模板设计器和Excel组件相关
        "openpyxl", "openpyxl.workbook", "openpyxl.worksheet", "openpyxl.styles", "openpyxl.utils", "openpyxl.cell", "openpyxl.formatting", "openpyxl.drawing", "openpyxl.chart",
        "api.report_templates", "models.report_template", "pydantic", "uuid", "datetime", "pathlib", "json", "ast", "io", "pandas", "aiofiles", "numpy", "aiohttp",
        "pandas._libs.window.aggregations", "pandas.libs",
        # browser-use相关的
        "browser-use", "fastmcp", "typer", "uvicorn", "langchain", "langchain-openai", "langchain-core", "patchright", "build", "ruff",
        "win32com", "win32com.client", "win32com.client.gencache", "browser_use",  # 确保添加这一行
        "pymupdf", "fitz", "keywords.word_operate_exec", "typing", "utils.word_tools.render_canvas", "utils.word_tools.canvas_template_renderer",
        "docx.oxml.ns", "docx.enum.text", "docx.shared", "re", "os", "statistics",
        "robotframework-browser", "rpaframework", "workflow_use",  # 同时添加主模块
        "workflow_use.recorder.service", "workflow_use.controller.service", "workflow_use.builder.service",
    ]
    # 在 hidden_imports 中追加
    for module in get_robot_libraries():
        if module not in hidden_imports:
            hidden_imports.append(module)

    # 在 hidden_imports 中追加docx
    for module in get_docx_modules():
        if module not in hidden_imports:
            hidden_imports.append(module)

    # 在 hidden_imports 中追加
    for module in get_all_robot_modules():
        if module not in hidden_imports:
            hidden_imports.append(module)

    # 在 hidden_imports 中追加openpyxl模块
    for module in get_openpyxl_modules():
        if module not in hidden_imports:
            hidden_imports.append(module)

    # 在 hidden_imports 中追加pandas模块
    for module in get_pandas_modules():
        if module not in hidden_imports:
            hidden_imports.append(module)

    # 在 hidden_imports 中追加numpy模块
    for module in get_numpy_modules():
        if module not in hidden_imports:
            hidden_imports.append(module)

    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])

    # 排除不需要的模块
    excludes = ["tkinter", "matplotlib"]  # 排除Browser库，避免依赖问题
    for module in excludes:
        cmd.extend(["--exclude-module", module])

    cmd.append("main.py")

    # 5. 执行打包
    print("🔨 开始打包...")
    print("命令:", " ".join(cmd))

    try:
        subprocess.run(cmd, check=True)
        print("✅ 打包成功!")
    except subprocess.CalledProcessError:
        print("❌ 打包失败!")
        return

    # 6. 创建分发包
    print("📦 创建分发包...")

    os.makedirs("package", exist_ok=True)
    # 复制可执行文件
    if os.path.exists("dist/main.exe"):
        appVersion = version.getVersion()
        packageFile = f"package/WimTask-{appVersion}.exe"
        shutil.copy2("dist/main.exe", packageFile)

        # 获取文件大小
        size_mb = os.path.getsize(packageFile) / (1024 * 1024)
        print(f"📏 文件大小: {size_mb:.1f} MB")
        # 增加版本号。只更新小版本 如1.0.0 -> 1.0.1
        version.incrVersion()

    # 创建目录
    for dir_name in [
        "logs",
        "outputs",
        "plugins",
        "config",
        "data",
        "data/reports",
        "data/reports/templates",
    ]:
        os.makedirs(f"package/{dir_name}", exist_ok=True)

    # 创建启动脚本
    with open("package/start.bat", "w", encoding="utf-8") as f:
        f.write(
            """@echo off
echo Starting WimTask Engine...
WimTask.exe
pause
"""
        )

    # 创建说明文件
    with open("package/README.txt", "w", encoding="utf-8") as f:
        f.write(
            """WimTask Engine
==================

使用说明:
1. 双击 start.bat 启动服务
2. 服务启动后查看控制台获取访问地址
3. 浏览器已内置，无需额外下载

功能特性:
- 内置统一浏览器引擎
- 支持录制和执行工作流
- 完全离线运行

注意事项:
- 确保防火墙允许程序访问网络
- 如遇端口冲突，程序会自动切换端口
- plugins/ 目录包含浏览器，请勿删除
"""
        )

    print("🎉 打包完成!")
    print("📁 分发包位置: package/")
    print("💡 可以将 package 目录整体分发给用户")


def get_robot_libraries():
    # 获取 robotframework 安装路径
    import robot

    lib_dir = Path(robot.__file__).parent / "libraries"

    # 扫描所有 .py 文件
    modules = []
    for file in os.listdir(lib_dir):
        if file.endswith(".py") and not file.startswith("__"):
            module_name = f"robot.libraries.{file[:-3]}"
            modules.append(module_name)
    return modules


def get_docx_modules():
    try:
        import docx
    except ImportError:
        print("⚠️ docx 模块未安装，跳过自动导入")
        return []

    from pathlib import Path
    import os

    docx_path = Path(docx.__file__).parent
    modules = []

    for root, dirs, files in os.walk(docx_path):
        rel_root = Path(root).relative_to(docx_path)
        for file in files:
            if file.endswith(".py") and not file.startswith("__"):
                module_name = "docx." + str(rel_root).replace(os.sep, ".")
                module_name = module_name.replace("..", "")
                if file[:-3] != "__init__":
                    module_name += "." + file[:-3]
                modules.append(module_name)

    return modules


# 获取所有 robot 子模块
def get_all_robot_modules():
    import robot
    from pathlib import Path
    import os

    robot_path = Path(robot.__file__).parent
    modules = []

    for root, dirs, files in os.walk(robot_path):
        rel_root = Path(root).relative_to(robot_path)
        for file in files:
            if file.endswith(".py") and not file.startswith("__"):
                module_name = "robot." + str(rel_root).replace(os.sep, ".")
                if file[:-3] != "__init__":
                    module_name += "." + file[:-3]
                modules.append(module_name)

    return modules


def get_openpyxl_modules():
    """获取openpyxl的所有子模块"""
    try:
        import openpyxl
    except ImportError:
        print("⚠️ openpyxl 模块未安装，跳过自动导入")
        return []

    from pathlib import Path
    import os

    openpyxl_path = Path(openpyxl.__file__).parent
    modules = []

    for root, dirs, files in os.walk(openpyxl_path):
        rel_root = Path(root).relative_to(openpyxl_path)
        for file in files:
            if file.endswith(".py") and not file.startswith("__"):
                module_name = "openpyxl." + str(rel_root).replace(os.sep, ".")
                module_name = module_name.replace("..", "")
                if file[:-3] != "__init__":
                    module_name += "." + file[:-3]
                modules.append(module_name)

    return modules


def get_pandas_modules():
    """获取pandas的核心模块"""
    try:
        import pandas
    except ImportError:
        print("⚠️ pandas 模块未安装，跳过自动导入")
        return []

    # 只导入pandas的核心模块，避免导入过多不必要的子模块
    core_modules = [
        "pandas",
        "pandas.core",
        "pandas.core.frame",
        "pandas.core.series",
        "pandas.io",
        "pandas.io.excel",
        "pandas.io.common",
        "pandas.io.parsers",
        "pandas._libs",
        "pandas.util",
    ]

    return core_modules


def get_numpy_modules():
    """获取numpy的核心模块"""
    try:
        import numpy
    except ImportError:
        print("⚠️ numpy 模块未安装，跳过自动导入")
        return []

    # 只导入numpy的核心模块
    core_modules = [
        "numpy",
        "numpy.core",
        "numpy.core._multiarray_umath",
        "numpy.core._multiarray_tests",
        "numpy.linalg",
        "numpy.fft",
        "numpy.random",
        "numpy.ma",
        "numpy.lib",
        "numpy.distutils",
    ]

    return core_modules


def wrapper_path():
    # 获取 robotframework-browser 库的安装路径
    try:
        import Browser

        browser_path = os.path.dirname(Browser.__file__)
        wrapper_path = os.path.join(browser_path, "wrapper")
    except ImportError:
        # 如果导入失败，手动指定路径（根据你的环境修改）
        wrapper_path = r"C:\path\to\site-packages\Browser\wrapper"  # Windows 示例

    return Path(wrapper_path)


if __name__ == "__main__":
    main()
# fmt: on
