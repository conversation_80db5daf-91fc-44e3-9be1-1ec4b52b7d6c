# -*- coding: utf-8 -*-

# from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from robotlibcore import keyword
from typing import Dict, List, Optional, Any, Set
import logging
import json
import requests
import base64
import re
import logging
import os
import fitz  # PyMuPDF
from datetime import datetime
from servers.websocket import ws_broadcast_proxy
from config.env_config import get_config_item, CV_URL



@keyword("Image Recognition")
def image_recognition(img: list[str], prompt: str):
    try:

        # 构造 Base64 图像列表
        base64_images = [image_file_to_base64(path) for path in img]

        # 请求地址
        url = "http://10.80.20.25:8888/v1/chat/completions"

        # 请求头
        headers = {"Authorization": "Bearer none", "Content-Type": "application/json"}

        # 构建消息内容
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": f"{prompt}"},
                    *[
                        {"type": "image_url", "image_url": {"url": base64_image}}
                        for base64_image in base64_images
                    ],
                ],
            }
        ]

        # 构建请求体 payload
        payload = {
            "model": "qwen2-vl",  # 根据你的实际模型名称调整
            "messages": messages,
            "temperature": 0.8,
            "top_p": 0.8,
        }
        # 发起 POST 请求
        response = requests.post(url, headers=headers, json=payload)

        choices = response.json().get("choices", [{}])

        out_content = choices[0]["message"]["content"]
        # for choice in choices:
        #     message = choice.get("message", {})
        #     content = message.get("content")
        #     out_content = content

        recognition_result = {"content": out_content, "url": img}

        return recognition_result
    except Exception as e:
        return f"image_recognition error:{str(e)}"


# 如果不是Base64编码则进行加密
def image_file_to_base64(url: str):
    if not is_base64(url):
        return url_to_base64(url)
    else:
        return url


# 判断字符串是否为 Base64 编码
def is_base64(sb):
    try:
        # 检查是否包含 Base64 前缀
        if isinstance(sb, str):
            # 检查是否有 data URI 前缀
            if sb.startswith("data:"):
                return True
            # 检查是否为纯 Base64 字符串（不含前缀）
            sb_bytes = bytes(sb, "ascii")
        elif isinstance(sb, bytes):
            sb_bytes = sb
        else:
            return False
        # 尝试解码
        decoded = base64.b64decode(sb_bytes, validate=True)
        return True
    except Exception:
        return False


# 从 URL 获取图片并转换为 Base64
def url_to_base64(url: str) -> str:
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        image_bytes = response.content
        base64_encoded = base64.b64encode(image_bytes).decode("utf-8")

        content_type = response.headers.get("Content-Type", "image/jpeg")
        return f"data:{content_type};base64,{base64_encoded}"
    except requests.exceptions.RequestException as e:
        return f"url_to_base64 error:{str(e)}"


invoice_field_mapping = {
    "trainTickets": [
        {"value": "company", "name": "购买方名称"},
        {"value": "date", "name": "乘车日期"},
        {"value": "train_number", "name": "车次 / 班次"},
        {"value": "departure", "name": "出发站"},
        {"value": "arrival", "name": "到达站"},
        {"value": "amount", "name": "金额"},
        {"value": "number", "name": "发票或票据号码"}
    ],
    "taxiTickets": [
        {"value": "car_number", "name": "车号"},
        {"value": "date", "name": "开票日期"},
        {"value": "amount", "name": "金额"},
        {"value": "number", "name": "发票或票据号码"}
    ],
    "invoice": [
        {"value": "type", "name": "发票类型"},
        {"value": "company", "name": "购买方名称"},
        {"value": "seller", "name": "销售方名称"},
        {"value": "date", "name": "开票日期"},
        {"value": "amount", "name": "价税合计金额"},
        {"value": "tax_rate", "name": "税率"},
        {"value": "number", "name": "发票号码"}
    ],
    "bankreceipt": [
        {"value": "type", "name": "银行回单类型"},
        {"value": "date", "name": "记账日期"},
        {"value": "amount", "name": "金额"}
    ],
    "highwayToll": [
        {"value": "type", "name": "发票类型"},
        {"value": "licensePlate", "name": "车牌号"},
        {"value": "date", "name": "开票日期"},
        {"value": "entryTime", "name": "通行时间起"},
        {"value": "exitTime", "name": "通行时间止"},
        {"value": "price", "name": "金额"},
        {"value": "amount", "name": "价税合计金额"},
        {"value": "tax_rate", "name": "税率"},
        {"value": "number", "name": "发票号码"},
        {"value": "tax_amount", "name": "税额"}
    ]
}


def invoice_type_to_prompt(invoice_type: str) -> str:
	base_intro = """
请识别我上传的图像内容，判断是否为对应类型发票，并按如下规则返回结构化结果：

---
"""
	prompts = {
		"trainTickets": """
## 请提取火车票中的以下信息：

- company（购买方名称）：提取发票或票据上的购票人名称，不是“中国铁路”或出票单位；如果没有明确购买方信息，请返回“无”。
- date（乘车日期）：仅保留日期部分，格式为“YYYY-MM-DD”，忽略具体时间。
- train_number（车次 / 班次）：为车次标识，通常是数字和字母组合，长度不超过5位，如"G1234"；避免提取为票据号码。
- departure（出发站）：火车出发站名称。
- arrival（到达站）：火车到达站名称。
- amount（金 额）：提取票据金额，不需要人民币符号（如¥或￥），保留单位为“元”，例如：123.45元。
- number（票据号码）：为发票或票据号码。

若图片信息无法识别为火车票（如登机牌、飞机票等），请仅返回以下内容：
当前仅支持识别火车票。

### 输出 JSON 示例：

{
  "type": "火车票",
  "company": "XXX有限公司",
  "date": "2024-08-01",
  "train_number": "G1234",
  "departure": "上海虹桥",
  "arrival": "北京南",
  "amount": "123.45元",
  "number": "E123456789"
}
""",
		"taxiTickets": """
## 请提取出租车票中的以下信息（必须完全基于图片内容，不得使用示例值，不得凭空编造）：

- car_number（车号）：从票据中识别的真实车牌号，必须符合中国大陆车牌号规则：
  2. 第一位是大写字母（A-Z）。
  3. 后面是5位由大写字母和数字组成的组合（如“A12345”）。
  4. 若图片中没有清晰车牌号，请返回 null。

- date（开票日期）：仅保留日期部分，格式为“YYYY-MM-DD”，忽略时间或其他多余信息。

- amount（金 额）：提取票据中标注的金额（单位为“元”），必须严格使用图片中的金额数值，并保留两位小数（如“21.00元”）。禁止虚构金额，禁止转换为其他货币单位。

- number（票据号码）：从票据中提取的发票号码，可能是八位纯数字。禁止使用示例号码。

若图片无法识别为出租车票（例如其他类型发票或非票据图像），请仅返回以下内容：
当前仅支持识别出租车票。

### 输出 JSON 示例（示例值仅作格式参考，所有字段必须根据图片内容填写）：

{
  "type": "出租车票",
  "car_number": "车牌号（来自图片，例如ABN9025）",
  "date": "YYYY-MM-DD",
  "amount": "金额（xx.xx元）",
  "number": "发票号码"
}
""",
		"invoice": """
请识别图片中的增值税发票内容，提取以下字段并返回标准 JSON 格式结果。

## 发票类型（type）：
- 能识别出具体类型请填写："增值税专用发票" 或 "增值税普通发票"
- 若无法判断具体类型，请填写："增值税发票"

## 需提取字段：
- type：发票类型
- company：购买方名称
- seller：销售方名称
- date：开票日期（格式为 "YYYY-MM-DD"）
- amount：请从图片中提取“价税合计”金额，格式为阿拉伯数字+元，例如："1234.56元"，不要保留中文大写。
- tax_rate：税率（例如："13%"）
- number：发票号码

请不要提取如下字段作为 amount：
- 不含税金额
- 合计金额
- 金额合计

只提取此字段：
- 价税合计

特别强调：
- number 请**从图中查找带“发票号码”字样的字段**，并提取其对应的数字编号。
- 必须是图像中真实存在的号码。
- 若未能识别，请将 number 设置为 null。

## 输出格式（请严格参考结构，仅字段值不同）：
```json
{
  "type": "增值税普通发票",
  "company": "示例公司名称",
  "seller": "示例销售方",
  "date": "2024-08-01",
  "amount": "1234.56元",
  "tax_rate": "13%",
  "number": "发票中的真实号码"
}
""",
		"bankreceipt": """
请识别图片中的银行回单内容，提取以下字段并返回标准 JSON 格式结果。

## 需提取字段：
- amount：从图片中提取实际金额，格式为“数字+元”，例如："2500.00元"。不得使用示例值“1234.56元”，不得保留人民币符号（如“￥”或“¥”），不得使用中文大写金额。
- date：从图片中提取带有“记账日期”字样的字段内容，格式必须为“YYYY-MM-DD”。

## 其他说明：
- 提取内容必须是图片中真实存在的信息，禁止虚构示例值。
- 固定字段 type，值为："银行回单(嘉源)"

## 输出格式（字段结构必须一致，仅字段值不同）：
```json
{
  "type": "银行回单(嘉源)",
  "date": "2024-08-01",
  "amount": "2500.00元"
}
		""",
		"highwayToll": """
请识别图片中的过路费发票内容，提取以下字段并返回标准 JSON 格式结果。

## 发票类型（type）：
- 请填写："过路费"

## 需提取字段说明：
- type：发票类型
- licensePlate：车牌号
- date：开票日期（格式为 "YYYY-MM-DD"），在图片右上角
- entryTime：通行时间起（格式为 "YYYY-MM-DD"）
- exitTime：通行时间止（格式为 "YYYY-MM-DD"）
- price: **请严格提取“金额”字段的金额**。格式为阿拉伯数字+元，例如："1234.56元"，不保留中文大写。
- tax_amount: **请严格提取“税额”字段的金额**。格式为阿拉伯数字+元，例如："1234.56元"，不保留中文大写。
- amount：**请严格提取“价税合计”字段的金额，不要提取“合计金额”“不含税金额”或“金额合计”字段**。格式为阿拉伯数字+元，例如："1234.56元"，不保留中文大写。
- tax_rate：税率（例如："13%"），如果是不征税，返回不征税而不是0%
- number：提取图片右上角靠上的“发票号码”字段，注意是8位纯数字，通常位于“发票代码”正下方，不是密码区或检验码中的数字，也不是纳税人识别号


## 输出格式（请严格参考结构，仅字段值不同）：
```json
{
"type": "过路费",
"licensePlate": "车牌号",
"date": "2024-07-01",
"entryTime": "2024-06-20",
"exitTime": "2024-06-20",
"price": "3.27元",
"tax_amount": "0.1元",
"amount": "3.37元",
"tax_rate": "3%",
"number": "发票号码(8位纯数字)"
}
"""
	}

	return base_intro + prompts.get(invoice_type, "错误：未知的发票类型")


@keyword("Invoice Recognitions")
def invoice_recognitions(
    path: str,
    invoice_type: str = "invoice",
    node_id: str = "",
    history_id: str = "",
    task_id: str = "",
) -> list[dict]:
    def image_path_to_base64(image_path: str) -> str:
        try:
            with open(image_path, "rb") as img_file:
                encoded = base64.b64encode(img_file.read()).decode("utf-8")
                ext = os.path.splitext(image_path)[-1].lower()
                mime_type = "image/jpeg" if ext in [".jpg", ".jpeg"] else "image/png"
                return f"data:{mime_type};base64,{encoded}"
        except Exception as e:
            return f"image_path_to_base64 error: {str(e)}"

    def extract_flexible_json(text: str) -> dict:
        try:
            match = re.search(r"```json\s*([\s\S]*?)\s*```", text)
            if match:
                return json.loads(match.group(1).strip())
            return json.loads(text.strip())
        except json.JSONDecodeError as e:
            return {"错误": f"解析失败，不是合法 JSON: {str(e)}"}

    file_paths = []
    if os.path.isdir(path):
        file_paths = [
            os.path.join(path, fname)
            for fname in os.listdir(path)
            if fname.lower().endswith((".pdf", ".jpg", ".jpeg", ".png"))
        ]
    elif os.path.isfile(path):
        file_paths = [path]
    else:
        return [{"错误": f"输入路径无效: {path}"}]

    results = []

    for file_path in file_paths:
        try:
            ext = os.path.splitext(file_path)[-1].lower()

            if ext in [".jpg", ".jpeg", ".png"]:
                img_base64 = image_path_to_base64(file_path)
            elif ext == ".pdf":
                doc = fitz.open(file_path)
                page = doc.load_page(0)
                pix = page.get_pixmap(dpi=200)
                img_bytes = pix.tobytes("png")
                img_base64 = f"data:image/png;base64,{base64.b64encode(img_bytes).decode('utf-8')}"
                doc.close()
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")

            # url = "http://10.80.20.22:5007/v1/chat/completions"
            url = f"{get_config_item(CV_URL)}/v1/chat/completions"
            headers = {
                "Authorization": "Bearer none",
                "Content-Type": "application/json",
            }

            system_prompt = invoice_type_to_prompt(invoice_type)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": system_prompt},
                        {"type": "image_url", "image_url": {"url": img_base64}},
                    ],
                }
            ]

            payload = {
                "model": "qwen2-vl",
                "messages": messages,
                "temperature": 0.5,
                "top_p": 0.8,
	            "seed": 2234
            }

            response = requests.post(url, headers=headers, json=payload, timeout=20)
            response.raise_for_status()
            result_text = (
                response.json()
                .get("choices", [{}])[0]
                .get("message", {})
                .get("content", "")
            )
            print(f"原始文本: {result_text}")

            parsed = extract_flexible_json(result_text)
            parsed["image_path"] = file_path
            parsed["invoice_type"] = invoice_type
            parsed["invoice_dict"] = invoice_field_mapping[invoice_type]
            results.append(parsed)

            # 发送 websocket 消息
            ws_broadcast_proxy.broadcast_data({
                "type": "invoice_ocr",
                "history_id": history_id,
                "taskId": task_id,
                "nodeId": node_id,
                "state": "progress",
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "result": parsed,
	            "total": len(file_paths),
	            "invoice_type": invoice_type,
	            "invoice_dict": invoice_field_mapping[invoice_type]
            })

        except Exception as e:
            error_result = {"错误": f"识别过程异常: {str(e)}", "image_path": file_path}
            results.append(error_result)

            ws_broadcast_proxy.broadcast_data({
                "type": "invoice_ocr",
                "history_id": history_id,
                "taskId": task_id,
                "nodeId": node_id,
                "state": "progress",
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "result": error_result,
	            "total": len(file_paths),
	            "invoice_type": invoice_type,
	            "invoice_dict": invoice_field_mapping[invoice_type]
            })

    # 发送最终结束消息
    ws_broadcast_proxy.broadcast_data({
        "type": "invoice_ocr",
        "history_id": history_id,
        "taskId": task_id,
        "nodeId": node_id,
        "state": "end",
        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "result": "",
	    "total": len(file_paths),
	    "invoice_type": invoice_type,
	    "invoice_dict": invoice_field_mapping[invoice_type]
	})

    return results


if __name__ == '__main__':
    test_file_paths = r"C:\Users\<USER>\Desktop\嘉源发票示例\车辆过路费发票"
    test_invoice_type = "highwayToll"
    result = invoice_recognitions(test_file_paths, test_invoice_type)
    print(result)