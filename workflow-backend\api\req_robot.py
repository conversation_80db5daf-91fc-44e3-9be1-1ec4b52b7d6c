from pydantic import BaseModel

from executor.task_executor import TaskExecutor, ExecuteParam
from scheduler.task_runner import task_runner
from scheduler.task_scheduler import TaskScheduler
from models.workflow import WorkflowData, ExecutionResult
from typing import Optional
from fastapi import HTTPException
from loguru import logger  # 你的日志模块
import requests
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from datetime import datetime, timedelta
import asyncio
import json

from transpiler.workflow_transpiler import WorkflowTranspiler
from config import globals

from utils.wimtask_server_api import (
    take_task,
    first_upload_task_update,
    upload_task_update,
)


class ExecutionResponse(BaseModel):
    success: bool
    execution_id: str
    message: str
    result: Optional[ExecutionResult] = None
    error: Optional[str] = None


executor = TaskExecutor()
scheduler = TaskScheduler()


async def run_robot_code(
    robot_code: str,
    authorization: str,
    task_id: str,
    options: Optional[dict] = None,
) -> ExecutionResponse:
    """执行Robot Framework代码并返回执行结果"""
    if not executor:
        raise HTTPException(status_code=500, detail="执行引擎未初始化")

    try:
        logger.info("开始执行Robot Framework代码")
        logger.info(f"Robot代码长度: {len(robot_code)} 字符")
        execute_param = ExecuteParam(
            robot_code=robot_code, task_id=task_id, token=authorization, options={}
        )
        execution_id = await executor.execute_in_queue(execute_param)

        return ExecutionResponse(
            success=True, execution_id=execution_id, message="", error="正则"
        )

    except Exception as e:
        logger.error(f"执行失败: {e}")
        logger.exception("异常详情:")
        return ExecutionResponse(
            success=False, execution_id="", message="执行过程中发生异常", error=str(e)
        )


class TaskSchedulerManager:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()
        self.job_map = {}  # task_id -> job 对象
        self.task_meta = (
            {}
        )  # task_id -> {'param': str, 'robot_code': str, 'authorization': str}

    async def _task_wrapper(self, task_id):
        meta = self.task_meta.get(task_id)
        if not meta:
            print(f"[{datetime.now()}] 任务 {task_id} 的元信息未找到，跳过执行")
            return

        workflow_json = meta["workflow_json"]
        authorization = meta.get("authorization")
        now_ts = int(datetime.now().timestamp() * 1000)

        try:
            job = self.scheduler.get_job(task_id)
            if job and authorization:
                next_time = int(job.next_run_time.timestamp() * 1000)
                upload_result = upload_task_update(
		            authorization=authorization,
		            task_id=task_id,
		            next_start_time=next_time
	            )
                logger.info(f"[预上传] 任务 {task_id} 执行前信息已上传，结果: {upload_result}")
        except Exception as e:
            logger.info(f"[预上传] 任务 {task_id} 上传失败: {e}")

        print(f"[{datetime.now()}] 执行任务 {task_id}")
        logger.info("[{}] 执行任务 {}".format(datetime.now(), task_id))

        try:
            content = json.loads(workflow_json)
            workflowdata = WorkflowData(
                nodes=content["nodes"],
                edges=content["edges"],
                viewport=content.get("viewport", None),
                metadata=content.get("metadata", None),
                variables=content.get("variables", None),
            )
            transpiler = WorkflowTranspiler()
            robot_code = await transpiler.transpile(workflowdata, task_id)
            # result = await run_robot_code(robot_code, authorization, task_id)

            param = ExecuteParam(
                robot_code=robot_code, task_id=task_id, token=authorization, options={}
            )

            result = await task_runner.submit(param)

            print(
                f"任务 {task_id} 执行完成，成功：{result.get('success','')}, 错误：{result.get('error','')}"
            )
        except Exception as e:
            # 打印具体异常堆栈信息
            import traceback

            logger.error(f"任务 {task_id} 执行异常: {traceback.format_exc()}")

        try:
            job = self.scheduler.get_job(task_id)
            if job and authorization:
                next_time = int(job.next_run_time.timestamp() * 1000)
                upload_result = upload_task_update(
				    authorization=authorization,
				    task_id=task_id,
				    next_start_time=next_time,
		            last_execute_time=now_ts
			    )
                logger.info(f"任务 {task_id} 执行信息已上传，结果: {upload_result}")
            elif not authorization:
                logger.info(f"任务 {task_id} 未上传（未提供 authorization）")
        except Exception as e:
            logger.info(f"上传任务 {task_id} 信息失败：{e}")

    async def run_task(self, task_id):
        await self._task_wrapper(task_id)

    def _build_task_func(self, task_id):
        def sync_wrapper():
            asyncio.run(self._task_wrapper(task_id))

        return sync_wrapper

    def _get_trigger(self, type_code, value, start_dt=None, end_dt=None):
        if type_code == 1:  # 分钟级间隔
            if not start_dt:
                raise ValueError("分钟级任务必须提供 start_time")

            # 锚定 start_dt，不用当前时间
            floored_start = start_dt.replace(second=0, microsecond=0)

            return IntervalTrigger(
                minutes=value, start_date=floored_start, end_date=end_dt
            )

        elif type_code == 2:  # 小时级间隔
            if not start_dt:
                raise ValueError("小时级任务必须提供 start_time")

            floored_start = start_dt.replace(second=0, microsecond=0)

            return IntervalTrigger(
                hours=value, start_date=floored_start, end_date=end_dt
            )

        elif type_code == 3:  # 天级间隔
            if not start_dt:
                raise ValueError("天级任务必须提供 start_time")

            floored_start = start_dt.replace(second=0, microsecond=0)

            return IntervalTrigger(
                days=value, start_date=floored_start, end_date=end_dt
            )

        elif type_code == 4:  # 星期
            if not start_dt:
                raise ValueError("星期任务必须提供 start_time")

            weekday = start_dt.weekday()  # 0=Monday
            weekday_map = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]

            return CronTrigger(
                day_of_week=weekday_map[weekday],
                hour=start_dt.hour,
                minute=start_dt.minute,
                end_date=end_dt,
            )

        elif type_code == 5:
            run_time = datetime.fromtimestamp(value / 1000)
            return DateTrigger(run_date=run_time)

        else:
            raise ValueError("未知调度类型")

    def add_or_update_task(
        self,
        task_id: str,
        param: str,
        workflow_json,
        authorization: str = None,
        start_time=None,
        end_time=None,
    ):
        try:
            now = datetime.now()
            start_dt = datetime.fromtimestamp(start_time / 1000) if start_time else None
            end_dt = datetime.fromtimestamp(end_time / 1000) if end_time else None
            if end_dt and now >= end_dt:
                print(f"任务 {task_id} 未添加，因为当前时间已超过结束时间")
                return

            type_str, value_str = param.split(",")
            type_code = int(type_str)
            value = int(value_str)

            if self.scheduler.get_job(task_id):
                self.scheduler.remove_job(task_id)
                print(f"任务 {task_id} 已存在，已更新")

            trigger = self._get_trigger(type_code, value, start_dt, end_dt)
            job = self.scheduler.add_job(
                self._build_task_func(task_id), trigger, id=task_id
            )

            self.job_map[task_id] = job
            self.task_meta[task_id] = {
                "param": param,
                "workflow_json": workflow_json,
                "authorization": authorization,
                "start_time": start_dt,
                "end_time": end_dt,
            }

            print(f"任务 {task_id} 添加成功，下次执行时间：{job.next_run_time}")
            if authorization is None:
                authorization = globals.token

            if job.next_run_time and authorization:
                next_time = int(job.next_run_time.timestamp() * 1000)
                upload_result = first_upload_task_update(
                    authorization, task_id, next_time
                )
                print(
                    f"[上传] 任务 {task_id} 的下一次执行时间已上传，结果: {upload_result}"
                )
            elif not authorization:
                print(f"[上传] 任务 {task_id} 未上传（未提供 authorization）")

        except Exception as e:
            print(f"[{task_id}] 添加失败: {e}")

    def remove_task(self, task_id: str):
        if self.scheduler.get_job(task_id):
            self.scheduler.remove_job(task_id)
            self.job_map.pop(task_id, None)
            self.task_meta.pop(task_id, None)
            print(f"任务 {task_id} 已删除")
        else:
            print(f"任务 {task_id} 不存在")

    def init_tasks(self, task_list: list[dict]):
        """
        task_list 示例：
        [
            {"id": "t1", "timeScheduled": "1,5", "robotCode": "print('a')", "authorization": "Bearer abc"},
            {"id": "t2", "timeScheduled": "5,1750386763000", "robotCode": "print('b')", "authorization": "Bearer def"},
        ]
        """
        for task in task_list:
            self.add_or_update_task(
                task_id=task["id"],
                param=task["timeScheduled"],
                workflow_json=task["configContent"],
                authorization=task.get("authorization"),
                start_time=task.get("missionStartTime"),
                end_time=task.get("missionEndTime", None),
            )

    def get_task_status(self, task_id: str) -> dict:
        job = self.scheduler.get_job(task_id)
        meta = self.task_meta.get(task_id)

        return {
            "task_id": task_id,
            "exists": job is not None,
            "next_run_time": job.next_run_time if job else None,
            "param": meta["param"] if meta else None,
            "robot_code": meta["robot_code"] if meta else None,
            "authorization": meta["authorization"] if meta else None,
        }

    def get_all_jobs(self):
        return self.scheduler.get_jobs()
