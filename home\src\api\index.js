import { request } from '@/utils/axios';
import utils from '@/utils/utils';

const saasApi = {
    //客户申请更新
    AIAgentWimtaskCustomerApplicationUpdate(params) {
        return request.post('/wimai/api/task/customerApplication/update', params);
    },
    //客户申请查询
    AIAgentWimtaskCustomerApplicationQuery(params) {
        return request.post('/wimai/api/task/customerApplication/query', params);
    },
    //客户申请新增
    AIAgentWimtaskCustomerApplicationInsert(params) {
        return request.post('/wimai/api/task/customerApplication/insert', params);
    },
    // 智能体后台指令查询接口
    AIAgentWimtaskCommandQuery(params) {
        return request.post('/wimai/api/task/authTarget/queryAuthCommand', params);
    },
    // 智能体后台分类管理查询接口
    AIAgentResourceCategoryQuery(params) {
        return request.post('/wimai/api/task/resource/category/query', params);
    },
    // 智能体后台栏目管理查询接口
    AIAgentLearningCategoryQuery(params) {
        return request.post('/wimai/api/task/learningCategory/query', params);
    },
    // 智能体后台内容管理查询接口
    AIAgentLearningContentQuery(params) {
        return request.post('/wimai/api/task/learningContent/query', params);
    },
    // 智能体后台模板管理及服务包管理查询接口
    AIAgentTaskTemplateQuery(params) {
        return request.post('/wimai/api/task/template/queryIg', params);
    },
    // 智能体后台模板管理及服务包管理查询接口
    AIAgentTaskTemplateQueryAuth(params) {
        return request.post('/wimai/api/task/authTarget/queryAuthTaskTemplate', params);
    },
    //服务包市场分类数量
    AIAgentTaskTemplateCountCategoryMainIg(params) {
        return request.post('/wimai/api/task/template/countCategoryMainIg', params);
    },
    //服务包市场分类列表
    AIAgentTaskCategoryQueryIg(params) {
        return request.post('/wimai/api/task/resource/category/queryIg', params);
    },
};
export default saasApi;
