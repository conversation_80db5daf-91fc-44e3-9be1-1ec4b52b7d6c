<template>
  <div class="qrCode-page">
    <div class="qrCode-head">
      <div class="title">二维码登录</div>
      <img 
        src="../../assets/images/login/qrCode.png" 
        alt="" 
        @click="loginChange('1-1')" 
      />
    </div>
    <div class="pwd-content">
      <div class="qr-box">
        <div 
          class="qr-code" 
          v-loading="loading" 
          @mouseenter="onShowRefresh" 
          @mouseleave="showRefresh = false"
        >
          <!-- 二维码 -->
          <vue-qr 
            v-if="websocket.data" 
            :text="websocket.data" 
            :size="220"
          ></vue-qr>
          
          <!-- 错误信息 -->
          <div class="qr-code-error" v-if="websocket.error">
            <div class="message-icon">
              <i class="iconfont icon-gantanhao"></i>
            </div>
            {{ websocket.error }}<span @click="initSocketEvent()">点击刷新</span>
          </div>

          <!-- 刷新二维码 -->
          <div 
            class="qr-code-error refresh-part" 
            v-if="showRefresh" 
            @click="initSocketEvent()"
          >
            <img src="../../assets/images/login/refresh.png" alt="" />
            <span>点击刷新</span>
          </div>
          
          <!-- 扫码成功 -->
          <div class="qr-code-error" v-if="showSuccess">
            <div class="message-icon success">
              <i class="iconfont icon-shuaxin"></i>
            </div>
            扫码成功！
          </div>
        </div>
      </div>
    </div>
    <div class="register-item">
      <span>没有账号，</span>
      <span class="register-btn" @click="loginChange(-1)">注册</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import utils from '@/utils/utils';
import systemApi from "@/api/system";
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import { useUserStore } from "@/stores/user";
import { useLoginStore } from "@/stores/login";

const userStore = useUserStore()
const loginStore = useLoginStore()


// 定义WebSocket消息类型
interface WebSocketMessage {
  type: 'uid' | 'token' | string;
  data: string;
}

// 定义登录响应数据类型
interface LoginResponse {
  token: string;
  expire: number;
  refreshToken: string;
  tenantId: string;
}

// 定义扫码接口响应类型
interface ScanQrcodeResponse {
  Success: boolean;
  Response?: LoginResponse;
}

// 接收Props
interface Props {
  configs: Record<string, any>;
  cloud: string;
  loginPop: boolean;
}

// 使用 withDefaults 设置默认值，类型更严格
const props = withDefaults(defineProps<Props>(), {
  configs: () => ({}), // 对象默认值必须用函数返回
  cloud: '',
  loginPop: false
});

// 定义Emits
const emit = defineEmits<{
  (e: 'onSwitchLoginType', type: string | number): void;
}>();

// 状态管理
const loading = ref<boolean>(false);
const showRefresh = ref<boolean>(false);
const showSuccess = ref<boolean>(false);
const uid = ref<string>('');
let scanCodeInterval: number | null = null;
let timer1: NodeJS.Timeout | null = null;
let timer2: NodeJS.Timeout | null = null;
let timer3: NodeJS.Timeout | null = null;
let timer4: NodeJS.Timeout | null = null;

// WebSocket状态管理
const websocket = reactive({
  init: null as WebSocket | null,
  error: null as string | null,
  data: '' as string
});

// 显示刷新按钮
const onShowRefresh = () => {
  if (websocket.error || showSuccess.value) return;
  showRefresh.value = true;
};

// 初始化WebSocket连接
const initSocketEvent = () => {
  loading.value = true;
  websocket.error = null;
  
  // 关闭现有连接
  if (websocket.init) {
    websocket.init.close();
  }
  
  // 清除现有定时器
  if (scanCodeInterval) {
    clearInterval(scanCodeInterval);
    scanCodeInterval = null;
  }

  // 重置状态
  timer1 = setTimeout(() => {
    showRefresh.value = false;
    showSuccess.value = false;
  }, 100);

  timer2 = setTimeout(() => {
    showRefresh.value = false;
    showSuccess.value = false;
    
    if (typeof WebSocket === 'undefined') return;

    const protocol = window.location.protocol;
    const wsProtocol = protocol === 'http:' ? 'ws' : 'wss';
    ;
    const host = import.meta.env.MODE === 'production' 
      ? 'www.dlmeasure.com' 
      : 'www.dlmeasure.com';
      
    // 创建新连接
    websocket.init = new WebSocket(`${wsProtocol}://${host}/uniwim/ump/qrcodeLogin`);

    // 连接打开
    websocket.init.onopen = () => {
      websocket.error = null;
      loading.value = false;
      getScanCodeData();
    };

    // 连接错误
    websocket.init.onerror = () => {
      websocket.error = '二维码获取失败';
      loading.value = false;
    };

    // 连接关闭
    websocket.init.onclose = () => {
      websocket.init?.close();
      timer3 = setTimeout(() => {
        loading.value = false;
      }, 300);
    };

    // 接收消息
    websocket.init.onmessage = (msg) => {
      const data = msg.data;
      if (data === '{}' || !data) return;

      try {
        const parsedData: WebSocketMessage = JSON.parse(data);
        if (parsedData.type === 'uid') {
          websocket.data = JSON.stringify(parsedData);
          websocket.error = null;
          uid.value = parsedData.data;
        }
        if (parsedData.type === 'token') {
          showSuccess.value = true;
          timer4 = setTimeout(() => {
            loginNext(parsedData.data as unknown as LoginResponse);
          }, 1500);
        }
      } catch (e) {
        websocket.error = '二维码获取失败';
      }
    };
  }, 500);
};

// 定时获取扫码结果
const getScanCodeData = () => {
  if (scanCodeInterval) {
    clearInterval(scanCodeInterval);
  }

  scanCodeInterval = window.setInterval(async () => {
    try {
      const res: ScanQrcodeResponse = await systemApi.getScanQrcodeInterface({ 
        uuid: uid.value 
      });
      
      if (res.Success && res.Response) {
        if (scanCodeInterval) {
          clearInterval(scanCodeInterval);
          scanCodeInterval = null;
        }
        loginNext(res.Response);
      }
    } catch (err) {
      // 静默处理错误
    }
  }, 2500);
};

// 登录后续处理
const loginNext = async(data: LoginResponse) => {
  utils.setLocalStorageInfo(
    data.token, 
    data.expire, 
    data.refreshToken, 
    data.tenantId
  );

  if (props.loginPop) {
    // 假设store有对应的action
    // store.dispatch('UPDTAINFO');
    let userInfo = await systemApi.initUserInfo();
    ;
    if(userInfo){
      userStore.setUserInfo(userInfo)
      loginStore.LOGIN_POP_VISIBLE(false)
    }
  } else {
    location.replace(`${import.meta.env.BASE_URL}index.html`);
  }
};

// 切换登录类型
const loginChange = (type: string | number) => {
  emit('onSwitchLoginType', type);
};

// 组件挂载时初始化
onMounted(() => {
  loading.value = true;
  initSocketEvent();
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 关闭WebSocket连接
  websocket.init?.close();
  
  // 清除所有定时器
  if (scanCodeInterval) {
    clearInterval(scanCodeInterval);
    scanCodeInterval = null;
  }
  [timer1, timer2, timer3, timer4].forEach(timer => {
    if (timer) {
      clearTimeout(timer);
    }
  });
});
</script>

<style scoped lang="less">
.qrCode-page {
  width: 400px;
  height: 525px;
  position: relative;
  background: transparent;
  
  .qrCode-head {
    padding: 35px 8px 53px 32px;
    position: relative;
    
    .title {
      font-weight: 400;
      font-size: 24px;
      color: #333333;
    }
    
    img {
      width: 60px;
      height: 60px;
      position: absolute;
      right: 8px;
      top: 8px;
      cursor: pointer;
    }
  }
  
  .pwd-content {
    display: flex;
    justify-content: center;
    
    .qr-code {
      width: 262px;
      height: 274px;
      text-align: center;
      position: relative;
      background: linear-gradient(#0054d2, #0054d2) left top, 
                  linear-gradient(#0054d2, #0054d2) left top, 
                  linear-gradient(#0054d2, #0054d2) right top, 
                  linear-gradient(#0054d2, #0054d2) right top, 
                  linear-gradient(#0054d2, #0054d2) left bottom, 
                  linear-gradient(#0054d2, #0054d2) left bottom, 
                  linear-gradient(#0054d2, #0054d2) right bottom, 
                  linear-gradient(#0054d2, #0054d2) right bottom;
      background-repeat: no-repeat;
      background-size: 1.2px 16px, 16px 1.2px;
      padding: 20px 0px;
      box-sizing: border-box;
      
      img {
        width: 222px;
        height: 222px;
        box-shadow: 0 0 10px 0 #20406f1a;
      }
      
      .qr-code-error {
        position: absolute;
        width: 262px;
        height: 274px;
        top: 0px;
        left: 0px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.9);
        
        span {
          cursor: pointer;
          font-weight: 400;
          font-size: 19px;
          color: #000000;
          letter-spacing: 0;
        }
        
        img {
          width: 50px;
          height: 50px;
          margin-bottom: 20px;
        }
      }
      
      .refresh-part {
        cursor: pointer;
      }
      
      .message-icon {
        width: 32px;
        height: 32px;
        background: #ffa90c;
        text-align: center;
        line-height: 32px;
        border-radius: 16px;
        color: #fff;
        margin-bottom: 4px;
        
        &.success {
          background: #09d95f;
        }
        
        .iconfont {
          font-size: 18px;
        }
      }
    }
  }
  
  .register-item {
    position: absolute;
    bottom: 30px;
    right: 28px;
    
    span {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      letter-spacing: 0;
      line-height: 17px;
    }
    
    .register-btn {
      cursor: pointer;
      color: #0054d2;
    }
  }
}
</style>