import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import {VantResolver} from '@vant/auto-import-resolver';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import {fileURLToPath, URL} from "node:url";
import {resolve} from "path";
import { visualizer } from 'rollup-plugin-visualizer';


export default defineConfig({
  build: {
    outDir: 'WimTaskApp', // 指定输出目录为WimTask
    emptyOutDir: true,//每次打包清空输出目录
    assetsDir: 'assets', // 静态资源目录
    minify: 'terser', // 使用terser进行代码压缩
    rollupOptions: {
      input: resolve(__dirname, 'index.html'),
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js', // 代码分割后的文件名格式
        entryFileNames: 'assets/js/[name]-[hash].js', // 入口文件名格式
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]', // 静态资源文件名格式
        manualChunks: {
          vue: ['vue', 'vue-router'],
          vant: ['vant'],
          element: ['element-plus'],
          vendor: ['axios', 'lodash'],
          xlsx: ['xlsx'],
          zrender: ['zrender'],
          echarts: ['echarts'],
        }
      }
    },
    // 设置图片转 Base64 的大小阈值
    assetsInlineLimit: 4096, // 单位为字节，这里设置为 4KB，小于 4KB 的图片会转为 Base64
  },
  base: './',
  plugins: [
    vue(),
    vueDevTools(),
    AutoImport({
      resolvers: [
        VantResolver(),
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
    Components({
      resolvers: [
        VantResolver(),
        ElementPlusResolver({
          importStyle: 'sass',
        })
      ],
    }),
    // visualizer({
    //   open: true,
    //   gzipSize: true,
    //   brotliSize: true,
    // })
  ],
  server: {
    proxy: {
      '^/uniwim': {
        target: 'http://*************:31830',
        changeOrigin: true
      },
      '^/wimai': {
        target: 'http://*************:31830',
        changeOrigin: true
      },
      '^/oneMap3D': {
        target: 'https://www.dlmeasure.com/extends',
        changeOrigin: true
      },
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
