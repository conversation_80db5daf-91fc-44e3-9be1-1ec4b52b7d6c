<template>
  <div class="box" v-loading="loading">
    <div class="view-box">
      <div class="view-left-view" :class="{ 'width0': !showLeftview }">
        <div class="left-view-arrow">
          <el-icon v-if="showLeftview" @click="showLeftview = !showLeftview"><CaretLeft /></el-icon>
          <el-icon v-else @click="showLeftview = !showLeftview"><CaretRight /></el-icon>
        </div>
        <div class="left-view-header">
          任务列表
        </div>
        <div class="left-view-list" ref="leftList" v-infinite-scroll="loadTaskList">
          <div class="left-view-list-item" :ref="`leftListItem${index}`" :class="{ 'active': item.id == activeId, 'disabled': isExc }" @click="taskListClick(item)" v-for="(item, index) in leftTaskList" :key="item.id">
            <div class="list-item-title">{{ item.missionName }}</div>
            <div class="list-item-tag-node">
              <el-tag type="warning" round v-if="item.taskState == 'running'">
                <el-tooltip content="任务已到开始时间未超过结束时间" placement="top">执行中</el-tooltip>
              </el-tag>
              <el-tag type="info" round v-else-if="item.taskState == 'unable'">
                <el-tooltip content="任务停用" placement="top">已停用</el-tooltip>
              </el-tag>
              <el-tag type="info" round v-else-if="item.taskState == 'end'">
                <el-tooltip content="任务超过结束时间" placement="top">已结束</el-tooltip>
              </el-tag>
              <el-tag type="success" round v-else-if="item.taskState == 'wait'">
                <el-tooltip content="任务未到开始时间" placement="top">待执行</el-tooltip>
              </el-tag>
              <span class="list-item-tag-node-text" v-if="item.nodeNum">{{ item.nodeNum }}个节点</span>
            </div>
            <div class="list-item-nextTime">
              <span class="list-item-nextTime-icon"><el-icon><Clock /></el-icon></span>
              <span class="list-item-nextTime-text">下次运行：</span>
              <span class="list-item-nextTime-time">{{ item.nextStartTime ? formatTime(item.nextStartTime, 'yyyy-MM-DD HH:mm') : '-' }}</span>
            </div>
            <div class="list-item-tooltip" v-if="item.taskState !== 'unable' && item.taskState !== 'end' && item.nextStartTime">
              <span>{{ handleLeftNextText(item) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="view-table">
        <div class="view-table-box">
          <div class="view-table-header">
            <div class="header-title">
              <span>运行监控</span>
              <span v-if="currentTask?.missionName"> - {{ currentTask.missionName }}</span>
            </div>
            <div class="header-tools">
              <div class="header-tools-date" v-if="!isExc">
                <span>历史执行</span>
                <el-select v-model="historyExcId" placeholder="请选择执行时间" style="width: 180px;" @change="changeHistoryExcId">
                  <el-option v-for="item in historyExcData" :label="formatTime(item.executeTime, 'YYYY-MM-DD HH:mm')" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </div>
              <div class="header-tools-drop">
                <!-- trigger="click" -->
                <el-dropdown placement="bottom-end" size="small" >
                  <el-button>
                    <span class="header-tools-drop-text">
                      <span style="margin-right: 5px;">导出</span>
                      <el-icon><CaretBottom /></el-icon>
                    </span>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="header-tools-drop-menu">
                      <el-dropdown-item>
                        <i class="action-iconfont icon-wenjiandaochu header-tools-drop-iconfont"></i>
                        <span class="header-tools-drop-text">选择导出</span>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <i class="action-iconfont icon-quanbudaochu header-tools-drop-iconfont"></i>
                        <span class="header-tools-drop-text">全部导出</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
          <el-scrollbar ref="scrollbar" style="height: calc(100% - 56px); width: 100%" wrap-style="overflow-x:hidden;">
            <template v-for="(key, keyIndex) in dataListKeys">
              <div class="view-table-card-box" v-if="showNodeContent(key)" :key="'card-box'+keyIndex">
                <div class="view-table-card-box-title">
                  <div class="card-title-left">
                    <div class="card-box-title-box" :style="{ 'background': handleNodesIcon('color', key) }">
                      <el-icon>
                        <i class="action-iconfont" :class="handleNodesIcon('icon', key)"></i>
                      </el-icon>
                    </div>
                    <span>{{ dictName[key] }}</span>
                  </div>
                  <div class="card-title-right">
                    <span>{{ dictTime[key] }}</span>
                  </div>
                </div>
                <div class="view-table-cards-title" v-if="dataTypeForId[key] === 'invoice_recognition'">
                  <div class="cards-title-item">
                    <div class="cards-title-item-label">
                      <span>识别总数：</span>
                    </div>
                    <div class="cards-title-item-value">
                      {{ dataList[key][0]?.total || dataList[key].length }}
                    </div>
                  </div>
                  <div class="cards-title-item">
                    <div class="cards-title-item-label">
                      <span>成功：</span>
                    </div>
                    <div class="cards-title-item-value success">
                      {{ dataList[key].filter(item => item.type)?.length }}
                    </div>
                  </div>
                  <div class="cards-title-item">
                    <div class="cards-title-item-label">
                      <span>失败：</span>
                    </div>
                    <div class="cards-title-item-value fail">
                      {{ dataList[key].filter(item => !item.type)?.length }}
                    </div>
                  </div>

                </div>
                <div class="view-table-cards" v-if="dataTypeForId[key] === 'excel_create'">
                  <div class="view-table-card excel" style="height: 500px;" v-for="(item, index) in dataList[key]" :key="'card'+key+index">
                    <div class="table-card-content">
                      <file-preview :configId="templateExcel.configId" :missionId="activeId" :historyId="item.historyId" :config="templateExcel.config"></file-preview>
                    </div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'data_forecast'">
                  <div class="left-table">
                    <el-table class="mask-table" :data="dataList[key][0].tableData" border :show-overflow-tooltip="true" :highlight-current-row="true"
                      style="width: 100%;height: 100%;">
                      <el-table-column type="index" label="序号" align="center" width="80" />
                      <el-table-column v-for="it in dataList[key][0].tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.data == 'time'" #default="{ row }">
                          {{ formatTime(row.time, 'MM-DD HH:mm') }}
                        </template>
                        <template v-else-if="it.data == 'value'" #default="{ row }">
                          {{ row.value }}
                        </template>
                      </el-table-column>
                      <template #empty>
                        <el-empty description="暂无数据" style="height: 10vh;" />
                      </template>
                    </el-table>
                  </div>
                  <div class="right-echarts">
                    <div class="right-table-text">
                      预测平均值
                      <span class="right-table-text-bold">{{ dataList[key][0].forecast_avg }}</span>
                      预测累计值
                      <span class="right-table-text-bold">{{ dataList[key][0].forecast_sum }}</span>
                      预测最大值
                      <span class="right-table-text-bold">{{ dataList[key][0].forecast_max }}</span>
                      预测最小值
                      <span class="right-table-text-bold">{{ dataList[key][0].forecast_min }}</span>
                    </div>
                    <div :ref="key+'echarts'" class="echarts-box">
                      {{ initCharts(key, dataList[key][0]) }}
                    </div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'water_shutoff_valve'">
                  <map-frame :nodes="nodes" :config="dataList[key][0]"/>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'http_post' || dataTypeForId[key] === 'http_get' || dataTypeForId[key] === 'http_request'">
                  <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card'+key+index">
                    <div class="table-card-content-title">
                      {{ item.desc }}
                    </div>
                    <div class="table-card-content http" v-if="handleHttpType(item.result) === 'object'">
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <el-input
                          type="textarea"
                          :model-value="handleHttpJSON(item.result)"
                          :rows="6"
                          :readonly="true"
                        />
                      </el-scrollbar>
                    </div>
                    <div class="table-card-content http-table" v-else-if="handleHttpType(item.result) === 'array'">
                      <el-table class="mask-table" ref="httpRef" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                        <el-table-column type="index" label="序号" align="center" width="80" />
                        <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed" :render-header="renderHeader">
                          <template v-if="it.dType == 'date'" #default="{ row }">
                            {{ formatTime(row[it.data]*1000, 'MM-DD HH:mm') }}
                          </template>
                          <template v-else-if="it.data == 'value'" #default="{ row }">
                            {{ row.value }}
                          </template>
                          <template v-else #default="{ row }">
                            <el-link type="primary" v-if="handleHttpType(row[it.data]) === 'array'" @click="openTableDialog(row[it.data])">
                              <span style="font-size: 12px;">详情</span>
                            </el-link>
                            <el-link type="primary" v-else-if="handleHttpType(row[it.data]) === 'object'" @click="openTableDialog([row[it.data]])">
                              <span style="font-size: 12px;">详情</span>
                            </el-link>
                            <span v-else>{{ handleHttpType(row[it.data]) === 'array' || handleHttpType(row[it.data]) === 'object' ? '' : row[it.data] }}</span>
                          </template>
                        </el-table-column>
                        <template #empty>
                          <el-empty description="暂无数据" style="height: 40vh;" />
                        </template>
                      </el-table>
                    </div>
                    <div class="table-card-content http" v-else>
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <div class="table-card-content-text">
                          {{ item.result }}
                        </div>
                      </el-scrollbar>
                    </div>
                  </div>
                </div>
                <div class="view-table-cards flexStart" v-else-if="dataTypeForId[key] === 'invoice_recognition'">
                  <div class="view-table-card card4" :class="{ 'train': invoiceClass('train', item), 'taxi': invoiceClass('taxi', item), 'addedTax': invoiceClass('addedTax', item), 'fail': !item.type }" v-for="(item, index) in dataList[key]" :key="'card'+key+index">
                    <div class="table-card-content">
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <div class="card-box" v-if="item.type">
                          <div class="view-table-card-title-box">
                            <!-- <div class="view-table-card-title-bg"></div> -->
                            <div class="view-table-card-title" :title="item.type">{{ item.type }}</div>
                            <div class="view-table-card-title-btn" @click="openFile(item, 'file')">查看原件</div>
                          </div>
                          <div class="view-table-card-baseInfo">
                            <template v-for="(it, i) in item.invoiceCard">
                              <div class="baseInfo-item" v-if="item[it.value]" :key="i">
                                <div class="baseInfo-item-label">
                                  <div class="baseInfo-item-label-icon"></div>
                                  <span>{{ (item.invoice_dict || []).find(key => key.value === it.value)?.name || it.value }}</span>
                                </div>
                                <div class="baseInfo-item-value" :title="item[it.value]">
                                  {{ item[it.value] }}
                                </div>
                              </div>
                            </template>
                          </div>
                          <div class="view-table-card-line" v-if="item.tax_rate || item.amount"></div>
                          <div class="view-table-card-baseInfo" style="minHeight: 24px;" v-if="item.tax_rate || item.amount">
                            <div class="baseInfo-item" v-if="item.tax_rate">
                              <div class="baseInfo-item-label">
                                <span>{{ (item.invoice_dict || []).find(key => key.value === 'tax_rate')?.name || 'tax_rate' }}</span>
                              </div>
                              <div class="baseInfo-item-value info" :title="item.tax_rate">
                                {{ item.tax_rate }}
                              </div>
                            </div>
                            <div class="baseInfo-item" v-if="item.amount">
                              <div class="baseInfo-item-label">
                                <span>{{ (item.invoice_dict || []).find(key => key.value === 'amount')?.name || 'amount' }}</span>
                              </div>
                              <div class="baseInfo-item-value info" :title="item.amount">
                                {{ item.amount }}
                              </div>
                            </div>
                          </div>
                          <div class="view-table-card-line" v-if="item.number"></div>
                          <div class="view-table-card-baseInfo" v-if="item.number">
                            <div class="baseInfo-item">
                              <div class="baseInfo-item-label">
                                <span>{{ (item.invoice_dict || []).find(key => key.value === 'number')?.name || 'number' }}</span>
                              </div>
                              <div class="baseInfo-item-value" :title="item.number">
                                {{ item.number }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="card-box" v-else>
                          <div class="view-table-card-title-box">
                            <div class="view-table-card-title">未识别</div>
                            <div class="view-table-card-title-btn" @click="openFile(item, 'file')">查看原件</div>
                          </div>
                          <div class="view-table-card-baseInfo info-box">
                            <el-icon class="info-icon"><WarningFilled /></el-icon>
                            <span class="info-text">图片模糊无法识别</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </div>
                  <!-- v-if="dataList[key].length !== (dataList[key][0]?.total || dataList[key].length)" -->
                  <div class="view-table-card card4 loading" style="min-height: 150px;" v-if="dataList[key].length !== (dataList[key][0]?.total || dataList[key].length)">
                    <div class="table-card-content">
                      <div class="loading-box">
                        <div class="is-loading">
                          <img src="@/assets/images/loading.gif" alt="">
                        </div>
                        <span>正在识别<span style="margin-left: 8px;">{{ `(${dataList[key].length+1}/${dataList[key][0]?.total || dataList[key].length})...` }}</span></span>
                        <el-progress class="loading-progress" :stroke-width="4" :percentage="handlePercentage(key)" color="#0054D2" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'img_recognition'">
                  <div class="view-table-card block image-card" v-for="(item, index) in dataList[key]" :key="index">
                    <div class="image-box">
                      <img v-for="(it, i) in item.url" :key="i" :src="it" alt="">
                    </div>
                    <div class="image-text">
                      <div class="image-text-title">识别结果：</div>
                      <div class="image-text-desc">
                        <div v-html="item.content"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'text_template'">
                  <div class="view-table-card block">
                    <div class="table-card-content table-card-content-scroll" style="height: auto;" v-html="dataList[key]"></div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'db_query'">
                  <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="index">
                    <div class="table-card-content http-table">
                      <el-table class="mask-table" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                        <el-table-column type="index" label="序号" align="center" width="80" />
                        <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                          <template v-if="it.dType == 'date'" #default="{ row }">
                            {{ formatTime(row[it.data]*1000, 'MM-DD HH:mm') }}
                          </template>
                          <template v-else-if="it.data == 'value'" #default="{ row }">
                            {{ row.value }}
                          </template>
                        </el-table-column>
                        <template #empty>
                          <el-empty description="暂无数据" style="height: 30vh;" />
                        </template>
                      </el-table>
                      <!-- <div class="view-table-pagination">
                        <el-pagination
                          v-model:current-page="item.pagination.page"
                          v-model:page-size="item.pagination.size"
                          :page-sizes="item.pageSizes"
                          size="default"
                          :disabled="false"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="item.pagination.total"
                          @size-change="handleSizeChange(item)"
                          @current-change="handleCurrentChange(item)"
                        />
                      </div> -->
                    </div>
                  </div>
                </div>
                <div class="view-table-cards" v-else-if="dataTypeForId[key] === 'ai_analyze'">
                  <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card'+key+index">
                    <div class="table-card-content">
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <markdownIt class="table-card-content-scroll" :content="handleMarkDown(item)"></markdownIt>
                      </el-scrollbar>
                    </div>
                  </div>
                </div>
                <!-- 与http类似 -->
                <div class="view-table-cards" v-else>
                  <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card'+key+index">
                    <div class="table-card-content-title">
                      {{ item.desc }}
                    </div>
                    <div class="table-card-content http" v-if="handleHttpType(item.result) === 'object'">
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <el-input
                          type="textarea"
                          :model-value="handleHttpJSON(item.result)"
                          :rows="6"
                          :readonly="true"
                        />
                      </el-scrollbar>
                    </div>
                    <div class="table-card-content http-table" v-else-if="handleHttpType(item.result) === 'array'">
                      <el-table class="mask-table" ref="httpRef" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                        <el-table-column type="index" label="序号" align="center" width="80" />
                        <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed" :render-header="renderHeader">
                          <template v-if="it.dType == 'date'" #default="{ row }">
                            {{ formatTime(row[it.data]*1000, 'MM-DD HH:mm') }}
                          </template>
                          <template v-else-if="it.data == 'value'" #default="{ row }">
                            {{ row.value }}
                          </template>
                          <template v-else #default="{ row }">
                            <el-link type="primary" v-if="handleHttpType(row[it.data]) === 'array'" @click="openTableDialog(row[it.data])">
                              <span style="font-size: 12px;">详情</span>
                            </el-link>
                            <el-link type="primary" v-else-if="handleHttpType(row[it.data]) === 'object'" @click="openTableDialog([row[it.data]])">
                              <span style="font-size: 12px;">详情</span>
                            </el-link>
                            <span v-else>{{ handleHttpType(row[it.data]) === 'array' || handleHttpType(row[it.data]) === 'object' ? '' : row[it.data] }}</span>
                          </template>
                        </el-table-column>
                        <template #empty>
                          <el-empty description="暂无数据" style="height: 40vh;" />
                        </template>
                      </el-table>
                    </div>
                    <div class="table-card-content http" v-else>
                      <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                        <div class="table-card-content-text">
                          {{ item.result }}
                        </div>
                      </el-scrollbar>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-scrollbar>
        </div>
      </div>
      <div class="view-right-view" :class="{ 'width0': !showRightview }">
        <div class="right-view-arrow">
          <el-icon v-if="showRightview" @click="showRightview = !showRightview"><CaretRight /></el-icon>
          <el-icon v-else @click="showRightview = !showRightview"><CaretLeft /></el-icon>
        </div>
        <div class="right-view-tab">
          <div class="tabs">
            <div class="tabs-item" :class="{ 'tabs-item-active': activeTab === 'executionRecord' }" @click="handleTabClick('executionRecord')">执行记录</div>
            <div class="tabs-item" :class="{ 'tabs-item-active': activeTab === 'executionResult' }" @click="handleTabClick('executionResult')">执行结果</div>
          </div>
          <div class="tabs-number">{{ motitorFileList.length }}个文件</div>
        </div>
        <div class="right-view-box" v-loading="listLoading">
          <el-scrollbar ref="rightScrollbar" style="height: calc(100% - 56px); width: 100%" wrap-style="overflow-x:hidden;">
            <div class="monitor-box" v-show="activeTab === 'executionRecord'">
              <el-timeline>
                <el-timeline-item v-for="(it, index) in motitorList" :key="index" timestamp="" placement="top"
                  :class="{
                    'error-tail': it.state === 'failed',
                    'loading-tail': it.state === 'loading',
                    'waiting-tail': it.state === 'waiting'}">
                  <template #dot>
                    <div class="time-icon-box">
                      <div class="time-icon" v-if="it.state === 'pass' || it.state === 'passed'">
                        <el-icon class="time-icon-select"><Select /></el-icon>
                        <el-icon v-if="it.state === 'failed'" class="time-icon-select"><Select /></el-icon>
                      </div>
                      <div class="time-icon error" v-else-if="it.state === 'failed'">
                        <el-icon class="time-icon-select"><Close /></el-icon>
                      </div>
                      <div class="time-icon loading" v-else-if="it.state === 'loading'">
                        <el-icon class="time-icon-select is-loading"><Refresh /></el-icon>
                      </div>
                      <div class="time-icon waiting" v-else>
                        <el-icon class="time-icon-select is-loading"><Loading /></el-icon>
                      </div>
                    </div>
                    <div class="time-box">
                      <div class="time-left">
                        <div class="time-time">{{ it.excTime }}</div>
                        <!-- 显示重试次数 -->
                        <div class="retry-count" v-if="it.failedCount > 1">
                          重试 {{ it.failedCount - 1 }} 次
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="time-content">
                    <div class="time-title-box">
                      <div class="time-title-box-left">
                        <div class="time-title-icon" :style="{ 'background': handleNodesIcon('color', it.nodeId) }">
                          <el-icon>
                            <i class="action-iconfont" :class="handleNodesIcon('icon', it.nodeId)"></i>
                          </el-icon>
                        </div>
                        <div class="time-title" :title="it.title">{{ it.title }}</div>
                      </div>
                      <div class="open-file" v-if="showOpenFile(it.nodeId)" @click="nodeOpenFile(it.nodeId)">打开文件</div>
                    </div>
                    <div class="content-item" v-if="handleContent(it)">
                      <div class="item-title">
                        <div class="complete-content" :title="handleContent(it)">
                          {{ handleContent(it) }}
                        </div>
                      </div>
                    </div>
                    <template v-if="it.nodeId !== 'start_node' && it.nodeId !== 'end_node'">
                      <div class="content-item flex-column code-content"
                           v-if="hasPuts(it.input)">
                        <div class="item-title font-bold" @click="toggleExpand(it, '_inputs')">
                          输入：
                          <div>
                            <el-icon v-if="expanedDict[it.historyId + it.nodeId + '_inputs']" title="收起">
                              <CaretTop />
                            </el-icon>
                            <el-icon v-else title="展开">
                              <CaretBottom />
                            </el-icon>
                          </div>
                        </div>
                        <div v-show="expanedDict[it.historyId + it.nodeId + '_inputs']" style="width: 100%;">
                          <json-viewer
                            :value="jsonViewParse(it.input)"
                            :copyable="{ copyText: '复制', copiedText: '已复制' }"
                            sort
                            boxed
                            show-double-quotes
                            class="my-awesome-json-theme"
                            theme="my-awesome-json-theme"
                          />
                        </div>
                      </div>
                      <div class="content-item flex-column code-content"
                        v-if="hasPuts(it.output) && it.state !== 'failed' && index < motitorList.length - 1">
                        <div class="item-title font-bold" @click="toggleExpand(it, '_outputs')">
                          输出：
                          <div>
                            <el-icon v-if="expanedDict[it.historyId + it.nodeId + '_outputs']" title="收起">
                              <CaretTop />
                            </el-icon>
                            <el-icon v-else title="展开">
                              <CaretBottom />
                            </el-icon>
                          </div>
                        </div>
                        <div v-show="expanedDict[it.historyId + it.nodeId + '_outputs']" style="width: 100%;">
                          <json-viewer
                            :value="jsonViewParse(it.output)"
                            :copyable="{ copyText: '复制', copiedText: '已复制' }"
                            sort
                            boxed
                            show-double-quotes
                            class="my-awesome-json-theme"
                            theme="my-awesome-json-theme"
                          />
                        </div>
                      </div>
                    </template>
                    <template v-if="it.nodeId !== 'end_node' && index === motitorList.length - 1 && !it.isOver">
                      <div class="log-loading" v-if="it.state !== 'failed' && !it.failedCount">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>正在执行中...</span>
                      </div>
                      <div class="log-loading" v-if="it.state === 'failed' || it.failedCount">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>执行失败，重试中...</span>
                      </div>
                    </template>
                  </div>
                </el-timeline-item>
              </el-timeline>
              <el-empty v-if="!motitorList.length" description="暂无数据" style="height: 80vh;" />
            </div>
            <div class="monitor-box" v-show="activeTab === 'executionResult'">
              <div class="file-item" v-for="(item, index) in motitorFileList" :key="index">
                <div class="file-item-title">
                  <img :src="getFileIcon(item.fileName)" alt="">
                  <div class="file-item-title-text" :title="item.fileName">{{ item.fileName }}</div>
                  <!-- <div class="file-item-title-tag" v-if="item.type === 'file'">本地</div>
                  <div class="file-item-title-tag online" v-else>云端</div> -->
                </div>
                <div class="file-item-size">
                  <div class="file-item-size-text">{{ item.size < 0 ? '-' : formatSize(item.size) }}</div>
                  <div class="file-item-size-text">{{ utilsFormatTime(item.excTime*1000) }}</div>
                </div>
                <div class="file-item-path" v-if="item.path">
                  <div class="file-item-path-text">{{ item.path }}</div>
                </div>
                <div class="file-item-buttons">
                  <div v-if="item.isOnline" class="file-item-btn" @click="fetchFileDownLoad(item)">
                    <i class="action-iconfont icon-yunduanxiazai"></i>
                    <span>云端下载</span>
                  </div>
                  <div class="file-item-botton primary" @click="openFile(item, 'file')">打开文件</div>
                  <div class="file-item-botton" @click="openFile(item, 'folder')">打开目录</div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <el-dialog
      title="表格详情"
      v-model="tableDialog"
      width="60%"
      :before-close="closeTableDialog"
    >
      <el-table class="mask-table" :data="tableDialogData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="500">
        <el-table-column type="index" label="序号" align="center" width="80" />
        <el-table-column v-for="it in tableDialogColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
          <template v-if="it.dType == 'date'" #default="{ row }">
            {{ formatTime(row[it.data]*1000, 'MM-DD HH:mm') }}
          </template>
          <template v-else-if="it.data == 'value'" #default="{ row }">
            {{ row.value }}
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无数据" style="height: 30vh;" />
        </template>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent, ref } from 'vue'
import moment from "moment";
import JsonViewer from 'vue-json-viewer'
import saasApi from '@/api/index';
import { isNull } from '@/utils/validate';
import { useUserStore } from "@/stores/user";
import { ElMessage, ElMessageBox } from 'element-plus'
import FilePreview from '@/views/filePreview/index.vue'
import MapFrame from '@/components/mapFrame.vue'
// @ts-ignore
import utils from '@/utils/utils'
import * as echarts from 'echarts';
import { generateComponentDescription } from '@/utils/componentDisplay'
import { componentCategories } from "@/utils/componentCategories"
import { useWebsocketStore} from "@/stores/websocket.js";
import markdownIt from './markdownIt.vue'

const userStore = useUserStore()
const websocketStore = useWebsocketStore()

export default defineComponent({
  name: 'manager',
  components: {MapFrame, FilePreview, JsonViewer, markdownIt },
  data() {
    return {
      loading: false,
      listLoading: false,
      dataList: {},
      dataTypeForId: {
        'start_node': 'workflow_start',
        'end_node': 'workflow_end'
      },
      dataListKeys: [],
      motitorList: [],
      motitorFileList: [],
      dictName: {},
      dictTime: {},
      dictIcon: {
        'workflow_start': 'icon-shouye',
        'workflow_end': 'icon-querenkuang'
      },
      dictBackgroundColor: {
        'workflow_start': '#8CD169',
        'workflow_end': '#37AED4'
      },
      pageSizes: [4, 8, 12, 16],
      pagination: {
        page: 1,
        size: 8,
        total: 0
      },
      completeIds: [],

      templateExcel: {},
      timer: null,
      nodes: [],
      showMonitorIds: [], // 节点配置show_monitor为true的节点id

      activeTab: 'executionRecord',
      activeId:"",
      showRightview: true,
      showLeftview: false,
      leftTaskTotal: 0,
      leftTaskList: [],
      leftQueryIndex: 1,
      leftFirstQuery: true,
      leftQueryFinish: false,
      leftQuerying: false,
      currentTask: null,
      historyExcId: '',

      childrenNodes: [], // 子节点配置信息
      nodeHistoryIds: {}, // 保存节点historyId,判断数据是否更新


      scrollTimer: null,
      historyExcData: [], // 历史执行记录数据
      isExc: false,
      expanedDict: {},
      tableDialog: false,
      tableDialogColumns: [],
      tableDialogData: [],

      aiText: {}, // 储存对应nodeId的ai节点文本
      aiTimer: {},  // 储存对应nodeId的ai节点定时器
    }
  },
  created () {
    this.isExc = this.$route.query.isExc === 'true'
    // 在任何需要检查WebSocket连接的地方调用
    window.checkWebSocketConnection();
  },
  computed: {
    // 是否是集成到一诺桌面端
    isUniwimPc() {
      return this.$route.query.uniwim === 'pc'
    },
    // 用户信息
    currentUser() {
      return userStore.userInfo
    },
    //
    missionId() {
      return this.$route.query.missionId
    },
    // 任务id
    historyId(){
      return this.$route.query.historyId
    },
    // 手动执行节点数据
    execList(){
      if (this.isExc && this.historyExcId) {
        return websocketStore.getMessagesByType(this.historyExcId, 'exec')
      } else {
        return null
      }
    },
    // 手动执行ai节点流式数据
    aiList(){
      if (this.isExc && this.historyExcId) {
        return websocketStore.getMessagesByType(this.historyExcId, 'ai')
      } else {
        return null
      }
    },
    // 手动执行发票识别节点流式数据
    invoice_ocrList(){
      if (this.isExc && this.historyExcId) {
        return websocketStore.getMessagesByType(this.historyExcId, 'invoice_ocr')
      } else {
        return null
      }
    }
  },
  watch: {
    execList: {
      handler(newVal, oldVal) {
        if (!newVal) return
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return
        let isOver = false
        this.motitorList = (newVal || []).map(it => {
          if (it.node_id) this.dataTypeForId[it.node_id] = it.node_type
          if (it.state.includes('suite.')) isOver = true
          return {
            ...it,
            historyId: it.history_id,
            nodeId: it.node_id,
            nodeName: it.node_name,
            nodeType: it.node_type,
            excTimeBack: moment(it.time).valueOf(),
            excTime: this.utilsFormatTime(moment(it.time).unix()*1000),
            title: it.node_name,
            input: typeof it.inputs === 'object' ? JSON.stringify(it.inputs) : it.inputs,
            output: typeof it.outputs === 'object' ? JSON.stringify(it.outputs) : it.outputs
          }
        })
        // 过滤掉没有node_id的结束节点
        this.motitorList = this.motitorList.filter(it => it.node_id)
        // 模拟运行状态，遍历判断，增加loading
        this.motitorList.forEach((it, index) => {
          it.isOver = isOver
          if (it.state !== 'failed' && it.node_id !== 'end_node' && index === this.motitorList.length - 1 && !it.isOver) {
            it.state = 'loading'
          } else if (it.state === 'progress') {
            it.state = isOver ?  'failed' : 'pass'
          }
        })
        // 因为数据替换问题导致排序异常,重新排序
        this.motitorList = this.motitorList.sort((a, b) => {
          return a.excTimeBack - b.excTimeBack
        });
        // 手动执行完成后不切换为监控状态
        if (isOver) {
          if (this.motitorFileList?.length > 0) {
            this.activeTab = 'executionResult'
          }
          this.historyExcQuery()
          this.isExc = false
        }
        console.log('this.motitorList', this.motitorList);
        this.handleMotitorList(this.motitorList)
        this.getFileList()
        // 清除下展开数据字典
        if (!newVal.length) {
          this.expanedDict = {}
        }
      },
      deep: true
    },
    aiList: {
      handler(newVal, oldVal) {
        if (!newVal) return
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return
        if (newVal[0]?.nodeId) {
          if (newVal[0]?.state === 'end') {
            if (this.aiTimer[newVal[0].nodeId]) {
              // 防止ai没有渲染完成
              setTimeout(() => {
                clearInterval(this.aiTimer[newVal[0].nodeId])
                this.aiTimer[newVal[0].nodeId] = null
              }, 1000)
            }
          } else {
            // 此时添加对应ai节点文本到aiText
            if (!this.aiText[newVal[0].nodeId]) this.aiText[newVal[0].nodeId] = ''
            this.aiText[newVal[0].nodeId] += newVal[0]?.text || ''
            if (this.dataList[newVal[0].nodeId] && this.dataList[newVal[0].nodeId].length > 0) {
              // 前端定时更新dom,200毫秒
              if (!this.aiTimer[newVal[0].nodeId]) {
                // 此处用setInterval,让流式输出更丝滑
                this.aiTimer[newVal[0].nodeId] = setInterval(() => {
                  const data = this.dataList[newVal[0].nodeId][0]
                  let output = data.output
                  output.ai_analyze_response = this.aiText[newVal[0].nodeId]
                  output.ai_analyze_response = output.ai_analyze_response.replace('<think>', '').replace('</think>', '')
                  this.dataList[newVal[0].nodeId][0].output.ai_analyze_response = output.ai_analyze_response
                  // 默认滚动到最下方
                  this.scrollToEnd()
                }, 200)
              }
            } else {
              const row = this.motitorList.find(it => it.node_id === newVal[0].nodeId)
              if (row) this.dataList[newVal[0].nodeId] = [row]
            }
          }
        }
      },
      deep: true
    },
    invoice_ocrList: {
      handler(newVal, oldVal) {
        if (!newVal) return
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return
        console.log('发票websocket', newVal, moment().format('YYYY-MM-DD HH:mm:ss'));
        if (newVal[0]?.nodeId && newVal[0]?.state !== 'end') {
          if (this.dataList[newVal[0].nodeId] && this.dataList[newVal[0].nodeId].length >= 0) {
            if (newVal[0].result) {
              const invoice_dict = newVal[0].invoice_dict || []
              const invoiceCard = Object.keys(newVal[0].result).filter(key => !['type', 'invoice_dict', 'invoice_type', 'tax_rate', 'amount', 'number', 'image_path'].includes(key)).map(key => ({
                value: key
              }))
              this.dataList[newVal[0].nodeId].push({
                ...newVal[0].result,
                total: newVal[0].total || this.dataList[newVal[0].nodeId].length,
                invoice_type: newVal[0].invoice_type || '',
                invoice_dict,
                invoiceCard
              })
            }
            // 默认滚动到最下方
            this.scrollToEnd()
          } else {
            const row = this.motitorList.find(it => it.node_id === newVal[0].nodeId)
            if (row) this.dataList[newVal[0].nodeId] = []
          }
        }
      },
      deep: true
    }
  },
  async mounted() {
    // 解析节点配置,抽出子节点
    componentCategories.forEach(item => {
      if (item.components?.length > 0) {
        item.components.forEach(it => {
          this.childrenNodes.push({
            ...it,
            color: item.color
          })
        })
      }
    })
    this.activeId = this.missionId || ''
    this.historyExcId = this.historyId || ''
    // 监控或执行记录进入时查询历史记录列表
    this.historyExcQuery()
    await this.queryTaskConfig()
    await this.queryTaskExcel()
    this.completeIds = []
    // 如果是手动执行节点,此处需要等待queryTaskConfig及queryTaskExcel之后在处理一次dataList
    if (this.isExc) {
      // 因为数据替换问题导致排序异常,重新排序
      this.motitorList = this.motitorList.sort((a, b) => {
        return a.excTimeBack - b.excTimeBack
      });
      this.handleMotitorList(this.motitorList)
      this.getFileList()
    }
    // 监控或执行记录进入时查询节点数据
    else {
      this.initDataList()
    }
  },
  methods: {
    // 发票节点时间轴
    handlePercentage(key) {
      let percentage = 20
      if (this.dataList[key] && this.dataList[key].length > 0) {
        percentage = Math.floor(this.dataList[key].length / (this.dataList[key][0].total || this.dataList[key].length) * 100)
      }
      return percentage
    },
    // table表头计算宽度
    renderHeader({ column }) {
      //创建一个元素用于存放表头信息
      const span = document.createElement('span')
      // 将表头信息渲染到元素上
      span.innerText = column.label
      // 在界面中添加该元素
      document.body.appendChild(span)
      //获取该元素的宽度（包含内外边距等信息）
      const spanWidth = span.getBoundingClientRect().width + 20  //渲染后的 div 内左右 padding 都是 10，所以 +20
      //判断是否小于element的最小宽度，两者取最大值
      column.minWidth = column.minWidth > spanWidth ? column.minWidth : spanWidth
      // 计算完成后，删除该元素
      document.body.removeChild(span)
      return column.label
    },
    // 初始化eacharts
    initCharts(key, obj) {
      console.log(obj)
      this.$nextTick(() => {
        if (!this.$refs[key+'echarts']) return
        const myChart = echarts.init(this.$refs[key+'echarts'][0]);
        const date = obj.historyTableData.map(it => {
          return moment(it.time).format('MM-DD HH:mm')
        })
        obj.tableData.forEach(it => {
          date.push(moment(it.time).format('MM-DD HH:mm'))
        })
        const data = []
        const hisData = obj.historyTableData.map(it => {
          data.push(null)
          return it.value
        })
        obj.tableData.map(it => {
          data.push(it.value)
        })
        const options = {
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%'];
            }
          },
          toolbox: {
            show: false // 禁用工具箱
          },
          legend: {
            data: ['历史值', '预测值'],
            itemWidth: 12,
            itemHeight: 2,
            bottom: '50px'
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '20%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLine: {//x轴线的颜色以及宽度
              show: true,
              lineStyle: {
                color: "#BCBFC3",
                width: 0,
                type: "solid"
              }
            },
            axisLabel: {
              margin: 16
            },
            data: date
          },
          yAxis: {
            type: 'value',
            axisLine: {//x轴线的颜色以及宽度
              show: true,
              lineStyle: {
                color: "#BCBFC3",
                width: 0,
                type: "solid"
              }
            },
            axisLabel: {
              margin: 16
            },
            boundaryGap: [0, '100%']
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 10
            },
            {
              start: 0,
              end: 10
            }
          ],
          series: [
            {
              name: '历史值',
              type: 'line',
              symbol: 'none',
              sampling: 'lttb',
              itemStyle: {
                color: '#999999'
              },
              data: hisData
            },
            {
              name: '预测值',
              type: 'line',
              symbol: 'none',
              sampling: 'lttb',
              itemStyle: {
                color: '#FF8E32'
              },
              data: data
            }
          ]
        };
        myChart.setOption(options);
      })
    },
    // 获取motitorFileList
    getFileList() {
      const params = {
        data: {},
        index: 1,
        size: -1
      }
      if (this.activeId) {
        params.data.missionId = this.activeId
      }
      if (this.historyExcId) {
        params.data.historyId = this.historyExcId
      }
      // 获取当前文件列表
      saasApi.AIAgentMissionExcAllFile(params).then(res=>{
        if (res?.length > 0) {
          this.motitorFileList = this.motitorFileList.filter(it => it.type !== 'file')
          const datas = res.map(it => {
            // 支持\
            it.path = it.path.replace(/\//g, '\\')
            it.dir = it.dir.replace(/\//g, '\\')
            const name = it.path.split('\\').pop()
            let isOnline = false
            if (this.dataList[it.nodeId] && this.dataList[it.nodeId][0]) {
              const newData = JSON.parse(isNull(this.dataList[it.nodeId][0].output) ? '{}' : this.dataList[it.nodeId][0].output)
              isOnline = newData.file_name ? true : false
            }
            return {
              ...it,
              path: it.path ? it.path : '',
              type: 'file',
              fileName: name || it.name,
              historyId: this.historyExcId,
              isOnline
            }
          })
          this.motitorFileList = datas
        } else {
          this.motitorFileList = []
        }
      }).catch(()=>{
        this.motitorFileList = []
      })
    },
    // 初始化分页参数
    resetData() {
      // this.pagination = {
      //   page: 1,
      //   size: 8,
      //   total: 0
      // }
      this.pagination.total = 0
    },
    // 获取编排节点配置
    queryTaskConfig() {
      return new Promise((resolve, reject) => {
        saasApi
        .AIAgentMissionDetail({ id: this.activeId }).then(res => {
          try {
            const nodesConfig = res.configContent ? JSON.parse(res.configContent) : {}
            this.nodes = nodesConfig.nodes || []
            nodesConfig.nodes.forEach(it => {
              if (it.data?.config?.show_monitor) {
                this.showMonitorIds.push(it.id)
              }
            })
            console.log('节点配置', this.nodes);
          } catch (error) {
            this.nodesConfig = {}
            this.nodes = []
            this.showMonitorIds = []
          }
        })
        .catch((error) => {
          this.nodesConfig = {}
          this.nodes = []
          this.showMonitorIds = []
        })
        .finally(() => {
          resolve(true)
        })
      })
    },
    // 根据当前任务是否有创建excel节点优先展示excel模板
    queryTaskExcel() {
      return new Promise((resolve, reject) => {
        // 判断当前节点中是否有excel节点
        const haveExcel = this.nodes.some(it => it.data?.componentType === 'excel_create')
        if (!this.activeId || !haveExcel) {
          resolve(true)
          return
        }
        const params = {
          url:"/wimai/api/office/template/getTaskTemplateList",
          query_param: {
            taskId: this.activeId
          },
          method:"get"
        }
        saasApi.AIDtemplateCrud(params).then(res=>{
          if (res?.config || res?.id) {
            this.templateExcel = {
              nodeId: 'templateExcel',
              configId: res.id || '',
              config: res.config || '',
              missionId: this.activeId,
              historyId: '',
              nodeType: 'excel_create'
            }
            this.dataTypeForId.templateExcel = 'excel_create'
            this.dictName.templateExcel = res.name
            // 默认不再展示模板
            // this.dataList.templateExcel = [this.templateExcel]
            // if (!this.dataListKeys.includes('templateExcel')) this.dataListKeys.push('templateExcel')
          }
          resolve(true)
        }).catch(()=>{
          this.templateExcel = {}
          resolve(true)
        })
      })
    },
    initDataList(noLoading) {
      if(!this.historyExcId && !this.activeId) {
        return ElMessage({
          message: '缺少任务参数，无法获取历史记录',
          type: 'error',
          showClose: true
        })
      }
      // 初始化分页参数
      this.resetData()
      if (!noLoading) this.listLoading = true
      const params = {
        data: {},
        index: 1,
        size: -1
      }
      if (this.activeId) {
        params.data.missionId = this.activeId
      }
      if (!noLoading) {
        if (this.historyExcId) {
          params.data.historyId = this.historyExcId
        }
      }


      saasApi.AIAgentMissionLogQuery(params).then(res=>{
        if(res?.rows?.length > 0){
          // 数据按时间字段excTime排序
          const rows = res.rows.sort((a, b) => {
            if (a.nodeId === 'start_node') return -1;
            if (b.nodeId === 'start_node') return 1;
            return a.excTime - b.excTime;
          });
          rows.forEach(it => {
            // 如果当前是新查询任务时清除之前数据
            if (this.motitorList[0]?.historyId && it.historyId !== this.motitorList[0].historyId) {
              this.motitorFileList = []
              this.dataListKeys = []
              this.motitorList = []
              this.dataTypeForId = {}
              this.completeIds = []
              this.dataList = {}
            }
            const thisIndex = this.motitorList.findIndex(item => item.nodeId === it.nodeId)
            if (thisIndex > -1) {
              this.motitorList[thisIndex] = {
                ...it,
                input: isNull(it.input) ? '无' : it.input,
                output: isNull(it.output) ? '无' : it.output,
                excTimeBack: it.excTime * 1000,
                excTime: utils.formatTime(it.excTime * 1000),
                finishTime: utils.formatTime(it.finishTime * 1000),
                isOver: true
              }
            } else {
              this.motitorList.unshift({
                ...it,
                input: isNull(it.input) ? '无' : it.input,
                output: isNull(it.output) ? '无' : it.output,
                excTimeBack: it.excTime * 1000,
                excTime: utils.formatTime(it.excTime * 1000),
                finishTime: utils.formatTime(it.finishTime * 1000),
                isOver: true
              })
            }
          })
          // 因为数据替换问题导致排序异常,重新排序
          this.motitorList = this.motitorList.sort((a, b) => {
            return a.excTimeBack - b.excTimeBack
          });

          this.handleMotitorList(rows)

          // 模拟运行状态，遍历判断，增加loading和waiting状态
          this.motitorList.forEach((item, index) => {
            // 如果有执行错误的，那索引小于他的都显示等待中
            const errorIndex = this.motitorList.findIndex(it => it.state === 'failed')
            if(errorIndex > -1){
              if(index > errorIndex){
                item.state = 'waiting'
              }
            }
            else {
              if(index === this.motitorList.length-1){
                item.state = 'loading'
                if(item.nodeId === 'end_node' || item.nodeType === 'workflow_end'){
                  item.state = 'pass'
                }
              }
            }
          })
        }else{
          // this.dataList = {}
          // this.motitorList = []
          // this.completeIds = []
        }
      }).catch(()=>{
        // this.dataList = {}
        // this.motitorList = []
      }).finally(()=>{
        // 获取当前文件列表
        this.getFileList()
        if (this.motitorFileList?.length > 0) {
          this.activeTab = 'executionResult'
        }
        this.listLoading = false
      })
    },
    // 处理节点数据抽离
    handleMotitorList(rows){
      // 根据nodeId及nodeType分类添加dataList数据
      rows.forEach(async(item, index) => {
        // id不重复时添加数据
        this.dataTypeForId[item.nodeId] = item.nodeType
        // 节点配置show_monitor为true时展示数据
        if (this.showMonitorIds.includes(item.nodeId)) {
          if (!this.completeIds.includes(item.nodeId)) {
            const newData = JSON.parse(isNull(item.output) ? '{}' : item.output)
            if (!this.dataListKeys.includes(item.nodeId)) {
              this.dataListKeys.push(item.nodeId)
            }
            if (!this.dataList[item.nodeId]) {
              this.dataList[item.nodeId] = []
            }
            this.dictName[item.nodeId] = item.title
            this.dictTime[item.nodeId] = typeof item.excTime === 'number' ? this.utilsFormatTime(item.excTime*1000) : item.excTime
            if (item.nodeType === 'ai_analyze') {
              if (typeof item.output === 'string') item.output = item.output.replace('<think>', '').replace('</think>', '')
              item.output = JSON.parse(item.output)
              this.dataList[item.nodeId] = [item]
            }
            else if (item.nodeType === 'http_post' || item.nodeType === 'http_get' || item.nodeType === 'http_request') {
              const childData = newData && newData[`response_text`] || {}
              // 重置数据
              this.dataList[item.nodeId] = []
              Object.keys(childData).forEach(it => {
                this.dataList[item.nodeId].push(childData[it])
              })
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }
            else if (item.nodeType === 'python_execute') {
              const childData = newData && newData[`python_output`] || {}
              const variable = this.nodes.find(it => it.id === item.nodeId)?.data?.config?.output_variable || []
              const variableObj = {}
              variable.forEach(it => {
                variableObj[it.variable] = it.desc
              })
              // 重置数据
              this.dataList[item.nodeId] = []
              if (this.handleHttpType(childData) === 'object') {
                Object.keys(childData).forEach(it => {
                  this.dataList[item.nodeId].push({
                    desc: variableObj[it] || it,
                    result: childData[it]
                  })
                })
              } else {
                this.dataList[item.nodeId].push({
                  desc: item.describe || item.title,
                  result: childData
                })
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }
            else if (item.nodeType === 'excel_create') {
              // 先删除模板数据
              if (this.dataList.templateExcel) {
                delete this.dataList.templateExcel
              }
              // 添加新数据
              this.dataList[item.nodeId] = [item]

            }
            else if (item.nodeType === 'word_create') {
              
            }
            else if (item.nodeType === 'data_forecast') {
              this.dataList[item.nodeId] = [this.handleDataList(item.nodeId, item)]
            }
            else if (item.nodeType === 'water_shutoff_valve') {
              this.dataList[item.nodeId] = [this.handleDataList(item.nodeId, item)]
            }
            // 发票识别
            else if (item.nodeType === 'invoice_recognition') {
              let childData = newData && newData[`recognition_results`] || []
              childData.forEach(it => {
                it.invoiceCard = Object.keys(it).filter(key => !['type', 'invoice_dict', 'invoice_type', 'tax_rate', 'amount', 'number', 'image_path'].includes(key)).map(key => ({
                  value: key
                }))
              })
              this.dataList[item.nodeId] = childData
            }
            // 图片识别
            else if (item.nodeType === 'img_recognition') {
              let childData = newData && newData[`recognition_result`] || {}
              if (typeof childData === 'string') {

              } else {
                if (!childData.url) childData.url = []
                if (childData.content) childData.content = childData.content.replace(/\n/g, '<br>')
                this.dataList[item.nodeId] = [childData]
              }
            }
            // 文本模板
            else if (item.nodeType === 'text_template') {
              let childData = newData && newData[`output_variable`] || ''
              childData = childData.replace(/\n/g, '<br>')
              this.dataList[item.nodeId] = childData
            }
            // sql执行
            else if (item.nodeType === 'db_query') {
              let childData = []
              try {
                childData = newData?.result ? JSON.parse(newData.result) || [] : []
              } catch (error) {
                console.log(error);
                childData = []
              }
              // newData?.result不是数组而是对象时再取一次数据
              if (!Array.isArray(childData)) {
                const key = this.nodes.find(it => it.id === item.nodeId)?.data?.config?.response_content_variable
                childData = childData[key] || childData
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              const tableColumns = Object.keys((childData[0] || {})).map(it => ({
                data: it,
                title: it
              }))
              item = {
                ...item,
                tableColumns,
                result: childData,
                pageSizes: [4, 8, 12, 16],
                pagination: {
                  page: 1,
                  size: 8,
                  total: childData.length || 0
                }
              }
              this.handleTableList(item)
              this.dataList[item.nodeId] = [item]
              if (this.dataListKeys.indexOf(item.nodeId) < 0) this.dataListKeys.push(item.nodeId)
              if (tableColumns.length === 0) {
                this.dataListKeys = this.dataListKeys.filter(it => it !== item.nodeId)
              }
            }
            // 节点通用展示
            else {
              const codes = this.nodes.find(it => it?.data?.componentType === item.nodeType)?.data?.outputs
              const code = (codes && codes[0]) || ''
              const childData = newData && newData[code] || {}
              // 重置数据
              this.dataList[item.nodeId] = []
              if (this.handleHttpType(childData) === 'object') {
                Object.keys(childData).forEach(it => {
                  this.dataList[item.nodeId].push({
                    desc: it,
                    result: childData[it]
                  })
                })
              } else {
                this.dataList[item.nodeId].push({
                  desc: item.describe || item.title,
                  result: childData
                })
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }

            // 储存已渲染节点的historyId
            if (!this.completeIds.includes(item.nodeId)) {
              this.completeIds.push(item.nodeId)
            }
            // 默认滚动到最下方
            this.scrollToEnd()
            // 储存节点historyId
            this.nodeHistoryIds[item.nodeId] = item.historyId
          }
          // id重复时修改数据
          else {
            // 手动执行时不启用该逻辑,因为同一个historyId数据会不同
            if (this.nodeHistoryIds[item.nodeId] === item.historyId && !this.isExc) return
            const newData = JSON.parse(isNull(item.output) ? '{}' : item.output)
            this.dictName[item.nodeId] = item.title
            this.dictTime[item.nodeId] = typeof item.excTime === 'number' ? this.utilsFormatTime(item.excTime*1000) : item.excTime
            if (item.nodeType === 'ai_analyze') {
              if (this.dataList[item.nodeId]) {
                if (typeof item.output === 'string') item.output = item.output.replace('<think>', '').replace('</think>', '')
                item.output = JSON.parse(item.output)
                this.dataList[item.nodeId] = [item]
              }
            } else if (item.nodeType === 'http_post' || item.nodeType === 'http_get' || item.nodeType === 'http_request') {
              const childData = newData && newData[`response_text`] || {}
              // 先删除旧数据
              this.dataList[item.nodeId] = []
              // 再添加新数据
              Object.keys(childData).forEach(it => {
                this.dataList[item.nodeId].push(childData[it])
              })
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }
            else if (item.nodeType === 'python_execute') {
              const childData = newData && newData[`python_output`] || {}
              const variable = this.nodes.find(it => it.id === item.nodeId)?.data?.config?.output_variable || []
              const variableObj = {}
              variable.forEach(it => {
                variableObj[it.variable] = it.desc
              })
              // 先删除旧数据
              this.dataList[item.nodeId] = []
              if (this.handleHttpType(childData) === 'object') {
                Object.keys(childData).forEach(it => {
                  this.dataList[item.nodeId].push({
                    desc: variableObj[it] || it,
                    result: childData[it]
                  })
                })
              } else {
                this.dataList[item.nodeId].push({
                  desc: item.describe || item.title,
                  result: childData
                })
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }
            else if (item.nodeType === 'excel_create') {
              this.dataList[item.nodeId] = [item]

            }
            else if (item.nodeType === 'word_create') {
              
            }
            else if (item.nodeType === 'data_forecast') {
              this.dataList[item.nodeId] = [this.handleDataList(item.nodeId, item)]
            }
            else if (item.nodeType === 'water_shutoff_valve') {
              this.dataList[item.nodeId] = [this.handleDataList(item.nodeId, item)]
            }
            // 发票识别
            else if (item.nodeType === 'invoice_recognition') {
              let childData = newData && newData[`recognition_results`] || []
              childData.forEach(it => {
                it.invoiceCard = Object.keys(it).filter(key => !['type', 'invoice_dict', 'invoice_type', 'tax_rate', 'amount', 'number', 'image_path'].includes(key)).map(key => ({
                  value: key
                }))
              })
              this.dataList[item.nodeId] = childData
            }
            // 图片识别
            else if (item.nodeType === 'img_recognition') {
              let childData = newData && newData[`recognition_result`] || {}
              if (typeof childData === 'string') {

              } else {
                if (!childData.url) childData.url = []
                if (childData.content) childData.content = childData.content.replace(/\n/g, '<br>')
                this.dataList[item.nodeId] = [childData]
              }
            }
            // 文本模板
            else if (item.nodeType === 'text_template') {
              let childData = newData && newData[`output_variable`] || ''
              childData = childData.replace(/\n/g, '<br>')
              this.dataList[item.nodeId] = childData
            }
            // sql执行
            else if (item.nodeType === 'db_query') {
              let childData = []
              try {
                childData = newData?.result ? JSON.parse(newData.result) || [] : []
              } catch (error) {
                console.log(error);
                childData = []
              }
              // newData?.result不是数组而是对象时再取一次数据
              if (!Array.isArray(childData)) {
                const key = this.nodes.find(it => it.id === item.nodeId)?.data?.config?.response_content_variable
                childData = childData[key] || childData
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              const tableColumns = Object.keys((childData[0] || {})).map(it => ({
                data: it,
                title: it
              }))
              item = {
                ...item,
                tableColumns,
                result: childData,
                pageSizes: [4, 8, 12, 16],
                pagination: {
                  page: 1,
                  size: 8,
                  total: childData.length || 0
                }
              }
              this.handleTableList(item)
              this.dataList[item.nodeId] = [item]
              if (this.dataListKeys.indexOf(item.nodeId) < 0) this.dataListKeys.push(item.nodeId)
              if (tableColumns.length === 0) {
                this.dataListKeys = this.dataListKeys.filter(it => it !== item.nodeId)
              }
            }
            // 节点通用展示
            else {
              const codes = this.nodes.find(it => it?.data?.componentType === item.nodeType)?.data?.outputs
              const code = (codes && codes[0]) || ''
              const childData = newData && newData[code] || {}
              // 重置数据
              this.dataList[item.nodeId] = []
              if (this.handleHttpType(childData) === 'object') {
                Object.keys(childData).forEach(it => {
                  this.dataList[item.nodeId].push({
                    desc: it,
                    result: childData[it]
                  })
                })
              } else {
                this.dataList[item.nodeId].push({
                  desc: item.describe || item.title,
                  result: childData
                })
              }
              // 数据处理,根据结果类型转换为字符串或对象或数组
              this.dataList[item.nodeId] = this.handleDataList(item.nodeId)
            }
            // 储存节点historyId
            this.nodeHistoryIds[item.nodeId] = item.historyId

            setTimeout(() => {
              // 默认滚动到最下方
              this.scrollToEnd()
            }, 500);
          }
        } else {
          // 储存已渲染节点的historyId
          if (!this.completeIds.includes(item.nodeId)) {
            this.completeIds.push(item.nodeId)
          }
          // 默认滚动到最下方
          this.scrollToEnd()
        }
      })
    },
    // 滚动最底部
    scrollToEnd() {
      // 当前是手动执行节点时滚动到底部
      if (!this.isExc) return
      this.$nextTick(()=>{
        if (this.$refs.scrollbar) this.$refs.scrollbar.setScrollTop(this.$refs.scrollbar.wrapRef?.scrollHeight || 0)
        if (this.$refs.rightScrollbar) this.$refs.rightScrollbar.setScrollTop(this.$refs.rightScrollbar.wrapRef?.scrollHeight || 0)
      })
    },
    // 文件下载
    async fetchFileDownLoad(data) {
      try {
        // 步骤1 请求 AIAgentMissionLogFile 接口，设置 responseType 为 arraybuffer
        const responseData = await saasApi.AIAgentMissionExcFileFile(data.historyId)

        // 步骤2 创建 Blob 对象
        const blob = new Blob([responseData], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })

        // 步骤3 创建临时 URL
        const blobUrl = URL.createObjectURL(blob)

        // 步骤4 创建 <a> 标签并设置属性
        const downloadLink = document.createElement('a')
        downloadLink.href = blobUrl
        // 指定下载文件名，可根据实际情况修改
        downloadLink.download = data.fileName

        // 步骤5 模拟点击 <a> 标签触发下载
        downloadLink.click()

        // 步骤6 释放临时 URL 避免内存泄漏
        URL.revokeObjectURL(blobUrl)
      } catch (error) {
        ElMessage.error('下载失败')
      }
    },
    // 定时查询历史记录数据
    async setIntervalQuery() {
      if (this.timer) {
        await this.clearTimer()
      }
      // 运行记录跳转过来不轮询,手动执行完成后及监控页面时需要轮询
      if (!this.historyId || (this.$route.query.isExc && !this.isExc)) {
        this.timer = setTimeout(() => {
          // this.initDataList(true)
          this.historyExcQuery()
        }, 5000)
      }
    },
    // 处理markdown表格数据
    handleMarkDown(data){
      let tableHtml = ''
      if (data.nodeType === 'ai_analyze') {
        const newData = data.output
        let matches = newData[`ai_analyze_response`] || ''
        tableHtml = matches
        // if (tableHtml) tableHtml += `<p class="markdown-time">${utils.formatTime(data.excTime*1000)}</p>`
      }
      return tableHtml
    },
    // 处理左侧列表内容数据
    handleContent(data) {
      const node = this.nodes.find(it => it.id === data.nodeId)
      let html = ''
      if (node?.data) {
        const config = node.data.config || {}
        config.type = 'taskMonitor'
        html = generateComponentDescription(node, 'taskMonitor')
      }
      if (html.includes('点击配置组件参数')) html = ''
      return html
    },
    // 节点是否显示打开文件
    showOpenFile(nodeId) {
      const file = this.motitorFileList.find(it => it.nodeId === nodeId)
      return file ? true : false
    },
    // 从节点触发打开文件
    nodeOpenFile(nodeId) {
      const file = this.motitorFileList.find(it => it.nodeId === nodeId)
      this.openFile(file, 'file')
    },

    // 分页数量改变事件
    handleSizeChange(item) {
      // console.log(`${val} items per page`)
      this.handleTableList(item)
    },
    // 分页页码改变事件
    handleCurrentChange(item) {
      // console.log(`current page: ${val}`)
      this.handleTableList(item)
    },
    // 处理DataList数据,监测数据时前端分页
    handleDataList(key, item) {
      if (this.dataTypeForId[key] === 'http_post' || this.dataTypeForId[key] === 'http_get' || this.dataTypeForId[key] === 'http_request') {
        const data = JSON.parse(JSON.stringify(this.dataList[key]));
        data.forEach(item => {
          if (this.handleHttpType(item.result) === 'object') {
            item.result = item.result ? item.result : {}
          } else if (this.handleHttpType(item.result) === 'array') {
            if (item.result && item.result[0]) {
              if (typeof item.result[0] === 'object') {
                item.tableColumns = Object.keys(item.result[0]).map(key => {
                  return {
                    data: key,
                    title: key,
                    dType: key === 'st' || key === 'et' ? 'date' : null
                  }
                })
              } else {
                item.tableColumns = item.tableColumns = [
                  {data: 'value', title: item.desc}
                ]
                item.result = item.result.map(it => {
                  return {
                    value: it
                  }
                })
              }
            } else {
              item.tableColumns = [
                {data: 'value', title: item.desc}
              ]
              item.result = []
            }
            item.pageSizes = [4, 8, 12, 16]
            item.pagination = {
              page: 1,
              size: 8,
              total: item.result?.length || 0
            }
            this.handleTableList(item)
          }
        })
        return data
      }
      else if (this.dataTypeForId[key] === 'data_forecast') {
        const objData = item ? item : this.dataList[key][0] || {}
        const dataItem = JSON.parse(isNull(objData.output) ? '{}' : objData.output)
        const data = JSON.parse(dataItem?.forecast_data || '{}')
        const obj = {
          ...data,
          nodeId: item.nodeId,
          tableData: (data.forecast_time || []).map((it, index) => {
            return {
              time: it*1000,
              value: data.forecast_data && data.forecast_data[index]
            }
          }),
          historyTableData: (data.his_win_time || []).map((it, index) => {
            return {
              time: it*1000,
              value: data.his_win_data && data.his_win_data[index]
            }
          }),
          tableColumns: [
            {data: 'time', title: '时间'},
            {data: 'value', title: '预测数据'}, // , width: 160
          ]
        }
        return obj
      }
      else if (this.dataTypeForId[key] === 'water_shutoff_valve') {
        const objData = item ? item : this.dataList[key][0] || {}
        try {
          objData.output = JSON.parse(objData.output)
        } catch (e) {
          objData.output = {}
        }
        return objData
      }
      else {
        const data = JSON.parse(JSON.stringify(this.dataList[key]));
        if (Array.isArray(data)) {
          data.forEach(item => {
            if (this.handleHttpType(item.result) === 'object') {
              item.result = item.result ? item.result : {}
            } else if (this.handleHttpType(item.result) === 'array') {
              if (item.result && item.result[0]) {
                if (typeof item.result[0] === 'object') {
                  item.tableColumns = Object.keys(item.result[0]).map(key => {
                    return {
                      data: key,
                      title: key,
                      dType: key === 'st' || key === 'et' ? 'date' : null
                    }
                  })
                } else {
                  item.tableColumns = item.tableColumns = [
                    {data: 'value', title: item.desc}
                  ]
                  item.result = item.result.map(it => {
                    return {
                      value: it
                    }
                  })
                }
              } else {
                item.tableColumns = [
                  {data: 'value', title: item.desc}
                ]
                item.result = []
              }
              item.pageSizes = [4, 8, 12, 16]
              item.pagination = {
                page: 1,
                size: 8,
                total: item.result?.length || 0
              }
              this.handleTableList(item)
            }
          })
        }
        return data
      }
    },
    // 处理http结果数据类型
    handleHttpType(data) {
      let type = ''
      const row = data || ''
      if (typeof row === 'object' && row !== null) {
        if (row.constructor === Array) {
          type = 'array'
        } else {
          type = 'object'
        }
      } else {
        type = typeof row;
      }
      return type
    },
    // 处理http结果json数据
    handleHttpJSON(data) {
      let newdata = JSON.stringify(data || {}, null, 2)
      return newdata
    },
    // http结果表格前端分页
    handleTableList(item) {
      // const list = item.result.filter((it,index) => index >= (item.pagination.page-1) * item.pagination.size && index < item.pagination.page * item.pagination.size)
      const list = item.result
      item.tableData = list
    },
    // http结果表格数据为数组时，处理表格数据
    openTableDialog(data) {
      this.tableDialog = true
      this.tableDialogColumns = Object.keys((data[0] || {})).map(it => ({
        data: it,
        title: it
      }))
      this.tableDialogData = data
    },
    closeTableDialog() {
      this.tableDialog = false
      this.tableDialogColumns = []
      this.tableDialogData = []
    },

    formatTime(data, format) {
      if (!data) return ''
      return moment(data).format(format)
    },
    // handleTabClick
    handleTabClick(type) {
      this.activeTab = type;
    },
    // 左侧列表请求任务列表
    loadTaskList() {
      if (this.leftQuerying || this.leftQueryFinish) return
      this.leftQuerying = true
      const params = {
        conditions: [],
        data: {},
        index: this.leftQueryIndex,
        size: 10,
      }
      if (this.historyId && !this.$route.query.isExc) {
        params.conditions.push({
          Field: "id",
          Group: 1,
          Operate: "=",
          Relation: "and",
          Value: this.missionId
        })
      } else {
        if (this.$route.query.currentPage && this.$route.query.pageSize && this.leftFirstQuery) {
          params.size = this.$route.query.currentPage * this.$route.query.pageSize
        } else {
          params.size = 10
        }
      }
      saasApi.AIAgentMissionQuery(params).then(res => {
        if (typeof res?.rows == 'object') {
          this.leftTaskTotal = res.total
          this.leftTaskList.push(...res.rows)
          this.currentTask = this.leftTaskList.find(it => it.id === this.activeId)
          if (this.leftTaskList.length >= res.total) {
            this.leftQueryFinish = true;
          } else {
            this.leftQueryIndex++
          }
        }
      }).finally(() => {
        this.$nextTick(() => {
          if (this.leftFirstQuery) {
            const index = this.leftTaskList.findIndex(it => it.id === this.missionId) || 0
            if (this.$refs[`leftListItem${index}`] && this.$refs[`leftListItem${index}`][0]) {
              const offsetTop = this.$refs[`leftListItem${index}`][0].offsetTop - 56
              this.$refs.leftList.scrollTop = offsetTop
            }
            this.leftFirstQuery = false
          }
        })
        this.leftQuerying = false
      })
    },
    // 左侧任务点击事件
    async taskListClick(data) {
      if (this.isExc) return
      if (this.currentTask?.id !== data.id) {
        this.currentTask = data
        this.activeId = data.id;
        this.historyExcId = null
        this.historyExcData = []

        // 切换任务id执行操作
        this.pagination = {
          page: 1,
          size: 8,
          total: 0
        }
        this.templateExcel = {}
        this.motitorList = []
        this.motitorFileList = []
        this.dataListKeys = []
        this.dataTypeForId = {}
        this.dataList = {}
        this.nodes = []
        this.showMonitorIds = []
        this.completeIds = []

        this.isExc = false
        await this.clearTimer()
        this.historyExcQuery()
        this.$nextTick(async () => {
          await this.queryTaskConfig()
          await this.queryTaskExcel()
          this.initDataList()
        })
      }
    },
    getFileIcon(name) {
      return utils.getFileIcon(name)
    },
    utilsFormatTime(time) {
      return utils.formatTime(time)
    },
    formatSize(size) {
      return utils.formatSize(size)
    },
    handleLeftNextText(item) {
      let text = ''
      const nowTime = moment().valueOf()
      if (item.nextStartTime) {
        const difference = item.nextStartTime - nowTime
        if (difference > 0) {
          const days = Math.floor(difference / (24 * 60 * 60 * 1000))
          const hours = Math.floor(difference / (60 * 60 * 1000)) % 24
          const minutes = Math.floor(difference / (60 * 1000)) % 60
          if (days > 0) {
            text = `${days}天后执行`
          } else if (hours > 0) {
            text = `${hours}小时后执行`
          } else if (minutes > 0) {
            text = `${minutes}分钟后执行`
          }
        } else {
          text = '任务已结束'
        }
      } else {
        text = '无下次运行时间'
      }
      return text
    },
    clearTimer() {
      return new Promise((resolve, reject) => {
        if (this.timer) {
          clearTimeout(this.timer)
          this.timer = null
          resolve(true)
        } else {
          resolve(true)
        }
      })
    },
    // 节点内容是否有数据
    showNodeContent(nodeId) {
      let show = false
      const data = this.dataList[nodeId]
      if (Array.isArray(data)) {
        if (data.length > 0) {
          show = true
        } else {
          show = false
        }
      } else {
        if (data) {
          show = true
        } else {
          show = false
        }
      }
      return show
    },
    // 读取节点图标及图标背景色
    handleNodesIcon(type, nodeId) {
      const nodeType = this.dataTypeForId[nodeId]
      const node = this.childrenNodes.find(it => it.type === nodeType)
      if (type === 'icon') {
        return node?.icon || this.dictIcon[nodeType] || ''
      }
      if (type === 'color') {
        return node?.color || this.dictBackgroundColor[nodeType] || ''
      }
    },
    // 发票识别类型判断
    invoiceClass(type, data) {
      let isTrue = false
      if (type === 'train') {
        if (data.invoice_type === 'trainTickets' || data.invoice_type === 'bankreceipt') {
          isTrue = true
        }
      } else if (type === 'taxi') {
        if (data.invoice_type === 'taxiTickets' || data.invoice_type === 'highwayToll') {
          isTrue = true
        }
      } else if (type === 'addedTax') {
        if (data.invoice_type === 'invoice') {
          isTrue = true
        }
      }
      return isTrue
    },
    // 打开本地文件或目录
    openFile(item, type) {
      if (window.electron) {
        let path = ''
        if (type === 'file') {
          path = item.path ? item.path.replace(/\\/g, '\\') : ''
          if (item.image_path) path = item.image_path
        } else {
          path = item.dir ? item.dir.replace(/\\/g, '\\') : ''
        }
        if (path) {
          window.electron.send('controller.robotMsg.openLocalFile', path)
        }
      }
    },
    // 历史记录切换
    async changeHistoryExcId() {
      if (!this.historyExcId) return
      this.motitorList = []
      this.motitorFileList = []
      this.dataListKeys = []
      this.dataTypeForId = {}
      this.dataList = {}
      this.completeIds = []
      this.$nextTick(async () => {
        this.initDataList(false)
      })
    },
    // 任务历史执行记录数据查询
    historyExcQuery() {
      if (!this.activeId) return
      this.maskLoading = true
      const params = {
        conditions: [],
        data: {},
        index: 1,
        size: 30,
        order:[
          {
            Field:"executeTime",
            Type:-1
          }
        ],
      }
      if (this.activeId){
        params.conditions.push({
          Field: "missionId",
          Group: 1,
          Operate: "=",
          Relation: "and",
          Value: this.activeId
        })
      }
      saasApi.AIAgentMissionHistoryQuery(params).then(res => {
        if (typeof res?.rows == 'object') {
          // 判断有没有最新执行数据
          if (this.historyExcData[0]?.id !== res.rows[0]?.id) {
            console.log('有最新执行数据');
            // 手动执行情况下
            if (this.$route.query.isExc) {
              if (this.historyId && this.isExc) {
                this.historyExcId = this.historyId
              } else {
                this.historyExcId = res.rows[0]?.id || ''
              }
            }
            // 监控及记录跳转情况下
            else {
              if (this.historyId) {
                this.historyExcId = this.historyId
              } else {
                this.historyExcId = res.rows[0]?.id || ''
              }
            }
            // 有历史记录数据时
            if (this.historyExcData[0]?.id) {
              // 重置当前页面数据
              this.motitorList = []
              this.motitorFileList = []
              this.dataListKeys = []
              this.dataTypeForId = {}
              this.dataList = {}
              this.completeIds = []
              // 获取最新执行数据
              this.isExc = true
            }
          }
          this.historyExcData = res.rows || []
        }
      }).finally(() => {
        this.setIntervalQuery()
      })
    },
    // 格式化下output内容
    jsonViewParse(data){
      try {
        return JSON.parse(data)
      } catch (e) {
        return data
      }
    },
    // 添加 toggleExpand 方法
    toggleExpand(item, key) {
      this.expanedDict[item?.historyId + item?.nodeId + key] =
        !this.expanedDict[item?.historyId + item?.nodeId + key]
    },
    // 判断是否要显示
    hasPuts(inputs){
      if (typeof inputs === 'object') {
        return JSON.stringify(inputs) !== '{}'
      }
      return inputs && inputs !== '{}' && inputs !== 'null'
    },
  },
  beforeUnmount() {
    Object.keys(this.aiTimer).forEach(key => {
      if (this.aiTimer[key]) {
        clearInterval(this.aiTimer[key])
        this.aiTimer[key] = null
      }
    })
    this.clearTimer()
  }
})
</script>

<style scoped lang="scss">
.flex-column {
  flex-direction: column;
}
.font-bold {
  font-weight: bold;
  color: #333333 !important;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 40px;
  z-index: 7;
  background: #F5F5F5;
}
.code-content {
  background: #f5f5f5;
  border: 1px solid #ebeef5;
  margin: 8px 0 0;
  padding: 0 !important;
}
.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // padding: 12px;
  box-sizing: border-box;
  background: #F5F8FC;
  overflow-x: auto;
  .view-box {
    width: 100%;
    height: 100%;
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-flow: row nowrap;
    flex-direction: row;
    .view-table {
      flex: 1;
      // min-width: 1100px;
      height: 100%;
      overflow-x: auto;
      .view-table-box {
        min-width: 900px;
        height: 100%;
      }
      .view-table-header {
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #ffffff;
        border-bottom: 1px solid #EEEEEE;
        font-weight: 500;
        font-size: 16px;
        color: #222222e6;
        .header-tools {
          display: flex;
          align-items: center;
          .header-tools-date {
            >span {
              font-size: 12px;
              color: #5C5F66;
              margin-right: 8px;
            }
          }
          .header-tools-drop {
            margin-left: 16px;
            .header-tools-drop-text {
              font-size: 12px;
            }
          }
        }
      }
      .view-table-card-box {
        display: flex;
        background: #ffffff;
        flex-direction: column;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: 1px solid #ECECEC;
        // margin-bottom: 16px;
        &:last-child {
          border-bottom: none;
        }
        .view-table-card-box-title {
          height: 52px;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .card-title-left {




            display: flex;
            align-items: center;
            .card-box-title-box {
              width: 24px;
              height: 24px;
              color: #FFFFFF;
              font-size: 12px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 8px;
              i {
                font-size: 12px;
              }
            }
          }
          .card-title-right {
            span {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
            }
          }
        }
        .view-table-cards-title {
          display: flex;
          align-items: center;
          height: 46px;
          background: #F7F7F9;
          border-radius: 4px;
          padding: 0 16px;
          margin-bottom: 16px;
          .cards-title-item {
            display: flex;
            justify-content: space-between;
            margin-right: 32px;
            .cards-title-item-label {
              font-weight: 400;
              font-size: 12px;
              color: #5C5F66;
              line-height: 24px;
              display: flex;
              align-items: center;
            }
            .cards-title-item-value {
              flex: 1;
              font-weight: 400;
              font-size: 14px;
              color: #222222;
              line-height: 24px;
              text-align: right;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              &.success {
                color: #67C23A;
              }
              &.fail {
                color: #F56C6C;
              }
            }
          }
        }
        .view-table-cards {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          &.flexStart {
            align-items: stretch;
          }
          .view-table-card {
            width: calc(33% - 11px);
            border: 1px solid #E6E7E9;
            margin-right: 16px;
            margin-bottom: 16px;
            box-sizing: border-box;
            display: inline-block;
            position: relative;
            border-radius: 4px;
            &.image-card {
              display: flex;
            }
            .image-box {
              width: calc(100% - 367px);
              max-height: 480px;
              padding: 16px;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              flex-direction: column;
              overflow-y: auto;
              img {
                max-width: 80%;
                max-height: 430px;
                margin-bottom: 12px;
                object-fit: contain;
              }
            }
            .image-text {
              width: 360px;
              max-height: 480px;
              margin-left: 16px;
              // color: red;
              .image-text-title {
                font-weight: 500;
                font-size: 14px;
                color: #BCBFC3;
                margin-bottom: 10px;
              }
              .image-text-desc {
                height: calc(100% - 28px);
                font-weight: 400;
                font-size: 12px;
                color: #222222;
                overflow-y: auto;
              }
            }
            .table-card-close {
              width: 14px;
              height: 0;
              float: right;
              margin-right: 12px;
              transform: translateY(10px);
              i {
                color: #222222;
                font-size: 14px;
                cursor: pointer;
              }
            }
            .table-card-content-scroll {
              padding-right: 20px;
              line-height: 2;
            }
            .table-card-content-title {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-bottom: 12px;
            }
            .table-card-content {
              width: 100%;
              // height: 300px;
              background: #ffffff;
              .markdown-string {
                padding: 16px;
                :deep(.markdown-time) {
                  color: #999999;
                }
              }
              :deep(table) {
                width: 100%;
                border-spacing: 0px;
                font-size: 12px;
                color: #222222;
                thead {
                  tr {
                    position: sticky;
                    top: 0;
                    z-index: 5;
                  }
                }
                tr {
                  th:first-child {
                    width: 30%;
                    border-right: 1px solid #ECECEC;
                  }
                  th {
                    // text-align: left;
                    height: 40px;
                    line-height: 40px;
                    // padding-left: 15px;
                    font-weight: 500;
                    color: #5C5F66;
                    background: #F7F7F9;
                  }
                  td:first-child {
                    width: 30%;
                    background: #FEFEFF;
                  }
                  td {
                    // text-align: left;
                    height: 40px;
                    line-height: 40px;
                    // padding-left: 15px;
                    border-top: 1px solid #ECECEC;
                    border-right: 1px solid #ECECEC;
                  }
                }
              }
            }

            .view-table-card-title-box {
              display: flex;
              justify-content: space-between;
              position: relative;
              .view-table-card-title-bg {
                width: 80px;
                height: 10px;
                background: #27b47a33;
                border-radius: 100px 0 0 0;
                position: absolute;
                top: 12px;
              }
              .view-table-card-title {
                font-weight: 500;
                font-size: 14px;
                color: #222222;
                margin-bottom: 20px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .view-table-card-title-btn {
                font-weight: 400;
                font-size: 12px;
                color: #0054D2;
                cursor: pointer;
              }
            }
            .view-table-card-baseInfo {
              .baseInfo-item {
                display: flex;
                justify-content: space-between;
                .baseInfo-item-label {
                  font-weight: 400;
                  font-size: 12px;
                  color: #5C5F66;
                  line-height: 24px;
                  display: flex;
                  align-items: center;
                  margin-right: 16px;
                  .baseInfo-item-label-icon {
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    display: inline-block;
                    margin-right: 4px;
                  }
                }
                .baseInfo-item-value {
                  flex: 1;
                  font-weight: 400;
                  font-size: 12px;
                  color: #222222;
                  line-height: 24px;
                  text-align: right;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  &.info {
                    font-weight: 700;
                    color: #E6A23C;
                  }
                }
              }
              &.info-box {
                height: 190px;
                display: flex;
                justify-content: center;
                align-items: center;
                .info-icon {
                  font-size: 14px;
                  margin-right: 8px;
                }
                .info-text {
                  font-weight: 400;
                  font-size: 12px;
                  color: #5c5f66e6;
                }
              }
            }
            .view-table-card-line {
              height: 0;
              border: 1px dashed #DDDDDD;
              margin: 10px 0;
            }
          }
          .view-table-card:nth-child(3n) {
            margin-right: 0;
          }
          .view-table-card.card4 {
            width: calc(25% - 12px);
            &.loading {
              border: none;
            }
            .table-card-content {
              height: 100%;
              // max-height: 285px;
              .card-box {
                padding: 16px;
                box-sizing: border-box;
              }
              .loading-box {
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                .is-loading {
                  img {
                    width: 40px;
                  }
                }
                span {
                  font-weight: 400;
                  font-size: 12px;
                  color: #BCBFC3;
                  margin-top: 16px;
                }
                .loading-progress {
                  width: 200px;
                  margin-top: 16px;
                  :deep(.el-progress-bar__outer) {
                    background-color: #E9EAEE;
                  }
                  :deep(.el-progress__text) {
                    font-size: 12px !important;
                    color: #222222 !important;
                  }
                }
              }
            }
          }
          .view-table-card.card4:nth-child(3n) {
            margin-right: 16px;
          }
          .view-table-card.card4:nth-child(4n) {
            margin-right: 0;
          }
          .view-table-card.train {
            .baseInfo-item-label-icon {
              background: #3b94e633;
            }
            &::after {
              content: "";
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 5px;
              background: #3B94E6;
              border-radius: 4px 4px 0 0;
            }
            .view-table-card-title-bg {
              background: rgba(59, 148, 230, 0.2);
            }
          }
          .view-table-card.taxi {
            .baseInfo-item-label-icon {
              background: #FFE9C1;
            }
            &::after {
              content: "";
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 5px;
              background: #FFA125;
              border-radius: 4px 4px 0 0;
            }
            .view-table-card-title-bg {
              background: rgba(59, 148, 230, 0.2);
            }
          }
          .view-table-card.addedTax {
            .baseInfo-item-label-icon {
              background: #D4F0E4;
            }
            &::after {
              content: "";
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 5px;
              background: #27B47A;
              border-radius: 4px 4px 0 0;
            }
            .view-table-card-title-bg {
              background: rgba(39, 180, 122, 0.2);
            }
          }
          .view-table-card {
            .baseInfo-item-label-icon {
              background: #D4F0E4;
            }
          }
          .view-table-card.fail {
            &::after {
              content: "";
              position: absolute;
              top: 0;
              right: -0.5px;
              width: calc(100% + 1px);
              height: 5px;
              background: #C1C5CE;
              border-radius: 4px 4px 0 0;
            }
          }
          .block {
            width: 100%;
            margin-right: 0;
            border: none;
            .table-card-content {
              // height: 400px;
              &.http {
                height: auto;
              }
              // &.http-table {
              //   height: 300px;
              // }
            }
          }
          .excel {
            width: 100%;
            margin-right: 0;
            border: none;
            .table-card-content{
              height: 100%;
            }
          }

          .left-table {
            width: calc(35% - 16px);
            height: 440px;
            margin-right: 16px;
            margin-bottom: 20px;
          }
          .right-echarts {
            width: 65%;
            height: 440px;
            margin-bottom: 20px;
            .right-table-text {
              font-weight: 400;
              font-size: 12px;
              color: #5C5F66;
              padding-left: 80px;
              position: absolute;
              .right-table-text-bold {
                font-weight: 500;
                font-size: 16px;
                color: #0054D2;
                margin-left: 8px;
                margin-right: 40px;
              }
            }
            .echarts-box {
              width: 100%;
              height: 100%;
            }
          }
        }
        .view-table-pagination {
          padding: 16px;
          display: flex;
          justify-content: flex-end;
          :deep(.el-pagination.is-background .el-pager li.is-active) {
            background-color: rgba(0, 84, 217, 0.08);
            color: rgba(0, 84, 217, 1);
          }
        }
      }
      .view-table-card-box:last-child {
        margin-bottom: 0;
      }
    }
    .view-left-view {
      width: 280px;
      min-width: 280px;
      max-width: 280px;
      height: 100%;
      background: #ffffff;
      margin-right: 16px;
      position: relative;
      &.width0 {
        width: 0;
        min-width: 0;
        .left-view-header {
          padding: 0;
        }
      }
      .left-view-arrow {
        width: 12px;
        height: 44px;
        color: #BCBFC3;
        background: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: calc(50% - 22px);
        right: -14px;
        transform: perspective(5px) rotateY(5deg);
        transform-origin: left;
        border-radius: 0 6px 6px 0;
        cursor: pointer;
      }
      .left-view-header {
        height: 56px;
        font-weight: 500;
        font-size: 16px;
        padding: 0 16px;
        color: #222222e6;
        display: flex;
        align-items: center;
        overflow: hidden;
      }
      .left-view-list {
        height: calc(100% - 56px);
        overflow-y: auto;
        .left-view-list-item {
          padding: 16px;
          box-sizing: border-box;
          border-bottom: 1px solid #EEEEEE;
          cursor: pointer;
          .list-item-title {
            font-weight: 500;
            font-size: 14px;
            color: #222222e6;
            margin-bottom: 10px;
          }
          .list-item-tag-node {
            margin-bottom: 10px;
            .list-item-tag-node-text {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-left: 10px;
            }
          }
          .list-item-nextTime {
            font-weight: 400;
            font-size: 12px;
            color: #5C5F66;
            letter-spacing: 0;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            .list-item-nextTime-icon {
              display: inline-block;
            }
            .list-item-nextTime-text {
              margin-left: 10px;
            }
          }
          .list-item-tooltip {
            font-weight: 400;
            font-size: 12px;
            color: #E6A23C;
            letter-spacing: 0;
          }
          &:hover {
            background: rgba(0, 84, 210, 0.06);
          }
          &.active {
            background: rgba(0, 84, 210, 0.06);
            border-left: 3px solid #0054D2;
          }
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
    }
    .view-right-view {
      width: 350px;
      max-width: 350px;
      min-width: 350px;
      height: 100%;
      background: #ffffff;
      margin-left: 16px;
      // transition: width 0.5s;
      position: relative;
      &.width0 {
        width: 0;
        min-width: 0;
        .tabs-item {
          &.tabs-item-active {
            &::after {
              content: '';
              width: 0 !important;
            }
          }
        }
      }
      .right-view-arrow {
        width: 12px;
        height: 44px;
        color: #BCBFC3;
        background: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: calc(50% - 22px);
        left: -14px;
        transform: perspective(5px) rotateY(-5deg);
        transform-origin: right;
        border-radius: 6px 0 0 6px;
        cursor: pointer;
      }
      .right-view-header {
        height: 40px;
        padding: 8px 16px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        border-bottom: 1px solid #e8ecf0;
        white-space: nowrap;
        overflow: hidden;
        .view-header-close {
          cursor: pointer;
        }
      }
      .right-view-tab {
        height: 56px;
        border-bottom: 1px solid #EEEEEE;
        display: flex;
        justify-content: space-between;
        overflow: hidden;
        .tabs-number {
          display: inline-flex;
          height: 100%;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          color: #BCBFC3;
          margin-right: 16px;
        }
        .tabs {
          display: inline-flex;
          height: 100%;
          align-items: center;
          .tabs-item {
            font-weight: 400;
            font-size: 16px;
            color: #222222;
            padding: 16px;
            cursor: pointer;
            &.tabs-item-active {
              font-weight: 500;
              color: #0054D2;
              position: relative;
              cursor: pointer;
              &::after {
                content: '';
                width: 100%;
                height: 2px;
                background: #0054D2;
                position: absolute;
                bottom: 0;
                left: 0;
              }
            }
          }
        }
      }
      .right-view-box {
        height: 100%;
        .monitor-box {
          padding: 16px;
          .el-timeline {
            padding-left: 0;
          }
          .el-timeline-item {
            padding-bottom: 10px;

            &.error-tail{
              ::v-deep(.el-timeline-item__tail) {
                border-left: 2px solid #f6693e;
              }
              .time-content{
                border: 1px solid #f6693e;
              }
            }
            &.loading-tail{
              ::v-deep(.el-timeline-item__tail) {
                border-left: 2px solid #0099CB;
              }
              .time-content{
                border: 1px solid #0099CB;
              }
            }
            &.waiting-tail{
              ::v-deep(.el-timeline-item__tail) {
                border-left: 2px solid #9e9e9e;
              }
              .time-content{
                border: 1px solid #9e9e9e;
              }
            }
          }
          .time-content {
            // width: 100%;
            border-radius: 2px;
            padding: 16px;
            box-sizing: border-box;
            // border: 1px solid #E6E7E9;
            border: 1px solid #17B26A;
            border-radius: 8px;
            margin-left: 16px;
            .time-title-box {
              display: flex;
              align-items: center;
              .time-title-box-left {
                flex: 1;
                display: flex;
                align-items: center;
                .time-title-icon {
                  width: 24px;
                  height: 24px;
                  color: #FFFFFF;
                  font-size: 12px;
                  border-radius: 6px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 8px;
                  i {
                    font-size: 12px;
                  }
                }
                .time-title {
                  font-size: 14px;
                  color: #222222;
                  font-weight: 500;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
              .open-file {
                color: #0054D2;
                cursor: pointer;
              }
            }
            .content-item {
              // margin-bottom: 10px;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              padding: 8px 0;
              border-radius: 4px;
              // background: #F5F5F5;
              line-height: 18px;
              .item-title {
                width: 100%;
                display: flex;
                align-items: flex-start;
                color: #5C5F66;
                .complete-content {
                  font-size: 12px;
                  color: #5C5F66;
                  line-height: 1.5;
                  word-wrap: break-word;
                  word-break: break-word;
                  white-space: pre-line;
                  overflow-wrap: break-word;
                  max-width: 100%;
                  min-height: 18px;
                }
              }
            }
          }
          ::v-deep(.el-timeline-item__wrapper) {
            padding-left: 16px;
            .el-timeline-item__content {
                margin-top: 38px;
            }
          }
          ::v-deep(.el-timeline-item__tail) {
            top: 12px;
            border-left: 2px solid #17B26A;
          }
          ::v-deep(.el-timeline-item__dot) {
            width: 100%;
            justify-content: flex-start;
            z-index: 1;
            .time-icon-box {
              padding: 5px 0;
              background: #ffffff;
              margin-left: -4px;
              .time-icon {
                width: 12px;
                height: 12px;
                background: #17B26A;
                border: 3px solid #E8F7F0;
                border-radius: 50%;
                // flex-shrink: 0;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                box-sizing: content-box;
                .time-icon-select {
                  font-size: 10px;
                  color: #FFFFFF;
                }
                &.error {
                  background: #ef3f09;
                  border: 3px solid #f8c6b8;
                }
                &.loading {
                  background: #0099CB;
                  border: 3px solid #E8F7F0;
                }
                &.waiting {
                  background: #9e9e9e;
                  border: 3px solid #dcfbec;
                }
              }
            }
            .icon-execute {
              border: 2px solid #0099CB;
            }
            .time-box {
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 32px;
              &:hover {
                //background-color: #F2F2F2;
                //border-radius: 2px;
              }
              .time-left {
                width: 100%;
                display: flex;
                align-items: center;
                cursor: pointer;
                .time-time {
                  font-size: 12px;
                  color: #B0B3C0;
                  margin-left: 16px;
                  flex-shrink: 0;
                }
                .isover {
                  color: #0099CB;
                }
                i {
                  color: rgba(255, 255, 255, .9);
                  margin-left: 10px;
                }
              }
            }
            .time-right-btn{
              margin-right: 8px;
              cursor: pointer;
              span {
                font-size: 12px;
                color: #0099CB;
                .el-icon-loading {
                  font-size: 14px;
                  margin-right: 8px;
                }
              }
            }
          }

          // 执行结果样式
          .file-item {
            padding: 16px;
            border: 1px solid #E6E7E9;
            border-radius: 4px;
            margin-bottom: 16px;
            &:last-child {
              margin-bottom: 0;
            }
            .file-item-title {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              img {
                width: 24px;
                height: 24px;
              }
              .file-item-title-text {
                max-width: calc(100% - 80px);
                font-weight: 400;
                font-size: 14px;
                color: #222222;
                margin-left: 8px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .file-item-title-tag {
                width: 40px;
                height: 22px;
                background: #F7F7F9;
                border-radius: 11px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                margin-left: 8px;
                &.online {
                  color: #1E39C3;
                  background: #F0F5FF;
                }
              }
            }
            .file-item-size {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              padding-left: 32px;
              .file-item-size-text {
                margin-right: 14px;
              }
            }
            .file-item-path {
              padding-left: 32px;
              margin-bottom: 12px;
              .file-item-path-text {
                background: #F7F7F9;
                border-radius: 4px;
                padding: 10px;
                box-sizing: border-box;
                font-weight: 400;
                font-size: 12px;
                color: #BCBFC3;
              }
            }
            .file-item-buttons {
              display: flex;
              justify-content: flex-end;
              .file-item-botton {
                display: inline-flex;
                align-content: center;
                justify-content: center;
                padding: 8px 16px;
                box-sizing: border-box;
                background: #FFFFFF;
                border: 1px solid #E6E7E9;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 16px;
                font-size: 12px;
                &.disabled {
                  color: #a8abb2;
                  border-color: 1px solid #e4e7ed;
                  cursor: not-allowed;
                }
                &:not(.disabled):hover {
                  color: #0054D2;
                  background: rgba(0, 84, 217, 0.08);
                  border: 1px solid #0054D2;
                }
                &.primary {
                  color: #0054D2;
                  border: 1px solid #0054D2;
                }
              }
              .file-item-botton:last-child {
                margin-right: 0;
              }
              .file-item-btn {
                display: inline-flex;
                align-items: center;
                align-content: center;
                justify-content: center;
                // padding: 8px 16px;
                box-sizing: border-box;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 32px;
                font-size: 12px;
                color: #0054D2;
                float: left;
                i {
                  font-size: 12px;
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }
    }
    .view-right-btn {
      position: fixed;
      top: 24px;
      right: 24px;
      cursor: pointer;
    }
  }
}
.text-clamp-2 {
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
// values are default one from jv-light template
// 自定义高亮json样式
.my-awesome-json-theme {
  border: none;
  white-space: nowrap;
  color: #333333;
  font-size: 14px;
  font-family: Consolas, Menlo, Courier, monospace;
  font-weight: 600;
  width: 100%;

  &.boxed {
    box-sizing: border-box;
    border: none;
    border-top: 1px solid #e5e5e5;
    border-radius: 0;

    &:hover {
      box-shadow: none;
      border: none;
      border-top: 1px solid #e5e5e5;
    }
  }

  :deep(.jv-tooltip.right) {
    right: 6px;
    font-weight: normal;
    font-size: 12px;
  }

  :deep(.jv-code) {
    padding: 15px 10px;
    overflow: auto;

    .jv-toggle {
      &:before {
        padding: 0px 2px;
        border-radius: 2px;
      }

      &:hover {
        &:before {
          background: #eee;
        }
      }
    }

    .jv-node {
      line-height: 20px;
    }

    .jv-ellipsis {
      color: #999;
      background-color: #eee;
      display: inline-block;
      line-height: 0.9;
      font-size: 0.9em;
      padding: 0px 4px 2px 4px;
      border-radius: 3px;
      vertical-align: 2px;
      cursor: pointer;
      user-select: none;
    }

    .jv-button {
      color: #49b3ff;
    }

    .jv-key {
      color: #92278f;
      margin-right: 8px;
    }

    .jv-item {
      &.jv-array {
        color: #111111;
      }

      &.jv-boolean {
        color: #fc1e70;
      }

      &.jv-function {
        color: #067bca;
      }

      &.jv-number {
        color: #067bca;
      }

      &.jv-number-float {
        color: #067bca;
      }

      &.jv-number-integer {
        color: #067bca;
      }

      &.jv-object {
        color: #111111;
      }

      &.jv-undefined {
        color: #e08331;
      }

      &.jv-string {
        color: #3ab54a;
        word-break: break-word;
        white-space: normal;
      }
    }
  }
}
.log-loading{
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  padding: 8px 8px 0 0;
  box-sizing: border-box;
}
.retry-count {
  font-size: 12px;
  color: #e6a23c;
  margin-left: 8px;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>
