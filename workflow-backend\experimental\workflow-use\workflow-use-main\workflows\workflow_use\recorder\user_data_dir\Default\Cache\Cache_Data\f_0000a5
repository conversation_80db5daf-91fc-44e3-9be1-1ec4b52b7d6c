(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-1f7c93c6"],{6481:function(t,e,s){"use strict";s.r(e);var i=(s("d9e2"),s("14d9"),s("e9f5"),s("910d"),s("7d54"),s("ab43"),s("88a7"),s("271a"),s("5494"),s("f778")),n=s("c1df"),a=s.n(n),o=s("037d"),l={mixins:[i.a],name:"ps_dispatching",props:{isFullScreen:{type:Boolean,default:!1},agent:{type:String,default:null}},data:()=>({loading:!1,answering:!1,templateIndex:0,templates:[],params:{scrollEnd:!1,isShowScrollBottom:!1,time:null,loading:!1,searchVal:null,answerList:[],keyword:null,fileList:[],sessionId:null,qrLoading:!1,qaRecommendationList:[],historyList:[],historyListVis:!1,suggestedMessage:[]}}),computed:{chatHistoryList(){let t=this.params.historyList.filter((t=>!this.params.searchVal||t.title.indexOf(this.params.searchVal)>-1));const e={"当天":[],"最近7天":[]};return t.forEach((t=>{const s=a()().add(-7,"d").format("YYYY-MM-DD"),i=a()(t.created).isSame(a()(),"day");i?e["当天"].push(t):!i&&a()().diff(s,"day")<=7&&e["最近7天"].push(t)})),e},chatHistoryListLen(){let t=0;return Object.values(this.chatHistoryList).forEach((e=>{t+=e.length})),t}},watch:{"params.answerList":{immediate:!0,handler(){this.onResetScrollbar(),this.getListSession()}},"params.keyword":{immediate:!0,handler(t){t||(this.tools.visible=!1)}}},mounted(){const t=this.aiTemplate.filter((t=>9===t.type));t.length&&(this.templates=t,this.params.qaRecommendationList=t.map((t=>t.content||t.name))),this.params.qaRecommendationList.length>6&&(this.params.qaRecommendationList=this.params.qaRecommendationList.slice(0,6)),this.scrollBind()},activated(){this.onScrollBottom(),this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_enter`,path:"排水调度",functionName:"进入页面"})},methods:{uploadExceed(t,e){this.$set(e[0],"raw",t[0]),this.$set(e[0],"name",t[0].name),this.$refs.fileUploader.clearFiles(),this.$refs.fileUploader.handleStart(t[0])},getRecommend(t=!1){this.params.qrLoading||(t&&(this.params.qrLoading=!0),this.templates.length>6&&this.params.qaRecommendationList.length>=6?this.templateIndex++:this.templateIndex=0,this.params.qaRecommendationList=this.templates.slice(6*this.templateIndex,6*this.templateIndex+6).map((t=>t.content||t.name)),this.params.qrLoading=!1)},getListSession(){this.hideHistory||this.$saasApi.AIListSession({conditions:[{Field:"type",Value:this.agent,Operate:"=",Relation:"and"},{Field:"created",Value:a()().add(-8,"d").format("YYYY-MM-DD 00:00:00"),Operate:">=",Relation:"and"}]}).then((t=>{t&&Array.isArray(t)&&(t.sort(((t,e)=>Date.parse(e.created)-Date.parse(t.created))),this.params.historyList=t.filter((t=>t.title)).map((t=>(t.isHandlerOpen=!1,t.isEdit=!1,t.rename=t.title,t))))}))},selectQa(t){this.params.keyword=t,this.getAnswer()},onFold(t,e,s=""){var i,n;this.$set(t,e,!t[e]),"fold"===e&&this.$set(t,"thinking_hide",t.fold),"parent"===s&&null!=t&&null!==(i=t.content)&&void 0!==i&&i.result_list.length&&this.$set(t,"reference",t.fold),"parent"===s&&null!=t&&null!==(n=t.content)&&void 0!==n&&null!==(n=n.metadata)&&void 0!==n&&null!==(n=n.retriever_resources)&&void 0!==n&&n.length&&this.$set(t,"resources",t.fold)},getHistory(){this.aiModelVisible?(this.params.historyListVis=!0,this.$nextTick((()=>{this.aiModelVisible=!1}))):this.params.historyListVis=!0},getNewAnswer(){if(this.aiModelVisible&&(this.aiModelVisible=!1),this.onMixinsTextToVoiceStop(null,this.params.answerList),this.onAbortAnswer(),this.params.historyListVis=!1,this.answering=!1,this.params.sessionId=null,this.params.keyword=null,this.params.answerList.length>0){let t=this.params.answerList.filter((t=>{var e;return"user"===t.role&&(null==t||null===(e=t.inputs)||void 0===e||null===(e=e.file)||void 0===e?void 0:e.url)}));t.forEach((t=>{URL.revokeObjectURL(t.inputs.file.url)}))}if(this.params.answerList=[],this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.params.fileList=[],this.$refs.fileUploader.clearFiles()},onSelectHistory(t){this.onMixinsTextToVoiceStop(null,this.params.answerList),this.onAbortAnswer(),this.params.historyListVis=!1,this.params.suggestedMessage=[],this.loading=!0,this.$saasApi.AIPageSessionRecord({conditions:[{Field:"sessionId",Value:t.id,Operate:"=",Relation:"and"}],order:[],index:1,size:9999}).then((e=>{e&&e.rows&&(this.params.sessionId=t.id,this.params.keyword=null,this.params.answerList=e.rows.map((t=>{let e=t.content;try{var s;if(e=JSON.parse(e),"menu"!==e.type&&"video"!==e.type||null===(s=e.content)||void 0===s||!s.length){var i;e.answer=e.content||"抱歉！我找不到相关信息。",e.result_list||(e.result_list=[]),(e.result_list||[]).forEach((t=>{t.id=Object(o.g)()}));const t={};((null===(i=e)||void 0===i||null===(i=i.metadata)||void 0===i?void 0:i.retriever_resources)||[]).forEach((e=>{t[e.document_id]?t[e.document_id].content=t[e.document_id].content+"\n"+e.content:t[e.document_id]=e})),Object.keys(t).length&&(e.metadata.retriever_resources=Object.values(t)),e.suggestedMessage&&(this.params.suggestedMessage=e.suggestedMessage)}else e.answer="以下是为您找到的相关信息：",e.menu=e.content}catch(s){e={answer:t.content||"抱歉！我找不到相关信息。",result_list:[]}}if("user"===t.role&&t.inputs)try{t.inputs=JSON.parse(t.inputs)}catch(t){}return{...t,content:"assistant"===t.role?e:t.content,recordId:t.id,thinking_hide:!0}})),setTimeout((()=>{this.$refs.scrollbar.wrap.scrollTop=this.$refs.scrollbar.wrap.scrollHeight}),10))})).catch((t=>{this.$message.error("获取历史对话信息失败！")})).finally((()=>{this.loading=!1}))},onKeydownModelSelect(t){const e=this.tools.selection-1,s=this.params.keyword.substring(0,e),i=this.params.keyword.substring(e+("@"+this.tools.keyword).length);this.params.keyword=s+"@"+t.name+i+" ",this.$refs.input.focus(),this.tools.visible=!1},Keydown(t){t.shiftKey||13!==t.keyCode||(t.cancelBubble=!0,t.stopPropagation(),t.preventDefault(),this.getAnswer())},onUploadFileQuest(t){this.getAnswerNoFile(t.file)},async getAnswerNoFile(t){var e;let s=this.params.keyword;this.params.keyword=null,this.params.scrollEnd=!0,this.params.suggestedMessage=[],this.onMixinsTextToVoiceStop(null,this.params.answerList),null===(e=this.suggestionController)||void 0===e||e.abort(),this.isMobile&&this.$refs.input.blur();let i={file:null};if(t&&(i.file={type:t.type.indexOf("image")>-1?"image":"document",fileName:t.name,url:URL.createObjectURL(t)}),this.params.answerList.push({role:"user",content:s||"",inputs:i}),this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_send`,path:"排水调度",functionName:"发送内容",param:s}),s&&/^(点击|进入|刷新|切换|选择|取消选择|勾选|取消勾选|取消)/.test(s.trim())){const t={"点击":"click","刷新":"refresh","进入":"click","切换":"switch","选择":"select","取消选择":"unselect","勾选":"check","取消勾选":"uncheck","取消":"cancel"}[s.trim().match(/^(点击|进入|刷新|切换|选择|取消选择|勾选|取消勾选|取消)/)[0]];window.parent.postMessage("*#hd#*"+JSON.stringify({action:"AI_ASSISTANT_SPECIAL_COMMAND",params:{content:s,type:t}}),"*")}else this.answering=!0,setTimeout((async()=>{const e=Object(o.g)();let i="web";this.isMobile?i="app":this.isUniwimPc&&(i="desk");let n={id:e,role:"assistant",loading:!0,question:s||"",content:{thinking:"",answer:"",result_list:[],metadata:{}},modelType:this.aiModelType,recordId:null,controller:new AbortController,controllerType:null,answerTimer:null,answerDateNow:Date.now(),thinking_result:null,isVoiceSpeaking:!1,firstText:null,firstTextIsGet:!1};this.params.answerList.push(n),this.$saasApi.AISend({content:s||"",sessionId:this.params.sessionId,file:t,applicationType:i,appCode:this.agent,type:this.agent},n.controller).then((async t=>{if(!t.ok)throw new Error;let e=null,s=null;"TextDecoderStream"in window?e=t.body.pipeThrough(new TextDecoderStream).getReader():(e=t.body.getReader(),s=new TextDecoder);let i=!0,a=null;for(;i;){const{done:t,value:c}=await e.read();if(t){var o,l;if(console.warn("done",n),i=!1,this.params.loading=!1,n.loading=!1,!n.content.answer&&this.$set(n,"content",{thinking:null==n||null===(o=n.content)||void 0===o?void 0:o.thinking,answer:"抱歉！我找不到相关信息。",result_list:[]}),n.content.suggestedMessage&&n.content.suggestedMessage.length?this.params.suggestedMessage=n.content.suggestedMessage.slice(0,3):(this.suggestionController=new AbortController,n.recordId&&this.getMixinsSuggestedMessage(n.recordId,this.suggestionController.signal)),n.content.outputs){var r;const t=/```json([\s\S]*?)```/,e=null===(r=n.content.outputs.match(t))||void 0===r||null===(r=r[1])||void 0===r?void 0:r.trim();if(!e)return;window.parent.postMessage(JSON.parse(e),"*"),console.log("发送排水数据",e)}if(this.onResetScrollbar(),!this.hideVoicePlay&&(null===(l=n.content)||void 0===l||null===(l=l.menu)||void 0===l||!l.length)&&this.hideToolsBtn(n.content.answer)&&n.content.answer&&this.isVoiceAutoPlay(this.agent)){console.warn("自动播放音频");let t=this.params.answerList.findIndex((t=>t.id===n.id));this.onMixinsTextToVoicePlay(n,t,this.params.answerList)}break}let d=s?s.decode(c,{stream:!0}):c,u=this.chunkData(d);this.setAnswerData(n,u,a),a=u}})).catch((t=>{var e;if(n.loading=!1,"当前访问人数过多，请稍候再试!"===(null==t?void 0:t.message))!n.content.answer&&this.$set(n,"content",{thinking:null==n||null===(e=n.content)||void 0===e?void 0:e.thinking,answer:"当前访问人数过多，请稍候再试!",result_list:[]});else if("abort"===n.controllerType){var s;!n.content.answer&&this.$set(n,"content",{thinking:null==n||null===(s=n.content)||void 0===s?void 0:s.thinking,answer:"已中止查询相关信息。",result_list:[]})}else{var i;this.$set(n,"content",{thinking:null==n||null===(i=n.content)||void 0===i?void 0:i.thinking,answer:"抱歉！我找不到相关信息。",result_list:[]})}this.onResetScrollbar()})).finally((()=>{n.controller=null,n.controllerType=null,this.answering=!1,this.getListSession()}))}),100)},onRegenerate(t,e){var s;if(!t.recordId||this.answering)return;this.onMixinsTextToVoiceStop(t),null===(s=this.suggestionController)||void 0===s||s.abort(),this.answering=!0,this.params.suggestedMessage=[];const i=t.recordId;this.$set(t,"content",{thinking:"",answer:"",result_list:[],metadata:{}}),this.$set(t,"isLike",null),this.$set(t,"thinking_hide",null),this.$set(t,"loading",!0),this.$set(t,"controller",new AbortController),this.$set(t,"controllerType",null),this.$set(t,"answerTimer",null),this.$set(t,"answerDateNow",Date.now()),this.$set(t,"thinking_result",null),this.$set(t,"isVoiceSpeaking",!1),this.$set(t,"firstText",null),this.$set(t,"firstTextIsGet",!1),this.aiModelType!==t.modelType&&this.$set(t,"modelType",this.aiModelType);const n=(this.params.answerList[e-1]||{}).content||"";this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_regenerator`,path:"排水调度",functionName:"重新生成",param:t.question?t.question:n}),this.$saasApi.AIRegeneratorMessage({recordId:i},t.controller).then((async e=>{if(!e.ok)throw new Error;let s=null,i=null;"TextDecoderStream"in window?s=e.body.pipeThrough(new TextDecoderStream).getReader():(s=e.body.getReader(),i=new TextDecoder);let n=!0,a=null;for(;n;){const{done:e,value:c}=await s.read();if(e){var o,l;if(console.warn("done"),n=!1,this.params.loading=!1,t.loading=!1,!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(o=t.content)||void 0===o?void 0:o.thinking,answer:"抱歉！我找不到相关信息。",result_list:[]}),t.content.suggestedMessage&&t.content.suggestedMessage.length?this.params.suggestedMessage=t.content.suggestedMessage.slice(0,3):(this.suggestionController=new AbortController,t.recordId&&this.getMixinsSuggestedMessage(t.recordId,this.suggestionController.signal)),t.content.outputs){var r;const e=/```json([\s\S]*?)```/,s=null===(r=t.content.outputs.match(e))||void 0===r||null===(r=r[1])||void 0===r?void 0:r.trim();if(!s)return;window.parent.postMessage(JSON.parse(s),"*")}if(this.onResetScrollbar(),!this.hideVoicePlay&&(null===(l=t.content)||void 0===l||null===(l=l.menu)||void 0===l||!l.length)&&this.hideToolsBtn(t.content.answer)&&this.isVoiceAutoPlay(this.agent)){console.warn("自动播放音频");let e=this.params.answerList.findIndex((e=>e.id===t.id));this.onMixinsTextToVoicePlay(t,e,this.params.answerList)}break}let d=i?i.decode(c,{stream:!0}):c,u=this.chunkData(d);this.setAnswerData(t,u,a),a=u}})).catch((e=>{var s;if(t.loading=!1,"当前访问人数过多，请稍候再试!"===(null==e?void 0:e.message))!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(s=t.content)||void 0===s?void 0:s.thinking,answer:"当前访问人数过多，请稍候再试!"});else if("abort"===t.controllerType){var i;!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(i=t.content)||void 0===i?void 0:i.thinking,answer:"已中止查询相关信息。"})}else{var n;!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(n=t.content)||void 0===n?void 0:n.thinking,answer:"抱歉！我找不到相关信息。"})}this.answering=!1,this.params.loading=!1,this.onResetScrollbar()})).finally((()=>{this.answering=!1,this.$set(t,"loading",!1),this.$set(t,"controller",null),this.$set(t,"controllerType",null),this.params.loading=!1,this.onResetScrollbar(),this.getListSession()}))},setAnswerData(t,e,s){let i=null,n=Object(o.g)();t.isInCodeBlock=t.isInCodeBlock||!1;for(let o=0;o<e.length;o++){try{if(this.params.loading=!0,e[o].endsWith("\n\n")&&(e[o]=e[o].slice(0,-2)),i=JSON.parse(e[o]).data,"string"==typeof i&&(i=JSON.parse(i)),"thinking"===i.type)t.content.thinking+=i.content;else if("text"===i.type){const e=this.filterCodeBlocks(i.content,t.isInCodeBlock);t.content.answer+=e.processed,t.isInCodeBlock=e.isInCodeBlock,!t.thinking_hide&&this.$set(t,"thinking_hide",!0)}else if("menu"===i.type||"video"===i.type){var a;null!==(a=i.content)&&void 0!==a&&a.length&&(t.content.answer="以下是为您找到的相关信息：",t.content.menu=i.content,1===i.content.length&&this.onMixinsOpenMenu(i.content[0]))}if(i.echart&&(t.content.echart=i.echart),i.suggestedMessage&&(t.content.suggestedMessage=i.suggestedMessage),i.metadata){var l;const e={};((null===(l=i.metadata)||void 0===l?void 0:l.retriever_resources)||[]).forEach((t=>{e[t.document_id]?e[t.document_id].content=e[t.document_id].content+"\n"+t.content:e[t.document_id]=t})),Object.keys(e).length&&(i.metadata.retriever_resources=Object.values(e)),t.content.metadata=i.metadata}var r;i.result_list&&(t.content.result_list=i.result_list.map((t=>(!t.id&&(t.id=n),t)))),"message_end"===i.event&&(t.content.outputs=null===(r=i.data)||void 0===r||null===(r=r.outputs)||void 0===r?void 0:r.answer),i.sessionId&&(this.params.sessionId=i.sessionId),t.loading=!0,t.recordId=i.recordId||i.id,t.modelType!==i.modelType&&i.modelType&&(t.modelType=i.modelType)}catch(t){s&&s.length>0&&(e[o]&&!e[o].startsWith("{")&&(0===o?(e[o]=(s[s.length-1]||"")+e[o],o=-1):(e[o]=e[o-1]+e[o],o--)),s=e)}this.onResetScrollbar()}},onAbortAnswer(){var t;if(!this.answering)return;null===(t=this.suggestionController)||void 0===t||t.abort();const e=this.params.answerList[this.params.answerList.length-1];e&&e.loading&&e.controller&&(e.controllerType="abort",e.controller.abort(),this.answering=!1,this.params.loading=!1)},getAnswer(){if(!this.answering&&this.params.keyword){if(this.tools.visible=!1,this.params.fileList.length)return this.params.fileList.forEach((t=>{t.status="ready"})),this.$refs.fileUploader&&(this.$refs.fileUploader.uploadFiles=this.params.fileList),this.$refs.fileUploader.submit(),void setTimeout((()=>{if(this.params.keyword=null,this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.params.fileList=[]}),200);this.params.keyword&&this.getAnswerNoFile()}},onFileUploadChange(t,e){e.forEach((t=>{t.raw.type.startsWith("image/")?t.type="image":t.type="document",t.url=URL.createObjectURL(t.raw)})),this.params.fileList=e,!this.isMobile&&this.$refs.input.focus()},onFileDelete(){if(this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.$refs.fileUploader.clearFiles(),this.params.fileList=[]},onHandlerHistoryDelete(t){this.$confirm("确定删除 "+t.title,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$saasApi.AIDeleteSession(t.id).then((e=>{this.$message.success("删除成功"),this.getListSession(),this.params.sessionId===t.id&&this.getNewAnswer()})).catch((()=>{this.$message.error("删除失败")}))}))},onMouseEnter(t){this.params.time&&clearTimeout(this.params.time)},onMouseLeave(t){this.isMobile||(this.params.time&&clearTimeout(this.params.time),this.params.time=setTimeout((()=>{t.isHandlerOpen=!1}),300))},onMapLocation(t){window.parent.postMessage({action:"mapMethod",params:{method:"locatePointByProperty",args:JSON.stringify({name:t})}},"*")},handleLocationClick(t){if("BUTTON"===t.target.tagName&&t.target.classList.contains("operate-btn")){const e=t.target.innerText;this.onMapLocation(e),t.stopPropagation()}}}},r=l,c=(s("e287"),s("f965"),s("2877")),d=Object(c.a)(r,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"comp-box",class:{fullscreen:t.isFullScreen,"is-mobile":t.isMobile}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"content-area"},[t.isMobile?e("div",{staticClass:"header"},[e("div",{staticClass:"left"},[e("div",{staticClass:"back-button",on:{click:t.onMixinsBack}},[e("i",{staticClass:"el-icon-arrow-left"})])]),e("div",{staticClass:"right"},[t.agentIntroduce?e("introduce",{attrs:{"agent-introduce":t.agentIntroduce,"is-mobile":t.isMobile,"custom-title":t.customTitle}}):t._e(),t.hideHistory?t._e():e("div",{staticClass:"right-item",on:{click:function(e){return t.getHistory()}}},[e("img",{attrs:{src:t.icon.chat_history,alt:""}})]),e("div",{staticClass:"right-item",on:{click:function(e){return t.getNewAnswer()}}},[e("img",{attrs:{src:t.icon.chat_new1,alt:""}})])],1)]):t._e(),e("div",{staticClass:"top",staticStyle:{overflow:"hidden"}},[e("transition",{attrs:{name:"el-fade-in"}},[t.audioLoading?e("div",{staticClass:"scroll-top-voice-loading",class:{fixed:t.isMobile}},[e("i",{staticClass:"el-icon-loading"}),e("span",[t._v("正在生成朗读语音")]),e("span",{staticClass:"stop",on:{click:function(e){return t.onMixinsTextToVoiceStop()}}},[e("i",{staticClass:"iconfont icon-zhongzhi1"}),t._v("中止")])]):t._e()]),e("el-scrollbar",{ref:"scrollbar",staticStyle:{height:"100%",width:"100%"},attrs:{native:t.isMobile,"wrap-style":"overflow-x:hidden;"}},[t.params.answerList.length?e("div",{staticClass:"answer-content-list"},[t._l(t.params.answerList,(function(s,i){var n,a,o,l,r,c,d,u,h,p,m,g,v,f,w;return e("div",{key:i,staticClass:"answer-content-item",class:{"is-send":"user"===s.role}},["user"===s.role?[null!=s&&null!==(n=s.inputs)&&void 0!==n&&null!==(n=n.file)&&void 0!==n&&n.url?e("div",{staticClass:"content is-file"},["image"===(null===(a=s.inputs.file)||void 0===a?void 0:a.type)?e("el-image",{staticClass:"image",attrs:{src:null===(o=s.inputs.file)||void 0===o?void 0:o.url,"preview-src-list":[null===(l=s.inputs.file)||void 0===l?void 0:l.url]}}):e("div",{staticClass:"document",on:{click:function(e){var i;return t.onMixinsOpenFile(null===(i=s.inputs.file)||void 0===i?void 0:i.url)}}},[e("img",{attrs:{src:t.icon.link,alt:""}}),e("span",[t._v(t._s(t.shortenText((null===(r=s.inputs.file)||void 0===r?void 0:r.fileName)||(null===(c=s.inputs.file)||void 0===c?void 0:c.url),13,13)))])])],1):t._e(),s.content?e("div",{staticClass:"content"},[e("span",[t._v(t._s(s.content))])]):t._e()]:"assistant"===s.role?[e("img",{staticClass:"avatar answer-avatar",attrs:{src:t.aiLogo||t.formatAnswerAvatar(s).icon,alt:""}}),e("div",{staticClass:"content",class:{fold:s.fold}},[e("div",{staticClass:"content-title"},[t._v(" "+t._s(t.aiName||t.formatAnswerAvatar(s).name)+" ")]),(s.loading,t._e()),e("span",{staticClass:"content-result",on:{click:function(e){return e.stopPropagation(),t.handleLocationClick.apply(null,arguments)}}},[null!==(d=s.content.thinking)&&void 0!==d&&d.trim()?e("div",{staticClass:"deepseek-thinking"},[e("div",{staticClass:"thinking-status",on:{click:function(e){return t.$set(s,"thinking_hide",!s.thinking_hide)}}},[t._v(" "+t._s(s.content.answer?"已深度思考":"思考中...")+" "),s.thinking_hide?e("i",{staticClass:"el-icon-arrow-down"}):e("i",{staticClass:"el-icon-arrow-up"})]),e("div",{directives:[{name:"show",rawName:"v-show",value:!s.thinking_hide,expression:"!item.thinking_hide"}],staticClass:"thinking-result"},[e("vue-markdown",{attrs:{source:s.content.thinking}})],1)]):t._e(),s.content.answer?e("vue-markdown",{ref:"markdownRef_"+i,refInFor:!0,attrs:{source:t.formatContent(s.content.answer,"markdownRef_"+i,i)}}):t._e(),s.content.answer&&s.content.echart&&!s.loading&&s.content.echart.length?t._l(s.content.echart,(function(s,n){return t.formatEchartOptions(s)?e("div",{staticClass:"ai-echart"},[e("hd-chart",{key:i+"_"+n,ref:"aiEchart"+i+"_"+n,refInFor:!0,attrs:{options:t.formatEchartOptions(s),"auto-resize":""}})],1):t._e()})):t._e(),null!==(u=s.content)&&void 0!==u&&null!==(u=u.menu)&&void 0!==u&&u.length?e("div",{staticClass:"toolbar-suggestion content-result-menu"},[e("transition-group",{attrs:{name:"list",tag:"div"}},t._l(null===(h=s.content)||void 0===h?void 0:h.menu,(function(s,i){return e("div",{key:i,staticClass:"suggestion",style:{"--delay":.2*i+"s"},on:{click:function(e){return t.onMixinsOpenMenu(s)}}},[s.fullName?e("span",{staticClass:"more"},[t._v(t._s(t.formatFullName(s.fullName)))]):[s.applicationName||s.pname?e("span",{staticClass:"more"},[t._v(t._s(s.applicationName?s.applicationName+" / ":"")+t._s(s.pname?s.pname+" / ":""))]):t._e(),e("span",[t._v(t._s(s.name))])],e("div",{staticClass:"item-right-arrow"})],2)})),0)],1):t._e(),s.loading?e("i",{staticClass:"el-icon-loading"}):t._e()],2),s.loading?t._e():[!t.isWimpic&&null!=s&&null!==(p=s.content)&&void 0!==p&&null!==(p=p.metadata)&&void 0!==p&&null!==(p=p.retriever_resources)&&void 0!==p&&p.length?e("div",{directives:[{name:"show",rawName:"v-show",value:!s.fold,expression:"!item.fold"}],staticClass:"content-reference"},[e("div",{staticClass:"content-reference-tab",on:{click:function(e){return t.onFold(s,"resources")}}},[e("span",[t._v("以下是为您整理的知识，共 "+t._s(null==s||null===(m=s.content)||void 0===m||null===(m=m.metadata)||void 0===m||null===(m=m.retriever_resources)||void 0===m?void 0:m.length)+" 处")]),s.resources?e("i",{staticClass:"el-icon-arrow-down"}):e("i",{staticClass:"el-icon-arrow-up"})]),s.resources?t._e():e("div",{staticClass:"content-reference-tab-list"},[e("ul",[t._l(s.content.metadata.retriever_resources,(function(s,i){return[e("li",{key:i},[e("div",{staticClass:"item-left-icon"},[e("img",{attrs:{src:t.icon.link,alt:""}})]),e("el-tooltip",{attrs:{placement:"top",disabled:!0,"open-delay":200,"hide-after":800}},[e("p",{style:{"max-width":t.isFullScreen?"600px":"400px"},attrs:{slot:"content"},slot:"content"},[t._v(t._s(s.document_name))]),e("span",{staticClass:"line",on:{click:function(e){return t.onMixinsClickResourcesItem(s)}}},[t._v(" "+t._s(s.document_name)+" ")])]),s.content?e("div",{staticClass:"content-expand-fold not-absolute",class:{fullscreen:t.isFullScreen},on:{click:function(e){return e.stopPropagation(),t.onFold(s,"fold")}}},[s.fold?e("i",{staticClass:"el-icon-arrow-up"}):e("i",{staticClass:"el-icon-arrow-down"})]):t._e()],1),s.content&&s.fold?e("el-tooltip",{attrs:{placement:"top",disabled:!0,"open-delay":200}},[e("p",{style:{"max-width":t.isFullScreen?"600px":"400px"},attrs:{slot:"content"},slot:"content"},[t._v(t._s(s.content))]),e("div",{staticClass:"more-text"},[e("vue-markdown",{attrs:{source:s.content}})],1)]):t._e()]}))],2)])]):t._e(),e("div",{staticClass:"content-regenerate"},[e("div",[null!==(g=s.content)&&void 0!==g&&null!==(g=g.menu)&&void 0!==g&&g.length||!t.hideToolsBtn(s.content.answer)?t._e():e("el-tooltip",{attrs:{content:"复制内容",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsPaste(s)}}},[e("i",{staticClass:"iconfont el-icon-copy-document"})])]),i>=t.params.answerList.length-1&&s.recordId?e("el-tooltip",{attrs:{content:"重新生成",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onRegenerate(s,i)}}},[e("i",{staticClass:"iconfont icon-zhongzhi"})])]):t._e(),t.hideVoicePlay||null!==(v=s.content)&&void 0!==v&&null!==(v=v.menu)&&void 0!==v&&v.length||!t.hideToolsBtn(s.content.answer)?t._e():e("el-tooltip",{attrs:{content:s.isVoiceSpeaking?"中止朗读":"朗读",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){s.isVoiceSpeaking?t.onMixinsTextToVoiceStop(s):t.onMixinsTextToVoicePlay(s,i,t.params.answerList)}}},[s.isVoiceSpeaking?e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])]):e("i",{staticClass:"iconfont icon-shengyin1"})])]),!t.isWimpic||!t.hideToolsBtn(s.content.answer)||null!==(f=s.content)&&void 0!==f&&null!==(f=f.menu)&&void 0!==f&&f.length?t._e():e("el-tooltip",{attrs:{content:"插入正文",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsInsertDoc(s)}}},[e("i",{staticClass:"iconfont icon-daoru1"})])]),!t.hideDoc&&t.hideToolsBtn(s.content.answer)?e("el-tooltip",{attrs:{content:"分享到WimPic",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsShareToWimPic(s,i)}}},[e("i",{staticClass:"iconfont el-icon-share"})])]):t._e(),t.hideDownload||t.isMobile||!t.hideToolsBtn(s.content.answer)?t._e():e("el-tooltip",{attrs:{content:"下载文档",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",staticStyle:{"margin-left":"10px"},on:{click:function(e){return t.onMixinsDownload(s,i)}}},[e("i",{staticClass:"iconfont el-icon-download"})])]),t.hideCorrect||null!==(w=s.content)&&void 0!==w&&null!==(w=w.menu)&&void 0!==w&&w.length||!t.hideToolsBtn(s.content.answer)?t._e():e("el-tooltip",{attrs:{content:"纠错",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",staticStyle:{"margin-left":"10px"},on:{click:function(e){return t.onMixinsCorrect(s,i)}}},[e("i",{staticClass:"iconfont el-icon-edit-outline"})])]),e("correct",{attrs:{content:t.correct.content,question:t.correct.question},model:{value:t.correct.visible,callback:function(e){t.$set(t.correct,"visible",e)},expression:"correct.visible"}})],1)])]],2)]:"document"===s.role?[e("img",{staticClass:"avatar",attrs:{src:t.aiLogo,alt:""}}),e("div",{staticClass:"content",class:{fold:s.fold}},[e("div",{staticClass:"content-title"},[t._v(" "+t._s(t.aiName)+" ")]),e("span",{staticClass:"content-result"},[s.loading&&!s.content?[e("i",{staticClass:"el-icon-loading"})]:[e("div",{staticClass:"document-content",class:{"is-link":s.docId},on:{click:function(e){return t.onMixinsOpenDoc(s)}}},[e("img",{staticClass:"file-icon",attrs:{src:t.icon.link,alt:""}}),e("div",[t._v(t._s(s.content))])])]],2),s.loading||t.hideTools?t._e():e("div",{staticClass:"content-regenerate"},[e("div",[e("el-tooltip",{attrs:{content:"复制链接地址",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsDocumentPaste(s)}}},[e("i",{staticClass:"iconfont el-icon-copy-document"})])])],1)])])]:t._e()],2)})),e("div",{directives:[{name:"show",rawName:"v-show",value:t.params.suggestedMessage.length,expression:"params.suggestedMessage.length"}],staticClass:"toolbar-suggestion"},[e("transition-group",{attrs:{name:"list",tag:"div"}},t._l(t.params.suggestedMessage,(function(s,i){return e("div",{key:i,staticClass:"suggestion",style:{"--delay":.2*i+"s"},on:{click:function(e){return t.selectQa(s)}}},[e("span",[t._v(t._s(s))]),e("div",{staticClass:"item-right-arrow"})])})),0)],1)],2):e("div",{staticClass:"default-content"},[e("img",{staticClass:"logo",attrs:{src:t.aiLogo1,alt:""}}),e("div",{staticClass:"line"},[t._v(t._s(t.aiModelDesc[t.agent]||"你好啊")+"，")]),e("div",{staticClass:"line more"},[t._v(t._s(t.aiModelDesc[t.agent+"_1"]||`我是${t.aiName}，水务行业全能AI助理`))]),t.params.qaRecommendationList.length?e("div",{staticClass:"qa-recommendation-list"},[e("div",{staticClass:"qa-t"},[t._v(" 您可以试着这样问我： "),t.templates.length>6?e("div",{staticClass:"right",on:{click:function(e){return t.getRecommend(!0)}}},[e("i",{staticClass:"refresh-icon el-icon-refresh-right",class:{loading:t.params.qrLoading}}),t.isFullScreen||t.isMobile?e("span",[t._v("换一批")]):t._e()]):t._e()]),e("div",{staticClass:"list-container"},t._l(t.params.qaRecommendationList,(function(s,i){return e("div",{key:i,staticClass:"list-container-item",on:{click:function(e){return t.selectQa(s)}}},[e("div",{staticClass:"item-center-text"},[t._v(t._s(s))]),e("div",{staticClass:"item-right-arrow"})])})),0)]):t._e()])])],1),e("div",{staticClass:"bottom-content-toolbar"},[e("transition",{attrs:{name:"el-fade-in"}},[!t.params.scrollEnd&&t.params.answerList.length?e("div",{staticClass:"scroll-to-bottom",class:{loading:t.answering},on:{click:t.onScrollBottom}},[e("i",{staticClass:"el-icon-bottom"})]):t._e()]),e("transition",{attrs:{name:"el-fade-in"}},[t.audioIsPlaying?e("div",{staticClass:"scroll-top-stop-voice loading",on:{click:function(e){return t.onMixinsTextToVoiceStop(null,t.params.answerList)}}},[e("el-tooltip",{attrs:{content:"中止朗读",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])])])],1):t._e()]),e("transition",{attrs:{name:"el-fade-in"}},[t.recognitionShow?e("div",{staticClass:"evoke-yi-nuo"},[e("div",{staticClass:"logo"},[e("div",{staticClass:"img"},[e("img",{attrs:{src:t.icon.logo2,alt:""}})]),e("span",[t._v("您好，我是一诺AI语音助手")])]),e("span",[t._v("可以试试对我说您想要了解的问题")]),e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])])]):t._e()]),e("div",{staticClass:"toolbar-tools"},[e("div",{staticClass:"tools-left"}),t.agentIntroduce&&!t.isMobile?e("introduce",{attrs:{"agent-introduce":t.agentIntroduce,"is-mobile":t.isMobile,"custom-title":t.customTitle}}):t._e(),t.hideHistory||t.isMobile?t._e():e("el-tooltip",{staticClass:"item",style:{marginLeft:t.agentIntroduce?"none":"12px"},attrs:{content:"对话历史",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"tool-item tool-history-chat",on:{click:function(e){t.params.historyListVis=!0}}},[e("i",{staticClass:"iconfont icon-lishiliaotian"})])]),t.isMobile?t._e():e("el-tooltip",{staticClass:"item",attrs:{content:"新对话",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":700}},[e("div",{staticClass:"tool-item tool-new-chat",on:{click:t.getNewAnswer}},[e("i",{staticClass:"iconfont icon-xinjianduihuaxiaoxi"})])]),e("el-drawer",{attrs:{direction:t.isMobile?"btt":"rtl",size:t.isMobile?"65%":t.historyDrawWidth,visible:t.params.historyListVis,"append-to-body":"","custom-class":"chat-history-drawer"},on:{"update:visible":function(e){return t.$set(t.params,"historyListVis",e)}}},[e("div",{staticClass:"chat-history-drawer-title",attrs:{slot:"title"},slot:"title"},[e("div",[t._v("对话历史 "),t.chatHistoryListLen?e("span",{staticClass:"number"},[t._v("("+t._s(t.chatHistoryListLen)+")")]):t._e()]),e("div",{staticClass:"search-input"},[e("el-input",{attrs:{clearable:"",placeholder:"搜索历史记录","prefix-icon":"el-icon-search"},model:{value:t.params.searchVal,callback:function(e){t.$set(t.params,"searchVal",e)},expression:"params.searchVal"}})],1)]),e("div",{staticClass:"ai-assistant-chat-history"},[e("div",{staticClass:"ai-assistant-chat-history-history-list"},[e("el-scrollbar",{staticStyle:{height:"100%"},attrs:{native:t.isMobile,"wrap-style":"overflow-x:hidden;"}},t._l(t.chatHistoryList,(function(s,i,n){return s.length?e("div",{staticClass:"list-item"},[e("span",{staticClass:"unit"},[t._v(t._s(i))]),t._l(s,(function(s,i){return e("div",{key:i,staticClass:"list-item-child",class:{active:t.params.sessionId===s.id,"is-edit":s.isEdit,"is-mobile":t.isMobile},on:{mouseenter:function(e){return t.onMouseEnter(s)},mouseleave:function(e){return t.onMouseLeave(s)}}},[e("span",{staticClass:"title",on:{click:function(e){return t.onSelectHistory(s)}}},[t._v(t._s(s.title))]),e("div",{staticClass:"delete-button",on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.onHandlerHistoryDelete(s)}}},[e("i",{staticClass:"iconfont icon-shanchu"})])])}))],2):t._e()})),0),0===t.chatHistoryListLen?e("div",{staticClass:"chat-history-no-data"},[e("img",{attrs:{src:t.icon.nodata,alt:""}}),e("span",[t._v("暂无历史记录")])]):t._e()],1)])]),t._t("tools")],2),e("div",{staticClass:"question_content"},[e("div",{staticClass:"box"},[t.params.fileList.length?e("div",{staticClass:"box-header",class:{column:"image"===t.params.fileList[0].type}},["image"===t.params.fileList[0].type?e("div",{staticClass:"file-image"},[e("img",{attrs:{src:t.params.fileList[0].url,alt:""}}),e("div",{staticClass:"delete-button"},[e("i",{staticClass:"el-icon-close",on:{click:function(e){return e.stopPropagation(),t.onFileDelete.apply(null,arguments)}}})])]):e("div",{staticClass:"file-name"},[t._v(" "+t._s(t.params.fileList[0]&&t.params.fileList[0].name)+" "),e("div",{staticClass:"delete-button"},[e("i",{staticClass:"el-icon-close",on:{click:function(e){return e.stopPropagation(),t.onFileDelete.apply(null,arguments)}}})])]),"image"===t.params.fileList[0].type?e("div",{staticClass:"file-name",on:{click:function(e){return t.selectQa("解释这张图片")}}},[t._v("解释这张图片"),e("i",{staticClass:"el-icon-arrow-right"})]):t._e()]):t._e(),e("div",{staticClass:"box-content",class:{"is-show-voice-to-text":t.isShowVoiceToText}},[e("div",{staticClass:"el-textarea el-input--mini el-input--suffix"},[e("pre",{ref:"inputHtml",staticClass:"el-textarea__inner",staticStyle:{visibility:"hidden","white-space":"pre-wrap",height:"auto"},domProps:{innerHTML:t._s(t.params.keyword)}}),e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.params.keyword,expression:"params.keyword"}],ref:"input",staticClass:"el-textarea__inner",staticStyle:{position:"absolute",top:"0",height:"100%"},attrs:{autosize:{maxRows:16,minRows:3},rows:3,placeholder:`给 ${t.aiName} 发送消息`,resize:"none",maxlength:"2000",enterkeyhint:"send"},domProps:{value:t.params.keyword},on:{keydown:t.Keydown,focus:function(e){return t.$emit("input-focus",!0)},blur:function(e){t.onBlur(),t.$emit("input-focus",!1)},input:function(e){e.target.composing||t.$set(t.params,"keyword",e.target.value)}}})])]),e("div",{staticClass:"box-toolbar"},[e("div",{staticClass:"send-tools"}),t.mediaRecorderLoading?e("div",{staticClass:"send-item-recorder-loading"},[t._v("语音转换中...")]):t._e(),t.isShowVoiceToText?e("el-tooltip",{staticClass:"item",attrs:{content:t.isRecording?"停止语音输入":"语音输入",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":700}},[t.isRecording?e("div",{staticClass:"send-item-tool border-right recording",on:{click:t.stopRecording,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.stopRecording.apply(null,arguments)}}},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])]):e("div",{staticClass:"send-item-tool border-right",class:{recording:t.isMobile},on:{click:t.startRecording,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.startRecording.apply(null,arguments)}}},[e("i",{staticClass:"iconfont icon-yuyin"})])]):t._e(),t.answering?t._e():e("div",{staticClass:"send-item",class:{answering:this.answering||t.params.loading},attrs:{disabled:!t.params.keyword},on:{click:t.getAnswer,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.getAnswer()}}},[e("i",{staticClass:"iconfont icon-fasong"})]),t.answering?e("el-tooltip",{attrs:{content:"中止",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"send-item-abort",on:{click:t.onAbortAnswer,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.onAbortAnswer.apply(null,arguments)}}})]):t._e()],1)])]),t.isMobile?t._e():e("div",{staticClass:"toolbar-desc"},[e("span",[t._v("内容由 "+t._s(t.aiName)+" 生成，请仔细甄别")]),e("div",{staticClass:"toolbar-right"})])],1)])])}),[],!1,null,"71abc9c8",null);e.default=d.exports},"77f8":function(t,e,s){},daf1:function(t,e,s){},e287:function(t,e,s){"use strict";s("daf1")},f965:function(t,e,s){"use strict";s("77f8")}}]);