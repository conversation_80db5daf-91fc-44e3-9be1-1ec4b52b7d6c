import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import utils from '@/utils/utils'

export const useLoginStore = defineStore('login', () => {
  // 扥古矿
  const loginPopVisible = ref<any>(false)

  // 方法
  const LOGIN_POP_VISIBLE = (view: any) => {
    loginPopVisible.value = view
  }
  
  const LOGOUT = () => {
    loginPopVisible.value = false
    let token = utils.GetAuthorization();
    utils.sysLogOut(token);
    utils.removeStorage();
    location.href = '/#/homepage';
  }

  return {
    LOGOUT,
    loginPopVisible,
    LOGIN_POP_VISIBLE
  }
})
