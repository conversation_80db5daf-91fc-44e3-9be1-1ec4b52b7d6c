{"specversion": 3, "name": "DynamicLibrary", "doc": "<p>Dummy documentation for <span class=\"name\">__intro__</span>.</p>", "version": "0.1", "generated": "2023-11-15T17:55:17+00:00", "type": "LIBRARY", "scope": "TEST", "docFormat": "HTML", "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": 5, "tags": ["hyvää", "my", "tägs", "<PERSON><PERSON><PERSON><PERSON>"], "inits": [{"name": "__init__", "args": [{"name": "arg1", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg1"}, {"name": "arg2", "type": null, "defaultValue": "These args are shown in docs", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "arg2=These args are shown in docs"}], "returnType": null, "doc": "<p>Dummy documentation for <span class=\"name\">__init__</span>.</p>", "shortdoc": "Dummy documentation for `__init__`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": 9}], "keywords": [{"name": "0", "args": [], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#0\" class=\"name\">0</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `0`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "De<PERSON>ults", "args": [{"name": "old", "type": null, "defaultValue": "style", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "old=style"}, {"name": "new", "type": null, "defaultValue": "style", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "new=style"}, {"name": "cool", "type": null, "defaultValue": "True", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "cool=True"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Defaults\" class=\"name\">Defaults</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Defaults`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Em${bed}ed ${args} 2", "args": [], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Em%24%7Bbed%7Ded%20%24%7Bargs%7D%202\" class=\"name\">Em${bed}ed ${args} 2</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Em${bed}ed ${args} 2`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Embedded ${args} 1", "args": [], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Embedded%20%24%7Bargs%7D%201\" class=\"name\">Embedded ${args} 1</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Embedded ${args} 1`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Invalid Source Info", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Invalid%20Source%20Info\" class=\"name\">Invalid source info</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Invalid source info`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Keyword 1", "args": [{"name": "arg1", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg1"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Keyword 1`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Keyword-only <PERSON><PERSON><PERSON>", "args": [{"name": "", "type": null, "defaultValue": null, "kind": "NAMED_ONLY_MARKER", "required": false, "repr": "*"}, {"name": "kwo", "type": null, "defaultValue": null, "kind": "NAMED_ONLY", "required": true, "repr": "kwo"}, {"name": "another", "type": null, "defaultValue": "default", "kind": "NAMED_ONLY", "required": false, "repr": "another=default"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Keyword-only%20Args\" class=\"name\">Keyword-only args</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Keyword-only args`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "KW 2", "args": [{"name": "arg1", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg1"}, {"name": "arg2", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg2"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#KW%202\" class=\"name\">KW2</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `KW2`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "KWO W/ Varargs", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "a", "type": null, "defaultValue": null, "kind": "NAMED_ONLY", "required": true, "repr": "a"}, {"name": "b", "type": null, "defaultValue": "2", "kind": "NAMED_ONLY", "required": false, "repr": "b=2"}, {"name": "c", "type": null, "defaultValue": null, "kind": "NAMED_ONLY", "required": true, "repr": "c"}, {"name": "kws", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kws"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#KWO%20W%2F%20Varargs\" class=\"name\">KWO w/ varargs</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `KWO w/ varargs`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "No Arg Spec", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#No%20Arg%20Spec\" class=\"name\">no arg spec</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `no arg spec`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Non-existing Source Path And Lineno", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Non-existing%20Source%20Path%20And%20Lineno\" class=\"name\">Non-existing source path and lineno</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Non-existing source path and lineno`.", "tags": [], "source": "whatever:xxx", "lineno": -1}, {"name": "Non-existing Source Path With Lineno", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Non-existing%20Source%20Path%20With%20Lineno\" class=\"name\">Non-existing source path with lineno</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Non-existing source path with lineno`.", "tags": [], "source": "everwhat", "lineno": 42}, {"name": "Nön-äsci<PERSON>", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Hyvää yötä.</p>\n<p>Спасибо! (Unicode)</p>", "shortdoc": "Hyvää yötä.", "tags": ["hyvää", "<PERSON><PERSON><PERSON><PERSON>"], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Nön-äscii ÜTF-8", "args": [{"name": "arg1", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg1"}, {"name": "arg2", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg2"}, {"name": "arg3", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg3"}, {"name": "arg4", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg4"}, {"name": "arg5", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg5"}, {"name": "arg6", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg6"}, {"name": "arg7", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg7"}, {"name": "arg8", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "arg8"}], "returnType": null, "doc": "<p>Hyvää yötä.</p>\n<p>Спасибо! (UTF-8)</p>", "shortdoc": "Hyvää yötä.", "tags": ["hyvää", "<PERSON><PERSON><PERSON><PERSON>"], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Source Info", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Source%20Info\" class=\"name\">Source info</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Source info`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": 83}, {"name": "Source Lineno Only", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Source%20Lineno%20Only\" class=\"name\">Source lineno only</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Source lineno only`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": 12345}, {"name": "Source Path Only", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Source%20Path%20Only\" class=\"name\">Source path only</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Source path only`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/Annotations.py", "lineno": -1}, {"name": "Tags", "args": [{"name": "varargs", "type": null, "defaultValue": null, "kind": "VAR_POSITIONAL", "required": false, "repr": "*varargs"}, {"name": "kwargs", "type": null, "defaultValue": null, "kind": "VAR_NAMED", "required": false, "repr": "**kwargs"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Tags\" class=\"name\">Tags</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Tags`.", "tags": ["my", "tägs"], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}, {"name": "Types", "args": [{"name": "integer", "type": {"name": "int", "typedoc": "integer", "nested": [], "union": false}, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "integer: int"}, {"name": "no type", "type": null, "defaultValue": null, "kind": "POSITIONAL_OR_NAMED", "required": true, "repr": "no type"}, {"name": "boolean", "type": {"name": "bool", "typedoc": "boolean", "nested": [], "union": false}, "defaultValue": "True", "kind": "POSITIONAL_OR_NAMED", "required": false, "repr": "boolean: bool = True"}], "returnType": null, "doc": "<p>Dummy documentation for <a href=\"#Types\" class=\"name\">Types</a>.</p>\n<p>Neither <a href=\"#Keyword%201\" class=\"name\">Keyword 1</a> or <a href=\"#KW%202\" class=\"name\">KW 2</a> do anything really interesting. They do, however, accept some <span class=\"name\">arguments</span>. Neither <a href=\"#Introduction\" class=\"name\">introduction</a> nor <a href=\"#Importing\" class=\"name\">importing</a> contain any more information.</p>\n<p>Examples:</p>\n<table border=\"1\">\n<tr>\n<td>Keyword 1</td>\n<td>arg</td>\n<td></td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 2</td>\n</tr>\n<tr>\n<td>KW 2</td>\n<td>arg</td>\n<td>arg 3</td>\n</tr>\n</table>\n<hr>\n<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>", "shortdoc": "Dummy documentation for `Types`.", "tags": [], "source": "/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DynamicLibrary.py", "lineno": -1}], "typedocs": [{"type": "Standard", "name": "boolean", "doc": "<p>Strings <code>TRUE</code>, <code>YES</code>, <code>ON</code> and <code>1</code> are converted to Boolean <code>True</code>, the empty string as well as strings <code>FALSE</code>, <code>NO</code>, <code>OFF</code> and <code>0</code> are converted to Boolean <code>False</code>, and the string <code>NONE</code> is converted to the Python <code>None</code> object. Other strings and other accepted values are passed as-is, allowing keywords to handle them specially if needed. All string comparisons are case-insensitive.</p>\n<p>Examples: <code>TRUE</code> (converted to <code>True</code>), <code>off</code> (converted to <code>False</code>), <code>example</code> (used as-is)</p>", "usages": ["Types"], "accepts": ["string", "integer", "float", "None"]}, {"type": "Standard", "name": "integer", "doc": "<p>Conversion is done using Python's <a href=\"https://docs.python.org/library/functions.html#int\">int</a> built-in function. Floating point numbers are accepted only if they can be represented as integers exactly. For example, <code>1.0</code> is accepted and <code>1.1</code> is not.</p>\n<p>Starting from RF 4.1, it is possible to use hexadecimal, octal and binary numbers by prefixing values with <code>0x</code>, <code>0o</code> and <code>0b</code>, respectively.</p>\n<p>Starting from RF 4.1, spaces and underscores can be used as visual separators for digit grouping purposes.</p>\n<p>Examples: <code>42</code>, <code>-1</code>, <code>0b1010</code>, <code>10 000 000</code>, <code>0xBAD_C0FFEE</code></p>", "usages": ["Types"], "accepts": ["string", "float"]}]}