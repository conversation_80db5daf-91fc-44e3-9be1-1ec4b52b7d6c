<!--  -->
<template>
    <div class="homepage" @scroll="handleScroll">
        <!-- <div class="banner-top" :class="{'banner-top-fixed':isFixed,'is-from-package':isUniwimPc&&isFromPackage}">
            <div class="banner-left">
                <img style="width:123px;height: 48px;cursor: pointer;" src="../../assets/images/admin/logoV2.png" alt="WimTask logo" />
                <div class="tabs-box">
                    <el-tabs v-model="activeName" class="tabs" @tab-click="handleTab">
                        <el-tab-pane label="产品介绍" name="product"></el-tab-pane>
                        <el-tab-pane label="服务包市场" name="market"></el-tab-pane>
                        <el-tab-pane label="学习中心" name="study"></el-tab-pane>
                    </el-tabs>
                </div>

            </div>

            <div class="banner-right">
                <div class="logo-tel">
                    <img style="width: 16px;height: 16px;margin-right:15px;"
                        src="../../assets/images/homepage/tel.png" />热线：0573-82875638
                </div>
                <div class="menus">
                    <div class="menu-item" @click="jumpToPage('manager')" v-if="token">工作台</div>
                </div>
            </div>
        </div> -->
        <div class="page-content">
            <div class="banner shrink">
                <div class="banner-inner">

                    <div class="banner-desc ">
                        <img src="../../assets/images/homepage/banner-text.png" alt="" width="1200px">
                    </div>
                    <div class="banner-button">
                        <div class="banner-button-inner">
                            <span class="banner-button-text">进入演示</span>
                            <img src="../../assets/images/homepage/banner-button-icon.png" width="46px" height="46px" alt="">
                        </div>
                        
                    </div>
                </div>

            </div>
            <div class="highlight scroll-animate">
                <div class="highlight-item" :key="item.name" v-for="item in highlights">
                    <img :src="item.img" alt="">
                    <div class="highlight-item-desc">
                        <div class="highlight-title">{{ item.name }}</div>
                        <div class="highlight-text">{{ item.desc }}</div>
                    </div>
                </div>
            </div>
            <div class="scroll-animate application" data-animation="fade-up">
                <div class="application-title">应用场景</div>
                <div class="application-desc">
                    在实际中发挥作用，融合客户需求、目的与环境条件
                </div>
                <div class="application-cards">
                    <div class="application-card">
                        <div class="application-card-content">
                            <div class="category">
                                <img src="../../assets/images/homepage/application1.png" alt="">水务管理
                            </div>
                            <div class="category-content">智能监控水质数据、设备状态，自动生成
                                巡检报告，异常预警处理</div>
                            <div class="category-highlights">
                                <p>- 水质数据自动采集分析</p>
                                <p>- 设备运行状态监控</p>
                                <p>- 异常报警自动处理</p>
                                <p>- 巡检报告自动生成</p>
                            </div>
                        </div>
                        <div class="application-card-img">
                            <img class="application-card-img-item" src="../../assets/images/homepage/category1.jpg" alt=""> 
                        </div>
                    </div>
                    <div class="application-card">
                        <div class="application-card-content">
                            <div class="category">
                                <img src="../../assets/images/homepage/application2.png" alt="" />日常办公
                            </div>
                            <div class="category-content">自动化处理重复性办公任务，提升工作效
                                率，减少人工错误</div>
                            <div class="category-highlights">
                                <p>- 数据录入和处理</p>
                                <p>- 报表自动生成</p>
                                <p>- 邮件批量发送</p>
                                <p>- 文档格式转换</p>
                            </div>
                        </div>
                        <div class="application-card-img">
                            <img class="application-card-img-item" src="../../assets/images/homepage/category2.jpg" alt="">
                        </div>
                    </div>
                    <div class="application-card">
                        <div class="application-card-content">
                            <div class="category">
                                <img src="../../assets/images/homepage/application1.png" alt="">制造业
                            </div>
                            <div class="category-content">生产流程自动化，质量检测，设备维护管理</div>
                            <div class="category-highlights">
                                <p>- 生产计划自动调度</p>
                                <p>- 质量数据分析</p>
                                <p>- 设备预测性维护</p>
                                <p>- 库存管理优化</p>
                            </div>
                        </div>
                        <div class="application-card-img">
                            <img class="application-card-img-item" src="../../assets/images/homepage/category3.jpg" alt="">
                        </div>
                    </div>
                </div>
            </div>
            <div class="scroll-animate plans " data-animation="fade-up">
                <div class="plans-title">
                    <div class="plans-title-inner">
                        <div class="plan-main-title">行业解决方案</div>
                        <div class="plan-sub-title">针对不同行业特点，提供专业化的智能自动化解决方案</div>
                    </div>

                </div>

                <div class="plan-cards">
                    <div class="plan-card auto-slide-left-in">
                        <div class="plan-card-content">
                            <div class="plan-card-main-title">水务管理解决方案</div>
                            <div class="plan-card-sub-title">智能化水务运营管理平台</div>
                            <div class="plan-card-functions">
                                <div class="plan-card-functions-title">
                                    核心功能
                                </div>
                                <div class="plan-card-functions-content">
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">水质数据自动采集与分析</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">异常报警自动处理</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">维护计划自动调度</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">设备运行状态实时监控</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">巡检报告智能生成</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">合规报告一键生成</span>
                                    </div>
                                </div>
                            </div>
                            <div class="plan-card-cases">
                                <div class="case-desc">
                                    <div class="case-desc-title">客户案例：某市自来水公司</div>
                                    <div class="case-desc-content">通过WimTask平台，实现了全市200个监测点的自动化管理，大幅提升了运营效率。</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">99.9%</div>
                                    <div class="case-item-name">系统可用性</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">60%</div>
                                    <div class="case-item-name">成本降低</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">85%</div>
                                    <div class="case-item-name">效率提升</div>
                                </div>
                            </div>
                            <div class="plan-card-button">
                                了解详情
                                <!-- <div class="plan-card-button-bg"></div> -->
                            </div>
                        </div>
                        <div class="plan-card-img">
                            <img src="../../assets/images/homepage/plan1.jpg" alt=""/>
                        </div>
                    </div>
                    <div class="plan-card right auto-slide-in">
                        <div class="plan-card-content">
                            <div class="plan-card-main-title">办公自动化解决方案</div>
                            <div class="plan-card-sub-title">提升办公效率的智能化平台</div>
                            <div class="plan-card-functions">
                                <div class="plan-card-functions-title">
                                    核心功能
                                </div>
                                <div class="plan-card-functions-content">
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">文档批量处理与转换</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">报表智能生成</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">审批流程自动化</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">数据录入自动化</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">邮件批量发送</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">会议安排智能调度</span>
                                    </div>
                                </div>
                            </div>
                            <div class="plan-card-cases">
                                <div class="case-desc">
                                    <div class="case-desc-title">客户案例：某大型企业集团</div>
                                    <div class="case-desc-content">部署WimTask后，月度报表生成时间从3天缩短至2小时，员工满意度显著提升。</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">70%</div>
                                    <div class="case-item-name">时间成节省</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">95%</div>
                                    <div class="case-item-name">准确率提升</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">50%</div>
                                    <div class="case-item-name">人力成本降低</div>
                                </div>
                            </div>
                            <div class="plan-card-button">了解详情</div>
                        </div>
                        
                        <div class="plan-card-img">
                            <img src="../../assets/images/homepage/plan2.jpg" alt=""/>
                        </div>
                    </div>
                    <div class="plan-card auto-slide-left-in">
                        <div class="plan-card-content">
                            <div class="plan-card-main-title">智能制造解决方案</div>
                            <div class="plan-card-sub-title">工业4.0智能制造管理平台</div>
                            <div class="plan-card-functions">
                                <div class="plan-card-functions-title">
                                    核心功能
                                </div>
                                <div class="plan-card-functions-content">
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">生产计划智能调度</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">设备预测性维护</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">供应链协同管理</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">质量数据实时分析</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">库存管理优化</span>
                                    </div>
                                    <div class="plan-card-functions-content-item">
                                        <span class="checkbox"></span><span class="function">能耗监控与优化</span>
                                    </div>
                                </div>
                            </div>
                            <div class="plan-card-cases">
                                <div class="case-desc">
                                    <div class="case-desc-title">客户案例：某汽车零部件制造商</div>
                                    <div class="case-desc-content">通过智能制造解决方案，实现了生产效率提升30%，产品质量稳定性大幅改善。</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">30%</div>
                                    <div class="case-item-name">生产效率提升</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">25%</div>
                                    <div class="case-item-name">质量改善</div>
                                </div>
                                <div class="case-item">
                                    <div class="case-item-percent">40%</div>
                                    <div class="case-item-name">维护成本降低</div>
                                </div>
                            </div>
                            <div class="plan-card-button">了解详情</div>
                        </div>
                        
                        <div class="plan-card-img">
                            <img src="../../assets/images/homepage/plan3.jpg" alt=""/>
                        </div>
                    </div>
                </div>
            </div>
            <div  class="scroll-animate price" data-animation="fade-up">
                <div class="price-title">定价方案</div>
                <div class="price-items">
                    <div class="price-item">
                        <div class="price-item-bg"></div>
                        <div class="price-item-head price1">
                            个人版
                        </div>
                        <div class="price-item-content">
                            <div class="price-value">
                                ¥ 待定
                            </div>
                            <div class="price-desc">
                                适合个人用户
                            </div>
                            <div class="customer-service-button">联系客服</div>
                            <div class="function-list">
                                <div class="function-item"><span class="function-item-text">无限工作流</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">完整组件库</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">MCP服务市场</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">基础智能体功能</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">云端执行</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">邮件支持</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text disabled">高级智能体协作</span><i
                                        class="checkbox disabled"></i></div>
                                <div class="function-item"><span class="function-item-text disabled">私有部署</span><i
                                        class="checkbox disabled"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="price-item price2">
                        <div class="price-item-bg"></div>
                        <div class="price-item-head price2">
                            企业版
                            <img class="recommend" src="../../assets/images/homepage/tuijian.png" alt="">
                        </div>
                        <div class="price-item-content">
                            <div class="price-value">
                                ¥ 待定
                            </div>
                            <div class="price-desc">
                                适合大型企业和高级用户
                            </div>
                            <div class="customer-service-button">联系客服</div>
                            <div class="function-list">
                                <div class="function-item"><span class="function-item-text">无限工作流</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">完整组件库</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text"><b>全部</b>MCP服务</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text"><b>高级</b>智能体功能</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">A2A协议支持</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">优先技术支持</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">API访问权限</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">数据分析报告</span><i
                                        class="checkbox"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="price-item price3">
                        <div class="price-item-bg"></div>
                        <div class="price-item-head price3">
                            定制版
                        </div>
                        <div class="price-item-content">
                            <div class="price-value">
                                ¥ 定制报价
                            </div>
                            <div class="price-desc">
                                为大型企业量身定制的解决方案
                            </div>
                            <div class="customer-service-button">联系客服</div>
                            <div class="function-list">
                                <div class="function-item"><span class="function-item-text">私有部署</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">定制开发</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">专属技术支持</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">培训服务</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">SLA保障</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">安全合规认证</span><i
                                        class="checkbox"></i></div>
                                <div class="function-item"><span class="function-item-text">专属客户经理</span><i
                                        class="checkbox disabled"></i></div>
                                <div class="function-item"><span class="function-item-text">7x24小时支持</span><i
                                        class="checkbox disabled"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="scroll-animate service-market" data-animation="fade-up">
                <div class="service-market-title">服务包市场</div>
                <div class="service-market-content">
                    <div class="service-market-tab">
                        <div class="tab-con">
                            <el-tabs v-model="marketType" class="market-tabs" @tab-click="changeMarketType">
                                <el-tab-pane name="scene" label="场景">
                                    <template #label>
                                        <div class="tab-label">场景</div>
                                    </template>
                                </el-tab-pane>
                                <el-tab-pane name="mcp" label="MCP">
                                    <template #label>
                                        <div class="tab-label">MCP</div>
                                    </template>
                                </el-tab-pane>
                                <el-tab-pane name="template" label="模板">
                                    <template #label>
                                        <div class="tab-label">模板</div>
                                    </template>
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                        <div class="more" @click="toMarket">查看更多</div>
                    </div>
                    <div class="service-market-main clearfix" v-loading="loading">
                        <div class="service-item-card" @click="clickCard(pk)" v-for="pk in packages" :key="pk.name">
                            <div class="service-item-card-img">
                                <img :src="pk.image" alt="" style="width:100%;height:100%;">
                            </div>
                            <div class="service-item-card-top">
                                <div class="service-item-card-name">
                                    <i class="service-item-card-icon"><img :src="pk.iconPath" style="width:100%;height:100%;"></i>
                                    <span>{{ pk.name }}</span>
                                </div>
                                <!-- <div class="service-item-card-price">{{pk.price||"￥ 待定"}}</div> -->
                            </div>
                            <div class="service-item-card-desc">{{ pk.summary||pk.desc }}</div>
                            <div class="service-item-card-rate">
                                <!-- <el-rate v-model="rate" disabled show-score text-color="#ff9900" score-template="{value}" /> -->
                                <div class="card-group">{{ pk.provider }}</div>
                                <!-- <span class="subscribe-number">已使用{{pk.downloadCount||0}}次</span> -->
                            </div>
                            <!-- <div class="service-item-card-button">查看详情</div> -->
                        </div>
                    </div>
                </div>

            </div>

            <div class="scroll-animate enterprise" data-animation="fade-up">
                <div class="enterprise-title">企业服务</div>
                <div class="enterprise-content clearfix">
                    <div class="enterprise-content-card enterprise1">
                        <div class="enterprise-content-card-top">
                            <i class="enterprise-content-card-icon"></i>
                            私有部署
                        </div>
                        <div class="enterprise-content-card-desc">
                            提供私有云和本地部署解决方案，确保数据安全和合规性
                        </div>
                        <div class="enterprise-content-card-function-list">
                            <div class="enterprise-content-card-function-item">
                                - 本地化部署支持
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 私有云环境搭建
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 数据安全保障
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 合规性认证
                            </div>
                        </div>
                    </div>
                    <div class="enterprise-content-card enterprise2">
                        <div class="enterprise-content-card-top">
                            <i class="enterprise-content-card-icon"></i>
                            定制开发
                        </div>
                        <div class="enterprise-content-card-desc">
                            企业级定制开发服务，包括模板开发、MCP服务开发等
                        </div>
                        <div class="enterprise-content-card-function-list">
                            <div class="enterprise-content-card-function-item">
                                - 专业工作流模板开发
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 行业MCP服务定制
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 系统集成开发
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - API接口定制
                            </div>
                        </div>
                    </div>
                    <div class="enterprise-content-card enterprise3">
                        <div class="enterprise-content-card-top">
                            <i class="enterprise-content-card-icon"></i>
                            技术支持
                        </div>
                        <div class="enterprise-content-card-desc">
                            全方位技术支持服务，确保系统稳定运行
                        </div>
                        <div class="enterprise-content-card-function-list">
                            <div class="enterprise-content-card-function-item">
                                - 7x24小时技术支持
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 远程故障诊断
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 系统优化建议
                            </div>
                            <div class="enterprise-content-card-function-item">
                                - 版本升级服务
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- <section class="scroll-animate contact-us" data-animation="fade-up">
                <div class="container">
                    <h2>联系我们</h2>
                    <h3>Contact Us</h3>
                    <p>想要了解更多？欢迎留言！<br>
                        在这里，您的声音很重要。分享您的见解，让我们一起成长！</p>
                </div>
                <form>
                    <div class="form-row">
                        <input type="text" class="name" name="name" placeholder="请输入您的姓名">
                        <input type="text" class="tel" name="tel" placeholder="请输入您的电话">
                    </div>
                    <div class="form-row">
                        <input type="email" class="email" name="email" placeholder="请输入您的邮箱">
                    </div>
                    <textarea name="content" placeholder="请填写您的内容"></textarea>
                    <button type="submit">提交</button>
                </form>
            </section> -->
            <!-- 底部信息模块 -->
            <footer class="footer-section">
                <div class="container">
                    <!-- <div class="footer-main">
                        <div class="footer-left">
                            <div class="footer-left-title">联系我们</div>
                            <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                            <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                            <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                        </div>
                        <div class="footer-middle">
                            <p>一诺数字助理</p>
                            <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                                class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                            <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                                class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                        </div>
                        <div class="footer-right">
                            <div class="qrcode-box">
                                <p>一诺APP</p>
                                <div class="qrcode">
                                    <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                                </div>
                            </div>
                            <div class="qrcode-box">
                                <p>度量公众号</p>
                                <div class="qrcode">
                                    <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
                </div>

            </footer>
        </div>
    </div>
</template>

<script>
import servicePackage1 from '../../assets/images/homepage/servicePackage1.png'
import highlight1 from "../../assets/images/homepage/highlight1.png"
import highlight2 from "../../assets/images/homepage/highlight2.png"
import highlight3 from "../../assets/images/homepage/highlight3.png"
import highlight4 from "../../assets/images/homepage/highlight4.png"
import highlight5 from "../../assets/images/homepage/highlight5.png"
import highlight6 from "../../assets/images/homepage/highlight6.png"
import highlight7 from "../../assets/images/homepage/highlight7.png"
import highlight8 from "../../assets/images/homepage/highlight8.png"
import packageIcon from '../../assets/images/homepage/package.png';
import utils from '@/utils/utils'
import saasApi from '@/api/index';
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
export default {
    data() {
        return {
            loading:false,
            isFixed:true,
            activeName:"product",
            rate: 5.0,
            marketType: 'scene',
            packages: [
                // { name: "和达水务专业包", desc: "专为水务行业设计的完整解决方案，包含水质监测、设备管理等功能" ,iconPath:packageIcon },
                // { name: "办公自动化包", desc: "提升办公效率的自动化工具集，支持文档处理、数据分析等" ,iconPath:packageIcon },
                // { name: "制造业智能包", desc: "工业4.0智能制造解决方案，包含生产调度、质量控制等" ,iconPath:packageIcon },
                // { name: "和达水务专业包", desc: "专为水务行业设计的完整解决方案，包含水质监测、设备管理等功能" ,iconPath:packageIcon },
                // { name: "办公自动化包", desc: "提升办公效率的自动化工具集，支持文档处理、数据分析等" ,iconPath:packageIcon },
                // { name: "制造业智能包", desc: "工业4.0智能制造解决方案，包含生产调度、质量控制等" ,iconPath:packageIcon }
            ],
            highlights: [
                {
                    name: '降低运营成本',
                    desc: '自动化重复性工作，减少人工错误，平均降低60%的运营成本',
                    img: highlight1
                },
                {
                    name: '提升工作效率',
                    desc: '24/7不间断执行，处理速度比人工快10倍，释放员工时间专注高价值工作',
                    img: highlight2
                },
                {
                    name: '保障业务质量',
                    desc: '标准化流程执行，确保业务操作的一致性和准确性，减少99%的人为错误',
                    img: highlight3
                },
                {
                    name: '专注核心业务',
                    desc: '自动处理繁琐事务，让团队专注于战略决策和创新工作，提升竞争优势',
                    img: highlight4
                },
                {
                    name: '零代码操作',
                    desc: '拖拽式可视化设计，业务人员无需编程即可创建自动化流程',
                    img: highlight5
                },
                {
                    name: '灵活扩展',
                    desc: '丰富的行业模版和工具库，快速适配不同业务场景',
                    img: highlight6
                },
                {
                    name: '开放生态',
                    desc: '支持MCP协议标准化集成，构建开放的工具生态，轻松接入第三方服务',
                    img: highlight7
                },
                {
                    name: '智能调度',
                    desc: '支持定时任务、条件触发、异常处理，智能化任务调度管理',
                    img: highlight8
                }
            ]
        };
    },

    components: {},

    computed: {
        token(){
            return  utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
        },
        isUniwimPc() {
            return this.$route.query.uniwim === 'pc'||utils.GetQueryString('uniwim')=='pc'
        },
        isFromPackage() {
            return this.$route.query.from === 'package'||utils.GetQueryString('from')=='package'
        },
    },

    mounted() {
        // 选择所有需要滚动动画的元素
        const animateElements = document.querySelectorAll('.scroll-animate');

        // 配置观察器：当元素可见比例 >= 0.1 时触发回调
        const observerOptions = {
        root: null, // 监听基于视口
        rootMargin: '0px', // 视口边缘扩展（可选，如 "-50px" 提前触发）
        threshold: 0.1 // 元素可见10%时触发
        };

        // 观察器回调：处理元素进入/离开视口
        const observerCallback = (entries) => {
        entries.forEach(entry => {
            // 当元素进入视口（isIntersecting为true）
            if (entry.isIntersecting) {
            // 添加active类触发动画
            entry.target.classList.add('active');
            // 动画触发后停止观察（避免重复触发）
            observer.unobserve(entry.target);
            }else{
                debugger;
                // observer.observe(entry.target);
                entry.target.classList.remove('active');
            }
        });
        };

        // 创建观察器实例
        const observer = new IntersectionObserver(observerCallback, observerOptions);

        // 监听所有目标元素
        animateElements.forEach(element => {
        observer.observe(element);
        });
        this.getServicePackageData()
     },

    methods: {
        handleScroll(event){
            if(event.target.scrollTop==0){
                this.isFixed = true;
            }else{
                this.isFixed = false;
            }
        },
        getServicePackageData(type='scene'){
            let self = this;
            let categoryMains = []
            if(type=='scene'){
                categoryMains = ['scene']
            }else if(type==='template'){
                categoryMains = ['template']
            }else if(type==='mcp'){
                categoryMains = ['mcp']
            }
            self.loading = true;
            saasApi.AIAgentTaskTemplateQuery({
                    data: {
                        status:1,
                        categoryMains
                    },
                    index: 1,
                    size: 8,
                }).then((res) => {
                if(res?.rows){
                    self.packages = res.rows
                }else{
                    // self.packages = []
                    throw new Error('获取服务包数据失败')
                }
            })
            .catch((err) => {
                // self.packages = []
            })
            .finally(() => {
                self.loading = false;
            })

        },
        handleTab(type){
            let path = '/homepage'
            if(type.paneName==='product'){
                path = '/homepage'
            }else if(type.paneName==='market'){
                path = '/serviceMarket'
            }else if(type.paneName==='study'){
                path = '/study'
            }
            this.$router.push({
                path: path,
                query: this.$route.query
            })
        },
        openDetail(type) {
            const urlDict = {
                yuanxing: 'http://192.168.100.205:25427/Products/金聪_WimTask/',
                xiangshe: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2MyYjhiYWRjOWVjMDRmMGViNWQxNDljODQ1ZTRmYzg4OzA7MQ==&showAi=true&hideNativeTitle=true',
                guihua: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2JjMWE0NWNmMGI5YTQ2ZjU4YjY5ZTJlNTY4YjJmNjgyOzE7Mg==&showAi=true&hideNativeTitle=true'
            }
            const urlToken = utils.GetAuthorization();
            let urlOrigin = `${urlDict[type]}`
            if (type !== 'yuanxing') urlOrigin += `&uniwater_utoken=${urlToken}`
            if (this.isUniwimPc) {
                window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
            } else {
                window.open(urlOrigin)
            }
        },
        clickCard(row){
            let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
            if(token){
                this.$route.query.uniwater_utoken = token;
            }
            let url = utils.generateUrlWithQuery('/#/serviceDetail/'+row.id,this.$route.query)
            window.open(url)
            // isDetailShow.value = true;
            // router.push({
            //     path:'/serviceDetail/'+row.id,
            //     query:route.query
            // })
        },
        jumpToPage(path) {
            // this.$router.push({
            //     name: path,
            //     query: this.$route.query
            // });
            let token = utils.GetQueryString('uniwater_utoken', 'hash') || utils.GetQueryString('uniwater_utoken')
            if(token){
                this.$route.query.uniwater_utoken = token
            }
            let url = utils.generateUrlWithQuery("https://www.dlmeasure.com/extends/WimTask/index.html#/manager",this.$route.query)
            // window.open(url)
            
            window.location.href = url
        },
        toMarket(){
            this.$router.push('/serviceMarket')
        },
        changeMarketType(type) {
            this.marketType = type.paneName;
            if (this.marketType == 'scene') {
                this.packages = [
                    // { name: "和达水务专业包", desc: "专为水务行业设计的完整解决方案，包含水质监测、设备管理等功能",iconPath:packageIcon },
                    // { name: "办公自动化包", desc: "提升办公效率的自动化工具集，支持文档处理、数据分析等" ,iconPath:packageIcon },
                    // { name: "制造业智能包", desc: "工业4.0智能制造解决方案，包含生产调度、质量控制等" ,iconPath:packageIcon },
                    // { name: "和达水务专业包", desc: "专为水务行业设计的完整解决方案，包含水质监测、设备管理等功能" ,iconPath:packageIcon },
                    // { name: "办公自动化包", desc: "提升办公效率的自动化工具集，支持文档处理、数据分析等" ,iconPath:packageIcon },
                    // { name: "制造业智能包", desc: "工业4.0智能制造解决方案，包含生产调度、质量控制等" ,iconPath:packageIcon },
                ]
            } else if (this.marketType == 'template') {
                this.packages = [
                    // { name: "水质监测模版", desc: "自动采集水质数据，生成监测报告，异常情况自动报警" ,iconPath:packageIcon },
                    // { name: "财务报表模版", desc: "自动收集财务数据，生成标准化报表，支持多种格式导出" ,iconPath:packageIcon },
                    // { name: "客服自动化模版", desc: "智能客服工作流，自动回复常见问题，工单自动分配" ,iconPath:packageIcon },
                ]
            } else {
                this.packages = [
                    // { name: "和达水务MCP", desc: "专为水务行业设计的MCP服务，提供水质监测、设备管理、数据分析等功能",iconPath:packageIcon,image:servicePackage1 },
                    // { name: "Excel处理MCP", desc: "强大的Excel文件处理工具，支持数据清洗、格式转换、图表生成",iconPath:packageIcon,image:servicePackage1 },
                    // { name: "邮件发送MCP", desc: "批量邮件发送工具，支持模板定制、发送统计、退信处理",iconPath:packageIcon,image:servicePackage1 },
                    // { name: "数据库连接MCP", desc: "支持多种数据库连接，提供数据查询、更新、备份等功能",iconPath:packageIcon,image:servicePackage1 }
                ]
            }
            this.getServicePackageData(type.paneName)
        }
    }
}

</script>
<style lang='less' scoped>
.homepage {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    background:#fff;
    .clearfix::after {
        content: "";
        display: table;
        clear: both;
    }
    .page-content{
        min-width:1440px;
        height:100%;
    }

    .banner-top {
        width:100%;
        // width:1400px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        background:#fff;
        // height: 80px;
        margin: 0 auto;
        position:fixed;
        top:0;
        z-index:10;
        transition: background-color 0.3s ease; // 添加过渡效果
        box-shadow: 0 1px 30px 0 #4a5c7a29;
        &.banner-top-fixed{
            background: transparent;
            box-shadow: none;
        }
        &.is-from-package{
            top:42px;
        }

        .banner-left{
            display:flex;
            // height:58px;
            // align-items: center;
            img{
                margin-top:5px;
            }
            .tabs-box{
                display:flex;
                align-items: center;
                height:58px;
            }
            .el-tabs{
                height:40px;
                margin-left:68px;
            }
            /deep/.el-tabs__header{
                margin:0;
            }
            /deep/.el-tabs__nav-wrap::after{
                background:transparent;
            }
            /deep/.el-tabs__item{
                font-size: 16px;
            }
            /deep/.el-tabs__item.is-active{
                font-weight: 700;
                font-size: 16px;
            }
        }
        .banner-right {
            display: flex;
            font-size: 16px;
            align-items: center;
            .logo-tel{
                display:flex;
                align-items: center;
            }
            .menus {
                display: flex;
                margin-left: 50px;

                .menu-item {
                    margin-right: 30px;
                    cursor: pointer;
                    &:last-of-type {
                        margin-right: 0;
                    }
                }
            }
        }

    }
    .banner {
        width: 100%;
        min-width: 1400px;
        height: 500px;
        background: url('../../assets/images/homepage/banner-bg.jpg') no-repeat center top;
        background-size: cover;
        .banner-inner{
            // width:1400px;
            // object-fit: cover;
            // /* 初始状态：放大1.5倍 */
            // transform: scale(1.5);
            // /* 动画：从1.5倍缩小到1倍，持续3秒，保持最终状态 */
            // animation: scaleDown 3s forwards;
            margin:0 auto;
        }

        .banner-desc {
            // height: 132px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            margin: 0 auto;
            // padding-top: 148px;
            padding-top:100px;
            width: 1200px;
            color: #232425;
            display: flex;
            align-items: center;
            opacity: 0;
            transform: translateY(30px); /* 向下偏移30px */
            animation: fadeInFromBottom 1s forwards; /* 执行动画并保持最终状态 */
            b {
                // height: 73px;
                font-family: YouSheBiaoTiHei;
                font-size: 56px;
                color: #222222;
                background: -webkit-linear-gradient(left, #ff7e5f, #feb47b);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-fill-color: transparent;
                text-shadow: 5px 8px 9px #0054d238;
            }
            img{
                // height:132px;
            }
        }

        .banner-button {
            overflow: hidden;
            width: 200px;
            height: 64px;
            // box-sizing: border-box;
            // background: url('../../assets/images/homepage/banner-button.png') no-repeat center;
            // background-size: cover;
            margin: 0 auto;
            // // margin-top: 97px;
            margin-top:80px;
            cursor: pointer;
            position: relative;
            
                // box-shadow: 0 6px 40px #3a5c7a29;
            // line-height: 64px;
            // padding-left: 40px;
            // font-size: 20px;
            // color: #FFFFFF;
            border-radius: 33px;
            box-sizing: initial;
            &:hover{
                // transform: translateX(10px);
                border-radius: 33px;
                box-shadow: 0 6px 40px #2a5c7a29;
                border: 1px solid #FFFFFF;
                .banner-button-inner img{
                    // transition: translateX(10px) 1s ease;
                    // transform: translateX(10px);
                }
            }
            .banner-button-immer img{
                // transition: transform 0.3s ease;
            }
            .banner-button-inner{
                // &:hover ::before {
                //     content: '';
                //     position: absolute;
                //     top: 0;
                //     left: -100%; /* 初始位置在左侧外部 */
                //     width: 50%;
                //     height: 100%;
                //     background: linear-gradient(
                //         to right,
                //         rgba(255, 255, 255, 0) 0%,
                //         rgba(255, 255, 255, 0.3) 50%,
                //         rgba(255, 255, 255, 0) 100%
                //     );
                //     transform: skewX(-25deg); /* 倾斜光线，增强扫过感 */
                //     animation: scanPulse 3s infinite; /* 无限循环动画 */
                // }
                display: flex;
                justify-content: center;
                align-items: center;;
                width: 200px;
                height: 64px;
                background-image: linear-gradient(270deg, #0054D2 0%, #B4BFFA 100%);
                // border-radius: 33px;
                &:hover{
                    // border-radius: 33px;
                    // border: 1px solid #FFFFFF;
                    // box-shadow: 0 6px 30px #4a5c7a29;
                }
                .banner-button-text{
                    height: 36px;
                    line-height: 36px;
                    margin-left:15px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 20px;
                    color: #FFFFFF;
                    margin-right: 40px;
                }
            }
        }
    }

    .highlight {
        width: 1400px;
        height: 265px;
        padding: 40px;
        box-sizing: border-box;
        margin: 0 auto;
        margin-top: -75px;
        background: #FFFFFF;
        box-shadow: 0 1px 30px 0 #4a5c7a29;
        border-radius: 16px;
        overflow: hidden;
        transition-delay: 0.2s;
        .highlight-item {
            display: flex;
            float: left;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            width: 25%;
            margin-bottom: 42px;

            img {
                width: 80px;
                height: 70px;
                background: #F2F3F7;
                border-radius: 20px;
            }

            .highlight-item-desc {
                margin-left: 16px;

                .highlight-title {
                    width: 120px;
                    height: 29px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 20px;
                    color: #232425;
                    margin-bottom: 8px;
                }

                .highlight-text {
                    width: 199px;
                    height: 36px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #808A94;
                }
            }
        }
    }

    .application {
        width: 100%;
        width: 1400px;
        margin: 0 auto;
        margin-top: 60px;
        transition-delay: 0.4s;    
        .application-title {
            height: 47px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 32px;
            color: #191919;
            letter-spacing: 0;
            text-align: center;
        }

        .application-desc {
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            margin-top: 6px;
            font-size: 16px;
            color: #808A94;
            text-align: center;
        }

        .application-cards {
            margin-top: 40px;
            width: 1400px;
            display: flex;
            justify-content: space-between;

            .application-card {
                width: 450px;
                padding: 24px;
                box-sizing: border-box;
                position: relative;
                height: 290px;
                background: #FFFFFF;
                box-shadow: 0 1px 30px 0 #4a5c7a29;
                border-radius: 16px;
                overflow: hidden;
                .application-card-content {
                    .category {
                        display: flex;
                        align-items: center;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 20px;
                        color: #232425;

                        img {
                            width: 48px;
                            height: 48px;
                            margin-right: 16px;
                        }
                    }

                    .category-content {
                        width: 216px;
                        margin-top: 16px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #808A94;
                    }

                    .category-highlights {
                        margin-top: 16px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #222222;
                        line-height: 24px;
                    }
                }

                .application-card-img {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 230px;
                    height: 290px;
                    // overflow: hidden;
                    clip-path: polygon(
                        30% 0,   /* 左上角 x=10% y=0 */
                        100% 0,  /* 右上角 x=100% y=0 */
                        100% 100%,/* 右下角 x=100% y=100% */
                        0 100%   /* 左下角 x=0 y=100% */
                    );
                    /* 圆角（需与裁剪配合，否则可能被裁剪覆盖） */
                    // border-radius: 16px 16px 16px 0;
                    
                    img{
                        width:100%;
                        height:100%;
                        object-fit: cover; /* 图片填充容器，保持比例不拉伸 */
                        transition: transform 0.3s ease; /* 放大过渡动画：时长0.3s，缓动效果 */
                        transform-origin: center; /* 放大原点：从中心放大（默认） */
                    }
                    &:hover img{
                        width:100%;
                        height:100%;
                        transform: scale(1.1);
                    }
                }
            }
        }
    }

    .plans {
        margin-top: 60px;
        // width:100%;
        .plans-title {
            min-width: 1400px;
            background: url('../../assets/images/homepage/plan-titlebg.jpg') no-repeat center center;
            height: 160px;
            background-size: 100% 100%;
            .plans-title-inner{
                width:1400px;
                margin:0 auto;
            }
            .plan-main-title {
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 32px;
                color: #FFFFFF;
                letter-spacing: 0;
                padding-top: 40px;
                text-align: center;
            }

            .plan-sub-title {
                margin-top: 6px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                color: #FFFFFF;
                text-align: center;
            }
        }

        .plan-cards {
            margin-top: 60px;

            .plan-card {
                width: 1400px;
                height: 449px;
                background: #FFFFFF;
                box-shadow: 0 1px 30px 0 #4a5c7a29;
                border-radius: 16px;
                position: relative;
                box-sizing: border-box;
                margin: 0 auto;
                margin-top: 40px;

                &.right {
                    .plan-card-img {
                        width:547px;
                        height:489px;
                        position: absolute;
                        left: 0;
                        top: -20px;
                    }

                    .plan-card-content {
                        margin-left: 550px;
                    }
                }

                .plan-card-content {
                    // margin-top:20px;
                    padding: 20px 48px;
                    height: 100%;
                    box-sizing: border-box;
                }

                .plan-card-main-title {
                    height: 36px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 24px;
                    color: #232425;
                }

                .plan-card-sub-title {
                    margin-top: 8px;
                    // width: 176px;
                    height: 24px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 16px;
                    color: #808A94;
                }

                .plan-card-functions-title {
                    margin-top: 30px;
                    width: 56px;
                    height: 24px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 14px;
                    color: #222222;
                    line-height: 24px;
                }

                .plan-card-functions-content {
                    margin-top: 6px;
                    width: 642px;
                    overflow: hidden;

                    .plan-card-functions-content-item {
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #808A94;
                        line-height: 24px;
                        display: flex;
                        align-items: center;
                        margin-left: 60px;
                        float: left;

                        &:first-of-type,
                        &:nth-of-type(4n) {
                            margin-left: 0;
                        }

                        .checkbox {
                            display: block;
                            width: 12px;
                            height: 12px;
                            background: url('../../assets/images/homepage/checkbox.png') no-repeat;
                            background-size: 100% 100%;
                        }

                        .function {
                            width: 132px;
                            margin-left: 10px;
                        }
                    }
                }
                &.right{
                    .plan-card-img{
                        
                    border-radius: 16px 0 16px 16px;
                        clip-path: polygon(
                            0% 0,   /* 左上角 x=10% y=0 */
                            100% 0,  /* 右上角 x=100% y=0 */
                            85% 100%,/* 右下角 x=100% y=100% */
                            0% 100%   /* 左下角 x=0 y=100% */
                        );
                    }
                }
                .plan-card-img{
                    width:547px;
                    height:489px;
                    position: absolute;
                    right: 0;
                    top: -20px;
                    clip-path: polygon(
                        0% 0,   /* 左上角 x=10% y=0 */
                        100% 0,  /* 右上角 x=100% y=0 */
                        100% 100%,/* 右下角 x=100% y=100% */
                        15% 100%   /* 左下角 x=0 y=100% */
                    );
                    // /* 圆角（需与裁剪配合，否则可能被裁剪覆盖） */
                    border-radius: 0px 16px 16px 16px;
                    overflow:hidden;
                    img{
                        width:100%;
                        height:100%;
                        transition: transform 0.3s ease; /* 放大过渡动画：时长0.3s，缓动效果 */
                        transform-origin: center; /* 放大原点：从中心放大（默认） */
                    }
                    &:hover img{
                        // width:100%;
                        // height:100%;
                        transform: scale(1.1);
                    }
                }

                // img {
                //     position: absolute;
                //     right: 0;
                //     top: -20px;
                // }

                .plan-card-cases {
                    width: 738px;
                    margin-top: 30px;
                    display: flex;
                    justify-content: space-between;

                    .case-desc {
                        width: 340px;
                        height: 110px;
                        background: #F8F9FA;
                        border-radius: 4px;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 16px;
                        color: #5D6E89;
                        padding: 16px 20px;
                        box-sizing: border-box;

                        .case-desc-content {
                            font-weight: 400;
                            font-size: 12px;
                            color: #808A94;
                            line-height: 24px;
                            margin-top: 10px;
                        }
                    }

                    .case-item {
                        width: 120px;
                        height: 110px;
                        background: #F8F9FA;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                        justify-content: center;

                        .case-item-percent {
                            font-family: SourceHanSansSC-Bold;
                            font-weight: 700;
                            font-size: 28px;
                            color: #5ACD90;
                            text-align: center;
                        }

                        .case-item-name {
                            font-family: SourceHanSansSC-Medium;
                            font-weight: 500;
                            font-size: 12px;
                            color: #222222;
                            text-align: center;
                            line-height: 24px;
                        }
                    }
                }

                .plan-card-button {
                    width: 140px;
                    height: 48px;
                    position: relative;
                    background: #0054D2;
                    border-radius: 4px;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 48px;
                    text-align: center;
                    margin-top: 32px;
                    cursor: pointer;
                }
                .plan-card-button:hover{
                    box-shadow: 0 1px 10px 0 rgba(74, 92, 122, 0.5);
                    // border: 2px solid #FFFFFF;
                }
                /* 脉冲光效的伪元素 */
                .plan-card-button:hover{

                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: -100%; /* 初始位置在左侧外部 */
                        width: 50%;
                        height: 100%;
                        background: linear-gradient(
                            to right,
                            rgba(255, 255, 255, 0) 0%,
                            rgba(255, 255, 255, 0.3) 50%,
                            rgba(255, 255, 255, 0) 100%
                        );
                        transform: skewX(-25deg); /* 倾斜光线，增强扫过感 */
                        animation: scanPulse 2s infinite; /* 无限循环动画 */
                    }
                }

                // .plan-card-button-bg{
                //     width: 140px;
                //     height: 48px;
                //     border-radius: 4px;
                //     background: #0054D2;
                //     position: absolute;
                //     left: 50%;
                //     top: 50%;
                //     transform: translate3d(-50%, -50%, 0) scale(0);
                //     z-index: -1;
                //     opacity: 1;
                //     transition: opacity .2s ease-in-out, transform 0s .2s;
                // }
                // .plan-card-button:hover{
                //     .plan-card-button-bg{
                //         transform: translate3d(-50%, -50%, 0) scale(1);
                //         opacity: 1;
                //         transition: transform .5s ease-in-out;
                //     }
                // }
            }
        }
    }

    .price {

        width: 100%;
        margin: 0 auto;
        background: #F5F6FB;
        padding-top: 90px;
        margin-top: 60px;

        .price-title {
            height: 47px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 32px;
            color: #191919;
            letter-spacing: 0;
            text-align: center;
        }

        .price-items {
            width: 1400px;
            height: 794px;
            background: url("../../assets/images/homepage/price-bg.png") no-repeat bottom center;
            background-size: 1400px 680px;
            border-radius: 16px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            .price-item {
                width: 450px;
                height: 640px;
                border-radius: 16px;
                position: relative;
                border: 2px solid #fff;
                overflow:hidden;
                &:hover {
                    box-shadow: 0 1px 20px 0 rgba(74, 92, 122, 0.5);
                    > .price-item-content > .customer-service-button{
                        
                        position: relative;
                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: -100%; /* 初始位置在左侧外部 */
                            width: 50%;
                            height: 100%;
                            background: linear-gradient(
                                to right,
                                rgba(255, 255, 255, 0) 0%,
                                rgba(255, 255, 255, 0.3) 50%,
                                rgba(255, 255, 255, 0) 100%
                            );
                            transform: skewX(-25deg); /* 倾斜光线，增强扫过感 */
                            animation: scanPulse 2s infinite; /* 无限循环动画 */
                        }
                    }
                }

                .price-item-bg {
                    background: #ffffff80;
                    border-radius: 16px;
                    filter: blur(4px);
                    position: absolute;
                    top: 100px;
                    bottom: 0;
                    left: 0;
                    right: 0;
                }

                &.price2 {
                    .customer-service-button {
                        background-image: linear-gradient(90deg, #D6B779 0%, #E3D3AC 100%) !important;
                    }
                }

                &.price3 {
                    .customer-service-button {
                        background-image: linear-gradient(90deg, #20201E 0%, #747474 100%) !important;
                    }
                }

                .price-item-head {
                    width: 450px;
                    height: 100px;
                    background-image: linear-gradient(90deg, #0054D2 0%, #54A2FF 100%);
                    border-radius: 16px 16px 0 0;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 32px;
                    color: #FFFFFF;
                    padding-left: 24px;
                    box-sizing: border-box;
                    line-height: 100px;
                    position: relative;
                    .recommend{
                        position: absolute;
                        width:100px;
                        height:100px;
                        right:0;
                        top:0;
                    }

                    &.price1 {
                        background: url("../../assets/images/homepage/price1.png") no-repeat;
                        background-size: cover;
                    }

                    &.price2 {
                        background: url("../../assets/images/homepage/price2.png") no-repeat;
                        background-size: cover;
                    }

                    &.price3 {
                        background: url("../../assets/images/homepage/price3.png") no-repeat;
                        background-size: cover;
                    }
                }

                .price-item-content {
                    position: absolute;
                    top: 100px;

                    padding: 24px;

                    .price-value {
                        height: 47px;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 32px;
                        color: #222222;
                    }

                    .price-desc {
                        margin-top: 20px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 16px;
                        color: #808A94;
                    }

                    .customer-service-button {
                        margin-top: 20px;
                        cursor: pointer;
                        width: 402px;
                        height: 48px;
                        background-image: linear-gradient(90deg, #0054D2 0%, #54A2FF 100%);
                        border-radius: 4px;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 16px;
                        color: #FFFFFF;
                        line-height: 48px;
                        text-align: center;
                        &:hover{
                            box-shadow: 0 1px 20px 0 rgba(74, 92, 122, 0.5);
                        }
                    }

                    .function-list {
                        margin-top: 40px;

                        .function-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .function-item-text {
                                font-family: SourceHanSansSC-Regular;
                                font-weight: 400;
                                font-size: 16px;
                                color: #222222;
                                line-height: 36px;
                                &.disabled{
                                    color:#808A94;
                                }
                                b{
                                    color:#B59453
                                }
                            }

                            .checkbox {
                                background: url(../../assets/images/homepage/checkbox.png) no-repeat;
                                background-size: cover;
                                width: 16px;
                                height: 16px;

                                &.disabled {
                                    display: block;
                                    background: url(../../assets/images/homepage/close.png) no-repeat;
                                    background-size: cover;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .service-market {
        margin-top: 60px;

        .service-market-title {
            height: 47px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 32px;
            color: #191919;
            letter-spacing: 0;
            text-align: center;
        }

        .service-market-content {
            width: 1400px;
            margin: 0 auto;
        }

        .service-market-tab {
            position: relative;
            height: 42px;

            .more {
                position: absolute;
                right: 0;
                top: 0;
                font-weight: 400;
                font-size: 18px;
                color: #808A94;
                cursor: pointer;
            }

            .market-tabs {
                /deep/.el-tabs__item {
                    align-items: flex-start;
                    font-size: 18px;
                }

                .tab-label {
                    padding: 0 20px;
                }
            }
        }

        .service-market-main {
            min-height:300px;
            // overflow:hidden;
            .service-item-card {
                float: left;
                margin: 36px 36px 0 0;
                width: 323px;
                height: 300px;
                padding: 12px;
                box-sizing: border-box;
                background: #FFFFFF;
                cursor: pointer;
                // box-shadow: 0 1px 30px 0 #4a5c7a29;
                border-radius: 8px;
                &:hover {
                    box-shadow: 0 2px 12px 0 #0000001a;
                }
                &:nth-of-type(4n) {
                    margin-right: 0;
                }
                .service-item-card-img{
                    width: 299px;
                    height: 160px;
                    background: #EDF1FF;
                    border-radius: 8px;
                }
                .service-item-card-top {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .service-item-card-name {
                        margin-top:12px;
                        width:100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        height: 24px;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 16px;
                        color: #222222;
                        display: flex;
                        align-items: center;
                        span{
                            width:100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .service-item-card-icon {
                        width: 24px;
                        display: block;
                        height: 24px;
                        // background: url('../../assets/images/homepage/package.png') no-repeat;
                        background-size: 100% 100%;
                        margin-right: 10px;
                    }

                    .service-item-card-price {
                        height: 24px;
                        font-family: SourceHanSansSC-Bold;
                        font-weight: 700;
                        font-size: 16px;
                        color: #E6A23C;
                    }
                    
                }

                .service-item-card-desc {
                    margin-top: 10px;
                    height:40px;
                    line-height:20px;
                    overflow: hidden;
                    display: -webkit-box;             /* 必须设置，用于多行溢出 */
                    -webkit-box-orient: vertical;     /* 设置盒子排列方向为垂直 */
                    -webkit-line-clamp: 2;            /* 限制显示的行数，例如显示 2 行 */
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 14px;
                    color: #808A94;
                }

                .service-item-card-rate {
                    display: flex;
                    align-items: center;
                    margin-top: 10px;

                    .subscribe-number {
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #BCBFC3;
                        // margin-left: 23px;
                    }
                    .card-group {
                        height: 20px;
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 14px;
                        color: #808A94;
                        // margin-top: 10px;
                    }
                }

                .service-item-card-button {
                    margin-top: 15px;
                    width: 140px;
                    height: 32px;
                    background: #0054D2;
                    border-radius: 4px;
                    line-height: 32px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    text-align: center;
                }
            }
        }

    }

    .enterprise {
        width: 1400px;
        margin: 0 auto;
        margin-top: 60px;
        padding-bottom:80px;
        .enterprise-title {
            width: 128px;
            height: 47px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 32px;
            color: #191919;
            letter-spacing: 0;
            text-align: center;
        }

        .enterprise-content {
            margin-top: 40px;

            // overflow:hidden;
            .enterprise-content-card {
                float: left;
                margin-right: 24px;
                width: 450px;
                height: 240px;
                background: #FFFFFF;
                box-shadow: 0 1px 30px 0 #4a5c7a29;
                border-radius: 16px;
                padding: 24px;
                box-sizing: border-box;

                &:last-of-type {
                    margin-right: 0;
                }

                &.enterprise1 {
                    .enterprise-content-card-icon {
                        background: url('../../assets/images/homepage/enterprise1.png') no-repeat center;
                        background-size: cover;
                    }
                }

                &.enterprise2 {
                    .enterprise-content-card-icon {
                        background: url('../../assets/images/homepage/enterprise2.png') no-repeat center;
                        background-size: cover;
                    }
                }

                &.enterprise3 {
                    .enterprise-content-card-icon {
                        background: url('../../assets/images/homepage/enterprise3.png') no-repeat center;
                        background-size: cover;
                    }
                }

                .enterprise-content-card-top {
                    display: flex;
                    align-items: center;
                    font-family: SourceHanSansSC-Medium;
                    font-weight: 500;
                    font-size: 20px;
                    color: #232425;
                }

                .enterprise-content-card-icon {
                    display: block;
                    width: 48px;
                    height: 48px;
                    background: #F5F6FA;
                    border-radius: 8px;
                    margin-right: 16px;
                }

                .enterprise-content-card-desc {
                    width: 402px;
                    margin-top: 16px;
                    height: 20px;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 14px;
                    color: #808A94;
                }

                .enterprise-content-card-function-list {
                    margin-top: 16px;

                    .enterprise-content-card-function-item {
                        font-family: SourceHanSansSC-Regular;
                        font-weight: 400;
                        font-size: 12px;
                        color: #222222;
                        line-height: 24px;
                    }
                }
            }
        }
    }

    // 联系我们模块样式
    .contact-us {
        width: 1400px;
        margin: 0 auto;
        margin-top: 60px;
        display: flex;
        justify-content: space-between;

        h2 {
            height: 45px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 32px;
            color: #222222;
            letter-spacing: 0;
            line-height: 44.8px;
        }

        h3 {
            height: 45px;
            font-family: ArialMT;
            font-size: 24px;
            color: #DADEE4;
            letter-spacing: 0;
            line-height: 44.8px;
        }

        p {
            line-height: 1.6;
            margin-top: 96px;
            margin-bottom: 20px;
        }

        form {
            width: 640px;
            padding-bottom: 84px;

            .form-row {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;

                input {
                    flex: 1;
                    padding: 10px 24px;
                    border-radius: 4px;
                    background: #F3F5F6;
                    border: none;
                    box-sizing: border-box;
                    outline: none;
                    font-size:18px;
                    font-family: SourceHanSansSC-Regular;
                }

                input:focus {
                    border: none;
                }

                .name,
                .tel {
                    width: 262px;
                    height: 48px;
                    box-sizing: border-box;
                }
            }

            input[type="email"] {
                width: 100%;
                padding: 10px 24px;
                border-radius: 4px;
                width: 592px;
                height: 48px;
            }

            textarea {
                outline: none;
                width: 100%;
                background: #F3F5F6;
                padding: 10px 24px;
                border: none;
                border-radius: 4px;
                margin-bottom: 15px;
                resize: none;
                width: 640px;
                height: 144px;
                box-sizing: border-box;
                font-size:18px;
                font-family: SourceHanSansSC-Regular;
            }

            button {
                width: 120px;
                height: 32px;
                background: #0054D2;
                border-radius: 4px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 12px;
                border: none;
                color: #FFFFFF;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }

    // 底部样式
    .footer-section {
        background-color: #f5f5f6;
        // padding: 20px 0;
        width: 100%;
        min-width: 1400px;
        .container {
            width: 1400px;
            margin: 0 auto;
            // display: flex;
            justify-content: space-between;

            .footer-left-title {
                width: 64px;
                height: 24px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                margin-bottom: 12px;
                color: #191919;
            }

            .footer-left {
                p {
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 14px;
                    color: #595959;
                    line-height: 40px;
                    display: flex;
                    align-items: center;

                    &.address i {
                        background: url('../../assets/images/homepage/position.png') no-repeat;
                        background-size: cover;
                    }

                    &.tel i {
                        background: url('../../assets/images/homepage/telephone.png') no-repeat;
                        background-size: cover;
                    }

                    &.email i {
                        background: url('../../assets/images/homepage/email.png') no-repeat;
                        background-size: cover;
                    }
                }

                i {
                    display: block;
                    width: 14px;
                    height: 14px;
                    margin-right: 10px;

                }
            }

            >div {
                flex: 1;
                margin-right: 20px;

                &:last-child {
                    margin-right: 0;
                }


            }
        }

        .footer-main {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }

        .footer-middle {
            height: 144px;

            p {
                height: 24px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                color: #191919;
                margin-bottom: 24px;
            }

            .download-btn {
                width: 149px;
                height: 38px;
                line-height: 38px;
                text-align: center;
                border: 1px solid #0054d2;
                border-radius: 4px;
                border-radius: 4px;
                text-decoration: none;
                display: flex;
                align-items: center;
                text-align: center;
                box-sizing: border-box;
                padding: 0 10px;
                // justify-content: center;
                margin-bottom: 20px;
                color: #0054d2;

                &:hover {
                    opacity: 0.8;
                }

                i {
                    display: block;
                    margin-right: 10px;
                }

                .ios-icon {
                    width: 14px;
                    height: 14px;
                    background: url("../../assets/images/homepage/ios.png") no-repeat;
                    background-size: cover;
                }

                .windows-icon {
                    width: 14px;
                    height: 14px;
                    background: url("../../assets/images/homepage/win.png") no-repeat;
                    background-size: cover;
                }
            }
        }

        .footer-right {
            height: 144px;
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            display: flex;
            gap: 30px;
        }

        .qrcode-box {
            p {
                font-weight: 400;
                font-size: 16px;
                color: #191919;
                height: 24px;
                margin-bottom: 15px;
                text-align: center;
            }
        }

        .qrcode {
            width: 118px;
            height: 118px;
            background: #ffffff00;
            border: 1px solid #DEDEDE;
            border-radius: 2px;
            text-align: center;

            img {
                width: 100px;
                height: 100px;
                display: block;
                margin-top: 9px;
                margin-left: 9px;
            }
        }

        .copyright {
            width: 100%;
            height:48px;
            line-height:48px;
            text-align: center;
            // margin-top: 75px;
            color: #898e96;
            font-size: 12px;
            position: inherit;
            a{
                color: #898e96;
                text-decoration: none;
            }
        }
    }
}
/* 初始状态：未进入视口时隐藏 */
.scroll-animate {
  /* 基础隐藏样式：透明度0 + 位移（可根据动画类型调整） */
  opacity: 0;
  transform: translateY(30px); /* 向下偏移30px，营造“从下方滑入”效果 */
  transition: 
    opacity 0.6s ease-out, 
    transform 0.6s ease-out; /* 过渡动画：0.6秒缓出 */
  will-change: opacity, transform; /* 优化动画性能 */
}

.active .auto-slide-in {
  animation: slideInLeft 1.6s ease 0.5s forwards; /* 延迟0.5s后执行 */
}
.active .auto-slide-left-in {
  animation: slideInRight 1.6s ease 0.5s forwards; /* 延迟0.5s后执行 */
}

/* 动画激活状态：进入视口后显示 */
.scroll-animate.active {
  opacity: 1;
  transform: translateY(0); /* 位移归位 */
}

/* 可选：不同动画类型（通过data-animation区分） */
.scroll-animate[data-animation="fade-in"] {
  transform: none; /* 纯淡入，无位移 */
}

.scroll-animate[data-animation="scale"] {
  transform: scale(0.9); /* 初始缩放0.9 */
}
.scroll-animate[data-animation="scale"].active {
  transform: scale(1); /* 激活后缩放1 */
}

/* 定义缩放动画 */
@keyframes scaleDown {
    from {
    transform: scale(1.5); /* 开始时放大1.5倍 */
    opacity: 0.8; /* 可选：初始半透明 */
    }
    to {
    transform: scale(1); /* 结束时恢复原尺寸 */
    opacity: 1; /* 可选：结束时完全不透明 */
    }
}

/* 定义扫描动画 */
@keyframes scanPulse {
    0% {
        left: -80%; /* 开始位置：左侧外部 */
    }
    100% {
        left: 150%; /* 结束位置：右侧外部 */
    }
}
/* 定义从下到上淡入的动画 */
@keyframes fadeInFromBottom {
    to {
    /* 最终状态：回到原位且完全不透明 */
    opacity: 1;
    transform: translateY(0);
    }
}
/* 定义关键帧 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%); /* 起点：左侧外部 */
    opacity: 0;
  }
  to {
    transform: translateX(0); /* 终点：原位置 */
    opacity: 1;
  }
}

/* 定义关键帧 */
@keyframes slideInRight {
  from {
    transform: translateX(100%); /* 起点：左侧外部 */
    opacity: 0;
  }
  to {
    transform: translateX(0); /* 终点：原位置 */
    opacity: 1;
  }
}
.highlight-item img{
   /* 开启 3D 空间，确保子元素（如果有）保持 3D 关系 */
  transform-style: preserve-3d;
  /* 过渡动画：让旋转更平滑 */
 
  /* 初始状态：不旋转 */
  transform: rotateY(0deg);
}

.highlight-item:hover img{
  transition: transform 1s ease;
  transform: rotateY(360deg);
}
</style>
