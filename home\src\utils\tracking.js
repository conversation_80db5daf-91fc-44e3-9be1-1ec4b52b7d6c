// 存储待发送的埋点数据
import {uuid} from "cue/src/utils/util";
import saasApi from "@/api/index"
import utils from "@/utils/utils";

// 判断是否需要埋点
function isTrackingNeeded() {
    const host = utils.getHost();
    return !(location.origin.indexOf("dlmeasure") === -1 && host.indexOf("dlmeasure") === -1);
}

// 从本地存储中读取埋点数据
function getTrackingDataFromStorage() {
    let data = []
    try {
        let tracking = sessionStorage.getItem('uniwimAiTrackingQueue');
        tracking = JSON.parse(tracking);
        if (tracking && Array.isArray(tracking)) {
            tracking.forEach(track => {
                data.push(track);
            })
        }
    } catch (error) {

    }
    return data
}

// 将埋点数据存储到本地缓存
function saveTrackingDataToStorage(data) {
    sessionStorage.setItem('uniwimAiTrackingQueue', JSON.stringify(data));
}

// 模拟发送埋点数据到服务器
async function sendTrackingData(data) {
    try {
        // 这里替换为实际的请求代码，如使用 axios 发送请求
        const response = await saasApi.accessLogSaveLog(data)
        if (response && response.Code === 0) {
            // 请求成功，移除本地缓存中对应的数据
            const trackingData = getTrackingDataFromStorage();
            const index = trackingData.findIndex(item => item.uuid === data.uuid);
            if (index!== -1) {
                trackingData.splice(index, 1);
                saveTrackingDataToStorage(trackingData);
            }
            return true;
        } else {
            console.error('埋点数据发送失败:', response.statusText);
            return false;
        }
    } catch (error) {
        console.error('埋点数据发送出错:', error);
        return false;
    }
}

// 处理单个埋点数据发送
async function processSingleTracking() {
    // 不在度量云域名环境下不埋点
    if(!isTrackingNeeded()) {
        return
    }
    const trackingData = getTrackingDataFromStorage();
    if (trackingData.length > 0) {
        const dataToSend = trackingData[0];
        const success = await sendTrackingData(dataToSend);
        if (success) {
            // 若发送成功，从本地缓存移除该数据
            trackingData.shift();
            saveTrackingDataToStorage(trackingData);
        }
    }
    // 继续处理下一个
    setTimeout(processSingleTracking, 3000);
}

// 封装全局埋点方法
function setupTracking(Vue) {
    // 启动处理流程
    processSingleTracking();

    Vue.prototype.$track = function (params = {}) {
        // 不在度量云域名环境下不埋点
        if(!isTrackingNeeded()) {
            return
        }
        const data = {
            ...params,
            "applicationId": params.functionId,
            "applicationName": '一诺AI',
            "path": params.path || "",
            "result": 1,
            "type": 1,
            "uuid": uuid(),
        };
        const trackingData = getTrackingDataFromStorage();
        trackingData.push(data);
        saveTrackingDataToStorage(trackingData);
    };
}

export default setupTracking;
