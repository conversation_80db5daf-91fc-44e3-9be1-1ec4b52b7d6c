var content=function(){"use strict";var Sn=Object.defineProperty;var vn=(he,k,ge)=>k in he?Sn(he,k,{enumerable:!0,configurable:!0,writable:!0,value:ge}):he[k]=ge;var ue=(he,k,ge)=>vn(he,typeof k!="symbol"?k+"":k,ge);var tr,rr;function he(e){return e}var k;(function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"})(k||(k={}));function ge(e){return e.nodeType===e.ELEMENT_NODE}function ke(e){var t=e==null?void 0:e.host;return(t==null?void 0:t.shadowRoot)===e}function Me(e){return Object.prototype.toString.call(e)==="[object ShadowRoot]"}function nr(e){return e.includes(" background-clip: text;")&&!e.includes(" -webkit-background-clip: text;")&&(e=e.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),e}function Qe(e){try{var t=e.rules||e.cssRules;return t?nr(Array.from(t).map(pt).join("")):null}catch{return null}}function pt(e){var t=e.cssText;if(or(e))try{t=Qe(e.styleSheet)||t}catch{}return t}function or(e){return"styleSheet"in e}var mt=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(t){var r;if(!t)return-1;var n=(r=this.getMeta(t))===null||r===void 0?void 0:r.id;return n??-1},e.prototype.getNode=function(t){return this.idNodeMap.get(t)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(t){return this.nodeMetaMap.get(t)||null},e.prototype.removeNodeFromMap=function(t){var r=this,n=this.getId(t);this.idNodeMap.delete(n),t.childNodes&&t.childNodes.forEach(function(o){return r.removeNodeFromMap(o)})},e.prototype.has=function(t){return this.idNodeMap.has(t)},e.prototype.hasNode=function(t){return this.nodeMetaMap.has(t)},e.prototype.add=function(t,r){var n=r.id;this.idNodeMap.set(n,t),this.nodeMetaMap.set(t,r)},e.prototype.replace=function(t,r){var n=this.getNode(t);if(n){var o=this.nodeMetaMap.get(n);o&&this.nodeMetaMap.set(r,o)}this.idNodeMap.set(t,r)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function ir(){return new mt}function Xe(e){var t=e.maskInputOptions,r=e.tagName,n=e.type,o=e.value,i=e.maskInputFn,a=o||"";return(t[r.toLowerCase()]||t[n])&&(i?a=i(a):a="*".repeat(a.length)),a}var It="__rrweb_original__";function sr(e){var t=e.getContext("2d");if(!t)return!0;for(var r=50,n=0;n<e.width;n+=r)for(var o=0;o<e.height;o+=r){var i=t.getImageData,a=It in i?i[It]:i,s=new Uint32Array(a.call(t,n,o,Math.min(r,e.width-n),Math.min(r,e.height-o)).data.buffer);if(s.some(function(l){return l!==0}))return!1}return!0}var ar=1,lr=new RegExp("[^a-z0-9-_:]"),Te=-2;function Ct(){return ar++}function cr(e){if(e instanceof HTMLFormElement)return"form";var t=e.tagName.toLowerCase().trim();return lr.test(t)?"div":t}function dr(e){return e.cssRules?Array.from(e.cssRules).map(function(t){return t.cssText||""}).join(""):""}function ur(e){var t="";return e.indexOf("//")>-1?t=e.split("/").slice(0,3).join("/"):t=e.split("/")[0],t=t.split("?")[0],t}var fe,yt,hr=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,gr=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/|#).*/,fr=/^(data:)([^,]*),(.*)/i;function De(e,t){return(e||"").replace(hr,function(r,n,o,i,a,s){var l=o||a||s,c=n||i||"";if(!l)return r;if(!gr.test(l)||fr.test(l))return"url(".concat(c).concat(l).concat(c,")");if(l[0]==="/")return"url(".concat(c).concat(ur(t)+l).concat(c,")");var d=t.split("/"),u=l.split("/");d.pop();for(var g=0,f=u;g<f.length;g++){var h=f[g];h!=="."&&(h===".."?d.pop():d.push(h))}return"url(".concat(c).concat(d.join("/")).concat(c,")")})}var pr=/^[^ \t\n\r\u000c]+/,mr=/^[, \t\n\r\u000c]+/;function Ir(e,t){if(t.trim()==="")return t;var r=0;function n(c){var d,u=c.exec(t.substring(r));return u?(d=u[0],r+=d.length,d):""}for(var o=[];n(mr),!(r>=t.length);){var i=n(pr);if(i.slice(-1)===",")i=pe(e,i.substring(0,i.length-1)),o.push(i);else{var a="";i=pe(e,i);for(var s=!1;;){var l=t.charAt(r);if(l===""){o.push((i+a).trim());break}else if(s)l===")"&&(s=!1);else if(l===","){r+=1,o.push((i+a).trim());break}else l==="("&&(s=!0);a+=l,r+=1}}}return o.join(", ")}function pe(e,t){if(!t||t.trim()==="")return t;var r=e.createElement("a");return r.href=t,r.href}function Cr(e){return!!(e.tagName==="svg"||e.ownerSVGElement)}function qe(){var e=document.createElement("a");return e.href="",e.href}function St(e,t,r,n){return r==="src"||r==="href"&&n&&!(t==="use"&&n[0]==="#")||r==="xlink:href"&&n&&n[0]!=="#"||r==="background"&&n&&(t==="table"||t==="td"||t==="th")?pe(e,n):r==="srcset"&&n?Ir(e,n):r==="style"&&n?De(n,qe()):t==="object"&&r==="data"&&n?pe(e,n):n}function yr(e,t,r){if(typeof t=="string"){if(e.classList.contains(t))return!0}else for(var n=e.classList.length;n--;){var o=e.classList[n];if(t.test(o))return!0}return r?e.matches(r):!1}function _e(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return r?_e(e.parentNode,t,r):!1;for(var n=e.classList.length;n--;){var o=e.classList[n];if(t.test(o))return!0}return r?_e(e.parentNode,t,r):!1}function vt(e,t,r){var n=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(n===null)return!1;if(typeof t=="string"){if(n.classList.contains(t)||n.closest(".".concat(t)))return!0}else if(_e(n,t,!0))return!0;return!!(r&&(n.matches(r)||n.closest(r)))}function Sr(e,t,r){var n=e.contentWindow;if(n){var o=!1,i;try{i=n.document.readyState}catch{return}if(i!=="complete"){var a=setTimeout(function(){o||(t(),o=!0)},r);e.addEventListener("load",function(){clearTimeout(a),o=!0,t()});return}var s="about:blank";if(n.location.href!==s||e.src===s||e.src==="")return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}}function vr(e,t,r){var n=!1,o;try{o=e.sheet}catch{return}if(!o){var i=setTimeout(function(){n||(t(),n=!0)},r);e.addEventListener("load",function(){clearTimeout(i),n=!0,t()})}}function br(e,t){var r=t.doc,n=t.mirror,o=t.blockClass,i=t.blockSelector,a=t.maskTextClass,s=t.maskTextSelector,l=t.inlineStylesheet,c=t.maskInputOptions,d=c===void 0?{}:c,u=t.maskTextFn,g=t.maskInputFn,f=t.dataURLOptions,h=f===void 0?{}:f,I=t.inlineImages,S=t.recordCanvas,y=t.keepIframeSrcFn,p=t.newlyAddedElement,m=p===void 0?!1:p,w=Ar(r,n);switch(e.nodeType){case e.DOCUMENT_NODE:return e.compatMode!=="CSS1Compat"?{type:k.Document,childNodes:[],compatMode:e.compatMode}:{type:k.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:k.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:w};case e.ELEMENT_NODE:return kr(e,{doc:r,blockClass:o,blockSelector:i,inlineStylesheet:l,maskInputOptions:d,maskInputFn:g,dataURLOptions:h,inlineImages:I,recordCanvas:S,keepIframeSrcFn:y,newlyAddedElement:m,rootId:w});case e.TEXT_NODE:return wr(e,{maskTextClass:a,maskTextSelector:s,maskTextFn:u,rootId:w});case e.CDATA_SECTION_NODE:return{type:k.CDATA,textContent:"",rootId:w};case e.COMMENT_NODE:return{type:k.Comment,textContent:e.textContent||"",rootId:w};default:return!1}}function Ar(e,t){if(t.hasNode(e)){var r=t.getId(e);return r===1?void 0:r}}function wr(e,t){var r,n=t.maskTextClass,o=t.maskTextSelector,i=t.maskTextFn,a=t.rootId,s=e.parentNode&&e.parentNode.tagName,l=e.textContent,c=s==="STYLE"?!0:void 0,d=s==="SCRIPT"?!0:void 0;if(c&&l){try{e.nextSibling||e.previousSibling||!((r=e.parentNode.sheet)===null||r===void 0)&&r.cssRules&&(l=dr(e.parentNode.sheet))}catch(u){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(u),e)}l=De(l,qe())}return d&&(l="SCRIPT_PLACEHOLDER"),!c&&!d&&l&&vt(e,n,o)&&(l=i?i(l):l.replace(/[\S]/g,"*")),{type:k.Text,textContent:l||"",isStyle:c,rootId:a}}function kr(e,t){for(var r=t.doc,n=t.blockClass,o=t.blockSelector,i=t.inlineStylesheet,a=t.maskInputOptions,s=a===void 0?{}:a,l=t.maskInputFn,c=t.dataURLOptions,d=c===void 0?{}:c,u=t.inlineImages,g=t.recordCanvas,f=t.keepIframeSrcFn,h=t.newlyAddedElement,I=h===void 0?!1:h,S=t.rootId,y=yr(e,n,o),p=cr(e),m={},w=e.attributes.length,W=0;W<w;W++){var x=e.attributes[W];m[x.name]=St(r,p,x.name,x.value)}if(p==="link"&&i){var F=Array.from(r.styleSheets).find(function(H){return H.href===e.href}),T=null;F&&(T=Qe(F)),T&&(delete m.rel,delete m.href,m._cssText=De(T,F.href))}if(p==="style"&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){var T=Qe(e.sheet);T&&(m._cssText=De(T,qe()))}if(p==="input"||p==="textarea"||p==="select"){var Y=e.value,P=e.checked;m.type!=="radio"&&m.type!=="checkbox"&&m.type!=="submit"&&m.type!=="button"&&Y?m.value=Xe({type:m.type,tagName:p,value:Y,maskInputOptions:s,maskInputFn:l}):P&&(m.checked=P)}if(p==="option"&&(e.selected&&!s.select?m.selected=!0:delete m.selected),p==="canvas"&&g){if(e.__context==="2d")sr(e)||(m.rr_dataURL=e.toDataURL(d.type,d.quality));else if(!("__context"in e)){var Q=e.toDataURL(d.type,d.quality),X=document.createElement("canvas");X.width=e.width,X.height=e.height;var q=X.toDataURL(d.type,d.quality);Q!==q&&(m.rr_dataURL=Q)}}if(p==="img"&&u){fe||(fe=r.createElement("canvas"),yt=fe.getContext("2d"));var L=e,z=L.crossOrigin;L.crossOrigin="anonymous";var $=function(){try{fe.width=L.naturalWidth,fe.height=L.naturalHeight,yt.drawImage(L,0,0),m.rr_dataURL=fe.toDataURL(d.type,d.quality)}catch(H){console.warn("Cannot inline img src=".concat(L.currentSrc,"! Error: ").concat(H))}z?m.crossOrigin=z:L.removeAttribute("crossorigin")};L.complete&&L.naturalWidth!==0?$():L.onload=$}if((p==="audio"||p==="video")&&(m.rr_mediaState=e.paused?"paused":"played",m.rr_mediaCurrentTime=e.currentTime),I||(e.scrollLeft&&(m.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(m.rr_scrollTop=e.scrollTop)),y){var te=e.getBoundingClientRect(),ne=te.width,V=te.height;m={class:m.class,rr_width:"".concat(ne,"px"),rr_height:"".concat(V,"px")}}return p==="iframe"&&!f(m.src)&&(e.contentDocument||(m.rr_src=m.src),delete m.src),{type:k.Element,tagName:p,attributes:m,childNodes:[],isSVG:Cr(e)||void 0,needBlock:y,rootId:S}}function M(e){return e===void 0?"":e.toLowerCase()}function Mr(e,t){if(t.comment&&e.type===k.Comment)return!0;if(e.type===k.Element){if(t.script&&(e.tagName==="script"||e.tagName==="link"&&e.attributes.rel==="preload"&&e.attributes.as==="script"||e.tagName==="link"&&e.attributes.rel==="prefetch"&&typeof e.attributes.href=="string"&&e.attributes.href.endsWith(".js")))return!0;if(t.headFavicon&&(e.tagName==="link"&&e.attributes.rel==="shortcut icon"||e.tagName==="meta"&&(M(e.attributes.name).match(/^msapplication-tile(image|color)$/)||M(e.attributes.name)==="application-name"||M(e.attributes.rel)==="icon"||M(e.attributes.rel)==="apple-touch-icon"||M(e.attributes.rel)==="shortcut icon")))return!0;if(e.tagName==="meta"){if(t.headMetaDescKeywords&&M(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&(M(e.attributes.property).match(/^(og|twitter|fb):/)||M(e.attributes.name).match(/^(og|twitter):/)||M(e.attributes.name)==="pinterest"))return!0;if(t.headMetaRobots&&(M(e.attributes.name)==="robots"||M(e.attributes.name)==="googlebot"||M(e.attributes.name)==="bingbot"))return!0;if(t.headMetaHttpEquiv&&e.attributes["http-equiv"]!==void 0)return!0;if(t.headMetaAuthorship&&(M(e.attributes.name)==="author"||M(e.attributes.name)==="generator"||M(e.attributes.name)==="framework"||M(e.attributes.name)==="publisher"||M(e.attributes.name)==="progid"||M(e.attributes.property).match(/^article:/)||M(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&(M(e.attributes.name)==="google-site-verification"||M(e.attributes.name)==="yandex-verification"||M(e.attributes.name)==="csrf-token"||M(e.attributes.name)==="p:domain_verify"||M(e.attributes.name)==="verify-v1"||M(e.attributes.name)==="verification"||M(e.attributes.name)==="shopify-checkout-api-token"))return!0}}return!1}function me(e,t){var r=t.doc,n=t.mirror,o=t.blockClass,i=t.blockSelector,a=t.maskTextClass,s=t.maskTextSelector,l=t.skipChild,c=l===void 0?!1:l,d=t.inlineStylesheet,u=d===void 0?!0:d,g=t.maskInputOptions,f=g===void 0?{}:g,h=t.maskTextFn,I=t.maskInputFn,S=t.slimDOMOptions,y=t.dataURLOptions,p=y===void 0?{}:y,m=t.inlineImages,w=m===void 0?!1:m,W=t.recordCanvas,x=W===void 0?!1:W,F=t.onSerialize,T=t.onIframeLoad,Y=t.iframeLoadTimeout,P=Y===void 0?5e3:Y,Q=t.onStylesheetLoad,X=t.stylesheetLoadTimeout,q=X===void 0?5e3:X,L=t.keepIframeSrcFn,z=L===void 0?function(){return!1}:L,$=t.newlyAddedElement,te=$===void 0?!1:$,ne=t.preserveWhiteSpace,V=ne===void 0?!0:ne,H=br(e,{doc:r,mirror:n,blockClass:o,blockSelector:i,maskTextClass:a,maskTextSelector:s,inlineStylesheet:u,maskInputOptions:f,maskTextFn:h,maskInputFn:I,dataURLOptions:p,inlineImages:w,recordCanvas:x,keepIframeSrcFn:z,newlyAddedElement:te});if(!H)return console.warn(e,"not serialized"),null;var ie;n.hasNode(e)?ie=n.getId(e):Mr(H,S)||!V&&H.type===k.Text&&!H.isStyle&&!H.textContent.replace(/^\s+|\s+$/gm,"").length?ie=Te:ie=Ct();var R=Object.assign(H,{id:ie});if(n.add(e,R),ie===Te)return null;F&&F(e);var ee=!c;if(R.type===k.Element){ee=ee&&!R.needBlock,delete R.needBlock;var re=e.shadowRoot;re&&Me(re)&&(R.isShadowHost=!0)}if((R.type===k.Document||R.type===k.Element)&&ee){S.headWhitespace&&R.type===k.Element&&R.tagName==="head"&&(V=!1);for(var we={doc:r,mirror:n,blockClass:o,blockSelector:i,maskTextClass:a,maskTextSelector:s,skipChild:c,inlineStylesheet:u,maskInputOptions:f,maskTextFn:h,maskInputFn:I,slimDOMOptions:S,dataURLOptions:p,inlineImages:w,recordCanvas:x,preserveWhiteSpace:V,onSerialize:F,onIframeLoad:T,iframeLoadTimeout:P,onStylesheetLoad:Q,stylesheetLoadTimeout:q,keepIframeSrcFn:z},C=0,B=Array.from(e.childNodes);C<B.length;C++){var Z=B[C],E=me(Z,we);E&&R.childNodes.push(E)}if(ge(e)&&e.shadowRoot)for(var J=0,A=Array.from(e.shadowRoot.childNodes);J<A.length;J++){var Z=A[J],E=me(Z,we);E&&(Me(e.shadowRoot)&&(E.isShadow=!0),R.childNodes.push(E))}}return e.parentNode&&ke(e.parentNode)&&Me(e.parentNode)&&(R.isShadow=!0),R.type===k.Element&&R.tagName==="iframe"&&Sr(e,function(){var K=e.contentDocument;if(K&&T){var Fe=me(K,{doc:K,mirror:n,blockClass:o,blockSelector:i,maskTextClass:a,maskTextSelector:s,skipChild:!1,inlineStylesheet:u,maskInputOptions:f,maskTextFn:h,maskInputFn:I,slimDOMOptions:S,dataURLOptions:p,inlineImages:w,recordCanvas:x,preserveWhiteSpace:V,onSerialize:F,onIframeLoad:T,iframeLoadTimeout:P,onStylesheetLoad:Q,stylesheetLoadTimeout:q,keepIframeSrcFn:z});Fe&&T(e,Fe)}},P),R.type===k.Element&&R.tagName==="link"&&R.attributes.rel==="stylesheet"&&vr(e,function(){if(Q){var K=me(e,{doc:r,mirror:n,blockClass:o,blockSelector:i,maskTextClass:a,maskTextSelector:s,skipChild:!1,inlineStylesheet:u,maskInputOptions:f,maskTextFn:h,maskInputFn:I,slimDOMOptions:S,dataURLOptions:p,inlineImages:w,recordCanvas:x,preserveWhiteSpace:V,onSerialize:F,onIframeLoad:T,iframeLoadTimeout:P,onStylesheetLoad:Q,stylesheetLoadTimeout:q,keepIframeSrcFn:z});K&&Q(e,K)}},q),R}function Tr(e,t){var r=t||{},n=r.mirror,o=n===void 0?new mt:n,i=r.blockClass,a=i===void 0?"rr-block":i,s=r.blockSelector,l=s===void 0?null:s,c=r.maskTextClass,d=c===void 0?"rr-mask":c,u=r.maskTextSelector,g=u===void 0?null:u,f=r.inlineStylesheet,h=f===void 0?!0:f,I=r.inlineImages,S=I===void 0?!1:I,y=r.recordCanvas,p=y===void 0?!1:y,m=r.maskAllInputs,w=m===void 0?!1:m,W=r.maskTextFn,x=r.maskInputFn,F=r.slimDOM,T=F===void 0?!1:F,Y=r.dataURLOptions,P=r.preserveWhiteSpace,Q=r.onSerialize,X=r.onIframeLoad,q=r.iframeLoadTimeout,L=r.onStylesheetLoad,z=r.stylesheetLoadTimeout,$=r.keepIframeSrcFn,te=$===void 0?function(){return!1}:$,ne=w===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:w===!1?{password:!0}:w,V=T===!0||T==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:T==="all",headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:T===!1?{}:T;return me(e,{doc:e,mirror:o,blockClass:a,blockSelector:l,maskTextClass:d,maskTextSelector:g,skipChild:!1,inlineStylesheet:h,maskInputOptions:ne,maskTextFn:W,maskInputFn:x,slimDOMOptions:V,dataURLOptions:Y,inlineImages:S,recordCanvas:p,preserveWhiteSpace:P,onSerialize:Q,onIframeLoad:X,iframeLoadTimeout:q,onStylesheetLoad:L,stylesheetLoadTimeout:z,keepIframeSrcFn:te,newlyAddedElement:!1})}function D(e,t,r=document){const n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}const Ie=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;let bt={map:{},getId(){return console.error(Ie),-1},getNode(){return console.error(Ie),null},removeNodeFromMap(){console.error(Ie)},has(){return console.error(Ie),!1},reset(){console.error(Ie)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(bt=new Proxy(bt,{get(e,t,r){return t==="map"&&console.error(Ie),Reflect.get(e,t,r)}}));function Ee(e,t,r={}){let n=null,o=0;return function(...i){const a=Date.now();!o&&r.leading===!1&&(o=a);const s=t-(a-o),l=this;s<=0||s>t?(n&&(clearTimeout(n),n=null),o=a,e.apply(l,i)):!n&&r.trailing!==!1&&(n=setTimeout(()=>{o=r.leading===!1?0:Date.now(),n=null,e.apply(l,i)},s))}}function We(e,t,r,n,o=window){const i=o.Object.getOwnPropertyDescriptor(e,t);return o.Object.defineProperty(e,t,n?r:{set(a){setTimeout(()=>{r.set.call(this,a)},0),i&&i.set&&i.set.call(this,a)}}),()=>We(e,t,i||{},!0)}function Ce(e,t,r){try{if(!(t in e))return()=>{};const n=e[t],o=r(n);return typeof o=="function"&&(o.prototype=o.prototype||{},Object.defineProperties(o,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=o,()=>{e[t]=n}}catch{return()=>{}}}function At(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function wt(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function _(e,t,r,n){if(!e)return!1;const o=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(!o)return!1;if(typeof t=="string"){if(o.classList.contains(t)||n&&o.closest("."+t)!==null)return!0}else if(_e(o,t,n))return!0;return!!(r&&(e.matches(r)||n&&o.closest(r)!==null))}function Er(e,t){return t.getId(e)!==-1}function je(e,t){return t.getId(e)===Te}function kt(e,t){if(ke(e))return!1;const r=t.getId(e);return t.has(r)?e.parentNode&&e.parentNode.nodeType===e.DOCUMENT_NODE?!1:e.parentNode?kt(e.parentNode,t):!0:!0}function Mt(e){return!!e.changedTouches}function Nr(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...t)=>{let r=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do if(this===r)return!0;while(r=r&&r.parentNode);return!1})}function Tt(e,t){return!!(e.nodeName==="IFRAME"&&t.getMeta(e))}function Et(e,t){return!!(e.nodeName==="LINK"&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&e.getAttribute("rel")==="stylesheet"&&t.getMeta(e))}function Nt(e){return!!(e!=null&&e.shadowRoot)}class Rr{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){var r;return(r=this.styleIDMap.get(t))!==null&&r!==void 0?r:-1}has(t){return this.styleIDMap.has(t)}add(t,r){if(this.has(t))return this.getId(t);let n;return r===void 0?n=this.id++:n=r,this.styleIDMap.set(t,n),this.idStyleMap.set(n,t),n}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}var b=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(b||{}),v=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e))(v||{}),$e=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))($e||{}),ye=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(ye||{});function Rt(e){return"__ln"in e}class Or{constructor(){this.length=0,this.head=null}get(t){if(t>=this.length)throw new Error("Position outside of list range");let r=this.head;for(let n=0;n<t;n++)r=(r==null?void 0:r.next)||null;return r}addNode(t){const r={value:t,previous:null,next:null};if(t.__ln=r,t.previousSibling&&Rt(t.previousSibling)){const n=t.previousSibling.__ln.next;r.next=n,r.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=r,n&&(n.previous=r)}else if(t.nextSibling&&Rt(t.nextSibling)&&t.nextSibling.__ln.previous){const n=t.nextSibling.__ln.previous;r.previous=n,r.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=r,n&&(n.next=r)}else this.head&&(this.head.previous=r),r.next=this.head,this.head=r;this.length++}removeNode(t){const r=t.__ln;this.head&&(r.previous?(r.previous.next=r.next,r.next&&(r.next.previous=r.previous)):(this.head=r.next,this.head&&(this.head.previous=null)),t.__ln&&delete t.__ln,this.length--)}}const Ot=(e,t)=>`${e}@${t}`;class Lr{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=t=>{t.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const t=[],r=new Or,n=s=>{let l=s,c=Te;for(;c===Te;)l=l&&l.nextSibling,c=l&&this.mirror.getId(l);return c},o=s=>{var l,c,d,u;let g=null;((c=(l=s.getRootNode)===null||l===void 0?void 0:l.call(s))===null||c===void 0?void 0:c.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&s.getRootNode().host&&(g=s.getRootNode().host);let f=g;for(;((u=(d=f==null?void 0:f.getRootNode)===null||d===void 0?void 0:d.call(f))===null||u===void 0?void 0:u.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&f.getRootNode().host;)f=f.getRootNode().host;const h=!this.doc.contains(s)&&(!f||!this.doc.contains(f));if(!s.parentNode||h)return;const I=ke(s.parentNode)?this.mirror.getId(g):this.mirror.getId(s.parentNode),S=n(s);if(I===-1||S===-1)return r.addNode(s);const y=me(s,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:p=>{Tt(p,this.mirror)&&this.iframeManager.addIframe(p),Et(p,this.mirror)&&this.stylesheetManager.trackLinkElement(p),Nt(s)&&this.shadowDomManager.addShadowRoot(s.shadowRoot,this.doc)},onIframeLoad:(p,m)=>{this.iframeManager.attachIframe(p,m),this.shadowDomManager.observeAttachShadow(p)},onStylesheetLoad:(p,m)=>{this.stylesheetManager.attachLinkElement(p,m)}});y&&t.push({parentId:I,nextId:S,node:y})};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const s of Array.from(this.movedSet.values()))Lt(this.removes,s,this.mirror)&&!this.movedSet.has(s.parentNode)||o(s);for(const s of Array.from(this.addedSet.values()))!Ft(this.droppedSet,s)&&!Lt(this.removes,s,this.mirror)||Ft(this.movedSet,s)?o(s):this.droppedSet.add(s);let i=null;for(;r.length;){let s=null;if(i){const l=this.mirror.getId(i.value.parentNode),c=n(i.value);l!==-1&&c!==-1&&(s=i)}if(!s)for(let l=r.length-1;l>=0;l--){const c=r.get(l);if(c){const d=this.mirror.getId(c.value.parentNode);if(n(c.value)===-1)continue;if(d!==-1){s=c;break}else{const g=c.value;if(g.parentNode&&g.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const f=g.parentNode.host;if(this.mirror.getId(f)!==-1){s=c;break}}}}}if(!s){for(;r.head;)r.removeNode(r.head.value);break}i=s.previous,r.removeNode(s.value),o(s.value)}const a={texts:this.texts.map(s=>({id:this.mirror.getId(s.node),value:s.value})).filter(s=>this.mirror.has(s.id)),attributes:this.attributes.map(s=>({id:this.mirror.getId(s.node),attributes:s.attributes})).filter(s=>this.mirror.has(s.id)),removes:this.removes,adds:t};!a.texts.length&&!a.attributes.length&&!a.removes.length&&!a.adds.length||(this.texts=[],this.attributes=[],this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(a))},this.processMutation=t=>{if(!je(t.target,this.mirror))switch(t.type){case"characterData":{const r=t.target.textContent;!_(t.target,this.blockClass,this.blockSelector,!1)&&r!==t.oldValue&&this.texts.push({value:vt(t.target,this.maskTextClass,this.maskTextSelector)&&r?this.maskTextFn?this.maskTextFn(r):r.replace(/[\S]/g,"*"):r,node:t.target});break}case"attributes":{const r=t.target;let n=t.target.getAttribute(t.attributeName);if(t.attributeName==="value"&&(n=Xe({maskInputOptions:this.maskInputOptions,tagName:t.target.tagName,type:t.target.getAttribute("type"),value:n,maskInputFn:this.maskInputFn})),_(t.target,this.blockClass,this.blockSelector,!1)||n===t.oldValue)return;let o=this.attributes.find(i=>i.node===t.target);if(r.tagName==="IFRAME"&&t.attributeName==="src"&&!this.keepIframeSrcFn(n))if(!r.contentDocument)t.attributeName="rr_src";else return;if(o||(o={node:t.target,attributes:{}},this.attributes.push(o)),t.attributeName==="style"){const i=this.doc.createElement("span");t.oldValue&&i.setAttribute("style",t.oldValue),(o.attributes.style===void 0||o.attributes.style===null)&&(o.attributes.style={});const a=o.attributes.style;for(const s of Array.from(r.style)){const l=r.style.getPropertyValue(s),c=r.style.getPropertyPriority(s);(l!==i.style.getPropertyValue(s)||c!==i.style.getPropertyPriority(s))&&(c===""?a[s]=l:a[s]=[l,c])}for(const s of Array.from(i.style))r.style.getPropertyValue(s)===""&&(a[s]=!1)}else o.attributes[t.attributeName]=St(this.doc,r.tagName,t.attributeName,n);break}case"childList":{if(_(t.target,this.blockClass,this.blockSelector,!0))return;t.addedNodes.forEach(r=>this.genAdds(r,t.target)),t.removedNodes.forEach(r=>{const n=this.mirror.getId(r),o=ke(t.target)?this.mirror.getId(t.target.host):this.mirror.getId(t.target);_(t.target,this.blockClass,this.blockSelector,!1)||je(r,this.mirror)||!Er(r,this.mirror)||(this.addedSet.has(r)?(et(this.addedSet,r),this.droppedSet.add(r)):this.addedSet.has(t.target)&&n===-1||kt(t.target,this.mirror)||(this.movedSet.has(r)&&this.movedMap[Ot(n,o)]?et(this.movedSet,r):this.removes.push({parentId:o,id:n,isShadow:ke(t.target)&&Me(t.target)?!0:void 0})),this.mapRemoves.push(r))});break}}},this.genAdds=(t,r)=>{if(this.mirror.hasNode(t)){if(je(t,this.mirror))return;this.movedSet.add(t);let n=null;r&&this.mirror.hasNode(r)&&(n=this.mirror.getId(r)),n&&n!==-1&&(this.movedMap[Ot(this.mirror.getId(t),n)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);_(t,this.blockClass,this.blockSelector,!1)||t.childNodes.forEach(n=>this.genAdds(n))}}init(t){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager"].forEach(r=>{this[r]=t[r]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function et(e,t){e.delete(t),t.childNodes.forEach(r=>et(e,r))}function Lt(e,t,r){return e.length===0?!1:xt(e,t,r)}function xt(e,t,r){const{parentNode:n}=t;if(!n)return!1;const o=r.getId(n);return e.some(i=>i.id===o)?!0:xt(e,n,r)}function Ft(e,t){return e.size===0?!1:Dt(e,t)}function Dt(e,t){const{parentNode:r}=t;return r?e.has(r)?!0:Dt(e,r):!1}const se=[],_t=typeof CSSGroupingRule<"u",Wt=typeof CSSMediaRule<"u",Bt=typeof CSSSupportsRule<"u",Ut=typeof CSSConditionRule<"u";function Ne(e){try{if("composedPath"in e){const t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0];return e.target}catch{return e.target}}function Gt(e,t){var r,n;const o=new Lr;se.push(o),o.init(e);let i=window.MutationObserver||window.__rrMutationObserver;const a=(n=(r=window==null?void 0:window.Zone)===null||r===void 0?void 0:r.__symbol__)===null||n===void 0?void 0:n.call(r,"MutationObserver");a&&window[a]&&(i=window[a]);const s=new i(o.processMutations.bind(o));return s.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),s}function xr({mousemoveCb:e,sampling:t,doc:r,mirror:n}){if(t.mousemove===!1)return()=>{};const o=typeof t.mousemove=="number"?t.mousemove:50,i=typeof t.mousemoveCallback=="number"?t.mousemoveCallback:500;let a=[],s;const l=Ee(u=>{const g=Date.now()-s;e(a.map(f=>(f.timeOffset-=g,f)),u),a=[],s=null},i),c=Ee(u=>{const g=Ne(u),{clientX:f,clientY:h}=Mt(u)?u.changedTouches[0]:u;s||(s=Date.now()),a.push({x:f,y:h,id:n.getId(g),timeOffset:Date.now()-s}),l(typeof DragEvent<"u"&&u instanceof DragEvent?v.Drag:u instanceof MouseEvent?v.MouseMove:v.TouchMove)},o,{trailing:!1}),d=[D("mousemove",c,r),D("touchmove",c,r),D("drag",c,r)];return()=>{d.forEach(u=>u())}}function Fr({mouseInteractionCb:e,doc:t,mirror:r,blockClass:n,blockSelector:o,sampling:i}){if(i.mouseInteraction===!1)return()=>{};const a=i.mouseInteraction===!0||i.mouseInteraction===void 0?{}:i.mouseInteraction,s=[],l=c=>d=>{const u=Ne(d);if(_(u,n,o,!0))return;const g=Mt(d)?d.changedTouches[0]:d;if(!g)return;const f=r.getId(u),{clientX:h,clientY:I}=g;e({type:$e[c],id:f,x:h,y:I})};return Object.keys($e).filter(c=>Number.isNaN(Number(c))&&!c.endsWith("_Departed")&&a[c]!==!1).forEach(c=>{const d=c.toLowerCase(),u=l(c);s.push(D(d,u,t))}),()=>{s.forEach(c=>c())}}function Vt({scrollCb:e,doc:t,mirror:r,blockClass:n,blockSelector:o,sampling:i}){const a=Ee(s=>{const l=Ne(s);if(!l||_(l,n,o,!0))return;const c=r.getId(l);if(l===t){const d=t.scrollingElement||t.documentElement;e({id:c,x:d.scrollLeft,y:d.scrollTop})}else e({id:c,x:l.scrollLeft,y:l.scrollTop})},i.scroll||100);return D("scroll",a,t)}function Dr({viewportResizeCb:e}){let t=-1,r=-1;const n=Ee(()=>{const o=At(),i=wt();(t!==o||r!==i)&&(e({width:Number(i),height:Number(o)}),t=o,r=i)},200);return D("resize",n,window)}function Zt(e,t){const r=Object.assign({},e);return t||delete r.userTriggered,r}const _r=["INPUT","TEXTAREA","SELECT"],Kt=new WeakMap;function Wr({inputCb:e,doc:t,mirror:r,blockClass:n,blockSelector:o,ignoreClass:i,maskInputOptions:a,maskInputFn:s,sampling:l,userTriggeredOnInput:c}){function d(y){let p=Ne(y);const m=y.isTrusted;if(p&&p.tagName==="OPTION"&&(p=p.parentElement),!p||!p.tagName||_r.indexOf(p.tagName)<0||_(p,n,o,!0))return;const w=p.type;if(p.classList.contains(i))return;let W=p.value,x=!1;w==="radio"||w==="checkbox"?x=p.checked:(a[p.tagName.toLowerCase()]||a[w])&&(W=Xe({maskInputOptions:a,tagName:p.tagName,type:w,value:W,maskInputFn:s})),u(p,Zt({text:W,isChecked:x,userTriggered:m},c));const F=p.name;w==="radio"&&F&&x&&t.querySelectorAll(`input[type="radio"][name="${F}"]`).forEach(T=>{T!==p&&u(T,Zt({text:T.value,isChecked:!x,userTriggered:!1},c))})}function u(y,p){const m=Kt.get(y);if(!m||m.text!==p.text||m.isChecked!==p.isChecked){Kt.set(y,p);const w=r.getId(y);e(Object.assign(Object.assign({},p),{id:w}))}}const f=(l.input==="last"?["change"]:["input","change"]).map(y=>D(y,d,t)),h=t.defaultView;if(!h)return()=>{f.forEach(y=>y())};const I=h.Object.getOwnPropertyDescriptor(h.HTMLInputElement.prototype,"value"),S=[[h.HTMLInputElement.prototype,"value"],[h.HTMLInputElement.prototype,"checked"],[h.HTMLSelectElement.prototype,"value"],[h.HTMLTextAreaElement.prototype,"value"],[h.HTMLSelectElement.prototype,"selectedIndex"],[h.HTMLOptionElement.prototype,"selected"]];return I&&I.set&&f.push(...S.map(y=>We(y[0],y[1],{set(){d({target:this})}},!1,h))),()=>{f.forEach(y=>y())}}function Be(e){const t=[];function r(n,o){if(_t&&n.parentRule instanceof CSSGroupingRule||Wt&&n.parentRule instanceof CSSMediaRule||Bt&&n.parentRule instanceof CSSSupportsRule||Ut&&n.parentRule instanceof CSSConditionRule){const a=Array.from(n.parentRule.cssRules).indexOf(n);o.unshift(a)}else if(n.parentStyleSheet){const a=Array.from(n.parentStyleSheet.cssRules).indexOf(n);o.unshift(a)}return o}return r(e,t)}function oe(e,t,r){let n,o;return e?(e.ownerNode?n=t.getId(e.ownerNode):o=r.getId(e),{styleId:o,id:n}):{}}function Br({styleSheetRuleCb:e,mirror:t,stylesheetManager:r},{win:n}){const o=n.CSSStyleSheet.prototype.insertRule;n.CSSStyleSheet.prototype.insertRule=function(d,u){const{id:g,styleId:f}=oe(this,t,r.styleMirror);return(g&&g!==-1||f&&f!==-1)&&e({id:g,styleId:f,adds:[{rule:d,index:u}]}),o.apply(this,[d,u])};const i=n.CSSStyleSheet.prototype.deleteRule;n.CSSStyleSheet.prototype.deleteRule=function(d){const{id:u,styleId:g}=oe(this,t,r.styleMirror);return(u&&u!==-1||g&&g!==-1)&&e({id:u,styleId:g,removes:[{index:d}]}),i.apply(this,[d])};let a;n.CSSStyleSheet.prototype.replace&&(a=n.CSSStyleSheet.prototype.replace,n.CSSStyleSheet.prototype.replace=function(d){const{id:u,styleId:g}=oe(this,t,r.styleMirror);return(u&&u!==-1||g&&g!==-1)&&e({id:u,styleId:g,replace:d}),a.apply(this,[d])});let s;n.CSSStyleSheet.prototype.replaceSync&&(s=n.CSSStyleSheet.prototype.replaceSync,n.CSSStyleSheet.prototype.replaceSync=function(d){const{id:u,styleId:g}=oe(this,t,r.styleMirror);return(u&&u!==-1||g&&g!==-1)&&e({id:u,styleId:g,replaceSync:d}),s.apply(this,[d])});const l={};_t?l.CSSGroupingRule=n.CSSGroupingRule:(Wt&&(l.CSSMediaRule=n.CSSMediaRule),Ut&&(l.CSSConditionRule=n.CSSConditionRule),Bt&&(l.CSSSupportsRule=n.CSSSupportsRule));const c={};return Object.entries(l).forEach(([d,u])=>{c[d]={insertRule:u.prototype.insertRule,deleteRule:u.prototype.deleteRule},u.prototype.insertRule=function(g,f){const{id:h,styleId:I}=oe(this.parentStyleSheet,t,r.styleMirror);return(h&&h!==-1||I&&I!==-1)&&e({id:h,styleId:I,adds:[{rule:g,index:[...Be(this),f||0]}]}),c[d].insertRule.apply(this,[g,f])},u.prototype.deleteRule=function(g){const{id:f,styleId:h}=oe(this.parentStyleSheet,t,r.styleMirror);return(f&&f!==-1||h&&h!==-1)&&e({id:f,styleId:h,removes:[{index:[...Be(this),g]}]}),c[d].deleteRule.apply(this,[g])}}),()=>{n.CSSStyleSheet.prototype.insertRule=o,n.CSSStyleSheet.prototype.deleteRule=i,a&&(n.CSSStyleSheet.prototype.replace=a),s&&(n.CSSStyleSheet.prototype.replaceSync=s),Object.entries(l).forEach(([d,u])=>{u.prototype.insertRule=c[d].insertRule,u.prototype.deleteRule=c[d].deleteRule})}}function Yt({mirror:e,stylesheetManager:t},r){var n,o,i;let a=null;r.nodeName==="#document"?a=e.getId(r):a=e.getId(r.host);const s=r.nodeName==="#document"?(n=r.defaultView)===null||n===void 0?void 0:n.Document:(i=(o=r.ownerDocument)===null||o===void 0?void 0:o.defaultView)===null||i===void 0?void 0:i.ShadowRoot,l=Object.getOwnPropertyDescriptor(s==null?void 0:s.prototype,"adoptedStyleSheets");return a===null||a===-1||!s||!l?()=>{}:(Object.defineProperty(r,"adoptedStyleSheets",{configurable:l.configurable,enumerable:l.enumerable,get(){var c;return(c=l.get)===null||c===void 0?void 0:c.call(this)},set(c){var d;const u=(d=l.set)===null||d===void 0?void 0:d.call(this,c);if(a!==null&&a!==-1)try{t.adoptStyleSheets(c,a)}catch{}return u}}),()=>{Object.defineProperty(r,"adoptedStyleSheets",{configurable:l.configurable,enumerable:l.enumerable,get:l.get,set:l.set})})}function Ur({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:r,stylesheetManager:n},{win:o}){const i=o.CSSStyleDeclaration.prototype.setProperty;o.CSSStyleDeclaration.prototype.setProperty=function(s,l,c){var d;if(r.has(s))return i.apply(this,[s,l,c]);const{id:u,styleId:g}=oe((d=this.parentRule)===null||d===void 0?void 0:d.parentStyleSheet,t,n.styleMirror);return(u&&u!==-1||g&&g!==-1)&&e({id:u,styleId:g,set:{property:s,value:l,priority:c},index:Be(this.parentRule)}),i.apply(this,[s,l,c])};const a=o.CSSStyleDeclaration.prototype.removeProperty;return o.CSSStyleDeclaration.prototype.removeProperty=function(s){var l;if(r.has(s))return a.apply(this,[s]);const{id:c,styleId:d}=oe((l=this.parentRule)===null||l===void 0?void 0:l.parentStyleSheet,t,n.styleMirror);return(c&&c!==-1||d&&d!==-1)&&e({id:c,styleId:d,remove:{property:s},index:Be(this.parentRule)}),a.apply(this,[s])},()=>{o.CSSStyleDeclaration.prototype.setProperty=i,o.CSSStyleDeclaration.prototype.removeProperty=a}}function Gr({mediaInteractionCb:e,blockClass:t,blockSelector:r,mirror:n,sampling:o}){const i=s=>Ee(l=>{const c=Ne(l);if(!c||_(c,t,r,!0))return;const{currentTime:d,volume:u,muted:g,playbackRate:f}=c;e({type:s,id:n.getId(c),currentTime:d,volume:u,muted:g,playbackRate:f})},o.media||500),a=[D("play",i(0)),D("pause",i(1)),D("seeked",i(2)),D("volumechange",i(3)),D("ratechange",i(4))];return()=>{a.forEach(s=>s())}}function Vr({fontCb:e,doc:t}){const r=t.defaultView;if(!r)return()=>{};const n=[],o=new WeakMap,i=r.FontFace;r.FontFace=function(l,c,d){const u=new i(l,c,d);return o.set(u,{family:l,buffer:typeof c!="string",descriptors:d,fontSource:typeof c=="string"?c:JSON.stringify(Array.from(new Uint8Array(c)))}),u};const a=Ce(t.fonts,"add",function(s){return function(l){return setTimeout(()=>{const c=o.get(l);c&&(e(c),o.delete(l))},0),s.apply(this,[l])}});return n.push(()=>{r.FontFace=i}),n.push(a),()=>{n.forEach(s=>s())}}function Zr(e){const{doc:t,mirror:r,blockClass:n,blockSelector:o,selectionCb:i}=e;let a=!0;const s=()=>{const l=t.getSelection();if(!l||a&&(l!=null&&l.isCollapsed))return;a=l.isCollapsed||!1;const c=[],d=l.rangeCount||0;for(let u=0;u<d;u++){const g=l.getRangeAt(u),{startContainer:f,startOffset:h,endContainer:I,endOffset:S}=g;_(f,n,o,!0)||_(I,n,o,!0)||c.push({start:r.getId(f),startOffset:h,end:r.getId(I),endOffset:S})}i({ranges:c})};return s(),D("selectionchange",s)}function Kr(e,t){const{mutationCb:r,mousemoveCb:n,mouseInteractionCb:o,scrollCb:i,viewportResizeCb:a,inputCb:s,mediaInteractionCb:l,styleSheetRuleCb:c,styleDeclarationCb:d,canvasMutationCb:u,fontCb:g,selectionCb:f}=e;e.mutationCb=(...h)=>{t.mutation&&t.mutation(...h),r(...h)},e.mousemoveCb=(...h)=>{t.mousemove&&t.mousemove(...h),n(...h)},e.mouseInteractionCb=(...h)=>{t.mouseInteraction&&t.mouseInteraction(...h),o(...h)},e.scrollCb=(...h)=>{t.scroll&&t.scroll(...h),i(...h)},e.viewportResizeCb=(...h)=>{t.viewportResize&&t.viewportResize(...h),a(...h)},e.inputCb=(...h)=>{t.input&&t.input(...h),s(...h)},e.mediaInteractionCb=(...h)=>{t.mediaInteaction&&t.mediaInteaction(...h),l(...h)},e.styleSheetRuleCb=(...h)=>{t.styleSheetRule&&t.styleSheetRule(...h),c(...h)},e.styleDeclarationCb=(...h)=>{t.styleDeclaration&&t.styleDeclaration(...h),d(...h)},e.canvasMutationCb=(...h)=>{t.canvasMutation&&t.canvasMutation(...h),u(...h)},e.fontCb=(...h)=>{t.font&&t.font(...h),g(...h)},e.selectionCb=(...h)=>{t.selection&&t.selection(...h),f(...h)}}function Yr(e,t={}){const r=e.doc.defaultView;if(!r)return()=>{};Kr(e,t);const n=Gt(e,e.doc),o=xr(e),i=Fr(e),a=Vt(e),s=Dr(e),l=Wr(e),c=Gr(e),d=Br(e,{win:r}),u=Yt(e,e.doc),g=Ur(e,{win:r}),f=e.collectFonts?Vr(e):()=>{},h=Zr(e),I=[];for(const S of e.plugins)I.push(S.observer(S.callback,r,S.options));return()=>{se.forEach(S=>S.reset()),n.disconnect(),o(),i(),a(),s(),l(),c(),d(),u(),g(),f(),h(),I.forEach(S=>S())}}class Pt{constructor(t){this.generateIdFn=t,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(t,r,n,o){const i=n||this.getIdToRemoteIdMap(t),a=o||this.getRemoteIdToIdMap(t);let s=i.get(r);return s||(s=this.generateIdFn(),i.set(r,s),a.set(s,r)),s}getIds(t,r){const n=this.getIdToRemoteIdMap(t),o=this.getRemoteIdToIdMap(t);return r.map(i=>this.getId(t,i,n,o))}getRemoteId(t,r,n){const o=n||this.getRemoteIdToIdMap(t);if(typeof r!="number")return r;const i=o.get(r);return i||-1}getRemoteIds(t,r){const n=this.getRemoteIdToIdMap(t);return r.map(o=>this.getRemoteId(t,o,n))}reset(t){if(!t){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let r=this.iframeIdToRemoteIdMap.get(t);return r||(r=new Map,this.iframeIdToRemoteIdMap.set(t,r)),r}getRemoteIdToIdMap(t){let r=this.iframeRemoteIdToIdMap.get(t);return r||(r=new Map,this.iframeRemoteIdToIdMap.set(t,r)),r}}class Pr{constructor(t){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new Pt(Ct),this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new Pt(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,r){var n;this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:r}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),(n=this.loadListener)===null||n===void 0||n.call(this,t),t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){if(t.data.type==="rrweb"){if(!t.source)return;const n=this.crossOriginIframeMap.get(t.source);if(!n)return;const o=this.transformCrossOriginEvent(n,t.data.event);o&&this.wrappedEmit(o,t.data.isCheckout)}}transformCrossOriginEvent(t,r){var n;switch(r.type){case b.FullSnapshot:return this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(r.data.node,t),{timestamp:r.timestamp,type:b.IncrementalSnapshot,data:{source:v.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:r.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case b.Meta:case b.Load:case b.DomContentLoaded:return!1;case b.Plugin:return r;case b.Custom:return this.replaceIds(r.data.payload,t,["id","parentId","previousId","nextId"]),r;case b.IncrementalSnapshot:switch(r.data.source){case v.Mutation:return r.data.adds.forEach(o=>{this.replaceIds(o,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(o.node,t)}),r.data.removes.forEach(o=>{this.replaceIds(o,t,["parentId","id"])}),r.data.attributes.forEach(o=>{this.replaceIds(o,t,["id"])}),r.data.texts.forEach(o=>{this.replaceIds(o,t,["id"])}),r;case v.Drag:case v.TouchMove:case v.MouseMove:return r.data.positions.forEach(o=>{this.replaceIds(o,t,["id"])}),r;case v.ViewportResize:return!1;case v.MediaInteraction:case v.MouseInteraction:case v.Scroll:case v.CanvasMutation:case v.Input:return this.replaceIds(r.data,t,["id"]),r;case v.StyleSheetRule:case v.StyleDeclaration:return this.replaceIds(r.data,t,["id"]),this.replaceStyleIds(r.data,t,["styleId"]),r;case v.Font:return r;case v.Selection:return r.data.ranges.forEach(o=>{this.replaceIds(o,t,["start","end"])}),r;case v.AdoptedStyleSheet:return this.replaceIds(r.data,t,["id"]),this.replaceStyleIds(r.data,t,["styleIds"]),(n=r.data.styles)===null||n===void 0||n.forEach(o=>{this.replaceStyleIds(o,t,["styleId"])}),r}}}replace(t,r,n,o){for(const i of o)!Array.isArray(r[i])&&typeof r[i]!="number"||(Array.isArray(r[i])?r[i]=t.getIds(n,r[i]):r[i]=t.getId(n,r[i]));return r}replaceIds(t,r,n){return this.replace(this.crossOriginIframeMirror,t,r,n)}replaceStyleIds(t,r,n){return this.replace(this.crossOriginIframeStyleMirror,t,r,n)}replaceIdOnNode(t,r){this.replaceIds(t,r,["id"]),"childNodes"in t&&t.childNodes.forEach(n=>{this.replaceIdOnNode(n,r)})}}class zr{constructor(t){this.shadowDoms=new WeakSet,this.restorePatches=[],this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror;const r=this;this.restorePatches.push(Ce(Element.prototype,"attachShadow",function(n){return function(o){const i=n.call(this,o);return this.shadowRoot&&r.addShadowRoot(this.shadowRoot,this.ownerDocument),i}}))}addShadowRoot(t,r){Me(t)&&(this.shadowDoms.has(t)||(this.shadowDoms.add(t),Gt(Object.assign(Object.assign({},this.bypassOptions),{doc:r,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),t),Vt(Object.assign(Object.assign({},this.bypassOptions),{scrollCb:this.scrollCb,doc:t,mirror:this.mirror})),setTimeout(()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(t.host)),Yt({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t)},0)))}observeAttachShadow(t){if(t.contentWindow){const r=this;this.restorePatches.push(Ce(t.contentWindow.HTMLElement.prototype,"attachShadow",function(n){return function(o){const i=n.call(this,o);return this.shadowRoot&&r.addShadowRoot(this.shadowRoot,t.contentDocument),i}}))}}reset(){this.restorePatches.forEach(t=>t()),this.shadowDoms=new WeakSet}}/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */function Hr(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function Jr(e,t,r,n){function o(i){return i instanceof r?i:new r(function(a){a(i)})}return new(r||(r=Promise))(function(i,a){function s(d){try{c(n.next(d))}catch(u){a(u)}}function l(d){try{c(n.throw(d))}catch(u){a(u)}}function c(d){d.done?i(d.value):o(d.value).then(s,l)}c((n=n.apply(e,[])).next())})}for(var Se="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qr=typeof Uint8Array>"u"?[]:new Uint8Array(256),Ue=0;Ue<Se.length;Ue++)Qr[Se.charCodeAt(Ue)]=Ue;var Xr=function(e){var t=new Uint8Array(e),r,n=t.length,o="";for(r=0;r<n;r+=3)o+=Se[t[r]>>2],o+=Se[(t[r]&3)<<4|t[r+1]>>4],o+=Se[(t[r+1]&15)<<2|t[r+2]>>6],o+=Se[t[r+2]&63];return n%3===2?o=o.substring(0,o.length-1)+"=":n%3===1&&(o=o.substring(0,o.length-2)+"=="),o};const zt=new Map;function qr(e,t){let r=zt.get(e);return r||(r=new Map,zt.set(e,r)),r.has(t)||r.set(t,[]),r.get(t)}const Ht=(e,t,r)=>{if(!e||!(Qt(e,t)||typeof e=="object"))return;const n=e.constructor.name,o=qr(r,n);let i=o.indexOf(e);return i===-1&&(i=o.length,o.push(e)),i};function Ge(e,t,r){if(e instanceof Array)return e.map(n=>Ge(n,t,r));if(e===null)return e;if(e instanceof Float32Array||e instanceof Float64Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Int8Array||e instanceof Uint8ClampedArray)return{rr_type:e.constructor.name,args:[Object.values(e)]};if(e instanceof ArrayBuffer){const n=e.constructor.name,o=Xr(e);return{rr_type:n,base64:o}}else{if(e instanceof DataView)return{rr_type:e.constructor.name,args:[Ge(e.buffer,t,r),e.byteOffset,e.byteLength]};if(e instanceof HTMLImageElement){const n=e.constructor.name,{src:o}=e;return{rr_type:n,src:o}}else if(e instanceof HTMLCanvasElement){const n="HTMLImageElement",o=e.toDataURL();return{rr_type:n,src:o}}else{if(e instanceof ImageData)return{rr_type:e.constructor.name,args:[Ge(e.data,t,r),e.width,e.height]};if(Qt(e,t)||typeof e=="object"){const n=e.constructor.name,o=Ht(e,t,r);return{rr_type:n,index:o}}}}return e}const Jt=(e,t,r)=>[...e].map(n=>Ge(n,t,r)),Qt=(e,t)=>!!["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter(o=>typeof t[o]=="function").find(o=>e instanceof t[o]);function jr(e,t,r,n){const o=[],i=Object.getOwnPropertyNames(t.CanvasRenderingContext2D.prototype);for(const a of i)try{if(typeof t.CanvasRenderingContext2D.prototype[a]!="function")continue;const s=Ce(t.CanvasRenderingContext2D.prototype,a,function(l){return function(...c){return _(this.canvas,r,n,!0)||setTimeout(()=>{const d=Jt([...c],t,this);e(this.canvas,{type:ye["2D"],property:a,args:d})},0),l.apply(this,c)}});o.push(s)}catch{const l=We(t.CanvasRenderingContext2D.prototype,a,{set(c){e(this.canvas,{type:ye["2D"],property:a,args:[c],setter:!0})}});o.push(l)}return()=>{o.forEach(a=>a())}}function Xt(e,t,r){const n=[];try{const o=Ce(e.HTMLCanvasElement.prototype,"getContext",function(i){return function(a,...s){return _(this,t,r,!0)||"__context"in this||(this.__context=a),i.apply(this,[a,...s])}});n.push(o)}catch{console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{n.forEach(o=>o())}}function qt(e,t,r,n,o,i,a){const s=[],l=Object.getOwnPropertyNames(e);for(const c of l)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(c))try{if(typeof e[c]!="function")continue;const d=Ce(e,c,function(u){return function(...g){const f=u.apply(this,g);if(Ht(f,a,this),!_(this.canvas,n,o,!0)){const h=Jt([...g],a,this),I={type:t,property:c,args:h};r(this.canvas,I)}return f}});s.push(d)}catch{const u=We(e,c,{set(g){r(this.canvas,{type:t,property:c,args:[g],setter:!0})}});s.push(u)}return s}function $r(e,t,r,n,o){const i=[];return i.push(...qt(t.WebGLRenderingContext.prototype,ye.WebGL,e,r,n,o,t)),typeof t.WebGL2RenderingContext<"u"&&i.push(...qt(t.WebGL2RenderingContext.prototype,ye.WebGL2,e,r,n,o,t)),()=>{i.forEach(a=>a())}}var jt=null;try{var en=typeof module<"u"&&typeof module.require=="function"&&module.require("worker_threads")||typeof __non_webpack_require__=="function"&&__non_webpack_require__("worker_threads")||typeof require=="function"&&require("worker_threads");jt=en.Worker}catch{}function tn(e,t){return Buffer.from(e,"base64").toString("utf8")}function rn(e,t,r){var n=tn(e),o=n.indexOf(`
`,10)+1,i=n.substring(o)+"";return function(s){return new jt(i,Object.assign({},s,{eval:!0}))}}function nn(e,t){var r=atob(e);return r}function on(e,t,r){var n=nn(e),o=n.indexOf(`
`,10)+1,i=n.substring(o)+"",a=new Blob([i],{type:"application/javascript"});return URL.createObjectURL(a)}function sn(e,t,r){var n;return function(i){return n=n||on(e),new Worker(n,i)}}var an=Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]";function ln(){return an}function cn(e,t,r){return ln()?rn(e):sn(e)}var dn=cn("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");class un{constructor(t){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(l,c)=>{(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId||!this.rafStamps.invokeId)&&(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(l)||this.pendingCanvasMutations.set(l,[]),this.pendingCanvasMutations.get(l).push(c)};const{sampling:r="all",win:n,blockClass:o,blockSelector:i,recordCanvas:a,dataURLOptions:s}=t;this.mutationCb=t.mutationCb,this.mirror=t.mirror,a&&r==="all"&&this.initCanvasMutationObserver(n,o,i),a&&typeof r=="number"&&this.initCanvasFPSObserver(r,n,o,i,{dataURLOptions:s})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(t,r,n,o,i){const a=Xt(r,n,o),s=new Map,l=new dn;l.onmessage=h=>{const{id:I}=h.data;if(s.set(I,!1),!("base64"in h.data))return;const{base64:S,type:y,width:p,height:m}=h.data;this.mutationCb({id:I,type:ye["2D"],commands:[{property:"clearRect",args:[0,0,p,m]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:S}],type:y}]},0,0]}]})};const c=1e3/t;let d=0,u;const g=()=>{const h=[];return r.document.querySelectorAll("canvas").forEach(I=>{_(I,n,o,!0)||h.push(I)}),h},f=h=>{if(d&&h-d<c){u=requestAnimationFrame(f);return}d=h,g().forEach(I=>Jr(this,void 0,void 0,function*(){var S;const y=this.mirror.getId(I);if(s.get(y))return;if(s.set(y,!0),["webgl","webgl2"].includes(I.__context)){const m=I.getContext(I.__context);((S=m==null?void 0:m.getContextAttributes())===null||S===void 0?void 0:S.preserveDrawingBuffer)===!1&&(m==null||m.clear(m.COLOR_BUFFER_BIT))}const p=yield createImageBitmap(I);l.postMessage({id:y,bitmap:p,width:I.width,height:I.height,dataURLOptions:i.dataURLOptions},[p])})),u=requestAnimationFrame(f)};u=requestAnimationFrame(f),this.resetObservers=()=>{a(),cancelAnimationFrame(u)}}initCanvasMutationObserver(t,r,n){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();const o=Xt(t,r,n),i=jr(this.processMutation.bind(this),t,r,n),a=$r(this.processMutation.bind(this),t,r,n,this.mirror);this.resetObservers=()=>{o(),i(),a()}}startPendingCanvasMutationFlusher(){requestAnimationFrame(()=>this.flushPendingCanvasMutations())}startRAFTimestamping(){const t=r=>{this.rafStamps.latestId=r,requestAnimationFrame(t)};requestAnimationFrame(t)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach((t,r)=>{const n=this.mirror.getId(r);this.flushPendingCanvasMutationFor(r,n)}),requestAnimationFrame(()=>this.flushPendingCanvasMutations())}flushPendingCanvasMutationFor(t,r){if(this.frozen||this.locked)return;const n=this.pendingCanvasMutations.get(t);if(!n||r===-1)return;const o=n.map(a=>Hr(a,["type"])),{type:i}=n[0];this.mutationCb({id:r,type:i,commands:o}),this.pendingCanvasMutations.delete(t)}}class hn{constructor(t){this.trackedLinkElements=new WeakSet,this.styleMirror=new Rr,this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,r){"_cssText"in r.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:r.id,attributes:r.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,r){if(t.length===0)return;const n={id:r,styleIds:[]},o=[];for(const i of t){let a;if(this.styleMirror.has(i))a=this.styleMirror.getId(i);else{a=this.styleMirror.add(i);const s=Array.from(i.rules||CSSRule);o.push({styleId:a,rules:s.map((l,c)=>({rule:pt(l),index:c}))})}n.styleIds.push(a)}o.length>0&&(n.styles=o),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}function O(e){return Object.assign(Object.assign({},e),{timestamp:Date.now()})}let N,Ve,tt,Ze=!1;const j=ir();function Re(e={}){const{emit:t,checkoutEveryNms:r,checkoutEveryNth:n,blockClass:o="rr-block",blockSelector:i=null,ignoreClass:a="rr-ignore",maskTextClass:s="rr-mask",maskTextSelector:l=null,inlineStylesheet:c=!0,maskAllInputs:d,maskInputOptions:u,slimDOMOptions:g,maskInputFn:f,maskTextFn:h,hooks:I,packFn:S,sampling:y={},dataURLOptions:p={},mousemoveWait:m,recordCanvas:w=!1,recordCrossOriginIframes:W=!1,userTriggeredOnInput:x=!1,collectFonts:F=!1,inlineImages:T=!1,plugins:Y,keepIframeSrcFn:P=()=>!1,ignoreCSSAttributes:Q=new Set([])}=e,X=W?window.parent===window:!0;let q=!1;if(!X)try{window.parent.document,q=!1}catch{q=!0}if(X&&!t)throw new Error("emit function is required");m!==void 0&&y.mousemove===void 0&&(y.mousemove=m),j.reset();const L=d===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:u!==void 0?u:{password:!0},z=g===!0||g==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:g==="all",headMetaDescKeywords:g==="all"}:g||{};Nr();let $,te=0;const ne=C=>{for(const B of Y||[])B.eventProcessor&&(C=B.eventProcessor(C));return S&&(C=S(C)),C};N=(C,B)=>{var Z;if(!((Z=se[0])===null||Z===void 0)&&Z.isFrozen()&&C.type!==b.FullSnapshot&&!(C.type===b.IncrementalSnapshot&&C.data.source===v.Mutation)&&se.forEach(E=>E.unfreeze()),X)t==null||t(ne(C),B);else if(q){const E={type:"rrweb",event:ne(C),isCheckout:B};window.parent.postMessage(E,"*")}if(C.type===b.FullSnapshot)$=C,te=0;else if(C.type===b.IncrementalSnapshot){if(C.data.source===v.Mutation&&C.data.isAttachIframe)return;te++;const E=n&&te>=n,J=r&&C.timestamp-$.timestamp>r;(E||J)&&Ve(!0)}};const V=C=>{N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.Mutation},C)}))},H=C=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.Scroll},C)})),ie=C=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.CanvasMutation},C)})),R=C=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.AdoptedStyleSheet},C)})),ee=new hn({mutationCb:V,adoptedStyleSheetCb:R}),re=new Pr({mirror:j,mutationCb:V,stylesheetManager:ee,recordCrossOriginIframes:W,wrappedEmit:N});for(const C of Y||[])C.getMirror&&C.getMirror({nodeMirror:j,crossOriginIframeMirror:re.crossOriginIframeMirror,crossOriginIframeStyleMirror:re.crossOriginIframeStyleMirror});tt=new un({recordCanvas:w,mutationCb:ie,win:window,blockClass:o,blockSelector:i,mirror:j,sampling:y.canvas,dataURLOptions:p});const we=new zr({mutationCb:V,scrollCb:H,bypassOptions:{blockClass:o,blockSelector:i,maskTextClass:s,maskTextSelector:l,inlineStylesheet:c,maskInputOptions:L,dataURLOptions:p,maskTextFn:h,maskInputFn:f,recordCanvas:w,inlineImages:T,sampling:y,slimDOMOptions:z,iframeManager:re,stylesheetManager:ee,canvasManager:tt,keepIframeSrcFn:P},mirror:j});Ve=(C=!1)=>{var B,Z,E,J,A,K;N(O({type:b.Meta,data:{href:window.location.href,width:wt(),height:At()}}),C),ee.reset(),se.forEach(U=>U.lock());const Fe=Tr(document,{mirror:j,blockClass:o,blockSelector:i,maskTextClass:s,maskTextSelector:l,inlineStylesheet:c,maskAllInputs:L,maskTextFn:h,slimDOM:z,dataURLOptions:p,recordCanvas:w,inlineImages:T,onSerialize:U=>{Tt(U,j)&&re.addIframe(U),Et(U,j)&&ee.trackLinkElement(U),Nt(U)&&we.addShadowRoot(U.shadowRoot,document)},onIframeLoad:(U,ft)=>{re.attachIframe(U,ft),we.observeAttachShadow(U)},onStylesheetLoad:(U,ft)=>{ee.attachLinkElement(U,ft)},keepIframeSrcFn:P});if(!Fe)return console.warn("Failed to snapshot the document");N(O({type:b.FullSnapshot,data:{node:Fe,initialOffset:{left:window.pageXOffset!==void 0?window.pageXOffset:(document==null?void 0:document.documentElement.scrollLeft)||((Z=(B=document==null?void 0:document.body)===null||B===void 0?void 0:B.parentElement)===null||Z===void 0?void 0:Z.scrollLeft)||((E=document==null?void 0:document.body)===null||E===void 0?void 0:E.scrollLeft)||0,top:window.pageYOffset!==void 0?window.pageYOffset:(document==null?void 0:document.documentElement.scrollTop)||((A=(J=document==null?void 0:document.body)===null||J===void 0?void 0:J.parentElement)===null||A===void 0?void 0:A.scrollTop)||((K=document==null?void 0:document.body)===null||K===void 0?void 0:K.scrollTop)||0}}})),se.forEach(U=>U.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&ee.adoptStyleSheets(document.adoptedStyleSheets,j.getId(document))};try{const C=[];C.push(D("DOMContentLoaded",()=>{N(O({type:b.DomContentLoaded,data:{}}))}));const B=E=>{var J;return Yr({mutationCb:V,mousemoveCb:(A,K)=>N(O({type:b.IncrementalSnapshot,data:{source:K,positions:A}})),mouseInteractionCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.MouseInteraction},A)})),scrollCb:H,viewportResizeCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.ViewportResize},A)})),inputCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.Input},A)})),mediaInteractionCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.MediaInteraction},A)})),styleSheetRuleCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.StyleSheetRule},A)})),styleDeclarationCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.StyleDeclaration},A)})),canvasMutationCb:ie,fontCb:A=>N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.Font},A)})),selectionCb:A=>{N(O({type:b.IncrementalSnapshot,data:Object.assign({source:v.Selection},A)}))},blockClass:o,ignoreClass:a,maskTextClass:s,maskTextSelector:l,maskInputOptions:L,inlineStylesheet:c,sampling:y,recordCanvas:w,inlineImages:T,userTriggeredOnInput:x,collectFonts:F,doc:E,maskInputFn:f,maskTextFn:h,keepIframeSrcFn:P,blockSelector:i,slimDOMOptions:z,dataURLOptions:p,mirror:j,iframeManager:re,stylesheetManager:ee,shadowDomManager:we,canvasManager:tt,ignoreCSSAttributes:Q,plugins:((J=Y==null?void 0:Y.filter(A=>A.observer))===null||J===void 0?void 0:J.map(A=>({observer:A.observer,options:A.options,callback:K=>N(O({type:b.Plugin,data:{plugin:A.name,payload:K}}))})))||[]},I)};re.addLoadListener(E=>{C.push(B(E.contentDocument))});const Z=()=>{Ve(),C.push(B(document)),Ze=!0};return document.readyState==="interactive"||document.readyState==="complete"?Z():C.push(D("load",()=>{N(O({type:b.Load,data:{}})),Z()},window)),()=>{C.forEach(E=>E()),Ze=!1}}catch(C){console.warn(C)}}Re.addCustomEvent=(e,t)=>{if(!Ze)throw new Error("please add custom event after start recording");N(O({type:b.Custom,data:{tag:e,payload:t}}))},Re.freezePage=()=>{se.forEach(e=>e.freeze())},Re.takeFullSnapshot=e=>{if(!Ze)throw new Error("please take full snapshot after start recording");Ve(e)},Re.mirror=j;var $t=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))($t||{}),er=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(er||{});let ve,G=!0,ae=null,Ke=null,Oe=null;const gn=500;let Ye=!0;function le(e){var n;if(e.id!=="")return`id("${e.id}")`;if(e===document.body)return e.tagName.toLowerCase();let t=0;const r=(n=e.parentNode)==null?void 0:n.children;if(r)for(let o=0;o<r.length;o++){const i=r[o];if(i===e)return`${le(e.parentElement)}/${e.tagName.toLowerCase()}[${t+1}]`;i.nodeType===1&&i.tagName===e.tagName&&t++}return e.tagName.toLowerCase()}const fn=new Set(["id","name","type","placeholder","aria-label","aria-labelledby","aria-describedby","role","for","autocomplete","required","readonly","alt","title","src","href","target","data-id","data-qa","data-cy","data-testid"]);function be(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e==="x"?t:t&3|8).toString(16)})}function Ae(e,t){try{let r=e.tagName.toLowerCase();if(e.classList&&e.classList.length>0){const n=/^[a-zA-Z_][a-zA-Z0-9_-]*$/;e.classList.forEach(o=>{o&&n.test(o)&&(r+=`.${CSS.escape(o)}`)})}for(const n of e.attributes){const o=n.name,i=n.value;if(o==="class"||!o.trim()||!fn.has(o))continue;const a=CSS.escape(o);if(i==="")r+=`[${a}]`;else{const s=i.replace(/"/g,'"');/["'<>`\s]/.test(i)?r+=`[${a}*="${s}"]`:r+=`[${a}="${s}"]`}}return r}catch(r){return console.error("Error generating enhanced CSS selector:",r),`${e.tagName.toLowerCase()}[xpath="${t.replace(/"/g,'"')}"]`}}function rt(){if(ve){console.log("Recorder already running.");return}console.log("Starting rrweb recorder for:",window.location.href),G=!0,ve=Re({emit(e){if(G)if(e.type===$t.IncrementalSnapshot&&e.data.source===er.Scroll){const t=e.data,r=t.y,n={...t,x:Math.round(t.x),y:Math.round(t.y),uuid:be()};let o=null;if(Ke!==null&&(o=r>Ke?"down":"up"),Oe!==null&&o!==null&&o!==Oe){ae&&(clearTimeout(ae),ae=null),chrome.runtime.sendMessage({type:"RRWEB_EVENT",payload:{...e,data:n}}),Oe=o,Ke=r;return}Oe=o,Ke=r,ae&&clearTimeout(ae),ae=setTimeout(()=>{chrome.runtime.sendMessage({type:"RRWEB_EVENT",payload:{...e,data:n}}),ae=null,Oe=null},gn)}else chrome.runtime.sendMessage({type:"RRWEB_EVENT",payload:{...e,uuid:be()}})},maskInputOptions:{password:!0},checkoutEveryNms:1e4,checkoutEveryNth:200}),window.rrwebStop=Le,document.addEventListener("click",nt,!0),document.addEventListener("input",ot,!0),document.addEventListener("change",it,!0),document.addEventListener("keydown",st,!0),document.addEventListener("mouseover",at,!0),document.addEventListener("mouseout",lt,!0),document.addEventListener("focus",ct,!0),document.addEventListener("blur",dt,!0),console.log("Permanently attached custom event listeners.")}function Le(){ve?(console.log("Stopping rrweb recorder for:",window.location.href),ve(),ve=void 0,G=!1,window.rrwebStop=void 0,document.removeEventListener("click",nt,!0),document.removeEventListener("input",ot,!0),document.removeEventListener("change",it,!0),document.removeEventListener("keydown",st,!0),document.removeEventListener("mouseover",at,!0),document.removeEventListener("mouseout",lt,!0),document.removeEventListener("focus",ct,!0),document.removeEventListener("blur",dt,!0)):console.log("Recorder not running, cannot stop.")}function nt(e){var r;if(!G)return;const t=e.target;if(t){if(t.closest("[data-workflow-use-ui]")){console.log("Ignoring click on workflow-use UI element");return}try{const n=le(t),o={timestamp:Date.now(),url:document.location.href,frameUrl:window.location.href,xpath:n,uuid:be(),cssSelector:Ae(t,n),elementTag:t.tagName,elementText:((r=t.textContent)==null?void 0:r.trim().slice(0,200))||""};console.log("Sending CUSTOM_CLICK_EVENT:",o),chrome.runtime.sendMessage({type:"CUSTOM_CLICK_EVENT",payload:o})}catch(n){console.error("Error capturing click data:",n)}}}function ot(e){if(!G)return;const t=e.target;if(!t||!("value"in t))return;if(t.closest("[data-workflow-use-ui]")){console.log("Ignoring input on workflow-use UI element");return}const r=t.type==="password";try{const n=le(t),o={timestamp:Date.now(),url:document.location.href,frameUrl:window.location.href,xpath:n,uuid:be(),cssSelector:Ae(t,n),elementTag:t.tagName,value:t.value,isPassword:r};console.log("Sending CUSTOM_INPUT_EVENT:",o),chrome.runtime.sendMessage({type:"CUSTOM_INPUT_EVENT",payload:o})}catch(n){console.error("Error capturing input data:",n)}}function it(e){if(!G)return;const t=e.target;if(!(!t||t.tagName!=="SELECT"))try{const r=le(t),n=t.options[t.selectedIndex],o={timestamp:Date.now(),url:document.location.href,frameUrl:window.location.href,xpath:r,uuid:be(),cssSelector:Ae(t,r),elementTag:t.tagName,selectedValue:t.value,selectedText:n?n.text:""};console.log("Sending CUSTOM_SELECT_EVENT:",o),chrome.runtime.sendMessage({type:"CUSTOM_SELECT_EVENT",payload:o})}catch(r){console.error("Error capturing select change data:",r)}}const pn=new Set(["Enter","Tab","Escape","ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Home","End","PageUp","PageDown","Backspace","Delete"]);function st(e){if(!G)return;const t=e.key;let r="";if(pn.has(t)?r=t:(e.ctrlKey||e.metaKey)&&t.length===1&&/[a-zA-Z0-9]/.test(t)&&(r=`CmdOrCtrl+${t.toUpperCase()}`),r){const n=e.target;let o="",i="",a="document";if(n&&typeof n.tagName=="string")try{o=le(n),i=Ae(n,o),a=n.tagName}catch(s){console.error("Error getting selector for keydown target:",s)}try{const s={timestamp:Date.now(),uuid:be(),url:document.location.href,frameUrl:window.location.href,key:r,xpath:o,cssSelector:i,elementTag:a};console.log("Sending CUSTOM_KEY_EVENT:",s),chrome.runtime.sendMessage({type:"CUSTOM_KEY_EVENT",payload:s})}catch(s){console.error("Error capturing keydown data:",s)}}}let ce=null,de=null;function at(e){if(!G)return;const t=e.target;if(t){ce&&(ce.remove(),ce=null);try{const r=le(t);let n=document.evaluate(r,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(!n){const o=Ae(t,r);console.log("CSS Selector:",o);const i=document.querySelectorAll(o);for(const a of i){const s=a.getBoundingClientRect();if(e.clientX>=s.left&&e.clientX<=s.right&&e.clientY>=s.top&&e.clientY<=s.bottom){n=a;break}}}if(n){const o=n.getBoundingClientRect(),i=document.createElement("div");i.className="highlight-overlay",i.setAttribute("data-workflow-use-ui","highlight"),Object.assign(i.style,{position:"absolute",top:`${o.top+window.scrollY}px`,left:`${o.left+window.scrollX}px`,width:`${o.width}px`,height:`${o.height}px`,border:"2px solid lightgreen",backgroundColor:"rgba(144, 238, 144, 0.05)",pointerEvents:"none",zIndex:"2147483000"}),document.body.appendChild(i),ce=i}else console.warn("No element found to highlight for xpath:",r)}catch(r){console.error("Error creating highlight overlay:",r)}}}function lt(e){G&&ce&&(ce.remove(),ce=null)}function ct(e){if(!G)return;const t=e.target;if(!(!t||!["INPUT","TEXTAREA","SELECT"].includes(t.tagName))){de&&(de.remove(),de=null);try{const r=le(t);let n=document.evaluate(r,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(!n){const o=Ae(t,r);n=document.querySelector(o)}if(n){const o=n.getBoundingClientRect(),i=document.createElement("div");i.className="focus-overlay",i.setAttribute("data-workflow-use-ui","focus"),Object.assign(i.style,{position:"absolute",top:`${o.top+window.scrollY}px`,left:`${o.left+window.scrollX}px`,width:`${o.width}px`,height:`${o.height}px`,border:"2px solid red",backgroundColor:"rgba(255, 0, 0, 0.05)",pointerEvents:"none",zIndex:"2147483100"}),document.body.appendChild(i),de=i}else console.warn("No element found to highlight for focus, xpath:",r)}catch(r){console.error("Error creating focus overlay:",r)}}}function dt(e){G&&de&&(de.remove(),de=null)}const mn={matches:["<all_urls>"],main(e){chrome.runtime.onMessage.addListener((r,n,o)=>{if(r.type==="SET_RECORDING_STATUS"){const i=r.payload;console.log(`Received recording status update: ${i}`),i&&!G?rt():!i&&G&&Le()}});const t=()=>{console.log("Content script loaded, requesting initial recording status..."),chrome.runtime.sendMessage({type:"REQUEST_RECORDING_STATUS"},r=>{if(chrome.runtime.lastError){console.error("Error requesting initial status:",chrome.runtime.lastError.message),setTimeout(t,1e3);return}r&&r.isRecordingEnabled?(console.log("Initial status: Recording enabled."),rt()):(console.log("Initial status: Recording disabled."),Le())})};document.readyState==="loading"?document.addEventListener("DOMContentLoaded",t):t(),window.addEventListener("beforeunload",()=>{document.removeEventListener("click",nt,!0),document.removeEventListener("input",ot,!0),document.removeEventListener("change",it,!0),document.removeEventListener("keydown",st,!0),document.removeEventListener("mouseover",at,!0),document.removeEventListener("mouseout",lt,!0),document.removeEventListener("focus",ct,!0),document.removeEventListener("blur",dt,!0),Le()}),chrome.runtime.onMessage.addListener((r,n,o)=>(console.log("Content script received message:",r),r.type==="START_RECORDING"?(console.log("Starting recording from background message"),rt(),o({success:!0})):r.type==="STOP_RECORDING"?(console.log("Stopping recording from background message"),Le(),o({success:!0})):r.type==="GET_RECORDING_STATUS"?o({isRecording:G&&!!ve,url:window.location.href,passwordSettings:{promptShown:passwordPromptShown,recordReal:Ye}}):r.type==="SET_PASSWORD_RECORDING"&&(Ye=r.recordReal||!1,passwordPromptShown=!0,console.log(`Password recording setting updated: ${Ye}`),o({success:!0,recordReal:Ye})),!0))}},Pe=(rr=(tr=globalThis.browser)==null?void 0:tr.runtime)!=null&&rr.id?globalThis.browser:globalThis.chrome;function ze(e,...t){}const In={debug:(...e)=>ze(console.debug,...e),log:(...e)=>ze(console.log,...e),warn:(...e)=>ze(console.warn,...e),error:(...e)=>ze(console.error,...e)},Je=class Je extends Event{constructor(t,r){super(Je.EVENT_NAME,{}),this.newUrl=t,this.oldUrl=r}};ue(Je,"EVENT_NAME",ht("wxt:locationchange"));let ut=Je;function ht(e){var t;return`${(t=Pe==null?void 0:Pe.runtime)==null?void 0:t.id}:content:${e}`}function Cn(e){let t,r;return{run(){t==null&&(r=new URL(location.href),t=e.setInterval(()=>{let n=new URL(location.href);n.href!==r.href&&(window.dispatchEvent(new ut(n,r)),r=n)},1e3))}}}const xe=class xe{constructor(t,r){ue(this,"isTopFrame",window.self===window.top);ue(this,"abortController");ue(this,"locationWatcher",Cn(this));ue(this,"receivedMessageIds",new Set);this.contentScriptName=t,this.options=r,this.abortController=new AbortController,this.isTopFrame?(this.listenForNewerScripts({ignoreFirstEvent:!0}),this.stopOldScripts()):this.listenForNewerScripts()}get signal(){return this.abortController.signal}abort(t){return this.abortController.abort(t)}get isInvalid(){return Pe.runtime.id==null&&this.notifyInvalidated(),this.signal.aborted}get isValid(){return!this.isInvalid}onInvalidated(t){return this.signal.addEventListener("abort",t),()=>this.signal.removeEventListener("abort",t)}block(){return new Promise(()=>{})}setInterval(t,r){const n=setInterval(()=>{this.isValid&&t()},r);return this.onInvalidated(()=>clearInterval(n)),n}setTimeout(t,r){const n=setTimeout(()=>{this.isValid&&t()},r);return this.onInvalidated(()=>clearTimeout(n)),n}requestAnimationFrame(t){const r=requestAnimationFrame((...n)=>{this.isValid&&t(...n)});return this.onInvalidated(()=>cancelAnimationFrame(r)),r}requestIdleCallback(t,r){const n=requestIdleCallback((...o)=>{this.signal.aborted||t(...o)},r);return this.onInvalidated(()=>cancelIdleCallback(n)),n}addEventListener(t,r,n,o){var i;r==="wxt:locationchange"&&this.isValid&&this.locationWatcher.run(),(i=t.addEventListener)==null||i.call(t,r.startsWith("wxt:")?ht(r):r,n,{...o,signal:this.signal})}notifyInvalidated(){this.abort("Content script context invalidated"),In.debug(`Content script "${this.contentScriptName}" context invalidated`)}stopOldScripts(){window.postMessage({type:xe.SCRIPT_STARTED_MESSAGE_TYPE,contentScriptName:this.contentScriptName,messageId:Math.random().toString(36).slice(2)},"*")}verifyScriptStartedEvent(t){var i,a,s;const r=((i=t.data)==null?void 0:i.type)===xe.SCRIPT_STARTED_MESSAGE_TYPE,n=((a=t.data)==null?void 0:a.contentScriptName)===this.contentScriptName,o=!this.receivedMessageIds.has((s=t.data)==null?void 0:s.messageId);return r&&n&&o}listenForNewerScripts(t){let r=!0;const n=o=>{if(this.verifyScriptStartedEvent(o)){this.receivedMessageIds.add(o.data.messageId);const i=r;if(r=!1,i&&(t!=null&&t.ignoreFirstEvent))return;this.notifyInvalidated()}};addEventListener("message",n),this.onInvalidated(()=>removeEventListener("message",n))}};ue(xe,"SCRIPT_STARTED_MESSAGE_TYPE",ht("wxt:content-script-started"));let gt=xe;function An(){}function He(e,...t){}const yn={debug:(...e)=>He(console.debug,...e),log:(...e)=>He(console.log,...e),warn:(...e)=>He(console.warn,...e),error:(...e)=>He(console.error,...e)};return(async()=>{try{const{main:e,...t}=mn,r=new gt("content",t);return await e(r)}catch(e){throw yn.error('The content script "content" crashed on startup!',e),e}})()}();
content;
