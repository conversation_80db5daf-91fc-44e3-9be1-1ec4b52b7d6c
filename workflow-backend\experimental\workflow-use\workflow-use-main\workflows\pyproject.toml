[project]
name = "workflow-use"
version = "0.0.2"
authors = [{ name = "<PERSON>" }]
description = "Create, edit, run deterministic workflows"
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved",
    "Operating System :: OS Independent",
]

dependencies = [
    "aiofiles>=24.1.0",
    "browser-use>=0.2.4",
    "fastapi>=0.115.12",
    "fastmcp>=2.3.4",
    "typer>=0.15.3",
    "uvicorn>=0.34.2",
]


[tool.uv]
dev-dependencies = [
    "build>=1.2.2.post1",
    "ruff>=0.11.8",
]

[tool.codespell]
ignore-words-list = "bu"
skip = "*.json"

[tool.ruff]
line-length = 130
fix = true

[tool.ruff.lint]
select = ["ASYNC", "E", "F", "FAST", "I", "PLE"]
ignore = ["ASYNC109", "E101", "E402", "E501", "F841", "E731"]  # TODO: determine if adding timeouts to all the unbounded async functions is needed / worth-it so we can un-ignore ASYNC109
unfixable = ["E101", "E402", "E501", "F841", "E731"]

[tool.ruff.format]
quote-style = "single"
indent-style = "tab"
docstring-code-format = true

[tool.pyright]
typeCheckingMode = "basic"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
include = [
    "workflow_use/**/*.py"
]

[tool.hatch.metadata]
allow-direct-references = true
