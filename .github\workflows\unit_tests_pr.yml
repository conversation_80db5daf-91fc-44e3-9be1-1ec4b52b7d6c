name: Unit tests

on:
  pull_request:
    paths:
      - '.github/workflows/**'
      - 'src/**'
      - 'utest/**'
      - '!**/*.rst'

jobs:
  test_using_builtin_python:

    strategy:
      fail-fast: true
      matrix:
        os: [ 'ubuntu-latest', 'windows-latest' ]
        python-version: [ '3.8', '3.12' ]

    runs-on: ${{ matrix.os }}

    name: Python ${{ matrix.python-version }} on ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.6.0
        with:
          python-version: ${{ matrix.python-version }}
          architecture: 'x64'

      - name: Run unit tests
        run: |
          python -m pip install -r utest/requirements.txt
          python utest/run.py -v
