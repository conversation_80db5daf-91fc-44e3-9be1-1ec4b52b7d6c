<!--  -->
<template>
  <div class="homepage">
    <!-- 导航栏 -->
    <!-- <header>
        <nav class="container">
            <div class="logo">WimTask</div>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#solutions">行业解决方案</a></li>
                <li><a href="#market">服务市场</a></li>
                <li><a href="#pricing">定价方案</a></li>
                <li><a href="#enterprise">企业服务</a></li>
                <li><a href="#resources">资源中心</a></li>
                <li><a href="#login">登录</a></li>
            </ul>
        </nav>
    </header> -->

    <!-- 1. WimTask主页 - 产品横幅 -->
    <section id="home" class="hero">
        <div class="header">
            <div class="logo">
                <img
                    style="width: 100px;height: 20px;"
                    src="../../assets/images/admin/logo.png"
                    alt="WimTask logo"
                />
            </div>
            <ul class="nav-links">
                <li><a href="javascript:void(0)" @click="jumpToPage('manager')">工作台</a></li>
                <li><a href="javascript:void(0)">文档</a></li>
            </ul>
        </div>
        <div class="container">
            <p>让重复工作自动化 让复杂工作协同化 让执行工作智能化</p>
            <p>人脑和AI结合 释放团队创造力 改变工作方式 提升工作效率和质量</p>
            <!-- <a href="#download" class="btn">立即下载</a> -->
            <a href="javascipt:void(0)" class="btn btn-secondary">在线演示</a>
        </div>
    </section>

    <!-- 产品优势 -->
    <section class="section">
        <div class="container">
            <h2>产品优势</h2>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">💰</div>
                    <h3>降低运营成本</h3>
                    <p>自动化重复性工作，减少人工错误，平均降低60%的运营成本</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>提升工作效率</h3>
                    <p>24/7不间断执行，处理速度比人工快10倍，释放员工时间专注高价值工作</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">�</div>
                    <h3>零代码操作</h3>
                    <p>拖拽式可视化设计，业务人员无需编程即可创建自动化流程</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔧</div>
                    <h3>灵活扩展</h3>
                    <p>丰富的行业模板和工具库，快速适配不同业务场景</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 应用场景 -->
    <section class="section">
        <div class="container">
            <h2>应用场景</h2>
            <div class="grid">
                <div class="card">
                    <h3>💧 水务管理</h3>
                    <p>智能监控水质数据、设备状态，自动生成巡检报告，异常预警处理</p>
                    <ul>
                        <li>水质数据自动采集分析</li>
                        <li>设备运行状态监控</li>
                        <li>异常报警自动处理</li>
                        <li>巡检报告自动生成</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>📊 日常办公</h3>
                    <p>自动化处理重复性办公任务，提升工作效率，减少人工错误</p>
                    <ul>
                        <li>数据录入和处理</li>
                        <li>报表自动生成</li>
                        <li>邮件批量发送</li>
                        <li>文档格式转换</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>🏭 制造业</h3>
                    <p>生产流程自动化，质量检测，设备维护管理</p>
                    <ul>
                        <li>生产计划自动调度</li>
                        <li>质量数据分析</li>
                        <li>设备预测性维护</li>
                        <li>库存管理优化</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 行业解决方案 -->
    <section id="solutions" class="section">
        <div class="solution-hero">
            <div class="container">
                <h2>行业解决方案</h2>
                <p>针对不同行业特点，提供专业化的智能自动化解决方案</p>
            </div>
        </div>

        <div class="container">
            <div class="industry-grid">
                <!-- 第一行：水务行业 -->
                <div class="industry-row-1">
                    <div class="industry-card">
                    <div class="industry-header water">
                        <div class="industry-icon">💧</div>
                        <h3>水务管理解决方案</h3>
                        <p>智能化水务运营管理平台</p>
                    </div>
                    <div class="industry-content">
                        <h4>核心功能</h4>
                        <ul class="solution-features">
                            <li>水质数据自动采集与分析</li>
                            <li>设备运行状态实时监控</li>
                            <li>异常报警自动处理</li>
                            <li>巡检报告智能生成</li>
                            <li>维护计划自动调度</li>
                            <li>合规报告一键生成</li>
                        </ul>

                        <div class="case-study">
                            <h5>客户案例：某市自来水公司</h5>
                            <p>通过WimTask平台，实现了全市200个监测点的自动化管理，大幅提升了运营效率。</p>
                        </div>

                        <div class="roi-stats">
                            <div class="roi-stat">
                                <div class="number">85%</div>
                                <div class="label">效率提升</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">60%</div>
                                <div class="label">成本降低</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">99.9%</div>
                                <div class="label">系统可用性</div>
                            </div>
                        </div>

                        <button class="btn" style="width: 100%;">了解详情</button>
                    </div>
                </div>
                </div>

                <!-- 第二行：办公自动化 + 制造业 -->
                <div class="industry-row-2">
                    <!-- 办公自动化 -->
                <div class="industry-card">
                    <div class="industry-header office">
                        <div class="industry-icon">📊</div>
                        <h3>办公自动化解决方案</h3>
                        <p>提升办公效率的智能化平台</p>
                    </div>
                    <div class="industry-content">
                        <h4>核心功能</h4>
                        <ul class="solution-features">
                            <li>文档批量处理与转换</li>
                            <li>数据录入自动化</li>
                            <li>报表智能生成</li>
                            <li>邮件批量发送</li>
                            <li>审批流程自动化</li>
                            <li>会议安排智能调度</li>
                        </ul>

                        <div class="case-study">
                            <h5>客户案例：某大型企业集团</h5>
                            <p>部署WimTask后，月度报表生成时间从3天缩短至2小时，员工满意度显著提升。</p>
                        </div>

                        <div class="roi-stats">
                            <div class="roi-stat">
                                <div class="number">70%</div>
                                <div class="label">时间节省</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">95%</div>
                                <div class="label">准确率提升</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">50%</div>
                                <div class="label">人力成本降低</div>
                            </div>
                        </div>

                        <button class="btn" style="width: 100%;">了解详情</button>
                    </div>
                </div>

                <!-- 制造业 -->
                <div class="industry-card">
                    <div class="industry-header manufacturing">
                        <div class="industry-icon">🏭</div>
                        <h3>智能制造解决方案</h3>
                        <p>工业4.0智能制造管理平台</p>
                    </div>
                    <div class="industry-content">
                        <h4>核心功能</h4>
                        <ul class="solution-features">
                            <li>生产计划智能调度</li>
                            <li>质量数据实时分析</li>
                            <li>设备预测性维护</li>
                            <li>库存管理优化</li>
                            <li>供应链协同管理</li>
                            <li>能耗监控与优化</li>
                        </ul>

                        <div class="case-study">
                            <h5>客户案例：某汽车零部件制造商</h5>
                            <p>通过智能制造解决方案，实现了生产效率提升30%，产品质量稳定性大幅改善。</p>
                        </div>

                        <div class="roi-stats">
                            <div class="roi-stat">
                                <div class="number">30%</div>
                                <div class="label">生产效率提升</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">25%</div>
                                <div class="label">质量改善</div>
                            </div>
                            <div class="roi-stat">
                                <div class="number">40%</div>
                                <div class="label">维护成本降低</div>
                            </div>
                        </div>

                        <button class="btn" style="width: 100%;">了解详情</button>
                    </div>
                </div>
                </div>

                <!-- 第三行：更多行业 -->
                <div class="industry-row-3">
                    <div class="more-industries" @click="showMoreIndustries()">
                        <div class="more-industries-content">
                            <div class="icon">🏢</div>
                            <h3>更多行业解决方案</h3>
                            <p>金融服务 | 零售电商 | 医疗健康 | 教育培训 | 物流运输 | 政府机构</p>
                            <p style="margin-top: 1rem; font-size: 0.9rem;">点击了解更多行业的专业解决方案</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 对比分析 -->
    <section class="section">
        <div class="container">
            <h2>与传统RPA工具对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>传统RPA</th>
                        <th>WimTask</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>智能化程度</td>
                        <td>基于规则的自动化</td>
                        <td>AI驱动的智能决策</td>
                    </tr>
                    <tr>
                        <td>工具生态</td>
                        <td>封闭式工具库</td>
                        <td>开放式MCP生态</td>
                    </tr>
                    <tr>
                        <td>协作能力</td>
                        <td>单一机器人执行</td>
                        <td>多智能体协作</td>
                    </tr>
                    <tr>
                        <td>扩展性</td>
                        <td>定制开发成本高</td>
                        <td>标准协议易扩展</td>
                    </tr>
                    <tr>
                        <td>学习成本</td>
                        <td>需要专业培训</td>
                        <td>可视化零代码</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <!-- 3. 服务市场 -->
    <section id="market" class="section">
        <div class="container">
            <h2>服务市场</h2>

            <!-- 服务搜索 -->
            <div class="search-bar">
                <input type="text" placeholder="搜索服务包、模板、MCP服务...">
                <select>
                    <option>全部分类</option>
                    <option>水务管理</option>
                    <option>办公自动化</option>
                    <option>制造业</option>
                    <option>金融服务</option>
                </select>
                <button class="btn">搜索</button>
            </div>

            <!-- 服务分类浏览 -->
            <div class="tabs">
                <button v-for="tab in tabs" :key="tab.value" class="tab" :class="currentTab === tab.value ? 'active' : ''" @click="showTab(tab.value)">{{ tab.label }}</button>
            </div>

            <!-- 服务包 -->
            <div id="services" class="tab-content" :class="currentTab === 'services' ? 'active' : ''">
                <div class="service-grid">
                    <div class="service-card">
                        <h4>和达水务专业服务包</h4>
                        <p>专为水务行业设计的完整解决方案，包含水质监测、设备管理、报告生成等功能</p>
                        <div class="price">面议</div>
                        <div>
                            <span>⭐⭐⭐⭐⭐ 4.8分</span>
                            <span style="float: right;">已安装 1,234次</span>
                        </div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">查看详情</button>
                    </div>

                    <div class="service-card">
                        <h4>办公自动化服务包</h4>
                        <p>提升办公效率的自动化工具集，支持文档处理、数据分析、邮件管理等</p>
                        <div class="price">面议</div>
                        <div>
                            <span>⭐⭐⭐⭐⭐ 4.6分</span>
                            <span style="float: right;">已安装 2,156次</span>
                        </div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">查看详情</button>
                    </div>

                    <div class="service-card">
                        <h4>制造业智能服务包</h4>
                        <p>工业4.0智能制造解决方案，包含生产调度、质量控制、设备维护等</p>
                        <div class="price">面议</div>
                        <div>
                            <span>⭐⭐⭐⭐⭐ 4.9分</span>
                            <span style="float: right;">已安装 856次</span>
                        </div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 模板库 -->
            <div id="templates" class="tab-content" :class="currentTab === 'templates' ? 'active' : ''">
                <div class="service-grid">
                    <div class="service-card">
                        <h4>水质监测工作流模板</h4>
                        <p>自动采集水质数据，生成监测报告，异常情况自动报警</p>
                        <div class="price">免费</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">下载模板</button>
                    </div>

                    <div class="service-card">
                        <h4>财务报表自动化模板</h4>
                        <p>自动收集财务数据，生成标准化报表，支持多种格式导出</p>
                        <div class="price">面议</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">购买模板</button>
                    </div>

                    <div class="service-card">
                        <h4>客户服务自动化模板</h4>
                        <p>智能客服工作流，自动回复常见问题，工单自动分配</p>
                        <div class="price">面议</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">购买模板</button>
                    </div>
                </div>
            </div>

            <!-- MCP广场 -->
            <div id="mcp" class="tab-content" :class="currentTab === 'mcp' ? 'active' : ''">
                <div class="service-grid">
                    <div class="service-card">
                        <h4>Excel数据处理MCP</h4>
                        <p>强大的Excel文件处理工具，支持数据清洗、格式转换、图表生成</p>
                        <div class="price">面议</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">订阅服务</button>
                    </div>

                    <div class="service-card">
                        <h4>邮件发送MCP</h4>
                        <p>批量邮件发送工具，支持模板定制、发送统计、退信处理</p>
                        <div class="price">面议</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">订阅服务</button>
                    </div>

                    <div class="service-card">
                        <h4>数据库连接MCP</h4>
                        <p>支持多种数据库连接，提供数据查询、更新、备份等功能</p>
                        <div class="price">面议</div>
                        <button class="btn" style="width: 100%; margin-top: 1rem;">订阅服务</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 定价方案 -->
    <section id="pricing" class="section">
        <div class="container">
            <h2>定价方案</h2>
            <p style="text-align: center; margin-bottom: 3rem; color: #666;">选择适合您的方案，开启智能自动化之旅</p>

            <div class="pricing-grid">
                <!-- 免费版 -->
                <!-- <div class="pricing-card">
                    <h3>免费版</h3>
                    <div class="price">
                        ¥0
                        <span class="period">/月</span>
                    </div>
                    <div class="description">适合个人用户和小团队试用</div>
                    <ul>
                        <li>最多3个工作流</li>
                        <li>基础组件库</li>
                        <li>社区支持</li>
                        <li>本地执行</li>
                        <li class="unavailable">MCP服务市场</li>
                        <li class="unavailable">智能体协作</li>
                        <li class="unavailable">企业级支持</li>
                    </ul>
                    <button class="btn" style="width: 100%;">免费开始</button>
                </div> -->

                <!-- 专业版 -->
                <div class="pricing-card featured">
                    <h3>专业版</h3>
                    <div class="price">
                        面议
                        <span class="period">/月</span>
                    </div>
                    <div class="description">适合中小企业和专业用户</div>
                    <ul>
                        <li>无限工作流</li>
                        <li>完整组件库</li>
                        <li>MCP服务市场</li>
                        <li>基础智能体功能</li>
                        <li>云端执行</li>
                        <li>邮件支持</li>
                        <li class="unavailable">高级智能体协作</li>
                        <li class="unavailable">私有部署</li>
                    </ul>
                    <button class="btn" style="width: 100%;">立即购买</button>
                </div>

                <!-- 企业版 -->
                <div class="pricing-card">
                    <h3>企业版</h3>
                    <div class="price">
                        <!-- ¥999 -->
                        面议
                        <span class="period">/月</span>
                    </div>
                    <div class="description">适合大型企业和高级用户</div>
                    <ul>
                        <li>无限工作流</li>
                        <li>完整组件库</li>
                        <li>全部MCP服务</li>
                        <li>高级智能体协作</li>
                        <li>A2A协议支持</li>
                        <li>优先技术支持</li>
                        <li>API访问权限</li>
                        <li>数据分析报告</li>
                    </ul>
                    <button class="btn" style="width: 100%;">联系销售</button>
                </div>

                <!-- 定制版 -->
                <div class="pricing-card">
                    <h3>定制版</h3>
                    <div class="price">
                        定制报价
                    </div>
                    <div class="description">为大型企业量身定制的解决方案</div>
                    <ul>
                        <li>私有部署</li>
                        <li>定制开发</li>
                        <li>专属技术支持</li>
                        <li>培训服务</li>
                        <li>SLA保障</li>
                        <li>安全合规认证</li>
                        <li>专属客户经理</li>
                        <li>7x24小时支持</li>
                    </ul>
                    <button class="btn" style="width: 100%;">咨询方案</button>
                </div>
            </div>

            <!-- 常见问题 -->
            <div style="margin-top: 4rem;">
                <h3>常见问题</h3>
                <div class="grid" style="margin-top: 2rem;">
                    <div class="card">
                        <h4>如何升级我的方案？</h4>
                        <p>您可以随时在用户中心升级您的方案，升级后立即生效，按比例计费。</p>
                    </div>
                    <div class="card">
                        <h4>是否支持按年付费？</h4>
                        <p>支持按年付费，年付可享受2个月免费优惠，相当于8.3折优惠。</p>
                    </div>
                    <div class="card">
                        <h4>MCP服务如何计费？</h4>
                        <p>MCP服务采用独立计费模式，您可以根据需要订阅相应的服务包。</p>
                    </div>
                    <div class="card">
                        <h4>是否提供试用期？</h4>
                        <p>专业版和企业版都提供14天免费试用，无需信用卡，试用期结束后可选择续费。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 4. 企业服务 -->
    <section id="enterprise" class="section">
        <div class="container">
            <h2>企业服务</h2>
            <div class="grid">
                <div class="card">
                    <h3>🏢 私有部署</h3>
                    <p>提供私有云和本地部署解决方案，确保数据安全和合规性</p>
                    <ul>
                        <li>本地化部署支持</li>
                        <li>私有云环境搭建</li>
                        <li>数据安全保障</li>
                        <li>合规性认证</li>
                    </ul>
                    <button class="btn" style="margin-top: 1rem;">咨询方案</button>
                </div>

                <div class="card">
                    <h3>🛠️ 定制开发</h3>
                    <p>企业级定制开发服务，包括模板开发、MCP服务开发等</p>
                    <ul>
                        <li>专业工作流模板开发</li>
                        <li>行业MCP服务定制</li>
                        <li>系统集成开发</li>
                        <li>API接口定制</li>
                    </ul>
                    <button class="btn" style="margin-top: 1rem;">联系开发</button>
                </div>

                <div class="card">
                    <h3>📚 培训服务</h3>
                    <p>专业的用户培训服务，快速提升团队使用效率</p>
                    <ul>
                        <li>基础操作培训</li>
                        <li>高级功能培训</li>
                        <li>管理员培训</li>
                        <li>认证考试</li>
                    </ul>
                    <button class="btn" style="margin-top: 1rem;">预约培训</button>
                </div>

                <div class="card">
                    <h3>🔧 技术支持</h3>
                    <p>全方位技术支持服务，确保系统稳定运行</p>
                    <ul>
                        <li>7x24小时技术支持</li>
                        <li>远程故障诊断</li>
                        <li>系统优化建议</li>
                        <li>版本升级服务</li>
                    </ul>
                    <button class="btn" style="margin-top: 1rem;">获取支持</button>
                </div>
            </div>
        </div>
    </section>

    <!-- 6. 资源中心 -->
    <!-- <section id="resources" class="section">
        <div class="container">
            <h2>资源中心</h2>
            <div class="grid">
                <div class="card">
                    <h3>📖 产品文档</h3>
                    <ul>
                        <li><a href="#">用户操作手册</a></li>
                        <li><a href="#">产品路线图</a></li>
                    </ul>
                </div>

                <div class="card">
                    <h3>📝 更新日志</h3>
                    <div style="max-height: 200px; overflow-y: auto;">
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <strong>v2.1.0 (2024-01-15)</strong>
                            <ul>
                                <li>新增MCP协议支持</li>
                                <li>优化工作流设计器性能</li>
                                <li>修复已知问题</li>
                            </ul>
                        </div>
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <strong>v2.0.5 (2024-01-01)</strong>
                            <ul>
                                <li>增强智能体协作功能</li>
                                <li>新增A2A协议支持</li>
                                <li>界面优化改进</li>
                            </ul>
                        </div>
                        <div style="padding: 10px 0;">
                            <strong>v2.0.0 (2023-12-15)</strong>
                            <ul>
                                <li>全新架构升级</li>
                                <li>AG-UI集成</li>
                                <li>Agno智能体框架</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <p>&copy; 2025 浙江和达科技股份有限公司 </p>
        </div>
    </footer>
</div>
</template>

<script> 
export default {
  data () {
    return {
        currentTab: 'services',
        tabs:[
            {label: '服务包', value: 'services'},
            {label: '模板库', value: 'templates'},
            {label: 'MCP广场', value: 'mcp'}
        ]
    };
  },

  components: {},

  computed: {},

  mounted(){
    
  },

  methods: {
    showMoreIndustries() {
        alert('更多行业解决方案页面开发中...\n\n即将推出：\n• 金融服务解决方案\n• 零售电商解决方案\n• 医疗健康解决方案\n• 教育培训解决方案\n• 物流运输解决方案\n• 政府机构解决方案');
    },
    showTab(tabName) {
        this.currentTab = tabName;
    },
    jumpToPage(path) {
        this.$router.push({
            name: path,
            query: this.$route.query
        });
    },
  }
}

</script>
<style>
* {
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    color: #333;
}
</style>   
<style scoped>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.homepage{
    width:100%;
    height:100%;
    overflow: auto;
    line-height:1.6;
}
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header{
            width:100%;
            display:flex;
            justify-content: space-between;
            align-items: center;
            margin-top:-1em;
            padding:0 1em;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
        }

        .section {
            padding: 3rem 0;
            border-bottom: 1px solid #eee;
        }

        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem 0;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid white;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature {
            text-align: center;
            padding: 1.5rem;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: #3498db;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2rem;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background: #f8f9fa;
        }

        .search-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .search-bar input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-bar select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            background: white;
        }

        .service-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .service-card .price {
            color: #e74c3c;
            font-weight: bold;
            margin: 1rem 0;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 2rem;
        }

        .tab {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .tab.active {
            border-bottom: 2px solid #3498db;
            color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-top: 2rem;
        }

        @media (max-width: 1200px) {
            .pricing-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }

        .pricing-card {
            background: white;
            border: 2px solid #eee;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .pricing-card.featured {
            border-color: #3498db;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: "推荐";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #3498db;
            color: white;
            padding: 5px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .pricing-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .pricing-card .price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #e74c3c;
            margin: 1rem 0;
        }

        .pricing-card .price .period {
            font-size: 1rem;
            color: #666;
            font-weight: normal;
        }

        .pricing-card .description {
            color: #666;
            margin-bottom: 2rem;
        }

        .pricing-card ul {
            list-style: none;
            text-align: left;
            margin-bottom: 2rem;
        }

        .pricing-card ul li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .pricing-card ul li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-right: 10px;
        }

        .pricing-card ul li.unavailable {
            color: #ccc;
        }

        .pricing-card ul li.unavailable:before {
            content: "✗";
            color: #e74c3c;
        }

        .solution-hero {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        .industry-grid {
            display: grid;
            gap: 2rem;
            margin-top: 3rem;
        }

        .industry-row-1 {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .industry-row-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-top: 2rem;
        }

        .industry-row-3 {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .industry-row-2 {
                grid-template-columns: 1fr;
            }
        }

        .industry-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .industry-card:hover {
            transform: translateY(-5px);
        }

        .industry-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .industry-header.water {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .industry-header.office {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .industry-header.manufacturing {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .industry-header.finance {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .industry-header.retail {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .industry-header.healthcare {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }

        .industry-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .industry-content {
            padding: 2rem;
        }

        .solution-features {
            list-style: none;
            margin: 1.5rem 0;
        }

        .solution-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .solution-features li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-right: 10px;
        }

        .case-study {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1.5rem 0;
        }

        .case-study h5 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .roi-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .roi-stat {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .roi-stat .number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #e74c3c;
        }

        .roi-stat .label {
            font-size: 0.9rem;
            color: #666;
        }

        .more-industries {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 300px;
            border-radius: 12px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .more-industries:hover {
            transform: translateY(-5px);
        }

        .more-industries-content h3 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .more-industries-content .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
</style>