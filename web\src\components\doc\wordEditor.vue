<template>
  <div :id="componentId" ref="wordEdtiorCon"
      @drop="handleDrop" class="canvas-content" :class="{'is-template': templateId, 'is-view': isView}">
    <div class="editor-loading" v-show="loading" v-loading="loading" element-loading-text="正在加载文件内容..."></div>
    <div v-show="!isView" class="menu" editor-component="menu">
      <!-- v-if="docId" -->
      <div class="menu-item">
        <div class="menu-item__undo">
          <i></i>
        </div>
        <div class="menu-item__redo">
          <i></i>
        </div>
        <div class="menu-item__painter" title="格式刷(双击可连续使用)">
          <i></i>
        </div>
        <div class="menu-item__format" title="清除格式">
          <i></i>
        </div>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item">
        <div class="menu-item__font">
          <span class="select" title="字体">微软雅黑</span>
          <div class="options">
            <ul>
              <li data-family="Microsoft YaHei" style="font-family:'Microsoft YaHei';">微软雅黑</li>
              <li data-family="宋体" style="font-family:'宋体';">宋体</li>
              <li data-family="黑体" style="font-family:'黑体';">黑体</li>
              <li data-family="仿宋" style="font-family:'仿宋';">仿宋</li>
              <li data-family="楷体" style="font-family:'楷体';">楷体</li>
              <li data-family="等线" style="font-family:'等线';">等线</li>
              <li data-family="华文琥珀" style="font-family:'华文琥珀';">华文琥珀</li>
              <li data-family="华文楷体" style="font-family:'华文楷体';">华文楷体</li>
              <li data-family="华文隶书" style="font-family:'华文隶书';">华文隶书</li>
              <li data-family="华文新魏" style="font-family:'华文新魏';">华文新魏</li>
              <li data-family="华文行楷" style="font-family:'华文行楷';">华文行楷</li>
              <li data-family="华文中宋" style="font-family:'华文中宋';">华文中宋</li>
              <li data-family="华文彩云" style="font-family:'华文彩云';">华文彩云</li>
              <li data-family="Arial" style="font-family:'Arial';">Arial</li>
              <li data-family="Segoe UI" style="font-family:'Segoe UI';">Segoe UI</li>
              <li data-family="Ink Free" style="font-family:'Ink Free';">Ink Free</li>
              <li data-family="Fantasy" style="font-family:'Fantasy';">Fantasy</li>
            </ul>
          </div>
        </div>
        <div class="menu-item__size">
          <span class="select" title="字体">小四</span>
          <div class="options">
            <ul>
              <li data-size="56">初号</li>
              <li data-size="48">小初</li>
              <li data-size="34">一号</li>
              <li data-size="32">小一</li>
              <li data-size="29">二号</li>
              <li data-size="24">小二</li>
              <li data-size="21">三号</li>
              <li data-size="20">小三</li>
              <li data-size="18">四号</li>
              <li data-size="16">小四</li>
              <li data-size="14">五号</li>
              <li data-size="12">小五</li>
              <li data-size="10">六号</li>
              <li data-size="8">小六</li>
              <li data-size="7">七号</li>
              <li data-size="6">八号</li>
            </ul>
          </div>
        </div>
        <div class="menu-item__size-add">
          <i></i>
        </div>
        <div class="menu-item__size-minus">
          <i></i>
        </div>
        <div class="menu-item__bold">
          <i></i>
        </div>
        <div class="menu-item__italic">
          <i></i>
        </div>
        <div class="menu-item__underline">
          <i></i>
          <span class="select"></span>
          <div class="options">
            <ul>
              <li data-decoration-style='solid'>
                <i></i>
              </li>
              <li data-decoration-style='double'>
                <i></i>
              </li>
              <li data-decoration-style='dashed'>
                <i></i>
              </li>
              <li data-decoration-style='dotted'>
                <i></i>
              </li>
              <li data-decoration-style='wavy'>
                <i></i>
              </li>
            </ul>
          </div>
        </div>
        <div class="menu-item__strikeout" title="删除线(Ctrl+Shift+X)">
          <i></i>
        </div>
        <div class="menu-item__superscript">
          <i></i>
        </div>
        <div class="menu-item__subscript">
          <i></i>
        </div>
        <div class="menu-item__color" title="字体颜色">
          <i></i>
          <span></span>
          <input type="color" id="color" />
        </div>
        <div class="menu-item__highlight" title="高亮">
          <i></i>
          <span></span>
          <input type="color" id="highlight">
        </div>
        <div class="menu-item__background" title="填充单元格颜色">
          <i></i>
          <span></span>
          <input type="color" id="background">
        </div>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item">
        <div class="menu-item__title">
          <i></i>
          <span class="select" title="切换标题">正文</span>
          <div class="options">
            <ul>
              <li style="font-size:16px;">正文</li>
              <li data-level="first" style="font-size:26px;">标题1</li>
              <li data-level="second" style="font-size:24px;">标题2</li>
              <li data-level="third" style="font-size:22px;">标题3</li>
              <li data-level="fourth" style="font-size:20px;">标题4</li>
              <li data-level="fifth" style="font-size:18px;">标题5</li>
              <li data-level="sixth" style="font-size:16px;">标题6</li>
            </ul>
          </div>
        </div>
        <div class="menu-item__left">
          <i></i>
        </div>
        <div class="menu-item__center">
          <i></i>
        </div>
        <div class="menu-item__right">
          <i></i>
        </div>
        <div class="menu-item__alignment">
          <i></i>
        </div>
        <div class="menu-item__justify">
          <i></i>
        </div>
        <div class="menu-item__row-margin">
          <i title="行间距"></i>
          <div class="options">
            <ul>
              <li data-rowmargin='1'>1</li>
              <li data-rowmargin="1.25">1.25</li>
              <li data-rowmargin="1.5">1.5</li>
              <li data-rowmargin="1.75">1.75</li>
              <li data-rowmargin="2">2</li>
              <li data-rowmargin="2.5">2.5</li>
              <li data-rowmargin="3">3</li>
            </ul>
          </div>
        </div>
        <div class="menu-item__list">
          <i></i>
          <div class="options">
            <ul>
              <li>
                <label>取消列表</label>
              </li>
              <li data-list-type="ol" data-list-style='decimal'>
                <label>有序列表：</label>
                <ol>
                  <li>________</li>
                </ol>
              </li>
              <li data-list-type="ul" data-list-style='checkbox'>
                <label>复选框列表：</label>
                <ul style="list-style-type: '☑️ ';">
                  <li>________</li>
                </ul>
              </li>
              <li data-list-type="ul" data-list-style='disc'>
                <label>实心圆点列表：</label>
                <ul style="list-style-type: disc;">
                  <li>________</li>
                </ul>
              </li>
              <li data-list-type="ul" data-list-style='circle'>
                <label>空心圆点列表：</label>
                <ul style="list-style-type: circle;">
                  <li>________</li>
                </ul>
              </li>
              <li data-list-type="ul" data-list-style='square'>
                <label>空心方块列表：</label>
                <ul style="list-style-type: square;">
                  <li>________</li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <span ref="popoverAppendTo"  v-click-outside="onClickOutside"  style="cursor:pointer" class="extend-tools"><el-icon><MoreFilled /></el-icon></span>
  
      <el-popover
        ref="popoverRef"
        title=""
        trigger="click"
        width="400"
        :append-to="wordEdtiorCon"
        :virtual-ref="popoverAppendTo"
        virtual-triggering
        key="">
       
        <div class="">
            <div class="menu-item">
                <div class="menu-item__chart" title="图表">
                <i></i>
                </div>
                <div class="menu-item__table">
                <i title="表格"></i>
                </div>
                <div class="menu-item__table__collapse">
                <div class="table-close">×</div>
                <div class="table-title">
                    <span class="table-select">插入</span>
                    <span>表格</span>
                </div>
                <div class="table-panel"></div>
                </div>
                <div class="menu-item__image">
                <i title="图片"></i>
                <input type="file" id="image" accept=".png, .jpg, .jpeg, .svg, .gif"/>
                </div>
                <div class="menu-item__imageVar">
                  <i title="图片占位符"></i>
                </div>
                <div class="menu-item__hyperlink">
                <i title="超链接"></i>
                </div>
                <div class="menu-item__separator">
                <i title="分割线"></i>
                <div class="options">
                    <ul>
                    <li data-separator='0,0'>
                        <i></i>
                    </li>
                    <li data-separator="1,1">
                        <i></i>
                    </li>
                    <li data-separator="3,1">
                        <i></i>
                    </li>
                    <li data-separator="4,4">
                        <i></i>
                    </li>
                    <li data-separator="7,3,3,3">
                        <i></i>
                    </li>
                    <li data-separator="6,2,2,2,2,2">
                        <i></i>
                    </li>
                    </ul>
                </div>
                </div>
                <!-- <div class="menu-item__watermark">
                <i title="水印(添加、删除)"></i>
                <div class="options">
                    <ul>
                    <li data-menu="add">添加水印</li>
                    <li data-menu="delete">删除水印</li>
                    </ul>
                </div>
                </div> -->
                <div class="menu-item__codeblock" title="代码块">
                <i></i>
                </div>
                <div class="menu-item__page-break" title="分页符">
                <i></i>
                </div>
                <div class="menu-item__control">
                <i title="控件"></i>
                <div class="options">
                    <ul>
                    <li data-control='text'>文本</li>
                    <li data-control="select">列举</li>
                    <li data-control="date">日期</li>
                    <li data-control="checkbox">复选框</li>
                    <li data-control="radio">单选框</li>
                    </ul>
                </div>
                </div>
                <div class="menu-item__checkbox" title="复选框">
                <i></i>
                </div>
                <div class="menu-item__radio" title="单选框">
                <i></i>
                </div>
                <!-- <div class="menu-item__latex" title="LateX">
                <i></i>
                </div> -->
                <div class="menu-item__date">
                <i title="日期"></i>
                <div class="options">
                    <ul>
                    <li data-format="yyyy-MM-dd"></li>
                    <li data-format="yyyy-MM-dd hh:mm:ss"></li>
                    </ul>
                </div>
                </div>
                <!-- <div class="menu-item__block" title="内容块">
                <i></i>
                </div> -->
                <div class="menu-item__search" data-menu="search">
                <i></i>
                </div>
                <div class="menu-item__search__collapse" data-menu="search">
                <div class="menu-item__search__collapse__search">
                    <input type="text" />
                    <label class="search-result"></label>
                    <div class="arrow-left">
                    <i></i>
                    </div>
                    <div class="arrow-right">
                    <i></i>
                    </div>
                    <span>×</span>
                </div>
                <div class="menu-item__search__collapse__replace">
                    <input type="text">
                    <button>替换</button>
                </div>
                </div>
                <div class="menu-item__print" data-menu="print">
                <i></i>
                </div>
            </div>
        </div>
      </el-popover>
    </div>
    <div class="catalog" editor-component="catalog" v-show="showCatelog">
      <div class="catalog__header">
        <span>目录</span>
        <div class="catalog__header__close" title="关闭">
          <i></i>
        </div>
      </div>
      <div class="catalog__main"></div>
    </div>
    <div class="editor-scroll">
      <div :class="`editor${componentId}`" class="editor" @click="closePanel"></div>
    </div>
    <div class="comment" editor-component="comment"></div>
    <div v-show="!isView" class="footer" editor-component="footer">
      <div>
        <div class="catalog-mode" title="目录">
          <i></i>
        </div>
        <div class="page-mode">
          <i title="页面模式(分页、连页)"></i>
          <div class="options">
            <ul>
              <li data-page-mode="paging" class="active">分页</li>
              <li data-page-mode="continuity">连页</li>
            </ul>
          </div>
        </div>
        <span>可见页码：<span class="page-no-list">1</span></span>
        <span>页面：<span class="page-no">1</span>/<span class="page-size">1</span></span>
        <span>字数：<span class="word-count">0</span></span>
      </div>
      <div v-show="showEditorMode" class="editor-mode" title="编辑模式(编辑、清洁、只读、表单)">编辑模式</div>
      <div>
        <div class="page-scale-minus" title="缩小(Ctrl+-)">
          <i></i>
        </div>
        <span class="page-scale-percentage" title="显示比例(点击可复原Ctrl+0)">100%</span>
        <div class="page-scale-add" title="放大(Ctrl+=)">
          <i></i>
        </div>
        <div class="paper-size">
          <i title="纸张类型"></i>
          <div class="options">
            <ul>
              <li data-paper-size="794*1123" class="active">A4</li>
              <li data-paper-size="1593*2251">A2</li>
              <li data-paper-size="1125*1593">A3</li>
              <li data-paper-size="565*796">A5</li>
              <li data-paper-size="412*488">5号信封</li>
              <li data-paper-size="450*866">6号信封</li>
              <li data-paper-size="609*862">7号信封</li>
              <li data-paper-size="862*1221">9号信封</li>
              <li data-paper-size="813*1266">法律用纸</li>
              <li data-paper-size="813*1054">信纸</li>
            </ul>
          </div>
        </div>
        <div class="paper-direction">
          <i title="纸张方向"></i>
          <div class="options">
            <ul>
              <li data-paper-direction="vertical" class="active">纵向</li>
              <li data-paper-direction="horizontal">横向</li>
            </ul>
          </div>
        </div>
        <div class="paper-margin" title="页边距">
          <i></i>
        </div>
        <div class="fullscreen" title="全屏显示">
          <i></i>
        </div>
        <div class="editor-option" title="编辑器设置">
          <i></i>
        </div>
      </div>
    </div>
  </div>
</template>
 
<script setup>
import { ref, unref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Init,convertHtml } from './canvas.js'
import { ClickOutside as vClickOutside } from 'element-plus'



// Props
const props = defineProps({
  allFields:Object,
  commentList:Array,
  documentModel: Object,
  draggingField: Object,
  afterLoad: Function,
  contentChange: Function,
  isView: Boolean,
  id:String
})

// 在editor插入文本
const executeInsertElementList = (html) => {
  const options = instance.value.command.getOptions()
  const margins = options.margins
  const direction = options.paperDirection == 'horizontal' ? 1 : 0

  const innerWidth = (direction === 0 ? options.width : options.height) - margins[1] - margins[3]
  const innerHeight =
    (direction === 1 ? options.width : options.height) - margins[0] - margins[2] - 100
  const element = convertHtml(html, {
    innerWidth,
  })

  const width = innerWidth
  const height = innerHeight
  // 处理图片过大的问题
  element.forEach((it) => {
    if (it.type == 'image') {
      let oldWidth = it.width
      let oldHeight = it.height
      if (oldWidth > width) {
        it.width = width
        it.height = oldHeight * (it.width / oldWidth)
        oldWidth = it.width
        oldHeight = it.height
      }
      if (oldHeight > height) {
        it.height = height
        it.width = oldWidth * (it.height / oldHeight)
      }
    }
  })
  // 插入元素
  instance.value.command.executeInsertElementList(element)
}

// 拖拽进入 canvas 区域事件处理函数
const handleDragEnter = (event) => {
  event.preventDefault();
  ;
  if(!props.isView){
    executeInsertElementList(`#{${props.draggingField.path}}`)
  }
  // 可在这里添加样式，提示用户进入可放置区域
};

// 放置事件处理函数
const handleDrop = (event) => {
  event.preventDefault();
  ;
  if(!props.isView){
    executeInsertElementList(`#{${props.draggingField.path}}`)
  }
};

// Router
const router = useRouter()
const route = useRoute()

// 生成组件唯一标识符
const componentId = ref(`word-editor-${Math.random().toString(36).substr(2, 9)}`)

// Refs
const popoverRef = ref()

const popoverAppendTo = ref()

const wordEdtiorCon = ref()



// Reactive data
const extendToolsVisible = ref(true)
const showCatelog = ref(false)
const loading = ref(false)
const dialogVisible = ref(false)
const titleWidth = ref(40)
const saveTitle = ref('')
const instance = ref(null)
const options = reactive({
  data: {
    header: [],
    main: [],
    footer: []
  },
  options: {
    margins: [
      50, // 上边距
      50, // 右边距
      70, // 下边距
      50 // 左边距
    ],
  }
})

const docData = reactive({
  updated: '',
  docName: ''
})

const showEditorMode = ref(true)
const disabledName = ref(false) // 是否禁用编辑文档名称

const model = reactive({
  name: ''
})

const docName = ref('')
const docId = ref(null)
const templateId = ref(null)
const listTabsLift = ref('290px')
const pid = ref(null)
const tabsVisible = ref(false)
const tabsType = ref(null)
const rangeText = ref('') // word编译器选中文本
const isChange = ref(false) // 文档内容是否改变
const isChangeTime = ref(0) // 记录文档内容变化时的savedTime值
const isSaved = ref(false) // 当前是否在保存中
const timer = ref(null) // 定时函数
const savedTime = ref(60) // 距离上次保存的时间,自动保存间隔30秒,默认30秒
const retentionTime = ref('') // 最近一次文档修改时间
const activeTool = ref(false)

const relationDialog = reactive({
  visible: false,
  url: ''
})


const emit = defineEmits(['content-change','update:commentList','click'])

const isReadonly = ref(false)
const isDownload = ref(true)
const applyDownload = ref(false)

const contentSize = reactive({
  'A4': '794*1123',
  'A2': '1593*2251',
  'A3': '1125*1593',
  'A5': '565*796',
  '5号信封': '412*488',
  '6号信封': '450*866',
  '7号信封': '609*862',
  '9号信封': '862*1221',
  '法律用纸': '813*1266',
  '信纸': '813*1054'
})

const paperType = ref('A4') // 纸张类型
const direction = ref(0) // 0:纵向, 1:横向
// Computed
const showAi = computed(() => {
  return !!route.query.showAi
})

const hideImb = computed(() => {
  return !!route.query.hideImb
})

const downloadShow = computed(() => {
  if(model.shareType == '0') {
    return model.permission && model.permission.length && (model.permission.includes(1) || model.permission.includes(2))
  }
  return model.permission && model.permission.length && !model.isDelete
})
// Watch
watch(rangeText, (n, o) => {
  if (!n) {
    tabsType.value = 1
  }
})

watch(() => model.name, () => {
  nextTick(() => {
    const text = document.getElementsByClassName('menu-display-name')[0]
    titleWidth.value = text ? text.clientWidth : 40
  })
})
// Lifecycle - activated equivalent
const resetData = () => {
  Object.assign(model, {
    id: '',
    name: '',
    fTtype: ''
  })
  // appHeight(true);
  nextTick(() => {
      destroyEditor()
      initEditor()
      extendToolsVisible.value = false
  })
  
}
const popoverBtnClick = (event) => {
  ;;
  if(event.target.nodeName !== 'I'){
    extendToolsVisible.value = !extendToolsVisible.value
  }
  
}
// Methods
// model.fTtype处理
const handlefType = () => {
  const nameArr = JSON.parse(JSON.stringify(model.name.split('.')))
  model.name = ''
  model.fTtype = ['doc', 'docx'].includes(nameArr[nameArr.length-1]) ? nameArr[nameArr.length-1] : 'docx'
  nameArr.forEach((it, index) => {
    if (nameArr.length == 1) {
      model.name = nameArr[0]
    } else {
      if (index < nameArr.length-1) {
        model.name += it
        if (index < nameArr.length-2) model.name += '.'
      }
    }
  })
}

const onShare = () => {
  // this.$refs.shareDia.open(this.model)
  // Note: refs need to be handled differently in Vue 3
}

// 返回按钮提示弹窗事件
const backConfirm = (value) => {
  dialogVisible.value = false
  if (value == 0) {
    jumpView()
  } else if (value == 1) {
    save({isJumpView: true})
  }
}

// 返回路由
const backView = () => {
  // Word文档内容改变且距上次改变不足30秒(即没有自动保存时,提示用户手动保存)
  if (isChange.value) {
    dialogVisible.value = true
    return
  }
  jumpView()
}
// 跳转路由
const jumpView = () => {
  // appHeight();
  Object.assign(options, {
    data: {
      header: [],
      main: [],
      footer: []
    },
    options: {
      margins: [50, 50, 70, 50],
    }
  })
  let query = {}
  if(showAi.value) query.showAi = true
  if(hideImb.value) query.hideImb = true
  if(route.query.uniwim) query.uniwim = route.query.uniwim
  router.push({
    path: '/doc',
    query
  })
  // 修改当前页签名称
  let paramsdata = {
    Name: 'WimPic'
  }
  window.top.postMessage('*#hd#*' + JSON.stringify({action: 'EDIT_C_MENU_NAME', params: paramsdata}), '*')
}

// 控制容器#app高度;适配word插件滚动
const appHeight = (value) => {
  const dom = document.getElementById('app')
  if (value) {
    dom.style.height = 'auto'
  } else {
    dom.style.height = ''
  }
}

// 清除富文本编译器实例
const destroyEditor = () => {
  if (instance.value) {
    instance.value.destroy()
    instance.value = null

    // 使用组件特定的选择器
    const componentContainer = document.querySelector(`#${componentId.value}`)
    if (componentContainer) {
      // 工具表格清除
      const editorElement = componentContainer.querySelector('.table-panel')
      if(editorElement) editorElement.innerHTML = ''
      // 清除目录
      const catalogMain = componentContainer.querySelector('.catalog__main')
      if(catalogMain) catalogMain.innerHTML = ''
      // 清除批注
      const commentDom = componentContainer.querySelector('.comment')
      if(commentDom) commentDom.innerHTML = ''
    }
  }
}
const convertSvgToPng = ()=>{
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100">
                  <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
                </svg>`;
  const canvas = this.$refs.canvas;
  const ctx = canvas.getContext('2d');
  const img = new Image();
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);
    const pngUrl = canvas.toDataURL('image/png');
    console.log(pngUrl); // 或者你可以在这里处理这个URL，例如显示图片或者下载图片
  };
  img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
}
// 初始化word编辑器
const initEditor = async () => {
  ;
  isreadOnly()
  if (instance.value) {
    // 销毁并重新初始化编译器
    destroyEditor()
  }
  // 使用组件特定的ID
  options.id = componentId.value
  options.allFields = props.allFields

  // 使用组件特定的选择器
  const componentContainer = document.querySelector(`#${componentId.value}`)
  if (!componentContainer) {
    console.error(`Component container not found: ${componentId.value}`)
    return
  }

  let text = componentContainer.querySelector('.page-scale-percentage')
  if (text) text.innerText = `${Math.floor((options.options.scale || 1) * 10 * 10)}%`
  instance.value = Init(JSON.parse(JSON.stringify(options)),props.commentList,props)
  ;
  // await convertToHtml()
  // let element = convertHtml(htmlResult.value)
  // convertSvgToPng()
  // instance.value.command.executeSetRange({startIndex:0,endIndex:0})
  // instance.value.command.executeInsertElementList(element)
  ;
  if (instance.value) {
    instance.value.eventBus.on('click', (evt) => {
      debugger;
      let htmlResult =`<table>
    <thead>
        <tr id="header-row">
            <th>ID</th>
            <th>名称</th>
            <th colspan="4">产品标签</th>
        </tr>
    </thead>
    <tbody>
        <tr id="data-row-9dbc828b">
            <td>101</td>
            <td>智能手表</td>
            <td>运动</td>
            <td>健康</td>
            <td>防水</td>
            <td>NFC</td>
        </tr>
    </tbody>
</table>`
      let element = convertHtml(htmlResult,{
        innerWidth:1000
      })
    })
    // 内容发生变化
    instance.value.eventBus.on('contentChange', () => {
      ;
      isChangeTime.value = savedTime.value // 记录内容发生变化时距上次保存的时间(秒)
      isChange.value = true
      emit('content-change')
      emit('update:commentList',instance.commentList)
    })
    // 监听编译器选取事件
    instance.value.eventBus.on('rangeStyleChange', () => {
      rangeText.value = instance.value.command.getRangeText()
    })
    // 关联超链接点击事件
    instance.value.eventBus.on('hyperlinkClick', (data) => {
      console.log('关联点击事件', data)
      // 以后对接关联弹窗,现在先弹出新页面
      // window.open(data.url);
      relationDialog.url = data.url
      relationDialog.visible = true
    })
    instance.value.eventBus.on('click', (event) => {
      debugger;;
      let context = instance.value.command.getPositionContextByEvent(event, {
        isMustDirectHit: false
      })
      
      emit('click',event,context)
    })
    // 编译器默认聚焦
    instance.value.command.executeFocus({
      position: 'after'
    })

    // 初始化滚动
    nextTick(() => {
      setTimeout(() => {
        const scrollDom = document.getElementsByClassName('editor'+props.id)[0]

        // scrollDomWidth.value = scrollDom?.clientWidth || 0
        if (scrollDom) scrollDom.scrollTo(0, 0)
      }, 0)

      // 在线文件下载的情况
      if (props.afterLoad) {
        props.afterLoad()
        exportWord()
      }
    })
  }
}

// 添加缺失的方法声明
const isreadOnly = () => {
  // 实现只读逻辑
  // 可以根据props.isView或其他条件设置只读状态
}

const save = (options = {}) => {
  // 实现保存逻辑
  if (!instance.value) return
  console.log('保存文档', options)
  // 这里可以添加具体的保存逻辑
}

const exportWord = () => {
  // 实现导出Word逻辑
  if (!instance.value) return
  console.log('导出Word文档')
  // 这里可以添加具体的导出逻辑
}

const onClickOutside = () => {
  ;
  unref(popoverRef).popperRef?.delayHide?.()
}

const init = () => {
  // 实现初始化逻辑
  resetData()
}

// 添加缺失的方法
const closePanel = () => {
  // 关闭工具栏面板
}

const fileNameChange = () => {
  // 文件名变化处理
}

const handleEnter = () => {
  // 回车键处理
}

const handlerStar = () => {
  // 收藏处理
}

const toolBtnClick = (type) => {
  // 工具按钮点击
  console.log('工具按钮点击', type)
}

const onApplyDownload = () => {
  // 申请下载
}

const beforeUpload = () => {
  // 上传前处理
  return false
}

const customUpload = () => {
  // 自定义上传
}

// 生命周期钩子
onMounted(() => {
  resetData()
})

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  destroyEditor()
})

// 暴露给模板的方法和数据
defineExpose({
  instance,
  model,
  docName,
  initEditor,
  save,
  exportWord,
  backView,
  onShare
})
</script>

<style style="less">
  @import url(./canvasContent.less);
</style>
<style lang="less" scoped>
.canvas-content {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    // position: relative;
    position: absolute;
    border-radius: 4px;
    // overflow-y: auto;

    img {
        -webkit-user-drag: none;
    }

    .editor-loading {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
    }

    .menu-item__save i {
        width: 13px;
        height: 13px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url('~@/assets/images/doc/04.png');
        background-size: 100% 100%;
    }
    .menu-item__import i {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url('~@/assets/images/doc/22.png');
        background-size: 100% 100%;
    }
    .menu-item__export i {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url('~@/assets/images/doc/10.png');
        background-size: 100% 100%;
    }

    .menu-name-home {
        position: fixed;
        padding-left: 16px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        background: #ffffff;
        top: 0;
        z-index: 21;
        box-sizing: border-box;
        white-space: nowrap;
        .menu-left {
            display: inline-flex;
            align-items: center;
            width: 0;
            flex: 1;
            overflow: hidden;
            margin-right: 10px;
            > i {
                font-size: 24px;
                color: #666666;
                cursor: pointer;
            }
            > i:hover {
                background: #f2f4f9;
            }
            .menu-name {
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 18px;
                color: rgba(13, 13, 13, 0.9);
                margin-left: 22px;
                .el-input {
                    width: 100%;
                    .el-input__inner {
                        height: 36px;
                        line-height: 36px;
                        border-radius: 2px;
                        font-family: SourceHanSansSC-Medium;
                        font-weight: 500;
                        font-size: 18px;
                        color: rgba(13, 13, 13, 0.9);
                        padding: 0 6px;
                        border-color: transparent;
                        background: transparent;
                        width: inherit;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                        &:hover {
                            border-color: #dcdfe6;
                        }
                        &:focus {
                            border-color: #0054c9;
                        }
                    }
                }
            }
            .menu-display-name {
                min-width: 40px;
                max-width: 40%;
                position: absolute;
                z-index: -10;
                border-radius: 2px;
                font-family: SourceHanSansSC-Medium;
                font-weight: 500;
                font-size: 18px;
                color: rgba(13, 13, 13, 0.9);
                padding: 0 8px;
                display: inline-block;
                opacity: 0;
                white-space: nowrap;
            }
            .saveIcon {
                color: rgba(13, 13, 13, 0.6);
                font-size: 16px;
                margin-left: 10px;
            }
            .saveTitle {
                height: 16px;
                line-height: 16px;
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 12px;
                color: rgba(13, 13, 13, 0.6);
                margin-left: 10px;
            }

            .star-box {
                font-size: 16px;
                padding: 2px;
                margin-left: 18px;
                &:hover {
                    background: #e4e4e4;
                }
                &:active {
                    background: #d2d9e4;
                }
            }
            .star-active {
                color: #ffa903;
                font-size: 20px;
                padding: 0;
            }
        }
        .menu-right {
            display: flex;
            align-items: center;
            padding-right: 16px;
            .el-button {
                border-radius: 4px;
                font-size: 14px;
                padding: 7px 15px;
                height: 32px;
                & + .el-button {
                    margin-left: 10px;
                }
                img {
                    width: 14px;
                    height: 14px;
                    vertical-align: bottom;
                    margin-right: 8px;
                }
            }
            .el-upload--text {
                width: 16px;
                height: 16px;
            }
            .tool-btns {
                display: flex;
                z-index: 10;
                margin-right: 10px;
                .tool-btn {
                    margin-left: 20px;
                    padding: 0 12px 0 4px;
                    height: 32px;
                    background: #f5f5f5;
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: SourceHanSansSC-Regular;
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(13, 13, 13, 0.9);
                    letter-spacing: 0;
                    cursor: pointer;
                    img {
                        width: 24px;
                        height: 24px;
                        margin-right: 8px;
                    }
                    &:hover {
                        background: #eeeeee;
                    }
                    &.active {
                        background: #e5e5e5;
                    }
                    &.disabled {
                        background: transparent;
                        color: #999;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }

    .listTabs {
        position: fixed;
        top: 101px;
        right: 0;
        height: calc(100% - 131px);
        z-index: 20;
    }

    &.is-template {
        .menu-name-home {
            .menu-left {
                > * {
                    display: none;
                }
            }
        }
    }

    .editor-scroll {
        width: 100%;
        display: inline-flex;
        height: calc(100% - 135px);
        margin: 105px auto 0;
        justify-content: center;
        .editor {
            overflow-y: auto;
            z-index: 1;
        }
    }

    &.is-view {
        .editor-scroll {
            height: 100%;
            margin-top: 0;
            padding: 10px 0;
            box-sizing: border-box;
            // overflow-y: auto;
            .editor {
                // overflow: initial;
            }
        }
    }
}

.back-confirm-dialog {
    margin-top: calc(50vh - 75px) !important;
    .el-dialog__header {
        padding: 0 15px;
        display: flex;
        height: 44px;
        align-items: center;
        justify-content: space-between;
        .el-dialog__headerbtn {
            position: relative;
            top: 0;
            right: 0;
        }
    }
    .el-dialog__body {
        padding: 0;
        .back-confirm-dialog-content {
            padding: 10px 15px;
            color: #606266;
            font-size: 14px;
            display: flex;
            align-items: center;
            i {
                font-size: 24px;
                color: #e6a23c;
                margin-right: 10px;
            }
        }
    }
}
</style>