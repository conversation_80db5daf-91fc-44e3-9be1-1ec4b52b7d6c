<template>
  <div class="wim-container">
    <div class="wim-header">
      <Navbar title="工作台"/>
    </div>
    <div class="wim-body">
      <van-tabs v-model:active="activeTab" sticky>
        <van-tab v-for="tab in tabs" :key="tab.name" :title="tab.title">
          <List :taskState="tab.name" :key="tab.name"/>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'workspace'
})
import { ref } from 'vue'
const tabs = ref([
  {
    title: '全部',
    name: 'all',
  },
  {
    title: '待执行',
    name: 'wait',
  },
  {
    title: '执行中',
    name: 'running',
  },
  {
    title: '已结束',
    name: 'end',
  },
  {
    title: '已停用',
    name: 'unable',
  },
])
const activeTab = ref('all')

import List from './list.vue'
</script>

<style scoped lang="scss">
.wim-body{
  .van-tabs{
    height: 100%;
    :deep(.van-tabs__wrap){
      position: relative;
      z-index: 2;
    }
    :deep(.van-tabs__content){
      height: calc(100% - 44px);

      .van-tab__panel{
        height: 100%;
      }
    }
  }
}
</style>
