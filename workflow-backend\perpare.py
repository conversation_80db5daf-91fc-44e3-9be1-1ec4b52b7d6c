import shutil
from pathlib import Path
from typing import Optional, List, Callable, Dict

from loguru import logger

# 存储所有注册的退出处理函数
_exit_handlers: Dict[str, Callable] = {}


def add_exit_handler(name: str, func: Callable):
    if not callable(func):
        raise TypeError("注册的退出处理函数必须是可调用对象")

    _exit_handlers[name] = func


def run_exit_handlers():
    logger.info("\n开始执行退出处理函数...")
    for name, func in _exit_handlers.items():
        try:
            func()
            logger.info(f"成功执行退出处理函数: {name}")
        except Exception as e:
            logger.info(f"执行退出处理函数 {name} 出错: {e}")
            pass


def delete_prefixed_directories(
    root_path: Path, prefix: str, skip_dirs: Optional[List[Path]] = None
) -> None:
    """
    删除指定路径下所有以特定前缀开头的目录，可通过完整路径指定需要跳过的目录

    参数:
        root_path: 要搜索的根目录（Path对象）
        prefix: 目录名前缀
        skip_dirs: 需要跳过的目录完整路径列表（Path对象）
    """
    # 初始化跳过目录列表（默认为空）
    skip_dirs = skip_dirs or []
    # 转换为绝对路径以便准确比较
    skip_abs_paths = [dir_path.resolve() for dir_path in skip_dirs]

    if not root_path.exists():
        logger.info(f"错误: 路径不存在 - {root_path}")
        return

    if not root_path.is_dir():
        logger.info(f"错误: 不是一个目录 - {root_path}")
        return

    deleted_count = 0
    failed_count = 0
    skipped_count = 0

    # 遍历目录下的所有项目
    for item in root_path.iterdir():
        # 检查是否为目录且名称以指定前缀开头
        if item.is_dir() and item.name.startswith(prefix):
            # 检查是否需要跳过该目录（比较绝对路径）
            if item.resolve() in skip_abs_paths:
                logger.info(f"已跳过: {item}")
                skipped_count += 1
                continue

            try:
                # 递归删除目录及其内容
                shutil.rmtree(item)
                logger.info(f"已删除: {item}")
                deleted_count += 1
            except Exception as e:
                logger.info(f"删除失败 {item}: {str(e)}")
                failed_count += 1

    logger.info(
        f"操作完成 - root:{root_path},prefix:{prefix},skip:{skip_dirs} 成功删除: {deleted_count} 个目录, 失败: {failed_count} 个目录, 已跳过: {skipped_count} 个目录"
    )
