<template>
  <div class="smart-property-panel">
    <template v-if="schema">
      <div class="panel-header">
        <h3 class="panel-title">
          <div class="node-icon" :style="{ '--el-color-primary': getCategoryColor(selectedNode?.data?.category) }">
            <i v-if="selectedNode?.data?.icon" :class="selectedNode?.data?.icon"></i>
            <el-icon v-else :size="16">
              <Setting />
            </el-icon>
          </div>
          <el-input
            class="panel-title-input"
            spellcheck="false"
            v-model="nodeConfig.label"
            @change="updateNodeLabel"
            placeholder="输入节点名称"
          />
        </h3>
        <div class="header-actions">
          <el-button size="small"  v-if="schema.test" title="测试运行" link type="primary" @click="handleTest">
            <el-icon :size="16">
              <i class="action-iconfont icon-yunhang" />
            </el-icon>
          </el-button>
          <!-- 预设选择 -->
          <el-dropdown v-if="hasPresets" @command="applyPreset" size="small">
            <el-button size="small" link type="primary">
              <el-icon>
                <Star />
              </el-icon>
              预设
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="(preset, key) in presets" :key="key" :command="key">
                  <div class="preset-item">
                    <div class="preset-name">{{ preset.label }}</div>
                    <div class="preset-desc">{{ preset.description }}</div>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 验证状态 -->
          <el-tooltip v-if="validationResult" :content="validationTooltip" placement="bottom">
            <el-icon
              :class="['validation-icon', validationResult.isValid ? 'valid' : 'invalid']"
              :size="16"
            >
              <SuccessFilled v-if="validationResult.isValid" />
              <WarningFilled v-else />
            </el-icon>
          </el-tooltip>

          <el-icon @click="handleClose" style="cursor: pointer;"><Close /></el-icon>
        </div>
      </div>
      <div class="panel-description">
        <el-input
            class="panel-title-input"
            spellcheck="false"
            v-model="nodeConfig.description"
            @change="updateNodeDescription"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 1, maxRows: 4 }"
            placeholder="输入节点描述"
          />
      </div>
    </template>
    <div class="panel-header" v-else>
      <h3 class="panel-title">
        <div class="node-icon">
          <el-icon>
            <Setting />
          </el-icon>
        </div>
        <span>属性配置</span>
      </h3>
      <div class="header-actions">
        <el-icon @click="handleClose" style="cursor: pointer;"><Close /></el-icon>
      </div>
    </div>

    <div class="panel-content">
      <el-scrollbar ref="panelScroll" class="panel-content-scrollbar" wrap-style="padding-right: 16px;">
      <!-- 未选中任何节点 -->
      <div v-if="!selectedNode" class="empty-state">
        <el-icon :size="48" color="#c0c4cc">
          <Setting />
        </el-icon>
        <p>请选择一个节点来配置其属性</p>
      </div>

      <!-- 节点属性配置 -->
      <div v-else-if="schema" class="node-properties">
        <!-- 动态配置组 -->
        <div
          v-for="group in visibleGroups"
          :key="group.id"
          class="property-section"
          :class="{ 'advanced-section': group.advanced }"
        >
          <div class="section-header" @click="group.collapsible && toggleGroup(group.id)">
            <div class="section-header-left">
              <el-icon size="16">
                <component :is="group.icon || 'Setting'" />
              </el-icon>
              <span>{{ group.label }}</span>
              <el-tooltip v-if="group.description" :content="group.description" placement="top">
                <el-icon class="help-icon">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
            <div class="section-header-right">
              <div class="extend-buttons" v-if="group.buttons">
                <el-button link type="primary" v-for="button in group.buttons" :key="button.text" @click="sectionHeaderButtonClick(button,nodeConfig)" text size="small" class="extend-button">
                  <el-icon>
                    <component :is="button.icon || ''" />
                  </el-icon>
                  <span>{{ button.text }}</span>
                </el-button>
              </div>
            </div>


<!--            <el-button v-if="group.collapsible" text size="small" class="toggle-button">-->
<!--              <el-icon>-->
<!--                <ArrowDown v-if="!groupCollapsed[group.id]" />-->
<!--                <ArrowRight v-else />-->
<!--              </el-icon>-->
<!--            </el-button>-->
          </div>

<!--          <div v-show="!groupCollapsed[group.id]" class="section-content">-->
          <div class="section-content">
            <el-form
              :model="nodeConfig.config"
              label-position="top"
              @validate="onFieldValidate"
            >
              <SmartConfigField
                v-for="field in getGroupFields(group.id)"
                :key="selectedNode.id + '_' + field.name"
                :field-name="field.name"
                :field-definition="field.definition"
                :value="nodeConfig.config[field.name]"
                :all-values="nodeConfig.config"
                :component-type="selectedNode.data.componentType"
                @update:value="updateConfig"
                @validate="onFieldValidate"
              />
            </el-form>
          </div>
        </div>

        <!-- 验证错误显示 -->
        <div v-if="validationResult && !validationResult.isValid" class="validation-errors">
          <div class="error-header">
            <el-icon color="#f56c6c">
              <WarningFilled />
            </el-icon>
            <span>配置错误</span>
          </div>
          <div class="error-list">
            <div
              v-for="error in validationResult.errors"
              :key="`${error.field}-${error.type}`"
              class="error-item"
            >
              <span class="error-field">{{ getFieldLabel(error.field) }}:</span>
              <span class="error-message">{{ error.message }}</span>
            </div>
          </div>
        </div>

        <!-- 验证警告显示 -->
        <div
          v-if="validationResult && validationResult.warnings.length > 0"
          class="validation-warnings"
        >
          <div class="warning-header">
            <el-icon color="#e6a23c">
              <Warning />
            </el-icon>
            <span>配置警告</span>
          </div>
          <div class="warning-list">
            <div
              v-for="warning in validationResult.warnings"
              :key="`${warning.field}-${warning.type}`"
              class="warning-item"
            >
              <span class="warning-field">{{ getFieldLabel(warning.field) }}:</span>
              <span class="warning-message">{{ warning.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Schema未找到 -->
      <div v-else class="schema-error">
        <el-icon :size="48" color="#f56c6c">
          <WarningFilled />
        </el-icon>
        <p>未找到组件 "{{ selectedNode?.data.componentType }}" 的配置定义</p>
        <p class="error-detail">请检查组件类型是否正确或联系开发人员</p>
      </div>

      </el-scrollbar>
    </div>

    <!-- 运行测试框 -->
    <el-dialog v-model="runTest.visible"  :title="runTest.title" width="500px" :append-to-body="false">
      <div class="variable-part"></div>
      <div class="reponse-part" v-loading="runTest.loading">
         <el-tag :type="runTest.response.status==='success'?'success':'danger'" class="reponse-status-tag">
          <div class="reponse-status-content">
            <div class="reponse-status">
              <div class="reponse-label">状态</div>
              <div class="reponse-content">{{runTest.response.status==='success'?'成功':'失败'}}</div>
            </div>
            <div class="reponse-time">
              <div class="reponse-label">运行时间</div>
              <div class="reponse-content">{{runTest.response.duration}}</div>
            </div>
            <div class="reponse-token-num">
              <div class="reponse-label">总TOKEN数</div>
              <div class="reponse-content">{{runTest.response.tokenNum}}</div>
            </div>
          </div>
         </el-tag>

         <div class="reponse-input">
            <el-form
              :model="runTest.form"
              label-position="top"
            >
              <el-form-item
                label="输入"
              >
                <VariableInputField
                  disabled
                  v-model="runTest.form.input"
                  :field-config="{'type':'json','label':'输入'}"
                  type="json"
                />
              </el-form-item>
              <el-form-item
                label="输出"
              >
                <VariableInputField
                    disabled
                    v-model="runTest.form.output"
                    :field-config="{'type':'json','label':'输出'}"
                    type="json"
                />
              </el-form-item>
            </el-form>
         </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick  } from 'vue'
import {
  Setting,
  InfoFilled,
  Tools,
  Star,
  SuccessFilled,
  WarningFilled,
  Warning,
  QuestionFilled,
  ArrowDown,
  ArrowRight,
} from '@element-plus/icons-vue'

import SmartConfigField from './config/SmartConfigField.vue'
import VariableInputField from '@/components/common/VariableInputField.vue'
import { useWorkflowStore } from '@/stores/workflow'
import {
  getComponentSchema,
  validateComponentConfig,
  getComponentPresets,
  getComponentGroups,
  getFieldsByGroup,
  applyPresetConfig,
} from '@/config/schemaRegistry'
import { getCategoryColor } from '@/utils/componentCategories'
import { configSchemaManager } from "@/utils/configSchema.ts";
import type {ElScrollbar} from "element-plus";
import {ElLoading} from "element-plus";
import saasApi from '@/api/index'
import { workerData } from 'worker_threads'
import {getAvailableVariables} from "@/utils/availableVariables.ts";

const availableVariables = computed(() => getAvailableVariables())

import utils from '@/utils/utils'


// 获取token
const authorization = utils.GetAuthorization()

const workflowStore = useWorkflowStore()

// 响应式数据
const groupCollapsed = ref<Record<string, boolean>>({})

// 计算属性
const selectedNode = computed(() => workflowStore.selectedNode)

const schema = computed(() => {
  if (!selectedNode.value?.data.componentType) return null
  return getComponentSchema(selectedNode.value.data.componentType)
})

// 关闭方法
const handleClose = () => {
  workflowStore.clearSelection()
}



const runTest = ref({
  title:"测试运行",
  visible:false,
  loading:false,
  form:{
    input:'',
    output:''
  },
  response:{
    status:'success',
    duration:null,
    tokenNum:0
  }
})

const nodeConfig = ref({
  label: '',
  description: '',
  config: {},
})

const presets = computed(() => {
  if (!selectedNode.value?.data.componentType) return {}
  return getComponentPresets(selectedNode.value.data.componentType)
})

const hasPresets = computed(() => Object.keys(presets.value).length > 0)



const groups = computed(() => {
  if (!selectedNode.value?.data.componentType) return []
  return getComponentGroups(selectedNode.value.data.componentType)
})

const visibleGroups = computed(() => {
  return groups.value.sort((a, b) => (a.order || 999) - (b.order || 999))
})

const fieldsByGroup = computed(() => {
  if (!selectedNode.value?.data.componentType) return {}
  return getFieldsByGroup(selectedNode.value.data.componentType)
})

const validationResult = computed(() => {
  if (!selectedNode.value?.data.componentType) return null
  return validateComponentConfig(selectedNode.value.data.componentType, nodeConfig.value.config)
})

const validationTooltip = computed(() => {
  if (!validationResult.value) return ''
  if (validationResult.value.isValid) {
    return '配置验证通过'
  }
  return `发现 ${validationResult.value.errors.length} 个错误`
})





// 方法
const handleTest = async() => {
  if (!selectedNode.value?.data.componentType) return
  let variables = availableVariables.value||[];
  let params = {}
  variables.forEach(vari=>{
    params[vari.name] = vari.value
  })
  runTest.value.loading = true;
  // 记录开始时间
  const startTime = performance.now();
  runTest.value.visible = true;
  runTest.value.response = {
      status:'',
      duration:null,
      tokenNum:0
  }
  runTest.value.form.output = '';
  runTest.value.form.input = JSON.stringify(selectedNode.value)
  try{
    const rep = await fetch('http://localhost:39876/execute_node', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization
      },
      body: JSON.stringify([{node:selectedNode.value,params}]),
    })

    runTest.value.loading = false;
    const response = await rep.json()
    // 记录结束时间
    const endTime = performance.now();
    // 计算耗时（毫秒）
    let duration = endTime - startTime;
    let duration_text = duration.toFixed(2)+'ms'
    if(duration>=1000){
      duration_text = parseFloat(duration/1000).toFixed(2)+'s'
    }
    if(response.result&&response.result['Execute Node_output']){
      runTest.value.response = {
        status:'success',
        duration:duration_text,
        tokenNum:0
      }
      runTest.value.form.output = JSON.stringify(response.result['Execute Node_output'])
    }else{
      runTest.value.response = {
        status:'fail',
        duration:duration_text,
        tokenNum:0
      }
      runTest.value.form.output = ''
    }
  }catch(err){
    runTest.value.loading = false;
    // 记录结束时间
    const endTime = performance.now();
    // 计算耗时（毫秒）
    let duration = endTime - startTime;
    let duration_text = duration.toFixed(2)+'ms'
    if(duration>=1000){
      duration_text = parseFloat(duration/1000).toFixed(2)+'s'
    }
    runTest.value.response = {
      status:'fail',
      duration:duration_text,
      tokenNum:0
    }
    runTest.value.form.output = ''
  }
  runTest.value.title = '测试运行 '+ selectedNode.value.data.label
}

// 方法
const sectionHeaderButtonClick = (button: any) => {
  if (button.callback&&typeof button.callback === 'function') {
    let variables = availableVariables.value||[];
    let params = {}
    variables.forEach(vari=>{
      params[vari.name] = vari.value
    })
    button.callback(nodeConfig,selectedNode,params)
  }
}

const getGroupFields = (groupId: string) => {
  return fieldsByGroup.value[groupId] || []
}

const toggleGroup = (groupId: string) => {
  groupCollapsed.value[groupId] = !groupCollapsed.value[groupId]
}

const updateConfig = (fieldName: string, value: any) => {
  if (selectedNode.value) {
    nodeConfig.value.config[fieldName] = value
    workflowStore.updateNodeConfig(selectedNode.value.id, { [fieldName]: value })
  }
}

const updateNodeLabel = () => {
  if (selectedNode.value) {
    workflowStore.updateNodeData(selectedNode.value.id, { label: nodeConfig.value.label })
  }
}

const updateNodeDescription = () => {
  if (selectedNode.value) {
    workflowStore.updateNodeData(selectedNode.value.id, {
      description: nodeConfig.value.description,
    })
  }
}

const applyPreset = (presetName: string) => {
  ;
  if (!selectedNode.value?.data.componentType) return

  const newConfig = applyPresetConfig(
    selectedNode.value.data.componentType,
    presetName,
    nodeConfig.value.config,
  )

  nodeConfig.value.config = { ...newConfig }
  workflowStore.updateNodeConfig(selectedNode.value.id, newConfig)
}



const getFieldLabel = (fieldName: string) => {
  let childrenFieldMap = {}
  if(schema.value.fields){
    for(const key in schema.value.fields){
      if(schema.value.fields[key].children){
        for(let i=0;i<schema.value.fields[key].children.length;i++){
          let childItem = schema.value.fields[key].children[i]
          childrenFieldMap[childItem.id] = childItem.label
        }
      }
    }
  }
  if (!schema.value || !schema.value.fields[fieldName]) {
    return childrenFieldMap[fieldName] || fieldName
  }
  return schema.value.fields[fieldName].label || fieldName
}

const onFieldValidate = (fieldName: string, isValid: boolean, message: string) => {
  // 处理字段验证结果
  console.log('Field validation:', fieldName, isValid, message)
}

// 声明 ref
const panelScroll = ref<InstanceType<typeof ElScrollbar>>()
const isNodeChanged = ref(false) // 新增标志位


// 监听选中节点变化
watch(
  selectedNode,
  (newNode, oldNode) => {
    console.log('Selected node changed:', oldNode?.id, '->', newNode?.id)

    if (newNode) {
      console.log('Updating config for node:', newNode.data.label, newNode.data)
      nodeConfig.value = {
        label: newNode.data.label || '',
        description: newNode.data.description || '',
        config: { ...newNode.data.config },
      }

      // 设置节点变化标志
      isNodeChanged.value = oldNode?.id !== newNode.id

      // 只在节点切换时重置滚动条
      if (isNodeChanged.value) {
        nextTick(() => {
          if (panelScroll.value) {
            panelScroll.value.setScrollTop(0)
          }
        })
      }

      // 仅在首次选择节点或切换到不同节点时重置折叠状态
      if (!oldNode || oldNode.id !== newNode.id) {
        const collapsed: Record<string, boolean> = {}
        schema.value?.groups?.forEach((group) => {
          collapsed[group.id] = group.collapsed || false
        })
        groupCollapsed.value = collapsed
      }
    } else {
      console.log('No node selected, clearing config')
      nodeConfig.value = {
        label: '',
        description: '',
        config: {},
      }
    }
  },
  { immediate: true },
)

// 新增：监听配置变化时不重置滚动条
watch(
  () => nodeConfig.value.config,
  () => {
    // 配置更新时不自动滚动
  },
  { deep: true }
)

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.reponse-status-tag{
  height:auto;
  width:100%;
  margin-bottom:20px;
}
.reponse-status-tag :deep(.el-tag__content){
  width:100%;
}
.reponse-label{
  color: #999;
  margin-bottom: 10px;
}
.reponse-status .reponse-content{
  font-size:14px;
}
.reponse-time .reponse-content,.reponse-token-num .reponse-content{
  color:#999;
  font-size:14px;
}
.reponse-status-content{
  width:100%;
  height:60px;
  align-items: center;
  display:flex;
  justify-content: space-between;
}
.smart-property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.panel-description{
  padding: 0 16px 16px;
  margin-top: -1px;
  z-index: 1;
  background: #ffffff;
  border-bottom: 1px solid #eeeeee;
  font-weight: 400;
  font-size: 12px;
  color: #BCBFC3;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.node-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary);
  color: white;
  border-radius: 6px;
  margin-right: 8px;

  .action-iconfont {
    font-size: 14px;
    font-weight: normal;
  }
}

:deep(.panel-title-input) {
  margin-left: -6px;

  .el-input__wrapper {
    box-shadow: none;
    padding: 0 6px;

    &:hover,
    &:focus-within {
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    }

    .el-input__inner {
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      &::placeholder {
        font-weight: normal;
      }
    }
  }

 .el-textarea__inner {
    font-size: 12px;
    color: #666666;
    box-shadow: none;
    resize: none;
    &::placeholder {
      font-weight: normal;
    }
    &:hover,
    &:focus-within {
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    }
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-icon {
  cursor: help;
}

.validation-icon.valid {
  color: #67c23a;
}

.validation-icon.invalid {
  color: #f56c6c;
}

.preset-item {
  padding: 4px 0;
}

.preset-name {
  font-weight: 500;
  color: #303133;
}

.preset-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  padding: 16px 0 16px 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #909399;
}

.empty-state p {
  margin: 16px 0 0 0;
  font-size: 14px;
}

.schema-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #f56c6c;
}

.schema-error p {
  margin: 16px 0 0 0;
  font-size: 14px;
}

.error-detail {
  font-size: 12px !important;
  color: #909399 !important;
}

.property-section {
  margin-bottom: 24px;
}

.advanced-section {
  border: 1px solid #eeeeee;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  font-size: 14px;
  color: #222222;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
}

.section-header-left{
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  color: #909399;
  cursor: help;
}

.toggle-button {
  margin-left: auto;
  font-size: 12px;
}

.section-content {
  :deep(.el-form-item__label){
    color: #5C5F66;
  }
}

.validation-errors,
.validation-warnings {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
}

.validation-errors {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.validation-warnings {
  background: #fdf6ec;
  border: 1px solid #f5dab1;
}

.error-header,
.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 8px;
}

.error-list,
.warning-list {
  font-size: 12px;
}

.error-item,
.warning-item {
  margin-bottom: 4px;
}

.error-field,
.warning-field {
  font-weight: 500;
}

.error-message,
.warning-message {
  margin-left: 4px;
}

/* Element Plus 表单样式调整 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  line-height: 1.4;
  padding-top: 6px;
}

:deep(.el-input__wrapper) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  font-size: 12px;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
}
</style>
<script setup lang="ts"></script>
