<template>
  <div class="wim-container">
    <div class="wim-header van-hairline--bottom">
      <Navbar :title="missionName" />
      <van-field
        class="history-picker"
        :model-value="onFormatHistoryId(historyExcId)"
        readonly
        name="picker"
        label=""
        placeholder="点击选择历史任务"
        @click="showHistoryExc = true"
      >
        <template #right-icon>
          <van-icon name="exchange" />
        </template>
      </van-field>
      <van-popup
        v-model:show="showHistoryExc"
        destroy-on-close
        closeable
        round
        position="bottom"
        :style="{ height: '50vh' }"
      >
        <div class="history-container">
          <div class="history-title">执行时间</div>
          <div class="history-list">
            <van-list
              v-model:loading="historyLoading"
              :finished="historyFinished"
              finished-text=""
              @load="GetHistoryData"
            >
              <van-empty description="暂无历史任务" v-if="!historyExcData.length" />
              <van-field
                is-link
                size="large"
                readonly
                v-for="(it, index) in historyExcData"
                :key="index"
                :model-value="it.executeDate"
                :class="{ active: it.id === historyExcId }"
                @click="onHistoryExcConfirm(it)"
              >
                <template #right-icon>
                  <span
                    class="st"
                    :class="{ success: it.result === '0', failed: it.result === '1' }"
                  >
                    {{ it.result === '0' ? '成功' : '失败' }}
                  </span>
                </template>
              </van-field>
            </van-list>
          </div>
        </div>
      </van-popup>
    </div>
    <div class="wim-body">
      <van-tabs v-model:active="activeTab" sticky>
        <van-tab v-for="tab in tabs" :key="tab.name" :title="tab.title">
          <Monitor
            v-if="tab.name === 'monitor'"
            :isLoading="isLoading"
            :historyExcId="historyExcId"
            :missionDetail="missionDetail"
            :recordData="recordData"
          />
          <Record v-if="tab.name === 'record'" :isLoading="isLoading" :recordData="recordData" :missionDetail="missionDetail"/>
          <Result v-if="tab.name === 'result'" :isLoading="isLoading" :recordData="recordData" />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'monitor',
})


import api from '@/api'
import { onMounted, ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'


// 组件导入
import Monitor from './monitor.vue'
import Record from './record.vue'
import Result from './result.vue'


//  route 参数
const route = useRoute()
const missionId = route.query.missionId || ''
const missionName = route.query.missionName || '任务监控'


// tabs
const tabs = ref([
  {
    title: '运行监控',
    name: 'monitor',
  },
  {
    title: '执行记录',
    name: 'record',
  },
  {
    title: '执行结果',
    name: 'result',
  },
])
const activeTab = ref(0)


// 根据任务id获取任务详情
const missionDetail = ref({})
const GetMissionDetail = async () => {
  if (!missionId) return (missionDetail.value = {})
  try {
    const res = await api.AIAgentMissionDetail({ id: missionId })
    if (res?.configContent) {
      missionDetail.value = JSON.parse(res.configContent)
    }
  } catch (e) {
    missionDetail.value = {}
  }
}


// 获取选中历史数据的执行记录
const recordData = ref([])
const GetRecordData = async () => {
  recordData.value = []
  if (!historyExcId.value) return
  try {
    const params = {
      data: {
        missionId: missionId,
        historyId: historyExcId.value,
      },
      index: 1,
      size: -1,
    }
    const res = await api.AIAgentMissionLogQuery(params)
    if (res?.rows) {
      const rows = res.rows.sort((a, b) => {
        if (a.nodeId === 'start_node') return -1
        if (b.nodeId === 'start_node') return 1
        return a.excTime - b.excTime
      })
      const messages = []
      rows
        .map((it: object) => {
          return {
            ...it,
            excTime: moment(it.excTime * 1000).format('YYYY-MM-DD HH:mm:ss'),
          }
        })
        .forEach((it: object) => {
          const existingIndex = messages.findIndex((f) => f.nodeId === it.nodeId)
          // 如果存在则替换
          if (existingIndex !== -1) {
            const existingMsg = messages[existingIndex]
            // 保留已有的失败次数
            const failedCount = existingMsg.failedCount || 0

            // 如果当前状态是failed，则增加重试次数
            if (it.state === 'failed') {
              it.failedCount = failedCount + 1
            }
            // 否则保持原有重试次数
            else {
              it.failedCount = failedCount
            }

            messages[existingIndex] = it
          }
          // 不存在则添加
          else {
            it.failedCount = 0

            messages.push(it)
          }
        })
      recordData.value = messages
    } else {
      recordData.value = []
    }
  } catch (e) {
    recordData.value = []
  }
}


// 历史数据分页相关
const historyPageNo = ref(1)
const historyFinished = ref(false)
const historyLoading = ref(false)
// 历史数据
const historyExcId = ref(null)
const historyExcSelect = ref<object>()
const showHistoryExc = ref(false)
const historyExcData = ref([])
// 格式化显示历史数据
const onFormatHistoryId = (id: string) => {
  return historyExcData.value.find((it) => it.id === id)?.executeDate
}
// 获取历史数据
const GetHistoryData = async () => {
  try {
    const params = {
      conditions: [
        {
          Field: 'missionId',
          Group: 1,
          Operate: '=',
          Relation: 'and',
          Value: missionId,
        },
      ],
      data: {},
      index: historyPageNo.value,
      size: 10,
      order: [
        {
          Field: 'executeTime',
          Type: -1,
        },
      ],
    }
    const res = await api.AIAgentMissionHistoryQuery(params)
    if (res.current <= res.pages && res.rows.length > 0) {
      const rows = (res.rows || []).map((it: object) => ({
        ...it,
        executeDate: moment(it.executeTime).format('YYYY-MM-DD HH:mm:ss'),
      }))
      if (historyPageNo.value === 1) {
        historyExcData.value = rows
      } else {
        historyExcData.value = historyExcData.value.concat(rows)
      }
      historyPageNo.value++
    } else {
      if(!res.rows.length && res.current === 1){
        historyExcData.value = []
      }
      historyFinished.value = true
    }
    historyLoading.value = false
  } catch (e) {
    historyFinished.value = true
    historyLoading.value = false
  }
}
// 选择指定历史记录
const isLoading = ref(false)
const onHistoryExcConfirm = async (selectedOptions: object) => {
  historyExcId.value = selectedOptions.id
  historyExcSelect.value = selectedOptions
  showHistoryExc.value = false
  toast.value = showLoadingToast({
    duration: 0,
    forbidClick: true,
  })
  isLoading.value = true
  await nextTick()
  // 获取选中历史数据的执行记录
  await GetRecordData()
  isLoading.value = false
  toast.value?.close()
}

const toast = ref(null)
// 初始化页面
onMounted(async () => {
  toast.value = showLoadingToast({
    duration: 0,
    forbidClick: true,
  })
  isLoading.value = true
  // 初始化获取任务详情配置
  await GetMissionDetail()
  // 初始化获取历史数据和默认选中第一个历史节点
  await GetHistoryData()
  // 默认选中第一个历史节点
  if (historyExcData.value.length > 0) {
    await onHistoryExcConfirm(historyExcData.value[0])
  }
  else{
    isLoading.value = false
    toast.value?.close()
  }
})
</script>

<style scoped lang="scss">
.wim-body {
  .van-tabs {
    height: 100%;

    :deep(.van-tabs__wrap) {
      position: relative;
      box-shadow: 0 1px 12px rgba(239,239,239,0.96);
      z-index: 1;

      &:after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
        border: 0 solid #cccccc;
        transform: scale(0.5);
        border-bottom-width: var(--van-border-width);
      }
    }

    :deep(.van-tabs__content) {
      height: calc(100% - 44px);

      .van-tab__panel {
        height: 100%;
      }
    }
  }
}

.history-picker {
  :deep(.van-field__body) {
    border: 1px solid #e6e7e9;
    padding: 3px 16px;
    box-sizing: border-box;
    border-radius: 4px;
  }
}

.history-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .history-title {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    color: #333333;
    flex-shrink: 0;
    font-weight: bold;
    text-align: center;
  }

  .history-list {
    flex: 1;
    overflow-y: auto;

    .active {
      background: #efefef;
    }
  }
}

.st {
  padding: 2px 6px;
  text-align: center;
  font-size: 12px;
  border-radius: 11px;
  box-sizing: border-box;
  line-height: 1;

  &.success {
    background: rgb(240, 249, 235);
    border: 1px solid rgb(225, 243, 216);
    color: #67c23a;
  }

  &.failed {
    background: #f7f7f9;
    border: 1px solid #e6e7e9;
    color: #666666;
  }
}
</style>
