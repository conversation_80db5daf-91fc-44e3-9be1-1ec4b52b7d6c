(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-016c31a6"],{"00ea":function(e,t,a){},1473:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"realTimeDetail"},[t("cue-crud",{ref:"crud",staticClass:"no-borer-crud",attrs:{version:"2",power:"",tools:e.tools,"context-menu-default":"",columns:e.columns,"before-query":e.beforeQuery,bindings:e.binding,"after-query":e.afterQuery,crud:e.crud,rowNo:!1,isLoading:e.isLoading,pager:{layout:"total,prev,pager,next",size:[30,60,120,300],pagerCount:5},"table-options":{"expand-row-keys":e.expandRowKeys}},on:{"expand-change":e.handleExpandRow,"row-click":e.rowClick,"selection-change":e.handleSelectChange,"sort-change":e.sortChange},scopedSlots:e._u([{key:"areaName",fn:function(a){return[t("span",{staticClass:"single-analysis-link text-overflow",attrs:{title:a.row.areaName}},[e._v(" "+e._s(a.row.areaName)+" ")])]}},{key:"expand",fn:function(a){return[t("div",{staticClass:"expandBox"},[t("div",{staticClass:"condition"},[t("el-date-picker",{staticStyle:{width:"105px"},attrs:{type:"date",clearable:!1,placeholder:"选择日期"},on:{change:t=>e.changeChartDate(t,a.row)},model:{value:e.chart_date,callback:function(t){e.chart_date=t},expression:"chart_date"}})],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:a.row.loading,expression:"scope.row.loading"}],staticClass:"echartBox"},[t("hd-chart",{ref:"hdchart",staticClass:"chart",attrs:{"auto-resize":"",options:a.row.chart_option,type:"echart"}})],1)])]}}])})],1)},s=[],i=a("b6e6"),o=i["a"],l=(a("2991"),a("2877")),n=Object(l["a"])(o,r,s,!1,null,"8f4df54a",null);t["default"]=n.exports},2991:function(e,t,a){"use strict";a("00ea")},b6e6:function(e,t,a){"use strict";(function(e){a("14d9"),a("0643"),a("2382"),a("fffc"),a("4e3e"),a("a573");var r=a("2a49"),s=a("a27e"),i=a("313e"),o=a("c1df"),l=a.n(o);t["a"]={inject:{setAlarmCount:{default:()=>()=>{console.error("setAlarmCount 未定义")}}},props:{classType:{type:String,default:"place"},drillingInfo:{type:Object,default:null},toolbarParams:{type:Object},queryData:{type:Object,default:()=>({})},pointData:{type:Object,default:()=>({})}},components:{},data(){return{isLoading:!0,classTypes:[{label:"场所",value:"place",Name:"场所",Value:"place"},{label:"设备",value:"device",Name:"设备",Value:"device"}],binding:{},columns:[{data:"",className:"expand",type:"expand",scoped:"expand",align:"center",width:20},{data:"areaName",title:"名称",width:124,orderable:!0,scoped:"areaName",align:"center"},{data:"stime",title:"报警时间",width:120,filterable:"string",orderable:!0,dtype:"date",format:"yyyy-MM-dd HH:mm",align:"center"},{data:"svalue",title:"报警值",width:60,filterable:"string",align:"center",scoped:"svalue",orderable:!0},{data:"ref1",title:"限值",width:50,scoped:"tag",align:"center"}],expandRowKeys:[],expandRowInfo:[],alarmDatas:[],title_str:"",masterChartSeriesName:"",isHistoryClick:!1,current_alarm_row:{},qxDatas:[],myChart:null,chart_date:l()().startOf("day").toDate(),limitValue:{up1:"严重报警上限",up2:"一般报警上限",up3:"轻微报警上限",down1:"轻微报警下限",down2:"一般报警下限",down3:"严重报警下限"},hideSeriesKey:["up1","up2","up3","down1","down2","down3","confidenceUp","confidenceDown"],chart_option:{color:["#0299CB","#51C7A6","#F0B54C","#8C95CC","#FC6060","#4DC4CB","#99739D","#FF9845","#52A2F2","#F083B0"],grid:{left:"10%",right:"10%",bottom:"15%",containLabel:!0},series:[],legend:{type:"scroll",show:!0,itemWidth:12,itemHeight:3,icon:"rect",top:0,textStyle:{color:this.$route.query.bigScreenNew?"#ddd":"#666"}},xAxis:{type:"category",name:"",axisLabel:{color:this.$route.query.bigScreenNew?"#ddd":"#666"}},yAxis:{type:"value",textStyle:{color:this.$route.query.bigScreenNew?"#ddd":"#666"},nameTextStyle:{color:this.$route.query.bigScreenNew?"#ddd":"#666"},axisLabel:{color:this.$route.query.bigScreenNew?"#ddd":"#666"}},tooltip:{trigger:"axis",formatter:this.chartFormatter}}}},mounted(){this.binding={...window.UNIWIM_Bindings,classType:this.classType}||{}},computed:{crud:function(){return{key:"id",query:t=>{let a=e.Deferred();return s["f"].post("/imb/hdAlarmRecord/alarm_record/query",t,{async:!0}).then(e=>{this.badges=[],this.badgesName=["","","",""];let t=[],r=0;e&&(this.tempTotal=r=e.total,this.badges=e.badges||[],this.badges=this.badges.sort((e,t)=>e.code-t.code),e.rows&&(t=e.rows)),a.resolve({rows:t,total:r})}),a.promise()}}},tools(){return[]}},watch:{queryData:{deep:!0,immediate:!0,handler(e){e&&this.$nextTick(()=>{var e;null!==(e=this.$refs)&&void 0!==e&&e.crud&&this.$refs.crud.Query()})}},pointData:{deep:!0,immediate:!0,handler(e){e&&this.$nextTick(()=>{var e;null!==(e=this.$refs)&&void 0!==e&&e.crud&&this.$refs.crud.Query()})}}},methods:{changeChartDate(e,t){this.chart_date=1e3*l()(e).unix(),this.getDetails(t,!0)},bindChartOptionToRow(e){this.$set(e,"loading",!1),e.chart_option=Object(r["a"])(this.chart_option)},async getDetails(e,t=!1){if(this.$set(e,"loading",!0),this.current_alarm_row=e,1==e.isRelated){let a={sensor:e.sensor,relations:[],planId:e.planId,isRelated:String(e.isRelated)};t&&(a.begin=l()(this.chart_date).startOf("day").unix(),a.end=l()(l()(this.chart_date).format("YYYY-MM-DD")+" 23:59:59").unix()),e.id&&(a.id=e.id),a.interval=60,this.chart_options.series=[],await Object(s["e"])("/imb/hdAlarmRecord/alarm_record/detail/query",a,{cancelMap:!0}).then(a=>{if(this.alarmDatas=a,a&&a.length){let r=[];a.forEach(e=>{var t;let a=JSON.parse(e.meta),s=(null===a||void 0===a||null===(t=a.hdres)||void 0===t||null===(t=t.entity)||void 0===t?void 0:t.name)||"";r.push(s)}),this.title_str=r.join(","),this.title_str||(this.title_str="暂无"),a[0].stime&&(t||(this.chart_date=1e3*l()(1e3*a[0].stime).startOf("day").unix())),this.renderChartDetails(a,"chart_option"),this.$set(e,"loading",!1)}else this.$set(e,"loading",!1)}).catch(t=>{this.$set(e,"loading",!1)})}else{this.title_str="",this.masterChartSeriesName="";let a={sensor:e.sensor,relations:[],planId:e.planId,isRelated:String(e.isRelated)};if(t&&(a.begin=l()(this.chart_date).startOf("day").unix(),a.end=l()(l()(this.chart_date).format("YYYY-MM-DD")+" 23:59:59").unix()),e.id&&(a.id=e.id),a.interval=60,this.chart_option.series=[],!e.sensor)return;await Object(s["e"])("/imb/hdAlarmRecord/alarm_record/detail/query",a,{cancelMap:!0}).then(a=>{if(a){if(this.current_alarm_row=a,a.meta){let e=JSON.parse(a.meta);this.title_str=e.hdres.entity.name}else this.title_str=a.objName;this.title_str||(this.title_str="暂无"),a.stime&&(t||(this.chart_date=1e3*l()(1e3*a[0].stime).startOf("day").unix())),this.renderChartDetail("chart_option",e),this.$set(e,"loading",!1)}else this.$set(e,"loading",!1)}).catch(t=>{this.$set(e,"loading",!1),console.log(t)})}},getChartxAxis(e){this[e].yAxis=[],this[e].xAxis=[{type:"category",name:"",axisPointer:{show:!0,type:"line",snap:!0},axisLabel:{color:this.$route.query.bigScreenNew?"#ddd":"#666"}}]},getChartyAxis(e){let t={type:"value"},a=[];if(Array.isArray(e))e.forEach(e=>{(e.vals||[]).forEach(e=>{e.v&&a.push(e.v)})});else{let r=e;if(r&&1==r.kind&&r.state){let e=JSON.parse(r.state),a=Object.keys(e).map(e=>e);t={type:"category",nameTextStyle:{padding:[0,0,0,80],color:this.$route.query.bigScreenNew?"#ddd":"#666"},axisLabel:{color:this.$route.query.bigScreenNew?"#ddd":"#666",formatter:function(t){return e[t]}},data:a}}else if(e&&e.historyVals){let t=e.historyVals;Object.keys(t).forEach(e=>{t[e].forEach(e=>{e.v&&a.push(e.v)})})}else e&&e.vals&&e.vals.forEach(e=>e.v&&a.push(e.v)),e&&e.qvals&&e.qvals.forEach(e=>e.v&&a.push(e.v)),e&&e.zvals&&e.zvals.forEach(e=>e.v&&a.push(e.v))}if(a.length>0){Math.max(...a);t={type:"value"}}return t},formatListData(e,t){if(!e)return"";if(!t)return e;try{e=parseInt(e);let a=JSON.parse(t);return a&&a[e]?a[e]:e}catch(a){return e}},chartFormatter(e){e=e.filter(e=>"xAxis.category"==e.axisType);let t="";e&&e.length>0&&(t=e[0].axisValue),e.forEach(e=>{this.limitValue[e.seriesId]&&(e.seriesName=this.limitValue[e.seriesId]),e.seriesId});for(let a=0;a<e.length;a++){const r=e[a];if("confidenceUp"==r.seriesId||"confidenceDown"==r.seriesId)t.indexOf("置信区间")<0&&(t+=`<br/>${r.marker} 置信区间 : ${r.value[2]||"--"}`);else if(r.seriesName){let e={},a=this.unitShow?r.data:r.data[1],s=r.data[2],i=a;e=r.data[3]||{},(i||null!==i&&0==i)&&("严重报警上限"==r.seriesName||"一般报警上限"==r.seriesName||"轻微报警上限"==r.seriesName||"轻微报警下限"==r.seriesName||"一般报警下限"==r.seriesName||"严重报警下限"==r.seriesName||"置信区间"==r.seriesName?t+="<br>"+r.marker+r.seriesName+":"+a:t+="<br>"+r.marker+r.seriesName+":"+(1==s?e[Number(i).toFixed(0)]:i))}}return t},renderChartDetail(e,t){let a=l()(new Date).unix(),s=this;if(this[e].yAxis=[],this.getChartxAxis(e),s.station_columns=[{title:"时间",data:"time"}],this.current_alarm_row.datas&&0==this.current_alarm_row.datas.length)return void(this.chart_option={});this.qxDatas=this.current_alarm_row.datas?this.current_alarm_row.datas:[];let o=this.current_alarm_row.datas,n=[];if(this.current_alarm_row.datas){let t=[],i=o.length>1?[o[1]]:o;i.forEach((i,o)=>{n.push(i.sensor_name),this.masterChartSeriesName=i.sensor_name;let d=i.kind,h=1==d&&i.state?JSON.parse(i.state):null;if(this[e].yAxis[o]=this.getChartyAxis(i),o>0){this[e].yAxis[o].position="right";let t=i.sensor_unit?i.sensor_unit.length:0;this[e].yAxis[o].nameTextStyle={padding:[0,t>3?10*t:0,0,0],color:this.$route.query.bigScreenNew?"#ddd":"#666"},this[e].yAxis[o].axisLabel={color:this.$route.query.bigScreenNew?"#ddd":"#666"}}o>1&&(this[e].yAxis[o].offset=40*o);let c=1e3*Number(this.current_alarm_row.stime),m="";m=this.current_alarm_row.rtime&&l()(c).format("YYYY-MM-DD")==l()(1e3*Number(this.current_alarm_row.rtime)).format("YYYY-MM-DD")?1e3*Number(this.current_alarm_row.rtime):l()(c).format("YYYY-MM-DD")==l()(new Date).format("YYYY-MM-DD")?l()(new Date).valueOf():l()(l()(c).format("YYYY-MM-DD")+" 23:59:59").valueOf();let u={type:"line",data:[],smooth:!0,zlevel:10,connectNulls:!0,step:1==d,code:i.sensor,yAxisIndex:o,lineStyle:{width:"chart_options"==e?4:2},symbol:"none",emphasis:{focus:"series",lineStyle:{width:5}}};s[e].yAxis[o].name=i.sensor_unit,s[e].yAxis[o].nameTextStyle={color:s.$route.query.bigScreenNew?"#fff":"#666"},s[e].yAxis[o].axisLabel={color:s.$route.query.bigScreenNew?"#fff":"#666"};let p=Object(r["a"])(u),f=(s.current_alarm_row.objName,i.sensor);if(p.code=i.sensor,p.name=i.sensor_name,0==o){l()(c).format("YYYY-MM-DD")==l()(this.chart_date).format("YYYY-MM-DD")?p.markArea={itemStyle:{color:"#FF0000",opacity:.3},data:[[{xAxis:l()(c).format("HH:mm")},{xAxis:l()(m).format("HH:mm")}]]}:l()(this.chart_date).format("YYYY-MM-DD")==l()().format("YYYY-MM-DD")?p.markArea={itemStyle:{color:"#FF0000",opacity:.3},data:[[{xAxis:l()(this.chart_date).format("HH:mm")},{xAxis:l()().format("HH:mm")}]]}:Number(c)<Number(this.chart_date)&&(p.markArea={itemStyle:{color:"#FF0000",opacity:.3},data:[[{xAxis:"00:00"},{xAxis:"23:59"}]]});const t={name:"报警区间",type:"line",data:[],markArea:p.markArea,lineStyle:{color:"transparent",width:0},itemStyle:{color:"#FF0000"},symbol:"none"};s[e].series.push(t),delete p.markArea}i.vals&&i.vals.forEach(e=>{let r=e.v;1e3*a>Number(e.t)&&l()(this.chart_date).unix()<a&&p.data.push([l()(Number(e.t)).format("HH:mm"),1==d&&e.v?Number(e.v).toFixed(0):this.formatListData(e.v,i.state),1==d&&h?"1":"0",h]);let s=t.findIndex(t=>t.time==l()(Number(e.t)).format("HH:mm"));s>-1?t[s]["val"+o]=r:t.push({time:l()(Number(e.t)).format("HH:mm"),["val"+o]:this.formatListData(e.v,i.state)})});let y=null,b=null,g=["#FF562B","#AF6E59","#B1CF58","#5ad8a6","#acf4f5","#f0c7f7","#5DB4C4","#C2C6C7"];if("chart_options"==e){let t={};if(i.historyVals){let a=Object.keys(i.historyVals).sort((e,t)=>l()(t).valueOf()-l()(e).valueOf());a.forEach((a,o)=>{i.historyVals[a]&&i.historyVals[a].length&&(t[a]=Object(r["a"])(u),t[a].code=a+i.sensor,t[a].color=g[o],t[a].zlevel=100-o,t[a].name=0==o?i.sensor_name+`(${a})`:""+a,0==o&&(this.masterChartSeriesName=t[a].name),n.push(t[a].name),i.historyVals[a].forEach((e,r)=>{t[a].data.push([l()(Number(e.t)).format("HH:mm"),1==d&&e.v?Number(e.v).toFixed(0):e.v,1==d&&h?"1":"0",h])}),t[a].data.length&&s[e].series.push({...t[a]}))})}}let x=Object(r["a"])(u),_=Object(r["a"])(u),w=Object(r["a"])(u);if(i.jyvals&&"exprange.v1"==this.current_alarm_row.alarmtype&&(_.name="上限",_.data=[],_.zlevel=1,_.smooth=!1,_.symbol="",_.code=_.code+"_up",_.itemStyle={color:"#D9001B"},w.name="下限",w.data=[],w.zlevel=12,w.smooth=!1,w.symbol="",w.code=w.code+"_low",w.itemStyle={color:"#D9001B"},x.name="平均值","chart_option"==e&&n.push(x.name),x.smooth=!1,x.symbol="",x.data=[],x.zlevel=1,x.code=x.code+"_td",x.itemStyle={color:"#ff8882"},i.jyvals.forEach(e=>{_.data.push([l()(Number(e.t)).format("HH:mm"),e.up,1==d&&h?"1":"0",h]),w.data.push([l()(Number(e.t)).format("HH:mm"),e.low,1==d&&h?"1":"0",h]),x.data.push([l()(Number(e.t)).format("HH:mm"),e.avg,1==d&&h?"1":"0",h])})),s.station_columns.push({data:"val"+o,title:i.sensor_name}),p&&"chart_option"==e&&s[e].series.push({...p}),y&&s[e].series.push({...y}),b&&s[e].series.push({...b}),"exprange.v1"==this.current_alarm_row.alarmtype){let t=s[e].series.find(e=>e.code==f+"_td");t?t=x:s[e].series.push({...x});let a=s[e].series.find(e=>e.code==f+"_up");a?a=_:s[e].series.push({..._});let r=s[e].series.find(e=>e.code==f+"_low");r?r=w:s[e].series.push({...w})}}),s.table_data=t}if(this.current_alarm_row.rule_ranges){let t=[],a=[],r=[],s=[],i=[],n=[];if(this.current_alarm_row.rule_ranges.length>0){let e=[];this.current_alarm_row.rule_ranges.forEach(t=>{let a=0==e.length?void 0:e.find(e=>e.t==t.t);a||e.push(t)}),e.forEach((e,o)=>{0!=o&&o%this.interval!=0||(t.push([l()(Number(e.t)).format("HH:mm"),e.up3]),a.push([l()(Number(e.t)).format("HH:mm"),e.up2]),r.push([l()(Number(e.t)).format("HH:mm"),e.up1]),s.push([l()(Number(e.t)).format("HH:mm"),e.down1]),i.push([l()(Number(e.t)).format("HH:mm"),e.down2]),n.push([l()(Number(e.t)).format("HH:mm"),e.down3]))})}var d=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"up1",data:t,itemStyle:{color:"#d90f1e"},zlevel:1});1==o.length&&this[e].series.push(d);var h=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"up2",data:a,itemStyle:{color:"#f49e36"},zlevel:1});1==o.length&&this[e].series.push(h);var c=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"up3",data:r,itemStyle:{color:"#fffe3a"},zlevel:1});1==o.length&&this[e].series.push(c);var m=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"down1",data:s,itemStyle:{color:"#fffe3a"},zlevel:1});1==o.length&&this[e].series.push(m);var u=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"down2",data:i,itemStyle:{color:"#f49e36"},zlevel:1});1==o.length&&this[e].series.push(u);var p=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"down3",data:n,itemStyle:{color:"#d90f1e"},zlevel:1});1==o.length&&this[e].series.push(p)}if(this.current_alarm_row.confidence&&this.current_alarm_row.confidence.length>0){let t=[],a=[];this.current_alarm_row.confidence.forEach(e=>{t.push([l()(1e3*Number(e.t)).format("HH:mm"),e.up,`${e.down}~${e.up}`]),a.push([l()(1e3*Number(e.t)).format("HH:mm"),e.down,`${e.down}~${e.up}`])});let r=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"confidenceUp",data:t,areaStyle:{color:new i["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(213,72,120,0.6)"},{offset:.3,color:"rgba(213,72,120,0)"}])},itemStyle:{color:"rgba(213,72,120,1)"},zlevel:2}),s=Object.assign({type:"line",smooth:!0,symbol:"none"},{id:"confidenceDown",data:a,itemStyle:{color:"rgba(213,72,120,1)"},zlevel:2});this[e].series.push(r),this[e].series.push(s)}let f={};this[e].series.forEach((e,t)=>{f[e.name]=n.includes(e.name)}),"chart_option"==e?(this[e].tooltip.textStyle={color:"#fff"},this[e].xAxis.textStyle={color:"#fff"},this[e].xAxis.nameTextStyle={color:this.$route.query.bigScreenNew?"#ddd":"#666"},this[e].xAxis.axisLabel={color:this.$route.query.bigScreenNew?"#ddd":"#666"},this[e].tooltip.formatter=(e,t,a)=>{let r=e[0].axisValueLabel+"<br/>";return e.forEach(e=>{this.limitValue[e.seriesId]&&(e.seriesName=this.limitValue[e.seriesId]),"confidenceUp"==e.seriesId||"confidenceDown"==e.seriesId?r.indexOf("置信区间")<0&&(r+=`${e.marker} 置信区间 : ${e.value[2]||"--"}<br/>`):r+=`${e.marker} ${e.seriesName} : ${e.value[1]||"--"}<br/>`}),r},this.bindChartOptionToRow(t)):(this.myChart.setOption({...this[e]}),this.myChart.off("legendselectchanged"),this.myChart.on("legendselectchanged",t=>{let a=Object(r["a"])(t.selected);if(t.name==this.masterChartSeriesName){if(t.selected[this.masterChartSeriesName]){let t=this[e].series;this[e].series=[...t,...this.hideSeries]}else{let t=this[e].series;this.hideSeries=t.filter(e=>this.hideSeriesKey.includes(e.id));let a=t.filter(e=>!this.hideSeriesKey.includes(e.id));this[e].series=a}this[e].legend.selected=a,this.myChart.setOption({...this[e]},!0)}}),window.addEventListener("resize",()=>{this.myChart.resize()}))},renderChartDetails(e,t){this.alarmDatas=e;let a=this;a[t].yAxis=[],this.qxDatas=[],e.forEach(e=>{this.qxDatas=this.qxDatas.concat(e.datas||[])});let r=this.getChartyAxis(this.qxDatas);a.station_columns=[{title:"时间",data:"time"}];let s=[];this.qxDatas&&this.qxDatas.forEach((i,o)=>{var n;0==o&&(a[t].yAxis[o]=r,a[t].yAxis[o].name=i.sensor_unit);let d={type:"line",data:[],smooth:!0,symbol:"none",lineStyle:{width:"chart_options"==t?4:2},zlevel:10,connectNulls:!0},h=i.sensor;i.sensor_unit;d.code=i.sensor;let c=JSON.parse(this.alarmDatas[o].meta),m=(null===c||void 0===c||null===(n=c.hdres)||void 0===n||null===(n=n.entity)||void 0===n?void 0:n.name)||"";d.name=m+"_"+i.sensor_name,a.station_columns.push({title:d.name,data:"val_"+o});let u=1e3*Number(e[o].stime),p="";p=e[o].rtime&&l()(u).format("YYYY-MM-DD")==l()(Number(e[o].rtime)).format("YYYY-MM-DD")?1e3*Number(e[o].rtime):l()(u).format("YYYY-MM-DD")==l()(new Date).format("YYYY-MM-DD")?l()(new Date).valueOf():l()(l()(u).format("YYYY-MM-DD")+" 23:59:59").valueOf(),l()(u).format("YYYY-MM-DD")==l()(this.chart_date).format("YYYY-MM-DD")&&(d.markArea={itemStyle:{color:"#dedede"},data:[[{xAxis:l()(u).format("HH:mm")},{xAxis:l()(p).format("HH:mm")}]]}),i.vals&&i.vals.forEach((e,t)=>{let a=e.v;d.data.push([l()(Number(e.t)).format("HH:mm"),e.v]),0==o?s.push({time:l()(Number(e.t)).format("HH:mm"),val_0:a}):s[t]["val_"+o]=a});let f=a[t].series.find(e=>e.code==h);f?f=d:a[t].series.push(d)}),a.table_data=s;let i={};this[t].series.forEach((e,t)=>{i[e.name]=0==t||"平均值"==e.name}),this[t].legend.selected=i,"chart_options"==t&&(this.myChart.setOption({...this[t]}),window.addEventListener("resize",()=>{this.myChart.resize()}))},beforeQuery(e){return"2"===this.queryData.currentTab?e={index:1,size:30,conditions:[{Field:"alarmObject",Value:"2",Operate:"=",Relation:"and"},{Field:"status",Value:"1"}],order:[],objName:this.pointData.name||this.queryData.placeName}:"1"===this.queryData.currentTab&&(e={index:1,size:30,conditions:[{Field:"alarmObject",Value:"2",Operate:"=",Relation:"and"},{Field:"status",Value:"1",Operate:"!=",Relation:"and"}],order:[],objName:this.pointData.name||this.queryData.placeName}),e},async afterQuery(e){return this.isLoading=!0,this.setAlarmCount(e.total||0),e},rowClick(e){"place"==this.classType?this.$mapControls.getEntityIdPoi(e.id):e.place&&this.$mapControls.getEntityIdPoi(e.place)},handleSelectChange(e){},sortChange({column:e,prop:t,order:a}){let r=t;t.includes("$")&&(r=t.split("$")[0]),"ascending"===a?(this.order={},this.order.Type=1,this.order.Field=r):"descending"===a?(this.order={},this.order.Type=0,this.order.Field=r):this.order=null,this.$refs["crud"].Query()},handleExpandRow(e,t){this.expandRowKeys=t.map(e=>e.id),this.expandRowInfo=Object(r["a"])(t);let a=t.find(t=>t.id==e.id);a&&this.getDetails(e,!0)}},beforeDestroy(){}}}).call(this,a("1157"))}}]);