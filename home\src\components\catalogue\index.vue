<template>
  <div class="toc-wrapper">
    <h3 class="toc-heading">{{ title }}</h3>
    <el-scrollbar ref="scrollRef" style="height: calc(100% - 48px)" v-if="tocItems.length">
      <ul class="toc-list">
        <TocItem 
          v-for="(item, index) in tocItems" 
          :key="index" 
          :item="item" 
          :level="item.level"
          @scroll-to="handleScrollTo"
        />
      </ul>
    </el-scrollbar>
    <p class="toc-empty" v-else>没有找到标题内容</p>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick,defineEmits } from 'vue';
import TocItem from './TocItem.vue';
// 定义事件
const emit = defineEmits<{
  (e: 'scroll-to', top: number): void;
}>();
// 定义标题项接口
export interface HeadingItem {
  level: number;               // 标题级别 1-6
  depth: number;               // 标题级别 1-6
  nodeId: string;               // id
  content: string;             // 标题内容
  active: boolean;             // 是否为当前激活项
  element: HTMLElement;        // 对应的DOM元素
  children?: HeadingItem[];    // 子标题
}

// 接收的props
const props = defineProps<{
  htmlContent?: string;  
  container?: HTMLElement | string; // 容器元素或选择器
  title?: string;                   // 目录标题
  maxLevel?: number;                // 最大显示级别，默认6
  offsetTop?: number;               // 滚动偏移量，默认60px
  selector?: string;                // 标题选择器，默认'h1,h2,h3,h4,h5,h6'
}>();

// 获取 el-scrollbar 组件实例
const scrollRef = ref(null)

// 目录数据
const tocItems = ref<HeadingItem[]>([]);
// 所有标题的扁平数组，用于滚动检测
const allHeadings = ref<HeadingItem[]>([]);
// 当前有效的容器元素
const containerElement = ref<HTMLElement | null>(null);

// 获取容器元素
const getContainerElement = (): HTMLElement | null => {
  if (!props.container) {
    return document.documentElement; // 默认使用整个文档
  }
  
  if (typeof props.container === 'string') {
    return document.querySelector(props.container);
  }
  
  return props.container;
};
function generateUUID() {
  // 生成一个符合UUID v4标准的字符串
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    // 生成随机数
    const r = Math.random() * 16 | 0;
    // 根据当前字符是x还是y进行不同处理
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    // 转换为16进制字符串并返回
    return v.toString(16);
  });
}

// 处理HTML内容，提取标题
const extractHeadings = () => {
  const container = containerElement.value;
  if (!container) return [];
  
  // 获取容器内的所有标题标签
  const selector = props.selector || 'h1,h2,h3,h4,h5,h6';
  const headings = Array.from(
    container.querySelectorAll(selector)
  ) as HTMLHeadingElement[];
  
  // 过滤超过最大级别的标题
  const maxLevel = props.maxLevel || 6;
  const filteredHeadings = headings.filter(heading => {
    const level = parseInt(heading.tagName.replace('H', ''));
    return level >= 1 && level <= maxLevel;
  });
  
  // 构建目录结构
  const toc: HeadingItem[] = [];
  const stack: HeadingItem[] = []; // 用于跟踪当前层级路径
  const flatHeadings: HeadingItem[] = [];
  
  filteredHeadings.forEach(heading => {
    const level = parseInt(heading.tagName.replace('H', ''));
    const content = heading.textContent?.trim() || `标题 ${level}`;
    
    // 关键优化：修正层级计算逻辑
    // 1. 先弹出栈中所有级别 >= 当前级别的标题（找到正确的父级）
    while (stack.length > 0 && stack[stack.length - 1].level >= level) {
      stack.pop();
    }
    
    // 2. 计算当前层级：栈的长度 + 1（栈为空时就是顶层，depth=1）
    const depth = stack.length + 1;
    
    // 创建标题项（包含正确的depth）
    const headingItem: HeadingItem = {
      level,
      depth, // 修正后的层级：直接反映嵌套深度
      nodeId: generateUUID(),
      content,
      active: false,
      element: heading,
      children: [] // 显式初始化children数组
    };
    
    flatHeadings.push(headingItem);
    
    // 3. 添加到正确的父级
    if (stack.length === 0) {
      toc.push(headingItem); // 顶级标题
    } else {
      stack[stack.length - 1].children.push(headingItem); // 子标题
    }
    
    // 4. 将当前标题入栈，作为后续标题的潜在父级
    stack.push(headingItem);
  });
  
  allHeadings.value = flatHeadings;
  return toc;
};

// 滚动到指定元素
const handleScrollTo = (element: HTMLElement,nodeId:string) => {
  ;
  if (!containerElement.value || !element) return;
  
  const container = containerElement.value;
  const offsetTop = props.offsetTop || 24;
  
  // 计算目标位置（考虑容器是否可滚动）
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
   // 更新激活状态
   debugger;
  allHeadings.value.forEach((heading, index) => {
    heading.active = heading.nodeId === nodeId;
  });
  // 如果容器是视口本身，使用window滚动
  if (container === document.documentElement || container === document.body) {
    window.scrollTo({
      top: elementRect.top + window.pageYOffset - offsetTop,
      behavior: 'smooth'
    });
  } else {
    // 否则使用容器自身的滚动
    emit('scroll-to',element.offsetTop - offsetTop)
  }
};

// 处理滚动事件，更新激活状态
const handleScroll = () => {
  ;
  if (!containerElement.value || allHeadings.value.length === 0) return;
  
  const container = containerElement.value;
  const offsetTop = props.offsetTop || 0;
  
  // 获取容器的滚动位置和视口信息
  let scrollTop: number;
  let viewportHeight: number;
  
  if (container === document.documentElement || container === document.body) {
    scrollTop = window.scrollY;
    viewportHeight = window.innerHeight;
  } else {
    scrollTop = container.scrollTop;
    viewportHeight = container.clientHeight;
  }
  
  // 计算当前可见区域的起始位置
  const visibleStart = scrollTop + offsetTop;
  // 可见区域的结束位置（用于处理页面底部的标题）
  const visibleEnd = scrollTop + viewportHeight - offsetTop;
  
  let activeIndex = -1;
  
  // 找到当前可见的标题
  for (let i = 0; i < allHeadings.value.length; i++) {
    const heading = allHeadings.value[i];
    const rect = heading.element.getBoundingClientRect();
    
    // 元素在文档中的绝对顶部位置
    const elementTop = rect.top + (window.scrollY || document.documentElement.scrollTop);
    
    // 标题顶部在可见区域内，或者标题底部在可见区域内（处理长内容页面底部）
    if (elementTop <= visibleEnd && rect.bottom >= offsetTop) {
      activeIndex = i;
    } else if (elementTop > visibleEnd) {
      // 一旦找到不在可见区域内的标题，停止搜索
      break;
    }
  }
  
 
};

// 初始化容器并提取标题
const initialize = () => {
  containerElement.value = getContainerElement();
  debugger;
  if (containerElement.value) {
    // 提取标题
      tocItems.value = extractHeadings();
    
    

    ;
    
    // 添加滚动监听
    containerElement.value.addEventListener('scroll', handleScroll);
    // 如果容器是文档，还需要监听window滚动
    if (containerElement.value === document.documentElement) {
      window.addEventListener('scroll', handleScroll);
    }
    
    // 初始检查一次
    setTimeout(handleScroll, 100);
  }
};

// 清理事件监听
const cleanup = () => {
  if (containerElement.value) {
    containerElement.value.removeEventListener('scroll', handleScroll);
    if (containerElement.value === document.documentElement) {
      window.removeEventListener('scroll', handleScroll);
    }
  }
};

// 监听容器变化
watch(
  () => props.container,
  () => {
    cleanup();
    initialize();
  }
);

// 监听容器变化
watch(
  () => props.htmlContent,
  () => {
    tocItems.value = []
    cleanup();;
    nextTick(()=>{
      initialize();
    })
  }
);

// 监听其他可能影响标题的props变化
watch(
  [() => props.maxLevel, () => props.selector],
  () => {
    if (containerElement.value) {
      tocItems.value = extractHeadings();
      handleScroll();
    }
  }
);

// 组件挂载时初始化
onMounted(() => {
  // 等待DOM更新后再初始化
  nextTick(() => {
    initialize();
  });
});

// 组件卸载时清理
onUnmounted(cleanup);
</script>
<style lang="less">
.toc-list .top-item{
  border-left:1px solid#D8D8D8;
}
</style>
<style scoped lang="scss">
.toc-wrapper {
  /* position: sticky;
  top: 20px; */
  /* padding: 1.5rem; */
  padding:14px 24px;
  box-sizing: border-box;
  width:100%;
  height:100%;
  background-color: #fff;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.toc-heading {
  height: 24px;
  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
  letter-spacing: 0;
  margin-bottom:16px;
  /* border-bottom: 1px solid #e5e7eb; */
}

.toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
  :deep(.top-item){
    border-left:1px solid#D8D8D8;
  }
}

.toc-empty {
  color: #9ca3af;
  text-align: center;
  padding: 1rem;
  margin: 0;
}

/* 滚动条样式优化 */
.toc-wrapper::-webkit-scrollbar {
  width: 6px;
}

.toc-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.toc-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.toc-wrapper::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>