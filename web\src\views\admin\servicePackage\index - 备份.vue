<template>
    <div class="service-package" v-loading="loading">
        <div class="header">
            <div class="title-section">
                {{ crudTitle[params.type] }}
                <div class="header-tools">
                    <el-button class="header-tools-item" type="circle" size="mini" @click="handleCateManage">
                        分类管理
                    </el-button>
                    <el-divider direction="vertical" style="margin: 0 16px" />
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
                        <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                        刷新
                    </el-button>
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                        新增
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onEdit(null)">
                        <i class="action-iconfont icon-bianji"></i>
                        编辑
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null || currentRow?.status === 1" size="mini"
                        @click="onDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu"></i>
                        删除
                    </el-button>
                </div>
            </div>
            <div class="condition-section">
                <el-form :inline="true" :model="params">
                    <el-form-item label="类型">
                        <el-select v-model="params.type" placeholder="" style="width: 160px" @change="changeType">
                            <el-option label="模板" value="template" />
                            <!-- <el-option label="MCP服务管理" value="mcp" /> -->
                            <el-option label="场景" value="servicePackage" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.classify" placeholder="请选择分类" @change="selectClassifyChange" style="width: 160px" clearable>
                            <el-option label="全部分类" value="all" />
                            <el-option v-for="it in classify[params.type]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.status" placeholder="请选择状态" @change="selectStatusChange" style="width: 160px" clearable>
                            <el-option v-for="it in status" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="params.name" :placeholder="params.type == 'template' ? '搜索模板名称' : '搜索场景名称'" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="table-section">
            <div class="table-content" v-show="tabType == 'table'">
                <!-- stripe -->
                <el-table class="service-table" ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)"
                    @rowClick="handleRowClick">
                    <el-table-column type="index" label="序号"
                        :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                    <el-table-column v-for="it in tableColumns[params.type]" :key="it.data" :prop="it.data" :label="it.title" align="left" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'status'" #default="{ row }">
                            <el-tag type="info" round v-if="row.status === 0">
                                已下架
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.status === 1">
                                已上架
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'iconPath'" #default="{ row }">
                            <img v-if="row.iconPath" class="table-icon" :src="utils.require(row.iconPath)" alt="">
                        </template>
                        <template v-else-if="it.scoped == 'templateType'" #default="{ row }">
                            {{ classify[params.type].find(it => it.Value == row.templateType)?.Name || '' }}
                        </template>
                        <template v-else-if="it.scoped == 'content'" #default="{ row }">
                            {{ row[it.data] }}
                        </template>
                        <template v-else-if="it.scoped == 'price'" #default="{ row }">
                            {{ row[it.data] }}
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <div v-if="params.type === 'template'">
                                <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link>
                                <el-link type="primary" class="task-link" @click="changeStatus(row)">{{ row.status === 0 ? '上架' : '下架' }}</el-link>
                                <el-link type="danger" class="task-link" @click="onDelete(row)" :disabled="row.status === 1">删除</el-link>
                                <el-divider direction="vertical" style="margin-left: 12px;" />
                                <el-link type="primary" class="task-link" @click="onVersion(row)">版本管理</el-link>
                            </div>
                            <div v-else-if="params.type === 'servicePackage'">
                                <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link>
                                <el-link type="primary" class="task-link" @click="changeStatus(row)">{{ row.status === 0 ? '上架' : '下架' }}</el-link>
                                <el-link type="danger" class="task-link" :disabled="row.status === 1" @click="onDelete(row)">删除</el-link>
                            </div>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 50vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                        :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total" background @change="tableQuery(false)" />
                </div>
            </div>
        </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :title="isNew ? '创建服务包' : '编辑服务包'" width="960" style="border-radius: 4px;"
        @close="templateTableData = []"
        :destroy-on-close="true"
        top="10vh">
        <el-form class="service-package-form" :model="form" ref="ruleFormRef" label-width="100px" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="场景名称" :label-line="true">
                        <el-input v-model.trim="form.name" :maxlength="32" type="text" placeholder="输入场景名称" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <!-- class="form-item-flex-column"  上下结构 -->
                    <el-form-item prop="description" label="场景描述" :label-line="true">
                        <el-input v-model.trim="form.description" :rows="4" type="textarea" placeholder="输入场景功能描述" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="price" label="场景价格" :label-line="true">
                        <el-input v-model.trim="form.price" type="text" clearable placeholder="例如：¥1,999/月" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="packageType" label="场景分类" :label-line="true">
                        <el-select v-model="form.packageType" placeholder="请选择分类">
                            <el-option v-for="it in classify[params.type]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="场景图标" :label-line="true">
                        <div v-if="!form.iconPath">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onUploaded" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div class="upload-item">
                                    <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview" style="height: 96px;" v-else>
                            <div class="icon-preview-item">
                                <img :src="form.iconPath" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="form.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="table-selected">
            <div class="table-selected-talbe">
                <div class="table-selected-title">可选模板</div>
                <el-table class="service-table" ref="serviceTableRef" :data="templateTableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: calc(100% - 80px);margin-bottom: 12px;" :row-key="(row) => row.id"  @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" :reserve-selection="true" />
                    <el-table-column v-for="it in dialogTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="left" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'templateType'" #default="{ row }">
                            {{ classify[params.type].find(it => it.Value == row.templateType)?.Name || '' }}
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 23vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="servicePagination.currentPage" v-model:page-size="servicePagination.pageSize" style="justify-content: flex-end;"
                        :page-sizes="[15, 30, 60, 150]" layout="total, sizes, prev, pager, next, jumper"
                        :total="servicePagination.total" background @change="templateTableQuery" />
                </div>
            </div>
            <div class="table-selected-selected">
                <div class="table-selected-title">已选模板</div>
                <div class="table-selected-cards">
                    <div class="table-selected-card" v-for="(item, index) in form.taskTemplates" :key="item.id">
                        <div class="card-left">
                            <div class="card-left-title">{{ item.name }}</div>
                            <div>
                                <span class="card-left-text">{{ item.templateType }}</span>
                                <span class="card-left-text">{{ item.versionNumber }}</span>
                            </div>
                            <div class="card-left-text">{{ item.description }}</div>
                        </div>
                        <div class="card-right">
                            <el-button type="danger" @click="deleteTemplate(item, index)">移除</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="save()">
                    保存
                </el-button>
                <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 模板管理弹窗 -->
    <!-- 新增弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="addTemplateVisible" title="上传模板文件" width="520" style="border-radius: 4px;" :destroy-on-close="true" top="5vh">
        <el-form class="service-package-form service-package-template-form" :model="templateForm" ref="ruleFormRef" label-width="110px" :rules="templateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="filePath" label="选择模板文件" :label-line="true">
                        <el-upload
                            class="service-package-template-form-upload"
                            :show-file-list="false"
                            :headers="headers"
                            style="width:100%;height: 240px;"
                            @success="onTemplateUploadedFiles"
                            drag
                            :disabled="!!templateForm.filePath"
                            action="/wimai/api/task/upload"
                            accept="application/json"
                            :multiple="false"
                            :before-upload="beforeUpload"
                        >
                            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                                <div class="el-upload__tip">支持JSON格式，文件大小不超过10MB</div>
                            </div>
                        </el-upload>
                        <div class="el-upload__list" v-if="!!templateForm.filePath">
                            <div class="icon-preview-item">
                                <img src="@/assets/images/servicePackage/servicePackage.png" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="templateForm.filePath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="name" label="模板名称" :label-line="true">
                        <el-input v-model.trim="templateForm.name" :maxlength="20" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="versionNumber" label="模板版本" :label-line="true">
                        <el-input v-model.trim="templateForm.versionNumber" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="templateType" label="模板分类" :label-line="true">
                        <el-select v-model="templateForm.templateType" placeholder="请选择分类">
                            <el-option v-for="it in classify[params.type]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="模板图标" :label-line="true">
                        <div v-if="!templateForm.iconPath">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onTemplateUploaded" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div class="upload-item">
                                    <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview" v-else>
                            <div class="icon-preview-item">
                                <img :src="templateForm.iconPath" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="templateForm.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="模板描述" :label-line="true">
                        <el-input v-model.trim="templateForm.description" :rows="4" type="textarea" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="addTemplateVisible = false">取消</el-button>
                <el-button type="primary" @click="save()">
                    上传模板
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 编辑弹窗 -->
     <el-dialog class="servicePackage-dialog" v-model="editTemplateVisible" title="编辑模板基本信息" width="520" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <el-form class="service-package-form service-package-template-form" :model="templateForm" ref="ruleFormRef" label-width="100px" :rules="templateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="模板名称" :label-line="true">
                        <el-input v-model.trim="templateForm.name" :maxlength="20" type="text" clearable />
                        <span class="form-tip">模板的显示名称，用户在选择模板时看到的名称</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="templateType" label="模板分类" :label-line="true">
                        <el-select v-model="templateForm.templateType" placeholder="请选择分类">
                            <el-option v-for="it in classify[params.type]" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                        <span class="form-tip">选择模板所属的功能分类</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="模板图标" :label-line="true">
                        <div v-if="!templateForm.iconPath">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onTemplateUploaded" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div class="upload-item">
                                    <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview" v-else>
                            <div class="icon-preview-item">
                                <img :src="templateForm.iconPath" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="templateForm.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="模板描述" :label-line="true">
                        <el-input v-model.trim="templateForm.description" :rows="4" type="textarea" />
                        <span class="form-tip">详细描述模板的功能和使用场景</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editTemplateVisible = false">取消</el-button>
                <el-button type="primary" @click="save()">
                    保存
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 版本管理 -->
     <el-dialog class="servicePackage-dialog" v-model="templateVersionVisible" title="模板版本管理 - 数据分析报告生成" width="960" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <div class="version-dialog-box">
            <div class="dialog-box-title">
                <div class="dialog-box-title-text">
                    <div class="text-content">版本历史</div>
                    <div class="text-tip">（管理模板的不同版本，包括版本发布、回滚等操作）</div>
                </div>
                <div class="dialog-box-title-tools">
                    <el-button class="table-header-tools-item" @click="templateVersionQuery">
                        <i class="action-iconfont icon-shuaxinzhongzhi" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>刷新</span>
                    </el-button>
                    <el-button class="table-header-tools-item" @click="onVersionAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>新增</span>
                    </el-button>
                    <!-- && currentUser?.id !== current?.creatorId -->
                    <el-button class="table-header-tools-item" @click="onVersionEdit(null)" :disabled="currentVersionRow == null">
                        <i class="action-iconfont icon-bianji" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>编辑</span>
                    </el-button>
                    <el-button class="table-header-tools-item" :disabled="currentVersionRow == null || currentRow?.versionNumber === currentVersionRow?.versionNumber" @click="onVersionDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>删除</span>
                    </el-button>
                </div>
            </div>
            <div class="dialog-box-table" v-loading="versionLoading">
                <el-table class="service-table" ref="tableRef" :data="versionTableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: 100%" @rowClick="handleVersionRowClick">
                    <el-table-column label="序号" type="index" width="55" />
                    <el-table-column v-for="it in versionTableColumns" :key="it.data" :prop="it.data"  :label="it.title" align="left" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'versionNumber'" #default="{ row }">
                            <span>{{ row[it.data] }}</span>
                            <el-tag style="margin-left: 8px;" type="primary" round v-if="currentRow?.versionNumber === row.versionNumber">
                                当前版本
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'releaseStatus'" #default="{ row }">
                            <el-tag type="info" round v-if="row.releaseStatus === 0">
                                草稿
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.releaseStatus === 1">
                                稳定版本
                            </el-tag>
                            <el-tag type="danger" round v-else-if="row.releaseStatus === 2">
                                测试版本
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <el-link type="primary" class="task-link" @click="onVersionEdit(row)">编辑</el-link>
                            <el-link type="danger" class="task-link" @click="onVersionDelete(row)" :disabled="currentRow?.versionNumber === row.versionNumber">删除</el-link>
                            <el-divider direction="vertical" style="margin-left: 0;margin-right: 16px;" />
                            <el-link type="primary" :disabled="currentRow?.versionNumber === row.versionNumber || row.releaseStatus !== 1" class="task-link" @click="setCurrentVersion(row)">设为当前</el-link>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 20vh;" />
                    </template>
                </el-table>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="templateVersionVisible = false">取消</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 版本管理创建新版本 -->
    <el-dialog class="servicePackage-dialog" v-model="addVersionVisible" :title="isNew ? '创建新版本' : '编辑版本'" width="520" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <el-form class="service-package-form service-package-template-form" :model="versionForm" ref="ruleFormRef" label-width="110px" :rules="versionRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="versionNumber" label="版本号" :label-line="true">
                        <el-input v-model.trim="versionForm.versionNumber" type="text" clearable />
                        <span class="form-tip">建议使用语义化版本格式：主版本.次版本.修订版本</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="versionType" label="版本类型" :label-line="true">
                        <el-select v-model="versionForm.versionType" placeholder="请选择分类">
                            <el-option v-for="it in versionTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="版本描述" :label-line="true">
                        <el-input v-model.trim="versionForm.description" :rows="4" type="textarea" />
                        <span class="form-tip">详细说明此版本的新功能、改进和修复</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="filePath" label="模板文件" :label-line="true">
                        <el-upload
                            class="service-package-template-form-upload"
                            :show-file-list="false"
                            :headers="headers"
                            style="width:100%;"
                            @success="onVersionUploaded"
                            drag
                            :disabled="!!versionForm.filePath"
                            action="/wimai/api/task/upload"
                            accept="application/json"
                            :multiple="false"
                            :before-upload="beforeUpload"
                        >
                            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                                <div class="el-upload__tip">支持JSON格式，文件大小不超过10MB</div>
                            </div>
                        </el-upload>
                        <div class="el-upload__list" v-if="!!versionForm.filePath">
                            <div class="icon-preview-item">
                                <img src="@/assets/images/servicePackage/servicePackage.png" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="versionForm.filePath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="compatibilityType" label="兼容性说明" :label-line="true">
                        <el-select v-model="versionForm.compatibilityType" placeholder="请选择分类">
                            <el-option v-for="it in compatibilityTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="releaseStatus" label="发布状态" :label-line="true">
                        <el-select v-model="versionForm.releaseStatus" placeholder="请选择分类">
                            <el-option v-for="it in releaseStatus" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="addVersionVisible = false">取消</el-button>
                <el-button type="primary" @click="saveVersion">
                    {{ isNew ? '创建版本' : '保存版本' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理弹窗 -->
    <el-dialog class="servicePackage-dialog" :append-to-body="false" v-model="cateManageVisible" :title=" params.type==='template'? '模版分类管理' : '场景分类管理'" width="1042" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <div class="title-section cate-header-tools">
            分类列表
            <div class="header-tools">
                <el-button class="header-tools-item" type="circle" size="mini" @click="onResetCate">
                    <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                    刷新
                </el-button>
                <el-button class="header-tools-item" type="circle" size="mini" @click="onAddCate">
                    <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                    新增
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onEditCate(null)">
                    <i class="action-iconfont icon-bianji"></i>
                    编辑
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onDeleteCate(null)">
                    <i class="action-iconfont icon-huishouzhanshanchu"></i>
                    删除
                </el-button>
            </div>
        </div>
        <el-table class="cate-table" ref="cateTableRef" :data="cateTableData" border :show-overflow-tooltip="true"
            :highlight-current-row="true" style="width: 100%;height: 545px"
            @rowClick="handleCateRowClick">
            <el-table-column type="index" label="序号"
                :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                <el-table-column v-for="it in cateTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="left" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                <template v-if="it.scoped == 'status'" #default="{ row }">
                    <el-tag type="danger" round v-if="row.status === 'disable'">
                        禁用
                    </el-tag>
                    <el-tag type="success" round v-else-if="row.status === 'enable'">
                        启用
                    </el-tag>
                </template>
                <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                    <el-link type="primary" class="task-link" @click="onEditCate(row)">编辑</el-link>
                    <el-link type="danger" class="task-link" style="margin-left:16px;" @click="onDeleteCate(row)">删除</el-link>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" style="height: 50vh;" />
            </template>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cateManageVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理编辑弹窗 -->
     <el-dialog class="servicePackage-dialog" v-model="editCateVisible" :title="params.type==='template'? '编辑模版分类' : '编辑场景分类'" width="480" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <el-form class="service-package-form service-package-template-form" :model="cateForm" ref="cateFormRef" label-width="100px" :rules="templateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="名称" required :label-line="true">
                        <el-input v-model.trim="cateForm.name" :maxlength="32" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="分类描述" :label-line="true">
                        <el-input v-model="cateForm.description" :rows="4" type="textarea" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="status" label="状态" :label-line="true">
                        <!-- <el-switch :inactive-value="'disable'" :active-value="'enable'" v-model="cateForm.status" :rows="4" type="textarea" /> -->
                        <el-select  v-model="cateForm.status" >
                            <el-option
                                label="启用"
                                value="enable"
                            />
                            <el-option
                                label="停用"
                                value="disable"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editCateVisible = false">取消</el-button>
                <el-button type="primary" @click="saveCate()">
                    保存
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'

const route = useRoute()
const userStore = useUserStore()


const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
    "Authorization": Authorization.value,
    "FROM_CHANNEL": "web"
})
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const isNew = ref(true)
const servicePagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const form = ref({
    name: "",
    description: "",
    price: "",
    packageType: '',
    iconPath: '',
    taskTemplates: []
})
const dialogFormVisible = ref(false)
const crudTitle = ref({
    'template': '官方模板列表',
    'servicePackage': '官方服务包列表'
})
const classify = ref({
    'template': [
        { Name: '数据处理', Value: '0' },
        { Name: '办公自动化', Value: '1' },
        { Name: '文档处理', Value: '2' },
        { Name: '水务专用', Value: '3' },
        { Name: '设备管理', Value: '4' },
        { Name: '财务管理', Value: '5' },
        { Name: '其他', Value: 'other' },
    ],
    'servicePackage': [
        { Name: '行业专用', Value: '0' },
        { Name: '通用工具', Value: '1' },
        { Name: '数据分析', Value: '2' },
        { Name: '办公自动化', Value: '3' },
        { Name: '其他', Value: 'other' },
    ]
})
const status = ref([
    { Name: '全部状态', Value: 'all' },
    { Name: '已上架', Value: 1 },
    { Name: '已下架', Value: 0 }
])
const versionTypes = ref([
    { Name: '主版本(Minor) - 重大更新', Value: 0 },
    { Name: '次版本(Minor) - 新功能添加', Value: 1 },
    { Name: '修订版本(Minor) - 问题修复', Value: 2 }
])
const compatibilityTypes = ref([
    { Name: '向后兼容 - 与旧版本完全兼容', Value: 1 },
    { Name: '部分兼容 - 可能需要调整参数', Value: 2 },
    { Name: '不兼容 - 需要重新配置', Value: 0 }
])
const releaseStatus = ref([
    { Name: '草稿 - 暂不发布', Value: 0 },
    { Name: '稳定版本 - 立即发布', Value: 1 },
    { Name: '测试版本 - 仅限测试', Value: 2 }
])
const tabType = ref('table')
const params = ref({
    type: 'template',
    classify: 'all',
    status: 'all',
    name: ""
})
// 定义 tableData 中元素的类型
interface TableDataItem {
    id: string;
    iconPath: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscriptionCount?: string;
    downloadCount?: number;
    templateType?: string;
    status: any;
}
const tableData = ref<TableDataItem[]>([])
const tableColumns = ref({
    'template': [
        { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true },
        { data: 'name', title: '模板名称', minWidth: 200, orderable: true, filterable: true },
        { data: 'templateType', title: '分类', scoped: 'templateType', minWidth: 160, orderable: true, filterable: true },
        { data: 'versionNumber', title: '版本', minWidth: 160, orderable: true, filterable: true },
        { data: 'downloadCount', title: '下载量', minWidth: 160, orderable: true, filterable: true },
        { data: 'status', title: '上架状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
        { data: 'handle', title: '操作', scoped: 'handle', minWidth: 200, fixed: 'right' },
    ],
    'servicePackage': [
        { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true },
        { data: 'name', title: '场景名称', minWidth: 200, orderable: true, filterable: true },
        // { data: 'content', title: '包含内容', scoped: 'content', minWidth: 120, orderable: true, filterable: true },
        { data: 'price', title: '价格', scoped: 'price', minWidth: 160, orderable: true, filterable: true },
        { data: 'subscriptionCount', title: '订阅量', minWidth: 160, orderable: true, filterable: true },
        { data: 'status', title: '上架状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
        { data: 'handle', title: '操作', scoped: 'handle', width: 200, fixed: 'right' },
    ]
})
interface templateTableDataItem {
    id: string;
    version: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscription?: number;
    downloadNum?: number;
    classify: string;
}
const templateTableData = ref<templateTableDataItem[]>([])
const dialogTableColumns = ref([
    { data: 'name', title: '模板名称', orderable: true, filterable: true },
    { data: 'templateType', title: '分类', scoped: 'templateType', width: 100, orderable: true, filterable: true },
    { data: 'versionNumber', title: '版本', width: 90, orderable: true, filterable: true },
    { data: 'description', title: '描述', width: 160, orderable: true, filterable: true },
])
interface versionTableDataItem {
    id: string;
    name: string;
    versionNumber: string;
    downloadCount: number;
    status: any;
}
const versionTableData = ref<versionTableDataItem[]>([])
const versionTableColumns = ref([
    { data: 'versionNumber', title: '版本号', scoped: 'versionNumber', width: 150, orderable: true, filterable: true },
    { data: 'created', title: '发布日期', scoped: 'created', orderable: true, filterable: true },
    { data: 'description', title: '版本描述', width: 160, orderable: true, filterable: true },
    { data: 'releaseStatus', title: '状态', scoped: 'releaseStatus', width: 100, orderable: true, filterable: true },
    { data: 'downloadCount', title: '下载量', width: 80, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 210, orderable: true, filterable: true },
])

const ruleFormRef = ref<FormInstance>()


//ref 
const tableRef = ref<HTMLElement>()
const serviceTableRef = ref<HTMLElement>()

// 模板管理
const addTemplateVisible = ref(false)
const templateForm = ref({
    filePath: '',
    name: "",
    description: "",
    templateType: '',
    versionNumber: '',
    iconPath: ''
})
const editTemplateVisible = ref(false)

const versionLoading = ref(false)
const currentVersionRow = ref(null)
const templateVersionVisible = ref(false)
const addVersionVisible = ref(false)
const versionForm = ref({
    versionNumber: '',
    versionType: '',
    description: "",
    filePath: '',
    compatibilityType: '',
    releaseStatus: ''
})


//分类管理
const cateManageVisible = ref(false)
const cateTableColumns = ref([
    { data: 'name', title: '名称', minWidth: 200, orderable: true, filterable: true },
    { data: 'description', title: '描述', minWidth: 200, orderable: true, filterable: true },
    { data: 'number', title: '模板数量', minWidth: 80, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', minWidth: 200, fixed: 'right' }
])
const currentCateRow = ref(null)
const cateTableData = ref([])
const isCateNew = ref(true)
const cateForm = ref({
    name: '',
    description: "",
    status: "enable"
})
const editCateVisible = ref(false)

const cateFormRef = ref<FormInstance>()



//computed
// isUniwimPc
const isUniwimPc = computed(() => {
    return route.query.uniwim === 'pc'
})

const currentUser = computed(() => {
    return userStore.userInfo
})

//分类管理方法

const handleCateManage = () => {
   cateManageVisible.value = true;
   cateTableQuery();
}

const cateTableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    currentCateRow.value = null
    const query_params: any = {
        conditions: [],
        data: {
            type:params.value.type
        },
        
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            cateTableData.value = res.rows
        } else {
            cateTableData.value = []
        }
    })
    .catch((err: any) => {
        cateTableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}

const onResetCate = () => {
    isCateNew.value = true;
    currentCateRow.value = null
    cateTableData.value = []
    cateTableQuery()
}

const onAddCate = () => {
    isCateNew.value = true
    editCateVisible.value = true
    cateForm.value = {
        name: '',
        description: "",
        status: "enable",
    }
}
const onEditCate = async (row: any) => {
    if (row) currentCateRow.value = row
    isCateNew.value = false
    cateForm.value = JSON.parse(JSON.stringify(await formSet(currentCateRow.value)))
    editCateVisible.value = true
}
const onDeleteCate = async (row: any) => {
    if (row) currentCateRow.value = row
    saasApi.AIAgentResourceCategoryDelete([currentCateRow.value.id]).then((res: any) => {
        if (res.Code === 0) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            cateTableQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {},
        size:Infinity,
        index:1
        
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            let classify_map:any = {}
            res.rows.forEach((row:any)=>{
                if(!classify_map[row.type]){
                    classify_map[row.type] = []
                }
                classify_map[row.type].push({
                    Name:row.name,
                    Value:row.id
                })
            })
            classify.value = classify_map
        } else {
            classify.value = {
                "template":[],
                "servicePackage":[]
            }
        }
    })
    .catch((err: any) => {
        classify.value = {
            "template":[],
            "servicePackage":[]
        }
    })
    .finally(() => {
    })

}
//方法
const onUploaded = (response: any, file: string, fileList: any) => {
    form.value.iconPath = response?.Response
}
//方法
const onTemplateUploadedFiles = (response: any, file: string, fileList: any) => {
    templateForm.value.filePath = response?.Response
}
const onTemplateUploaded = (response: any, file: string, fileList: any) => {
    templateForm.value.iconPath = response?.Response
}
//方法
const onVersionUploaded = (response: any, file: string, fileList: any) => {
    versionForm.value.filePath = response?.Response
}
// 拖拽上传之前事件,只能json文件且不超过10M
interface UploadRawFile extends File {
  uid: number
  isDirectory?: boolean
}
const beforeUpload = (rawFile: UploadRawFile) => {
    if (rawFile.type !== 'application/json' || rawFile.size > 10 * 1024 * 1024) {
        return false
    }
}
// 验证版本号格式 xx.xx.xx 的正则表达式
const validateVersion = (rule: any, value: any, callback: any) => {
    const regex = /^\d{1,2}\.\d{1,2}\.\d{1,2}$/;
    if (!regex.test(value)) {
        callback(new Error('版本号格式不正确'))
    } else {
        callback()
    }
};

const rules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入服务包名称', trigger: 'change' }
    ],
    description: [
        { required: true, message: '请输入服务包描述', trigger: 'change' }
    ],
    price: [
        { required: true, message: '请输入服务包价格', trigger: 'change' }
    ],
    packageType: [
        { required: true, message: '请选择服务包分类', trigger: 'change' }
    ],
    iconPath: [
        { required: true, message: '请上传服务包图标', trigger: 'change' }
    ],
})
const templateRules = reactive<FormRules>({
    filePath: [
        { required: true, message: '请上传模板文件', trigger: 'change' }
    ],
    iconPath: [
        { required: true, message: '请上传模板图标', trigger: 'change' }
    ],
    name: [
        { required: true, message: '请输入模板名称', trigger: 'change' }
    ],
    versionNumber: [
        { required: true, message: '请输入版本号', trigger: 'change', },
        { validator: validateVersion, trigger: 'change' }
    ]
})
const versionRules = reactive<FormRules>({
    versionNumber: [
        { required: true, message: '请输入版本号', trigger: 'change', },
        { validator: validateVersion, trigger: 'change' }
    ],
    description: [
        { required: true, message: '请输入版本描述', trigger: 'change' }
    ],
    filePath: [
        { required: true, message: '请上传模板文件', trigger: 'change' }
    ],
})

const save = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    if (params.value.type === 'template') {
        if (isNew.value) {
            const update_params = {
                ...templateForm.value
            }
            tableInsert(update_params)
        } else {
            const update_params = {
                name: templateForm.value.name,
                description: templateForm.value.description,
                templateType: templateForm.value.templateType,
                iconPath: templateForm.value.iconPath,
            }
            tableUpdate(update_params)
        }
    } else {
        let update_params = {
            ...form.value
        }
        if (isNew.value) {
            tableInsert(update_params)
        } else {
            tableUpdate(update_params)
        }
    }
}
const saveCate = async () => {
    let isValidate = await cateFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    if (isCateNew.value) {
        const update_params = {
            ...cateForm.value,
            type:params.value.type
        }
        cateInsert(update_params)
    } else {
        const update_params = {
            id: currentCateRow.value.id,
            name: cateForm.value.name,
            description: cateForm.value.description,
            status: cateForm.value.status,
            type:params.value.type
        }
        cateUpdate(update_params)
    }
}
const saveVersion = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    const update_params = {
        ...versionForm.value,
        templateId: currentRow.value.id
    }
    if (!currentRow.value.id) {
        ElMessage({
            message: '未获取到模板id',
            type: 'error',
            showClose: true
        })
        return
    }
    if (isNew.value) {
        templateVersionInsert(update_params)
    } else {
        templateVersionUpdate(update_params)
    }
}

const handleRowClick = (row: any, show: boolean) => {
    currentRow.value = row
}
const handleVersionRowClick = (row: any, show: boolean) => {
    currentVersionRow.value = row
}
const handleCateRowClick = (row: any, show: boolean) => {
    currentCateRow.value = row
}


const handleSelectionChange = (val: any, row) => {
    form.value.taskTemplates = val
    console.log('选中', form.value);
}

const deleteTemplate = (row: any, index: number) => {
    form.value.taskTemplates.splice(index, 1)
    serviceTableRef.value?.toggleRowSelection(row, false)
}
const templateTableQuery = () => {
    const query_params: any = {
        conditions: [],
        data: {},
        index: servicePagination.value.currentPage,
        size: servicePagination.value.pageSize,
    }
    saasApi.AIAgentTaskTemplateQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            servicePagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            templateTableData.value = res.rows
            // 回显勾选状态
            const defaultChecked = JSON.parse(JSON.stringify(form.value.taskTemplates))
            defaultChecked.forEach(row => {
                serviceTableRef.value?.toggleRowSelection(row, true)
            })
        } else {
            templateTableData.value = []
        }
    }).finally(() => {
        
    })
}
const tableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    currentRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    if (params.value.type === 'template') {
        if (params.value.status !== 'all') {
            query_params.data.status = params.value.status
        }
        if (params.value.classify !== 'all') {
            query_params.data.templateType = params.value.classify
        }
        if (params.value.name) {
            query_params.data.name = params.value.name
        }
        saasApi.AIAgentTaskTemplateQuery(query_params).then((res: any) => {
            if (typeof res?.rows == 'object') {
                pagination.value = {
                    currentPage: res.current,
                    pageSize: res.size,
                    total: res.total
                }
                tableData.value = res.rows
            } else {
                tableData.value = []
            }
        })
        .catch((err: any) => {
            tableData.value = []
        })
        .finally(() => {
            if (!noloading) loading.value = false
        })
    } else if (params.value.type === 'servicePackage') {
        if (params.value.status !== 'all') {
            query_params.data.status = params.value.status
        }
        if (params.value.classify !== 'all') {
            query_params.data.packageType = params.value.classify
        }
        if (params.value.name) {
            query_params.data.name = params.value.name
        }
        saasApi.AIAgentServicePackageQuery(query_params).then((res: any) => {
            if (typeof res?.rows == 'object') {
                pagination.value = {
                    currentPage: res.current,
                    pageSize: res.size,
                    total: res.total
                }
                tableData.value = res.rows
            } else {
                tableData.value = []
            }
        })
        .catch((err: any) => {
            tableData.value = []
        })
        .finally(() => {
            if (!noloading) loading.value = false
        })
    }
}
const tableInsert = (insert_params: any) => {
    if (params.value.type === 'template') {
        saasApi.AIAgentTaskTemplateInsert(insert_params).then((res: any) => {
            if (res?.Success) {
                ElMessage({
                    message: '新增成功!',
                    type: 'success',
                    showClose: true
                })

                setTimeout(() => {
                    tableQuery()
                }, 200)
                dialogFormVisible.value = false
                addTemplateVisible.value = false
            } else {
                ElMessage({
                    message: '新增失败!',
                    type: 'error',
                    showClose: true
                })
            }
        }).finally(() => {
        })
    } else if (params.value.type === 'servicePackage') {
        saasApi.AIAgentServicePackageInsert(insert_params).then((res: any) => {
            if (res?.Success) {
                ElMessage({
                    message: '新增成功!',
                    type: 'success',
                    showClose: true
                })

                setTimeout(() => {
                    tableQuery()
                }, 200)
                dialogFormVisible.value = false
                addTemplateVisible.value = false
            } else {
                ElMessage({
                    message: '新增失败!',
                    type: 'error',
                    showClose: true
                })
            }
        }).finally(() => {
        })
    }
}
const tableUpdate = (update_params: any, noQuery?: boolean) => {
    let input_params = {
        ...currentRow.value,
        ...update_params
    }
    if (params.value.type === 'template') {
        saasApi.AIAgentTaskTemplateUpdate(input_params).then((res: any) => {
            if (res?.Code === 0) {
                ElMessage({
                    message: '编辑成功!',
                    type: 'success',
                    showClose: true
                })
                // 编辑不再跳转编排页面
                setTimeout(() => {
                    if (!noQuery) tableQuery()
                }, 200)
                dialogFormVisible.value = false
                editTemplateVisible.value = false
            } else {
                ElMessage({
                    message: '编辑失败!',
                    type: 'error',
                    showClose: true
                })
            }
        }).finally(() => {

        })
    } else if (params.value.type === 'servicePackage') {
        saasApi.AIAgentServicePackageUpdate(input_params).then((res: any) => {
            if (res?.Code === 0) {
                ElMessage({
                    message: '编辑成功!',
                    type: 'success',
                    showClose: true
                })
                // 编辑不再跳转编排页面
                setTimeout(() => {
                    if (!noQuery) tableQuery()
                }, 200)
                dialogFormVisible.value = false
                editTemplateVisible.value = false
            } else {
                ElMessage({
                    message: '编辑失败!',
                    type: 'error',
                    showClose: true
                })
            }
        }).finally(() => {

        })
    }
}

const templateVersionQuery = () => {
    versionLoading.value = true
    currentVersionRow.value = null
    const query_params: any = {
        conditions: [],
        data: {
            templateId: currentRow.value?.id
        },
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    saasApi.AIAgentTaskTemplateVersionQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            versionTableData.value = res.rows
        } else {
            versionTableData.value = []
        }
    }).finally(() => {
        versionLoading.value = false
    })
}
const templateVersionInsert = (insert_params: any) => {
    saasApi.AIAgentTaskTemplateVersionInsert(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                templateVersionQuery()
            }, 200)
            addVersionVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const templateVersionUpdate = (update_params: any) => {
    let input_params = {
        ...update_params
    }
    saasApi.AIAgentTaskTemplateVersionUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                templateVersionQuery()
            }, 200)
            if (currentVersionRow.value?.versionNumber === currentRow.value?.versionNumber) setCurrentVersion(input_params)
            addVersionVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}

const cateInsert = (insert_params: any) => {
    saasApi.AIAgentResourceCategoryAdd(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const cateUpdate = (update_params: any) => {
    let input_params = {
        ...update_params
    }
    saasApi.AIAgentResourceCategoryUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                cateTableQuery()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const changeType = () => {
    params.value.classify = 'all'
    params.value.status = 'all'
    params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}
changeType()

const onSubmit = () => {
    tableQuery()
}
const selectClassifyChange = (val: any) => {
    if (!val) {
        params.value.classify = 'all'
    }
    tableQuery()
}
const selectStatusChange = (val: any) => {
    if (!val && val !== 0) {
        params.value.status = 'all'
    }
    tableQuery()
}

const formatTime = (data: any, format: string = 'YYYY-MM-DD HH:mm') => {
    if (!data) return ''
    return moment(data).format(format)
}

//发布
const onPublish = (row: any) => {

}

//撤回
const onRevoke = (row: any) => {

}

const onReset = () => {
    params.value.classify = 'all'
    params.value.status = 'all'
    params.value.name = ''

    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isNew.value = true
    if (params.value.type === 'template') {
        templateForm.value = {
            filePath: '',
            name: "",
            description: "",
            templateType: '',
            versionNumber: '',
            iconPath: ''
        }
    } else {
        form.value = {
            name: "",
            describe: "",
            price: "",
            classify: '',
            icon: '',
            templates: []
        }
    }
    currentRow.value = null
    tableData.value = []
    tableQuery()
}
const formSet = async (model: any) => {
    // const form_params: any = {
    //     name: "",
    //     describe: "",
    //     price: "",
    //     classify: '',
    //     icon: [],
    //     templates: []
    // }
    let data = JSON.parse(JSON.stringify(model))
    // // 设置默认值
    // Object.keys(form_params).forEach((key) => {
    //     if (!data[key]) {
    //         data[key] = form_params[key]
    //     }
    // })

    return data
}
const onAdd = async () => {
    isNew.value = true
    if (params.value.type === 'template') {
        addTemplateVisible.value = true
        templateForm.value = {
            filePath: '',
            name: "",
            description: "",
            templateType: '',
            versionNumber: '',
            iconPath: ''
        }
    } else {
        form.value = JSON.parse(JSON.stringify(await formSet({
            name: "",
            description: "",
            price: "",
            packageType: '',
            iconPath: '',
            taskTemplates: []
        })))
        servicePagination.value = {
            currentPage: 1,
            pageSize: 30,
            total: 0
        }
        templateTableQuery()
        dialogFormVisible.value = true
        serviceTableRef.value?.clearSelection()
    }
}
const onEdit = async (row: any) => {
    if (row) currentRow.value = row
    isNew.value = false
    if (params.value.type === 'template') {
        editTemplateVisible.value = true
        templateForm.value = JSON.parse(JSON.stringify(currentRow.value))
    } else {
        form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
        servicePagination.value = {
            currentPage: 1,
            pageSize: 30,
            total: 0
        }
        templateTableQuery()
        dialogFormVisible.value = true
        serviceTableRef.value?.clearSelection()
    }
}
const changeStatus = (row: any) => {
    currentRow.value = row
    row.status = row.status === 0 ? 1 : 0
    const update_params = {
        status: row.status
    }
    tableUpdate(update_params)
}
const onDelete = (row: any) => {
    if (row) currentRow.value = row
    const text = params.value.type === 'template' ? '确认删除模板吗？' : '确认删除场景吗？'
    ElMessageBox.alert(text, '提示', {
        // autofocus: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: () => {
            if (params.value.type === 'template') {
                saasApi.AIAgentTaskTemplateDelete([currentRow.value.id]).then((res: any) => {
                    if (res.Code === 0) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        tableQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            } else {
                saasApi.AIAgentServicePackageDelete([currentRow.value.id]).then((res: any) => {
                    if (res.Code === 0 || res === true) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        tableQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            }
        },
    })
}
const onVersion = (row: any) => {
    if (row) currentRow.value = row
    templateVersionVisible.value = true;
    templateVersionQuery()
}

const onVersionAdd = () => {
    isNew.value = true
    addVersionVisible.value = true
    versionForm.value = {
        versionNumber: '',
        versionType: '',
        description: "",
        filePath: '',
        compatibilityType: '',
        releaseStatus: ''
    }
}
const onVersionEdit = async (row: any) => {
    if (row) currentVersionRow.value = row
    isNew.value = false
    versionForm.value = JSON.parse(JSON.stringify(await formSet(currentVersionRow.value)))
    addVersionVisible.value = true
}
const onVersionDelete = async (row: any) => {
    if (row) currentVersionRow.value = row
    saasApi.AIAgentTaskTemplateVersionDelete([currentVersionRow.value.id]).then((res: any) => {
        if (res.Code === 0) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            templateVersionQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}
const setCurrentVersion = async (row: any) => {
    if (!currentRow.value) {
        ElMessage({
            message: '未获取到模板id',
            type: 'error',
            showClose: true
        })
        return
    }
    const update_params = {
        versionNumber: row.versionNumber,
        filePath: row.filePath
    }
    currentRow.value = {
        ...currentRow.value,
        ...update_params
    }
    tableData.value.forEach(it => {
        if (it.id === currentRow.value.id) {
            it.versionNumber = row.versionNumber;
            it.filePath = row.filePath;
        }
    })
    tableUpdate(update_params, true)
}


onMounted(() => {
    getClassifyData();
    tableQuery()
})
</script>
<style scoped lang="scss">
:deep(.service-table) {
    .el-table__cell {
        border-bottom: 1px solid #EEEEEE !important;
    }
}
.service-package {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px;
    box-sizing: border-box;
    background: #e8ecf0 !important;
    display: flex;
    flex-direction: column;

    .header {
        background: #fff;
        width: 100%;
        height: 112px;
        box-sizing: border-box;
        overflow: hidden;

        

        .condition-section {
            padding: 8px 16px;
            box-sizing: border-box;
            // border-top: solid 1px #e8ecf0;
            display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 16px;
            }

            .tab-list {
                .tab-list-item {
                    width: 80px;
                    height: 32px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    border: 1px solid #E6E7E9;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &.left {
                        border-radius: 4px 0 0 4px;
                    }

                    &.right {
                        border-radius: 0 4px 4px 0;
                    }

                    &.active {
                        color: #FFFFFF;
                        background: #0054D9;
                        border-color: #0054D9;
                    }
                }
            }
        }
    }

    .table-section {
        flex: 1;
        background: #fff;
    }

    .table-content {
        height: 100%;

        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }

        .table-icon {
            width: 24px;
            height: 24px;
        }

        .table-content-pagination {
            height: 48px;
            padding: 0 12px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        ::v-deep(.el-scrollbar__view) {
            height: 100%;
        }
    }
   
    
}
.title-section {
    height: 64px;
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSansSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #222222;

    .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
    }
    }

    .header-tools {
    display: flex;
    align-items: center;

    .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
            margin-right: 4px;
            font-size: 14px;
        }

        span {
            margin-left: 6px;
            line-height: 17px;
        }

        &:hover {
            color: rgba(0, 84, 210, 0.8);
        }

        &:active {
            color: #0044A9;
        }

        &.is-disabled {
            color: #BCBFC3;
            cursor: not-allowed;
        }
    }
    }
    .cate-table{
        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }
    }
</style>