{"name": "libdoc", "source": "libdoc/libdoc.html", "scripts": {"start": "parcel", "build": "rm -rf .parcel-cache && parcel build --config .parcelrc && cp dist/libdoc.html ../robot/htmldata/libdoc", "pretty": "prettier  --write", "test": "jest"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "parcel": "^2.12.0", "parcel-transformer-plaintext": "^1.0.1", "prettier": "3.2.5", "ts-jest": "^29.1.2", "typescript": "^5.4.4"}, "dependencies": {"handlebars": "^4.7.8", "mark.js": "^8.11.1"}}