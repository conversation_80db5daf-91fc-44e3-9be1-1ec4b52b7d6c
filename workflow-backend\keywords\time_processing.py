import re
import time
from datetime import datetime
from datetime import timedelta
from dateutil.relativedelta import relativedelta


def convert_to_standard_format(time_input):
    """
    将各种时间格式转换为标准格式 yyyy-MM-dd HH:mm:ss
    
    Args:
        time_input: 输入的时间字符串或时间戳
    
    Returns:
        str: 标准格式的时间字符串 yyyy-MM-dd HH:mm:ss
    """
    
    # 如果输入是None或空字符串
    if not time_input:
        return None
    
    # 转换为字符串处理
    time_str = str(time_input).strip()
    
    # 常见的时间格式模式
    patterns = [
        # 标准格式
        ('%Y-%m-%d %H:%M:%S', r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'),
        ('%Y-%m-%d %H:%M', r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$'),
        ('%Y-%m-%d', r'^\d{4}-\d{2}-\d{2}$'),
        
        # 斜杠分隔格式
        ('%Y/%m/%d %H:%M:%S', r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}$'),
        ('%Y/%m/%d %H:%M', r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}$'),
        ('%Y/%m/%d', r'^\d{4}/\d{2}/\d{2}$'),
        ('%m/%d/%Y %H:%M:%S', r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}$'),
        ('%m/%d/%Y %H:%M', r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}$'),
        ('%m/%d/%Y', r'^\d{2}/\d{2}/\d{4}$'),
        
        # 点分隔格式
        ('%Y.%m.%d %H:%M:%S', r'^\d{4}\.\d{2}\.\d{2} \d{2}:\d{2}:\d{2}$'),
        ('%Y.%m.%d %H:%M', r'^\d{4}\.\d{2}\.\d{2} \d{2}:\d{2}$'),
        ('%Y.%m.%d', r'^\d{4}\.\d{2}\.\d{2}$'),
        
        # 中文格式
        ('%Y年%m月%d日 %H:%M:%S', r'^\d{4}年\d{1,2}月\d{1,2}日 \d{2}:\d{2}:\d{2}$'),
        ('%Y年%m月%d日 %H:%M', r'^\d{4}年\d{1,2}月\d{1,2}日 \d{2}:\d{2}$'),
        ('%Y年%m月%d日', r'^\d{4}年\d{1,2}月\d{1,2}日$'),
        
        # ISO格式
        ('%Y-%m-%dT%H:%M:%S', r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$'),
        ('%Y-%m-%dT%H:%M:%SZ', r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$'),
        
        # 紧凑格式
        ('%Y%m%d%H%M%S', r'^\d{14}$'),
        ('%Y%m%d%H%M', r'^\d{12}$'),
        ('%Y%m%d', r'^\d{8}$'),
        
        # 时间在前的格式
        ('%H:%M:%S %Y-%m-%d', r'^\d{2}:\d{2}:\d{2} \d{4}-\d{2}-\d{2}$'),
        ('%H:%M %Y-%m-%d', r'^\d{2}:\d{2} \d{4}-\d{2}-\d{2}$'),
    ]
    
    # 处理时间戳（秒级和毫秒级）
    if time_str.isdigit():
        timestamp = int(time_str)
        # 判断是秒级还是毫秒级时间戳
        if timestamp > 1000000000000:  # 毫秒级时间戳
            timestamp = timestamp / 1000
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, OSError):
            pass
    
    # 尝试匹配各种格式
    for fmt, pattern in patterns:
        if re.match(pattern, time_str):
            try:
                # 处理ISO格式中的Z
                if time_str.endswith('Z'):
                    time_str = time_str[:-1]
                
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except ValueError:
                continue
    
    # 如果都匹配不上，抛出异常
    raise ValueError(f"无法识别的时间格式: {time_input}")


def add_timev2(dt, duration, duration_unit):
    if duration_unit in ["months"]:
            duration = int(duration)
            if duration > 0:
                dt = dt + relativedelta(months=duration)

    if duration_unit in ["years"]:
        duration = int(duration)
        if duration > 0:
            dt = dt + relativedelta(years=duration)
    
    # 使用timedelta处理天、时、分、秒的增加
    if duration_unit in ["seconds"]:
        duration = float(duration)
        if duration > 0:
            dt = dt + timedelta(seconds=duration)

    if duration_unit in ["minutes"]:
        duration = float(duration)
        if duration > 0:
            dt = dt + timedelta(minutes=duration)

    if duration_unit in ["hours"]:
        duration = float(duration)
        if duration > 0:
            dt = dt + timedelta(hours=duration)

    if duration_unit in ["days"]:
        duration = float(duration)
        if duration > 0:
            dt = dt + timedelta(days=duration)
    return dt


def reduce_timev2(dt, duration, duration_unit):
        
    if duration_unit in ["months"]:
            duration = int(duration)
            if duration > 0:
                dt = dt - relativedelta(months=duration)

    if duration_unit in ["years"]:
        duration = int(duration)
        if duration > 0:
            dt = dt - relativedelta(years=duration)
    
    # 使用timedelta处理天、时、分、秒的增加
    if duration_unit in ["seconds"]:
        duration = float(duration)
        if duration > 0:
            dt = dt - timedelta(seconds=duration)

    if duration_unit in ["minutes"]:
        duration = float(duration)
        if duration > 0:
            dt = dt - timedelta(minutes=duration)

    if duration_unit in ["hours"]:
        duration = float(duration)
        if duration > 0:
            dt = dt - timedelta(hours=duration)

    if duration_unit in ["days"]:
        duration = float(duration)
        if duration > 0:
            dt = dt - timedelta(days=duration)
    return dt


def time_calculation(ori_time, method, duration, duration_unit):
    try:
        # 将时间转换为'%Y-%m-%d %H:%M:%S'
        time_str = convert_to_standard_format(ori_time)
        # 解析输入的时间字符串
        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        print("转换为字符串：", dt)
        if method == "add":
            dt = add_timev2(dt, duration, duration_unit)
        elif method == "decrease":
            dt = reduce_timev2(dt, duration, duration_unit)
        
        # 格式化时间字符串
        new_time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 计算星期几（中文）
        weekday_names = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        weekday_cn = weekday_names[dt.weekday()]
        timestamp = int(dt.timestamp())
        return new_time_str   #, weekday_cn, timestamp
    except ValueError as e:
        return f"时间格式错误: {e}"
    

def get_weekday(time_str):
    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    weekday_names = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
    weekday = weekday_names[dt.weekday()]
    return weekday


def get_timestamp(time_str):
    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    timestamp = int(dt.timestamp())
    return timestamp