from datetime import datetime
from time import time
from typing import Optional


def str_to_datetime(time_str: str, fmt: Optional[str] = None) -> datetime:
    """
    将时间字符串转换为datetime对象

    参数:
        time_str: 待转换的时间字符串
        fmt: 时间字符串的格式，为None时自动尝试常见格式

    返回:
        datetime对象

    异常:
        ValueError: 无法解析时间字符串时抛出
    """
    # 如果指定了格式，直接使用该格式解析
    if fmt:
        return datetime.strptime(time_str, fmt)

    # 常见的时间格式列表，可根据需要扩展
    common_formats = [
        # 日期格式
        "%Y-%m-%d",
        "%Y/%m/%d",
        "%d-%m-%Y",
        "%d/%m/%Y",
        # 日期时间格式（带横线/斜线）
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d %H:%M",
        # 日期时间格式（带T）
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%S.%f",
        "%Y-%m-%dT%H:%M",
        # 带时区的格式
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f%z",
    ]

    # 尝试各种常见格式
    for fmt in common_formats:
        try:
            return datetime.strptime(time_str, fmt)
        except ValueError:
            continue

    # 如果所有格式都无法解析，抛出异常
    raise ValueError(f"无法解析时间字符串: {time_str}，请指定格式")


def hms_str_to_time(hms_str: str, fmt: Optional[str] = None) -> time:
    """
    将仅含时分秒的时间字符串转换为datetime.time对象

    参数:
        hms_str: 时分秒时间字符串（如"14:30:00"、"09:15"）
        fmt: 时间格式字符串，为None时自动识别常见格式

    返回:
        datetime.time对象

    异常:
        ValueError: 无法解析时间字符串时抛出
    """
    # 常见的时分秒格式列表
    common_formats = [
        "%H:%M:%S",  # 时:分:秒（如14:30:00）
        "%H:%M:%S.%f",  # 带微秒（如14:30:00.123456）
        "%H:%M",  # 时:分（如09:15）
        "%I:%M:%S%p",  # 12小时制带AM/PM（如02:30:00PM）
        "%I:%M%p",  # 12小时制简化（如09:15AM）
    ]

    # 尝试解析时间字符串
    if fmt:
        # 使用指定格式解析
        from datetime import datetime

        try:
            return datetime.strptime(hms_str, fmt).time()
        except ValueError:
            raise ValueError(f"无法用格式 {fmt} 解析时间字符串: {hms_str}")
    else:
        # 自动尝试常见格式
        from datetime import datetime

        for fmt_candidate in common_formats:
            try:
                return datetime.strptime(hms_str, fmt_candidate).time()
            except ValueError:
                continue

        # 所有格式尝试失败
        raise ValueError(f"无法识别时分秒格式: {hms_str}")
