<?xml version="1.0" encoding="UTF-8"?>
<keywordspec name="DataTypesLibrary" type="LIBRARY" format="ROBOT" scope="TEST" generated="2023-12-08T12:58:59+00:00" specversion="6" source="/home/<USER>/Devel/robotframework/atest/testdata/libdoc/DataTypesLibrary.py" lineno="88">
<version/>
<doc>This Library has Data Types.

It has some in ``__init__`` and others in the `Keywords`.

The DataTypes are the following that should be linked.
`HttpCredentials` , `GeoLocation` , `Small` and `AssertionOperator`.</doc>
<tags>
</tags>
<inits>
<init name="__init__" lineno="97">
<arguments repr="credentials: Small = one">
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="credentials: Small = one">
<name>credentials</name>
<type name="Small" typedoc="Small"/>
<default>one</default>
</arg>
</arguments>
<doc>This is the init Docs.

It links to `Set Location` keyword and to `GeoLocation` data type.</doc>
<shortdoc>This is the init Docs.</shortdoc>
</init>
</inits>
<keywords>
<kw name="Assert Something" lineno="107">
<arguments repr="value, operator: AssertionOperator | None = None, exp: str = something?">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="value">
<name>value</name>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="operator: AssertionOperator | None = None">
<name>operator</name>
<type name="Union" union="true">
<type name="AssertionOperator" typedoc="AssertionOperator"/>
<type name="None" typedoc="None"/>
</type>
<default>None</default>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="exp: str = something?">
<name>exp</name>
<type name="str" typedoc="string"/>
<default>something?</default>
</arg>
</arguments>
<doc>This links to `AssertionOperator` .

This is the next Line that links to `Set Location` .</doc>
<shortdoc>This links to `AssertionOperator` .</shortdoc>
</kw>
<kw name="Custom" lineno="134">
<arguments repr="arg: CustomType, arg2: CustomType2, arg3: CustomType, arg4: Unknown">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg: CustomType">
<name>arg</name>
<type name="CustomType" typedoc="CustomType"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg2: CustomType2">
<name>arg2</name>
<type name="CustomType2" typedoc="CustomType2"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg3: CustomType">
<name>arg3</name>
<type name="CustomType" typedoc="CustomType"/>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg4: Unknown">
<name>arg4</name>
<type name="Unknown"/>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
<kw name="Funny Unions" lineno="114">
<arguments repr="funny: bool | int | float | str | AssertionOperator | Small | GeoLocation | None = equal">
<arg kind="POSITIONAL_OR_NAMED" required="false" repr="funny: bool | int | float | str | AssertionOperator | Small | GeoLocation | None = equal">
<name>funny</name>
<type name="Union" union="true">
<type name="bool" typedoc="boolean"/>
<type name="int" typedoc="integer"/>
<type name="float" typedoc="float"/>
<type name="str" typedoc="string"/>
<type name="AssertionOperator" typedoc="AssertionOperator"/>
<type name="Small" typedoc="Small"/>
<type name="GeoLocation" typedoc="GeoLocation"/>
<type name="None" typedoc="None"/>
</type>
<default>equal</default>
</arg>
</arguments>
<returntype name="Union" union="true">
<type name="int" typedoc="integer"/>
<type name="List" typedoc="list">
<type name="int" typedoc="integer"/>
</type>
</returntype>
<doc/>
<shortdoc/>
</kw>
<kw name="Set Location" lineno="104">
<arguments repr="location: GeoLocation">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="location: GeoLocation">
<name>location</name>
<type name="GeoLocation" typedoc="GeoLocation"/>
</arg>
</arguments>
<returntype name="bool" typedoc="boolean"/>
<doc/>
<shortdoc/>
</kw>
<kw name="Typing Types" lineno="128">
<arguments repr="list_of_str: List[str], dict_str_int: Dict[str, int], whatever: Any, *args: List[Any]">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="list_of_str: List[str]">
<name>list_of_str</name>
<type name="List" typedoc="list">
<type name="str" typedoc="string"/>
</type>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="dict_str_int: Dict[str, int]">
<name>dict_str_int</name>
<type name="Dict" typedoc="dictionary">
<type name="str" typedoc="string"/>
<type name="int" typedoc="integer"/>
</type>
</arg>
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="whatever: Any">
<name>whatever</name>
<type name="Any" typedoc="Any"/>
</arg>
<arg kind="VAR_POSITIONAL" required="false" repr="*args: List[Any]">
<name>args</name>
<type name="List" typedoc="list">
<type name="Any" typedoc="Any"/>
</type>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
<kw name="X Literal" lineno="131">
<arguments repr="arg: Literal[1, 'xxx', b'yyy', True, None, one]">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg: Literal[1, 'xxx', b'yyy', True, None, one]">
<name>arg</name>
<type name="Literal" typedoc="Literal">
<type name="1"/>
<type name="'xxx'"/>
<type name="b'yyy'"/>
<type name="True"/>
<type name="None"/>
<type name="one"/>
</type>
</arg>
</arguments>
<doc/>
<shortdoc/>
</kw>
</keywords>
<typedocs>
<type name="Any" type="Standard">
<doc>Any value is accepted. No conversion is done.
</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>Typing Types</usage>
</usages>
</type>
<type name="AssertionOperator" type="Enum">
<doc>This is some Doc

This has was defined by assigning to __doc__.</doc>
<accepts>
<type>string</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
</usages>
<members>
<member name="equal" value="=="/>
<member name="==" value="=="/>
<member name="&lt;" value="&lt;"/>
<member name="&gt;" value="&gt;"/>
<member name="&lt;=" value="&lt;="/>
<member name="&gt;=" value="&gt;="/>
</members>
</type>
<type name="boolean" type="Standard">
<doc>Strings ``TRUE``, ``YES``, ``ON`` and ``1`` are converted to Boolean ``True``,
the empty string as well as strings ``FALSE``, ``NO``, ``OFF`` and ``0``
are converted to Boolean ``False``, and the string ``NONE`` is converted
to the Python ``None`` object. Other strings and other accepted values are
passed as-is, allowing keywords to handle them specially if
needed. All string comparisons are case-insensitive.

Examples: ``TRUE`` (converted to ``True``), ``off`` (converted to ``False``),
``example`` (used as-is)
</doc>
<accepts>
<type>string</type>
<type>integer</type>
<type>float</type>
<type>None</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Set Location</usage>
</usages>
</type>
<type name="CustomType" type="Custom">
<doc>Converter method doc is used when defined.</doc>
<accepts>
<type>string</type>
<type>integer</type>
</accepts>
<usages>
<usage>Custom</usage>
</usages>
</type>
<type name="CustomType2" type="Custom">
<doc>Class doc is used when converter method has no doc.</doc>
<accepts>
</accepts>
<usages>
<usage>Custom</usage>
</usages>
</type>
<type name="dictionary" type="Standard">
<doc>Strings must be Python [https://docs.python.org/library/stdtypes.html#dict|dictionary]
literals. They are converted to actual dictionaries using the
[https://docs.python.org/library/ast.html#ast.literal_eval|ast.literal_eval]
function. They can contain any values ``ast.literal_eval`` supports, including
dictionaries and other containers.

If the type has nested types like ``dict[str, int]``, items are converted
to those types automatically. This in new in Robot Framework 6.0.

Examples: ``{'a': 1, 'b': 2}``, ``{'key': 1, 'nested': {'key': 2}}``
</doc>
<accepts>
<type>string</type>
<type>Mapping</type>
</accepts>
<usages>
<usage>Typing Types</usage>
</usages>
</type>
<type name="float" type="Standard">
<doc>Conversion is done using Python's
[https://docs.python.org/library/functions.html#float|float] built-in function.

Starting from RF 4.1, spaces and underscores can be used as visual separators
for digit grouping purposes.

Examples: ``3.14``, ``2.9979e8``, ``10 000.000 01``
</doc>
<accepts>
<type>string</type>
<type>Real</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
</usages>
</type>
<type name="GeoLocation" type="TypedDict">
<doc>Defines the geolocation.

- ``latitude`` Latitude between -90 and 90.
- ``longitude`` Longitude between -180 and 180.
- ``accuracy`` *Optional* Non-negative accuracy value. Defaults to 0.

Example usage: ``{'latitude': 59.95, 'longitude': 30.31667}``</doc>
<accepts>
<type>string</type>
<type>Mapping</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Set Location</usage>
</usages>
<items>
<item key="longitude" type="float" required="true"/>
<item key="latitude" type="float" required="true"/>
<item key="accuracy" type="float" required="false"/>
</items>
</type>
<type name="integer" type="Standard">
<doc>Conversion is done using Python's [https://docs.python.org/library/functions.html#int|int]
built-in function. Floating point
numbers are accepted only if they can be represented as integers exactly.
For example, ``1.0`` is accepted and ``1.1`` is not.

Starting from RF 4.1, it is possible to use hexadecimal, octal and binary
numbers by prefixing values with ``0x``, ``0o`` and ``0b``, respectively.

Starting from RF 4.1, spaces and underscores can be used as visual separators
for digit grouping purposes.

Examples: ``42``, ``-1``, ``0b1010``, ``10 000 000``, ``0xBAD_C0FFEE``
</doc>
<accepts>
<type>string</type>
<type>float</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
<type name="list" type="Standard">
<doc>Strings must be Python [https://docs.python.org/library/stdtypes.html#list|list]
literals. They are converted to actual lists using the
[https://docs.python.org/library/ast.html#ast.literal_eval|ast.literal_eval]
function. They can contain any values ``ast.literal_eval`` supports, including
lists and other containers.

If the type has nested types like ``list[int]``, items are converted
to those types automatically. This in new in Robot Framework 6.0.

Examples: ``['one', 'two']``, ``[('one', 1), ('two', 2)]``
</doc>
<accepts>
<type>string</type>
<type>Sequence</type>
</accepts>
<usages>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
<type name="Literal" type="Standard">
<doc>Only specified values are accepted. Values can be strings,
integers, bytes, Booleans, enums and None, and used arguments
are converted using the value type specific conversion logic.

Strings are case, space, underscore and hyphen insensitive,
but exact matches have precedence over normalized matches.
</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>X Literal</usage>
</usages>
</type>
<type name="None" type="Standard">
<doc>String ``NONE`` (case-insensitive) is converted to Python ``None`` object.
Other values cause an error.
</doc>
<accepts>
<type>string</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
</usages>
</type>
<type name="Small" type="Enum">
<doc>This is the Documentation.

This was defined within the class definition.</doc>
<accepts>
<type>string</type>
<type>integer</type>
</accepts>
<usages>
<usage>__init__</usage>
<usage>Funny Unions</usage>
</usages>
<members>
<member name="one" value="1"/>
<member name="two" value="2"/>
<member name="three" value="3"/>
<member name="four" value="4"/>
</members>
</type>
<type name="string" type="Standard">
<doc>All arguments are converted to Unicode strings.</doc>
<accepts>
<type>Any</type>
</accepts>
<usages>
<usage>Assert Something</usage>
<usage>Funny Unions</usage>
<usage>Typing Types</usage>
</usages>
</type>
</typedocs>
</keywordspec>
