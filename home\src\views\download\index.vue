<template>
  <div class="download-page">
      <div class="head-part">
        <div class="title-item">下载客户端</div>
        <!-- <div class="describe-item">
          同时支持 Windows，macOS，IOS，iPadOS，Android，微信小程序版本
        </div> -->
        <div class="agreement-part">
          <span>开发者：浙江和达科技股份有限公司</span>
          <span class="agreement-item" @click="openPage('userAgreement')">用户协议</span>
          <span class="agreement-item" @click="openPage('privacyPolicy')">隐私政策</span>
        </div>
        <div class="content-part">
            <div class="content-item windows" @click="download('windows', '下载', '下载Windows')">
            <div class="img-item">
                <img src="../../assets/images/homepage/windows.png" alt="" />
            </div>
            <div class="text-box">
                <div class="text-item">Windows 桌面客户端</div>
                <div class="describe-item">支持 Win10及其以上版本</div>
            </div>
            <div class="down-img">
                <img src="../../assets/images/homepage/down.png" alt="" />
                <div class="down-text-item">点击下载</div>
            </div>
            </div>
            <div class="content-item">
                <div class="img-item"><img src="../../assets/images/homepage/mac.png" alt="" /></div>
                <div class="text-box">
                    <div class="text-item">macOS 桌面客户端</div>
                    <div class="describe-item">即将推出</div>
                </div>
                <!-- <div class="down-img">
                                <img src="../../assets/images/downloadPage/down.png" alt="" />
                                <div class="down-text-item">点击下载</div>
                            </div> -->
            </div>
        </div>
        <img src="@/assets/images/homepage/banner-text.png" style="margin-bottom:66px;" width="900" height="100" alt="" class="text-img">
        <img src="@/assets/images/homepage/download-img.png" style="margin-bottom:90px;" width="1156" height="563" alt="" class="text-img">
        <!-- <div class="history-button">
            查看所有更新日志
        </div> -->
      </div>
      
        <!-- 底部信息模块 -->
        <footer class="footer-section">
            <div class="container">
                <!-- <div class="footer-main">
                    <div class="footer-left">
                        <div class="footer-left-title">联系我们</div>
                        <p class="address"><i></i>浙江省嘉兴市昌盛南路36号嘉兴智慧产业创新园18幢</p>
                        <p class="tel"><i></i>电话：0573-82697301 / 0573-82229997</p>
                        <p class="email"><i></i>邮箱：<EMAIL> / <EMAIL></p>
                    </div>
                    <div class="footer-middle">
                        <p>一诺数字助理</p>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/windows/cbc388ff4ba64f08b7a800944980b0d6/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.exe"
                            class="download-btn"><i class="windows-icon"></i>Windows下载</a>
                        <a href="https://www.dlmeasure.com/uniwim/package/history/mac/a5d397b86082458dab8b791df40ad346/%E4%B8%80%E8%AF%BA%E6%95%B0%E5%AD%97%E5%8A%A9%E7%90%86.dmg"
                            class="download-btn"><i class="ios-icon"></i>Mac下载</a>
                    </div>
                    <div class="footer-right">
                        <div class="qrcode-box">
                            <p>一诺APP</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/yinuo.png" alt="一诺APP">
                            </div>
                        </div>
                        <div class="qrcode-box">
                            <p>度量公众号</p>
                            <div class="qrcode">
                                <img src="../../assets/images/homepage/dl.png" alt="度量公众号">
                            </div>
                        </div>
                    </div>
                </div> -->
                <p class="copyright">Copyright © 2025 浙江和达科技股份有限公司 <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备14035819号-7</a></p>
            </div>

        </footer>

      

  </div>
</template>

<script setup lang="ts">
import utils from '@/utils/utils'
import saasApi from '@/api/index'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const openPage = (type: string) => {
  if (type == 'userAgreement') {
    // 用户协议
    let params = {
      Name: '用户协议',
      Val: 'https://edu.dlmeasure.com/water-app/yhlo/privacy-agreement.html?loginIsRequired=false',
      target: '_blank',
    }
    var message = '*#hd#*' + JSON.stringify({ action: 'OPEN_TAG', params })
    // window.top.postMessage(message, '*')
    window.open(params.Val)
    utils.saveLogInfo({
      source: 'web',
      type: 2,
      applicationId: '',
      applicationName: '下载', //应用名称
      functionId: '',
      functionName: '[下载]查看用户协议', //功能名称
      param: '',
      path: 'https://edu.dlmeasure.com/water-app/yhlo/privacy-agreement.html?loginIsRequired=false', //路径
      result: 1, //访问结果
    })
  } else if (type == 'privacyPolicy') {
    // 隐私政策
    let params = {
      Name: '隐私政策',
      Val: 'https://edu.dlmeasure.com/water-app/yhlo/privacy-policy.html?loginIsRequired=false',
      target: '_blank',
    }
    var message = '*#hd#*' + JSON.stringify({ action: 'OPEN_TAG', params })
    // window.top.postMessage(message, '*')
    window.open(params.Val)
    utils.saveLogInfo({
      source: 'web',
      type: 2,
      applicationId: '',
      applicationName: '下载', //应用名称
      functionId: '',
      functionName: '[下载]查看隐私政策', //功能名称
      param: '',
      path: 'https://edu.dlmeasure.com/water-app/yhlo/privacy-policy.html?loginIsRequired=false', //路径
      result: 1, //访问结果
    })
  }
}
// 下载
const download = (type: string, applicationName: string, functionName: string) => {
  utils.downloadYN(type, applicationName, functionName)
}
</script>

<style scoped lang="scss">
.download-page {
    padding-top:58px;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow: auto;
    // display: flex;
    justify-content: center;
    // background-image: linear-gradient(180deg, #e2f0ff 0%, #f8faff 100%);
    background-image: linear-gradient(180deg, #FAFCFF 0%, #F2F6FF 70%, #FFFFFF 91%);
    
    .head-part {
        background: url('../../assets/images/homepage/comment-bg.png')  no-repeat top left;
        background-size: 100% 1080px;
        // width: 1540px;
        padding-top:100px;
        width:100%;
        // height: 1080px;
        // margin-top: 26px;
        // margin-bottom: 100px;
        text-align: center;
        box-sizing: border-box;
        .title-item {
            height: 71px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 48px;
            color: #000000;
            text-align: center;
        }
        .describe-item {
            font-weight: 400;
            font-size: 18px;
            color: #222222;
            text-align: center;
            margin-bottom: 10px;
        }
        .agreement-part {
            margin-top:50px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #999999;
            text-align: center;
            span {
                margin-right: 10px;
            }
            .agreement-item {
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                color: #0054D2;
                text-align: center;
                cursor:pointer;
            }
            &:last-child {
                margin-right: 0px;
            }
        }
    }

    .content-part {
        display: flex;
        justify-content: center;
        .content-item {
            width: 380px;
            height: 160px;
            background: #FFFFFF;
            display:flex;
            gap:25px;
            margin-right:80px;
            margin-top:80px;
            box-shadow: 0 2px 8px 0 #00163714;
            border-radius: 16px;
            padding: 40px 55px;
            box-sizing: border-box;
            cursor: pointer;
            text-align: center;
            position: relative;
            margin-bottom: 100px;
            &:last-of-type{
                margin-right:0;
            }
            .img-item {
                img {
                    width: 80px;
                    height: 80px;
                    margin: auto;
                }
            }
            .text-box{
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            .text-item {
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 16px;
                color: #222222;
                margin-bottom:16px;;
                margin-top:16px;
            }
            .describe-item {
                font-weight: 400;
                font-size: 14px;
                color: #999999;
            }
            .down-img {
                width:380px;
                height:160px;
                position: absolute;
                top: 0;
                left: 0;
                right:0;
                bottom:0;
                background: #ffffff;
                box-shadow: 0 2px 8px 0 rgba(0, 22, 55, 0.12);
                border-radius: 16px;
                align-items: center;
                padding: 30px 111px;
                box-sizing: border-box;
                display: none;
                img {
                    width: 64px;
                    height: 64px;
                }
                .down-text-item {
                    margin-top: 20px;
                    width: 64px;
                    font-weight: 400;
                    font-size: 16px;
                    color: #222222;
                    text-align: center;
                }
            }
            .down-text-item {
                display: none;
                width: 64px;
                font-weight: 400;
                font-size: 16px;
                color: #222222;
                text-align: center;
            }
            .app-img-item {
                display: none;
            }
            &.windows:hover {
                // padding: 43px 64px;
                box-shadow: 0 2px 8px 0 rgba(0, 22, 55, 0.3);
                .down-img,
                .down-text-item,
                .app-img-item {
                    display: inline-block;
                }
                .down-img{
                    img{
                        display: block;
                        margin: 0 auto;
                    }
                }
                .text-item,
                .describe-item,
                .app-default-img {
                    display: none;
                }
                .img-item {
                    // width: 160px;
                    // height: 160px;
                    margin-bottom: 10px;
                    img {
                        // width: 160px;
                        // height: 160px;
                        margin: auto;
                    }
                }
            }
        }
        &:last-child {
            margin-right: 0px;
        }
    }
    
    .history-button{
        margin:0 auto;
        cursor: pointer;
        margin-bottom:100px;
        width: 180px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        background: #FFFFFF;
        border: 1px solid #0054D2;
        border-radius: 4px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #0054D2;
        text-align: center;
    }
}
// 底部样式
.footer-section {
    background-color: #f5f5f5;
    // padding: 20px 0;
    width: 100%;
    min-width: 1400px;
    .container {
        width: 1400px;
        margin: 0 auto;
        // display: flex;
        justify-content: space-between;

        .footer-left-title {
            width: 64px;
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            margin-bottom: 12px;
            color: #191919;
        }

        .footer-left {
            p {
                font-family: SourceHanSansSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #595959;
                line-height: 40px;
                display: flex;
                align-items: center;

                &.address i {
                    background: url('../../assets/images/homepage/position.png') no-repeat;
                    background-size: cover;
                }

                &.tel i {
                    background: url('../../assets/images/homepage/telephone.png') no-repeat;
                    background-size: cover;
                }

                &.email i {
                    background: url('../../assets/images/homepage/email.png') no-repeat;
                    background-size: cover;
                }
            }

            i {
                display: block;
                width: 14px;
                height: 14px;
                margin-right: 10px;

            }
        }

        >div {
            flex: 1;
            margin-right: 20px;

            &:last-child {
                margin-right: 0;
            }


        }
    }

    .footer-main {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .footer-middle {
        height: 144px;

        p {
            height: 24px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            margin-bottom: 24px;
        }

        .download-btn {
            width: 149px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            border: 1px solid #0054d2;
            border-radius: 4px;
            border-radius: 4px;
            text-decoration: none;
            display: flex;
            align-items: center;
            text-align: center;
            box-sizing: border-box;
            padding: 0 10px;
            // justify-content: center;
            margin-bottom: 20px;
            color: #0054d2;

            &:hover {
                opacity: 0.8;
            }

            i {
                display: block;
                margin-right: 10px;
            }

            .ios-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/ios.png") no-repeat;
                background-size: cover;
            }

            .windows-icon {
                width: 14px;
                height: 14px;
                background: url("../../assets/images/homepage/win.png") no-repeat;
                background-size: cover;
            }
        }
    }

    .footer-right {
        height: 144px;
        font-weight: 400;
        font-size: 16px;
        color: #191919;
        display: flex;
        gap: 30px;
    }

    .qrcode-box {
        p {
            font-weight: 400;
            font-size: 16px;
            color: #191919;
            height: 24px;
            margin-bottom: 15px;
            text-align: center;
        }
    }

    .qrcode {
        width: 118px;
        height: 118px;
        background: #ffffff00;
        border: 1px solid #DEDEDE;
        border-radius: 2px;
        text-align: center;

        img {
            width: 100px;
            height: 100px;
            display: block;
            margin-top: 9px;
            margin-left: 9px;
        }
    }

    .copyright {
        width: 100%;
        height:48px;
        line-height:48px;
        text-align: center;
        // margin-top: 75px;
        color: #898e96;
        font-size: 12px;
        position: inherit;
        a{
            color: #898e96;
            text-decoration: none;
        }
    }
}
</style>
