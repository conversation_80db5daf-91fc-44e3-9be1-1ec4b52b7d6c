/**
 * 全站http配置
 *
 * axios参数说明
 * isToken是否需要token
 */
import axios from 'axios';
import {isNull} from './validate';
import utils from '@/utils/utils';
const pageName = import.meta.env.VITE_BUILD_PAGE;
// let Prefix = 'http://localhost:39876/api' // 有exe程序时使用该端口
let Prefix = '' // 无exe程序时置空
axios.defaults.timeout = 3000000;
axios.defaults.withCredentials = true;
axios.defaults.baseURL = Prefix
//返回其他状态吗
axios.defaults.validateStatus = function (status) {
    return status >= 200 && status <= 500; // 默认的
};

// 是否桌面端
const isUniwimPc = (utils.GetQueryString('uniwim') || utils.GetQueryString('uniwim', 'hash')) === 'pc'
        || location.hash.indexOf('/ai/uniwimpc') > -1;
// FROM_CHANNEL 来源渠道
let FROM_CHANNEL = 'web';
if (utils.isMobile()) {
    FROM_CHANNEL = 'app'
}
else if(isUniwimPc){
    FROM_CHANNEL = 'desk'
}

//HTTP Request拦截
axios.interceptors.request.use(
    (config) => {
        const meta = config.meta || {};
        const isToken = meta.isToken === false;
        if (!isToken) {
            config.headers['Authorization'] = utils.GetAuthorization();
        }
        config.headers['FROM_CHANNEL'] = FROM_CHANNEL;
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);
//HTTP Response拦截
axios.interceptors.response.use(
    async (res) => {
        // 判断 http 返回码
        const status = Number(res.status) || 200;

        // 获取 后端返回得错误提示
        const message = res.data.Message || '未知错误，请联系系统管理员！';

        // {meta:{isMessage:false}}默认全局不进行错误提示  如果配置了 {meta:{isMessage:true}} 则进行提示
        const meta = res.config.meta || {};
        const isMessage = meta.isMessage === true;

        //如果配置了 {meta:{isData:false}} 或者 1001 不进行拦截返回结果
        const isData = meta.isData === false;
        if (isData || res.data.Code == 1001) {
            if (status === 200) {
                return Promise.resolve(res.data);
            } else {
                return Promise.reject(message);
            }
        }
        if (status === 404) {
            return Promise.reject(message);
        }
        if (status !== 200 && isMessage) {
            return Promise.reject(message);
        }
        // 兼容http 返回码为200，但是R.success 是false的情况。 后端使用 R.fail（）的情况
        // 还有可能直接返回数据 ，没有 success， 则认为返回正常。
        const isSuccess = isNull(res.data.Success) || res.data.Success == true;
        if (!isSuccess && isMessage) {
            return Promise.reject(message);
            // return Promise.reject(new Error(message));
        }
        if (res.data instanceof Blob) {
            return Promise.resolve(res.data);
        }
        if (res.data.Code !== 0 && res.data.code !== 0 && isMessage) {
            return Promise.reject(message);
        }
        // 直接返回 response 的data， 因为不清楚 是否使用的统一包装类R
        return Promise.resolve(res.data.Response || res.data);
    },
    (error) => {
        return Promise.reject(error);
    }
);

export const post = (url, data, opts = { meta: { isMessage: false, isCrud: false } }) => {
    let temp = opts || {};
    return axios({
        ...temp,
        method: 'post',
        baseURL: 'baseURL' in temp ? temp.baseURL : Prefix,
        url: url,
        data: data,
        timeout: 300000
    });
};

export const get = (url, data, opts = { meta: { isMessage: false, isCrud: false } }) => {
    let temp = opts || {};
    return axios({
        ...temp,
        method: 'get',
        baseURL: 'baseURL' in temp ? temp.baseURL : Prefix,
        url: url,
        data: data,
        timeout: 300000
    });
};

export const del = (url, data, opts = {}) => {
    let temp = opts || {};
    return axios({
        ...temp,
        method: 'delete',
        baseURL: 'baseURL' in temp ? temp.baseURL : Prefix,
        url: url,
        data: data,
        timeout: 300000
    });
};


export const request = {
    get: (url, params, config) => {
        let c = config || { meta: { isCrud: false } };
        c.params = params;
        return axios.get(url, c);
    },
    post: (url, data, config) => {
        return axios.post(url, data || {}, config || { meta: { isCrud: false } });
    },
};
