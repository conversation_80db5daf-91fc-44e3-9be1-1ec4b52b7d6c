<template>
    <div class="service-package" v-loading="loading">
        <div class="header">
            <div class="title-section">
                服务包管理
                <div class="header-tools">
                    <el-button class="header-tools-item" type="circle" size="mini" @click="handleCateManage">
                        分类管理
                    </el-button>
                    <el-divider direction="vertical" style="margin: 0 16px" />
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onReset">
                        <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                        刷新
                    </el-button>
                    <el-button class="header-tools-item" type="circle" size="mini" @click="onAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                        新增
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null" size="mini"
                        @click="onEdit(null)">
                        <i class="action-iconfont icon-bianji"></i>
                        编辑
                    </el-button>
                    <el-button class="header-tools-item" type="circle" :disabled="currentRow == null || currentRow?.status === 1" size="mini"
                        @click="onDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu"></i>
                        删除
                    </el-button>
                </div>
            </div>
            <div class="condition-section">
                <el-form :inline="true" :model="params">
                    <el-form-item label="类型">
                        <el-select v-model="params.type" placeholder="" style="width: 160px" @change="changeType" clearable>
                            <el-option label="全部类型" value="all" />
                            <el-option v-for="it in types" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.classify" placeholder="请选择分类" @change="selectClassifyChange" style="width: 160px" clearable>
                            <el-option label="全部分类" value="all" />
                            <el-option v-for="it in classify" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="params.status" placeholder="请选择状态" @change="selectStatusChange" style="width: 160px" clearable>
                            <el-option v-for="it in status" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="params.name" placeholder="搜索名称" clearable style="width: 160px" @keyup.enter="onSubmit" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">
                            <i class="action-iconfont icon-sousuofangdajing" style="margin-right: 8px;font-size: 12px;"></i>
                            <span>查询</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="table-section">
            <div class="table-content" v-show="tabType == 'table'">
                <!-- stripe -->
                <el-table class="service-table" ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)"
                    @rowClick="handleRowClick">
                    <el-table-column type="index" label="序号"
                        :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                    <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'status'" #default="{ row }">
                            <el-tag type="info" round v-if="row.status === 0">
                                已下架
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.status === 1">
                                已上架
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'image'" #default="{ row }">
                            <img v-if="row.image" class="table-icon" :src="utils.require(row.image)" alt="">
                        </template>
                        <template v-else-if="it.scoped == 'iconPath'" #default="{ row }">
                            <img v-if="row.iconPath" class="table-icon" :src="utils.require(row.iconPath)" alt="">
                        </template>
                        <template v-else-if="it.scoped == 'categoryMain'" #default="{ row }">
                            {{ types.find(it => it.Value === row.categoryMain)?.Name || '' }}
                        </template>
                        <template v-else-if="it.scoped == 'categoryIds'" #default="{ row }">
                            {{ formatCategoryIds(row.categoryIds, '') }}
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <el-link type="primary" class="task-link" @click="onEdit(row)">编辑</el-link>
                            <el-link type="primary" class="task-link" @click="changeStatus(row)">{{ row.status === 0 ? '上架' : '下架' }}</el-link>
                            <el-link type="danger" class="task-link" @click="onDelete(row)" :disabled="row.status === 1">删除</el-link>
                            <el-divider direction="vertical" style="margin-left: 12px;" />
                            <el-link type="primary" class="task-link" @click="onVersion(row)">版本管理</el-link>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 50vh;" />
                    </template>
                </el-table>
                <div class="table-content-pagination">
                    <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                        :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total" @change="tableQuery(false)" />
                </div>
            </div>
        </div>
    </div>

    <!-- 新增服务包弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="dialogFormVisible" :title="isNew ? '新增' : '编辑'" width="800" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <el-form class="service-package-form service-package-template-form" :model="form" ref="ruleFormRef" label-width="110px" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="categoryMain" label="服务包分类" :label-line="true">
                        <el-select v-model="form.categoryMain" placeholder="请选择服务包分类" @change="categoryMainChange" :disabled="!isNew">
                            <el-option v-for="it in types" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="name" label="服务包名称" :label-line="true">
                        <el-input v-model.trim="form.name" :maxlength="20" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="provider" label="提供方" :label-line="true">
                        <el-input v-model="form.provider" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24" v-if="isNew">
                    <el-form-item prop="versionNumber" label="版本号" :label-line="true">
                        <el-input v-model.trim="form.versionNumber" type="text" clearable :disabled="!isNew" />
                    </el-form-item>
                </el-col>
                <el-col :span="18">
                    <el-form-item prop="categoryIds" label="分类" :label-line="true">
                        <el-select v-model="form.categoryIds" multiple placeholder="请选择分类"  :disabled="!isNew">
                            <el-option v-for="it in filterClassify()" :key="it.Value" :label="it.Name" :value="it.Value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-button type="primary" size="mini" @click="handleCateManage">
                        分类管理
                    </el-button>
                </el-col>
                <el-col :span="24" v-if="form.categoryMain === 'template'&&isNew">
                    <el-form-item prop="formatType" label="模板类型" :label-line="true">
                        <el-select v-model="form.formatType" placeholder="请选择模板类型"  :disabled="!isNew">
                            <el-option label="Word模板" value="word" />
                            <el-option label="Excel模板" value="excel" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" v-if="form.categoryMain === 'template'&&isNew">
                    <el-form-item prop="filePath"  class="file-upload-formitem" label="模板文件上传" :label-line="true">
                        <!-- accept=".docx,.pptx,.xlsx" -->
                        <el-upload
                            class="service-package-template-form-upload file-upload"
                            :show-file-list="false"
                            :headers="headers"
                            style="width:100%;"
                            @success="onUploadedFilePath"
                            drag
                            :disabled="!!form.filePath||!isNew"
                            action="/wimai/api/task/upload"
                            :multiple="false"
                            accept="application/json"
                            :before-upload="beforeUpload"
                        >
                            <!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                                <div class="el-upload__tip">支持word或excel，文件大小不超过10MB</div>
                            </div> -->
                            <div class="upload-item" style="width:64px;height:64px;font-size:12px;margin-bottom:0;">
                                <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                            </div>
                        </el-upload>
                        <div class="el-upload__list" v-if="!!form.filePath">
                            <div class="icon-preview-item">
                                <img src="@/assets/images/servicePackage/servicePackage.png" alt="">
                                <div class="icon-preview-item-tool" v-if="isNew">
                                    <el-icon @click="form.filePath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24" v-if="form.categoryMain === 'scene' || form.categoryMain === 'mcp'">
                    <el-form-item prop="filePath" class="file-upload-formitem" :label="`${form.categoryMain === 'scene' ? '场景' : 'MCP'}文件上传`" :label-line="true">
                        <el-upload
                            class="service-package-template-form-upload file-upload"
                            :show-file-list="false"
                            :headers="headers"
                            style="width:100%;"
                            @success="onUploadedFilePath"
                            drag
                            :disabled="!!form.filePath||!isNew"
                            action="/wimai/api/task/upload"
                            accept="application/json"
                            :multiple="false"
                            :before-upload="beforeUpload"
                        >
                            <!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                                <div class="el-upload__tip">支持JSON格式，文件大小不超过10MB</div>
                            </div> -->
                            <div class="upload-item" style="width:64px;height:64px;font-size:12px;margin-bottom:0;">
                                <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                            </div>
                            <!-- <el-icon style="width:64px;height:64px;font-size:12px;margin-bottom:0;" class="el-icon--upload"><Plus /></el-icon> -->
                        </el-upload>
                        <div class="el-upload__list" v-if="!!form.filePath">
                            <div class="icon-preview-item">
                                <img src="@/assets/images/servicePackage/servicePackage.png" alt="">
                                <div class="icon-preview-item-tool" v-if="isNew">
                                    <el-icon @click="form.filePath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="iconPath" label="服务包图标" :label-line="true">
                        <div v-if="!form.iconPath">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onUploaded" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div style="display: flex;">
                                    <div class="upload-item">
                                        <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                    </div>
                                    <el-button style="margin-left: 8px;" type="primary" size="mini" @click.stop="showIconDialog = true">
                                        选择默认图标
                                    </el-button>
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview small-height" v-else>
                            <div class="icon-preview-item">
                                <img :src="utils.require(form.iconPath)" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="form.iconPath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="image" label="封面图" :label-line="true">
                        <div v-if="!form.image">
                            <el-upload :show-file-list="false" :headers="headers" style="width:100%;height: 64px;"
                            @success="onUploadedCoverPath" action="/wimai/api/task/upload" accept="image/png,image/jpeg" :multiple="false">
                                <div style="display: flex;">
                                    <div class="upload-item">
                                        <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                                    </div>
                                    <el-button style="margin-left: 8px;" type="primary" size="mini" @click.stop="showImageDialog = true">
                                        选择默认封面图
                                    </el-button>
                                </div>
                            </el-upload>
                            <span class="upload-item-text">支持 PNG、JPG格式，建议尺寸64x64px，若不上传则为默认图标</span>
                        </div>
                        <div class="icon-preview small-height" v-else>
                            <div class="icon-preview-item">
                                <img :src="utils.require(form.image)" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="form.image = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="summary" label="简介" :label-line="true">
                        <el-input v-model.trim="form.summary" maxlength="200" :rows="4" type="textarea" />
                    </el-form-item>
                </el-col>
                
                <el-col :span="24">
                    <el-form-item prop="sort" label="排序" :label-line="true">
                        <el-input-number
                            style="width:100px;"
                            :controls="false"
                            v-model="form.sort"
                            :min="1"
                            controls-position="right"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="详情" :label-line="true">
                        <div style="width: 100%;height: 450px;">
                            <!-- contentType="text"   默认delta  html  text -->
                            <QuillEditor v-model:content="form.description" @ready="quillReady" @textChange="textChange" :options="editorOptions" contentType="html" style="height: calc(100% - 50px);">
                            </QuillEditor>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="save()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 版本管理 -->
     <el-dialog class="servicePackage-dialog" v-model="templateVersionVisible" :title="templateVersionTitle" width="960" style="border-radius: 4px;" :destroy-on-close="true" top="30vh">
        <div class="version-dialog-box">
            <div class="dialog-box-title">
                <div class="dialog-box-title-text">
                    <div class="text-content">版本历史</div>
                    <div class="text-tip">（管理{{(currentRow.categoryMain==='scene'?'场景':(currentRow.categoryMain==='mcp'?'MCP':'模板'))}}的不同版本，包括版本发布、回滚等操作）</div>
                </div>
                <div class="dialog-box-title-tools">
                    <el-button class="table-header-tools-item" @click="templateVersionQuery">
                        <i class="action-iconfont icon-shuaxinzhongzhi" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>刷新</span>
                    </el-button>
                    <el-button class="table-header-tools-item" @click="onVersionAdd">
                        <i class="action-iconfont icon-jiahaoxinzengtianjia" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>新增</span>
                    </el-button>
                    <!-- && currentUser?.id !== current?.creatorId -->
                    <el-button class="table-header-tools-item" @click="onVersionEdit(null)" :disabled="currentVersionRow == null">
                        <i class="action-iconfont icon-bianji" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>编辑</span>
                    </el-button>
                    <el-button class="table-header-tools-item" :disabled="currentVersionRow == null || currentRow?.versionNumber === currentVersionRow?.versionNumber" @click="onVersionDelete(null)">
                        <i class="action-iconfont icon-huishouzhanshanchu" style="font-size: 12px;margin-right: 8px;"></i>
                        <span>删除</span>
                    </el-button>
                </div>
            </div>
            <div class="dialog-box-table" v-loading="versionLoading">
                <el-table class="service-table" ref="tableRef" :data="versionTableData" border :show-overflow-tooltip="true"
                    :highlight-current-row="true" style="width: 100%;height: 100%" @rowClick="handleVersionRowClick">
                    <el-table-column label="序号" type="index" width="55" />
                    <el-table-column v-for="it in versionTableColumns" :key="it.data" :prop="it.data"  :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                        <template v-if="it.scoped == 'versionNumber'" #default="{ row }">
                            <span>{{ row[it.data] }}</span>
                            <el-tag style="margin-left: 8px;" type="primary" round v-if="currentRow?.versionNumber === row.versionNumber">
                                当前版本
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'releaseStatus'" #default="{ row }">
                            <el-tag type="info" round v-if="row.releaseStatus === 0">
                                草稿
                            </el-tag>
                            <el-tag type="success" round v-else-if="row.releaseStatus === 1">
                                稳定版本
                            </el-tag>
                            <el-tag type="danger" round v-else-if="row.releaseStatus === 2">
                                测试版本
                            </el-tag>
                        </template>
                        <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                            <el-link type="primary" class="task-link" @click="onVersionEdit(row)">编辑</el-link>
                            <el-link type="danger" class="task-link" @click="onVersionDelete(row)" :disabled="currentRow?.versionNumber === row.versionNumber">删除</el-link>
                            <el-divider direction="vertical" style="margin-left: 0;margin-right: 16px;" />
                            <el-link type="primary" :disabled="currentRow?.versionNumber === row.versionNumber || row.releaseStatus !== 1" class="task-link" @click="setCurrentVersion(row)">设为当前</el-link>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="暂无数据" style="height: 20vh;" />
                    </template>
                </el-table>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="templateVersionVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 版本管理创建新版本 -->
    <el-dialog class="servicePackage-dialog" v-model="addVersionVisible" :title="isNew ? '新增' : '编辑'" width="520" style="border-radius: 4px;" :destroy-on-close="true" top="25vh">
        <el-form class="service-package-form service-package-template-form" :model="versionForm" ref="ruleFormRef" label-width="110px" :rules="versionRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="versionNumber" label="版本号" :label-line="true">
                        <el-input v-model.trim="versionForm.versionNumber" type="text" clearable />
                        <!-- <span class="form-tip">建议使用语义化版本格式：主版本.次版本.修订版本</span> -->
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="版本描述" :label-line="true">
                        <el-input v-model.trim="versionForm.description" :rows="4" type="textarea" />
                        <!-- <span class="form-tip">详细说明此版本的新功能、改进和修复</span> -->
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="filePath" class="file-upload-formitem" :label="(currentRow.categoryMain==='scene'?'场景':(currentRow.categoryMain==='mcp'?'MCP':'模板'))+'文件'" :label-line="true">
                        <el-upload
                            class="service-package-template-form-upload file-upload"
                            :show-file-list="false"
                            :headers="headers"
                            style="width:100%;"
                            @success="onVersionUploaded"
                            drag
                            :disabled="!!versionForm.filePath"
                            action="/wimai/api/task/upload"
                            accept="application/json"
                            :multiple="false"
                            :before-upload="beforeVersionUpload"
                        >
                            <!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                                <div class="el-upload__tip">支持JSON格式，文件大小不超过10MB</div>
                            </div> -->
                            <div class="upload-item" style="width:64px;height:64px;font-size:12px;margin-bottom:0;">
                                <img class="upload-item-img" src="@/assets/images/servicePackage/upload.png" alt="">
                            </div>
                        </el-upload>
                        <div class="el-upload__list" v-if="!!versionForm.filePath">
                            <div class="icon-preview-item">
                                <img src="@/assets/images/servicePackage/servicePackage.png" alt="">
                                <div class="icon-preview-item-tool">
                                    <el-icon @click="versionForm.filePath = ''"><Delete /></el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="24" v-if="currentRow.categoryMain === 'template'">
                    <el-form-item prop="formatType" label="模板类型" :label-line="true">
                        <el-select v-model="versionForm.formatType" placeholder="请选择模板类型">
                            <el-option label="Word模板" value="word" />
                            <el-option label="Excel模板" value="excel" />
                        </el-select>
                    </el-form-item>
                </el-col> -->
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="addVersionVisible = false">取消</el-button>
                <el-button type="primary" @click="saveVersion">
                    {{ isNew ? '创建版本' : '保存版本' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理弹窗 -->
    <el-dialog class="servicePackage-dialog" :append-to-body="false" v-model="cateManageVisible" :title=" params.type==='template'? '模版分类管理' : '场景分类管理'" width="1042" style="border-radius: 4px;" :destroy-on-close="true" top="10vh">
        <div class="title-section cate-header-tools">
            <span></span>
            <div class="header-tools">
                <el-button class="header-tools-item" type="circle" size="mini" @click="onResetCate">
                    <i class="action-iconfont icon-shuaxinzhongzhi"></i>
                    刷新
                </el-button>
                <el-button class="header-tools-item" type="circle" size="mini" @click="onAddCate">
                    <i class="action-iconfont icon-jiahaoxinzengtianjia"></i>
                    新增
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onEditCate(null)">
                    <i class="action-iconfont icon-bianji"></i>
                    编辑
                </el-button>
                <el-button class="header-tools-item" type="circle" :disabled="currentCateRow == null" size="mini"
                    @click="onDeleteCate(currentCateRow)">
                    <i class="action-iconfont icon-huishouzhanshanchu"></i>
                    删除
                </el-button>
            </div>
        </div>
        <el-table class="cate-table" ref="cateTableRef" :data="cateTableData" border :show-overflow-tooltip="true"
            :highlight-current-row="true" style="width: 100%;height: 545px"
            @rowClick="handleCateRowClick">
            <el-table-column type="index" label="序号"
                :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
                <el-table-column v-for="it in cateTableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                <template v-if="it.scoped == 'status'" #default="{ row }">
                    <el-tag type="danger" round v-if="row.status === 'disable'">
                        禁用
                    </el-tag>
                    <el-tag type="success" round v-else-if="row.status === 'enable'">
                        启用
                    </el-tag>
                </template>
                <template v-if="it.scoped == 'applicableType'" #default="{ row }">
                    <template v-for="type in row.applicableType">
                        <el-tag type="danger" class="scene-tag" color="#F0F5FF" round v-if="type === 'scene'">
                            场景
                        </el-tag>
                        <el-tag type="success" class="mcp-tag" color="#FFFDDF" round v-else-if="type === 'mcp'">
                            MCP
                        </el-tag>
                        <el-tag type="success" class="template-tag" round v-else-if="type === 'template'">
                            模板
                        </el-tag>
                    </template>
                   
                </template>
                <template v-else-if="it.scoped == 'handle'" #default="{ row }">
                    <el-link type="primary" class="task-link" @click="onEditCate(row)">编辑</el-link>
                    <el-link type="danger" class="task-link" style="margin-left:16px;" @click="onDeleteCate(row)">删除</el-link>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" style="height: 50vh;" />
            </template>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cateManageVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 分类管理编辑弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="editCateVisible" :title="isCateNew ? '新增' : '编辑'" width="480" style="border-radius: 4px;" :destroy-on-close="true" top="25vh">
        <el-form class="service-package-form service-package-template-form" :model="cateForm" ref="cateFormRef" label-width="100px" :rules="templateRules">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item prop="name" label="名称" required :label-line="true">
                        <el-input v-model.trim="cateForm.name" :maxlength="32" type="text" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="description" label="分类描述" required :label-line="true">
                        <el-input v-model="cateForm.description" :rows="4" type="textarea" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="applicableType" label="适用类型" required :label-line="true">
                        <el-checkbox-group v-model="cateForm.applicableType">
                            <el-checkbox label="scene">场景</el-checkbox>
                            <el-checkbox label="mcp">MCP</el-checkbox>
                            <el-checkbox label="template">模板</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="status" label="状态" :label-line="true">
                        <!-- <el-switch :inactive-value="'disable'" :active-value="'enable'" v-model="cateForm.status" :rows="4" type="textarea" /> -->
                        <el-select  v-model="cateForm.status" >
                            <el-option
                                label="启用"
                                value="enable"
                            />
                            <el-option
                                label="停用"
                                value="disable"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="sort" label="排序" :label-line="true">
                        <el-input-number
                            style="width:100px;"
                            :controls="false"
                            v-model="cateForm.sort"
                            :min="1"
                            controls-position="right"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editCateVisible = false">取消</el-button>
                <el-button type="primary" @click="saveCate()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 默认图标选择弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="showIconDialog" title="选择默认图标" width="480" style="border-radius: 4px;" :destroy-on-close="true" top="20vh">
        <div class="icon-box">
            <div class="icon-item" :class="{ active: iconId === index }" v-for="(item, index) in iconMap" :key="'icon'+index" @click="iconId = index">
                <img :src="item" alt="">
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showIconDialog = false">取消</el-button>
                <el-button type="primary" @click="iconSave">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 默认封面图选择弹窗 -->
    <el-dialog class="servicePackage-dialog" v-model="showImageDialog" title="选择默认封面图" width="960" style="border-radius: 4px;" :destroy-on-close="true" top="20vh">
        <div class="icon-box image">
            <div class="icon-item" :class="{ active: imageId === index }" v-for="(item, index) in coverMap" :key="'icon'+index" @click="imageId = index">
                <!-- <img :src="utils.require(`@/assets/images/servicePackages/image/image (${index+1}).png`)" alt=""> -->
                <img :src="item" alt="">
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showImageDialog = false">取消</el-button>
                <el-button type="primary" @click="imageSave">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'                                       
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'
import { Quill } from '@vueup/vue-quill'
import type { FormInstance, FormRules } from 'element-plus'
import { VideoExtend, QuillVideoWatch } from 'quill-video-extend-module'
const customSizes = ['10px', '12px', '14px', '16px', '18px', '20px', '24px', '30px', '36px']
const allowedLineHeights = ['1', '1.5', '2', '2.5', '3'];
const fontNames = [
  'SourceHanSansSC-Regular',//思源黑体
  'Arial', 
  'SimSun', // 宋体
  'KaiTi', // 楷体
  'Microsoft YaHei', // 微软雅黑
];


const route = useRoute()
const userStore = useUserStore()

/**
 * 从一个文件名中提取括号内的数字。
 * 
 * @param {string} fileName - 要解析的文件名字符串，例如 "image (1).png"。
 * @returns {string|null} - 如果找到，返回括号内的数字字符串 (e.g., "1")；
 *                          如果没有找到匹配的模式，则返回 null。
 */
function extractNumberInParentheses(fileName) {
  // 步骤 1: 验证输入是否为字符串，确保函数健壮性
  if (typeof fileName !== 'string') {
    return null;
  }
  
  // 步骤 2: 定义正则表达式
  //  - \( 和 \) : 匹配字面的左、右括号。需要用反斜杠 `\` 进行转义。
  //  - (\d+)   : 这是一个“捕获组”，是我们要提取的核心内容。
  //    - \d    : 匹配任何数字 (0-9)。
  //    - +     : 匹配一个或多个前面的字符（即一个或多个数字）。
  const regex = /\((\d+)\)/;
  
  // 步骤 3: 使用 String.prototype.match() 方法执行匹配
  const matchResult = fileName.match(regex);
  
  // 步骤 4: 处理匹配结果
  // 如果 matchResult 不为 null，说明找到了匹配项。
  // 匹配结果是一个数组：
  //  - matchResult[0] 是整个匹配到的字符串，例如 "(1)"
  //  - matchResult[1] 是第一个捕获组的内容，也就是我们想要的数字 "1"
  return matchResult ? matchResult[1] : null;
}
const iconsModule = import.meta.glob('@/assets/images/servicePackage/icon/*.png', { eager: true })
const coversModule = import.meta.glob('@/assets/images/servicePackage/image/*.png', { eager: true })
const iconMap = {}
const coverMap = {}
for(const icon in iconsModule) {
    ;
  const filename = icon.match(/\/([^/]+)\.png$/)?.[1]
  const index = extractNumberInParentheses(filename)
  iconMap[1*index] = iconsModule[icon].default
}
for(const icon in coversModule) {
  const filename = icon.match(/\/([^/]+)\.png$/)?.[1]
  const index = extractNumberInParentheses(filename)
  coverMap[1*index] = coversModule[icon].default
}

// 已上传过的文件不再上传
const isUploadImg = ref({})

const iconSave = async() => {
    if (isUploadImg.value[iconMap[iconId.value]]) {
        form.value.iconPath = isUploadImg.value[iconMap[iconId.value]];
        showIconDialog.value = false;
    } else {
        // 请求文件数据
        try {
            const response = await fetch(iconMap[iconId.value]);
            if (response.ok) {
                // 转换为文件流
                const blob = await response.blob();
                // 提取文件名
                const fileName = iconMap[iconId.value].split('/').pop() || 'icon.png';
                // 文件流转换为file文件
                const file = new File([blob], fileName, { type: blob.type });
                const formData = new FormData();
                formData.append('file', file);
                saasApi.AITaskUpload(formData).then(res => {
                    isUploadImg.value[iconMap[iconId.value]] = res
                    form.value.iconPath = res;
                    showIconDialog.value = false;
                })
            }
        } catch (error) {
            form.value.iconPath = iconMap[iconId.value];
            showIconDialog.value = false;
        }
    }
}

const imageSave = async() => {
    // 如果文件已上传过,不再上传
    if (isUploadImg.value[coverMap[imageId.value]]) {
        form.value.image = isUploadImg.value[coverMap[imageId.value]];
        showImageDialog.value = false;
    } else {
        // 请求文件数据
        try {
            const response = await fetch(coverMap[imageId.value]);
            if (response.ok) {
                // 转换为文件流
                const blob = await response.blob();
                // 提取文件名
                const fileName = coverMap[imageId.value].split('/').pop() || 'icon.png';
                // 文件流转换为file文件
                const file = new File([blob], fileName, { type: blob.type });
                const formData = new FormData();
                formData.append('file', file);
                saasApi.AITaskUpload(formData).then(res => {
                    isUploadImg.value[coverMap[imageId.value]] = res
                    form.value.image = res;
                    showImageDialog.value = false;
                })
            }
        } catch (error) {
            form.value.image = coverMap[imageId.value];
            showImageDialog.value = false;
        }
    }
}


;
const loadImage = async(path:string)=>{
    let img = await import(path)
    return img.default
}
const initimages = async()=>{
    let list = []
    for(let i=1;i<146;i++){
        let img = await loadImage(`../../../assets/images/servicePackages/icon/icon (${i+1}).png`)
        list.push(img)
    }
    return list
}
let images = computed(()=>initimages())
;
;

const base64Images = ref(images);
const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
    "Authorization": Authorization.value,
    "FROM_CHANNEL": "web"
})
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
    currentPage: 1,
    pageSize: 30,
    total: 0
})
const isNew = ref(true)
const videoUploadUrl = ref('/wimai/api/task/upload')
const token = utils.GetAuthorization()
const form = ref({
    categoryMain: "scene",
    name: "",
    versionNumber: "",
    categoryIds: [],
    iconPath: '',
    image: '',
    description: "",
    summary: "",
    status: 0,
    formatType: 'word',
    filePath: "",
    provider:"",
    sort:1
})
const dialogFormVisible = ref(false)

const editorOptions = ref({
    // debug: 'info',
    modules: {
        toolbar: {
            container:[
                { header: [1,2,3,4,5,6,false] },
                "bold", "italic", "underline", "strike", "link", "image","video", 
                { 'size': customSizes }, // 4. 在工具栏中加入自定义的字体大小
                { list: "ordered" }, 
                { 'lineheight': allowedLineHeights }, // 使用我们注册的 'lineheight'
                { list: "bullet" },
                { 'script': 'sub'}, 
                { 'script': 'super' },
                { 'indent': '-1'}, 
                { 'indent': '+1' },
                { 'direction': 'rtl' },
                { 'color': [] }, { 'background': [] },
                { 'font': fontNames },
                { 'align': [] },
                'clean'
            ],
            handlers: {
              'video': function() {
                QuillVideoWatch.emit(this.quill.id)
              }
            }
        },
        VideoExtend: {
            loading: true,
            name: 'file',
            action: videoUploadUrl,
            headers: (xhr: XMLHttpRequest) => {
              // set custom token(optional)
              xhr.setRequestHeader('token', token)
            },
            response: (res:any) => {
              // video uploaded path
              // custom your own
              debugger;
              return res.Response
            }
        },
        imageResize: {
            displayStyles: {
                backgroundColor: "black",
                border: "none",
                color: "white"
            },
            parchment: Quill.import('parchment'),
            modules: ["Resize", "DisplaySize", "Toolbar"]
        }
    },
    placeholder: '请输入详情',
    // readOnly: true,
    theme: 'snow'
})

const types = ref([
    { Name: '场景', Value: 'scene' },
    { Name: 'MCP', Value: 'mcp' },
    { Name: '模板', Value: 'template' },
])
const classify = ref([])
const status = ref([
    { Name: '全部状态', Value: 'all' },
    { Name: '已上架', Value: 1 },
    { Name: '已下架', Value: 0 }
])
const tabType = ref('table')
const params = ref({
    type: 'all',
    classify: 'all',
    status: 'all',
    name: ""
})
// 定义 tableData 中元素的类型
interface TableDataItem {
    id: string;
    iconPath: string;
    name: string;
    versionNumber?: string;
    content?: string;
    price?: string;
    subscriptionCount?: string;
    downloadCount?: number;
    formatType?: string;
    status: any;
}
const tableData = ref<TableDataItem[]>([])
const tableColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true },
    { data: 'image', title: '封面', scoped: 'image', width: 120, orderable: true, filterable: true },
    { data: 'name', title: '名称', minWidth: 200, orderable: true, filterable: true },
    { data: 'categoryMain', title: '类型', scoped: 'categoryMain', minWidth: 160, orderable: true, filterable: true },
    { data: 'categoryIds', title: '分类', scoped: 'categoryIds', minWidth: 160, orderable: true, filterable: true },
    { data: 'versionNumber', title: '版本', minWidth: 160, orderable: true, filterable: true },
    { data: 'downloadCount', title: '使用量', minWidth: 160, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 240, fixed: 'right' },
])
interface versionTableDataItem {
    id: string;
    name: string;
    versionNumber: string;
    downloadCount: number;
    status: any;
}
const versionTableData = ref<versionTableDataItem[]>([])
const versionTableColumns = ref([
    { data: 'versionNumber', title: '版本号', scoped: 'versionNumber', width: 150, orderable: true, filterable: true },
    { data: 'created', title: '发布日期', scoped: 'created', orderable: true, filterable: true },
    { data: 'description', title: '版本描述', width: 160, orderable: true, filterable: true },
    // { data: 'releaseStatus', title: '状态', scoped: 'releaseStatus', width: 100, orderable: true, filterable: true },
    { data: 'downloadCount', title: '使用量', width: 80, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', width: 210, orderable: true, filterable: true },
])

// 图片选择弹窗
const showIconDialog = ref(false)
const iconId = ref(0)

const showImageDialog = ref(false)
const imageId = ref(0)

const ruleFormRef = ref<FormInstance>()


//ref 
const tableRef = ref<HTMLElement>()
const serviceTableRef = ref<HTMLElement>()


const versionLoading = ref(false)
const currentVersionRow = ref(null)
const templateVersionVisible = ref(false)
const templateVersionTitle = ref('')
const addVersionVisible = ref(false)
const versionForm = ref({
    versionNumber: '',
    versionType: '',
    description: "",
    filePath: '',
    compatibilityType: '',
    releaseStatus: '',
    formatType: ''
})


//分类管理
const cateManageVisible = ref(false)
const cateTableColumns = ref([
    { data: 'name', title: '名称', minWidth: 150, orderable: true, filterable: true },
    { data: 'description', title: '描述', minWidth: 200, orderable: true, filterable: true },
    { data: 'applicableType', title: '适用类型', minWidth: 200, orderable: true, filterable: true,scoped:"applicableType" },
    { data: 'resourceNum', title: '服务包数量', minWidth: 100, orderable: true, filterable: true },
    { data: 'status', title: '状态', scoped: 'status', minWidth: 100, orderable: true, filterable: true },
    { data: 'handle', title: '操作', scoped: 'handle', minWidth: 150, fixed: 'right' }
])
const currentCateRow = ref(null)
const cateTableData = ref([
])
const isCateNew = ref(true)
const cateForm = ref({
    name: '',
    description: "",
    status: "enable",
    applicableType:["scene","template"],
    sort:1
})
const editCateVisible = ref(false)

const cateFormRef = ref<FormInstance>()



//computed
// isUniwimPc
const isUniwimPc = computed(() => {
    return route.query.uniwim === 'pc'
})

const currentUser = computed(() => {
    return userStore.userInfo
})

//分类管理方法


const quillReady = (quill:any)=>{
    quill.root.quill = quill;
}
const handleCateManage = () => {
   cateManageVisible.value = true;
   cateTableQuery();
}

const categoryMainChange = () => {
    form.value.filePath = ''
    form.value.categoryIds = []
}

const filterClassify = () => {
    const data = classify.value.filter(it => {
        let value = false
        it.typeList.forEach(item => {
            if (item.type === form.value.categoryMain) {
                value = true
            }
        })
        if (value) return it
        else return false
    })
    return data
}

const cateTableQuery = (noloading: boolean = false) => {
    ;
    if (!noloading) {
        loading.value = true
    }
    currentCateRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"sort",
                Type:1
            },
            {
                Field:"updated",
                Type:-1
            }
        ]
        
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach((row:any)=>{
                if(row.typeList){
                    let order_map = {
                        "scene":1,
                        "mcp":2,
                        "template":3
                    }
                    row.typeList.sort((a,b)=>order_map[a.type]-order_map[b.type])
                    row.applicableType = row.typeList.map((tp:any)=>tp.type)
                }
            })
            cateTableData.value = res.rows
        } else {
            cateTableData.value = []
        }
    })
    .catch((err: any) => {
        cateTableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}

const onResetCate = () => {
    isCateNew.value = true;
    currentCateRow.value = null
    cateTableData.value = []
    cateTableQuery()
}

const onAddCate = () => {
    isCateNew.value = true
    editCateVisible.value = true
    cateForm.value = {
        name: '',
        description: "",
        status: "disable",
        applicableType:['scene'],
        sort:cateTableData.value.length+1
    }
}
const onEditCate = async (row: any) => {
    if (row) currentCateRow.value = row
    isCateNew.value = false
    cateForm.value = JSON.parse(JSON.stringify(await formSet(currentCateRow.value)))
    editCateVisible.value = true
}
const onDeleteCate = async (row: any) => {
    if (row) currentCateRow.value = row
    if(row.resourceNum)return ElMessage('该分类下已有服务包”，不可删除。')
    await ElMessageBox.confirm(`确认删除分类？`, `确认删除`, {
        type: 'warning',
    })
    saasApi.AIAgentResourceCategoryDelete([currentCateRow.value.id]).then((res: any) => {
        if (res.Code === 0) {
            ElMessage({
                message: '删除成功!',
                type: 'success',
                showClose: true
            })
            cateTableQuery();
        } else {
            ElMessage({
                message: res.Message || '删除失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const getClassifyData  = ()=>{
    const query_params: any = {
        conditions: [],
        data: {
            status:"enable"
        },
        size:Infinity,
        index:1
    }
    saasApi.AIAgentResourceCategoryQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            classify.value = res.rows.map(it => {
                return {
                    ...it,
                    Name: it.name,
                    Value: it.id
                }
            })
        } else {
            classify.value = []
        }
    })
    .catch((err: any) => {
        classify.value = []
    })
    .finally(() => {
    })

}
//上传回调
const onUploaded = (response: any, file: string, fileList: any) => {
    form.value.iconPath = response?.Response
}
const onUploadedCoverPath = (response: any, file: string, fileList: any) => {
    debugger;
    form.value.image = response?.Response
}
const onUploadedFilePath = (response: any, file: string, fileList: any) => {
    form.value.filePath = response?.Response
}
//方法

const textChange = (data) =>{
    ;
}
const onVersionUploaded = (response: any, file: string, fileList: any) => {
    versionForm.value.filePath = response?.Response
}
// 拖拽上传之前事件,只能json文件且不超过10M
interface UploadRawFile extends File {
  uid: number
  isDirectory?: boolean
}
const beforeUpload = async(rawFile: UploadRawFile) => {
    if (rawFile.type !== 'application/json' || rawFile.size > 10 * 1024 * 1024) {
        return false
    }
    const isTrue = await judgeJsonData(rawFile, form.value.categoryMain)
    if (!isTrue) {
        ElMessage({
            message: '模板格式不正确，请从工作流编排页面中的模板库中导出',
            type: 'error',
            showClose: true
        })
        return false
    }
}
const beforeVersionUpload = async(rawFile: UploadRawFile) => {
    if (rawFile.type !== 'application/json' || rawFile.size > 10 * 1024 * 1024) {
        return false
    }
    const isTrue = await judgeJsonData(rawFile, currentRow.value?.categoryMain)
    if (!isTrue) {
        ElMessage({
            message: '请上传正确格式的JSON文件!',
            type: 'error',
            showClose: true
        })
        return false
    }
}
// 判断上传json文件格式是否正确
const judgeJsonData = (rawFile: UploadRawFile, type: string) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const jsonData = JSON.parse(e.target.result); // 解析JSON字符串
                let isTrue = false
                if (type === 'scene') {
                    if (typeof jsonData.nodes === 'object' && jsonData.nodes.length > 0) {
                        isTrue = true
                    } else {
                        isTrue = false
                    }
                } else if (type === 'template') {
                    if (jsonData.exportType === 'excel' || jsonData.exportType === 'word') {
                        isTrue = true
                    } else {
                        isTrue = false
                    }
                } else if (type === 'mcp') {
                    isTrue = true
                }
                resolve(isTrue)
            } catch (error) {
                resolve(false)
            }
        };
        reader.readAsText(rawFile); // 读取文件内容为文本
    })
}
// 验证版本号格式 xx.xx.xx 的正则表达式
const validateVersion = (rule: any, value: any, callback: any) => {
    const regex = /^\d{1,2}\.\d{1,2}\.\d{1,2}$/;
    if (!regex.test(value)) {
        callback(new Error('版本号格式不正确'))
    } else {
        callback()
    }
};

// 验证版本号格式 xx.xx.xx 的正则表达式
const validateApplicableType = (rule: any, value: any, callback: any) => {
    ;
    if (!value||!value.length) {
        callback(new Error('版本号格式不正确'))
    } else {
        callback()
    }
};

const rules = reactive<FormRules>({
    summary: [
        { required: true, message: '请填写简介', trigger: 'change' }
    ],
    description: [
        { required: true, message: '请填写详情', trigger: 'change' }
    ],
    formatType: [
        { required: true, message: '请选择模板类型', trigger: 'change' }
    ],
    categoryMain: [
        { required: true, message: '请选择服务包类型', trigger: 'change' }
    ],
    name: [
        { required: true, message: '请输入服务包名称', trigger: 'change' }
    ],
    versionNumber: [
        { required: true, message: '请输入版本号', trigger: 'change', },
        { validator: validateVersion, trigger: 'change' }
    ],
    categoryIds: [
        { required: true, message: '请选择服务包分类', trigger: 'change' }
    ],
    image:[{ required: true, message: '请选择上封面图', trigger: 'change' }],
    iconPath:[{ required: true, message: '请选择图标', trigger: 'change' }],
    filePath: [
        { required: true, message: '请选择上传JSON文件', trigger: 'change' }
    ],
    iconPath: [
        { required: true, message: '请选择上传服务包图片', trigger: 'change' }
    ],
    image: [
        { required: true, message: '请选择上传封面图', trigger: 'change' }
    ]
})
const templateRules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入名称', trigger: 'change' }
    ],
    applicableType: [
        { required:true,validator:validateApplicableType,message: '请选择适用类型', trigger: 'blur' }
    ],
    description: [
        { required: true, message: '请输入描述', trigger: 'change' }
    ],
    versionNumber: [
        { required: true, message: '请输入版本号', trigger: 'change', },
        { validator: validateVersion, trigger: 'change' }
    ]
})// 自定义唯一性校验规则
const validateVersionUnique = (rule, value, callback) => {
  if(currentVersionRow&&currentVersionRow.value&&currentVersionRow.value.versionNumber == value){
    callback()
    return
  }
  // 模拟异步请求检查用户名唯一性
  saasApi.AIAgentTaskTemplateVersionQuery({
      data:{
        versionNumber:value,
        templateId: currentRow.value?.id
      }
    }).then(response => {
    if (response&&response.rows&&response.rows.length) {
      callback(new Error('该版本号已存在，请重新输入'));
    } else {
      callback();
    }
  }).catch(error => {
    callback();
  });
};
const versionRules = reactive<FormRules>({
    versionNumber: [
        { validator: validateVersionUnique, trigger: 'blur' },
        { required: true, message: '请输入版本号', trigger: 'change', },
        { validator: validateVersion, trigger: 'change' }
    ],
    description: [
        { required: true, message: '请输入版本描述', trigger: 'change' }
    ],
    filePath: [
        { required: true, message: '请上传模板文件', trigger: 'change' }
    ],
})

const save = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    
    if(form.value.categoryMain!='template'){
        form.value.formatType = ''
    }
    let update_params = {
        ...form.value
    }
    if (isNew.value) {
        tableInsert(update_params)
    } else {
        tableUpdate(update_params)
    }
}
const saveCate = async () => {
    let isValidate = await cateFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    if (isCateNew.value) {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            ...cateForm.value,
            typeList
            // type:params.value.type
        }
        delete update_params.applicableType;
        cateInsert(update_params)
    } else {
        let typeList:any = []
        if(Array.isArray(cateForm.value.applicableType)){
            typeList = cateForm.value.applicableType.map(tp=>{
                return {
                    type:tp
                }
            })
        }
        const update_params = {
            id: currentCateRow.value.id,
            name: cateForm.value.name,
            description: cateForm.value.description,
            status: cateForm.value.status,
            sort: cateForm.value.sort,
            typeList
            // type:params.value.type
        }
        cateUpdate(update_params)
    }
}
const saveVersion = async () => {
    let isValidate = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
    if (!isValidate) return
    const update_params = {
        ...versionForm.value,
        templateId: currentRow.value.id
    }
    if (!currentRow.value.id) {
        ElMessage({
            message: '未获取到模板id',
            type: 'error',
            showClose: true
        })
        return
    }
    if (isNew.value) {
        templateVersionInsert(update_params)
    } else {
        templateVersionUpdate(update_params)
    }
}

const handleRowClick = (row: any, show: boolean) => {
    currentRow.value = row
}
const handleVersionRowClick = (row: any, show: boolean) => {
    currentVersionRow.value = row
}
const handleCateRowClick = (row: any, show: boolean) => {
    currentCateRow.value = row
}


const tableQuery = (noloading: boolean = false) => {
    if (!noloading) {
        loading.value = true
    }
    currentRow.value = null
    const query_params: any = {
        conditions: [],
        data: {},
        order:[
            {
                Field:"sort",
                Type:1
            },
            {
                Field:"updated",
                Type:-1
            }
        ],
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    if (params.value.type !== 'all') {
        query_params.data.categoryMain = params.value.type
    }
    if (params.value.status !== 'all') {
        query_params.data.status = params.value.status
    }
    if (params.value.classify !== 'all') {
        query_params.data.categoryIds = [params.value.classify]
    }
    if (params.value.name) {
        query_params.data.name = params.value.name
    }
    saasApi.AIAgentTaskTemplateQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            pagination.value = {
                currentPage: res.current,
                pageSize: res.size,
                total: res.total
            }
            tableData.value = res.rows
        } else {
            tableData.value = []
        }
    })
    .catch((err: any) => {
        tableData.value = []
    })
    .finally(() => {
        if (!noloading) loading.value = false
    })
}
const tableInsert = (insert_params: any) => {
    saasApi.AIAgentTaskTemplateInsert(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const tableUpdate = (update_params: any, noQuery?: boolean, noMessage?: boolean) => {
    let input_params = {
        ...currentRow.value,
        ...update_params
    }
    saasApi.AIAgentTaskTemplateUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            if (!noMessage) {
                ElMessage({
                    message: '编辑成功!',
                    type: 'success',
                    showClose: true
                })
            }
            // 编辑不再跳转编排页面
            setTimeout(() => {
                if (!noQuery) tableQuery()
            }, 200)
            dialogFormVisible.value = false
        } else {
            if (!noMessage) {
                ElMessage({
                    message: '编辑失败!',
                    type: 'error',
                    showClose: true
                })
            }
        }
    }).finally(() => {

    })
}

const templateVersionQuery = () => {
    versionLoading.value = true
    currentVersionRow.value = null
    const query_params: any = {
        conditions: [],
        data: {
            templateId: currentRow.value?.id
        },
        index: pagination.value.currentPage,
        size: pagination.value.pageSize,
    }
    function removeHtmlTags(html:string) {
        // 检查输入是否为字符串
        if (typeof html !== 'string') {
            return '';
        }
        
        // 使用正则表达式匹配并移除所有HTML标签
        // 匹配模式：< 开头，中间是除 > 之外的任意字符，以 > 结尾
        return html.replace(/<[^>]*>/g, '');
    }
    saasApi.AIAgentTaskTemplateVersionQuery(query_params).then((res: any) => {
        if (typeof res?.rows == 'object') {
            res.rows.forEach(row=>{
                row.description = removeHtmlTags(row.description)
            })
            versionTableData.value = res.rows
        } else {
            versionTableData.value = []
        }
    }).finally(() => {
        versionLoading.value = false
    })
}
const templateVersionInsert = (insert_params: any) => {
    saasApi.AIAgentTaskTemplateVersionInsert(insert_params).then((res: any) => {
        if (res?.Success) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                templateVersionQuery()
            }, 200)
            addVersionVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const templateVersionUpdate = (update_params: any) => {
    let input_params = {
        ...update_params
    }
    saasApi.AIAgentTaskTemplateVersionUpdate(input_params).then((res: any) => {
        if (res?.Code === 0) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                templateVersionQuery()
            }, 200)
            if (currentVersionRow.value?.versionNumber === currentRow.value?.versionNumber) setCurrentVersion(input_params)
            addVersionVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}

const cateInsert = (insert_params: any) => {
    saasApi.AIAgentResourceCategoryAdd(insert_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '新增成功!',
                type: 'success',
                showClose: true
            })

            setTimeout(() => {
                cateTableQuery()
                getClassifyData()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '新增失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {
    })
}
const cateUpdate = (update_params: any) => {
    let input_params = {
        ...update_params
    }
    saasApi.AIAgentResourceCategoryUpdate(input_params).then((res: any) => {
        if (res) {
            ElMessage({
                message: '编辑成功!',
                type: 'success',
                showClose: true
            })
            // 编辑不再跳转编排页面
            setTimeout(() => {
                cateTableQuery()
                getClassifyData()
            }, 200)
            editCateVisible.value = false
        } else {
            ElMessage({
                message: '编辑失败!',
                type: 'error',
                showClose: true
            })
        }
    }).finally(() => {

    })
}


const changeType = (val:any) => {
    if (!val) {
        params.value.type = 'all'
    }
    params.value.classify = 'all'
    params.value.status = 'all'
    params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}
changeType(null)

const onSubmit = () => {
    tableQuery()
}
const selectClassifyChange = (val: any) => {
    if (!val) {
        params.value.classify = 'all'
    }
    tableQuery()
}
const selectStatusChange = (val: any) => {
    if (!val && val !== 0) {
        params.value.status = 'all'
    }
    tableQuery()
}

const formatTime = (data: any, format: string = 'YYYY-MM-DD HH:mm') => {
    if (!data) return ''
    return moment(data).format(format)
}

const formatCategoryIds = (data: any, text: any) => {
    (data || []).forEach((item, index) => {
        const name = classify.value.find(it => it.Value === item)?.Name || ''
        if (name) {
            if (index === 0) {
                text += `${name}`
            } else {
                text += `,${name}`
            }
        }
    })
    return text
}

//发布
const onPublish = (row: any) => {

}

//撤回
const onRevoke = (row: any) => {

}

const onReset = () => {
    params.value.classify = 'all'
    params.value.type = 'all'
    params.value.status = 'all'
    params.value.name = ''

    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    isNew.value = true
    form.value = {
        categoryMain: "scene",
        name: "",
        versionNumber: "",
        categoryIds: [],
        iconPath: '',
        image: '',
        description: "",
        summary: "",
        status: 0,
        formatType: 'word',
        filePath: "",
        provider:"",
        sort:1
    }
    currentRow.value = null
    tableData.value = []
    tableQuery()
}
const formSet = async (model: any) => {
    // const form_params: any = {
    //     name: "",
    //     describe: "",
    //     price: "",
    //     classify: '',
    //     icon: [],
    //     templates: []
    // }
    let data = JSON.parse(JSON.stringify(model))
    // // 设置默认值
    // Object.keys(form_params).forEach((key) => {
    //     if (!data[key]) {
    //         data[key] = form_params[key]
    //     }
    // })

    return data
}
const onAdd = async () => {
    isNew.value = true
    iconId.value = 0
    imageId.value = 0
    debugger;
    form.value = JSON.parse(JSON.stringify(await formSet({
        categoryMain: "scene",
        name: "",
        versionNumber: "1.0.0",
        categoryIds: [],
        iconPath: '',
        image: '',
        description: "",
        summary: "",
        status: 0,
        formatType: 'word',
        filePath: "",
        provider:"",
        sort:tableData.value.length+1
    })))
    // tableQuery()
    dialogFormVisible.value = true
}
const onEdit = async (row: any) => {
    if (row) currentRow.value = row
    isNew.value = false
    iconId.value = 0
    imageId.value = 0
    form.value = JSON.parse(JSON.stringify(await formSet(currentRow.value)))
    // tableQuery()
    dialogFormVisible.value = true
}
const changeStatus = (row: any) => {
    ElMessageBox.confirm(`确定${row.status === 0 ? '上架' : '下架'}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: (action) => {
            if (action === 'confirm') {
                currentRow.value = row
                row.status = row.status === 0 ? 1 : 0
                const update_params = {
                    status: row.status
                }
                tableUpdate(update_params)
            }
        },
    })
}
const onDelete = (row: any) => {
    const text = row.categoryMain === 'template' ? '确定删除模板吗？' : row.categoryMain === 'scene' ? '确定删除场景吗？' : '确定删除MCP吗？'
    ElMessageBox.confirm(text, '提示', {
        // autofocus: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: (action) => {
            if (action === 'confirm') {
                if (row) currentRow.value = row
                saasApi.AIAgentTaskTemplateDelete([currentRow.value.id]).then((res: any) => {
                    if (res.Code === 0 || res === true) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        tableQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            }
        },
    })
}
const onVersion = (row: any) => {
    if (row) currentRow.value = row
    templateVersionVisible.value = true;
    templateVersionTitle.value = (row.categoryMain==='scene'?'场景':(row.categoryMain==='mcp'?'MCP':'模板'))+'版本管理 - '+row.name
    templateVersionQuery()
}

const onVersionAdd = () => {
    isNew.value = true
    addVersionVisible.value = true
    let current_version = versionTableData.value?.[0]?.versionNumber
    let versionNumber = '1.0.0'
    if(current_version){
        versionNumber = utils.incrementVersion(current_version)
    }
    versionForm.value = {
        versionNumber,
        versionType: '',
        description: "",
        filePath: '',
        compatibilityType: '',
        releaseStatus: '',
        formatType: 'word'
    }
}
const onVersionEdit = async (row: any) => {
    if (row) currentVersionRow.value = row
    isNew.value = false
    versionForm.value = JSON.parse(JSON.stringify(await formSet(currentVersionRow.value)))
    addVersionVisible.value = true
}
const onVersionDelete = async (row: any) => {
    const text = '确定删除该版本吗？'
    ElMessageBox.confirm(text, '提示', {
        // autofocus: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: true,
        customClass: 'default-confirm-class',
        callback: (action) => {
            if (action === 'confirm') {
                if (row) currentVersionRow.value = row
                saasApi.AIAgentTaskTemplateVersionDelete([currentVersionRow.value.id]).then((res: any) => {
                    if (res.Code === 0) {
                        ElMessage({
                            message: '删除成功!',
                            type: 'success',
                            showClose: true
                        })
                        templateVersionQuery();
                    } else {
                        ElMessage({
                            message: res.Message || '删除失败!',
                            type: 'error',
                            showClose: true
                        })
                    }
                }).finally(() => {

                })
            }
        },
    })
}
const setCurrentVersion = async (row: any) => {
    if (!currentRow.value) {
        ElMessage({
            message: '未获取到模板id',
            type: 'error',
            showClose: true
        })
        return
    }
    const update_params = {
        versionNumber: row.versionNumber,
        filePath: row.filePath
    }
    currentRow.value = {
        ...currentRow.value,
        ...update_params
    }
    tableData.value.forEach(it => {
        if (it.id === currentRow.value.id) {
            it.versionNumber = row.versionNumber;
            it.filePath = row.filePath;
        }
    })
    tableUpdate(update_params, true, true)
}


onMounted(async () => {
    // for (const image of images) {
    //     const url = utils.require(image);
    //     try {
    //     const base64 = await utils.imageUrlToBase64(url);
    //         base64Images.value.push(base64);
    //     } catch (error) {
    //     console.error('图片转换失败:', error);
    //     }
    // }
    getClassifyData();
    // tableQuery()
})
</script>
<style scoped lang="scss">
::v-deep.cate-table .el-table__cell {
    border-bottom: 1px solid #EEEEEE !important;
}
.file-upload :deep(.el-upload--text){
  width:64px;
}
.file-upload  :deep(.el-upload-dragger){
  padding:0;
  border:none;
}
.file-upload-formitem :deep(.el-upload__list){
    width:64px;
}
:deep(.service-table) {
    .el-table__cell {
        border-bottom: 1px solid #EEEEEE !important;
    }
}
.service-package {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px;
    box-sizing: border-box;
    background: #f7f7f9 !important;
    display: flex;
    flex-direction: column;

    .header {
        background: #fff;
        width: 100%;
        height: 112px;
        box-sizing: border-box;
        overflow: hidden;

        

        .condition-section {
            padding: 8px 16px;
            box-sizing: border-box;
            // border-top: solid 1px #e8ecf0;
            display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 16px;
            }

            .tab-list {
                .tab-list-item {
                    width: 80px;
                    height: 32px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #222222;
                    border: 1px solid #E6E7E9;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &.left {
                        border-radius: 4px 0 0 4px;
                    }

                    &.right {
                        border-radius: 0 4px 4px 0;
                    }

                    &.active {
                        color: #FFFFFF;
                        background: #0054D9;
                        border-color: #0054D9;
                    }
                }
            }
        }
    }

    .table-section {
        //flex: 1;
        height:calc(100% - 112px);
        background: #fff;
    }

    .table-content {
        height: 100%;

        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }

        .table-icon {
            width: 24px;
            height: 24px;
            display: block;
            margin: 0 auto;
        }

        .table-content-pagination {
            height: 48px;
            padding: 0 12px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        ::v-deep(.el-scrollbar__view) {
            height: 100%;
        }
    }
   
    
}
.title-section {
    height: 64px;
    width: 100%;
    padding: 12px 16px 16px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSansSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    

    .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
    }
    }

    .header-tools {
    display: flex;
    align-items: center;

    .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
            margin-right: 4px;
            font-size: 14px;
        }

        span {
            margin-left: 6px;
            line-height: 17px;
        }

        &:hover {
            color: rgba(0, 84, 210, 0.8);
        }

        &:active {
            color: #0044A9;
        }

        &.is-disabled {
            color: #BCBFC3;
            cursor: not-allowed;
        }
    }
    }
    .cate-table{
        .el-link.task-link {
            font-size: 12px;

            &~.task-link {
                margin-left: 12px;
            }

            &.is-disabled {
                color: #BCBFC3;
            }
        }
        .scene-tag{
            height: 22px;
            background: #F0F5FF;
            border: 1px solid #BED2FF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #1E39C3;
            line-height: 22px;
            margin-right:8px;
        }
        .mcp-tag{
            height: 22px;
            background: #FFFDDF;
            border: 1px solid #E9DE9A;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #D7A710;
            line-height: 22px;
            margin-right:8px;
        }
        .template-tag{
            height: 22px;
            background: #F0FFFB;
            border: 1px solid #C0E7DF;
            border-radius: 11px;
            font-weight: 400;
            font-size: 12px;
            color: #2B9196;
            line-height: 22px;
            margin-right:8px;
        }
    }
</style>