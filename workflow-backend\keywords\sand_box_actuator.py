import re
import io
import sys
import types
import traceback
from RestrictedPython import compile_restricted
from RestrictedPython.Eval import default_guarded_getiter,default_guarded_getitem
from RestrictedPython.Guards import safe_builtins, full_write_guard
from RestrictedPython.PrintCollector import PrintCollector
from models.workflow import CustomKeyWordResult
import logging


# 预导入安全模块
def import_safe_modules():
    imported = {}
    # 允许导入的安全模块白名单
    SAFE_MODULES = {
        'math', 'random', 'datetime', 'time', 'json', 'collections', 'itertools',
        'functools', 'operator', 'string', 'base64', 'hashlib', 're', 'statistics',
        'bisect', 'heapq', 'array', 'copy', 'pprint', 'textwrap', 'unicodedata',
        'uuid', 'zlib', 'sys', 'io', '_strptime' # 包含 sys 和 io 模块
    }
    for module_name in SAFE_MODULES:
        try:
            # 特殊处理 sys 和 io 模块
            if module_name == 'sys':
                imported[module_name] = create_safe_sys_module()
            elif module_name == 'io':
                imported[module_name] = create_safe_io_module()
            else:
                imported[module_name] = __import__(module_name)
        except ImportError:
            pass
    return imported


# 创建安全的 sys 模块
def create_safe_sys_module():
    """创建具有 __guarded_setattr__ 的安全 sys 模块"""
    safe_sys = types.ModuleType('sys')

    # 添加只读属性
    safe_sys.version = sys.version
    safe_sys.platform = sys.platform
    safe_sys.maxsize = sys.maxsize
    safe_sys.argv = []
    safe_sys.path = []
    safe_sys.modules = {}
    safe_sys.hexversion = sys.hexversion
    safe_sys.api_version = sys.api_version
    safe_sys.copyright = sys.copyright
    safe_sys.executable = ""
    safe_sys.prefix = ""
    safe_sys.base_prefix = ""

    # 添加可写属性
    safe_sys.stdout = None
    safe_sys.stdin = None
    safe_sys.stderr = None

    # 添加 __guarded_setattr__ 方法
    def guarded_setattr(name, value):
        """允许设置特定属性"""
        if name in ['stdout', 'stdin', 'stderr']:
            object.__setattr__(safe_sys, name, value)
        else:
            raise AttributeError(f"不允许设置属性 '{name}'")

    safe_sys.__guarded_setattr__ = guarded_setattr
    return safe_sys


# 创建安全的 io 模块
def create_safe_io_module():
    """创建具有 __guarded_setattr__ 的安全 io 模块"""
    safe_io = types.ModuleType('io')
    safe_io.StringIO = io.StringIO

    # 添加 __guarded_setattr__ 方法
    def guarded_setattr(name, value):
        """禁止设置任何属性"""
        raise AttributeError(f"不允许设置属性 '{name}'")

    safe_io.__guarded_setattr__ = guarded_setattr
    return safe_io




def safe_import(name, globals=None, locals=None, fromlist=(), level=0):
    """自定义安全导入函数，仅允许导入白名单中的模块"""
    if name in SAFE_MODULES_DICT:
        module = SAFE_MODULES_DICT[name]
        if fromlist:
            mock_module = types.ModuleType(name)
            for attr in fromlist:
                if hasattr(module, attr):
                    setattr(mock_module, attr, getattr(module, attr))
            return mock_module
        return module

    parts = name.split('.')
    if parts[0] in SAFE_MODULES_DICT:
        base_module = SAFE_MODULES_DICT[parts[0]]
        current = base_module
        for part in parts[1:]:
            if hasattr(current, part):
                current = getattr(current, part)
            else:
                break
        else:
            return current

    raise ImportError(f"模块 '{name}' 不允许导入")


# 添加 _write_ 函数支持
def guarded_write(obj):
    """允许写入对象的属性"""
    return full_write_guard(obj)


SAFE_MODULES_DICT = import_safe_modules()

# robot 方法调用关键字：Execute Python Script
def execute_python_script(code: str, global_variables: dict = {}, workflow_variables: dict = {}):
    """
    在沙箱环境中执行Python代码片段，支持变量替换、模块导入和输出捕获

    :param code: 包含${variable}占位符的Python代码片段
    :param variables: 变量字典，key为变量名（不含${}），value为替换值
    :return: 执行结果（包含打印输出和最终结果）或错误信息
    """
    # 合并变量字典
    variables = {**(global_variables or {}), **(workflow_variables or {})}
    # 更健壮的变量替换
    def replace_var(match):
        var_name = match.group(1)
        if var_name in variables:
            value = variables[var_name]
            # 根据值类型决定如何表示
            if isinstance(value, str):
                return f"'{value}'"  # 字符串添加引号
            elif isinstance(value, (list, dict, tuple, set)):
                return str(value)  # 容器类型直接转换
            return repr(value)  # 其他类型使用repr
        raise NameError(f"变量 '{var_name}' 未定义")

    try:
        # 使用正则替换占位符
        processed_code = re.sub(r'\$\{(\w+)\}', replace_var, code)

        # 移除所有变量名中的下划线前缀
        header = """import sys
import io
originalstdout = sys.stdout
sys.stdout = outputbuffer = io.StringIO()
"""
        # 修复：移除对 locals() 的调用
        footer = """sys.stdout = originalstdout
capturedoutput = outputbuffer.getvalue()
# 不再使用 locals() 函数
"""
        # 保留原始代码的缩进结构
        wrapped_code = header + processed_code + "\n" + footer

        # 编译受限代码
        byte_code = compile_restricted(
            wrapped_code,
            filename="<sandbox>",
            mode="exec"
        )

        # 创建安全执行环境
        restricted_globals = {
            "__builtins__": {
                **{k: v for k, v in safe_builtins.items() if k not in ['eval', 'exec', 'open', 'input', 'locals']},
                "__import__": safe_import,
                "print": PrintCollector,
                # 添加必要的内置函数
                "isinstance": isinstance,
                "issubclass": issubclass,
                "type": type,
                "str": str,
                "int": int,
                "float": float,
                "list": list,
                "tuple": tuple,
                "dict": dict,
                "set": set,
                "bool": bool,
                "range": range,
                "enumerate": enumerate,
                "zip": zip,
                "filter": filter,
                "map": map,
                "sorted": sorted,
                "reversed": reversed,
                "len": len,
                "min": min,
                "max": max,
                "sum": sum,
                "abs": abs,
                "round": round,
                "divmod": divmod,
                "bin": bin,
                "hex": hex,
                "oct": oct,
                "chr": chr,
                "ord": ord,
                "hash": hash,
                "id": id,
                "dir": dir,
                "getattr": getattr,
                "setattr": setattr,
                "hasattr": hasattr,
                "callable": callable,
                "staticmethod": staticmethod,
                "classmethod": classmethod,
                "property": property,
                "object": object,
                "slice": slice,
                "memoryview": memoryview,
                "bytearray": bytearray,
                "bytes": bytes,
                "complex": complex,
                "frozenset": frozenset,
                "vars": vars,
                "globals": globals,
            },
            # 添加必要的 RestrictedPython 函数
            "_print_": PrintCollector,
            "_getiter_": default_guarded_getiter,
            "_write_": guarded_write,
            "_getitem_": default_guarded_getitem,  # 添加 _getitem_ 支持
            # 添加其他安全模块
            **{name: module for name, module in SAFE_MODULES_DICT.items()}
        }
        restricted_locals = {}

        # 执行代码
        exec(byte_code, restricted_globals, restricted_locals)

        # 获取捕获的输出和结果
        # captured_output = restricted_locals.get("capturedoutput", "")
        exec_result = restricted_locals.get("result", None)
        CustomKeyWordResult.stdout = exec_result
        CustomKeyWordResult.stderr = ""
        CustomKeyWordResult.rc = 0


    except Exception as e:
        # 详细的错误信息
        error_type = type(e).__name__
        error_msg = str(e)
        tb = traceback.format_exc()
        stderr = f"执行错误: {error_type}: {error_msg}\n{tb}"
        logging.error(f"python脚本执行错误: {e}")

        CustomKeyWordResult.stdout = ""
        CustomKeyWordResult.stderr = stderr
        CustomKeyWordResult.rc = 0
    # 组合输出和结果
    return CustomKeyWordResult
