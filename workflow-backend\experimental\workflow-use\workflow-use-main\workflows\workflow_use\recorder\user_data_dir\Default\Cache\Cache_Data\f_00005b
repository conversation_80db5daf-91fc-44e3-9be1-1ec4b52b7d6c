(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-mapgeogl"],{"9f2e":function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("14d9"),core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("13d5"),core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_esnext_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("0643"),core_js_modules_esnext_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_esnext_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_esnext_iterator_filter_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("2382"),core_js_modules_esnext_iterator_filter_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_esnext_iterator_filter_js__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_esnext_iterator_find_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("fffc"),core_js_modules_esnext_iterator_find_js__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_esnext_iterator_find_js__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_esnext_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("4e3e"),core_js_modules_esnext_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_esnext_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_esnext_iterator_map_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("a573"),core_js_modules_esnext_iterator_map_js__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_esnext_iterator_map_js__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_esnext_iterator_reduce_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("9d4a"),core_js_modules_esnext_iterator_reduce_js__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_esnext_iterator_reduce_js__WEBPACK_IMPORTED_MODULE_7__),_modules__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("a574"),_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("d615"),wkt__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("bcfb"),wkt__WEBPACK_IMPORTED_MODULE_10___default=__webpack_require__.n(wkt__WEBPACK_IMPORTED_MODULE_10__),_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__("4f11");class Mapgeogl{constructor(e){this.map=null,this.options=e,this.modules=_modules__WEBPACK_IMPORTED_MODULE_8__["a"],this.clickEvents=[],this.mapClickFun=null,this.currEditFeatureLayer=null,this.currEditFeature=null,this.layerInfos={},this.authkey=null,this.mode="2d",this.isLayerLoad=!1,this.popup=null,this.popupCallback=null,this.drawlayer=null,this.drawTool=null,this.highLightLayer=null,this.threeDreamLayer=null,this.AmbientLight=null,this.threePipeStyle=[],this.init()}async init(){var e;if(null!==(e=this.options)&&void 0!==e&&e.mapOptions.threebase){var t,i,r,l,o,a,s;this.map=new mapgeogl.Map(this.options.el,{basemapstyle:null===(t=this.options)||void 0===t?void 0:t.baseLayer,center:null===(i=this.options)||void 0===i?void 0:i.mapOptions.threebase.center,zoom:null===(r=this.options)||void 0===r?void 0:r.mapOptions.threebase.zoom,zooms:null===(l=this.options)||void 0===l?void 0:l.mapOptions.threebase.zooms,maptype:null===(o=this.options)||void 0===o?void 0:o.mapOptions.threebase.mapscene,projection:"EPSG:3857",rotation:(null===(a=this.options)||void 0===a||null===(a=a.mapOptions.threebase)||void 0===a?void 0:a.rotation)||0,pitch:70,Preciselevels:!1,rotateSpeed:.02,ScaleControl:{isScaleControl:!0,style:{left:"20px",bottom:"50px"}},planeStyle:null===(s=this.options)||void 0===s||null===(s=s.mapOptions.threebase)||void 0===s?void 0:s.planeStyle}),this.initThreeD()}else{var n,p,y,d,h;this.authkey=this.options.tokenManager.token,this.mode=(null!==(n=this.options)&&void 0!==n&&null!==(n=n.mapOptions.mode)&&void 0!==n&&n.includes("2")?"2d":null===(p=this.options)||void 0===p?void 0:p.mapOptions.mode)||"2d";null===(y=this.options.baseLayer)||void 0===y||null===(y=y.Mapgeogl)||void 0===y||y.find(e=>e.visible);let e=null===(d=this.options)||void 0===d?void 0:d.baseLayer;if(this.map=new mapgeogl.Map(this.options.el,{basemapstyle:e,center:[this.options.mapOptions.centerX,this.options.mapOptions.centerY],zoom:this.options.mapOptions.zoom,zooms:[2,22],basemaprender:"canvas",maptype:this.mode,projection:"EPSG:"+this.options.mapOptions.sr.id,rotation:(null===(h=this.options.opt3D)||void 0===h?void 0:h.rotation)||0,pitch:70,Preciselevels:!1,rotateSpeed:.02,ScaleControl:{isScaleControl:!0,style:{left:"20px",bottom:"50px"}},planeStyle:{color:"#536987",lighteffect:!0,opacity:1}}),"custom"===e){var u;let e=null===(u=this.options.baseLayer)||void 0===u?void 0:u.Mapgeogl;if(null!==e&&void 0!==e&&e.length)this.initBaselayer(e);else{let e=this.createBounds(this.options.mapOptions.sr.extent);this.map.setLimitBounds(e)}}}this.AmbientLight=new mapgeogl.Light.AmbientLight({color:"#ffffff",intensity:.1,time:new Date("2024-06-01 14:00:00")}),this.map.setZooms([this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom]),this.map.waitForMapLoad().then(()=>{var e;this.mode.includes("3d")&&this.initThreeD(),null!==(e=this.options.featureLayers)&&void 0!==e&&e.length&&this.initFeatureLayer(this.options.featureLayers),this.initHighLightLayer(),this.initDrawTools()})}setProjection(e){let t=new mapgeogl.ProjectionSystem({projectioncode:"EPSG:"+this.options.mapOptions.sr.id,custom:!1}),i=new mapgeogl.tileGrid({firstresolution:e.firstresolution,origin:"3857"===this.options.mapOptions.sr.id?[-20037508.342787,20037508.342787]:[-180,90],tileindexorder:[1,-1],mend:!0,custommaxzoom:e.custommaxzoom,zoomoffset:e.zoomoffset}),r=new mapgeogl.spatialReference({projectionsystem:t,tileGrid:i});return r}mapLoaded(){return new Promise(e=>{this.map.waitForMapLoad().then(()=>{e(!0)})})}initBaselayer(e){if(!e||!e.length)return[];e.forEach(e=>{if("WMTS"===e.layerType&&"Geoserver"===e.platform){let t=new mapgeogl.Layer.WMTSTileLayer({isOnline:!1,platform:"GeoServer",projection:this.setProjection(e.tilegridParam),url:e.url,layerid:e.id,params:{layer:e.layer,tilematrixset:e.tilematrixset,style:" ",format:"image/png"}});e.visible||t.loadTilelayer().then(()=>{t.hide()}),this.map.addLayer(t)}})}initFeatureLayer(e){e.length&&e.forEach(async e=>{this.isLayerLoad=!1;let t=JSON.parse(JSON.stringify(e));if("raster"===t.type){if("WMS"===t.layerType){let e=new mapgeogl.Layer.WMSLayer({url:t.url,layerid:t.id,params:{layers:t.layers,styles:"",version:"1.3.0",format:"image/png",transparent:!0,uppercase:!0},zooms:[19,30]});e.setMap(this.map)}else if("WMTS"===t.layerType){let e=t.url.split("/").findIndex(e=>"geoserver"===e),i=t.url.split("/").slice(0,e+1).join("/")+"/gwc/service/wmts",r=new mapgeogl.Layer.WMTSTileLayer({url:i,layerid:t.id,params:{layer:t.layers}});r.setMap(this.map)}}else if("vector"===t.type){if("关键点"!=t.pipeType){let e=t.url.split("/").findIndex(e=>"geoserver"===e);t.url=t.url.split("/").slice(0,e+1).join("/")+"/gwc/service/wmts",t.type="vector",t.layerType="WMTS"}void 0==this.layerInfos[t.pipeType+t.id]&&await this.getLayersInfo(t);let e=this.layerInfos[t.pipeType+t.id]["layers"].reverse();e=e.filter(e=>!e.id.includes("_arrow")&&!e.id.includes("_anno")),await Promise.all(e.map(e=>this.getVectorWMTSStyle(e))).then(e=>{e=e.reduce((e,t)=>e.concat(t)).filter(e=>e),e.forEach(e=>{"point"==e.type&&(e.filter={condition:"and",expression:[["==",["get","code"],"00"]]})});let i=this.options.ThreeGISAnimatLine;null!=i&&i.map(i=>{i.layerid==t.id&&i.style.map(t=>{t.opacity=0,e.push(t)})}),e=e.filter(e=>{if("Label"!==(null===e||void 0===e?void 0:e.lineStringType))return e}),this.layerInfos[t.pipeType+t.id].style=e,this.mode.includes("2")&&this.addVectorWMTS({layer:t,style:"2d"===this.mode?e.filter(e=>"tube"!=e.type&&"gltf"!=e.type):e.filter(e=>"tube"==e.type||"gltf"==e.type||"line"==e.type)}),this.isLayerLoad=!0})}})}initHighLightLayer(){var e;let t=0;"3d"===this.mode&&(t=null!==(e=this.options.opt3D)&&void 0!==e&&e.pipeHeight?Number(this.options.opt3D.pipeHeight)/200+.05:0),this.highLightLayer=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"highLightLayer",layertype:"point",altitude:t,zIndex:999999}),this.highLightLayer.setMap(this.map)}addVectorWMTS({layer:e,style:t}){try{var i;let r=new mapgeogl.Layer.VectorTileLayer({layerid:e.id,features:!0,url:`${e.url}?REQUEST=GetTile&SERVICE=WMTS&VERSION=1.0.0&LAYER=${e.layers}&STYLE=&TILEMATRIX=EPSG:${this.options.mapOptions.sr.id}:{z}&TILEMATRIXSET=EPSG:${this.options.mapOptions.sr.id}&FORMAT=application/vnd.mapbox-vector-tile&TILECOL={x}&TILEROW={y}&authkey=${this.authkey}`,zooms:e.zooms||[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],style:t,altitude:e.altitude||0,zIndex:e.zIndex||10});return this.map.addLayer(r),"false"==(null===(i=e.visible)||void 0===i?void 0:i.toString())&&r.hide(),r}catch(r){console.log("加载矢量切片失败",r)}}async addMapLayer(e){if("raster"===e.type){if("Geoserver"===e.platform){if("WMS"===e.layerType){let t=new mapgeogl.Layer.WMSLayer({url:e.url,layerid:e.id,params:{layers:e.layers,styles:"",version:"1.3.0",format:"image/png",transparent:!0,uppercase:!0}});return t.setMap(this.map),t}if("WMTS"===e.layerType){let t=e.url.split("/").findIndex(e=>"geoserver"===e),i=e.url.split("/").slice(0,t+1).join("/")+"/gwc/service/wmts",r=new mapgeogl.Layer.WMTSTileLayer({url:i,layerid:e.id,params:{layer:e.layers}});return r.setMap(this.map),r}}}else if("vector"===e.type){if("Geoserver"===e.platform){let t=[];t=e.ruleDetail?"line"===e.geometryType?this.getLineStyleOperate(e):this.getPointStyleOperate(e):await this.getVectorWMTSStyle(subitem),t.forEach(t=>t.layername=e.layers.split(":")[1]);let i=this.addVectorWMTS({layer:e,style:t});return i}}else e.type}setLayerVisible(e,t,i,r){if("basemap"===(null===r||void 0===r?void 0:r.type)){if(!t||e.includes("mark"))return;let i;if(r.basemapstyle)i=r.basemapstyle;else{var l;let t=null===(l=this.options.baseLayer)||void 0===l||null===(l=l.Mapgeogl)||void 0===l?void 0:l.find(t=>t.id===e);i="WMTS"===(null===t||void 0===t?void 0:t.layerType)?"custom":(null===t||void 0===t?void 0:t.layerType)||"天地图电子"}var o;if("custom"===i)null===(o=this.options.baseLayer)||void 0===o||o.Mapgeogl.forEach(t=>{t.id.includes(e)?this.getLayerById(t.id).show():this.getLayerById(t.id).hide()});else if("2d"==this.mode)this.map.setBasemapstyle(i);else{let e="";e="天地图电子,深色"==i?"day":"night",this.updateThreeDLayers(e,i)}}else if("pipe"===(null===r||void 0===r?void 0:r.type)){let l=this.options.featureLayers.find(t=>t.id==e),o=this.layerInfos[`${l.pipeType}${l.id}`].style;if("23d"!=r.mode){if(i){let t=o.filter(e=>{let t=i.split(",").find(t=>e.layername.includes(t));return t});t="2d"===this.mode?t.filter(e=>"tube"!=e.type&&"gltf"!=e.type):t.filter(e=>"tube"==e.type||"gltf"==e.type||"line"==e.type),this.getLayerById(e).updateStyle(t)}else if(this.getLayerById(e))if(t){let t="2d"===this.mode?o.filter(e=>"tube"!=e.type&&"gltf"!=e.type):t.filter(e=>"tube"==e.type||"gltf"==e.type||"line"==e.type);this.getLayerById(e).updateStyle(t)}else this.getLayerById(e).hide()}else{let t=[];o.forEach(e=>{let i;var l,o;"tube"!=e.type&&"gltf"!=e.type?i=null===(l=r.twoLayers)||void 0===l||null===(l=l.split(","))||void 0===l?void 0:l.find(t=>t===e.layername):i=null===(o=r.threeLayers)||void 0===o||null===(o=o.split(","))||void 0===o?void 0:o.find(t=>t===e.layername);i&&t.push(e)}),this.getLayerById(e).updateStyle(t)}}else this.getLayerById(e)&&(t?this.getLayerById(e).show():this.getLayerById(e).hide()),this.getLayerById(e+"-line")&&(t?this.getLayerById(e+"-line").show():this.getLayerById(e+"-line").hide()),this.getLayerById(e+"-polygon")&&(t?this.getLayerById(e+"-polygon").show():this.getLayerById(e+"-polygon").hide())}setLayerStatus(e,t,i){if(this.options.isVector)this.isLayerLoad?e.forEach(t=>{var r;let l;if("2d"==this.mode)l=this.layerInfos[`${t.pipeType}${t.id}`].style.filter(e=>t.status[e.layername]&&!["gltf","tube"].includes(e.type));else if("3d"==(null===(r=this.options.opt3D)||void 0===r?void 0:r.mode)){var o;let i=null===e||void 0===e?void 0:e.find(e=>e.id===t.id);l=null===(o=this.layerInfos[`${t.pipeType}${t.id}`])||void 0===o||null===(o=o.style)||void 0===o?void 0:o.filter(e=>i.status[e.layername]&&["gltf","tube","line"].includes(e.type))}else{var a;let r=(null===i||void 0===i?void 0:i.find(e=>e.id===t.id))||(null===e||void 0===e?void 0:e.find(e=>e.id===t.id));l=null===(a=this.layerInfos[`${t.pipeType}${t.id}`])||void 0===a||null===(a=a.style)||void 0===a?void 0:a.filter(e=>t.status[e.layername]&&!["gltf","tube"].includes(e.type)||r.status[e.layername]&&["gltf","tube","line"].includes(e.type))}l&&this.getLayerById(t.id).updateStyle(l)}):setTimeout(()=>{this.setLayerStatus(e,t)},100);else{this.getLayerById(e[0].id)&&this.getLayerById(e[0].id).remove();const t=Object.keys(e[0].status).filter(t=>{if(e[0].status[t])return t});if(t.length){let i={id:e[0].id,url:e[0].url,layers:t.join(","),style:"",visible:!0,layerType:"WMS",type:"raster",platform:"Geoserver"};this.addMapLayer(i)}}}clearMeasure(){this.measureTools&&(this.measureTools.turnOff(),this.measureTools.clearAllFeatures())}measureLegth(){this.clearMeasure(),this.measureTools=new mapgeogl.MeasureTool({map:this.map,type:"ruler",isAgain:!1,pointstyle:{fill:"#ffcc33",width:8,height:8,lineWidth:.01},linestyle:{width:2.5,color:"#ffcc33"}})}measureArea(){this.clearMeasure(),this.measureTools=new mapgeogl.MeasureTool({map:this.map,type:"area",isAgain:!1,pointstyle:{fill:"#ffcc33",width:8,height:8,lineWidth:.01},polygonstyle:{polygonFill:"#ffcc33",polygonOpacity:.5,lineWidth:1,lineColor:"#ffcc33",lineOpacity:1}})}pointToScreen(e){let t=mapgeogl.GeoTransforms.SysToScreen(e.coordinates).coordinates;return{x:t[0],y:t[1]}}zoomIn(){this.map.zoomIn()}zoomOut(){this.map.zoomOut()}getViewScale(){let e=this.map.getViewScale();return"3857"!=this.options.mapOptions.sr.id&&(e=111319.55*Number(e)),e}setViewScale(e){this.map.setViewScale(e)}getZoomFromResolution(){return this.map.getZoomFromResolution()}getViewLods(){let e=this.options.mapOptions.minZoom,t=this.options.mapOptions.maxZoom>22?22:this.options.mapOptions.maxZoom,i=[];for(let r=e;r<=t;r++){let e=this.map._getResolution(r),t=this.map.resolutionToScale(e,"m");i.push({zoom:r,resolution:e,scale:t})}return i}getCurZoom(){return this.map.getZoom()}getCurViewExtent(){let e=this.map.getBounds(),t=e.maxpoint.coordinates,i=e.minpoint.coordinates,r=[[[t[0],t[1]],[i[0],t[1]],[i[0],i[1]],[t[0],i[1]],[t[0],t[1]]]];return this.createPolygon(r)}setCenterZoom(e,t,i,r){"2d"==this.mode?this.map.setZoomAndCenter(i,[e,t],!0,0):this.map.flytoByZoom([e,t],{pitch:r.pitch||this.options.opt3D.pitch,duration:r.duration||1e3,zoom:i,rotation:r.rotation||this.options.opt3D.rotation},()=>{})}zoomToGeometry(e,t,i,r={}){Array.isArray(e)&&(e=this.getUnionGeometry(e)),i=i||23;let l=this.createFeature(e);this.map.flyToGeometry(l,{paddingleft:r.padding||200,paddingtop:r.padding||200,paddingright:r.padding||200,paddingbottom:r.padding||200,maxZoom:r.maxZoom||19,pitch:r.pitch||0,callback:t})}viewScaleListener(e){return this.viewScaleEvent=this.map.on("zoomchange",t=>{e&&e(this.map.getZoom())}),this.viewScaleEvent}viewExtentListener(e){return this.viewExtentEvent=this.map.on("mapmove",()=>{let t=this.map.getCenter(),i=this.pointToScreen(t);e&&e({centerCoords:t.coordinates,centerScreen:i,centerPoint:t})}),this.viewExtentEvent}pointerMoveListener(e){return this.pointerMoveListener=this.map.on("mousemove",t=>{let i=this.pointToScreen(t.geometry);e&&e({coordinate:t.geometry.coordinates,screenPoint:i,mapPoint:t.geometry})}),this.pointerMoveListener}mapOnClick(e,t){return this.mapClickEvent&&t&&this.removeMapEvent(this.mapClickEvent),this.mapClickEvent=this.map.on("click",i=>{let r=this.pointToScreen(i.geometry);e&&e({coordinate:i.geometry.coordinates,screenPoint:r,mapPoint:i.geometry,evt:i}),t||this.removeMapEvent(this.mapClickEvent)}),this.mapClickEvent}featureOnSelectAll(e,t=!0,i=3){}getFeaturesAtPixel(e){}removeClickEvent(){}removeMapEvent(e){e&&this.map.off(e.type,e.callback)}closePopup(){this.popup&&this.popup.close(),this.popupCallback&&this.popupCallback()}getPointCoords(e){let t=e.coordinates;return{x:t[0],y:t[1]}}getPolylineCoords(e){return e.coordinates}getPolygonCoords(e){return e.coordinates}getGeometryBounds(e){let t=this.createFeature(e),i=mapgeogl.bbox(t);return[[i[0],i[1]],[i[2],i[3]]]}getGeometryCenter(e){let t=this.createFeature(e).getCenter();return this.createPoint(t[0],t[1],t[2]||0)}getGeometryType(e){return e.type.includes("LineString")?"polyline":e.type.toLowerCase()}geometryToWkt(e){return wkt__WEBPACK_IMPORTED_MODULE_10___default.a.stringify(e)}wktToGeometry(e){return wkt__WEBPACK_IMPORTED_MODULE_10___default.a.parse(e)}featureToJson(e){return JSON.stringify(e)}jsonToFeature(e){let t=e;return"string"==typeof e&&(t=JSON.parse(e)),t}xmlToJson(e){return this.modules.x2js.xml2js(e)}createPoint(e,t,i){let r="2d"===this.mode?[Number(e),Number(t)]:[Number(e),Number(t),Number(i)];return{type:"Point",coordinates:r}}createPolyline(e){let t=this.getArrayDepth(e),i=3==t?"MultiLineString":"LineString";return{type:i,coordinates:e}}createPolygon(e){return{type:"Polygon",coordinates:e}}createMultiPolygon(e){return{type:"MultiPolygon",coordinates:e}}createBuffer(e,t,i){let r=this.createFeature(e);"meters"===i&&(t/=1e3);let l=mapgeogl.TopoLogy.buffer(r,t);return l.Feature.geometry}createBounds(e){return new mapgeogl.Bounds([[e[0],e[1]],[e[2],e[3]]])}getArrayDepth(e){return e instanceof Array?Math.max(...e.map(e=>1+parseInt(this.getArrayDepth(e)))):0}getPointStyle(e,t){let i=e,r=1;return Array.isArray(e)&&(i=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.slice(0,3)),r=e[3]||1),{fill:i,opacity:r,width:t,height:t}}getPointStyleOpt(e){let t=e.color,i=1;Array.isArray(e.color)&&(t=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.color.slice(0,3)),i=e.color[3]||1);let r=Array.isArray(e.outLineColor)?_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.outLineColor.slice(0,3)):e.outLineColor||"#fff";return{fill:t,opacity:i,width:e.size,height:e.size,lineColor:r,lineWidth:e.outLineWidth||1}}getPointStyleOperate(e){let t=[],i={type:"point",fill:"#3D93FD",width:6,height:6,lineColor:"#fff",lineWidth:.01};return t.push(i),e.ruleDetail.styleDetails.length&&(t=[],"step"===e.ruleDetail.operate?e.ruleDetail.styleDetails.forEach(r=>{t.push({...i,fill:r.color,width:r.radius,height:r.radius,filter:{condition:"and",expression:[[">=",["get",e.ruleDetail.property],Number(r.minValue)],["<",["get",e.ruleDetail.property],Number(r.maxValue)]]}})}):"match"===e.ruleDetail.operate&&e.ruleDetail.styleDetails.forEach(r=>{t.push({...i,fill:r.color,width:r.radius,height:r.radius,filter:{condition:"and",expression:[["==",["get",e.ruleDetail.property],r.value]]}})})),t}getMarkerStyle(e,t,i,r,l){return{pointType:"Icon",url:e,iconWidth:t,iconHeight:i,iconOffsetX:r||0,iconOffsetY:l||0}}getMarkerStyleOpt(e){return{pointType:"Icon",url:e.url,iconWidth:e.setWidth,iconHeight:e.setHeight,iconOffsetX:e.xoffset||0,iconOffsetY:e.yoffset||0}}getTextStyle(e,t,i,r,l){let o=t,a=1;return Array.isArray(t)&&(o=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(t.slice(0,3)),a=t[3]||1),{PointType:"Label",text:e,fontFill:o,fontOpacity:a,shadowFill:"#fff",shadowFillOpacity:1,shadowWidth:1,fontSize:i,fontWeight:"lighter",textOffsetX:r,textOffsetY:l}}getTextStyleOpt(e){let t=e.color,i=1;Array.isArray(e.color)&&(t=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.color.slice(0,3)),i=e.color[3]||1);let r=Array.isArray(e.outLineColor)?_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.outLineColor.slice(0,3)):e.outLineColor||"#fff";return{PointType:"Label",text:e.text,fontFill:t,fontOpacity:i,shadowFill:r,shadowFillOpacity:1,shadowWidth:e.outLineWidth||1,fontSize:e.fontSize,fontSpacing:e.fontSpacing||2,fontWeight:e.fontWeight||"lighter",textOffsetX:e.xoffset||0,textOffsetY:e.yoffset||0}}getLineStyle(e,t,i){let r=e,l=1;Array.isArray(e)&&(r=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.slice(0,3)),l=e[3]||1);let o={width:t,color:r,opacity:l,lineJoin:"round",lineCap:"round"};return"dash"===i&&(o.lineStringType="DottedLine",o.lineDottedArray=[10,10,10],o.lineDottedColor=[0,0,0,0]),o}getLineStyleOpt(e){let t=e.color,i=1;Array.isArray(e.color)&&(t=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.color.slice(0,3)),i=e.color[3]||1);let r={type:"line",width:e.width,color:t,opacity:i,lineJoin:"round",lineCap:"round"};return"dash"===e.style&&(r.lineStringType="DottedLine",r.lineDottedArray=[10,10,10],r.lineDottedColor=[0,0,0,0]),"AnimatLine"===e.lineStringType&&(r.lineStringType="AnimatLine",r.url=e.url,r.speed=e.speed||.1),r}getLineStyleOperate(e){let t=[],i={type:"line",lineStringType:"line",width:2,color:"#0054D3",opacity:1};return t.push(i),e.ruleDetail.styleDetails.length&&(t=[],"step"===e.ruleDetail.operate?e.ruleDetail.styleDetails.forEach((r,l)=>{t.push({...i,width:r.width,color:r.color,filter:{condition:"and",expression:[[">=",["get",e.ruleDetail.property],Number(r.minValue)],["<",["get",e.ruleDetail.property],Number(r.maxValue)]]}})}):"match"===e.ruleDetail.operate&&e.ruleDetail.styleDetails.forEach((r,l)=>{t.push({...i,width:r.width,color:r.color,filter:{condition:"and",expression:[["==",["get",e.ruleDetail.property],r.value]]}})})),t}getPolygonStyle(e,t,i,r){if(e.includes("rgba")){let t=e.split("rgba")[1].replace("(","[").replace(")","]");e=JSON.parse(t)}let l=e,o=1;Array.isArray(e)&&(l=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.slice(0,3)),o=e[3]||1);let a=t,s=1;return Array.isArray(t)&&(a=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(t.slice(0,3)),s=t[3]||1),{polygonFill:l,polygonOpacity:o,lineWidth:i,lineColor:a,lineOpacity:s}}getPolygonStyleOpt(e){let t=e.color,i=1;Array.isArray(e.color)&&(t=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.color.slice(0,3)),i=e.color[3]||1);let r=e.outLineColor,l=1;return Array.isArray(e.outLineColor)&&(r=_utils_transformColor__WEBPACK_IMPORTED_MODULE_9__["a"].rgb2hex(e.outLineColor.slice(0,3)),l=e.outLineColor[3]||1),{polygonFill:t,polygonOpacity:i,lineWidth:e.outLineWidth,lineColor:r,lineOpacity:l}}getDefaultStyle(e){let t;switch(e){case"point":t={fill:"#3D93FD",width:6,height:6,lineColor:"#fff",lineWidth:1};break;case"marker":t={pointType:"Icon",url:__webpack_require__("7eb7"),iconWidth:28,iconHeight:36,iconOffsetX:0,iconOffsetY:0};break;case"line":t={width:4,color:"#0000ff"};break;case"polygon":t={lineWidth:2,lineColor:"#F10091",polygonFill:"#94dbe6",polygonOpacity:.28};break}return t}async getVectorWMTSStyle(e){var t,i,r,l,o,a;try{var s;let f,L,v,T=e.url.split("/").findIndex(e=>"geoserver"===e),I=[],b=[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],E=[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom];switch("real3d"===(null===(s=this.options.opt3D)||void 0===s?void 0:s.mode)&&(b=e.zooms||b),e.geometryType){case"point":v=`${e.url.split("/").slice(0,T+1).join("/")}/rest/resource/styles/${e.name}.png?format='image/png'`,f={name:e.name,type:e.geometryType,layername:e.name,pointType:"Icon",url:v,iconWidth:16,iconHeight:16,rotation:{property:"sy_angle",type:"identity"},zooms:b},I.push(f);let s=!1;var n;if(null!==(t=this.options.layerOpt)&&void 0!==t&&t.isUserSld)s=null===(n=this.options.layerOpt)||void 0===n?void 0:n.isUserSld.includes(e.id);if(s&&(L=await this.getSldToJson(e),L.length>1)){I=[];let t=L.filter(e=>e.Filter),i=L.filter(e=>e.ElseFilter);if(t.length&&t.forEach((t,i)=>{var r,l;let o=t.PointSymbolizer.Graphic.ExternalGraphic.OnlineResource["_xlink:href"],a=o.split("/").splice(o.split("/").length-1)[0].replace(".png",""),s=`${e.url.split("/").slice(0,T+1).join("/")}/rest/resource/styles/${a}.png?format='image/png'`,n={condition:"and",expression:[]};if(null!==(r=t.Filter.And)&&void 0!==r&&r.PropertyIsGreaterThanOrEqualTo||null!==(l=t.Filter.And)&&void 0!==l&&l.PropertyIsGreaterThan){var p,y,d,h,u,m;let e=(null===(p=t.Filter.And.PropertyIsGreaterThanOrEqualTo)||void 0===p?void 0:p.PropertyName.__text)||(null===(y=t.Filter.And.PropertyIsGreaterThan)||void 0===y?void 0:y.PropertyName.__text),i=Number((null===(d=t.Filter.And.PropertyIsGreaterThanOrEqualTo)||void 0===d?void 0:d.Literal.__text)||(null===(h=t.Filter.And.PropertyIsGreaterThan)||void 0===h?void 0:h.Literal.__text)),r=Number((null===(u=t.Filter.And.PropertyIsLessThanOrEqualTo)||void 0===u?void 0:u.Literal.__text)||(null===(m=t.Filter.And.PropertyIsLessThan)||void 0===m?void 0:m.Literal.__text)),l=t.Filter.And.PropertyIsGreaterThanOrEqualTo?">=":">",o=t.Filter.And.PropertyIsLessThanOrEqualTo?"<=":"<";n.expression.push([l,["get",e],i],[o,["get",e],r])}else if(t.Filter.PropertyIsEqualTo){var g;let e=t.Filter.PropertyIsEqualTo.PropertyName.__text,i=(null===(g=t.Filter)||void 0===g||null===(g=g.PropertyIsEqualTo)||void 0===g?void 0:g.Literal.__text)||"";n.expression.push(["==",["get",e],i])}I.push({...f,name:`${e.name}-Icon-${i}`,url:s,filter:n})}),i.length){let t=i[0].PointSymbolizer.Graphic.ExternalGraphic.OnlineResource["_xlink:href"],r=t.split("/").splice(t.split("/").length-1)[0].replace(".png",""),l=`${e.url.split("/").slice(0,T+1).join("/")}/rest/resource/styles/${r}.png?format='image/png'`,o=I.map(e=>["!="].concat(e.filter.expression[0].slice(1,3)));I.push({...f,name:e.name+"-Icon-else",url:l,filter:{condition:"and",expression:o}})}}if(null!==(i=this.options.opt3D)&&void 0!==i&&null!==(i=i.mode)&&void 0!==i&&i.includes("3d")&&null!==(r=this.options.layerOpt)&&void 0!==r&&r.layerModel){var p;let t=null===(p=this.options.layerOpt)||void 0===p?void 0:p.layerModel.find(t=>t.includes(e.name));t&&(f={name:e.name+"-gltf",type:"gltf",layername:e.name,pointType:"gltf",url:`${this.options.resourceDataUrl}/${e.name}.glb`,translationX:0,translationY:0,translationZ:1,scaleX:2,scaleY:2,scaleZ:2,rotationX:0,rotationY:0,rotationZ:{property:"sy_angle",type:"identity"},bloom:!0,zooms:E},I.push(f))}break;case"circle":if(f={fill:"#3D93FD",width:6,height:6,lineColor:"#fff",lineWidth:1},I.push(f),L=await this.getSldToJson(e),L.length){var y;let e,t,i;I=[],null!==(y=L[0].Filter.And)&&void 0!==y&&y.PropertyIsGreaterThanOrEqualTo?(e=L[0].Filter.And.PropertyIsGreaterThanOrEqualTo.PropertyName.__text,t=">=",i=Number(L[0].Filter.And.PropertyIsGreaterThanOrEqualTo.Literal.__text)||0):L[0].Filter.PropertyIsEqualTo&&(e=L[0].Filter.PropertyIsEqualTo.PropertyName.__text,t="=="),L.forEach((i,r)=>{var l,o,a,s;let n=Number(null===(l=i.Filter.And)||void 0===l?void 0:l.PropertyIsLessThan.Literal.__text)||"",p=i.PointSymbolizer.Graphic.Mark.Fill.CssParameter.__text,y=Number(i.PointSymbolizer.Graphic.Size.__text)/4,d=(null===(o=i.PointSymbolizer.Graphic.Mark.Stroke)||void 0===o?void 0:o.CssParameter)||i.PointSymbolizer.Graphic.Mark.Fill.CssParameter;Array.isArray(d)||(d=[d]);let h,u=(null===(a=d.find(e=>"stroke"===e._name))||void 0===a?void 0:a.__text)||d.find(e=>"fill"===e._name).__text,m=Number(null===(s=d.find(e=>"stroke-width"===e._name))||void 0===s?void 0:s.__text)||.001;">="===t||"=="===t&&(h=[[t,["get",e],n]]),I.push({fill:p,width:y,height:y,lineColor:u,lineWidth:m,filter:{condition:"and",expression:h}})})}break;case"line":if(f={name:e.name,type:e.geometryType,layername:e.name,lineStringType:"line",width:2,color:"#0054D3",opacity:1,zooms:b},L=await this.getSldToJson(e),null!==(l=this.options.layerOpt)&&void 0!==l&&null!==(l=l.layerArrow)&&void 0!==l&&l.find(t=>t===e.name)){let t=`${e.url.split("/").slice(0,T+1).join("/")}/rest/resource/styles/${e.name}.png?format='image/png'`;I.push({name:e.name+"-arrow",type:"line",layername:e.name,lineStringType:"Icon",width:10,url:t,filter:{condition:"and",expression:[["!=",["get","flowdirect"],"0"]]}}),I.push({name:e.name+"-reverse",type:"line",layername:e.name,lineStringType:"Icon",width:10,url:`${e.url.split("/").slice(0,T+1).join("/")}/rest/resource/styles/${e.name}_right.png?format='image/png'`,filter:{condition:"and",expression:[["==",["get","flowdirect"],"0"]]}})}else if(L.length>1){let t=L.filter(e=>e.Filter),i=L.filter(e=>e.ElseFilter);if(t.length&&t.forEach((t,i)=>{var r,l,o;let a=t.LineSymbolizer.Stroke.CssParameter;Array.isArray(a)||(a=[a]);let s=a.find(e=>"stroke"===e._name).__text,n=1.5*Number(null===(r=a.find(e=>"stroke-width"===e._name))||void 0===r?void 0:r.__text)||2;if(n=n>3?n:3,null!==(l=t.Filter.And)&&void 0!==l&&l.PropertyIsGreaterThanOrEqualTo||null!==(o=t.Filter.And)&&void 0!==o&&o.PropertyIsGreaterThan){var p,y,d,h,u,m;let r=(null===(p=t.Filter.And.PropertyIsGreaterThanOrEqualTo)||void 0===p?void 0:p.PropertyName.__text)||(null===(y=t.Filter.And.PropertyIsGreaterThan)||void 0===y?void 0:y.PropertyName.__text),l=Number((null===(d=t.Filter.And.PropertyIsGreaterThanOrEqualTo)||void 0===d?void 0:d.Literal.__text)||(null===(h=t.Filter.And.PropertyIsGreaterThan)||void 0===h?void 0:h.Literal.__text)),o=Number((null===(u=t.Filter.And.PropertyIsLessThanOrEqualTo)||void 0===u?void 0:u.Literal.__text)||(null===(m=t.Filter.And.PropertyIsLessThan)||void 0===m?void 0:m.Literal.__text)),a=t.Filter.And.PropertyIsGreaterThanOrEqualTo?">=":">",g=t.Filter.And.PropertyIsLessThanOrEqualTo?"<=":"<";I.push({...f,name:`${e.name}-${i}`,color:s,width:n,filter:{condition:"and",expression:[[a,["get",r],l],[g,["get",r],o]]}})}else if(t.Filter.PropertyIsEqualTo){var g;let i=t.Filter.PropertyIsEqualTo.PropertyName.__text,r=(null===(g=t.Filter)||void 0===g||null===(g=g.PropertyIsEqualTo)||void 0===g?void 0:g.Literal.__text)||"";I.push({...f,name:e.name+"-equal",color:s,width:n,filter:{condition:"and",expression:[["==",["get",i],r]]}})}}),i.length){var d;let t=i[0].LineSymbolizer.Stroke.CssParameter;Array.isArray(t)||(t=[t]);let r=t.find(e=>"stroke"===e._name).__text,l=1.5*Number(null===(d=t.find(e=>"stroke-width"===e._name))||void 0===d?void 0:d.__text)||2;l=l>3?l:3,I.push({...f,name:e.name+"-else",color:r,width:l,filter:{condition:"not",expression:[I.map(e=>e.filter.expression[0])]}})}}else{var h;let t=L[0].LineSymbolizer.Stroke.CssParameter;Array.isArray(t)||(t=[t]);let i=t.find(e=>"stroke"===e._name).__text,r=1.5*Number(null===(h=t.find(e=>"stroke-width"===e._name))||void 0===h?void 0:h.__text)||2;r=r>3?r:3,I.push({...f,name:e.name+"-one",color:i,width:r})}if(L[0].TextSymbolizer){var u,m;let t={name:e.name+"-label",type:e.geometryType,layername:e.name,lineStringType:"Label",text:"",zooms:[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom]},i=L[0].TextSymbolizer.Label.PropertyName;Array.isArray(i)||(i=[i]);let r=i.find(e=>"d_s"===e.__text.toLowerCase());r&&(t.text+=`DN{${r.__text}} `);let l=i.filter(e=>"d_s"!=e.__text.toLowerCase());l.forEach((e,i)=>{t.text+=`{${e.__text}} `,i!=l.length-1&&(t.text+=" ")}),t.fontFill=L[0].TextSymbolizer.Fill.CssParameter.__text;let o=L[0].TextSymbolizer.Font.CssParameter;Array.isArray(o)||(o=[o]),t.fontSize=Number(null===(u=o.find(e=>"font-size"===e._name))||void 0===u?void 0:u.__text)||12,t.textFaceName=(null===(m=o.find(e=>"font-family"===e._name))||void 0===m?void 0:m.__text)||"微软雅黑",t.verticalAlignment="middle",I.push(t)}if(null!==(o=this.options.opt3D)&&void 0!==o&&null!==(o=o.mode)&&void 0!==o&&o.includes("3d")&&null!==(a=this.options.layerOpt)&&void 0!==a&&a.layerModel){var g;let t=null===(g=this.options.layerOpt)||void 0===g?void 0:g.layerModel.find(t=>t.includes(e.name));if(t){var c,_;let t=L[0].LineSymbolizer.Stroke.CssParameter;Array.isArray(t)||(t=[t]);let i=t.find(e=>"stroke"===e._name).__text;f={name:e.name+"-tube",type:"tube",layername:e.name,lineStringType:"line",color:i||"#fff000",width:(null===(c=this.options.opt3D)||void 0===c?void 0:c.pipeHeight)||150,height:(null===(_=this.options.opt3D)||void 0===_?void 0:_.pipeHeight)||150,metalness:1,roughness:.3,zooms:E},I.push(f)}}break;case"polygon":f={name:""+e.name,type:e.geometryType,polygonFill:"#94dbe6",polygonOpacity:.28,lineWidth:2,lineColor:"#F10091",lineOpacity:1};break}return I}catch(f){console.log({msg:e.name+"获取失败",error:f})}}getThreePointStyle(e){let t={name:e.name,filter:{condition:"and",expression:[["==",["get","layername"],e.name],["==","$type","Point"]]},type:"gltf",pointType:"gltf",url:`${this.options.resourceDataUrl}/${e.layername||e.name}.glb`,translationX:0,translationY:0,translationZ:0,rotationY:{property:"roll",type:"identity"},rotationZ:{property:"azimuth",type:"identity"},bloom:!1};return e.style&&(t=Object.assign(t,e.style)),t}getThreeLineStyle(e={}){var t;let i={name:e.name,filter:{condition:"and",expression:[["==",["get","layername"],e.name],["==","$type","LineString"]]},type:"tube",color:(null===(t=e.style)||void 0===t?void 0:t.stroke)||[0,.319,.414,1],metric:"mm",radialSegments:32,width:{type:"identity",property:"d_s"},height:{type:"identity",property:"d_s"},metalness:.8,roughness:.8,bloom:!0};return e.style&&(i=Object.assign(i,e.style)),i}getSldToJson(e){let t=JSON.parse(JSON.stringify(e)),i=t.url.split("/").findIndex(e=>"geoserver"===e);return t.url=`${t.url.split("/").slice(0,i+1).join("/")}/rest/styles/${t.styleName}.sld`,new Promise((e,i)=>{_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].querySld(t).then(t=>{var i,r;let l=this.xmlToJson(t.data),o=(null===(i=l.StyledLayerDescriptor.UserLayer)||void 0===i?void 0:i.UserStyle.FeatureTypeStyle.Rule)||(null===(r=l.StyledLayerDescriptor.NamedLayer)||void 0===r?void 0:r.UserStyle.FeatureTypeStyle.Rule);Array.isArray(o)||(o=[o]),e(o)}).catch(e=>{i({msg:"获取失败",error:e})})})}setFeatureStyle(e,t){e.updateSymbol(t)}setFeatureGeometry(e,t){let i=this.getGeometryType(t);switch(i){case"point":e.setPosition(t.coordinates);break;case"polyline":e.setLineString(t.coordinates);break;case"polygon":e.setPolygon(t.coordinates);break}}setFeatureAttributes(e,t){e.setProperties=t}createFeature(e,t={},i={}){return new mapgeogl.ComFeature(e,t,i)}createFeatureCollection(e){return{type:"FeatureCollection",features:e}}getGeometryByFeature(e){return Array.isArray(e)?e.map(e=>e.Feature.geometry):e.Feature.geometry}getAttributeByFeature(e){return Array.isArray(e)?e.map(e=>e.Feature.properties):e.Feature.properties}addHighLightFeature(e){if(Array.isArray(e)){let r=e.filter(e=>this.getGeometryType(e.Feature.geometry).includes("point")),l=e.filter(e=>this.getGeometryType(e.Feature.geometry).includes("polyline")),o=e.filter(e=>this.getGeometryType(e.Feature.geometry).includes("polygon"));if(r.length&&this.highLightLayer.addFeatureList(r),l.length){if(!this.getLayerById("highLightLayer-line")){var t;let e=0;"3d"===this.mode&&(e=null!==(t=this.options.opt3D)&&void 0!==t&&t.pipeHeight?Number(this.options.opt3D.pipeHeight)/200+.05:0);let i=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"highLightLayer-line",layertype:"line",altitude:e,zIndex:999999});i.setMap(this.map)}this.getLayerById("highLightLayer-line").addFeatureList(l)}if(o.length){if(!this.getLayerById("highLightLayer-polygon")){var i;let e=0;"3d"===this.mode&&(e=null!==(i=this.options.opt3D)&&void 0!==i&&i.pipeHeight?Number(this.options.opt3D.pipeHeight)/200+.05:0);let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"highLightLayer-polygon",layertype:"polygon",altitude:e});t.setMap(this.map)}this.getLayerById("highLightLayer-polygon").addFeatureList(o)}}else{let t=this.getGeometryType(e.Feature.geometry);if(t.includes("point"))this.highLightLayer.addFeature(e);else if(t.includes("polyline")){if(!this.getLayerById("highLightLayer-line")){var r;let e=0;"3d"===this.mode&&(e=null!==(r=this.options.opt3D)&&void 0!==r&&r.pipeHeight?Number(this.options.opt3D.pipeHeight)/200+.05:0);let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"highLightLayer-line",layertype:"line",altitude:e});t.setMap(this.map)}this.getLayerById("highLightLayer-line").addFeature(e)}else if(t.includes("polygon")){if(!this.getLayerById("highLightLayer-polygon")){var l;let e=0;"3d"===this.mode&&(e=null!==(l=this.options.opt3D)&&void 0!==l&&l.pipeHeight?Number(this.options.opt3D.pipeHeight)/200+.05:0);let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"highLightLayer-polygon",layertype:"polygon",altitude:e});t.setMap(this.map)}this.getLayerById("highLightLayer-polygon").addFeature(e)}}}clearHighLightLayer(){var e;null===(e=this.highLightLayer)||void 0===e||e.clearAll(),this.getLayerById("highLightLayer-line")&&this.getLayerById("highLightLayer-line").clearAll(),this.getLayerById("highLightLayer-polygon")&&this.getLayerById("highLightLayer-polygon").clearAll()}createGraphicsLayer(e,t={}){this.getLayerById(e)&&this.removeLayer(e);let i={layerid:e,layertype:t.layertype||"point",zooms:t.zooms||[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],collision:t.collision||!1,altitude:t.altitude||0};t.zIndex?i.zIndex=t.zIndex:i.zIndex=1e4;let r=new mapgeogl.Layer.FeatureGeometryLayer(i);return r.setMap(this.map),r}removeLayer(e){if(!e)return;let t=[];"string"===typeof e?t=[e]:Array.isArray(e)?e.forEach(e=>{t.push(e.id)}):t=[e.id],t.forEach(e=>{var t;null===(t=this.getLayerById(e))||void 0===t||t.remove(),this.getLayerById(e+"-line")&&this.getLayerById(e+"-line").remove(),this.getLayerById(e+"-polygon")&&this.getLayerById(e+"-polygon").remove()})}getLayerById(e){return this.map.getLayerById(e)}hasLayer(e){return this.map.hasLayer(e)}addFeatureToLayer(e,t,i,r={}){if(e)if("string"===typeof e&&(e=this.getLayerById(e)),Array.isArray(t)){let i=t.filter(e=>this.getGeometryType(e.Feature.geometry).includes("point")),l=t.filter(e=>this.getGeometryType(e.Feature.geometry).includes("polyline")),o=t.filter(e=>this.getGeometryType(e.Feature.geometry).includes("polygon"));if(i.length&&e.addFeatureList(i),l.length){if(!this.getLayerById(e.id+"-line")){let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:e.id+"-line",layertype:"line",zIndex:r.zIndex||0,altitude:r.altitude||0});t.setMap(this.map)}this.getLayerById(e.id+"-line").addFeatureList(l)}if(o.length){if(!this.getLayerById(e.id+"-polygon")){let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:e.id+"-polygon",layertype:"polygon",zIndex:r.zIndex||0,altitude:r.altitude||0});t.setMap(this.map)}this.getLayerById(e.id+"-polygon").addFeatureList(o)}}else{let i=this.getGeometryType(t.Feature.geometry);if(i.includes("point"))e.addFeature(t);else if(i.includes("polyline")){if(!this.getLayerById(e.id+"-line")){let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:e.id+"-line",layertype:"line",zIndex:r.zIndex||1,altitude:r.altitude||0});t.setMap(this.map)}this.getLayerById(e.id+"-line").addFeature(t)}else if(i.includes("polygon")){if(!this.getLayerById(e.id+"-polygon")){let t=new mapgeogl.Layer.FeatureGeometryLayer({layerid:e.id+"-polygon",layertype:"polygon",altitude:r.altitude||0});t.setMap(this.map)}this.getLayerById(e.id+"-polygon").addFeature(t)}}}removeHighLightFeature(e,t){let i=this.highLightLayer.getFeatureAll().filter(i=>i.Feature.properties&&i.Feature.properties[e]==t);if(i.length&&this.highLightLayer.removeFeatureList(i),this.getLayerById("highLightLayer-line")){let i=this.getLayerById("highLightLayer-line").getFeatureAll().filter(i=>i.Feature.properties&&i.Feature.properties[e]==t);i.length&&this.getLayerById("highLightLayer-line").removeFeatureList(i)}if(this.getLayerById("highLightLayer-polygon")){let i=this.getLayerById("highLightLayer-polygon").getFeatureAll().filter(i=>i.Feature.properties&&i.Feature.properties[e]==t);i.length&&this.getLayerById("highLightLayer-polygon").removeFeatureList(i)}}removeFeature(e,t){let i="string"===typeof e?e:e.id;this.getLayerById(i).removeFeature(t),this.getLayerById(i+"-line")&&this.getLayerById(i+"-line").remove(),this.getLayerById(i+"-polygon")&&this.getLayerById(i+"-polygon").remove()}removeFeatureFromLayer(e,t,i){let r="string"===typeof e?e:e.id;if(r.includes("highLightLayer"))this.removeHighLightFeature(t,i);else{let e=this.getLayerById(r).getFeatureAll().filter(e=>e.Feature.properties&&e.Feature.properties[t]==i);if(e.length&&this.getLayerById(r).removeFeatureList(e),this.getLayerById(r+"-line")){let e=this.getLayerById(r+"-line").getFeatureAll().filter(e=>e.Feature.properties&&e.Feature.properties[t]==i);e.length&&this.getLayerById(r+"-line").removeFeatureList(e)}if(this.getLayerById(r+"-polygon")){let e=this.getLayerById(r+"-polygon").getFeatureAll().filter(e=>e.Feature.properties&&e.Feature.properties[t]==i);e.length&&this.getLayerById(r+"-polygon").removeFeatureList(e)}}}clearGraphicsLayer(e){if(!e)return;let t="string"===typeof e?e:e.id;this.getLayerById(t)&&this.getLayerById(t).clearAll(),this.getLayerById(t+"-line")&&this.getLayerById(t+"-line").clearAll(),this.getLayerById(t+"-polygon")&&this.getLayerById(t+"-polygon").clearAll(),this.getLayerById(t+"-label")&&this.getLayerById(t+"-label").clearAll()}calcLineLength(e){let t=this.createFeature(e);return mapgeogl.TopoLogy.calclinelength(t)}getConvexHull(e){let t=e.map(e=>this.createFeature(e));return mapgeogl.TopoLogy.convexhull(t).Feature.geometry}getGeometryDistance(e,t){let i=this.createFeature(e),r=this.createFeature(t);return 1e3*mapgeogl.TopoLogy.distance(i,r)}getUnionGeometry(e){let t=e.map(e=>this.createFeature(e));return mapgeogl.TopoLogy.unionfeature(t).Feature.geometry}getIntersectGeometry(e,t){let i=this.createFeature(e),r=this.createFeature(t);return mapgeogl.TopoLogy.lineIntersect(i,r).Feature.geometry}isIntersect(e,t){let i=this.createFeature(e),r=this.createFeature(t);return mapgeogl.TopoLogy.booleanDisjoint(i,r)}isContains(e,t){let i=this.createFeature(e),r=this.createFeature(t);return mapgeogl.TopoLogy.booleanPointInPolygon(r,i)}getNearPointtoLine(e,t){let i=this.createFeature(e),r=this.createFeature(t);return mapgeogl.TopoLogy.nearSetPointtoLine(i,r)}async getLayersInfo(e){if("WMTS"==e.layerType){let t=this.options.featureLayers.find(t=>t.id==e.id);t&&(e=t)}const t=e.url,i=e.pipeType,r=e.id;if(void 0!=this.layerInfos[i+r]&&"{}"!=JSON.stringify(this.layerInfos[i+r]))return this.layerInfos[i+r];let{results:l}=await this.getLayerFields(t);return new Promise(async(o,a)=>{_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].queryWMSGetCapabilities(e).then(async s=>{const n=this.xmlToJson(s.data).WMT_MS_Capabilities;try{let a=n.Capability.Layer.Layer;const s=a.length;let d=0;for(let e=0;e<s-d;e++){var p,y;a[e]["layerType"]=(null===(p=l[a[e].Name])||void 0===p||null===(p=p.find(e=>"smgeometry"===e.name))||void 0===p?void 0:p.type)||"point",a[e]["geometryType"]=null!==(y=a[e].Name)&&void 0!==y&&y.includes("zt")?"circle":"point","Polyline"===a[e]["layerType"]?(a[e]["geometryType"]="line",a.push(a[e]),a.splice(e,1),e--,d++):"Polygon"===a[e]["layerType"]&&(a[e]["geometryType"]="polygon")}let h,u=[],m=[],g=[],c={},_={};a.forEach(r=>{var l,o,a;if("_bs"==(null===(l=r.Name)||void 0===l?void 0:l.slice(-3)))return;if(r.Name.includes("_arrow")||r.Name.includes("_anno"))return;const s=null===(o=r.Name)||void 0===o?void 0:o.substr(0,2);if("供水"===i){let l="";if("gs"===s&&(c[s]||(c[s]=!0,l=r.Name+"-"+e.id,h={id:e.id+"#供水",name:"供水",url:t,children:[],isParent:!0,layerType:"",geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id},m.push(h)),!r.Name.includes("gcj02"))){const l={id:r.Name,name:r.Name,title:r.Title,url:t,isParent:!1,layerType:r.layerType,geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id,parentId:e.id};h.children.push(l),g.push(l)}}if("排水"===i&&("ws"===s||"ys"===s||"hl"===s)&&(_[s]||("ws"===s?(_[s]={id:e.id+"#污水",name:"污水",url:t,children:[],isParent:!0,layerType:"",geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id},m.push(_[s])):"ys"===s?(_[s]={id:e.id+"#雨水",name:"雨水",url:t,children:[],isParent:!0,layerType:"",geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id},m.push(_[s])):"hl"===s&&(_[s]={id:e.id+"#合流",name:"合流",url:t,children:[],isParent:!0,layerType:"",geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id},m.push(_[s]))),!r.Name.includes("gcj02"))){const l={id:r.Name,name:r.Name,title:r.Title,url:t,isParent:!1,layerType:r.layerType,geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id,parentId:e.id};_[s].children.push(l),g.push(l)}if("关键点"===i&&"keypoint"===r.Name&&!r.Name.includes("gcj02")){const l={id:r.Name,name:r.Name,title:r.Title,url:t,isParent:!1,layerType:r.layerType,geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id};m.push(l),g.push(l)}if(e.customType&&s===(null===(a=e.id)||void 0===a?void 0:a.substr(0,2))&&(c[s]||(c[s]=!0,h={id:`${e.id}#${e.pipeType}`,name:e.pipeType,url:t,children:[],isParent:!0,layerType:"",geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id},m.push(h)),!r.Name.includes("gcj02"))){const l={id:r.Name,name:r.Name,title:r.Title,url:t,isParent:!1,layerType:r.layerType,geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id};h.children.push(l),g.push(l)}if("zt"===s){const l={id:r.Name,name:r.Name,url:t,isParent:!1,layerType:r.layerType,geometryType:r.geometryType,styleName:r.Style.Name,pipeType:i,mapLayerid:r.Name+"-"+e.id};u.push(l)}});let f={};u=[...g,...u,...this.layerInfos["allLayer"]||[]].reduce((e,t)=>(!f[t.id]&&(f[t.id]=e.push(t)),e),[]),this.layerInfos[i+r]=Object.assign(this.layerInfos[i+r]||{},{treeLayers:m,layers:g}),this.layerInfos["allLayer"]=u,this.map.layerInfos=this.layerInfos,o({treeLayers:m,layers:g})}catch(d){a({msg:"获取失败",error:d})}})})}getLayerFields(e){return new Promise((t,i)=>{let r={url:e.replace("wms","ows")};_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].queryWFSLayerInfo(r).then(e=>{var r;let l=this.xmlToJson(e.data);if(null===(r=l.schema.complexType)||void 0===r||!r.length)return void i({msg:"查询数据为空",results:l});let o={};l.schema.complexType.forEach(e=>{if(!e._name.includes("gcj02")&&!e._name.includes("GCJ02")){var t;let i=e._name.replace("Type","");o[i]=null===(t=e.complexContent)||void 0===t||null===(t=t.extension)||void 0===t||null===(t=t.sequence)||void 0===t||null===(t=t.element)||void 0===t?void 0:t.map(e=>({name:e._name,type:this.resetFieldType(e._type.split(":")[1]),alias:e._name}))}}),t({results:o})}).catch(e=>{i({msg:"查询失败",error:e})})})}queryFeatures(e){return new Promise((t,i)=>{e.url=e.url.replace("wms","ows"),_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].queryWFSsql(e,{cql_filter:e.where,EPSG:this.options.mapOptions.sr.id}).then(i=>{let r=i.data.features,l=i.data,o=r,a=[];_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].queryApi("/thirdgwapi/api/Gis_Manage/Get",{tableId:e.layers}).then(i=>{var r;const s=null===(r=i.data.Data)||void 0===r?void 0:r.Result;let n=[];s&&s.forEach(e=>{e&&"null"!==e&&null!==e&&" "!==e&&n.push(e)}),n.forEach(e=>{a.push({name:e.Column_Name,type:this.resetFieldType(e.Data_Type),alias:e.Remark||e.Column_Name})});let p=o.map(t=>{let i=t.properties;return i.geometry=this.jsonToFeature(t).geometry,"function"===typeof e.hookCallbak&&e.hookCallbak(i),i});t({results:p,fields:a,total:o.length,featureCollection:l})})})})}getEventCoordinate(e){let t=mapgeogl.GeoTransforms.ScreenToSys([e.offsetX,e.offsetY]).coordinates;return t}resetFieldType(e){let t=[{types:["TEXT","WTEXT","string"],resetType:"string"},{types:["INT32","INT64","DOUBLE","double"],resetType:"number"},{types:["DATETIME"],resetType:"date"},{types:["PointPropertyType","MultiPointPropertyType"],resetType:"Point"},{types:["CurvePropertyType","MultiCurvePropertyType"],resetType:"Polyline"},{types:["SurfacePropertyType","MultiSurfacePropertyType"],resetType:"Polygon"}],i="string";return t.forEach(t=>{t.types.includes(e)&&(i=t.resetType)}),i}resetLayerType(e){let t={POINT:"Point",LINE:"Polyline",REGION:"Polygon"};return t[e]}destroyMap(){this.map.destroy()}showPopup(e,t,i,r,l){this.closePopup(),setTimeout(()=>{let o=null;var a,s;"point"===this.getGeometryType(i)?o=i.coordinates.slice():(o=this.getGeometryCenter(i).coordinates.slice(),null!==(a=this.options.opt3D)&&void 0!==a&&a.pipeZOffset&&(o[2]=null===(s=this.options.opt3D)||void 0===s?void 0:s.pipeZOffset));let n;if(l.isCustom)n=l.element;else{n=document.createElement("div"),n.innerHTML=`<div id="popup" class="ol-popup" style="padding:10px;background-color: #fff">\n                    <span style="\n                    border-top: 10px solid #fff;\n                    border-bottom: 10px solid transparent;\n                    border-right: 10px solid transparent;\n                    border-left: 10px solid transparent;\n                    position: absolute;\n                    bottom: -18px;\n                    left: calc(50% - 10px)">\n                    </span>\n                    <div class="popup-title">${e} <span id="popupClose" style=" position: absolute; right: 5px; top: 0px;padding: 5px;">x</span></div>\n                    <div class="popup-content" style="overflow-y: auto;min-width:250px;"></div>\n                    </div>`;let i=n.getElementsByClassName("popup-content")[0];"string"===typeof t?i.innerHTML=t:i.appendChild(t)}if(this.popup=new mapgeogl.InfoWindow({content:n,isCustom:!0}),this.popup.open(this.map,o),this.popupCallback=r,!l.isCustom){let e=document.getElementById("popupClose");e.addEventListener("click",this.closePopup.bind(this)),e.parentElement.parentElement.parentElement.style.top="-10px"}},800)}closePopup(){this.popup&&this.popup.close(),this.popupCallback&&this.popupCallback()}initDrawTools(){var e;let t=null!==(e=this.options.opt3D)&&void 0!==e&&e.baseZOffset?Number(this.options.opt3D.baseZOffset)+.1:0;this.drawTool=new mapgeogl.DreamPainting({map:this.map,isAgain:!0}),this.closeDraw(),this.drawlayer=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"drawlayer",zooms:[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],zIndex:1e4,layertype:"point",altitude:t}),this.drawlayer.setMap(this.map),this.drawlayerLine=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"drawlayer-line",zooms:[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],zIndex:1e4,layertype:"line",altitude:t}),this.drawlayerLine.setMap(this.map),this.drawlayerPolygon=new mapgeogl.Layer.FeatureGeometryLayer({layerid:"drawlayer-polygon",zooms:[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],zIndex:1e4,layertype:"polygon",altitude:t}),this.drawlayerPolygon.setMap(this.map)}startDraw(e,t,i,r){this.clearDraw();let l={Point:{Fill:"#027ea7",width:5,height:5},Marker:{pointType:"Icon",url:__webpack_require__("7eb7"),iconWidth:28,iconHeight:36,iconOffsetX:0,iconOffsetY:0},LineString:{Width:2,Color:"#FF00FF",Opacity:1},Polygon:{lineWidth:2,lineColor:"#34495e",lineOpacity:1,polygonFill:"#73b8ff",polygonOpacity:.6}},o="Marker"===e?"Point":e;this.drawTool.startDraw(o,l[e],r=>{switch(e){case"Point":r.feature.add(this.drawlayer);break;case"Marker":r.feature.add(this.drawlayer);break;case"LineString":r.feature.add(this.drawlayerLine);break;case"Polygon":r.feature.add(this.drawlayerPolygon);break}i||this.closeDraw(),t(r.feature.Feature.geometry,r.feature)})}clearDraw(){this.closeDraw(),this.drawlayer&&this.drawlayer.clearAll(),this.drawlayerLine&&this.drawlayerLine.clearAll(),this.drawlayerPolygon&&this.drawlayerPolygon.clearAll(),this.clearMeasure()}closeDraw(){this.drawTool&&this.drawTool.turnOff()}startEdit(e,t,i,r){let l=this.getGeometryType(t.Feature.geometry);"polygon"==l?this.currEditFeatureLayer=e+"-polygon":"polyline"==l&&(this.currEditFeatureLayer=e+"-polyline"),this.currEditFeature=t,this.currEditFeature.turnOnEdit(),this.mapClickFun&&this.map.off("click",this.mapClickFun.callback),this.mapClickFun=this.map.on("click",e=>{this.closeEdit(),i(this.getGeometryByFeature(this.currEditFeature),this.currEditFeature),this.mapClickFun&&this.map.off("click",this.mapClickFun.callback)})}closeEdit(){this.currEditFeature&&(this.currEditFeature.turnOffEdit(),this.getLayerById(this.currEditFeatureLayer).removeFeature(this.currEditFeature))}featureOnSelect(e,t=!0,i,r){return this.mapClickFun&&t&&this.map.off("click",this.mapClickFun.callback),this.mapClickFun=this.map.on("click",l=>{if(r.selThreePipe){var o,a;this.clearhighLightThreeFeature();let t,i=null===(o=this.options.opt3D)||void 0===o?void 0:o.threePipePath.filter(e=>"3dtile"===e.layerType),s=null===(a=this.options.opt3D)||void 0===a?void 0:a.threePipePath.filter(e=>"jsonTiles"===e.layerType);if(i.length>0){let e=[];e=e.concat(i.filter(e=>!e.id.includes("gx")).map(e=>e.id)),e=e.concat(i.filter(e=>e.id.includes("gx")).map(e=>e.id));for(let i=0;i<e.length;i++){let r=this.getLayerById(e[i]);if(t=r.identify(l.geometry.coordinates),console.log("features--dtileLayer",t),t){let r=e[i].replace("Layer","");t[0].data.tableId=r,t={feature:{properties:t[0].data},coordinate:t[0].coordinate};break}}}if(s.length>0&&"line"!=r.selectType)for(let e=0;e<s.length;e++){let i=this.getLayerById(s[e].id),r=i.identify(l.geometry.coordinates);if(console.log("features--jsonTilesLayer",r),null!==r&&void 0!==r&&r.length){t=r.find(e=>e.type.includes("gltf"))?r.find(e=>e.type.includes("gltf")):r[0],t.feature&&(t.feature.properties.tableId=t.feature.properties.layername);break}}e&&e(t)}else{let t=new mapgeogl.Feature.Point({position:l.geometry.coordinates}),r=new mapgeogl.TopoLogy.buffer(t,.005),o={feature:r};i&&(o.layergroups=i),this.map.identify(o).then(t=>{var i;t.length&&(e&&e(null===(i=t[0])||void 0===i?void 0:i.feature))}).catch(e=>{console.error(e)})}!t&&this.mapClickFun&&this.map.off("click",this.mapClickFun.callback)}),{callback:this.mapClickFun.callback,type:"click"}}highLightThreeFeature(e){if(e.type){var t;let l=null===(t=this.options.opt3D)||void 0===t?void 0:t.threePipePath.find(t=>{var i;return null===(i=t.layers)||void 0===i?void 0:i.includes(e.feature.properties.tableId)});if(e.type.includes("tube"))this.getLayerById(l.id).highLight({id:e.feature.id,plugin:e.plugin},{heighcolor:"#FF1E00",bloom:!0,opacity:1,visable:!1});else{var i,r;let t=e.feature.properties.layername+e.feature.properties.globalid,o=this.getThreePointStyle({layername:e.feature.properties.layername,name:t,style:{filter:{condition:"and",expression:[["==",["get","layername"],e.feature.properties.layername],["==","$type","Point"],["==",["get","globalid"],e.feature.properties.globalid]]},bloom:!0,fill:"red"}});null!==(i=this.threePipeStyle.find(t=>t.name===e.feature.properties.layername))&&void 0!==i&&i.modelHeight&&(o.modelHeight=null===(r=this.threePipeStyle.find(t=>t.name===e.feature.properties.layername))||void 0===r?void 0:r.modelHeight);let a=this.threePipeStyle.concat([o]);this.getLayerById(l.id).setStyle(a)}}else{var l;let t=null===(l=this.options.opt3D)||void 0===l?void 0:l.threePipePath.find(t=>t.id.includes(e.feature.properties.tableId));this.getLayerById(t.id).highLight([{id:e.feature.properties.batchId}],{color:"red",bloom:!0,opacity:1,visible:!0})}}clearhighLightThreeFeature(){var e,t;let i=null===(e=this.options.opt3D)||void 0===e||null===(e=e.threePipePath)||void 0===e?void 0:e.filter(e=>"3dtile"===e.layerType),r=null===(t=this.options.opt3D)||void 0===t||null===(t=t.threePipePath)||void 0===t?void 0:t.filter(e=>"jsonTiles"===e.layerType);(null===i||void 0===i?void 0:i.length)>0&&i.forEach(e=>{var t;null===(t=this.getLayerById(e.id))||void 0===t||t.cancelAllHighlight()}),(null===r||void 0===r?void 0:r.length)>0&&r.forEach(e=>{this.getLayerById(e.id).cancelHighlight()})}switchMode(e,t){if(this.isLayerLoad)if("2d"===e){var i;if(e===this.mode)return;null===(i=this.threeDreamLayer)||void 0===i||i.hide(),this.options.opt3D.threeDLayers.forEach(e=>{var t;null===(t=this.getLayerById(e.id))||void 0===t||t.hide()}),this.mode=e,this.map.switchMapMode(()=>{this.options.featureLayers.forEach(e=>{var t,i;let r=null===(t=this.layerInfos[`${e.pipeType}${e.id}`].style)||void 0===t?void 0:t.filter(e=>"tube"!=e.type&&"gltf"!=e.type);null===(i=this.getLayerById(e.id))||void 0===i||i.updateStyle(r)})})}else{var r;null===(r=this.threeDreamLayer)||void 0===r||r.show(),e!=this.mode&&(this.mode=e,this.map.switchMapMode(()=>{this.options.featureLayers.forEach(e=>{var t,i;let r=null===(t=this.layerInfos[`${e.pipeType}${e.id}`])||void 0===t||null===(t=t.style)||void 0===t?void 0:t.filter(e=>"tube"==e.type||"gltf"==e.type||"line"==e.type);null===(i=this.getLayerById(e.id))||void 0===i||i.updateStyle(r)})})),this.updateThreeDLayers(t)}else setTimeout(()=>{this.switchMode(e,t)},100)}initThreeD(e){console.log("我执行了-------------------------------");let t=this.options.opt3D.threeDLayers.map(t=>{let i=null;return i=e?!t.id.includes(e):!String(t.visible)||t.visible,{...t,visible:i}}).filter(e=>e.visible);this.initThreeDLayers(t)}async initTwoPipeLayer(){var e,t;if(null!==(e=this.options.layerOpt)&&void 0!==e&&null!==(e=e.layerModel)&&void 0!==e&&e.find(e=>e.includes("gx"))&&null!==(t=this.options.opt3D)&&void 0!==t&&null!==(t=t.twoPipePath)&&void 0!==t&&t.length){var i;let e=null===(i=this.options.layerOpt)||void 0===i?void 0:i.layerModel.filter(e=>e.includes("gx")).map(e=>({geometryType:"line",name:e,url:this.options.tokenManager.serverUrl,styleName:e}));await Promise.all(e.map(e=>this.getVectorWMTSStyle(e))).then(e=>{e=e.reduce((e,t)=>e.concat(t));let t=e.filter(e=>"tube"!=e.type&&"Label"!=e.lineStringType),i=this.options.opt3D.twoPipePath[0],r=i.url.split("/").findIndex(e=>"geoserver"===e);i.url=i.url.split("/").slice(0,r+1).join("/")+"/gwc/service/wmts",this.addVectorWMTS({layer:i,style:t.map(e=>({...e,color:"#C500FF"}))})})}}initThreeDLayers(e){if(!e.length)return;let t=[],i=[];e.forEach(e=>{"vector"===e.layerType&&"build"!=e.type&&"water"!=e.type?t.push(e):i.push(e)}),this.createVectorLayer(t),i.forEach(async e=>{switch(e.layerType){case"vector":setTimeout(()=>{"nightBuildLayer"!=e.id&&this.createVectorLayer(e)},3e3);break;case"3dtile":this.create3dtileLayer(e);break;case"glb":let t=e.style||{};t.url=e.url;let i=this.createModelFeature(e.style.center,e.style),r=this.createModelLayer(e.id,{visible:e.visible});i.addModel(r);break;case"geojson":this.createGeojsonLayer(e)}})}initThreePipeLayers(e){e.length&&e.forEach(e=>{switch(e.layerType){case"vector":this.createVectorLayer(e);break;case"3dtile":this.create3dtileLayer(e);break;case"glb":r=e.style||{},r.url=e.url;let t=this.createModelFeature(e.style.center,e.style),i=this.createModelLayer(e.id,{visible:e.visible});t.addModel(i);break;case"geojson":this.createGeojsonLayer(e);case"jsonTiles":let r=e.layers.split(",").map(t=>{var i,r;return t.includes("gx")?this.getThreeLineStyle({name:t.split(":")[1],style:null===(i=e.style)||void 0===i?void 0:i[t.split(":")[1]]}):this.getThreePointStyle({name:t.split(":")[1],style:null===(r=e.style)||void 0===r?void 0:r[t.split(":")[1]]})});this.threePipeStyle=r;let l=e.layers.split(",").map(t=>({url:e.url,layers:t})),o=l.map(e=>this.queryFeatures(e));Promise.all(o).then(t=>{var i;let l=[];t.forEach(t=>{t.featureCollection.features.forEach(t=>{var i,r;let l=t.id.split(".")[0];t.properties.layername=l,t.properties.subFeatureIndex=t.properties.globalid,null!==(i=e.style)&&void 0!==i&&null!==(i=i[l])&&void 0!==i&&i.modelHeight&&(t.properties.modelHeight=Number(t.properties.d_s)*((null===(r=e.style)||void 0===r||null===(r=r[l])||void 0===r||null===(r=r.modelHeight)||void 0===r?void 0:r.scale)||1))}),l=l.concat(t.featureCollection.features)});let o=this.createFeatureCollection(l),a=this.createThreeJsonTilesLayer(e.id,{style:r,altitude:null===(i=this.options.opt3D)||void 0===i?void 0:i.pipeZOffset,zooms:e.zooms});a.addData({layerdata:o})})}})}updateThreeDLayers(e,t){var i;let r=null===(i=this.options.baseLayer)||void 0===i||null===(i=i.Mapgeogl)||void 0===i?void 0:i.find(e=>e.visible);if("day"===e?r?this.map.setBasemapstyle("天地图电子,深色"):this.map.setPlaneStyle({color:"#00192E"}):r?t&&this.map.setBasemapstyle(t):this.map.setPlaneStyle({color:"#536987"}),this.threeDreamLayer){let t=[],i=[];this.options.opt3D.threeDLayers.forEach(r=>{var l,o;r.id.includes(e)?"vector"!=r.layerType&&(null===(l=this.getLayerById(r.id))||void 0===l||l.hide()):(null===(o=this.getLayerById(r.id))||void 0===o||o.show(),"vector"!=r.layerType||("vector"===r.layerType&&"build"!=r.type&&"water"!=r.type?t.push(r):i.push(r)))}),t.forEach(i=>{if(i.style&&i.nightStyle){let r=this.getLayerById(t[0].id),l="day"===e?i.nightStyle:i.style,o={type:"polygonMaterial",polygonFill:l.polygonFill};r.RevisingStyle(i.style.layername,o)}}),i.forEach(t=>{if("build"===t.type){var i;let r=null===(i=this.options.opt3D)||void 0===i?void 0:i.threeDLayers.find(e=>e.id===t.id),l="day"===e?t.nightStyle:t.style;null===r||void 0===r||r.style.forEach(e=>{let t=l.find(t=>t.name.split(",").find(t=>t===e.name)),i={"rendering-type":t["rendering-type"],material:{map:t.material.map,uvTransformScale:t.material.uvTransformScale,uvTransformOffset:t.material.uvTransformOffset}};this.getLayerById(r.id).RevisingStyle(e.name,i)})}else if("water"===t.type){let i=this.getLayerById(t.id),r="day"===e?t.nightStyle:t.style,l={type:t.type,color:r.color};i.RevisingStyle(i.id,l)}})}else this.initThreeD(e)}createVectorLayer(e){if(Array.isArray(e)){let t=[];if(e.forEach(e=>{if("polygonMaterial"===e.type)t.push({...e.style,type:e.type,name:e.style.layername});else if("gltf"===e.type)e.style.name=e.style.layername,e.style.type=e.type,t.push(e.style);else if("Label"===e.type){let i=this.getTextStyleOpt({color:"#737272",outLineColor:"#737272",fontSpacing:0,fontSize:10,outLineWidth:.5,fontWeight:"normal"});i.layername=e.style.layername,i.name=e.style.layername,i.textProperties="{fname}",i=Object.assign(i,e.style||{}),t.push(e)}}),!e.length)return;let i=e[0].url.split("/").findIndex(e=>"geoserver"===e),r=e[0].url.split("/").slice(0,i+1).join("/")+"/gwc/service/wmts",l=Object.assign(e[0],{url:r});this.addVectorWMTS({layer:l,style:t})}else{let i=e.url.split("/").findIndex(e=>"geoserver"===e),r=e.url.split("/").slice(0,i+1).join("/")+"/gwc/service/wmts";if("build"===e.type){let t={name:e.id,type:"fill-extrusion","rendering-type":"gradient-color","gradient-colors":["#679AF0","#3775E9"],opacity:.6,attribute:{field:"floor",scale:4}};t=Array.isArray(e.style)?e.style:Object.assign(t,e.style);let i=new mapgeogl.Layer.BuildLayer({layerid:e.id,url:`${r}?REQUEST=GetTile&SERVICE=WMTS&VERSION=1.0.0&LAYER=${e.layers}&STYLE=&TILEMATRIX=EPSG:3857:{z}&TILEMATRIXSET=EPSG:3857&FORMAT=application/vnd.mapbox-vector-tile&TILECOL={x}&TILEROW={y}`,style:t,altitude:e.altitude||0,visible:e.visible,zIndex:1});i&&this.map.addLayer(i)}else if("water"===e.type){var t;let i={name:e.id,type:"water",layername:(null===(t=e.layers.split(":"))||void 0===t?void 0:t[1])||"sx_hydrgn",color:"#327DF3",opacity:.8};i=Object.assign(i,e.style);let l=new mapgeogl.Layer.WaterLayer({layerid:e.id,height:0,url:`${r}?REQUEST=GetTile&SERVICE=WMTS&VERSION=1.0.0&LAYER=${e.layers}&STYLE=&TILEMATRIX=EPSG:3857:{z}&TILEMATRIXSET=EPSG:3857&FORMAT=application/vnd.mapbox-vector-tile&TILECOL={x}&TILEROW={y}`,style:[i],visible:e.visible,zIndex:1});l&&this.map.addLayer(l)}else if("Label"===e.type){let t=this.getTextStyleOpt({name:e.id,color:"#336699",outLineColor:"#336699",fontSpacing:0,fontSize:10,outLineWidth:.5,fontWeight:"normal"});t.layername=e.layers.split(":")[1]||e.layers,t.textProperties="{fname}",t=Object.assign(t,e.style||{});let i=Object.assign(e,{url:r});this.addVectorWMTS({layer:i,style:[t]})}}}create3dtileLayer(e){let t={layerid:e.id,zooms:[this.options.mapOptions.minZoom,this.options.mapOptions.maxZoom],url:e.url,isOffset:!0,heightOffset:0,scale:.95,maximumScreenSpaceError:18,rotation:[0,0,0],ambientLight:[.3,.3,.3],opacity:1,geometryEvents:!1,visible:e.visible,zIndex:1};t=Object.assign(t,e.style);let i=new mapgeogl.Layer.ThreeTilesLayer(t);this.map.addLayer(i)}createGlbLayer(e){}async createGeojsonLayer(subItem){let{featureCollection:featureCollection}=await this.queryFeatures(subItem),style;if("polygonMaterial"===subItem.type||"polygonBasic"===subItem.type){if(style={id:subItem.id,type:"geojson",dreamType:subItem.type,data:featureCollection,material:{color:"#22FB00"},unifiedMaterial:!0,height:.2,bottomHeight:.12},style=Object.assign(style,subItem.style),style){let e=this.createThreeDFeature(style);this.addThreeDreamFeature(e)}}else if("Path"===subItem.type){if(style={id:subItem.id,type:"geojson",dreamType:"Path",data:featureCollection,unifiedMaterial:!1,material:{isUrl:!0},width:5,height:.2,bottomHeight:0,bloom:!1,animation:!1,collision:!0},style){let e=this.createThreeDFeature(style);this.addThreeDreamFeature(e)}}else if("Label"===subItem.type){let e=this.createGraphicsLayer(subItem.id,{zooms:subItem.zooms,collision:!0});featureCollection.features.forEach(t=>{style=this.getTextStyleOpt({text:t.properties.fname,color:"#ffffff",outLineColor:"#000000",fontSpacing:0,fontSize:10,outLineWidth:1,fontWeight:"normal"}),style.height=2,style=Object.assign(style,subItem.style||{});let i=this.createFeature(t.geometry,style,t.properties);this.addFeatureToLayer(e,i)})}else if("build"===subItem.type&&Object.keys(subItem.style).length>1&&JSON.stringify(subItem.style).includes("filter")){let features={},elseFilter=[],property="";for(let key in subItem.style){let filter=subItem.style[key].filter;"object"===typeof filter?(features[key]=featureCollection.features.filter(o=>eval(`${o.properties[filter.key]}${filter.operate}${filter.value}`)),elseFilter.push(`${filter.operate}${filter.value}`),property=filter.key):"else"===filter&&(featureCollection.features.filter(o=>!eval("o.properties.floor>4")),features[key]=featureCollection.features.filter(o=>eval(elseFilter.map(e=>`!(${o.properties[property]}${e})`).join(" && "))))}for(let e in features)switch(e){case"ImportantBuild":style={type:"feature",dreamType:"ImportantBuild",style:{asynchronous:!1,hologramColor:"#E374FF",hologramBrightness:.5,fresnelAmount:1,scanlineSize:2.1,signalSpeed:.4,fresnelOpacity:0,enableBlinking:!1}},style.style=Object.assign(style.style,subItem.style[e].style||{}),features[e].forEach(e=>{style.id=`${subItem.id}-${e.id}`,style.data=e,style.style.height=3*e.properties.floor;let t=this.createThreeDFeature(style);this.addThreeDreamFeature(t)});break;case"NightBuild":style={id:subItem.id,type:"features",dreamType:"NightBuild",data:features[e],unifiedMaterial:!0,style:{interactive:!1,color:"#005eff",topcolor:"#2270f6",gradient:!0,lightcolor:"#57eff4",circletime:3,borderwidth:.002,opacity:.8,importtant:[]}},style.style=Object.assign(style.style,subItem.style[e].style||{});let t=this.createThreeDFeature(style);this.addThreeDreamFeature(t);break}}}createModelFeature(e,t={}){let i={translationX:0,translationY:25,translationZ:0,scaleX:1.875,scaleY:1.875,scaleZ:1.875,rotationX:0,rotationY:0,rotationZ:0,bloom:!1,shadow:!1,ssr:!0,shader:"pbr",material:{materialFill:[1,1,1,1],metallic:0,roughness:0}};i=Object.assign(i,t);let r=new mapgeogl.Feature.Model({position:e,symbol:i});return r}createModelLayer(e,t){let i=new mapgeogl.Layer.ModelLayer({layerid:e,visible:t.visible,zIndex:1});return i.setMap(this.map),i}createThreeDFeature(e){return new mapgeogl.Feature.ThreeDream(e)}addThreeDreamFeature(e){Array.isArray(e)?e.forEach(e=>e.add(this.threeDreamLayer)):e.add(this.threeDreamLayer)}createThreeJsonTilesLayer(e,t={}){var i,r;let l=new mapgeogl.Layer.ThreeJsonTilesLayer({layerid:e,style:t.style,minZoom:(null===t||void 0===t||null===(i=t.zooms)||void 0===i?void 0:i[0])||this.options.mapOptions.minZoom,maxZoom:(null===t||void 0===t||null===(r=t.zooms)||void 0===r?void 0:r[1])||this.options.mapOptions.maxZoom,altitude:t.altitude||0});return l.setMap(this.map),l.on("dataload",(e,t)=>{console.log("dataload===dataload")}),l}clearThreeDreamLayer(){if(this.threeDreamLayer)if(this.options.opt3D.threeDLayers.find(e=>"geojson"===e.layerType)){let e=this.options.opt3D.threeDLayers.filter(e=>"geojson"===e.layerType).map(e=>e.id);this.threeDreamLayer.getFeatureAll().forEach(t=>{e.includes(t.id.split("-")[0])||this.threeDreamLayer.removeFeature(t)})}else this.threeDreamLayer.clearAll()}getFieldDistinctValue(e,t,i){return new Promise((e,r)=>{_utils_queryAxios_js__WEBPACK_IMPORTED_MODULE_11__["a"].queryApi(window.config.apiPath+"/api/Gis_Manage/GetDistinctValueForTableId",{tableId:t,Fileds:i}).then(t=>{var i;const r=null===(i=t.data.Data)||void 0===i?void 0:i.Result;let l=[];r?(r.forEach(e=>{e&&"null"!==e&&null!==e&&" "!==e&&l.push(e)}),e({results:l})):e({results:l})}).catch(e=>{r({msg:"获取失败",error:e})})})}layerControl(e,t,i){let r=this.map.getLayerById(e);if(r){let v=[];for(var l in this.layerInfos){let t=this.removeChineseCharacters(l);if(t===e){let e=this.layerInfos[l];v=e.style}}let T=[];if(T=e===t?v:v.filter(e=>{if(e.layername===t)return e}),t.includes("gsgx_pipe")){let e=this.map.getLayerById("gsgx_pipe_anno-wms"),t=this.map.getLayerById("gsgx_pipe_arrow-wms");i?(null===e||void 0===e||e.show(),null===t||void 0===t||t.show()):(null===e||void 0===e||e.hide(),null===t||void 0===t||t.hide())}var o,a,s,n,p,y,d,h,u,m,g,c,_,f,L;if(t.includes("wsgx_pipe"))if(i)null===(o=this.map.getLayerById("wsgx_pipe_anno-wms"))||void 0===o||o.show(),this.map.getLayerById("wsgx_pipe_arrow-wms").show();else null===(a=this.map.getLayerById("wsgx_pipe_anno-wms"))||void 0===a||a.hide(),null===(s=this.map.getLayerById("wsgx_pipe_arrow-wms"))||void 0===s||s.hide();if(t.includes("wsss_well"))if(i)null===(n=this.map.getLayerById("wsss_well_anno-wms"))||void 0===n||n.show();else null===(p=this.map.getLayerById("wsss_well_anno-wms"))||void 0===p||p.hide();if(e===t)if(t.includes("ps"))if(i)null===(y=this.map.getLayerById("wsss_well_anno-wms"))||void 0===y||y.show(),null===(d=this.map.getLayerById("wsgx_pipe_anno-wms"))||void 0===d||d.show(),null===(h=this.map.getLayerById("wsgx_pipe_arrow-wms"))||void 0===h||h.show();else null===(u=this.map.getLayerById("wsss_well_anno-wms"))||void 0===u||u.hide(),null===(m=this.map.getLayerById("wsgx_pipe_anno-wms"))||void 0===m||m.hide(),null===(g=this.map.getLayerById("wsgx_pipe_arrow-wms"))||void 0===g||g.hide();else if(i)null===(c=this.map.getLayerById("gsgx_pipe_anno-wms"))||void 0===c||c.show(),null===(_=this.map.getLayerById("gsgx_pipe_arrow-wms"))||void 0===_||_.show();else null===(f=this.map.getLayerById("gsgx_pipe_anno-wms"))||void 0===f||f.hide(),null===(L=this.map.getLayerById("gsgx_pipe_arrow-wms"))||void 0===L||L.hide();T.map(e=>{try{if(null!=e.isLayer&&0==e.isLayer)return;i?r.RevisingVisible(e.name,1):r.RevisingVisible(e.name,0)}catch(t){}})}}removeChineseCharacters(e){return e.replace(/[\u4e00-\u9fa5]/g,"")}}__webpack_exports__["a"]=Mapgeogl}}]);