import logging
import sys
from pathlib import Path
from loguru import logger


def setup_logging():
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    """设置日志配置"""
    # 移除默认的logger
    logger.remove()

    # 添加控制台输出（仅在非noconsole模式下）
    if sys.stdout is not None:  # 检查stdout是否存在
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
        )

    # 添加文件输出
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)

    logger.add(
        log_dir / "WimTask.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="20 MB",
        retention="1 days",
        compression="zip",
    )
