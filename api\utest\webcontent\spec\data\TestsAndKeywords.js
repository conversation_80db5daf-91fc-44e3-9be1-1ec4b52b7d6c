window.testsAndKeywordsOutput = {};

window.testsAndKeywordsOutput["suite"] = [1,2,3,0,[],[1,0,12],[],[[4,0,0,[],[1,10,2],[[0,5,0,0,0,0,0,0,[1,10,0],[[0,6,7,0,8,0,0,0,[1,10,0],[]]]],[0,9,0,0,0,0,0,0,[1,10,0],[[0,6,7,0,8,0,0,0,[1,10,0],[]]]],[0,10,0,0,0,0,0,0,[1,11,0],[[0,6,7,0,8,0,0,0,[1,11,0],[]]]],[0,11,0,0,0,0,0,0,[1,11,0],[[0,6,7,0,8,0,0,0,[1,11,0],[]]]]]],[12,0,0,[],[1,11,0],[[0,6,7,0,8,0,0,0,[1,11,0],[]]]],[13,0,0,[],[1,12,0],[[0,6,7,0,8,0,0,0,[1,12,0],[]]]],[14,0,0,[],[1,12,0],[[0,6,7,0,8,0,0,0,[1,12,0],[]]]]],[],[4,4,0,0]];

window.testsAndKeywordsOutput["strings"] = [];

window.testsAndKeywordsOutput["strings"] = window.testsAndKeywordsOutput["strings"].concat(["*","*TestsAndKeywords","*/home/<USER>/Devel/robotframework/utest/webcontent/spec/data/TestsAndKeywords.robot","*utest/webcontent/spec/data/TestsAndKeywords.robot","*Test 1","*kw1","*No Operation","*BuiltIn","*<p>Does absolutely nothing.\x3c/p>","*kw2","*kw3","*kw4","*Test 2","*Test 3","*Test 4"]);

window.testsAndKeywordsOutput["stats"] = [[{"elapsed":"00:00:00","fail":0,"label":"All Tests","pass":4,"skip":0}],[],[{"elapsed":"00:00:00","fail":0,"id":"s1","label":"TestsAndKeywords","name":"TestsAndKeywords","pass":4,"skip":0}]];

window.testsAndKeywordsOutput["errors"] = [];

window.testsAndKeywordsOutput["baseMillis"] = 1724172740235;

window.testsAndKeywordsOutput["generated"] = 14;

window.testsAndKeywordsOutput["expand_keywords"] = null;

window.settings = {"background":{"fail":"DeepPink"},"logURL":"log.html","reportURL":"report.html"};

