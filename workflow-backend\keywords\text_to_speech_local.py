import pyttsx3
from typing import Optional, List


class LocalTextToSpeech:
    def __init__(self):
        # 初始化语音引擎
        self.engine = pyttsx3.init()
        # 获取可用语音
        self.voices = self.engine.getProperty("voices")
        self._show_available_voices()

    def _show_available_voices(self):
        """显示系统中可用的语音"""
        print("可用语音列表：")
        for i, voice in enumerate(self.voices):
            print(f"语音 {i}:")
            print(f"  ID: {voice.id}")
            print(f"  名称: {voice.name}")
            print(f"  语言: {voice.languages[0] if voice.languages else '未知'}\n")

    def set_voice(self, voice_id: int) -> bool:
        """
        设置语音
        :param voice_id: 语音ID（从可用语音列表中选择）
        :return: 是否设置成功
        """
        if 0 <= voice_id < len(self.voices):
            self.engine.setProperty("voice", self.voices[voice_id].id)
            return True
        print(f"错误：语音ID {voice_id} 不存在")
        return False

    def set_rate(self, rate: int = 150):
        """
        设置语速
        :param rate: 语速（默认150，范围一般在50-300之间）
        """
        self.engine.setProperty("rate", rate)
        print(f"已设置语速：{rate}")

    def set_volume(self, volume: float = 1.0):
        """
        设置音量
        :param volume: 音量（0.0-1.0之间）
        """
        volume = max(0.0, min(1.0, volume))  # 确保在有效范围
        self.engine.setProperty("volume", volume)
        print(f"已设置音量：{volume:.1f}")

    def speak(self, text: str, wait: bool = True):
        """
        播报文本
        :param text: 要播报的文本
        :param wait: 是否等待播报完成（True-阻塞，False-非阻塞）
        """
        if not text.strip():
            print("警告：无有效文本可播报")
            return

        self.engine.say(text)

        if wait:
            self.engine.runAndWait()  # 等待播报完成
        else:
            self.engine.startLoop(False)  # 非阻塞模式
            self.engine.iterate()

    def speak_multiple(self, texts: List[str], interval: float = 0.5):
        """
        播报多条文本
        :param texts: 文本列表
        :param interval: 文本间的间隔时间（秒）
        """
        import time

        for i, text in enumerate(texts):
            print(f"播报第 {i + 1}/{len(texts)} 条文本")
            self.speak(text)
            if i < len(texts) - 1:
                time.sleep(interval)


def text_to_speech(text=""):
    tts = LocalTextToSpeech()
    # 配置语音参数（可根据可用语音列表调整）
    tts.set_voice(1)  # 选择第1个语音（0为索引）
    tts.set_rate(160)  # 设置语速
    tts.set_volume(0.9)  # 设置音量
    tts.speak(text)
