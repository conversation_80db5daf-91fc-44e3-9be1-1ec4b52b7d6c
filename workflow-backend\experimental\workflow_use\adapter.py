"""
Workflow-Use 适配器
将workflow-use的功能适配到WimTask的工作流系统中
"""

import typing

from loguru import logger

from utils.path import get_resource_path

if hasattr(typing, "_ProtocolMeta"):
    typing._ProtocolMeta._is_protocol = lambda *_: False  # 禁用协议检查

import asyncio
import json
import logging
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# 添加workflow-use路径到Python路径
# 替换路径
# workflow_use_path = (
#     Path(__file__).parent.parent / "workflow-use" / "workflow-use-main" / "workflows"
# )
def get_workflow_use_path():
    """获取正确的workflow-use路径，兼容打包后的exe环境"""
    try:
        if getattr(sys, 'frozen', False):
            # 打包环境，使用 _MEIPASS 获取资源路径
            base_path = sys._MEIPASS
            return os.path.join(base_path, "experimental", "workflow-use", "workflow-use-main", "workflows")
        else:
            # 开发环境
            return get_resource_path("experimental/workflow-use/workflow-use-main/workflows")
    except Exception:
        # fallback 方案
        if getattr(sys, 'frozen', False):
            return os.path.join(os.path.dirname(sys.executable), "experimental", "workflow-use", "workflow-use-main", "workflows")
        else:
            return get_resource_path("experimental/workflow-use/workflow-use-main/workflows")

# 应用新的路径获取方法
workflow_use_path = get_workflow_use_path()
sys.path.insert(0, str(workflow_use_path))


# 简单的转换，最好还是从插件处直接给出标准的xpath
def playwright_to_xpath(playwright_selector: str) -> str:
    # 处理 id() 语法
    if 'id("' in playwright_selector:
        playwright_selector = playwright_selector.replace('id("', '//*[@id="').replace(
            '")', '"]'
        )

    # 处理 text() 语法 (简化版，实际可能需要更复杂的处理)
    if 'text("' in playwright_selector:
        playwright_selector = playwright_selector.replace(
            'text("', '//*[contains(text(), "'
        ).replace('")', '")]')

    # 处理其他 Playwright 特定语法...

    return playwright_selector


try:
    from workflow_use import Workflow, WorkflowDefinitionSchema
    from workflow_use.recorder.service import RecordingService
    from workflow_use.controller.service import WorkflowController
    from workflow_use.builder.service import BuilderService
except ImportError as e:
    logging.warning(f"无法导入workflow-use模块: {e}")

    # 创建占位符类以避免导入错误
    class Workflow:
        pass

    class WorkflowDefinitionSchema:
        pass

    class RecordingService:
        pass

    class WorkflowController:
        pass

    class BuilderService:
        pass


class WorkflowUseAdapter:
    """
    Workflow-Use 适配器类
    负责将workflow-use的功能适配到WimTask的工作流系统中
    """

    def __init__(self, enable_ai_fallback: bool = False):
        """
        初始化适配器

        Args:
            enable_ai_fallback: 是否启用AI回退功能（默认关闭，符合用户偏好）
        """
        self.enable_ai_fallback = enable_ai_fallback
        self.recording_service = None
        self.controller = None
        self.builder_service = None
        self.current_workflow = None

        # 初始化服务
        self._initialize_services()

        logger.info(
            f"WorkflowUse适配器初始化完成，AI回退: {'启用' if enable_ai_fallback else '禁用'}"
        )

    def _initialize_services(self):
        """初始化workflow-use服务"""
        try:
            # 初始化录制服务（不需要LLM）
            self.recording_service = RecordingService()

            # 初始化控制器（确定性执行，不需要LLM）
            # 初始化控制器 - 绕过协议检查
            try:
                from typing import TYPE_CHECKING

                if not TYPE_CHECKING:  # 仅在运行时绕过检查
                    self.controller = WorkflowController.__new__(WorkflowController)
                    # self.controller.__init__()
                else:
                    self.controller = WorkflowController()
            except Exception as e:
                logger.warning(f"WorkflowController初始化失败(已绕过协议检查): {e}")
                self.controller = None

            # 只有在启用AI回退时才初始化BuilderService
            if self.enable_ai_fallback:
                try:
                    # 这里可能需要LLM，但我们优先使用非AI方案
                    self.builder_service = BuilderService()
                except Exception as e:
                    logger.warning(f"BuilderService初始化失败，将使用非AI方案: {e}")
                    self.builder_service = None

            logger.info("Workflow-Use服务初始化成功")

        except Exception as e:
            logger.error(f"初始化workflow-use服务失败: {e}")
            # 设置为None以便后续检查
            self.recording_service = None
            self.controller = None
            self.builder_service = None

    def convert_wimtask_to_workflow_use(
        self, wimtask_nodes: List[Dict]
    ) -> Dict[str, Any]:
        """
        将WimTask的工作流节点转换为workflow-use格式

        Args:
            wimtask_nodes: WimTask格式的工作流节点列表

        Returns:
            workflow-use格式的工作流定义
        """
        try:
            workflow_steps = []

            for i, node in enumerate(wimtask_nodes):
                step = self._convert_node_to_step(node, i)
                if step:
                    workflow_steps.append(step)

            # 创建workflow-use格式的工作流定义
            workflow_definition = {
                "name": "WimTask转换工作流",
                "description": f"从WimTask转换的工作流，包含{len(workflow_steps)}个步骤",
                "version": "1.0.0",
                "steps": workflow_steps,
                "input_schema": [],
            }

            logger.info(
                f"成功转换{len(wimtask_nodes)}个WimTask节点为{len(workflow_steps)}个workflow-use步骤"
            )
            return workflow_definition

        except Exception as e:
            logger.error(f"转换WimTask节点失败: {e}")
            return None

    def _convert_node_to_step(self, node: Dict, index: int) -> Optional[Dict]:
        """将单个WimTask节点转换为workflow-use步骤"""
        try:
            node_type = node.get("type", "")
            config = node.get("config", {})

            # 根据节点类型转换
            if node_type == "click" or node_type == "click_element":
                return {
                    "type": "click",
                    "description": node.get("label", f"点击操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "elementText": config.get("text", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "fill_text" or node_type == "input_text":
                return {
                    "type": "input",
                    "description": node.get("label", f"输入操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "value": config.get("text", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "new_browser":
                return {
                    "type": "navigation",
                    "description": node.get("label", f"打开浏览器 {index + 1}"),
                    "url": config.get("url", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "go_to":
                return {
                    "type": "navigation",
                    "description": node.get("label", f"导航操作 {index + 1}"),
                    "url": config.get("url", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "key_press":
                return {
                    "type": "key_press",
                    "description": node.get("label", f"按键操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "key": config.get("key", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "scroll":
                return {
                    "type": "scroll",
                    "description": node.get("label", f"滚动操作 {index + 1}"),
                    "scrollX": config.get("x", 0),
                    "scrollY": config.get("y", 0),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "navigate_to":
                return {
                    "type": "navigation",
                    "description": node.get("label", f"导航操作 {index + 1}"),
                    "url": config.get("url", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "hover_element":
                return {
                    "type": "hover",
                    "description": node.get("label", f"悬停操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "select_option":
                return {
                    "type": "select",
                    "description": node.get("label", f"选择操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "value": config.get("option_value", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "get_text":
                return {
                    "type": "get_text",
                    "description": node.get("label", f"获取文本 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "get_attribute":
                return {
                    "type": "get_attribute",
                    "description": node.get("label", f"获取属性 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "attribute": config.get("attribute_name", "value"),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "wait_for_element":
                return {
                    "type": "wait_for_element",
                    "description": node.get("label", f"等待元素 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "condition": config.get("wait_condition", "visible"),
                    "timeout": config.get("timeout", 30),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "scroll_to_element":
                return {
                    "type": "scroll_to_element",
                    "description": node.get("label", f"滚动到元素 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "check_checkbox":
                action = config.get("action", "check")
                return {
                    "type": "checkbox" if action == "check" else "uncheck",
                    "description": node.get("label", f"复选框操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "checked": action == "check",
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "upload_file":
                return {
                    "type": "upload_file",
                    "description": node.get("label", f"文件上传 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "file_path": config.get("file_path", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "switch_frame":
                return {
                    "type": "switch_frame",
                    "description": node.get("label", f"切换框架 {index + 1}"),
                    "frame_selector": config.get("frame_selector", ""),
                    "action": config.get("frame_action", "switch_to"),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "handle_alert":
                return {
                    "type": "alert",
                    "description": node.get("label", f"处理弹窗 {index + 1}"),
                    "action": config.get("action", "accept"),
                    "prompt_text": config.get("prompt_text", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "close_browser":
                return {
                    "type": "close_browser",
                    "description": node.get("label", f"关闭浏览器 {index + 1}"),
                    "close_type": config.get("close_type", "current_page"),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }
            elif node_type == "take_screenshot":
                return {
                    "type": "screenshot",
                    "description": node.get("label", f"截图 {index + 1}"),
                    "cssSelector": config.get("element_selector", ""),
                    "screenshot_type": config.get("screenshot_type", "full_page"),
                    "filename": config.get("filename", "screenshot.png"),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            elif node_type == "send_keys":
                return {
                    "type": "key_press",
                    "description": node.get("label", f"按键操作 {index + 1}"),
                    "cssSelector": config.get("selector", ""),
                    "key": config.get("keys", ""),
                    "timestamp": int(datetime.now().timestamp() * 1000),
                }

            else:
                logger.warning(f"不支持的节点类型: {node_type}")
                return None

        except Exception as e:
            logger.error(f"转换节点失败: {e}")
            return None

    def convert_workflow_use_to_wimtask(self, workflow_definition: Dict) -> List[Dict]:
        """
        将workflow-use格式转换为WimTask格式

        Args:
            workflow_definition: workflow-use格式的工作流定义

        Returns:
            WimTask格式的工作流节点列表
        """
        try:
            wimtask_nodes = []
            steps = workflow_definition.get("steps", [])

            # 预处理：过滤不必要的导航步骤
            filtered_steps = self._filter_redundant_navigation(steps)

            cur_tab = ""

            cur_idx = 0

            tab_idxes = {}

            max_idx = 0

            for i, step in enumerate(filtered_steps):
                tab_changed = False
                tab_id = step.get("tabId", cur_tab)
                if tab_id != cur_tab:
                    if cur_tab != "":
                        tab_changed = True
                    idx = tab_idxes.get(tab_id, None)
                    if idx is None:
                        tab_idxes[tab_id] = max_idx
                        idx = max_idx
                        max_idx = max_idx + 1
                    cur_idx = idx
                    cur_tab = tab_id

                node = self._convert_step_to_node(step, i)
                if node:
                    node["tab_idx"] = cur_idx
                    node["tab_changed"] = tab_changed
                    wimtask_nodes.append(node)

            logger.info(
                f"成功转换{len(steps)}个workflow-use步骤（过滤后{len(filtered_steps)}个）为{len(wimtask_nodes)}个WimTask节点"
            )
            return wimtask_nodes

        except Exception as e:
            logger.error(f"转换workflow-use步骤失败: {e}")
            return []

    def _filter_redundant_navigation(self, steps: List[Dict]) -> List[Dict]:
        """过滤冗余的导航步骤"""
        if not steps:
            return steps

        filtered_steps = []

        for i, step in enumerate(steps):
            step_type = step.get("type", "")

            # 如果不是导航步骤，直接保留
            if step_type != "navigation":
                filtered_steps.append(step)
                continue

            # 检查是否应该跳过这个导航步骤
            should_skip = self._should_skip_navigation(step, i, steps)

            if not should_skip:
                filtered_steps.append(step)
            else:
                logger.info(
                    f"跳过冗余导航步骤: {step.get('description', step.get('url', ''))}"
                )

        return filtered_steps

    def _should_skip_navigation(
        self, step: Dict, index: int, all_steps: List[Dict]
    ) -> bool:
        """判断是否应该跳过导航步骤"""
        if not step or step.get("type") != "navigation":
            return False

        current_url = step.get("url", "")

        # 第一个导航步骤永远不跳过（需要打开浏览器）
        if index == 0:
            return False

        # 获取前一步和后一步
        previous_step = all_steps[index - 1] if index > 0 else None
        next_step = all_steps[index + 1] if index < len(all_steps) - 1 else None

        # 检查是否是连续的相同URL导航
        if previous_step and previous_step.get("type") == "navigation":
            prev_url = previous_step.get("url", "")
            if self._urls_similar(prev_url, current_url):
                logger.info(f"跳过连续的相同URL导航: {current_url}")
                return True

        # 如果前一步是输入或按键操作，且URL相同，可能是误判的导航
        if previous_step:
            prev_type = previous_step.get("type")
            prev_url = previous_step.get("url", "")

            # 特别检查：如果前一步是按键操作（如Tab），且URL相同，很可能是误判
            if prev_type == "key_press" and self._urls_similar(prev_url, current_url):
                # 进一步检查：如果后一步是在同一页面的操作，确认是误判
                if (
                    next_step
                    and next_step.get("type") in ["input", "click"]
                    and self._urls_similar(current_url, next_step.get("url", ""))
                ):
                    logger.info(
                        f"跳过可能的误判导航: {current_url} (前一步: {prev_type})"
                    )
                    return True

        # 检查是否是真正的页面跳转
        if previous_step and next_step:
            prev_url = previous_step.get("url", "")
            next_url = next_step.get("url", "")

            # 如果前后步骤的URL都与当前导航URL相同，可能是误判
            if (
                self._urls_similar(prev_url, current_url)
                and self._urls_similar(current_url, next_url)
                and previous_step.get("type") in ["input", "key_press", "click"]
                and next_step.get("type") in ["input", "key_press", "click"]
            ):
                logger.info(f"跳过中间的重复导航: {current_url}")
                return True

        return False

    def _urls_similar(self, url1: str, url2: str) -> bool:
        """判断两个URL是否相似（忽略查询参数和片段）"""
        if not url1 or not url2:
            return False

        try:
            from urllib.parse import urlparse

            parsed1 = urlparse(url1)
            parsed2 = urlparse(url2)

            # 比较协议、域名和路径
            return (
                parsed1.scheme == parsed2.scheme
                and parsed1.netloc == parsed2.netloc
                and parsed1.path == parsed2.path
            )
        except:
            # 如果解析失败，进行简单的字符串比较
            return url1.split("?")[0].split("#")[0] == url2.split("?")[0].split("#")[0]

    def _optimize_selector(
        self, selector: str, element_text: str = "", step_type: str = ""
    ) -> tuple[str, str]:
        """优化选择器，提高精确性和容错性"""
        if not selector:
            return "", "css"

        # 检测选择器类型
        if selector.startswith("//") or selector.startswith("./"):
            selector_type = "xpath"
        else:
            selector_type = "css"

        # 如果选择器过于宽泛，尝试优化
        broad_selectors = [
            "span",
            "div",
            "a",
            "button",
            "input",
            "p",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
        ]

        # 🎯 更严格的宽泛选择器检测
        is_truly_broad = False

        if selector_type == "css":
            # 检查选择器的精确性
            class_count = selector.count(".")
            attr_count = selector.count("[")
            id_count = selector.count("#")

            # 🎯 只有真正宽泛的选择器才需要优化
            # 如果选择器包含多个类名、属性或ID，说明已经很精确了
            if class_count >= 1 or attr_count >= 1 or id_count >= 1:
                # 选择器已经有足够的标识符，不需要优化
                logger.info(f"选择器已经足够精确，保持不变: {selector}")
                return selector, selector_type

            # 只有纯标签选择器才被认为是宽泛的
            if selector.strip() in broad_selectors:
                is_truly_broad = True

        if selector_type == "css" and is_truly_broad:
            # 🎯 只对真正宽泛的选择器（如纯"span"、"div"）进行优化
            if element_text and element_text.strip():
                # 使用文本匹配来增加精确性
                escaped_text = element_text.replace('"', '\\"').replace("'", "\\'")
                optimized_selector = f'{selector}:text("{escaped_text}")'
                logger.info(f"优化真正宽泛的选择器: {selector} → {optimized_selector}")
                return optimized_selector, selector_type
            else:
                # 如果没有文本内容，添加可见性约束 + 第一个元素
                optimized_selector = f"{selector}:visible >> nth=0"
                logger.info(f"添加可见性约束: {selector} → {optimized_selector}")
                return optimized_selector, selector_type

        # 对于XPath，检查是否过于宽泛
        if selector_type == "xpath" and selector.strip() in [
            f"//{tag}" for tag in broad_selectors
        ]:
            if element_text and element_text.strip():
                # 使用文本内容优化XPath + 第一个匹配
                escaped_text = element_text.replace('"', "'")
                optimized_selector = (
                    f'({selector}[contains(text(), "{escaped_text}")])[1]'
                )
                logger.info(
                    f"优化宽泛XPath(精确+首个): {selector} → {optimized_selector}"
                )
                return optimized_selector, selector_type

        # 对于复杂的CSS选择器，检查是否已经足够精确
        if selector_type == "css" and len(selector) > 20:
            # 如果选择器已经很复杂（包含多个类名或属性），通常已经足够精确
            # 不需要再添加文本匹配，避免产生冲突

            # 检查是否包含多个类名或属性
            class_count = selector.count(".")
            attr_count = selector.count("[")

            if class_count >= 2 or attr_count >= 2:
                # 选择器已经很精确，直接返回
                logger.info(f"保持精确选择器不变: {selector}")
                return selector, selector_type

            # 对于不够精确的复杂选择器，尝试简化
            if "input" in selector and "submit" in selector:
                # 搜索按钮的通用选择器
                simplified_selector = 'input[type="submit"]'
                logger.info(f"简化复杂选择器: {selector} → {simplified_selector}")
                return simplified_selector, selector_type
            # 移除按钮选择器的简化逻辑，保持原始精确选择器

        return selector, selector_type

    def _convert_step_to_node(self, step: Dict, index: int) -> Optional[Dict]:
        """将单个workflow-use步骤转换为WimTask节点"""
        try:
            step_type = step.get("type", "")

            # 确保description永远不为None或空字符串
            description = (
                step.get("description")
                or step.get("elementText")
                or f"操作 {index + 1}"
            )
            if not description or description.strip() == "":
                description = f"{step_type}操作 {index + 1}"

            # 获取选择器信息
            css_selector = step.get("cssSelector", "")
            xpath_selector = step.get("xpath", "")
            element_text = step.get("elementText", "")

            # 优先使用XPath选择器，如果没有则使用CSS
            if xpath_selector:
                xpath_selector = playwright_to_xpath(xpath_selector)
                selector, selector_type = self._optimize_selector(
                    f"{xpath_selector}", element_text, step_type
                )
            elif css_selector:
                selector, selector_type = self._optimize_selector(
                    css_selector, element_text, step_type
                )
            else:
                selector = ""
                selector_type = "css"

            if selector_type == "xpath":
                selector = f"xpath={selector}"

            # 根据步骤类型转换为WimTask组件
            if step_type == "click":
                # 为点击操作生成更具描述性的标签
                element_text = step.get("elementText", "").strip()
                if element_text:
                    label = f'点击 "{element_text}"'
                elif selector:
                    label = f"点击元素 {selector}"
                else:
                    label = f"点击操作 {index + 1}"

                if step.get("isExtract", True):
                    return {
                        "id": f"workflow_use_node_{index}",
                        "type": "get_text",
                        "label": "获取文本",
                        "config": {
                            "selector": xpath_selector,
                            "selector_type": "xpath",
                            "timeout": 10,
                            "retry_times": 1,
                            "retry_delay": 1,
                            "error_handle": "stop",
                            "normalize_space": False,
                            "trim_text": True,
                            "show_monitor": True,
                        },
                        "icon": "action-iconfont icon-congwenbenzhongtiquneirong",
                        "category": "browser",
                        "description": "获取指定元素的文本内容",
                        "componentType": "get_text",
                        "inputs": ["browser_instance"],
                        "outputs": ["text_content"],
                    }

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "click_element",  # 使用WimTask的组件类型
                    "icon": "action-iconfont icon-dianjiyuansu",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "timeout": 10,
                        "wait_after": 1,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "input" or step_type == "fill":
                # 为输入操作生成更具描述性的标签
                input_value = step.get("value", "").strip()
                is_password = step.get("isPassword", False)

                # 在界面显示中掩码密码，但保留真实值用于执行
                display_value = "********" if is_password else input_value
                actual_value = input_value  # 保留真实值用于执行

                if display_value:
                    label = f'输入 "{display_value}"'
                elif selector:
                    label = f"在 {selector} 中输入文本"
                else:
                    label = f"输入操作 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "input_text",  # 使用WimTask的组件类型
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "text": actual_value,  # 使用真实值
                        "timeout": 10,
                        "clear_first": True,
                        "is_password": is_password,  # 标记是否为密码字段
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "navigation":
                # 为导航操作生成更具描述性的标签
                url = step.get("url", "").strip()
                if url:
                    # 提取域名作为标签的一部分
                    try:
                        from urllib.parse import urlparse

                        parsed_url = urlparse(url)
                        domain = parsed_url.netloc or url
                        nav_label = f"访问 {domain}"
                    except:
                        nav_label = (
                            f"访问 {url[:50]}..." if len(url) > 50 else f"访问 {url}"
                        )
                else:
                    nav_label = f"页面导航 {index + 1}"

                # 对于导航操作，如果是第一个节点，创建new_browser，否则创建navigate_to
                if index == 0:
                    return {
                        "id": f"workflow_use_node_{index}",
                        "type": "new_browser",  # 使用WimTask的浏览器组件
                        "label": f"打开浏览器 - {nav_label}",
                        "category": "browser",
                        "config": {
                            "url": step.get("url", ""),
                            "browser": "chromium",
                            "headless": False,
                            "timeout": 30000,
                            "retry_delay": 1,
                            "retry_times": 0,
                        },
                        "inputs": [],
                        "outputs": ["browser_instance"],
                        "screenshot": step.get("screenshot", ""),
                    }
                else:
                    # 后续的导航操作使用页面跳转
                    return {
                        "id": f"workflow_use_node_{index}",
                        "type": "navigate_to",
                        "label": nav_label,
                        "category": "browser",
                        "config": {
                            "url": step.get("url", ""),
                            "timeout": 30,
                        },
                        "inputs": ["browser_instance"],
                        "outputs": [],
                        "screenshot": step.get("screenshot", ""),
                    }

            elif step_type == "keypress" or step_type == "key_press":
                # 为按键操作生成更具描述性的标签
                key = step.get("key", "").strip()
                if key:
                    label = f"按键 {key}"
                else:
                    label = f"按键操作 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "send_keys",  # 使用WimTask的按键组件
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "keys": step.get("key", ""),
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "scroll":
                # 为滚动操作生成更具描述性的标签
                scroll_y = step.get("scrollY", 0)
                scroll_x = step.get("scrollX", 0)

                # 确定滚动方向和距离
                if abs(scroll_y) > abs(scroll_x):
                    # 垂直滚动为主
                    direction = "down" if scroll_y > 0 else "up"
                    amount = abs(scroll_y)
                    label = f'向{"下" if scroll_y > 0 else "上"}滚动 {amount}px'
                elif scroll_x != 0:
                    # 水平滚动
                    direction = "right" if scroll_x > 0 else "left"
                    amount = abs(scroll_x)
                    label = f'向{"右" if scroll_x > 0 else "左"}滚动 {amount}px'
                else:
                    # 默认向下滚动
                    direction = "down"
                    amount = 500
                    label = f"页面滚动 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "scroll_page",  # 使用新添加的页面滚动组件
                    "label": label,
                    "category": "browser",
                    "config": {
                        "direction": direction,
                        "amount": amount,
                        "behavior": "smooth",
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "wait":
                # 为等待操作生成更具描述性的标签
                duration = step.get("duration", 1)
                label = f"等待 {duration} 秒"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "wait",
                    "label": label,
                    "category": "control",
                    "config": {"duration": duration, "unit": "seconds"},
                    "inputs": [],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "hover":
                # 为悬停操作生成更具描述性的标签
                element_text = step.get("elementText", "").strip()
                if element_text:
                    label = f'悬停在 "{element_text}"'
                elif selector:
                    label = f"悬停在 {selector}"
                else:
                    label = f"悬停操作 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "hover_element",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "select":
                # 为选择操作生成更具描述性的标签
                value = step.get("value", "").strip()
                if value:
                    label = f'选择 "{value}"'
                elif selector:
                    label = f"在 {selector} 中选择选项"
                else:
                    label = f"选择操作 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "select_option",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "selection_method": "text",
                        "option_value": step.get("value", ""),
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "get_text" or step_type == "extract_text":
                # 为获取文本操作生成更具描述性的标签
                if selector:
                    label = f"获取 {selector} 的文本"
                else:
                    label = f"获取文本 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "get_text",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": ["text_content"],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "get_attribute" or step_type == "extract_attribute":
                # 为获取属性操作生成更具描述性的标签
                attribute_name = step.get(
                    "attribute", step.get("attributeName", "value")
                )
                if selector and attribute_name:
                    label = f"获取 {selector} 的 {attribute_name} 属性"
                else:
                    label = f"获取元素属性 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "get_attribute",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "attribute_name": attribute_name,
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": ["attribute_value"],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "wait_for_element" or step_type == "wait_element":
                # 为等待元素操作生成更具描述性的标签
                wait_condition = step.get("condition", step.get("state", "visible"))
                if selector:
                    label = f"等待 {selector} 变为 {wait_condition}"
                else:
                    label = f"等待元素 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "wait_for_element",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "wait_condition": wait_condition,
                        "timeout": step.get("timeout", 30),
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "scroll_to_element" or step_type == "scroll_into_view":
                # 为滚动到元素操作生成更具描述性的标签
                if selector:
                    label = f"滚动到 {selector}"
                else:
                    label = f"滚动到元素 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "scroll_to_element",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "timeout": 10,
                        "scroll_behavior": "smooth",
                        "block_position": "center",
                        "inline_position": "nearest",
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif step_type == "take_screenshot":

                node = {
                    "id": f"workflow_use_node_{index}",
                    "type": "take_screenshot",
                    "label": "截图",
                    "description": "对当前页面或指定元素进行截图",
                    "category": "browser",
                    "icon": "Camera",
                    "config": {
                        "error_handle": "stop",
                        "filename": "screenshot.png",
                        "full_page": True,
                        "hide_scrollbars": True,
                        "quality": 80,
                        "retry_delay": 1,
                        "retry_times": 1,
                        "save_path": "./files/snapshots",
                        "selector_type": "xpath",
                        "show_monitor": True,
                        "timeout": 15,
                    },
                }

                if step.get("screenshotType", "full_page"):
                    node.get("config")["screenshot_type"] = "full_page"
                    node.get("config")["full_page"] = True

                return node
            elif (
                step_type == "checkbox"
                or step_type == "check"
                or step_type == "uncheck"
            ):
                # 为复选框操作生成更具描述性的标签
                action = "check" if step_type in ["checkbox", "check"] else "uncheck"
                checked_state = step.get("checked", step_type == "check")

                if selector:
                    label = f'{"选中" if checked_state else "取消选中"} {selector}'
                else:
                    label = f"复选框操作 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "check_checkbox",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "action": "check" if checked_state else "uncheck",
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif (
                step_type == "upload"
                or step_type == "file_upload"
                or step_type == "upload_file"
            ):
                # 为文件上传操作生成更具描述性的标签
                file_path = step.get(
                    "file_path", step.get("filePath", step.get("files", ""))
                )
                if isinstance(file_path, list):
                    file_path = file_path[0] if file_path else ""

                if file_path:
                    import os

                    filename = os.path.basename(file_path)
                    label = f'上传文件 "{filename}"'
                elif selector:
                    label = f"在 {selector} 上传文件"
                else:
                    label = f"文件上传 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "upload_file",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "selector": selector,
                        "selector_type": selector_type,
                        "file_path": file_path,
                        "timeout": 15,
                        "multiple_files": isinstance(step.get("files"), list)
                        and len(step.get("files", [])) > 1,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif (
                step_type == "switch_frame"
                or step_type == "frame"
                or step_type == "iframe"
            ):
                # 为切换框架操作生成更具描述性的标签
                frame_selector = step.get(
                    "frame_selector", step.get("frameSelector", selector)
                )
                frame_action = step.get("action", "switch_to")

                if frame_action == "switch_to_main":
                    label = "切换到主页面"
                elif frame_action == "switch_to_parent":
                    label = "切换到父框架"
                elif frame_selector:
                    label = f"切换到框架 {frame_selector}"
                else:
                    label = f"框架切换 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "switch_frame",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "frame_selector": frame_selector,
                        "selector_type": selector_type,
                        "frame_action": frame_action,
                        "timeout": 10,
                        "wait_for_load": True,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif (
                step_type == "alert"
                or step_type == "confirm"
                or step_type == "prompt"
                or step_type == "handle_alert"
            ):
                # 为弹窗处理操作生成更具描述性的标签
                action = step.get("action", "accept")
                prompt_text = step.get("prompt_text", step.get("text", ""))

                if action == "accept":
                    label = "确认弹窗"
                elif action == "dismiss":
                    label = "取消弹窗"
                elif action == "prompt" and prompt_text:
                    label = f'在弹窗中输入 "{prompt_text}"'
                else:
                    label = f"处理弹窗 {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "handle_alert",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "action": action,
                        "prompt_text": prompt_text,
                        "timeout": 10,
                        "save_alert_text": True,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": ["alert_text"] if step.get("save_text", True) else [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif (
                step_type == "close_browser"
                or step_type == "close"
                or step_type == "quit"
            ):
                # 为关闭浏览器操作生成更具描述性的标签
                close_type = step.get("close_type", "current_page")

                if close_type == "browser":
                    label = "关闭整个浏览器"
                elif close_type == "all_pages":
                    label = "关闭所有页面"
                else:
                    label = "关闭当前页面"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "close_browser",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "close_type": close_type,
                        "force_close": step.get("force", False),
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

            elif (
                step_type == "screenshot"
                or step_type == "take_screenshot"
                or step_type == "capture"
            ):
                # 为截图操作生成更具描述性的标签
                screenshot_type = step.get(
                    "screenshot_type", step.get("type", "full_page")
                )
                filename = step.get("filename", step.get("file", "screenshot.png"))

                if screenshot_type == "element" and selector:
                    label = f"截取元素 {selector}"
                elif screenshot_type == "viewport":
                    label = "截取可视区域"
                else:
                    label = "截取整个页面"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "take_screenshot",
                    "label": label,
                    "category": "browser",
                    "config": {
                        "screenshot_type": screenshot_type,
                        "element_selector": (
                            selector if screenshot_type == "element" else ""
                        ),
                        "selector_type": selector_type,
                        "filename": filename,
                        "quality": step.get("quality", 80),
                        "timeout": 10,
                    },
                    "inputs": ["browser_instance"],
                    "outputs": ["screenshot_path"],
                    "screenshot": step.get("screenshot", ""),
                }

            else:
                logger.warning(f"不支持的步骤类型: {step_type}，将创建通用操作节点")
                # 创建一个通用的操作节点，包含原始信息
                # 确保label永远不为空
                fallback_label = f"未知操作 - {step_type} {index + 1}"

                return {
                    "id": f"workflow_use_node_{index}",
                    "type": "log_message",  # 使用日志组件作为占位符
                    "label": fallback_label,
                    "category": "control",
                    "config": {
                        "message": f"Workflow-Use操作: {step_type}",
                        "level": "INFO",
                        "original_step": step,  # 保留原始步骤信息
                    },
                    "inputs": [],
                    "outputs": [],
                    "screenshot": step.get("screenshot", ""),
                }

        except Exception as e:
            logger.error(f"转换步骤失败: {e}")
            return None

    async def start_recording(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动workflow-use录制

        Args:
            config: 录制配置

        Returns:
            录制结果
        """
        # 隔离日志配置
        import logging
        original_logging_config = None

        try:
            # 保存当前日志配置
            original_logging_config = {
                'level': logging.root.level,
                'handlers': logging.root.handlers[:],
            }

            if not self.recording_service:
                return {"success": False, "error": "RecordingService未初始化"}

            logger.info("开始启动workflow-use录制...")

            # 使用workflow-use的录制服务
            recording_result = await self.recording_service.capture_workflow()

            if recording_result:
                logger.info("Workflow-Use录制完成")
                return {
                    "success": True,
                    "recording_data": recording_result,
                    "message": "Workflow-Use录制完成",
                }
            else:
                return {"success": False, "error": "录制失败或被取消"}

        except Exception as e:
            # 打印错误堆栈
            import traceback
            logger.error(f"启动录制失败: {traceback.format_exc()}")
            return {"success": False, "error": str(e)}
        finally:
            # 恢复日志配置
            try:
                if original_logging_config:
                    logging.root.setLevel(original_logging_config['level'])
                    logging.root.handlers = original_logging_config['handlers']
            except:
                pass

    def is_available(self) -> bool:
        """检查workflow-use是否可用"""
        return self.recording_service is not None and self.controller is not None

    def get_status(self) -> Dict[str, Any]:
        """获取适配器状态"""
        return {
            "available": self.is_available(),
            "ai_fallback_enabled": self.enable_ai_fallback,
            "services": {
                "recording_service": self.recording_service is not None,
                "controller": self.controller is not None,
                "builder_service": self.builder_service is not None,
            },
        }
