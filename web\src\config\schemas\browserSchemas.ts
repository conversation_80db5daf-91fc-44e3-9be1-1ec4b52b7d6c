/**
 * 浏览器相关组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

// Playwright 点击组件
export const clickSchema: ComponentConfigSchema = {
  componentType: 'click',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '元素定位和点击的基本参数',
      icon: 'Mouse',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '点击行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'string',
      label: '元素选择器',
      description: '用于定位页面元素的选择器表达式',
      placeholder: 'button#submit, .btn-primary, text="提交"',
      help: 'Playwright支持CSS选择器、文本选择器等多种定位方式',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '元素选择器不能为空',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 2,
      default: 10000,
      min: 1000,
      max: 60000,
      step: 1000,
      unit: 'ms',
    },

    force: {
      type: 'boolean',
      label: '强制点击',
      description: '即使元素不可见也尝试点击',
      help: '某些情况下元素可能被遮挡但仍可点击',
      group: 'advanced',
      order: 1,
      default: false,
    },

    delay: {
      type: 'number',
      label: '点击延迟',
      description: '点击前的延迟时间',
      group: 'advanced',
      order: 2,
      default: 0,
      min: 0,
      max: 5000,
      step: 100,
      unit: 'ms',
    },

    wait_for_navigation: {
      type: 'boolean',
      label: '等待页面导航',
      description: '点击后是否等待页面导航完成',
      help: '当点击可能导致页面跳转时，启用此选项可确保后续操作在新页面加载完成后执行',
      group: 'advanced',
      order: 3,
      default: true,
    },
  },

  presets: {
    safe: {
      label: '安全模式',
      description: '保守的点击配置，确保稳定性',
      config: {
        timeout: 15000,
        force: false,
        delay: 500,
        wait_for_navigation: true,
      },
    },

    fast: {
      label: '快速模式',
      description: '快速点击配置，适合批量操作',
      config: {
        timeout: 5000,
        force: true,
        delay: 0,
        wait_for_navigation: false,
      },
    },
  },
}

export const fillTextSchema: ComponentConfigSchema = {
  componentType: 'fill_text',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '文本填写的基本参数',
      icon: 'Edit',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '填写行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'string',
      label: '输入框选择器',
      description: '用于定位输入框的选择器表达式',
      placeholder: 'input[name="username"], #email',
      required: true,
      group: 'basic',
      order: 1,
    },

    text: {
      type: 'textarea',
      label: '填写文本',
      description: '要填写的文本内容',
      placeholder: '请输入要填写的文本',
      required: true,
      group: 'basic',
      order: 2,
      rows: 3,
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待输入框出现的最大时间',
      group: 'basic',
      order: 3,
      default: 10000,
      min: 1000,
      max: 60000,
      step: 1000,
      unit: 'ms',
    },

    force: {
      type: 'boolean',
      label: '强制填写',
      description: '即使元素不可见也尝试填写',
      group: 'advanced',
      order: 1,
      default: false,
    },
  },

  presets: {
    safe: {
      label: '安全模式',
      description: '保守的填写配置',
      config: {
        timeout: 15000,
        force: false,
      },
    },

    fast: {
      label: '快速模式',
      description: '快速填写配置',
      config: {
        timeout: 5000,
        force: true,
      },
    },
  },
}

export const clickElementSchema: ComponentConfigSchema = {
  componentType: 'click_element',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '元素定位和点击的基本参数',
      icon: 'Mouse',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '点击行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位页面元素的选择器表达式',
      placeholder: '//button[@id="submit"]',
      help: '支持XPath、CSS选择器等多种定位方式',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '元素选择器不能为空',
        },
      ],
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath', description: '功能强大，支持复杂定位' },
        { label: 'CSS选择器', value: 'css', description: '简洁易用，性能较好' },
        { label: 'ID', value: 'id', description: '通过元素ID定位' },
        { label: '名称', value: 'name', description: '通过元素name属性定位' },
        { label: '类名', value: 'class', description: '通过CSS类名定位' },
        { label: '标签名', value: 'tag', description: '通过HTML标签定位' },
      ],
    },

    click_type: {
      type: 'select',
      label: '点击类型',
      description: '鼠标点击的类型',
      group: 'basic',
      order: 3,
      default: 'left',
      options: [
        { label: '左键单击', value: 'left' },
        { label: '右键单击', value: 'right' },
        { label: '双击', value: 'double' },
        { label: '中键点击', value: 'middle' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 4,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    wait_before: {
      type: 'number',
      label: '点击前等待',
      description: '点击前的等待时间',
      group: 'advanced',
      order: 1,
      default: 0,
      min: 0,
      max: 10,
      step: 0.1,
      precision: 1,
      unit: '秒',
    },

    wait_after: {
      type: 'number',
      label: '点击后等待',
      description: '点击后的等待时间',
      group: 'advanced',
      order: 2,
      default: 0.5,
      min: 0,
      max: 10,
      step: 0.1,
      precision: 1,
      unit: '秒',
    },

    scroll_to_element: {
      type: 'boolean',
      label: '滚动到元素',
      description: '点击前是否滚动到元素位置',
      group: 'advanced',
      order: 3,
      default: true,
    },

    force_click: {
      type: 'boolean',
      label: '强制点击',
      description: '即使元素不可见也尝试点击',
      help: '某些情况下元素可能被遮挡但仍可点击',
      group: 'advanced',
      order: 4,
      default: false,
    },
  },

  presets: {
    safe: {
      label: '安全模式',
      description: '保守的点击配置，确保稳定性',
      config: {
        timeout: 15,
        wait_before: 0.5,
        wait_after: 1,
        scroll_to_element: true,
        force_click: false,
      },
    },

    fast: {
      label: '快速模式',
      description: '快速点击配置，适合批量操作',
      config: {
        timeout: 5,
        wait_before: 0,
        wait_after: 0.2,
        scroll_to_element: false,
        force_click: true,
      },
    },
  },
}

export const inputTextSchema: ComponentConfigSchema = {
  componentType: 'input_text',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '文本输入的基本参数',
      icon: 'Edit',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '输入行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '输入框选择器',
      description: '用于定位输入框的选择器表达式',
      placeholder: '//input[@name="username"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    text: {
      type: 'textarea',
      label: '输入文本',
      description: '要输入的文本内容',
      placeholder: '请输入要填写的文本',
      required: true,
      group: 'basic',
      order: 2,
      rows: 3,
    },

    clear_before: {
      type: 'boolean',
      label: '输入前清空',
      description: '输入前是否清空原有内容',
      group: 'basic',
      order: 3,
      default: true,
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待输入框出现的最大时间',
      group: 'basic',
      order: 4,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    typing_speed: {
      type: 'select',
      label: '输入速度',
      description: '模拟打字的速度',
      group: 'advanced',
      order: 1,
      default: 'normal',
      options: [
        { label: '很慢', value: 'very_slow', description: '每个字符间隔200ms' },
        { label: '慢', value: 'slow', description: '每个字符间隔100ms' },
        { label: '正常', value: 'normal', description: '每个字符间隔50ms' },
        { label: '快', value: 'fast', description: '每个字符间隔20ms' },
        { label: '很快', value: 'very_fast', description: '每个字符间隔5ms' },
        { label: '瞬间', value: 'instant', description: '直接设置值，不模拟打字' },
      ],
    },

    press_enter: {
      type: 'boolean',
      label: '输入后按回车',
      description: '输入完成后是否按回车键',
      group: 'advanced',
      order: 2,
      default: false,
    },

    press_tab: {
      type: 'boolean',
      label: '输入后按Tab',
      description: '输入完成后是否按Tab键',
      group: 'advanced',
      order: 3,
      default: false,
    },
  },
}

// Playwright 新浏览器组件
export const newBrowserSchema: ComponentConfigSchema = {
  componentType: 'new_browser',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '浏览器创建的基本参数',
      icon: 'Monitor',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '浏览器的高级配置选项',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    browser: {
      type: 'select',
      label: '浏览器类型',
      // description: '选择要使用的浏览器引擎',
      // help: 'Playwright支持的浏览器引擎',
      required: true,
      group: 'basic',
      order: 1,
      default: 'chromium',
      options: [
        {
          label: 'Chrome',
          value: 'chromium',
          description: '基于Chromium的浏览器（推荐）',
          icon: 'chrome',
        },
        /*{
          label: 'Firefox',
          value: 'firefox',
          description: 'Mozilla Firefox浏览器',
          icon: 'firefox',
        },
        {
          label: 'WebKit',
          value: 'webkit',
          description: 'Safari使用的WebKit引擎',
          icon: 'safari',
        },*/
      ],
    },

    headless: {
      type: 'boolean',
      label: '无头模式',
      description: '是否以无头模式运行浏览器',
      help: '无头模式下浏览器不会显示界面，运行更快但无法看到执行过程',
      group: 'basic',
      order: 2,
      default: false,
    },


    url: {
      type: 'url',
      label: '网页地址',
      description: '要访问的网页地址',
      placeholder: 'https://www.dlmeasure.com/',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '网页地址不能为空',
        },
        {
          type: 'pattern',
          value: '^https?://.+',
          message: 'URL必须以http://或https://开头',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '默认超时时间',
      description: '浏览器操作的默认超时时间',
      help: '单位为毫秒，Playwright默认为30秒',
      group: 'basic',
      order: 3,
      default: 30000,
      min: 1000,
      max: 300000,
      step: 1000,
      unit: 'ms',
    },
  },

  presets: {
    fast: {
      label: '快速模式',
      description: '无头模式，适合批量处理',
      config: {
        browser: 'chromium',
        headless: true,
        timeout: 15000,
      },
    },

    debug: {
      label: '调试模式',
      description: '有头模式，便于调试',
      config: {
        browser: 'chromium',
        headless: false,
        timeout: 60000,
      },
    },
  },
}

export const newPageSchema: ComponentConfigSchema = {
  componentType: 'new_page',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '页面创建的基本参数',
      icon: 'Plus',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '页面的高级配置选项',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    url: {
      type: 'url',
      label: 'URL地址',
      description: '要打开的网页地址',
      placeholder: 'https://www.example.com',
      help: '请输入完整的网页地址，包含协议（http://或https://）',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: 'URL地址不能为空',
        },
        {
          type: 'pattern',
          value: '^https?://.+',
          message: 'URL必须以http://或https://开头',
        },
      ],
    },

    viewport_width: {
      type: 'number',
      label: '视口宽度',
      description: '页面视口的宽度',
      group: 'advanced',
      order: 1,
      default: 1920,
      min: 320,
      max: 3840,
      unit: 'px',
    },

    viewport_height: {
      type: 'number',
      label: '视口高度',
      description: '页面视口的高度',
      group: 'advanced',
      order: 2,
      default: 1080,
      min: 240,
      max: 2160,
      unit: 'px',
    },
  },

  presets: {
    desktop: {
      label: '桌面尺寸',
      description: '标准桌面分辨率',
      config: {
        viewport_width: 1920,
        viewport_height: 1080,
      },
    },

    mobile: {
      label: '移动设备',
      description: '移动设备分辨率',
      config: {
        viewport_width: 375,
        viewport_height: 667,
      },
    },
  },
}

export const openBrowserSchema: ComponentConfigSchema = {
  componentType: 'new_browser',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '浏览器启动的基本参数',
      icon: 'Monitor',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '浏览器的高级配置选项',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    url: {
      type: 'url',
      label: 'URL地址',
      description: '要打开的网页地址',
      placeholder: 'https://www.example.com',
      help: '请输入完整的网页地址，包含协议（http://或https://）',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: 'URL地址不能为空',
        },
        {
          type: 'pattern',
          value: '^https?://.+',
          message: 'URL必须以http://或https://开头',
        },
      ],
    },

    browser: {
      type: 'select',
      label: '浏览器类型',
      description: '选择要使用的浏览器',
      help: '不同浏览器可能有不同的兼容性和性能表现',
      required: true,
      group: 'basic',
      order: 2,
      default: 'chromium',
      options: [
        {
          label: 'Google Chrome',
          value: 'chromium',
          description: '推荐使用，兼容性最好',
          icon: 'chrome',
        },
        {
          label: 'Mozilla Firefox',
          value: 'firefox',
          description: '开源浏览器，隐私保护好',
          icon: 'firefox',
        },
        {
          label: 'Microsoft Edge',
          value: 'edge',
          description: 'Windows系统默认浏览器',
          icon: 'edge',
        },
        {
          label: 'Safari',
          value: 'safari',
          description: '仅限macOS系统',
          icon: 'safari',
          disabled: false, // 在运行时动态判断
        },
      ],
    },

    headless: {
      type: 'boolean',
      label: '无头模式',
      description: '是否以无头模式运行浏览器',
      help: '无头模式下浏览器不会显示界面，运行更快但无法看到执行过程',
      group: 'basic',
      order: 3,
      default: false,
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '浏览器启动的最大等待时间',
      placeholder: '30',
      help: '如果在指定时间内浏览器未能成功启动，将抛出超时错误',
      group: 'basic',
      order: 4,
      default: 30,
      min: 5,
      max: 300,
      step: 5,
      unit: '秒',
      suffix: 's',
    },

    window_size: {
      type: 'select',
      label: '窗口大小',
      description: '浏览器窗口的初始大小',
      help: '可以选择预设尺寸或自定义大小',
      group: 'advanced',
      order: 1,
      default: '1920x1080',
      options: [
        { label: '1920x1080 (Full HD)', value: '1920x1080' },
        { label: '1366x768 (常见笔记本)', value: '1366x768' },
        { label: '1280x720 (HD)', value: '1280x720' },
        { label: '最大化', value: 'maximized' },
        { label: '自定义', value: 'custom' },
      ],
    },

    custom_width: {
      type: 'number',
      label: '自定义宽度',
      description: '自定义窗口宽度',
      group: 'advanced',
      order: 2,
      min: 320,
      max: 3840,
      unit: 'px',
      conditions: [
        {
          field: 'window_size',
          operator: 'equals',
          value: 'custom',
        },
      ],
    },

    custom_height: {
      type: 'number',
      label: '自定义高度',
      description: '自定义窗口高度',
      group: 'advanced',
      order: 3,
      min: 240,
      max: 2160,
      unit: 'px',
      conditions: [
        {
          field: 'window_size',
          operator: 'equals',
          value: 'custom',
        },
      ],
    },

    user_agent: {
      type: 'textarea',
      label: 'User Agent',
      description: '自定义浏览器标识字符串',
      placeholder: '留空使用默认User Agent',
      help: '某些网站可能会根据User Agent来判断浏览器类型',
      group: 'advanced',
      order: 4,
      rows: 2,
    },

    download_directory: {
      type: 'file',
      label: '下载目录',
      description: '设置文件下载的默认目录',
      help: '如果不设置，将使用系统默认下载目录',
      group: 'advanced',
      order: 5,
      accept: 'directory',
    },

    disable_images: {
      type: 'boolean',
      label: '禁用图片加载',
      description: '禁用图片加载以提高页面加载速度',
      help: '适用于只需要获取文本内容的场景',
      group: 'advanced',
      order: 6,
      default: false,
    },

    disable_javascript: {
      type: 'boolean',
      label: '禁用JavaScript',
      description: '禁用JavaScript执行',
      help: '某些静态页面可以禁用JS来提高加载速度',
      group: 'advanced',
      order: 7,
      default: false,
    },
  },

  presets: {
    fast: {
      label: '快速模式',
      description: '优化速度的配置，适合批量处理',
      config: {
        headless: true,
        disable_images: true,
        timeout: 15,
      },
    },

    debug: {
      label: '调试模式',
      description: '便于调试的配置，窗口可见且超时时间长',
      config: {
        headless: false,
        timeout: 60,
        window_size: '1280x720',
      },
    },

    mobile: {
      label: '移动端模拟',
      description: '模拟移动设备的配置',
      config: {
        window_size: 'custom',
        custom_width: 375,
        custom_height: 667,
        user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
      },
    },
  },

  examples: [
    {
      title: '打开百度首页',
      description: '使用Chrome浏览器打开百度搜索',
      config: {
        url: 'https://www.baidu.com',
        browser: 'chromium',
        headless: false,
        timeout: 30,
      },
    },
    {
      title: '无头模式访问',
      description: '后台模式访问网页，不显示浏览器界面',
      config: {
        url: 'https://httpbin.org/get',
        browser: 'chromium',
        headless: true,
        timeout: 15,
        disable_images: true,
      },
    },
  ],
}

export const getTextSchema: ComponentConfigSchema = {
  componentType: 'get_text',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '文本获取的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '文本处理的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位要获取文本的元素',
      placeholder: '//div[@class=&quot;content&quot;]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 3,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    attribute: {
      type: 'string',
      label: '属性名称',
      description: '获取元素的指定属性值（留空获取文本内容）',
      placeholder: 'title, href, value等',
      group: 'advanced',
      order: 1,
    },

    trim_text: {
      type: 'boolean',
      label: '去除空白字符',
      description: '是否去除文本前后的空白字符',
      group: 'advanced',
      order: 2,
      default: true,
    },

    normalize_space: {
      type: 'boolean',
      label: '标准化空格',
      description: '是否将多个连续空格合并为一个',
      group: 'advanced',
      order: 3,
      default: false,
    },
  },

  presets: {
    simple: {
      label: '简单获取',
      description: '基本的文本获取配置',
      config: {
        timeout: 10,
        trim_text: true,
        normalize_space: false,
      },
    },

    attribute: {
      label: '属性获取',
      description: '获取元素属性值的配置',
      config: {
        timeout: 10,
        attribute: 'value',
        trim_text: true,
      },
    },
  },
}

// 导航到页面组件
export const navigateToSchema: ComponentConfigSchema = {
  componentType: 'navigate_to',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '页面导航的基本参数',
      icon: 'Navigation',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '导航行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    url: {
      type: 'url',
      label: 'URL地址',
      description: '要导航到的网页地址',
      placeholder: 'https://www.example.com',
      help: '请输入完整的网页地址，包含协议（http://或https://）',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: 'URL地址不能为空',
        },
        {
          type: 'pattern',
          value: '^https?://.+',
          message: 'URL必须以http://或https://开头',
        },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '页面加载的最大等待时间',
      group: 'basic',
      order: 2,
      default: 30,
      min: 5,
      max: 300,
      step: 5,
      unit: '秒',
    },

    wait_for_load: {
      type: 'select',
      label: '等待加载状态',
      description: '等待页面达到指定的加载状态',
      group: 'advanced',
      order: 1,
      default: 'networkidle',
      options: [
        { label: '网络空闲', value: 'networkidle', description: '等待网络请求完成' },
        { label: 'DOM加载完成', value: 'domcontentloaded', description: '等待DOM内容加载完成' },
        { label: '页面完全加载', value: 'load', description: '等待所有资源加载完成' },
        { label: '立即返回', value: 'commit', description: '页面开始加载即返回' },
      ],
    },

    referer: {
      type: 'string',
      label: '来源页面',
      description: '设置HTTP Referer头',
      placeholder: 'https://www.google.com',
      group: 'advanced',
      order: 2,
    },
  },

  presets: {
    fast: {
      label: '快速导航',
      description: '快速导航配置，适合简单页面',
      config: {
        timeout: 15,
        wait_for_load: 'domcontentloaded',
      },
    },

    safe: {
      label: '安全导航',
      description: '保守的导航配置，确保页面完全加载',
      config: {
        timeout: 60,
        wait_for_load: 'networkidle',
      },
    },
  },

  examples: [
    {
      title: '导航到百度',
      description: '导航到百度首页',
      config: {
        url: 'https://www.baidu.com',
        timeout: 30,
        wait_for_load: 'networkidle',
      },
    },
  ],
}

// 悬停元素组件
export const hoverElementSchema: ComponentConfigSchema = {
  componentType: 'hover_element',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '元素悬停的基本参数',
      icon: 'Mouse',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '悬停行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位要悬停的元素',
      placeholder: '//button[@id="menu"]',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '元素选择器不能为空',
        },
      ],
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath', description: '功能强大，支持复杂定位' },
        { label: 'CSS选择器', value: 'css', description: '简洁易用，性能较好' },
        { label: 'ID', value: 'id', description: '通过元素ID定位' },
        { label: '名称', value: 'name', description: '通过元素name属性定位' },
        { label: '类名', value: 'class', description: '通过CSS类名定位' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 3,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    force: {
      type: 'boolean',
      label: '强制悬停',
      description: '即使元素不可见也尝试悬停',
      group: 'advanced',
      order: 1,
      default: false,
    },

    position: {
      type: 'select',
      label: '悬停位置',
      description: '在元素的哪个位置悬停',
      group: 'advanced',
      order: 2,
      default: 'center',
      options: [
        { label: '中心', value: 'center' },
        { label: '左上角', value: 'top-left' },
        { label: '右上角', value: 'top-right' },
        { label: '左下角', value: 'bottom-left' },
        { label: '右下角', value: 'bottom-right' },
      ],
    },
  },

  presets: {
    menu: {
      label: '菜单悬停',
      description: '适合菜单展开的悬停配置',
      config: {
        timeout: 15,
        force: false,
        position: 'center',
      },
    },

    tooltip: {
      label: '提示悬停',
      description: '适合显示提示信息的悬停配置',
      config: {
        timeout: 5,
        force: true,
        position: 'center',
      },
    },
  },
}

// 选择下拉选项组件
export const selectOptionSchema: ComponentConfigSchema = {
  componentType: 'select_option',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '下拉选择的基本参数',
      icon: 'List',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '选择行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '下拉框选择器',
      description: '用于定位下拉框的选择器表达式',
      placeholder: '//select[@name="country"]',
      required: true,
      group: 'basic',
      order: 1,
      validation: [
        {
          type: 'required',
          message: '下拉框选择器不能为空',
        },
      ],
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    selection_method: {
      type: 'select',
      label: '选择方式',
      description: '选择选项的方式',
      group: 'basic',
      order: 3,
      default: 'text',
      options: [
        { label: '按文本', value: 'text', description: '根据选项显示文本选择' },
        { label: '按值', value: 'value', description: '根据选项value属性选择' },
        { label: '按索引', value: 'index', description: '根据选项位置索引选择' },
      ],
    },

    option_value: {
      type: 'string',
      label: '选项值',
      description: '要选择的选项值',
      placeholder: '请输入选项文本、值或索引',
      required: true,
      group: 'basic',
      order: 4,
      help: '根据选择方式输入对应的值：文本内容、value属性值或数字索引（从0开始）',
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待下拉框出现的最大时间',
      group: 'basic',
      order: 5,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    multiple: {
      type: 'boolean',
      label: '多选模式',
      description: '是否为多选下拉框',
      group: 'advanced',
      order: 1,
      default: false,
    },

    clear_first: {
      type: 'boolean',
      label: '先清空选择',
      description: '选择前是否先清空已有选择',
      group: 'advanced',
      order: 2,
      default: false,
      conditions: [
        {
          field: 'multiple',
          operator: 'equals',
          value: true,
        },
      ],
    },
  },

  presets: {
    simple: {
      label: '简单选择',
      description: '基本的单选配置',
      config: {
        selection_method: 'text',
        timeout: 10,
        multiple: false,
      },
    },

    multiple_select: {
      label: '多选配置',
      description: '多选下拉框配置',
      config: {
        selection_method: 'text',
        timeout: 15,
        multiple: true,
        clear_first: true,
      },
    },
  },

  examples: [
    {
      title: '选择国家',
      description: '在国家下拉框中选择中国',
      config: {
        selector: '//select[@name="country"]',
        selection_method: 'text',
        option_value: '中国',
        timeout: 10,
      },
    },
  ],
}

// 获取元素属性组件
export const getAttributeSchema: ComponentConfigSchema = {
  componentType: 'get_attribute',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '属性获取的基本参数',
      icon: 'Info',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '属性处理的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位要获取属性的元素',
      placeholder: '//input[@name="username"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    attribute_name: {
      type: 'string',
      label: '属性名称',
      description: '要获取的属性名称',
      placeholder: 'value, href, title, class等',
      required: true,
      group: 'basic',
      order: 3,
      help: '常用属性：value(输入值)、href(链接地址)、title(标题)、class(样式类)、id(元素ID)',
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 4,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    default_value: {
      type: 'string',
      label: '默认值',
      description: '当属性不存在时返回的默认值',
      placeholder: '留空则返回空字符串',
      group: 'advanced',
      order: 1,
    },

    trim_value: {
      type: 'boolean',
      label: '去除空白字符',
      description: '是否去除属性值前后的空白字符',
      group: 'advanced',
      order: 2,
      default: true,
    },
  },

  presets: {
    link_url: {
      label: '获取链接地址',
      description: '获取链接元素的href属性',
      config: {
        attribute_name: 'href',
        timeout: 10,
        trim_value: true,
      },
    },

    input_value: {
      label: '获取输入值',
      description: '获取输入框的value属性',
      config: {
        attribute_name: 'value',
        timeout: 10,
        trim_value: true,
      },
    },

    element_class: {
      label: '获取样式类',
      description: '获取元素的class属性',
      config: {
        attribute_name: 'class',
        timeout: 10,
        trim_value: true,
      },
    },
  },
}

// 等待元素组件
export const waitForElementSchema: ComponentConfigSchema = {
  componentType: 'wait_for_element',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '元素等待的基本参数',
      icon: 'Clock',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '等待行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位要等待的元素',
      placeholder: '//div[@class="loading"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    wait_condition: {
      type: 'select',
      label: '等待条件',
      description: '等待元素达到的状态',
      group: 'basic',
      order: 3,
      default: 'visible',
      options: [
        { label: '可见', value: 'visible', description: '元素在页面上可见' },
        { label: '存在', value: 'attached', description: '元素存在于DOM中' },
        { label: '隐藏', value: 'hidden', description: '元素不可见或不存在' },
        { label: '可点击', value: 'enabled', description: '元素可以被点击' },
        { label: '不可点击', value: 'disabled', description: '元素不可点击' },
        { label: '稳定', value: 'stable', description: '元素位置稳定不变' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待的最大时间',
      group: 'basic',
      order: 4,
      default: 30,
      min: 1,
      max: 300,
      unit: '秒',
    },

    check_interval: {
      type: 'number',
      label: '检查间隔',
      description: '检查元素状态的间隔时间',
      group: 'advanced',
      order: 1,
      default: 1,
      min: 0.1,
      max: 10,
      step: 0.1,
      unit: '秒',
    },

    fail_on_timeout: {
      type: 'boolean',
      label: '超时时失败',
      description: '超时时是否抛出错误',
      group: 'advanced',
      order: 2,
      default: true,
    },
  },

  presets: {
    loading: {
      label: '等待加载',
      description: '等待加载元素消失',
      config: {
        wait_condition: 'hidden',
        timeout: 60,
        fail_on_timeout: true,
      },
    },

    element_appear: {
      label: '等待出现',
      description: '等待元素出现并可见',
      config: {
        wait_condition: 'visible',
        timeout: 30,
        fail_on_timeout: true,
      },
    },

    clickable: {
      label: '等待可点击',
      description: '等待元素变为可点击状态',
      config: {
        wait_condition: 'enabled',
        timeout: 15,
        fail_on_timeout: true,
      },
    },
  },
}

// 滚动到元素组件
export const scrollToElementSchema: ComponentConfigSchema = {
  componentType: 'scroll_to_element',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '滚动操作的基本参数',
      icon: 'ArrowDown',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '滚动行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '元素选择器',
      description: '用于定位要滚动到的元素',
      placeholder: '//div[@id="target"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 3,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    scroll_behavior: {
      type: 'select',
      label: '滚动行为',
      description: '滚动的动画效果',
      group: 'advanced',
      order: 1,
      default: 'smooth',
      options: [
        { label: '平滑滚动', value: 'smooth', description: '带动画效果的平滑滚动' },
        { label: '立即滚动', value: 'instant', description: '立即跳转到目标位置' },
        { label: '自动', value: 'auto', description: '浏览器默认行为' },
      ],
    },

    block_position: {
      type: 'select',
      label: '垂直对齐',
      description: '元素在视口中的垂直位置',
      group: 'advanced',
      order: 2,
      default: 'center',
      options: [
        { label: '顶部', value: 'start', description: '元素顶部对齐视口顶部' },
        { label: '中心', value: 'center', description: '元素中心对齐视口中心' },
        { label: '底部', value: 'end', description: '元素底部对齐视口底部' },
        { label: '就近', value: 'nearest', description: '最小滚动距离' },
      ],
    },

    inline_position: {
      type: 'select',
      label: '水平对齐',
      description: '元素在视口中的水平位置',
      group: 'advanced',
      order: 3,
      default: 'nearest',
      options: [
        { label: '左侧', value: 'start', description: '元素左侧对齐视口左侧' },
        { label: '中心', value: 'center', description: '元素中心对齐视口中心' },
        { label: '右侧', value: 'end', description: '元素右侧对齐视口右侧' },
        { label: '就近', value: 'nearest', description: '最小滚动距离' },
      ],
    },
  },

  presets: {
    smooth_center: {
      label: '平滑居中',
      description: '平滑滚动到元素中心位置',
      config: {
        scroll_behavior: 'smooth',
        block_position: 'center',
        inline_position: 'center',
        timeout: 10,
      },
    },

    quick_view: {
      label: '快速查看',
      description: '立即滚动到元素可见位置',
      config: {
        scroll_behavior: 'instant',
        block_position: 'nearest',
        inline_position: 'nearest',
        timeout: 5,
      },
    },
  },
}

// 复选框操作组件
export const checkboxSchema: ComponentConfigSchema = {
  componentType: 'check_checkbox',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '复选框操作的基本参数',
      icon: 'CheckSquare',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '复选框操作的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '复选框选择器',
      description: '用于定位复选框的选择器表达式',
      placeholder: '//input[@type="checkbox"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    action: {
      type: 'select',
      label: '操作类型',
      description: '对复选框执行的操作',
      group: 'basic',
      order: 3,
      default: 'check',
      options: [
        { label: '选中', value: 'check', description: '选中复选框' },
        { label: '取消选中', value: 'uncheck', description: '取消选中复选框' },
        { label: '切换', value: 'toggle', description: '切换复选框状态' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待复选框出现的最大时间',
      group: 'basic',
      order: 4,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    force: {
      type: 'boolean',
      label: '强制操作',
      description: '即使复选框不可见也尝试操作',
      group: 'advanced',
      order: 1,
      default: false,
    },

    verify_state: {
      type: 'boolean',
      label: '验证状态',
      description: '操作后验证复选框状态是否正确',
      group: 'advanced',
      order: 2,
      default: true,
    },
  },

  presets: {
    agree_terms: {
      label: '同意条款',
      description: '选中同意条款复选框',
      config: {
        action: 'check',
        timeout: 10,
        verify_state: true,
      },
    },

    toggle_option: {
      label: '切换选项',
      description: '切换复选框状态',
      config: {
        action: 'toggle',
        timeout: 10,
        verify_state: true,
      },
    },
  },
}

// 文件上传组件
export const uploadFileSchema: ComponentConfigSchema = {
  componentType: 'upload_file',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '文件上传的基本参数',
      icon: 'Upload',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '文件上传的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    selector: {
      type: 'xpath',
      label: '文件输入框选择器',
      description: '用于定位文件输入框的选择器表达式',
      placeholder: '//input[@type="file"]',
      required: true,
      group: 'basic',
      order: 1,
    },

    selector_type: {
      type: 'select',
      label: '选择器类型',
      description: '选择器表达式的类型',
      group: 'basic',
      order: 2,
      default: 'xpath',
      options: [
        { label: 'XPath', value: 'xpath' },
        { label: 'CSS选择器', value: 'css' },
        { label: 'ID', value: 'id' },
        { label: '名称', value: 'name' },
        { label: '类名', value: 'class' },
      ],
    },

    file_path: {
      type: 'file',
      label: '文件路径',
      description: '要上传的文件路径',
      placeholder: '选择要上传的文件',
      required: true,
      group: 'basic',
      order: 3,
      accept: '*',
      help: '支持绝对路径和相对路径，相对路径基于工作流执行目录',
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待文件输入框出现的最大时间',
      group: 'basic',
      order: 4,
      default: 10,
      min: 1,
      max: 60,
      unit: '秒',
    },

    multiple_files: {
      type: 'boolean',
      label: '多文件上传',
      description: '是否支持多文件上传',
      group: 'advanced',
      order: 1,
      default: false,
    },

    file_paths: {
      type: 'textarea',
      label: '多文件路径',
      description: '多个文件路径，每行一个',
      placeholder: 'C:\\file1.txt\nC:\\file2.txt',
      group: 'advanced',
      order: 2,
      rows: 3,
      conditions: [
        {
          field: 'multiple_files',
          operator: 'equals',
          value: true,
        },
      ],
    },

    verify_upload: {
      type: 'boolean',
      label: '验证上传',
      description: '上传后验证文件是否成功选择',
      group: 'advanced',
      order: 3,
      default: true,
    },
  },

  presets: {
    single_file: {
      label: '单文件上传',
      description: '上传单个文件',
      config: {
        multiple_files: false,
        timeout: 15,
        verify_upload: true,
      },
    },

    multiple_files_preset: {
      label: '多文件上传',
      description: '上传多个文件',
      config: {
        multiple_files: true,
        timeout: 30,
        verify_upload: true,
      },
    },
  },

  examples: [
    {
      title: '上传头像',
      description: '上传用户头像图片',
      config: {
        selector: '//input[@name="avatar"]',
        file_path: 'C:\\Users\\<USER>