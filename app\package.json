{"name": "wimTaskApp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@univerjs/core": "^0.8.3", "@univerjs/preset-sheets-advanced": "^0.8.2", "@univerjs/preset-sheets-conditional-formatting": "^0.8.2", "@univerjs/preset-sheets-data-validation": "^0.8.2", "@univerjs/preset-sheets-drawing": "^0.8.2", "@univerjs/preset-sheets-filter": "^0.8.2", "@univerjs/preset-sheets-hyper-link": "^0.8.2", "@univerjs/presets": "^0.8.2", "axios": "^1.11.0", "echarts": "^5.6.0", "element-plus": "^2.10.5", "markdown-it": "^14.1.0", "moment": "^2.30.1", "pinia": "^3.0.3", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "sass-loader": "^16.0.5", "snowflake-id": "^1.1.0", "vant": "^4.9.21", "vue": "^3.5.13", "vue-json-viewer": "^3.0.4", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass-embedded": "^1.89.2", "terser": "^5.42.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^2.2.8", "wait-on": "^8.0.3"}}