(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-2a6aa068"],{2456:function(t,e,s){},"5ba8":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAiNJREFUOE99k8FrE1EQxmd23UOqJkLEVoNQBElCoT2IgR6KlpIcRQ+CCGv/gHZDDkpJqdAKRdAeQrIgHgQ1iuCl980WgniQ6kVB8upBA6JsxUC6rY0mS56ZZ1s3m20Hln3v7Te/980wi+ATlx6xo40GXgZox6ANAJLEWugsl6eGtrxy9B6kCp80Du27AHC4P6iIz+t2i15bnGPWTEd1d04XIFlgtzv33UnFQ6CeD4MbYLANKK7WgAPMmVpscReyB0jmKxcAsawmwqAmjvtVBsXVn/8gEo6ZU9HXJPoPKFReAOC14o0zYDBbAAjmDiplacWC99+2n5W0mOoBsGYqHlJuTgyA+uQzDAQVUcLwqQCMRPoEh/Y7LholLSYOXQ5YU02EFbJ/a/kr3SKSqB9GZUOsjemoWC+tWP6AkUifcv/KadF1a7MFJWYLBx++N0QilVd8W6N1LyBVYM9PBJXrJPIGJa9vOjAcCYge/LCbTw0tPtlVwkR+bUxC/orqJRd+QckE41weNdNn33QBaJPMV2YBcZGaRXNA9e8M0W73STbTaeC9njkQB5xjUl+bo2Hyc4CIs8Z0lKZ0L3pG+epLLtctNkNO3EIOPGvWOjfPI/0d+wPoy7mH75ST27Dw59CRLO0Dv60H9i+eLs+PO15nPQ5IoGnaFw7SYP3YkNCH6h9BgnZVluXxXC5XPdBBJpMZdBxnEhEvAgA9FFXO+WNd1xe8Dv4CT8zlEZ3mjggAAAAASUVORK5CYII="},"69e9":function(t,e,s){"use strict";s("ff4c2")},"6f190":function(t,e,s){"use strict";s.r(e);var i=(s("d9e2"),s("71b4"),s("14d9"),s("e9f5"),s("910d"),s("f665"),s("7d54"),s("ab43"),s("c73d"),s("88a7"),s("271a"),s("5494"),s("f778")),n=s("c1df"),a=s.n(n),o=s("037d"),l=s("2f62"),r={mixins:[i.a],name:"twinmap",props:{isFullScreen:{type:Boolean,default:!1},agent:{type:String,default:null}},data:()=>({loading:!1,answering:!1,templateIndex:0,templates:[],params:{scrollEnd:!1,isShowScrollBottom:!1,time:null,loading:!1,searchVal:null,answerList:[],keyword:null,fileList:[],sessionId:null,qrLoading:!1,qaRecommendationList:[{title:"界面分析",data:[{name:"停水关阀仿真分析",icon:s("df73"),type:1},{name:"模拟仿真分析",icon:s("9d14"),type:2},{name:"管网运行状态",icon:s("5ba8"),type:3}]}],historyList:[],historyListVis:!1},isSelectAsk:!1,selectVal:null,firstChatName:"",monitorList:[],questionList:["第一次提问","第二次提问","第三次提问"],analysisType:null,pipeList:[],currentPipeList:[],currentIndex:0,answerId:null,searchVal:"",questionVal:"",mapId:null,currentDS:null,beforeDS:null}),computed:{...Object(l.d)("twinmap",["mapData"]),chatHistoryList(){let t=this.params.historyList.filter((t=>!this.params.searchVal||t.title.indexOf(this.params.searchVal)>-1));const e={"当天":[],"最近7天":[]};return t.forEach((t=>{const s=a()().add(-7,"d").format("YYYY-MM-DD"),i=a()(t.created).isSame(a()(),"day");i?e["当天"].push(t):!i&&a()().diff(s,"day")<=7&&e["最近7天"].push(t)})),e},chatHistoryListLen(){let t=0;return Object.values(this.chatHistoryList).forEach((e=>{t+=e.length})),t},aiModelListFilter(){return this.aiModelList.length?this.tools.keyword?this.aiModelList.filter((t=>t.name.toLowerCase().indexOf(this.tools.keyword.toLowerCase())>-1)):this.aiModelList:[]}},watch:{"params.answerList":{immediate:!0,handler(){this.onResetScrollbar(),this.getListSession()}},aiModelListFilter:{immediate:!0,handler(t){t.length||(this.tools.visible=!1)}},"params.keyword":{immediate:!0,handler(t){t||(this.tools.visible=!1)}}},mounted(){this.scrollBind(),window.addEventListener("message",this.receiveMapParams,!1)},activated(){this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_enter`,path:"孪生图",functionName:"进入页面"})},methods:{uploadExceed(t,e){this.$set(e[0],"raw",t[0]),this.$set(e[0],"name",t[0].name),this.$refs.fileUploader.clearFiles(),this.$refs.fileUploader.handleStart(t[0])},getRecommend(t=!1){if(!this.params.qrLoading){if(t&&(this.params.qrLoading=!0),this.templates.length>6&&this.params.qaRecommendationList.length>=6){this.templateIndex++;let t=this.templates.slice(6*this.templateIndex,6*this.templateIndex+6).map((t=>t.content||t.name));if(t.length)return this.params.qaRecommendationList=t,void(this.params.qrLoading=!1)}this.templates=[],this.$saasApi.AIRecommend().then((t=>{if(!t)return;let e=t.replace(/\\/g,"").replace(/n/g,"").replace(/`/g,"").replace(/"/g,"").replace(/markdow/g,"").split("#### ").filter((t=>!!t));e.length&&(e.length>6&&(e=e.slice(0,6)),this.params.qaRecommendationList=e)})).finally((()=>{this.params.qrLoading=!1}))}},getListSession(){this.hideHistory||this.$saasApi.AIListSession({conditions:[{Field:"type",Value:this.agent,Operate:"=",Relation:"and"},{Field:"created",Value:a()().add(-8,"d").format("YYYY-MM-DD 00:00:00"),Operate:">=",Relation:"and"}]}).then((t=>{t&&Array.isArray(t)&&(t.sort(((t,e)=>Date.parse(e.created)-Date.parse(t.created))),this.params.historyList=t.filter((t=>t.title)).map((t=>(t.isHandlerOpen=!1,t.isEdit=!1,t.rename=t.title,t))))}))},selectQa(t){this.isSelectAsk=!0,this.selectVal=t,this.params.keyword=null==t?void 0:t.name,this.$refs.input.focus(),this.analysisType=null==t?void 0:t.type,this.getAnswer()},onFold(t,e){this.$set(t,e,!t[e]),"fold"===e&&this.$set(t,"thinking_hide",t.fold)},formatContent(t){return this.removeThinkingProcess(t)},getHistory(){this.aiModelVisible?(this.params.historyListVis=!0,this.$nextTick((()=>{this.aiModelVisible=!1}))):this.params.historyListVis=!0},getNewAnswer(){if(this.aiModelVisible&&(this.aiModelVisible=!1),this.onMixinsTextToVoiceStop(null,this.params.answerList),this.onAbortAnswer(),this.params.historyListVis=!1,this.answering=!1,this.params.sessionId=null,this.params.keyword=null,this.params.answerList.length>0){let t=this.params.answerList.filter((t=>{var e;return"user"===t.role&&(null==t||null===(e=t.inputs)||void 0===e||null===(e=e.file)||void 0===e?void 0:e.url)}));t.forEach((t=>{URL.revokeObjectURL(t.inputs.file.url)}))}if(this.params.answerList=[],this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.params.fileList=[],this.$refs.fileUploader.clearFiles()},onSelectHistory(t){this.onMixinsTextToVoiceStop(null,this.params.answerList),this.onAbortAnswer(),this.params.historyListVis=!1,this.loading=!0,this.$saasApi.AIPageSessionRecord({conditions:[{Field:"sessionId",Value:t.id,Operate:"=",Relation:"and"}],order:[],index:1,size:9999}).then((e=>{e&&e.rows&&(this.params.sessionId=t.id,this.params.keyword=null,this.params.answerList=e.rows.map((t=>{let e=t.content;try{e=JSON.parse(e),e.answer=e.content||"抱歉！我找不到相关信息。"}catch(s){e={answer:t.content||"抱歉！我找不到相关信息。"}}if("user"===t.role&&t.inputs)try{t.inputs=JSON.parse(t.inputs)}catch(t){}return{...t,content:"assistant"===t.role?e:t.content,recordId:t.id,thinking_hide:!0}})),setTimeout((()=>{this.$refs.scrollbar.wrap.scrollTop=this.$refs.scrollbar.wrap.scrollHeight}),10))})).catch((t=>{this.$message.error("获取历史对话信息失败！")})).finally((()=>{this.loading=!1}))},formatKeywordHtml(t){let e=t?t.replace(/@/g,"<span>@</span>"):"";return e=e.replace(/(\r\n|\n|\r)/g,"<br/> "),e?e+"<span></span>":""},onKeydownModelSelect(t){const e=this.tools.selection-1,s=this.params.keyword.substring(0,e),i=this.params.keyword.substring(e+("@"+this.tools.keyword).length);this.params.keyword=s+"@"+t.name+i+" ",this.$refs.input.focus(),this.tools.visible=!1},Keydown(t){t.shiftKey||13!==t.keyCode||(t.cancelBubble=!0,t.stopPropagation(),t.preventDefault(),this.getAnswer())},onUploadFileQuest(t){this.getAnswerNoFile(t.file)},onRegenerate(t,e){if(!t.recordId||this.answering)return;this.onMixinsTextToVoiceStop(t),this.answering=!0;const s=t.recordId;this.$set(t,"content",{thinking:"",answer:""}),this.$set(t,"isLike",null),this.$set(t,"thinking_hide",null),this.$set(t,"loading",!0),this.$set(t,"controller",new AbortController),this.$set(t,"controllerType",null),this.$set(t,"answerTimer",null),this.$set(t,"answerDateNow",Date.now()),this.$set(t,"thinking_result",null),this.$set(t,"isVoiceSpeaking",!1),this.aiModelType!==t.modelType&&this.$set(t,"modelType",this.aiModelType);const i=(this.params.answerList[e-1]||{}).content||"";this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_regenerator`,path:"孪生图",functionName:"重新生成",param:t.question?t.question:i}),this.$saasApi.AIRegeneratorMessage({recordId:s},t.controller).then((async e=>{if(!e.ok)throw new Error;let s=null,i=null;"TextDecoderStream"in window?s=e.body.pipeThrough(new TextDecoderStream).getReader():(s=e.body.getReader(),i=new TextDecoder);let n=!0,a=null;for(;n;){const{done:e,value:l}=await s.read();if(e){var o;console.warn("done"),n=!1,this.params.loading=!1,t.loading=!1,!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(o=t.content)||void 0===o?void 0:o.thinking,answer:"抱歉！我找不到相关信息。",result_list:[]}),this.onResetScrollbar();break}let r=i?i.decode(l,{stream:!0}):l,c=this.chunkData(r);this.setAnswerData(t,c,a),a=c}})).catch((e=>{var s;if(t.loading=!1,"当前访问人数过多，请稍候再试!"===(null==e?void 0:e.message))!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(s=t.content)||void 0===s?void 0:s.thinking,answer:"当前访问人数过多，请稍候再试!"});else if("abort"===t.controllerType){var i;!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(i=t.content)||void 0===i?void 0:i.thinking,answer:"已中止查询相关信息。"})}else{var n;!t.content.answer&&this.$set(t,"content",{thinking:null==t||null===(n=t.content)||void 0===n?void 0:n.thinking,answer:"抱歉！我找不到相关信息。"})}this.answering=!1,this.params.loading=!1,this.onResetScrollbar()})).finally((()=>{this.answering=!1,this.$set(t,"loading",!1),this.$set(t,"controller",null),this.$set(t,"controllerType",null),this.params.loading=!1,this.onResetScrollbar(),this.getListSession()}))},setAnswerData(t,e,s){let i=null;for(let n=0;n<e.length;n++){try{this.params.loading=!0,e[n].endsWith("\n\n")&&(e[n]=e[n].slice(0,-2)),i=JSON.parse(e[n]).data,"string"==typeof i&&(i=JSON.parse(i)),"thinking"===i.type?t.content.thinking+=i.content:"text"===i.type&&(t.content.answer+=i.content,!t.thinking_hide&&this.$set(t,"thinking_hide",!0)),i.sessionId&&(this.params.sessionId=i.sessionId),t.loading=!0,t.recordId=i.recordId||i.id,t.modelType!==i.modelType&&i.modelType&&(t.modelType=i.modelType)}catch(t){s&&s.length>0&&(e[n]&&!e[n].startsWith("{")&&(0===n?(e[n]=(s[s.length-1]||"")+e[n],n=-1):(e[n]=e[n-1]+e[n],n--)),s=e)}this.onResetScrollbar()}},onAbortAnswer(){if(!this.answering)return;const t=this.params.answerList[this.params.answerList.length-1];t&&t.loading&&t.controller&&(t.controllerType="abort",t.controller.abort(),this.answering=!1,this.params.loading=!1)},getAnswer(){if(!this.answering&&this.params.keyword){if(this.tools.visible=!1,this.params.fileList.length)return this.params.fileList.forEach((t=>{t.status="ready"})),this.$refs.fileUploader&&(this.$refs.fileUploader.uploadFiles=this.params.fileList),this.$refs.fileUploader.submit(),void setTimeout((()=>{if(this.params.keyword=null,this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.params.fileList=[]}),200);this.params.keyword&&this.getAnswerNoFile()}},onFileUploadChange(t,e){e.forEach((t=>{t.raw.type.startsWith("image/")?t.type="image":t.type="document",t.url=URL.createObjectURL(t.raw)})),this.params.fileList=e,!this.isMobile&&this.$refs.input.focus()},onFileDelete(){if(this.params.fileList.length){this.params.fileList.filter((t=>t.url)).forEach((t=>{URL.revokeObjectURL(t.url)}))}this.$refs.fileUploader.clearFiles(),this.params.fileList=[]},onHandlerHistoryDelete(t){this.$confirm("确定删除 "+t.title,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$saasApi.AIDeleteSession(t.id).then((e=>{this.$message.success("删除成功"),this.getListSession(),this.params.sessionId===t.id&&this.getNewAnswer()})).catch((()=>{this.$message.error("删除失败")}))}))},onMouseEnter(t){this.params.time&&clearTimeout(this.params.time)},onMouseLeave(t){this.isMobile||(this.params.time&&clearTimeout(this.params.time),this.params.time=setTimeout((()=>{t.isHandlerOpen=!1}),300))},receiveMapParams(t){try{var e,s,i;if(null==t||null===(e=t.data)||void 0===e||!e.action||"mapEvent"!==(null==t||null===(s=t.data)||void 0===s?void 0:s.action))return;const{method:c,args:d}=null==t||null===(i=t.data)||void 0===i?void 0:i.params,h=JSON.parse(d);if("aiScenePipeBurstInitMessge"==c){console.warn("孪生图",{method:c,args:h});const{features:t}=h;window.testInfo=h,this.pipeList=t,this.currentPipeList=this.getRandomElements(this.pipeList,5),window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstAddPoints",args:JSON.stringify({features:this.currentPipeList})}},"*");const e={0:"1️⃣",1:"2️⃣",2:"3️⃣",3:"4️⃣",4:"5️⃣"};var n="我找到了以下符合条件的管道，请选择一个进行分析\n";this.currentPipeList.length?(this.currentPipeList.forEach(((t,s)=>{var i,a,o;n+=`${e[s]} **GD${null===(i=t.properties)||void 0===i?void 0:i.OBJECTID}** | ${null===(a=t.properties)||void 0===a?void 0:a.d_s} | ${null===(o=t.properties)||void 0===o?void 0:o.material}\n                            `,s===this.currentPipeList.length-1&&(n+="您也可以直接告诉我：选择管道1\n                                您也可以继续告诉我更多信息，例如：\n                                * <u>换一批</u>\n                                * <u>DN50的管道</u>")})),this.params.answerList.push({id:this.answerId,loading:!1,role:"assistant",question:this.searchVal,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:n}})):this.params.answerList.push({id:this.answerId,loading:!1,role:"assistant",question:this.searchVal,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"抱歉，暂未找到符合要求的数据！"}})}else if("aiScenePipeBurstAnalysisMessge"==c){var a,o,l,r;console.warn("停水关阀分析结果",{method:c,args:h});const t=null==h||null===(a=h.Response)||void 0===a?void 0:a[0];let e="";null!=t&&null!==(o=t.ListValve)&&void 0!==o&&o.length&&(null==t||t.ListValve.forEach(((s,i)=>{(null==t?void 0:t.ListValve.length)-1!==i?e+="FM"+s.ElementId+"->":e+="FM"+s.ElementId})));const s=`🚰**关阀方案**\n- 需关闭阀门：${null==t||null===(l=t.ListValve)||void 0===l?void 0:l.length} 个\n- 关阀顺序：${e}\n\n👨‍👩‍👧‍👦**停水用户**：${null==t||null===(r=t.ListUser)||void 0===r?void 0:r.length} 户\n\n🏢**重点单位**：\n- 医院 第一人民医院\n- 学校 中山小学\n- 商业 世纪大道体育馆\n\n🕒**预计停水时间**：约 8 小时。(基于历史维修数据和管道状况预估)\n`;this.streamOutput(s)}else if("aiSceneSimulationEmulationMessge"==c){console.warn("模拟仿真分析结果",{method:c,args:h});const t=null==h?void 0:h.Response,e=`# **管网模型模拟仿真分析报告**\n### 一、模拟内容描述\n\n🔧 **调整参数**：将管道 ${this.mapId}​管径由**${this.beforeDS}mm → ${this.currentDS}mm**。\n⚠️ **核心影响**：管径缩减显著改变局部水力条件，需重点分析压力波动、流量分配及用户端影响。\n\n### 二、对水厂的影响\n\n🏭 **受影响水厂**：${null==t?void 0:t.SourceName}\n\n* **第一水厂**：\n\n* 无压力骤降数据，源压力 **0.4752**，稳定性未受直接冲击。\n* **第二水厂**：\n\n* 无流量波动数据，源流量 **1117.25**，流量变化率  **-0.18%** ，呈小幅减少趋势。\n\n### 三、对管网的影响\n\n#### 🔍 **压力变化分析**\n\n* **影响范围**：部分监测点压力波动（如壬十九大道 0.219MPa、第二十大街 0.169MPa）。\n* **压力变化区间**：\n\n* 波动范围 **0.0046~0.0153**，平均压力变化 **0.0105**。\n* **风险区域**：未明确高压热点，需关注压力点稳定性。\n\n#### 💧 **流量与流速变化**\n\n* **流量变化区间**：**13.32~44.41**，部分管道因管径缩小流量下降。\n* **流速峰值**：**0.46m/s**，需核查是否超设计流速。\n\n#### 🔄 **水流方向变化**\n\n* **方向反转管道**：部分管道流向改变（如${this.mapId}​），需验证对管网稳定性影响。\n\n### 四、对用户的影响\n\n👥 **受影响用户**：\n\n* **低压用户（&lt;0.15MPa）** ：无新增。\n* **高压用户（&gt;0.4MPa）** ：无减少。\n* **实际受影响用户**：共 **${null==t?void 0:t.UserOfImpactTotalCount} 户**，压力均上升：\n\n### 五、总结与建议\n\n#### 📌 **核心结论**\n\n1. **风险聚焦**：管径缩减引发局部压力波动，部分用户压力上升。\n2. **管网影响**：流量、流速及水流方向改变，需关注管道磨损与稳定性。\n\n#### 🛠 **改进建议**\n\n1. **分阶段调整**：先小幅度调整管径并监测，逐步优化至目标值。\n2. **增设监测**：对压力波动明显节点加装实时监测设备，动态跟踪。\n3. **用户端排查**：联系受影响用户，检查用水设备是否受压力上升影响，必要时安装调压装置。\n4. **模型校准**：结合实时数据校准模型，补充管道材质、用户分布等参数，提升模拟精度。\n\n💡 **模拟价值**：识别管径调整对管网的影响，但需完善数据提升预测准确性。\n📢 **下一步行动**：低峰期实测压力、流量数据，对比仿真结果，优化最终改造方案。`;this.streamOutput(e)}}catch(t){}},onMapLocation(t){window.parent.postMessage({action:"mapMethod",params:{method:"browsePointsByNames",args:JSON.stringify({names:[t]})}},"*"),window.parent.postMessage({action:"mapMethod",params:{method:"locatePointByProperty",args:JSON.stringify({name:t})}},"*")},handleLocationClick(t){if("BUTTON"===t.target.tagName&&t.target.classList.contains("operate-btn")){const e=t.target.innerText;this.onMapLocation(e),t.stopPropagation()}},onQuestion(t){this.params.keyword=t,this.$refs.input.focus(),this.getAnswer()},findTargetParagraph(t){const e=Array.from(document.querySelectorAll(".operate-btn")).find((e=>t.includes(e.textContent)&&e.closest("li")));return null==e?void 0:e.closest("li")},scrollToElement(t){null==t||t.scrollIntoView({behavior:"smooth",block:"center"})},highlightParagraph(t){this.clearHighlight();const e=this.findTargetParagraph(t);e&&(e.classList.add("active-paragraph"),this.scrollToElement(e))},clearHighlight(){document.querySelectorAll(".active-paragraph").forEach((t=>{t.classList.remove("active-paragraph")}))},executeWithDelay(t){let e=0;const s=()=>{const i=t[e];this.highlightParagraph(i),e++,e<t.length?setTimeout(s,3e3):setTimeout((()=>{this.clearHighlight()}),3e3)};s()},removeAllBracketsContent(t){let e=0,s="",i=[];for(const n of t){if("("===n||"（"===n)e++,i.push("("===n?"en":"cn");else if(")"===n||"）"===n){const t=i.pop();if(!(")"===n&&"en"===t||"）"===n&&"cn"===t))return"";e--}0!==e||["(",")","（","）"].includes(n)||(s+=n)}return 0===i.length?s.trim():""},getRandomElements(t,e){const s=t.slice();for(let t=s.length-1;t>0;t--){const e=Math.floor(Math.random()*(t+1));[s[t],s[e]]=[s[e],s[t]]}return s.slice(0,e)},getPipeData(t,e,s){window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstInit",args:JSON.stringify({radius:100,center:s})}},"*")},changePipe(){this.currentPipeList=this.getRandomElements(this.pipeList,5),window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstAddPoints",args:JSON.stringify({features:this.currentPipeList})}},"*")},findLongestMatch(t,e){const s=[...e].sort(((t,e)=>e.name.length-t.name.length));for(const e of s){const s=e.name;if(t.includes(s))return{name:s,item:e}}let i=0,n="",a={};for(const e of s){const s=e.name;for(let o=0;o<s.length;o++)for(let l=o+1;l<=s.length;l++){const r=s.substring(o,l);t.includes(r)&&r.length>i&&(i=r.length,n=r,a=e)}}return{name:n,item:a}},insertDoc(t){var e;this.$saasApi.AIInsertMarkdown({type:"1",name:t.question,content:null===(e=t.content)||void 0===e?void 0:e.answer}).then((t=>{if(!t.ok)throw new Error("HTTP error! status: "+t.status);return t.json()})).then((e=>{if(e){var s,i;this.$store.commit("setState",{isGenerateDoc:!0}),this.$store.commit("setState",{generateDocInfo:{id:null===(s=e.Response)||void 0===s?void 0:s.id,name:t.question,author:null===(i=e.Response)||void 0===i?void 0:i.creator}});let n={id:null,role:"assistant",loading:!1,question:t.question,content:{answer:"已为您生成WimPic文档，可输入“发送给[某人]”将文档发送至对方"},modelType:this.aiModelType.active,recordId:null,controller:null,controllerType:null};this.params.answerList.push(n)}}))},streamOutput(t){const e={id:this.answerId,loading:!1,role:"assistant",question:this.questionVal,modelType:this.aiModelType,recordId:null,content:{answer:""}};this.params.answerList.push(e);const s=this.params.answerList.length-1,i=null==t?void 0:t.split(/(?=#+ )/);let n=0;const a=()=>{n<i.length?(this.params.answerList[s].loading=!0,this.params.answerList[s].content.answer+=i[n]+"\n\n",n++,setTimeout(a,800)):(this.answering=!1,this.params.answerList[s].loading=!1),this.onResetScrollbar()};a()},async getAnswerNoFile(t){var e,s,i;let n,a=JSON.stringify(this.mapData)||"",l=!(null===(e=this.selectVal)||void 0===e||!e.type);n=this.params.keyword,this.questionVal=n,this.params.keyword=null,this.params.scrollEnd=!0,this.onMixinsTextToVoiceStop(null,this.params.answerList),this.isSelectAsk=!1,this.selectVal=null,this.isMobile&&this.$refs.input.blur();let r={file:null};if(t&&(r.file={type:t.type.indexOf("image")>-1?"image":"document",fileName:t.name,url:URL.createObjectURL(t)}),this.params.answerList.push({role:"user",content:n||"",inputs:r}),this.trackEvent({functionId:`uniwimai_${this.$vnode.key}_send`,path:"孪生图",functionName:"发送内容",param:n}),n&&/^(点击|进入|刷新|切换|选择|取消选择|勾选|取消勾选|取消)/.test(n.trim())){const t={"点击":"click","刷新":"refresh","进入":"click","切换":"switch","选择":"select","取消选择":"unselect","勾选":"check","取消勾选":"uncheck","取消":"cancel"}[n.trim().match(/^(点击|进入|刷新|切换|选择|取消选择|勾选|取消勾选|取消)/)[0]];window.parent.postMessage("*#hd#*"+JSON.stringify({action:"AI_ASSISTANT_SPECIAL_COMMAND",params:{content:n,type:t}}),"*")}else if(this.answering=!0,1!==this.analysisType)if(2!==this.analysisType)if(3!==this.analysisType)if(null!==(s=n)&&void 0!==s&&s.includes("分享")){var c;if(null!==(c=this.params.answerList)&&void 0!==c&&c.length){const t=this.params.answerList.filter((t=>t.id)),e=this.params.answerList.findLastIndex((t=>t.id)),s=t[t.length-1];this.answering=!1,this.onMixinsShareToWimPic(s,e,"noOperate")}}else{if(null!==(i=n)&&void 0!==i&&i.includes("发送给")){var d;let t=null===(d=n)||void 0===d?void 0:d.split("发送给")[1];return t?(t+="|"+Object(o.g)(),this.$store.commit("setState",{GenerateDocSender:t}),void(this.answering=!1)):void this.$message.error("请输入姓名")}setTimeout((async()=>{const e=Object(o.g)();let s={id:e,role:"assistant",loading:!0,question:n||"",content:{thinking:"",answer:""},modelType:this.aiModelType,recordId:null,controller:new AbortController,controllerType:null,answerTimer:null,answerDateNow:Date.now(),thinking_result:null,isVoiceSpeaking:!1};const{signal:i}=s.controller;if(this.params.answerList.push(s),top!=self&&s.question.includes("总结该网页")){const t=()=>(console.log("开始等待..."),this.summaryArc={},new Promise((t=>{const s=this.$watch("summaryArc",(i=>{console.warn("监听到总结页面返回内容",{summaryArc:i}),s&&s(),t(i.id!==e?"error":i)}),{deep:!0});window.parent.postMessage("*#hd#*"+JSON.stringify({action:"AI_ASSISTANT_SUMMARY_PAGE",params:{id:e}}),"*"),setTimeout((()=>{s&&s(),t("timeout")}),1e4)}))),i=await t();if("timeout"===i||"error"===i){let t=this.params.answerList.findIndex((t=>t.id===e));return this.$set(this.params.answerList[t],"loading",!1),this.$set(this.params.answerList[t],"content",{answer:"timeout"===i?"获取网页信息超时":"获取网页信息失败"}),this.onResetScrollbar(),s.controller=null,s.controllerType=null,void(this.answering=!1)}}console.warn("继续执行对话"),this.$saasApi.AIMapReport({data:a,content:n||"",sessionId:this.params.sessionId,tasktype:"Map_Analysis",file:t,modelType:this.aiModelType},i).then((async t=>{if(!t.ok)throw new Error;const s=t.body.pipeThrough(new TextDecoderStream).getReader();let i=!0,n=[];for(;i;){const{done:t,value:l}=await s.read();if(t){var a,o;i=!1,this.params.loading=!1;let t=this.params.answerList.findIndex((t=>t.id===e));this.$set(this.params.answerList[t],"loading",!1),this.monitorList=(null===(a=this.params.answerList[t].content)||void 0===a?void 0:a.label)||[],this.questionList=null===(o=this.params.answerList[t].content)||void 0===o?void 0:o.question,window.parent.postMessage({action:"mapMethod",params:{method:"browsePointsByNames",args:JSON.stringify({names:this.monitorList})}},"*"),console.log("browsePointsByNames",{method:"browsePointsByNames",args:JSON.stringify({names:this.monitorList})}),this.monitorList.length&&this.executeWithDelay(this.monitorList),this.params.answerList[t].content&&this.params.answerList[t].content.answer||this.$set(this.params.answerList[t],"content",{answer:"抱歉！我找不到相关信息。"}),this.isScroll&&this.onResetScrollbar(),n=null;break}let r=l;r.startsWith("data:")&&!r.startsWith("data:{")?(r=l.slice(5,r.length),n.push("data:"),n.push(""),n[n.length-1]=(n[n.length-1]||"")+r):r.includes("data:{")?(r=r.split("data:{"),r.forEach(((t,e)=>{0===e?n[0===n.length?0:n.length-1]=(n[n.length-1]||"")+t:(n.push("data:"),n.push("{"),n[n.length-1]=(n[n.length-1]||"")+t)}))):n[0===n.length?0:n.length-1]=(n[n.length-1]||"")+r;for(let t=0;t<n.length;t++){try{this.params.loading=!0,r=JSON.parse(n[t]).data,r.content=JSON.parse(r.content),r.sessionId&&(this.params.sessionId=r.sessionId);let s=this.params.answerList.findIndex((t=>t.id===e));this.$set(this.params.answerList[s],"loading",!0),this.$set(this.params.answerList[s],"recordId",r.recordId),this.$set(this.params.answerList[s],"content",r.content),this.params.answerList[s].modelType!==this.aiModelType.active&&this.$set(this.params.answerList[s],"modelType",r.modelType)}catch(t){}this.onResetScrollbar()}}})).catch((()=>{var t,e;s.loading=!1,"abort"===s.controllerType?!s.content.answer&&this.$set(s,"content",{thinking:null==s||null===(t=s.content)||void 0===t?void 0:t.thinking,answer:"已中止查询相关信息。"}):this.$set(s,"content",{thinking:null==s||null===(e=s.content)||void 0===e?void 0:e.thinking,answer:"抱歉！我找不到相关信息。"}),this.onResetScrollbar()})).finally((()=>{s.controller=null,s.controllerType=null,this.answering=!1,this.getListSession()}))}),100)}else setTimeout((()=>{const t=Object(o.g)();if(this.answerId=t,this.params.loading=!1,l)this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n||"",modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,answerTimer:null,answerDateNow:Date.now(),thinking_result:null,content:{answer:"你好！我是管网运行状态AI助手。我可以帮您进行管网运行状态的分析。请直接告诉我您需要了解到管网运行状态。例如：\n                * <u>压力运行状态</u>\n                * <u>流量运行状态</u>\n                * <u>流速运行状态</u>"}});else{var e,s,i,a;let t="";null!==(e=n)&&void 0!==e&&e.includes("流量")?(window.parent.postMessage({action:"mapMethod",params:{method:"aiAttributeThematic",args:JSON.stringify({name:"流量"})}},"*"),t="### 管网运行状态流量分析报告\n\n#### 一、数据概况\n\n根据管道分级数据，管网总长度为2,927,576.34 m，各流量区间管道长度及占比如下：\n\n|**流量分级（m³/h）**|**管道长度（m）**|**比例（%）**|\n| -----| --------| -------|\n| **&lt;5**|41,022|32.93|\n|**5-50**|35,594|28.6|\n|50 **-400**|40,679|32.5|\n|400 **-1500**|6,356|5.13|\n| **&gt;1500**|1,271|0.84|\n\n#### 二、流量分布特征分析\n\n* 低流量管道（<5 m³/h）占比最高（32.27%），表明近半数管道处于低负载运行状态，可能存在末端管道冗余或用水需求不足问题。\n* 中低流量区间（50 **-400** m³/h）合计占比32.5%，是管网输配的常规流量段，承担基础供水功能。\n* 高流量管道（>**1500** m³/h）占比极低（0.84%），仅少数关键干线承载高流量输配。\n\n#### 三、水厂出水分析\n\n* **第一水厂**：下游管道呈现红色线条为主，表明其出水经主干管向城市核心高需求区域输送，流量显著高于常规管道。红色管道的集中分布，反映第一水厂承担大规模集中供水任务，出水流量大且输配效率高，支撑城市核心区高强度用水需求。。\n* **第二水厂**：下游同样存在红色高流量管道，承担常规输配；延伸至城市中部的主干管呈现蓝色（50-400 m³/h），协同参与城市整体供水，形成“常规输配+主干协同”的出水模式，匹配区域用水结构。\n\n#### 四、地图分布分析\n\n* **绿色（≤5 m³/h）** ：集中于城市边缘末梢管网，反映末端供水区域用水分散、单管流量低。\n* **浅蓝色（5-50 m³/h）** ：覆盖部分居民区，呈现网状分布，满足常规生活用水输配。\n* **蓝色（50-400 m³/h）** ：构成水厂连接的主干管网络，呈放射状与环状布局，是核心输配通道。\n* **粉色/红色（400-1500 m³/h及&gt;1500 m³/h）** ：仅分布于关键跨区域干线，承载高流量输配任务。\n* **红色（&gt;1500 m³/h）** ：集中于水厂下游主干管，呈放射状延伸至城市核心区，直观体现水厂高流量出水向重点区域的输配路径，是管网中承担高强度供水的关键通道。\n\n#### 五、经济流量分析\n\n该区间管道长度占比需结合实际数据计算，但从地图看，蓝色主干管是经济运行的核心。然而，低流量管道（<5 m³/h）占比达50.54%，拉低整体经济性，可能因管道布局不合理或末端利用率低导致资源浪费。\n\n#### 六、总结与建议\n\n* **总结**：水厂下游红色高流量管道凸显其核心供水地位，但管网中低流量管道占比仍高。高流量管道虽保障重点区域供水，却可能因过载增加运行成本与风险。管网低流量管道占比高，部分主干管承担经济输配功能，但整体运行效率待提升；水厂出水模式匹配区域需求，但需优化末端管网。\n* **建议**：\n\n1. 优化低流量管道布局，合并冗余管段，提升末端利用率。\n2. 监测高流量主干管负载，评估扩容或分流方案，降低运行风险。\n3. 结合用水需求预测，调整管网结构，提高经济流量管道占比，降低运行成本。\n"):null!==(s=n)&&void 0!==s&&s.includes("压力")?(window.parent.postMessage({action:"mapMethod",params:{method:"aiAttributeThematic",args:JSON.stringify({name:"压力"})}},"*"),t="### 管网管点压力专题图分析报告\n\n#### 一、概况\n\n图中以颜色区分管点压力等级，具体分级如下：\n\n* **蓝色（≤26）** ：低压力管点；\n* **深蓝色（26-29）** ：较低压力管点；\n* **黄色（29-35）** ：中等压力管点；\n* **橙色（35-38）** ：较高压力管点；\n* **红色（≥38）** ：高压力管点。\n\n#### 二、压力分布特征分析\n\n* **水厂周边高压集中**：水厂附近管点以红色（≥38）、橙色（35-38）为主，压力显著高于其他区域，体现水厂出水端压力充足，保障核心输配。\n* **外围压力递减**：从水厂向外延伸，压力逐渐降低，城市边缘区域以蓝色（≤26）、深蓝色（26-29）为主，符合“水厂高压供水—外围末梢低压”的规律。\n* **压力分区明确**：形成“高压核心区—中压过渡区—低压末梢区”的分层结构，高压区（红、橙）集中于主干管，中压区（黄）衔接，低压区（蓝、深蓝）覆盖末端管网。\n\n#### 三、地图分布分析\n\n* **红色/橙色区域**：集中于水厂周边及主干管沿线，呈放射状延伸至城市中心，是高压输配的核心通道。\n* **黄色区域**：分布于城市中部，连接高低压力区域，承担压力过渡与输配功能。\n* **蓝色/深蓝色区域**：主要位于城市边缘、老旧小区等末梢管网，压力偏低，反映远端供水压力衰减。\n\n#### 四、供水系统分析\n\n* **水厂压力支撑**：水厂作为压力源头，高压管点（红、橙）沿主干管分布，确保核心区域供水压力稳定，满足集中用水需求。\n* **管网压力传导**：压力从水厂向外逐级递减，与管网输配结构匹配，体现供水系统“高压供水、分级输配”的设计逻辑。但末端低压区可能存在供水动力不足风险，影响边缘区域用水体验。\n\n#### 五、总结与建议\n\n* **总结**：管网压力分布整体符合供水逻辑，但末端低压区（≤29）占比较大，可能影响部分区域供水效果；水厂周边高压区需关注长期运行稳定性。\n* **建议**：\n\n  1. 对水厂周边高压管点（红、橙区域）定期监测压力波动，避免长期高压导致管道老化破裂。\n  2. 针对边缘低压区（蓝、深蓝区域），评估是否需增设加压泵站或优化管网布局，提升末端供水压力。\n  3. 结合压力分区特征，优化整个供水系统调度方案，平衡不同区域压力，保障供水稳定性与均匀性。"):null!==(i=n)&&void 0!==i&&i.includes("流速")?(window.parent.postMessage({action:"mapMethod",params:{method:"aiAttributeThematic",args:JSON.stringify({name:"流速"})}},"*"),t="### 管道流速分析报告\n\n#### 一、数据概况\n\n图中以颜色区分管道流速等级：\n\n* **蓝色（≤0.1 m/s）** ：低流速管道；\n* **青色（0.1-0.3 m/s）** ：较低流速管道；\n* **绿色（0.3-0.5 m/s）** ：中等流速管道；\n* **橙色（0.5-1 m/s）** ：较高流速管道；\n\n* **粉色（&gt;1 m/s）** ：高流速管道。\n\n通过地图可视化，直观呈现不同流速管道的空间分布，以及水厂周边的流速特征。\n\n#### 二、流速分布特征分析\n\n* **低流速主导**：蓝色（≤0.1 m/s）与青色（0.1-0.3 m/s）管道在图中分布广泛，表明管网中低流速管道占比较高，多对应末梢管网或用水需求较低区域。\n* **中高流速支撑**：绿色（0.3-0.5 m/s）、橙色（0.5-1 m/s）管道构成输配主干，承担常规供水流速需求；粉色（>1 m/s）管道集中在关键节点，体现局部高流速输配特征。\n\n#### 三、水厂出水分析\n\n* 周边粉色（>1 m/s）管道密集，显示水厂出水端流速高，快速向核心区域输配；橙色（0.5-1 m/s）管道向外延伸，形成“高流速核心+较高流速扩散”的输配模式。\n* 粉色管道覆盖部分主干管，橙色管道向南部延伸，兼顾高流速输配与区域常规供水，适配周边用水需求。\n\n#### 四、地图分布分析\n\n* **蓝色（≤0.1 m/s）** ：主要分布于城市边缘及末梢管网，流速极低，反映远端或低需求区域。\n* **青色（0.1-0.3 m/s）** ：密集覆盖居民区，满足日常用水的较低流速需求。\n* **绿色（0.3-0.5 m/s）** ：多位于工业区、商业区支线，适配中等流速用水场景。\n* **橙色（0.5-1 m/s）** ：构成城市主干管网，保障整体输配效率。\n* **粉色（&gt;1 m/s）** ：集中于水厂核心输配干线及关键节点，形成高速输配通道。\n\n#### 五、经济流速分析\n\n假设经济流速区间为0.3-1 m/s（绿+橙）：\n\n* 绿色与橙色管道承担主要输配功能，是经济流速的核心载体。但低流速管道（蓝+青）分布广泛，可能导致管道淤积、资源浪费；高流速粉色管道虽提升输配效率，却存在长期磨损风险。\n\n#### 六、总结与建议\n\n* **总结**：管网低流速管道占比较高，经济流速管道支撑输配，但需优化低流速区域；水厂周边高流速管道保障核心供水，需关注运行损耗。\n* **建议**：\n\n1. 排查低流速管道（蓝、青色）淤积问题，优化布局以提升流速，减少管道损耗。\n2. 定期监测高流速粉色管道的磨损情况，评估材质耐久性，及时更新维护。\n3. 结合城市用水需求，调整管网流速分布，扩大经济流速管道覆盖范围，平衡输配效率与成本。\n"):null!==(a=n)&&void 0!==a&&a.includes("供水范围")&&(window.parent.postMessage({action:"mapMethod",params:{method:"aiAttributeThematic",args:JSON.stringify({name:"分界线"})}},"*"),t="### 供水范围专题图分析报告\n\n#### 一、概况\n\n图中以颜色区分供水范围：粉色为第一水厂供水区域，黄色为第二水厂供水区域，清晰展示两水厂在管网中的覆盖范围与空间布局。\n\n#### 二、分布特征\n\n* **第一水厂（粉色）** ：覆盖城市北部及中部核心区，管网呈密集网状，涵盖主要商业区、住宅区，是核心区域供水主力。\n* **第二水厂（黄色）** ：覆盖城市南部及部分中部区域，管网布局密集，主要服务南部居民区、工业区，与第一水厂形成区域互补。\n* **重叠与互补**：两水厂在城市中部存在供水重叠区（粉黄交织），边缘区域则独立覆盖（北部粉色、南部黄色），形成“核心共供+边缘独供”的格局。\n\n#### 三、与城市功能匹配\n\n第一水厂的粉色区域适配城市核心区高密度用水需求，第二水厂的黄色区域贴合南部功能区（如工业区）用水特点，整体布局与城市功能分区、人口分布高度契合，保障各区域用水供给。\n\n#### 四、总结与建议\n\n* **总结**：两水厂供水范围划分合理，既保障核心区域共供可靠性，又实现边缘区域独立覆盖，与城市功能布局协同。\n* **建议**：\n\n  1. **优化重叠区管理**：针对中部重叠供水区，加强管网压力与流量协同调度，避免资源浪费或供需失衡。\n  2. **强化边缘区保障**：定期监测南北部边缘独立供水区域的管网运行状态，预防末端水压不足或管道老化问题。\n  3. **完善应急方案**：基于供水范围分布，制定两水厂互为备用的应急预案，提升极端情况下（如单水厂故障）的供水韧性。\n"),this.streamOutput(t)}this.onResetScrollbar(),this.answering=!1}),1e3);else setTimeout((()=>{const t=Object(o.g)();if(this.answerId=t,this.params.loading=!1,l)this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n||"",modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,answerTimer:null,answerDateNow:Date.now(),thinking_result:null,content:{answer:"你好！我是管网模拟仿真AI助手。我可以帮您进行管网的模拟仿真分析。请直接告诉我您需要分析的内容。也可以输入需要模拟仿真的地点，例如：\n                * <u>第一大街</u>\n                * <u>第二大街</u>\n                * <u>第三大街</u>"}});else{var e,s,i,a;let o="",l=[];const u=this.findLongestMatch(n,this.mapData);var r,c;if(null!=u&&u.name&&(l[1]=Number(null==u||null===(r=u.item)||void 0===r?void 0:r.x),l[0]=Number(null==u||null===(c=u.item)||void 0===c?void 0:c.y),o=null==u?void 0:u.name,this.searchVal=null==u?void 0:u.name),n.includes("换一批")){this.changePipe();const e={0:"1️⃣",1:"2️⃣",2:"3️⃣",3:"4️⃣",4:"5️⃣"};var d="我找到了以下符合条件的管道，请选择一个进行分析\n";this.currentPipeList.length?(this.currentPipeList.forEach(((t,s)=>{var i,n,a;d+=`${e[s]} **GD${null===(i=t.properties)||void 0===i?void 0:i.OBJECTID}** | ${null===(n=t.properties)||void 0===n?void 0:n.d_s} | ${null===(a=t.properties)||void 0===a?void 0:a.material}\n                        `,s===this.currentPipeList.length-1&&(d+="您也可以直接告诉我：选择管道1\n                                        您也可以继续告诉我更多信息，例如：\n                                        * <u>换一批</u>\n                                        * <u>DN50的管道</u>")})),this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:d}})):this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"抱歉，暂未找到符合要求的数据！"}})}else if(n.includes("管道")){const e=n.match(/\d+/g);var h,p;null!=e&&e.length&&(this.mapId=null===(h=this.currentPipeList[Number(e[0])-1])||void 0===h||null===(h=h.properties)||void 0===h?void 0:h.globalid,this.beforeDS=null===(p=this.currentPipeList[Number(e[0])-1])||void 0===p||null===(p=p.properties)||void 0===p?void 0:p.d_s,this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"请输入您需要模拟管道的数据类型及修改后的内容\n如：模拟管径为500mm"}}))}else if(n.includes("模拟管径")){const t=n.match(/\d+/g);null!=t&&t.length&&(this.currentDS=Number(t[0]),window.parent.postMessage({action:"mapMethod",params:{method:"aiSceneSimulationEmulation",args:JSON.stringify({input:[{globalid:this.mapId,AttributeId:"d_s",Value:Number(t[0])}],hour:15})}},"*"))}else if(null!==(e=n)&&void 0!==e&&e.includes("DN")||null!==(s=n)&&void 0!==s&&s.includes("dn")){const e=n.match(/\d+/g);if(null!=e&&e.length){const s=this.pipeList.filter((t=>{var s;return(null===(s=t.properties)||void 0===s?void 0:s.d_s)===Number(e[0])}));this.currentPipeList=this.getRandomElements(s,5),window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstAddPoints",args:JSON.stringify({features:this.currentPipeList})}},"*");const i={0:"1️⃣",1:"2️⃣",2:"3️⃣",3:"4️⃣",4:"5️⃣"};let a="我找到了以下符合条件的管道，请选择一个进行分析\n";this.currentPipeList.length?(this.currentPipeList.forEach(((t,e)=>{var s,n,o;a+=`${i[e]} **GD${null===(s=t.properties)||void 0===s?void 0:s.OBJECTID}** | ${null===(n=t.properties)||void 0===n?void 0:n.d_s} | ${null===(o=t.properties)||void 0===o?void 0:o.material}\n                        `,e===this.currentPipeList.length-1&&(a+="您也可以直接告诉我：选择管道1\n                                            您也可以继续告诉我更多信息，例如：\n                                            * <u>换一批</u>\n                                            * <u>DN50的管道</u>")})),this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:a}})):this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"抱歉，暂未找到符合要求的数据！"}})}}else null!==(i=n)&&void 0!==i&&i.includes("如何通知受影响用户")?this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"建议通过以下方式通知受影响用户：1. 向所有受影响用户发送短信通知；2. 在社区公告栏张贴停水通知；3. 通过物业管理系统推送通知；4. 对重点单位进行电话确认。通知内容应包括停水时间、影响范围、恢复时间预估和应急联系方式。\n\n                ---\n                为了确保所有受影响的用户能够及时收到停水通知，建议采取以下多种渠道进行通知：\n\n                1. **短信通知**：向所有受影响的用户发送短信，确保每个人都能快速接收到重要信息。\n                2. **社区公告栏**：在社区的主要公告栏张贴详细的停水通知，方便居民随时查看。\n                3. **物业管理系统推送**：利用物业管理系统的消息推送功能，直接将停水信息发送至用户的手机应用或电子邮件。\n                4. **电话确认**：对重点单位（如医院、学校、企业等）进行电话沟通，确保这些关键场所已充分了解停水安排并做好准备。\n                5. **社交媒体及公众号推送**：通过社区或相关部门的微信公众号、微博等社交平台发布停水通知，扩大信息覆盖范围。\n\n                通知内容应包括：\n                - 停水的具体时间\n                - 影响的地理范围\n                - 预计恢复供水的时间\n                - 应急联系方式，以便用户在遇到问题时能迅速联系到相关部门"}}):null!==(a=n)&&void 0!==a&&a.includes("关阀方案")?this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"关阀方案包括需要关闭的 2个阀门：509、909。关阀顺序为先关闭上游阀门 909，然后关闭其他阀门。这样可以最小化水锤效应，保护管网安全。"}}):o&&this.getPipeData(t,o,l)}this.onResetScrollbar(),this.answering=!1}),1e3);else setTimeout((()=>{const t=Object(o.g)();if(this.answerId=t,this.params.loading=!1,l)this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n||"",modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,answerTimer:null,answerDateNow:Date.now(),thinking_result:null,content:{answer:"你好！我是停水关阀AI助手。我可以帮您进行停水关阀分析。请直接告诉我您需要分析道路的名称，例如：\n                * <u>第一大街</u>\n                * <u>第二大街</u>\n                * <u>第三大街</u>"}});else{var e,s,i,a,r;let o="",l=[];const u=this.findLongestMatch(n,this.mapData);var c,d;if(null!=u&&u.name&&(l[1]=Number(null==u||null===(c=u.item)||void 0===c?void 0:c.x),l[0]=Number(null==u||null===(d=u.item)||void 0===d?void 0:d.y),o=null==u?void 0:u.name,this.searchVal=null==u?void 0:u.name),n.includes("换一批")){this.changePipe();const e={0:"1️⃣",1:"2️⃣",2:"3️⃣",3:"4️⃣",4:"5️⃣"};var h="我找到了以下符合条件的管道，请选择一个进行分析\n";this.currentPipeList.length?(this.currentPipeList.forEach(((t,s)=>{var i,n,a;h+=`${e[s]} **GD${null===(i=t.properties)||void 0===i?void 0:i.OBJECTID}** | ${null===(n=t.properties)||void 0===n?void 0:n.d_s} | ${null===(a=t.properties)||void 0===a?void 0:a.material}\n                        `,s===this.currentPipeList.length-1&&(h+="您也可以直接告诉我：选择管道1\n                                        您也可以继续告诉我更多信息，例如：\n                                        * <u>换一批</u>\n                                        * <u>DN50的管道</u>")})),this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:h}})):this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"抱歉，暂未找到符合要求的数据！"}})}else if(n.includes("管道")){const t=n.match(/\d+/g);if(null!=t&&t.length){var p;const e=null===(p=this.currentPipeList[Number(t[0])-1])||void 0===p||null===(p=p.properties)||void 0===p?void 0:p.globalid;window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstHighlightPoint",args:JSON.stringify({globalid:[e]})}},"*"),window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstAnalysis",args:JSON.stringify({globalid:[e]})}},"*")}}else if(null!==(e=n)&&void 0!==e&&e.includes("DN")||null!==(s=n)&&void 0!==s&&s.includes("dn")){const e=n.match(/\d+/g);if(null!=e&&e.length){const s=this.pipeList.filter((t=>{var s;return(null===(s=t.properties)||void 0===s?void 0:s.d_s)===Number(e[0])}));this.currentPipeList=this.getRandomElements(s,5),window.parent.postMessage({action:"mapMethod",params:{method:"aiScenePipeBurstAddPoints",args:JSON.stringify({features:this.currentPipeList})}},"*");const i={0:"1️⃣",1:"2️⃣",2:"3️⃣",3:"4️⃣",4:"5️⃣"};let a="我找到了以下符合条件的管道，请选择一个进行分析\n";this.currentPipeList.length?(this.currentPipeList.forEach(((t,e)=>{var s,n,o;a+=`${i[e]} **GD${null===(s=t.properties)||void 0===s?void 0:s.OBJECTID}** | ${null===(n=t.properties)||void 0===n?void 0:n.d_s} | ${null===(o=t.properties)||void 0===o?void 0:o.material}\n                        `,e===this.currentPipeList.length-1&&(a+="您也可以直接告诉我：选择管道1\n                                            您也可以继续告诉我更多信息，例如：\n                                            * <u>换一批</u>\n                                            * <u>DN50的管道</u>")})),this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:a}})):this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"抱歉，暂未找到符合要求的数据！"}})}}else if(null!==(i=n)&&void 0!==i&&i.includes("影响分析")){window.parent.postMessage({action:"mapMethod",params:{method:"onClickImpactAnalysis"}},"*");const t=`#### **管网模型模拟仿真分析报告**\n##### 一、模拟内容描述\n\n⚠ **核心影响**：关阀显著改变局部水力条件，需重点分析压力波动、流量分配及用户端影响。\n\n#####  二、对水厂的影响\n\n🏭 **受影响水厂**：\n\n* **第一水厂**：\n\n* 无压力骤降数据，源压力 **0.4752**，稳定性未受直接冲击。\n* **第二水厂**：\n\n* 无流量波动数据，源流量 **1117.25**，流量变化率  **-0.18%** ，呈小幅减少趋势。\n\n#####  三、对管网的影响\n\n###### 🔍 **压力变化分析**\n\n* **影响范围**：部分监测点压力波动（如壬十九大道 0.219MPa、第二十大街 0.169MPa）。\n* **压力变化区间**：\n\n* 波动范围 **0.0046~0.0153**，平均压力变化 **0.0105**。\n* **风险区域**：未明确高压热点，需关注压力点稳定性。\n\n###### 💧 **流量与流速变化**\n\n* **流量变化区间**：**13.32~44.41**，部分管道因管径缩小流量下降。\n* **流速峰值**：**0.46m/s**，需核查是否超设计流速。\n\n###### 🔄 **水流方向变化**\n\n* **方向反转管道**：部分管道流向改变（如${this.mapId}​），需验证对管网稳定性影响。\n\n##### 四、对用户的影响\n\n👥 **受影响用户**：\n\n* **低压用户（&lt;0.15MPa）** ：无新增。\n* **高压用户（&gt;0.4MPa）** ：无减少。\n* **实际受影响用户**：共 **户**，压力均上升：\n\n##### 五、总结与建议\n\n###### 📌 **核心结论**\n\n1. **风险聚焦**：管径缩减引发局部压力波动，部分用户压力上升。\n2. **管网影响**：流量、流速及水流方向改变，需关注管道磨损与稳定性。\n\n###### 🛠 **改进建议**\n\n1. **分阶段调整**：先小幅度调整管径并监测，逐步优化至目标值。\n2. **增设监测**：对压力波动明显节点加装实时监测设备，动态跟踪。\n3. **用户端排查**：联系受影响用户，检查用水设备是否受压力上升影响，必要时安装调压装置。\n4. **模型校准**：结合实时数据校准模型，补充管道材质、用户分布等参数，提升模拟精度。\n\n💡 **模拟价值**：识别管径调整对管网的影响，但需完善数据提升预测准确性。\n📢 **下一步行动**：低峰期实测压力、流量数据，对比仿真结果，优化最终改造方案。`;this.streamOutput(t)}else null!==(a=n)&&void 0!==a&&a.includes("如何通知受影响用户")?this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"建议通过以下方式通知受影响用户：1. 向所有受影响用户发送短信通知；2. 在社区公告栏张贴停水通知；3. 通过物业管理系统推送通知；4. 对重点单位进行电话确认。通知内容应包括停水时间、影响范围、恢复时间预估和应急联系方式。\n\n                ---\n                为了确保所有受影响的用户能够及时收到停水通知，建议采取以下多种渠道进行通知：\n\n                1. **短信通知**：向所有受影响的用户发送短信，确保每个人都能快速接收到重要信息。\n                2. **社区公告栏**：在社区的主要公告栏张贴详细的停水通知，方便居民随时查看。\n                3. **物业管理系统推送**：利用物业管理系统的消息推送功能，直接将停水信息发送至用户的手机应用或电子邮件。\n                4. **电话确认**：对重点单位（如医院、学校、企业等）进行电话沟通，确保这些关键场所已充分了解停水安排并做好准备。\n                5. **社交媒体及公众号推送**：通过社区或相关部门的微信公众号、微博等社交平台发布停水通知，扩大信息覆盖范围。\n\n                通知内容应包括：\n                - 停水的具体时间\n                - 影响的地理范围\n                - 预计恢复供水的时间\n                - 应急联系方式，以便用户在遇到问题时能迅速联系到相关部门"}}):null!==(r=n)&&void 0!==r&&r.includes("关阀方案")?this.params.answerList.push({id:t,loading:!1,role:"assistant",question:n,modelType:this.aiModelType,recordId:null,controller:null,controllerType:null,content:{answer:"关阀方案包括需要关闭的 2个阀门：509、909。关阀顺序为先关闭上游阀门 909，然后关闭其他阀门。这样可以最小化水锤效应，保护管网安全。"}}):o&&this.getPipeData(t,o,l)}this.onResetScrollbar(),this.answering=!1}),1e3)}},destroyed(){window.removeEventListener("message",this.receiveMapParams,!1)}},c=r,d=(s("69e9"),s("cf27"),s("2877")),h=Object(d.a)(c,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"comp-box",class:{fullscreen:t.isFullScreen,"is-mobile":t.isMobile}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"content-area"},[t.isMobile?e("div",{staticClass:"header"},[e("div",{staticClass:"left"}),e("div",{staticClass:"right"},[t.agentIntroduce?e("introduce",{attrs:{"agent-introduce":t.agentIntroduce,"is-mobile":t.isMobile,"custom-title":t.customTitle}}):t._e(),t.hideHistory?t._e():e("div",{staticClass:"right-item",on:{click:function(e){return t.getHistory()}}},[e("img",{attrs:{src:t.icon.chat_history,alt:""}})]),e("div",{staticClass:"right-item",on:{click:function(e){return t.getNewAnswer()}}},[e("img",{attrs:{src:t.icon.chat_new1,alt:""}})])],1)]):t._e(),e("div",{staticClass:"top",staticStyle:{overflow:"hidden"}},[e("transition",{attrs:{name:"el-fade-in"}},[t.audioLoading?e("div",{staticClass:"scroll-top-voice-loading",class:{fixed:t.isMobile}},[e("i",{staticClass:"el-icon-loading"}),e("span",[t._v("正在生成朗读语音")]),e("span",{staticClass:"stop",on:{click:function(e){return t.onMixinsTextToVoiceStop()}}},[e("i",{staticClass:"iconfont icon-zhongzhi1"}),t._v("中止")])]):t._e()]),e("el-scrollbar",{ref:"scrollbar",staticStyle:{height:"100%",width:"100%"},attrs:{native:t.isMobile,"wrap-style":"overflow-x:hidden;"}},[t.params.answerList.length?e("div",{staticClass:"answer-content-list"},t._l(t.params.answerList,(function(s,i){var n,a,o,l,r,c,d;return e("div",{key:i,staticClass:"answer-content-item",class:{"is-send":"user"===s.role}},["user"===s.role?[null!=s&&null!==(n=s.inputs)&&void 0!==n&&null!==(n=n.file)&&void 0!==n&&n.url?e("div",{staticClass:"content is-file"},["image"===(null===(a=s.inputs.file)||void 0===a?void 0:a.type)?e("el-image",{staticClass:"image",attrs:{src:null===(o=s.inputs.file)||void 0===o?void 0:o.url,"preview-src-list":[null===(l=s.inputs.file)||void 0===l?void 0:l.url]}}):e("div",{staticClass:"document",on:{click:function(e){var i;return t.onMixinsOpenFile(null===(i=s.inputs.file)||void 0===i?void 0:i.url)}}},[e("img",{attrs:{src:t.icon.link,alt:""}}),e("span",[t._v(t._s(t.shortenText((null===(r=s.inputs.file)||void 0===r?void 0:r.fileName)||(null===(c=s.inputs.file)||void 0===c?void 0:c.url),13,13)))])])],1):t._e(),s.content?e("div",{staticClass:"content"},[e("span",[t._v(t._s(s.content))])]):t._e()]:"assistant"===s.role?[e("img",{staticClass:"avatar answer-avatar",attrs:{src:t.aiLogo||t.formatAnswerAvatar(s).icon,alt:""}}),e("div",{staticClass:"content",class:{fold:s.fold}},[e("div",{staticClass:"content-title"},[t._v(" "+t._s(t.aiName||t.formatAnswerAvatar(s).name)+" ")]),(s.loading,t._e()),t._e(),e("span",{staticClass:"content-result",on:{click:function(e){return e.stopPropagation(),t.handleLocationClick.apply(null,arguments)}}},[s.content.answer&&t.extractThinkingProcess(s.content.answer)?e("div",{staticClass:"deepseek-thinking"},[e("div",{staticClass:"thinking-status",on:{click:function(e){return t.$set(s,"thinking_hide",!s.thinking_hide)}}},[t._v(" "+t._s(t.formatContent(s.content.answer)?"已深度思考":"思考中...")+" "),s.thinking_hide?e("i",{staticClass:"el-icon-arrow-down"}):e("i",{staticClass:"el-icon-arrow-up"})]),e("div",{directives:[{name:"show",rawName:"v-show",value:!s.thinking_hide,expression:"!item.thinking_hide"}],staticClass:"thinking-result"},[e("vue-markdown",{attrs:{source:t.extractThinkingProcess(s.content.answer)}})],1)]):t._e(),s.content.answer?e("vue-markdown",{ref:"markdownRef_"+i,refInFor:!0,attrs:{source:t.formatContent(s.content.answer)}}):t._e(),s.loading?e("i",{staticClass:"el-icon-loading"}):t._e()],1),s.loading?t._e():[e("div",{staticClass:"content-regenerate"},[e("div",[t.hideToolsBtn(s.content.answer)?e("el-tooltip",{attrs:{content:"复制内容",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsPaste(s)}}},[e("i",{staticClass:"iconfont el-icon-copy-document"})])]):t._e(),i>=t.params.answerList.length-1&&s.recordId?e("el-tooltip",{attrs:{content:"重新生成",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onRegenerate(s,i)}}},[e("i",{staticClass:"iconfont icon-zhongzhi"})])]):t._e(),!t.hideVoicePlay&&t.hideToolsBtn(s.content.answer)?e("el-tooltip",{attrs:{content:s.isVoiceSpeaking?"中止朗读":"朗读",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){s.isVoiceSpeaking?t.onMixinsTextToVoiceStop(s):t.onMixinsTextToVoicePlay(s,i,t.params.answerList)}}},[s.isVoiceSpeaking?e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])]):e("i",{staticClass:"iconfont icon-shengyin1"})])]):t._e(),t.isWimpic&&t.hideToolsBtn(s.content.answer)?e("el-tooltip",{attrs:{content:"插入正文",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsInsertDoc(s)}}},[e("i",{staticClass:"iconfont icon-daoru1"})])]):t._e(),!t.hideDoc&&t.hideToolsBtn(s.content.answer)?e("el-tooltip",{attrs:{content:"分享到WimPic",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsShareToWimPic(s,i)}}},[e("i",{staticClass:"iconfont el-icon-share"})])]):t._e(),t.hideCorrect||null!==(d=s.content)&&void 0!==d&&null!==(d=d.menu)&&void 0!==d&&d.length||!t.hideToolsBtn(s.content.answer)?t._e():e("el-tooltip",{attrs:{content:"纠错",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",staticStyle:{"margin-left":"10px"},on:{click:function(e){return t.onMixinsCorrect(s,i)}}},[e("i",{staticClass:"iconfont el-icon-edit-outline"})])]),e("correct",{attrs:{content:t.correct.content,question:t.correct.question},model:{value:t.correct.visible,callback:function(e){t.$set(t.correct,"visible",e)},expression:"correct.visible"}})],1)]),t._e(),s.recordId?e("div",{staticClass:"question"},[e("div",{staticClass:"question-content"},t._l(t.questionList,(function(s,i){return e("div",{key:i,staticClass:"question-item",on:{click:function(e){return t.onQuestion(s)}}},[e("img",{attrs:{src:t.icon.twin_question,alt:""}}),e("span",{staticClass:"question-item-name"},[t._v(t._s(s))])])})),0)]):t._e()]],2)]:"document"===s.role?[e("img",{staticClass:"avatar",attrs:{src:t.aiLogo,alt:""}}),e("div",{staticClass:"content",class:{fold:s.fold}},[e("div",{staticClass:"content-title"},[t._v(" "+t._s(t.aiName)+" ")]),e("span",{staticClass:"content-result"},[s.loading&&!s.content?[e("i",{staticClass:"el-icon-loading"})]:[e("div",{staticClass:"document-content",class:{"is-link":s.docId},on:{click:function(e){return t.onMixinsOpenDoc(s)}}},[e("img",{staticClass:"file-icon",attrs:{src:t.icon.link,alt:""}}),e("div",[t._v(t._s(s.content))])])]],2),s.loading||t.hideTools?t._e():e("div",{staticClass:"content-regenerate"},[e("div",[e("el-tooltip",{attrs:{content:"复制链接地址",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"d0",on:{click:function(e){return t.onMixinsDocumentPaste(s)}}},[e("i",{staticClass:"iconfont el-icon-copy-document"})])])],1)])])]:t._e()],2)})),0):e("div",{staticClass:"default-content"},[e("img",{staticClass:"logo",attrs:{src:t.aiLogo1,alt:""}}),e("div",{staticClass:"line"},[t._v(t._s(t.aiModelDesc[t.agent]||"你好啊")+"，")]),e("div",{staticClass:"line more"},[t._v(t._s(t.aiModelDesc[t.agent+"_1"]||`我是${t.aiName}，水务行业全能AI助理`))]),t.params.qaRecommendationList.length?e("div",{staticClass:"qa-recommendation-list"},[e("div",{staticClass:"list-container"},t._l(t.params.qaRecommendationList,(function(s,i){return e("div",{key:i,staticClass:"container-item"},[e("div",{staticClass:"title"},[t._v(t._s(s.title))]),t._l(s.data,(function(s,i){return e("div",{key:i,staticClass:"list-container-item",on:{click:function(e){return t.selectQa(s)}}},[e("img",{staticClass:"item-center-img",attrs:{src:s.icon,alt:""}}),e("div",{staticClass:"item-center-text"},[t._v(t._s(s.name))])])}))],2)})),0)]):t._e()])])],1),e("div",{staticClass:"bottom-content-toolbar"},[e("transition",{attrs:{name:"el-fade-in"}},[!t.params.scrollEnd&&t.params.answerList.length?e("div",{staticClass:"scroll-to-bottom",class:{loading:t.answering},on:{click:t.onScrollBottom}},[e("i",{staticClass:"el-icon-bottom"})]):t._e()]),e("transition",{attrs:{name:"el-fade-in"}},[t.audioIsPlaying?e("div",{staticClass:"scroll-top-stop-voice loading",on:{click:function(e){return t.onMixinsTextToVoiceStop(null,t.params.answerList)}}},[e("el-tooltip",{attrs:{content:"中止朗读",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])])])],1):t._e()]),e("transition",{attrs:{name:"el-fade-in"}},[t.recognitionShow?e("div",{staticClass:"evoke-yi-nuo"},[e("div",{staticClass:"logo"},[e("div",{staticClass:"img"},[e("img",{attrs:{src:t.icon.logo2,alt:""}})]),e("span",[t._v("您好，我是一诺AI语音助手")])]),e("span",[t._v("可以试试对我说您想要了解的问题")]),e("div",{staticClass:"recording tools"},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])])]):t._e()]),e("div",{staticClass:"toolbar-tools"},[e("div",{staticClass:"tools-left"},[e("el-upload",{ref:"fileUploader",staticClass:"file-uploader",staticStyle:{"margin-left":"0"},attrs:{action:"action",accept:".txt,.json,.md,.mdx,.pdf,.html,.csv,.htm,.markdown,.xls,.xlsx,.docx,image/*,.wav","show-file-list":!1,"auto-upload":!1,limit:1,"http-request":t.onUploadFileQuest,"on-change":t.onFileUploadChange,"on-exceed":t.uploadExceed}},[e("div",{staticClass:"item"},[e("el-tooltip",{attrs:{content:"附件上传",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"tool-item"},[e("i",{staticClass:"iconfont icon-fujian"})])])],1)])],1),t.agentIntroduce&&!t.isMobile?e("introduce",{attrs:{"agent-introduce":t.agentIntroduce,"is-mobile":t.isMobile,"custom-title":t.customTitle}}):t._e(),t.hideHistory||t.isMobile?t._e():e("el-tooltip",{staticClass:"item",style:{marginLeft:t.agentIntroduce?"none":"12px"},attrs:{content:"对话历史",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"tool-item tool-history-chat",on:{click:function(e){t.params.historyListVis=!0}}},[e("i",{staticClass:"iconfont icon-lishiliaotian"})])]),t.isMobile?t._e():e("el-tooltip",{staticClass:"item",attrs:{content:"新对话",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":700}},[e("div",{staticClass:"tool-item tool-new-chat",on:{click:t.getNewAnswer}},[e("i",{staticClass:"iconfont icon-xinjianduihuaxiaoxi"})])]),e("el-drawer",{attrs:{direction:t.isMobile?"btt":"rtl",size:t.isMobile?"65%":t.historyDrawWidth,visible:t.params.historyListVis,"append-to-body":"","custom-class":"chat-history-drawer"},on:{"update:visible":function(e){return t.$set(t.params,"historyListVis",e)}}},[e("div",{staticClass:"chat-history-drawer-title",attrs:{slot:"title"},slot:"title"},[e("div",[t._v("对话历史 "),t.chatHistoryListLen?e("span",{staticClass:"number"},[t._v("("+t._s(t.chatHistoryListLen)+")")]):t._e()]),e("div",{staticClass:"search-input"},[e("el-input",{attrs:{clearable:"",placeholder:"搜索历史记录","prefix-icon":"el-icon-search"},model:{value:t.params.searchVal,callback:function(e){t.$set(t.params,"searchVal",e)},expression:"params.searchVal"}})],1)]),e("div",{staticClass:"ai-assistant-chat-history"},[e("div",{staticClass:"ai-assistant-chat-history-history-list"},[e("el-scrollbar",{staticStyle:{height:"100%"},attrs:{native:t.isMobile,"wrap-style":"overflow-x:hidden;"}},t._l(t.chatHistoryList,(function(s,i,n){return s.length?e("div",{staticClass:"list-item"},[e("span",{staticClass:"unit"},[t._v(t._s(i))]),t._l(s,(function(s,i){return e("div",{key:i,staticClass:"list-item-child",class:{active:t.params.sessionId===s.id,"is-edit":s.isEdit,"is-mobile":t.isMobile},on:{mouseenter:function(e){return t.onMouseEnter(s)},mouseleave:function(e){return t.onMouseLeave(s)}}},[e("span",{staticClass:"title",on:{click:function(e){return t.onSelectHistory(s)}}},[t._v(t._s(s.title))]),e("div",{staticClass:"delete-button",on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.onHandlerHistoryDelete(s)}}},[e("i",{staticClass:"iconfont icon-shanchu"})])])}))],2):t._e()})),0),0===t.chatHistoryListLen?e("div",{staticClass:"chat-history-no-data"},[e("img",{attrs:{src:t.icon.nodata,alt:""}}),e("span",[t._v("暂无历史记录")])]):t._e()],1)])]),t._t("tools")],2),e("div",{staticClass:"question_content"},[e("div",{staticClass:"box"},[t.params.fileList.length?e("div",{staticClass:"box-header",class:{column:"image"===t.params.fileList[0].type}},["image"===t.params.fileList[0].type?e("div",{staticClass:"file-image"},[e("img",{attrs:{src:t.params.fileList[0].url,alt:""}}),e("div",{staticClass:"delete-button"},[e("i",{staticClass:"el-icon-close",on:{click:function(e){return e.stopPropagation(),t.onFileDelete.apply(null,arguments)}}})])]):e("div",{staticClass:"file-name"},[t._v(" "+t._s(t.params.fileList[0]&&t.params.fileList[0].name)+" "),e("div",{staticClass:"delete-button"},[e("i",{staticClass:"el-icon-close",on:{click:function(e){return e.stopPropagation(),t.onFileDelete.apply(null,arguments)}}})])]),"image"===t.params.fileList[0].type?e("div",{staticClass:"file-name",on:{click:function(e){return t.selectQa("解释这张图片")}}},[t._v("解释这张图片"),e("i",{staticClass:"el-icon-arrow-right"})]):t._e()]):t._e(),e("div",{staticClass:"box-content",class:{"is-show-voice-to-text":t.isShowVoiceToText}},[e("div",{staticClass:"el-textarea el-input--mini el-input--suffix"},[e("pre",{ref:"inputHtml",staticClass:"el-textarea__inner",staticStyle:{visibility:"hidden","white-space":"pre-wrap",height:"auto"},domProps:{innerHTML:t._s(t.formatKeywordHtml(t.params.keyword))}}),e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.params.keyword,expression:"params.keyword"}],ref:"input",staticClass:"el-textarea__inner",staticStyle:{position:"absolute",top:"0",height:"100%"},attrs:{autosize:{maxRows:16,minRows:3},rows:3,placeholder:`给 ${t.aiName} 发送消息`,resize:"none",maxlength:"2000",enterkeyhint:"send"},domProps:{value:t.params.keyword},on:{keydown:t.Keydown,focus:function(e){return t.$emit("input-focus",!0)},blur:function(e){t.onBlur(),t.$emit("input-focus",!1)},input:function(e){e.target.composing||t.$set(t.params,"keyword",e.target.value)}}})]),e("transition",{attrs:{name:"fade"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.tools.visible,expression:"tools.visible"}],ref:"modeListPopover",staticClass:"mode-list-popover el-dropdown-menu"},t._l(t.aiModelListFilter,(function(s){return e("div",{staticClass:"el-dropdown-menu__item ai-assistant-model-item",on:{click:function(e){return t.onKeydownModelSelect(s)}}},[e("img",{attrs:{src:s.icon,alt:""}}),e("span",[t._v(t._s(s.name))])])})),0)])],1),e("div",{staticClass:"box-toolbar"},[e("div",{staticClass:"send-tools"}),t.mediaRecorderLoading?e("div",{staticClass:"send-item-recorder-loading"},[t._v("语音转换中...")]):t._e(),t.isShowVoiceToText?e("el-tooltip",{staticClass:"item",attrs:{content:t.isRecording?"停止语音输入":"语音输入",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":700}},[t.isRecording?e("div",{staticClass:"send-item-tool border-right recording",on:{click:t.stopRecording,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.stopRecording.apply(null,arguments)}}},[e("div",{staticClass:"voice-loading"},[e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"}),e("div",{staticClass:"voice-loading-bar"})])]):e("div",{staticClass:"send-item-tool border-right",class:{recording:t.isMobile},on:{click:t.startRecording,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.startRecording.apply(null,arguments)}}},[e("i",{staticClass:"iconfont icon-yuyin"})])]):t._e(),t.answering?t._e():e("div",{staticClass:"send-item",class:{answering:this.answering||t.params.loading},attrs:{disabled:!t.params.keyword},on:{click:t.getAnswer,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.getAnswer()}}},[e("i",{staticClass:"iconfont icon-fasong"})]),t.answering?e("el-tooltip",{attrs:{content:"中止",placement:"top",disabled:t.isMobile,"open-delay":200,"hide-after":800}},[e("div",{staticClass:"send-item-abort",on:{click:t.onAbortAnswer,touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.onAbortAnswer.apply(null,arguments)}}})]):t._e()],1)])]),t.isMobile?t._e():e("div",{staticClass:"toolbar-desc"},[e("span",[t._v("内容由 "+t._s(t.aiName)+" 生成，请仔细甄别")]),e("div",{staticClass:"toolbar-right"})])],1)])])}),[],!1,null,"05604e31",null);e.default=h.exports},"9d14":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAVlJREFUOE+lkz1Lw1AUht9zqejimPgx9Rc03VTQwd0xkRSEOrg5iCio/8DBwcVR0MnSBJydFHTQyaSTmw7BYuPYreUeSUhCGvMhmOmQe+5zzznPvYR/flS2v6H7pgDmHVs5L8r7BdAM/xbMMwwMCbQMYo8ZXggQ8HpddT8NmwBo+uAUoDbA1yT4O3sqS3FAQMuxlYd4bQLQ3Pw6lJKWerZqNI3BtmOpV0FiHDf0gSUEvzjdubNSgABdMHBPwHqQGMcSvFsKCIZGxHuupa7kDa26ArNf53HtkcHPeQAC6Zgarbk3i0+5LYT9mv06pNBztQlpO52Fj2ILKYWF9yOjMrFQpTAGZlUmgKoBxgBN918BvnNt9Tj4lwCC3nlcewfxpWupO3ktRFUepQdZcBND+w4AN4wEpgVji6UYMvFJz1Y6hRa01ucqRrWNKEGLAG/EmM2rrPQ1/uWl/wBHcqERyRjyMAAAAABJRU5ErkJggg=="},cf27:function(t,e,s){"use strict";s("2456")},df73:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAQCAYAAAAbBi9cAAAAAXNSR0IArs4c6QAAARxJREFUOE/tlLFOAkEURe9FA+xfkPgJa2GnJITKhmBiufABdmJCpVQmLp21ET7A5QMoXBNaqO1o/YNJiOHpI8w64C5LZ+MUO5PMfee+7LsZYr3CyIg977tTMLhuem3VUz/3I1M5FDxvAwQoEziB4APE+697wfCm6Q0SUFYHa4O565ylXXWUthRCkYsDMKRgsgSerHuaPhPUj8xcgMpGEVHtNLw4FbRqHwgAvLmiMDKvAM7cIhG0bVfbddwocBzDkVHIqQvqNLyeM+XE6FN4pKCfsRN342npPG/8FBnW/MWj1RWIWgqo2P1ORGkXjIKrf9Af/6MkDxq48axYzhu/3tf9xaUNLLk85sOLaZEIQMRu4PJgWlcgbiGI9Sn5AgCFoPwrMbS4AAAAAElFTkSuQmCC"},ff4c2:function(t,e,s){}}]);