import ast
from datetime import datetime, time
from typing import Optional, List, Any


def cast_list(s: Any, must=False) -> Optional[List[Any]]:

    if isinstance(s, list):
        return s

    if isinstance(s, str):
        s = s.strip()
        if not (s.startswith("[") and s.endswith("]")):
            if must:
                raise ValueError("无法转换为List")
            return None
        try:
            result = ast.literal_eval(s)
            if isinstance(result, list):
                return result
            else:
                if must:
                    raise ValueError(f"不支持转换为list的str{s}")
                return None
        except (SyntaxError, ValueError, TypeError):
            if must:
                raise ValueError(f"不支持转换为list的str{s}")
            return None

    else:
        if must:
            raise ValueError(f"不支持转换为list的类型{type(s)}")
        return None


def cast_datetime(s: Any, must=False) -> Optional[datetime]:

    if isinstance(s, datetime):
        return s

    if not isinstance(s, str):
        if must:
            raise ValueError(f"不支持转换为datetime的类型{type(s)}")
        return None

    s = s.strip()
    datetime_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%dT%H:%M:%S",
        "%Y/%m/%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y/%m/%d %H:%M",
        "%Y/%m/%d",
        "%Y-%m-%d",
    ]
    for fmt in datetime_formats:
        try:
            return datetime.strptime(s, fmt)
        except ValueError:
            continue

    if must:
        raise ValueError(f"不支持的datetime的格式{s}")
    return None


def cast_time(s: str, must=False) -> Optional[time]:
    if isinstance(s, time):
        return s

    if not isinstance(s, str):
        if must:
            raise ValueError(f"不支持转换为time的类型{type(s)}")
        return None
    s = s.strip()
    time_formats = [
        "%H:%M:%S",
        "%H:%M",
        "%I:%M:%S %p",
        "%I:%M %p",
    ]
    for fmt in time_formats:
        try:
            return datetime.strptime(s, fmt).time()
        except ValueError:
            continue
    if must:
        raise ValueError(f"不支持的time的格式{s}")
    return None


def cast_bool(s: str, must=False) -> Optional[bool]:

    if isinstance(s, bool):
        return s

    if not isinstance(s, str):
        if must:
            raise ValueError(f"不支持转换为布尔值的类型{type(s)}")
        return None

    match s.strip().lower():
        case "true":
            return True
        case "false":
            return False
        case _:
            if must:
                raise ValueError(f"不支持的布尔值格式{s}")
            return None


def cast_float(s: str, must=False) -> Optional[float]:

    if isinstance(s, float):
        return s

    if isinstance(s, int):
        return float(s)

    if not isinstance(s, str):
        if must:
            raise ValueError(f"不支持转换为数字的类型{type(s)}")
        return None

    s = s.strip()
    try:
        return float(s)
    except ValueError:
        if must:
            raise ValueError(f"不支持的数字格式{s}")
        return None

def cast_str(s: Any, must=False) -> Optional[str]:
   return str(s)


def is_dict_str_any(obj: Any) -> bool:
    if not isinstance(obj, dict):
        return False
    return all(isinstance(key, str) for key in obj.keys())