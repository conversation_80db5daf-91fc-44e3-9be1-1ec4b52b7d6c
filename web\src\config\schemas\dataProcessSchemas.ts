/**
 * 数据处理组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const variableAssignmentSchema: ComponentConfigSchema = {
  componentType: 'variable_assignment',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '变量',
      description: '变量赋值',
      icon: 'Document',
      order: 1,
      collapsible: false,
    }
  ],

  fields: {
    variables: {
      type: 'variables',
      label: '变量',
      description: '',
      placeholder: '',
      group: 'basic',
      required: true,
      order: 1,
    },
  },

  presets: {},

  examples: [],
}

export const textTemplateSchema: ComponentConfigSchema = {
  componentType: 'text_template',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      // description: '文本模板的基本配置',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    text: {
      type: 'textarea',
      label: '文本模板',
      description: '',
      placeholder: '',
      group: 'basic',
      required: true,
      order: 1,
      rows: 4,
      variableSupport: true,
    },
    output_variable: {
      type: 'string',
      label: '输入内容变量',
      // description: '存储格式化后的变量名（可选）',
      placeholder: 'formatted_text',
      group: 'response',
      required: true,
      order: 2,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 1,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 3,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' }
      ]
    }
  },

  presets: {},

  examples: [],
}

export const addTimeSchema: ComponentConfigSchema = {
  componentType: 'add_time',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      // description: '文本模板的基本配置',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    date: {
      type: 'string',
      label: '原日期时间',
      description: '',
      placeholder: '',
      group: 'basic',
      required: true,
      order: 1,
      rows: 4,
      variableSupport: true,
    },
    method: {
      type: 'radio',
      label: '调整方式',
      description: '',
      placeholder: '',
      group: 'basic',
      required: true,
      options:[
        { label: '增加时间', value: 'add' },
        { label: '减少时间', value: 'decrease' }
      ]
    },
    duration: {
      type: 'number',
      label: '调整时长',
      description: '',
      placeholder: '',
      group: 'basic',
      controls:false,
      required: true,
      suffix:{
        key:'duration_unit',
        options:[
          { label: '秒', value: 'seconds' },
          { label: '分钟', value: 'minutes' },
          { label: '小时', value: 'hours' },
          { label: '天', value: 'days' }
        ]
      },
      
    },
    response_date_variable: {
      type: 'string',
      label: '响应日期变量名',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'response_date',
      group: 'response',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    response_timestamp_variable: {
      type: 'string',
      label: '响应时间戳变量名',
      // description: '存储HTTP状态码的变量名（可选）',
      placeholder: 'response_timestamp',
      group: 'response',
      order: 3,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    response_week_variable: {
      type: 'string',
      label: '响应星期变量名',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'response_week',
      group: 'response',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
  },

  presets: {},

  examples: [],
}
