import os

from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Pt

from actions.file import group, logger

from typing import Dict, Optional, Tuple

from core.executor import ExecutionContext, ActionContext


@group.action(
    type="word_create",
    label="创建Word文档",
    description="创建新的Word文档并添加内容",
    category="file",
    icon="Document",
    config_schema={
        "content": {"type": "string", "required": True},
        "output_folder": {"type": "string", "required": True},
        "output_filename": {"type": "string", "required": True},
        "title": {"type": "string", "default": ""},
        "font_size": {"type": "number", "default": 12},
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
    inputs=["content"],
    outputs=["word_path"],
)
def word_create(context: ActionContext, config: Dict):
    try:
        # 创建临时内容文件
        temp_file = os.path.join(os.environ.get("TEMPDIR", "/tmp"), "word_content.txt")
        with open(temp_file, "w", encoding="utf-8") as f:
            f.write("这是Word文档的第一行内容\n\n这是第二行内容，包含换行")

        # 创建Word文档
        success, msg = create_word_document(
            temp_content_file=temp_file, output_path="./example.docx", title="示例文档"
        )
        print(msg)
    except Exception as e:
        print(f"Word处理错误: {e}")

    # 示例2: 保存Markdown文件
    try:
        md_content = "# 标题\n\n这是一段Markdown内容\n- 列表项1\n- 列表项2"
        success, msg, path = save_markdown_file(
            content=md_content,
            output_folder="./",
            output_filename="example.md",
            encoding="utf-8",
            retry_times=2,
            retry_delay=1,
        )
        print(msg)
    except Exception as e:
        print(f"Markdown处理错误: {e}")


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不允许的字符
    """
    invalid_chars = '/\\:*?"<>|'
    for char in invalid_chars:
        filename = filename.replace(char, "_")
    return filename


def create_word_document(
    temp_content_file: str, output_path: str, title: Optional[str] = None
) -> Tuple[bool, str]:
    """
    创建Word文档（替代Robot Framework的Create Word Document关键字）

    参数:
        temp_content_file: 包含文档内容的临时文本文件路径
        output_path: 生成的Word文档保存路径
        title: 文档标题（可选）

    返回:
        (是否成功, 消息)
    """
    try:
        logger.info("开始创建Word文档")

        # 确保输出目录存在
        output_folder = os.path.dirname(output_path)
        os.makedirs(output_folder, exist_ok=True)
        logger.info(f"输出路径: {output_path}")

        # 读取临时内容文件
        if not os.path.exists(temp_content_file):
            return False, f"临时内容文件不存在: {temp_content_file}"

        with open(temp_content_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 创建Word文档
        doc = Document()

        # 添加标题（如果提供）
        if title:
            title_paragraph = doc.add_heading(title, level=0)
            title_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        # 添加内容（按换行分割段落）
        paragraphs = content.split("\n")
        for para_text in paragraphs:
            para = doc.add_paragraph(para_text)
            # 设置默认字体和大小
            run = para.runs[0] if para.runs else para.add_run()
            run.font.name = "Arial"
            run.font.size = Pt(12)

        # 保存文档
        doc.save(output_path)

        # 清理临时文件
        if os.path.exists(temp_content_file):
            os.remove(temp_content_file)

        logger.info(f"Word文档已保存到: {output_path}")
        return True, f"Word文档创建成功: {output_path}"

    except Exception as e:
        error_msg = f"Word文档创建失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


def save_markdown_file(
    content: str,
    output_folder: str,
    output_filename: str,
    encoding: str = "utf-8",
    retry_times: int = 0,
    retry_delay: int = 2,
) -> Tuple[bool, str, str]:
    """
    保存Markdown文件（替代Robot Framework的markdown_save组件）

    参数:
        content: 要保存的Markdown内容
        output_folder: 输出文件夹路径
        output_filename: 输出文件名
        encoding: 文件编码
        retry_times: 失败重试次数
        retry_delay: 重试间隔（秒）

    返回:
        (是否成功, 消息, 文件路径)
    """
    output_path = os.path.join(output_folder, output_filename)
    attempt = 0

    while attempt <= retry_times:
        try:
            # 确保输出目录存在
            os.makedirs(output_folder, exist_ok=True)

            # 写入文件
            with open(output_path, "w", encoding=encoding) as f:
                f.write(content)

            logger.info(f"Markdown文件已保存到: {output_path}")
            return True, "Markdown文件保存成功", output_path

        except Exception as e:
            attempt += 1
            error_msg = (
                f"Markdown文件保存失败（尝试{attempt}/{retry_times + 1}）: {str(e)}"
            )
            logger.warning(error_msg)

            if attempt > retry_times:
                logger.error(f"达到最大重试次数，保存失败")
                return False, error_msg, output_path

            # 重试前等待
            import time

            time.sleep(retry_delay)

    # 理论上不会到达这里，因循环已处理所有尝试
    return False, "未知错误", output_path
