testdoc = {"generated":"20130201 11:27:20 GMT +03:00","generatedMillis":1359710840000,"suite":{"doc":"","fullName":"Misc &amp; Dir.Suite","id":"s1","keywords":[],"metadata":[],"name":"Misc &amp; Dir.Suite","numberOfTests":182,"relativeSource":"","source":"","suites":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc","id":"s1-s1","keywords":[],"metadata":[],"name":"Misc","numberOfTests":170,"relativeSource":"../../../atest/testdata/misc","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc","suites":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Dummy Lib Test","id":"s1-s1-s1","keywords":[],"metadata":[],"name":"Dummy Lib Test","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/dummy_lib_test.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/dummy_lib_test.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Dummy Lib Test.Dummy Test","id":"s1-s1-s1-t1","keywords":[{"arguments":"","name":"dummykw","type":"KEYWORD"}],"name":"Dummy Test","tags":[],"timeout":""}]},{"doc":"<p>Initially created for testing for loops with testdoc but can be used also for other purposes and extended as needed.</p>","fullName":"Misc &amp; Dir.Suite.Misc.For Loops","id":"s1-s1-s2","keywords":[],"metadata":[],"name":"For Loops","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/for_loops.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/for_loops.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.For Loops.For Loop In Test","id":"s1-s1-s2-t1","keywords":[{"arguments":"","name":"${pet} IN [ cat | dog | horse ]","type":"FOR"}],"name":"For Loop In Test","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.For Loops.For In Range Loop In Test","id":"s1-s1-s2-t2","keywords":[{"arguments":"","name":"${i} IN RANGE [ 10 ]","type":"FOR"}],"name":"For In Range Loop In Test","tags":[],"timeout":""}]},{"doc":"<p>We have <i>formatting</i> and &lt;escaping&gt;.</p>\n<table border=\"1\">\n<tr>\n<td><b>Name</b></td>\n<td><b>URL</b></td>\n</tr>\n<tr>\n<td>Robot</td>\n<td><a href=\"http://robotframework.org\">http://robotframework.org</a></td>\n</tr>\n<tr>\n<td>Custom</td>\n<td><a href=\"http://robotframework.org\">link</a></td>\n</tr>\n</table>","fullName":"Misc &amp; Dir.Suite.Misc.Formatting And Escaping","id":"s1-s1-s3","keywords":[],"metadata":[["Escape","<p>this is &lt;b&gt;not bold&lt;/b&gt;</p>"],["Format","<p>this is <b>bold</b></p>"]],"name":"Formatting And Escaping","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/formatting_and_escaping.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/formatting_and_escaping.txt","suites":[],"tests":[{"doc":"<p><b>I</b> can haz <i>formatting</i> &amp; &lt;escaping&gt;!!</p>\n<ul>\n<li>list</li>\n<li>here</li>\n</ul>","fullName":"Misc &amp; Dir.Suite.Misc.Formatting And Escaping.Formatting","id":"s1-s1-s3-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"Formatting","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Formatting And Escaping.&lt;Escaping&gt;","id":"s1-s1-s3-t2","keywords":[{"arguments":"&lt;&amp;&gt;","name":"&lt;blink&gt;NO&lt;/blink&gt;","type":"KEYWORD"}],"name":"&lt;Escaping&gt;","tags":["*not bold*","&lt;b&gt;not bold either&lt;/b&gt;"],"timeout":""}]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests","id":"s1-s1-s4","keywords":[{"arguments":"Setup","name":"Log","type":"SETUP"},{"arguments":"","name":"Noop","type":"TEARDOWN"}],"metadata":[["Something","<p>My Value</p>"]],"name":"Many Tests","numberOfTests":5,"relativeSource":"../../../atest/testdata/misc/many_tests.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/many_tests.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests.First","id":"s1-s1-s4-t1","keywords":[{"arguments":"Test 1","name":"Log","type":"KEYWORD"}],"name":"First","tags":["f1","t1","t2"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests.Second One","id":"s1-s1-s4-t2","keywords":[{"arguments":"Test 2","name":"Log","type":"KEYWORD"}],"name":"Second One","tags":["d1","d2","f1"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests.Third One","id":"s1-s1-s4-t3","keywords":[{"arguments":"Test 3","name":"Log","type":"KEYWORD"}],"name":"Third One","tags":["d1","d2","f1"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests.Fourth One With More Complex Name","id":"s1-s1-s4-t4","keywords":[{"arguments":"Test 4","name":"Log","type":"KEYWORD"}],"name":"Fourth One With More Complex Name","tags":["d1","d2","f1"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Many Tests.Fifth","id":"s1-s1-s4-t5","keywords":[{"arguments":"Test 5","name":"Log","type":"KEYWORD"}],"name":"Fifth","tags":["d1","d2","f1"],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites","id":"s1-s1-s5","keywords":[],"metadata":[],"name":"Multiple Suites","numberOfTests":132,"relativeSource":"../../../atest/testdata/misc/multiple_suites","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites","suites":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First","id":"s1-s1-s5-s1","keywords":[],"metadata":[],"name":"Suite First","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/01__suite_first.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/01__suite_first.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test1","id":"s1-s1-s5-s1-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test2","id":"s1-s1-s5-s1-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test3","id":"s1-s1-s5-s1-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test4","id":"s1-s1-s5-s1-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test5","id":"s1-s1-s5-s1-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test6","id":"s1-s1-s5-s1-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test7","id":"s1-s1-s5-s1-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test8","id":"s1-s1-s5-s1-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test9","id":"s1-s1-s5-s1-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test10","id":"s1-s1-s5-s1-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test11","id":"s1-s1-s5-s1-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite First.test12","id":"s1-s1-s5-s1-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1","id":"s1-s1-s5-s2","keywords":[],"metadata":[],"name":"Sub.Suite.1","numberOfTests":24,"relativeSource":"../../../atest/testdata/misc/multiple_suites/02__sub.suite.1","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/02__sub.suite.1","suites":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4","id":"s1-s1-s5-s2-s1","keywords":[],"metadata":[],"name":"Suite4","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/02__sub.suite.1/first__suite4.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/02__sub.suite.1/first__suite4.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test1","id":"s1-s1-s5-s2-s1-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test2","id":"s1-s1-s5-s2-s1-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test3","id":"s1-s1-s5-s2-s1-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test4","id":"s1-s1-s5-s2-s1-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test5","id":"s1-s1-s5-s2-s1-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test6","id":"s1-s1-s5-s2-s1-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test7","id":"s1-s1-s5-s2-s1-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test8","id":"s1-s1-s5-s2-s1-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test9","id":"s1-s1-s5-s2-s1-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test10","id":"s1-s1-s5-s2-s1-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test11","id":"s1-s1-s5-s2-s1-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1.Suite4.test12","id":"s1-s1-s5-s2-s1-t12","keywords":[{"arguments":"warning, WARN","name":"Log","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2.","id":"s1-s1-s5-s2-s2","keywords":[],"metadata":[],"name":".Sui.te.2.","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/02__sub.suite.1/second__.Sui.te.2..html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/02__sub.suite.1/second__.Sui.te.2..html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test1","id":"s1-s1-s5-s2-s2-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test2","id":"s1-s1-s5-s2-s2-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test3","id":"s1-s1-s5-s2-s2-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test4","id":"s1-s1-s5-s2-s2-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test5","id":"s1-s1-s5-s2-s2-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test6","id":"s1-s1-s5-s2-s2-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test7","id":"s1-s1-s5-s2-s2-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test8","id":"s1-s1-s5-s2-s2-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test9","id":"s1-s1-s5-s2-s2-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test10","id":"s1-s1-s5-s2-s2-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test11","id":"s1-s1-s5-s2-s2-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Sub.Suite.1..Sui.te.2..test12","id":"s1-s1-s5-s2-s2-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]}],"tests":[]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3","id":"s1-s1-s5-s3","keywords":[],"metadata":[],"name":"Suite3","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/03__suite3.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/03__suite3.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test1","id":"s1-s1-s5-s3-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test2","id":"s1-s1-s5-s3-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test3","id":"s1-s1-s5-s3-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test4","id":"s1-s1-s5-s3-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test5","id":"s1-s1-s5-s3-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test6","id":"s1-s1-s5-s3-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test7","id":"s1-s1-s5-s3-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test8","id":"s1-s1-s5-s3-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test9","id":"s1-s1-s5-s3-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test10","id":"s1-s1-s5-s3-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test11","id":"s1-s1-s5-s3-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite3.test12","id":"s1-s1-s5-s3-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4","id":"s1-s1-s5-s4","keywords":[],"metadata":[],"name":"Suite4","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/04__suite4.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/04__suite4.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test1","id":"s1-s1-s5-s4-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test2","id":"s1-s1-s5-s4-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test3","id":"s1-s1-s5-s4-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test4","id":"s1-s1-s5-s4-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test5","id":"s1-s1-s5-s4-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test6","id":"s1-s1-s5-s4-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test7","id":"s1-s1-s5-s4-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test8","id":"s1-s1-s5-s4-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test9","id":"s1-s1-s5-s4-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test10","id":"s1-s1-s5-s4-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test11","id":"s1-s1-s5-s4-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite4.test12","id":"s1-s1-s5-s4-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5","id":"s1-s1-s5-s5","keywords":[],"metadata":[],"name":"Suite5","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/05__suite5.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/05__suite5.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test1","id":"s1-s1-s5-s5-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test2","id":"s1-s1-s5-s5-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test3","id":"s1-s1-s5-s5-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test4","id":"s1-s1-s5-s5-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test5","id":"s1-s1-s5-s5-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test6","id":"s1-s1-s5-s5-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test7","id":"s1-s1-s5-s5-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test8","id":"s1-s1-s5-s5-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test9","id":"s1-s1-s5-s5-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test10","id":"s1-s1-s5-s5-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test11","id":"s1-s1-s5-s5-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite5.test12","id":"s1-s1-s5-s5-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10","id":"s1-s1-s5-s6","keywords":[],"metadata":[],"name":"Suite10","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/10__suite10.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/10__suite10.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test1","id":"s1-s1-s5-s6-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test2","id":"s1-s1-s5-s6-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test3","id":"s1-s1-s5-s6-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test4","id":"s1-s1-s5-s6-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test5","id":"s1-s1-s5-s6-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test6","id":"s1-s1-s5-s6-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test7","id":"s1-s1-s5-s6-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test8","id":"s1-s1-s5-s6-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test9","id":"s1-s1-s5-s6-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test10","id":"s1-s1-s5-s6-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test11","id":"s1-s1-s5-s6-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite10.test12","id":"s1-s1-s5-s6-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6","id":"s1-s1-s5-s7","keywords":[],"metadata":[],"name":"Suite 6","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/suite%206.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/suite 6.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test1","id":"s1-s1-s5-s7-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test2","id":"s1-s1-s5-s7-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test3","id":"s1-s1-s5-s7-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test4","id":"s1-s1-s5-s7-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test5","id":"s1-s1-s5-s7-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test6","id":"s1-s1-s5-s7-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test7","id":"s1-s1-s5-s7-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test8","id":"s1-s1-s5-s7-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test9","id":"s1-s1-s5-s7-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test10","id":"s1-s1-s5-s7-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test11","id":"s1-s1-s5-s7-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":["some"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 6.test12","id":"s1-s1-s5-s7-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":["some"],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7","id":"s1-s1-s5-s8","keywords":[],"metadata":[],"name":"SUite7","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/SUite7.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/SUite7.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test1","id":"s1-s1-s5-s8-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test2","id":"s1-s1-s5-s8-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test3","id":"s1-s1-s5-s8-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test4","id":"s1-s1-s5-s8-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test5","id":"s1-s1-s5-s8-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test6","id":"s1-s1-s5-s8-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test7","id":"s1-s1-s5-s8-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test8","id":"s1-s1-s5-s8-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test9","id":"s1-s1-s5-s8-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test10","id":"s1-s1-s5-s8-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test11","id":"s1-s1-s5-s8-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.SUite7.test12","id":"s1-s1-s5-s8-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8","id":"s1-s1-s5-s9","keywords":[],"metadata":[],"name":"suiTe 8","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/suiTe_8.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/suiTe_8.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test1","id":"s1-s1-s5-s9-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test2","id":"s1-s1-s5-s9-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test3","id":"s1-s1-s5-s9-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test4","id":"s1-s1-s5-s9-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test5","id":"s1-s1-s5-s9-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test6","id":"s1-s1-s5-s9-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test7","id":"s1-s1-s5-s9-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test8","id":"s1-s1-s5-s9-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test9","id":"s1-s1-s5-s9-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test10","id":"s1-s1-s5-s9-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test11","id":"s1-s1-s5-s9-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.suiTe 8.test12","id":"s1-s1-s5-s9-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name","id":"s1-s1-s5-s10","keywords":[],"metadata":[],"name":"Suite 9 Name","numberOfTests":12,"relativeSource":"../../../atest/testdata/misc/multiple_suites/suite_9_name.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/multiple_suites/suite_9_name.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test1","id":"s1-s1-s5-s10-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test1","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test2","id":"s1-s1-s5-s10-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test2","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test3","id":"s1-s1-s5-s10-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test3","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test4","id":"s1-s1-s5-s10-t4","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test4","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test5","id":"s1-s1-s5-s10-t5","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test5","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test6","id":"s1-s1-s5-s10-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test6","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test7","id":"s1-s1-s5-s10-t7","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test7","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test8","id":"s1-s1-s5-s10-t8","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test8","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test9","id":"s1-s1-s5-s10-t9","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test9","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test10","id":"s1-s1-s5-s10-t10","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test10","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test11","id":"s1-s1-s5-s10-t11","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test11","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Multiple Suites.Suite 9 Name.test12","id":"s1-s1-s5-s10-t12","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"test12","tags":[],"timeout":""}]}],"tests":[]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Normal","id":"s1-s1-s6","keywords":[],"metadata":[["Something","<p>My Value</p>"]],"name":"Normal","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/normal.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/normal.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Normal.First One","id":"s1-s1-s6-t1","keywords":[{"arguments":"Test 1","name":"Log","type":"KEYWORD"},{"arguments":"Logging with debug level, DEBUG","name":"Log","type":"KEYWORD"},{"arguments":"","name":"logs on trace","type":"KEYWORD"}],"name":"First One","tags":["f1","t1","t2"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Normal.Second One","id":"s1-s1-s6-t2","keywords":[{"arguments":"Test 2","name":"Log","type":"KEYWORD"},{"arguments":"0.01","name":"Sleep","type":"KEYWORD"}],"name":"Second One","tags":["d1","d_2","f1"],"timeout":""}]},{"doc":"<p>Some tests here</p>","fullName":"Misc &amp; Dir.Suite.Misc.Pass And Fail","id":"s1-s1-s7","keywords":[{"arguments":"Suite Setup","name":"My Keyword","type":"SETUP"}],"metadata":[],"name":"Pass And Fail","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/pass_and_fail.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/pass_and_fail.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Pass And Fail.Pass","id":"s1-s1-s7-t1","keywords":[{"arguments":"Pass","name":"My Keyword","type":"KEYWORD"}],"name":"Pass","tags":["force","pass"],"timeout":""},{"doc":"<p>FAIL Expected failure</p>","fullName":"Misc &amp; Dir.Suite.Misc.Pass And Fail.Fail","id":"s1-s1-s7-t2","keywords":[{"arguments":"Fail","name":"My Keyword","type":"KEYWORD"},{"arguments":"msg=Expected failure","name":"Fail","type":"KEYWORD"}],"name":"Fail","tags":["fail","force"],"timeout":""}]},{"doc":"<p>This suite was initially created for testing keyword types with listeners but can be used for other purposes too.</p>","fullName":"Misc &amp; Dir.Suite.Misc.Setups And Teardowns","id":"s1-s1-s8","keywords":[{"arguments":"","name":"Suite Setup","type":"SETUP"},{"arguments":"","name":"Suite Teardown","type":"TEARDOWN"}],"metadata":[],"name":"Setups And Teardowns","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/setups_and_teardowns.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/setups_and_teardowns.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Setups And Teardowns.Test with setup and teardown","id":"s1-s1-s8-t1","keywords":[{"arguments":"","name":"Test Setup","type":"SETUP"},{"arguments":"","name":"Keyword","type":"KEYWORD"},{"arguments":"","name":"Test Teardown","type":"TEARDOWN"}],"name":"Test with setup and teardown","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites","id":"s1-s1-s9","keywords":[{"arguments":"${SUITE_TEARDOWN_ARG}","name":"${SUITE_TEARDOWN_KW}","type":"TEARDOWN"}],"metadata":[],"name":"Suites","numberOfTests":10,"relativeSource":"../../../atest/testdata/misc/suites","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites","suites":[{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Fourth","id":"s1-s1-s9-s1","keywords":[{"arguments":"Suite Teardonw of Fourth","name":"Log","type":"TEARDOWN"}],"metadata":[["Something","<p>My Value</p>"]],"name":"Fourth","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/suites/fourth.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/fourth.html","suites":[],"tests":[{"doc":"<p>FAIL Expected</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Fourth.Suite4 First","id":"s1-s1-s9-s1-t1","keywords":[{"arguments":"Suite4_First","name":"Log","type":"KEYWORD"},{"arguments":"0.1","name":"Sleep","type":"KEYWORD"},{"arguments":"Expected","name":"Fail","type":"KEYWORD"},{"arguments":"Huhuu","name":"Log","type":"TEARDOWN"}],"name":"Suite4 First","tags":["f1","t1"],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites","id":"s1-s1-s9-s2","keywords":[],"metadata":[],"name":"Subsuites","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/suites/subsuites","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/subsuites","suites":[{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites.Sub1","id":"s1-s1-s9-s2-s1","keywords":[{"arguments":"Hello, world!","name":"Log","type":"SETUP"},{"arguments":"","name":"No Operation","type":"TEARDOWN"}],"metadata":[["Something","<p>My Value</p>"]],"name":"Sub1","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/suites/subsuites/sub1.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/subsuites/sub1.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites.Sub1.SubSuite1 First","id":"s1-s1-s9-s2-s1-t1","keywords":[{"arguments":"SubSuite1_First","name":"Log","type":"KEYWORD"},{"arguments":"${SLEEP}, Make sure elapsed time &gt; 0","name":"Sleep","type":"KEYWORD"},{"arguments":"${FAIL}, NO, This test was doomed to fail","name":"Should Be Equal","type":"KEYWORD"}],"name":"SubSuite1 First","tags":["f1","t1"],"timeout":""}]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites.Sub2","id":"s1-s1-s9-s2-s2","keywords":[],"metadata":[["Something","<p>My Value</p>"]],"name":"Sub2","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/suites/subsuites/sub2.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/subsuites/sub2.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites.Sub2.SubSuite2 First","id":"s1-s1-s9-s2-s2-t1","keywords":[{"arguments":"SubSuite2_First","name":"Log","type":"KEYWORD"},{"arguments":"${SLEEP}, Make sure elapsed time &gt; 0","name":"Sleep","type":"KEYWORD"}],"name":"SubSuite2 First","tags":["f1"],"timeout":""}]}],"tests":[]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites2","id":"s1-s1-s9-s3","keywords":[],"metadata":[],"name":"Subsuites2","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/suites/subsuites2","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/subsuites2","suites":[{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites2.Subsuite3","id":"s1-s1-s9-s3-s1","keywords":[],"metadata":[["Something","<p>My Value</p>"]],"name":"Subsuite3","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/suites/subsuites2/subsuite3.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/subsuites2/subsuite3.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites2.Subsuite3.SubSuite3 First","id":"s1-s1-s9-s3-s1-t1","keywords":[{"arguments":"SubSuite3_First","name":"Log","type":"KEYWORD"},{"arguments":"0.1","name":"Sleep","type":"KEYWORD"}],"name":"SubSuite3 First","tags":["f1","sub3","t1"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Subsuites2.Subsuite3.SubSuite3 Second","id":"s1-s1-s9-s3-s1-t2","keywords":[{"arguments":"SubSuite3_Second","name":"Log","type":"KEYWORD"}],"name":"SubSuite3 Second","tags":["f1","sub3","t2"],"timeout":""}]}],"tests":[]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite1","id":"s1-s1-s9-s4","keywords":[],"metadata":[["Something","<p>My Value</p>"]],"name":"Tsuite1","numberOfTests":3,"relativeSource":"../../../atest/testdata/misc/suites/tsuite1.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/tsuite1.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite1.Suite1 First","id":"s1-s1-s9-s4-t1","keywords":[{"arguments":"Suite1_First","name":"Log","type":"KEYWORD"},{"arguments":"0.1","name":"Sleep","type":"KEYWORD"}],"name":"Suite1 First","tags":["f1","t1"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite1.Suite1 Second","id":"s1-s1-s9-s4-t2","keywords":[{"arguments":"Suite1_Second","name":"Log","type":"KEYWORD"}],"name":"Suite1 Second","tags":["f1","t2"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite1.Third In Suite1","id":"s1-s1-s9-s4-t3","keywords":[{"arguments":"Suite2_third","name":"Log","type":"KEYWORD"}],"name":"Third In Suite1","tags":["d1","d2","f1"],"timeout":""}]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite2","id":"s1-s1-s9-s5","keywords":[],"metadata":[["Something","<p>My Value</p>"]],"name":"Tsuite2","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/suites/tsuite2.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/tsuite2.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite2.Suite2 First","id":"s1-s1-s9-s5-t1","keywords":[{"arguments":"Suite2_First","name":"Log","type":"KEYWORD"},{"arguments":"0.1","name":"Sleep","type":"KEYWORD"}],"name":"Suite2 First","tags":["f1","t1"],"timeout":""}]},{"doc":"<p>Normal test cases</p>","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite3","id":"s1-s1-s9-s6","keywords":[{"arguments":"Suite Teardown of Tsuite3","name":"Log","type":"TEARDOWN"}],"metadata":[["Something","<p>My Value</p>"]],"name":"Tsuite3","numberOfTests":1,"relativeSource":"../../../atest/testdata/misc/suites/tsuite3.html","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/suites/tsuite3.html","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Suites.Tsuite3.Suite3 First","id":"s1-s1-s9-s6-t1","keywords":[{"arguments":"Suite3_First","name":"Log","type":"KEYWORD"},{"arguments":"0.1","name":"Sleep","type":"KEYWORD"}],"name":"Suite3 First","tags":["f1","t1"],"timeout":""}]}],"tests":[]},{"doc":"<p>Initially created for testing timeouts with testdoc but can be used also for other purposes and extended as needed.</p>","fullName":"Misc &amp; Dir.Suite.Misc.Timeouts","id":"s1-s1-s10","keywords":[],"metadata":[],"name":"Timeouts","numberOfTests":3,"relativeSource":"../../../atest/testdata/misc/timeouts.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/timeouts.txt","suites":[],"tests":[{"doc":"<p>I have a timeout</p>","fullName":"Misc &amp; Dir.Suite.Misc.Timeouts.Default Test Timeout","id":"s1-s1-s10-t1","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"Default Test Timeout","tags":[],"timeout":"1 minute 42 seconds"},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Timeouts.Test Timeout With Message","id":"s1-s1-s10-t2","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"Test Timeout With Message","tags":[],"timeout":"1 day 2 hours :: The message"},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Timeouts.Test Timeout With Variable","id":"s1-s1-s10-t3","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"Test Timeout With Variable","tags":[],"timeout":"${100}"}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode","id":"s1-s1-s11","keywords":[],"metadata":[],"name":"Unicode","numberOfTests":8,"relativeSource":"../../../atest/testdata/misc/unicode.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/unicode.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode In Log Messages","id":"s1-s1-s11-t1","keywords":[{"arguments":"","name":"Print Unicode Strings","type":"KEYWORD"},{"arguments":"Fran\u00e7ais","name":"Log","type":"KEYWORD"}],"name":"Unicode In Log Messages","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode Return Value","id":"s1-s1-s11-t2","keywords":[{"arguments":"u'Fran\\\\xe7ais'","name":"${msg} = Evaluate","type":"KEYWORD"},{"arguments":"${msg}, Fran\u00e7ais","name":"Should Be Equal","type":"KEYWORD"},{"arguments":"${msg}","name":"Log","type":"KEYWORD"}],"name":"Unicode Return Value","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode In Return Value Attributes","id":"s1-s1-s11-t3","keywords":[{"arguments":"","name":"${obj} = Print And Return Unicode Object","type":"KEYWORD"},{"arguments":"${obj.message}","name":"Log","type":"KEYWORD"}],"name":"Unicode In Return Value Attributes","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode Failure","id":"s1-s1-s11-t4","keywords":[{"arguments":"","name":"Raise Unicode Error","type":"KEYWORD"}],"name":"Unicode Failure","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode Failure In Setup","id":"s1-s1-s11-t5","keywords":[{"arguments":"","name":"Raise Unicode Error","type":"SETUP"},{"arguments":"","name":"No Operation","type":"KEYWORD"}],"name":"Unicode Failure In Setup","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode Failure In Teardown","id":"s1-s1-s11-t6","keywords":[{"arguments":"","name":"No Operation","type":"KEYWORD"},{"arguments":"","name":"Raise Unicode Error","type":"TEARDOWN"}],"name":"Unicode Failure In Teardown","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.Unicode Failure In Teardown After Normal Failure","id":"s1-s1-s11-t7","keywords":[{"arguments":"Just ASCII here","name":"Fail","type":"KEYWORD"},{"arguments":"","name":"Raise Unicode Error","type":"TEARDOWN"}],"name":"Unicode Failure In Teardown After Normal Failure","tags":[],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Unicode.\u00dcn\u00efc\u00f6d\u00eb T\u00ebst \u00e4nd K\u00ebyw\u00f6rd N\u00e4m\u00ebs","id":"s1-s1-s11-t8","keywords":[{"arguments":"","name":"\u00dcn\u00efc\u00f6d\u00eb K\u00ebyw\u00f6rd N\u00e4m\u00eb","type":"KEYWORD"}],"name":"\u00dcn\u00efc\u00f6d\u00eb T\u00ebst \u00e4nd K\u00ebyw\u00f6rd N\u00e4m\u00ebs","tags":[],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Warnings And Errors","id":"s1-s1-s12","keywords":[{"arguments":"suite setup","name":"Warning in","type":"SETUP"},{"arguments":"suite teardown","name":"Warning in","type":"TEARDOWN"}],"metadata":[],"name":"Warnings And Errors","numberOfTests":2,"relativeSource":"../../../atest/testdata/misc/warnings_and_errors.txt","source":"/home/<USER>/Devel/robotframework/atest/testdata/misc/warnings_and_errors.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Misc.Warnings And Errors.Warning in test case","id":"s1-s1-s12-t1","keywords":[{"arguments":"test case","name":"Warning in","type":"KEYWORD"}],"name":"Warning in test case","tags":[],"timeout":""},{"doc":"<p>Duplicate name causes warning</p>","fullName":"Misc &amp; Dir.Suite.Misc.Warnings And Errors.Warning in test case","id":"s1-s1-s12-t1","keywords":[{"arguments":"No warnings here","name":"Log","type":"KEYWORD"}],"name":"Warning in test case","tags":[],"timeout":""}]}],"tests":[]},{"doc":"<p>Documentation for the whole test suite. All this forms a single paragraph starting from RF 2.7.2. Supported formatting is demonstrated below.</p>\n<ul>\n<li><b>URL:</b> <a href=\"http://robotframework.org\">http://robotframework.org</a></li>\n<li><i>Image:</i> <img src=\"http://code.google.com/p/robotframework/logo?ext.png\" title=\"http://code.google.com/p/robotframework/logo?ext.png\"></li>\n<li><i><b>Link:</b></i> <a href=\"http://robotframework.org\">Robot Framework</a></li>\n<li>Image link: <a href=\"http://robotframework.org\"><img src=\"http://code.google.com/p/robotframework/logo?ext.png\" title=\"http://robotframework.org\"></a></li>\n</ul>\n<hr>\n<table border=\"1\">\n<tr>\n<td><b>My</b></td>\n<td><b>Table</b></td>\n</tr>\n<tr>\n<td>1</td>\n<td>2</td>\n</tr>\n<tr>\n<td>foo</td>\n<td></td>\n</tr>\n</table>\n<p>regular line</p>\n<pre>\npre <b>formatted</b>\n  content\twith whitespaces\n</pre>\n<hr>\n<ul>\n<li>first list item</li>\n<li>second list item is continued  using <b>two</b> different approaches</li>\n</ul>","fullName":"Misc &amp; Dir.Suite.Dir.Suite","id":"s1-s2","keywords":[{"arguments":"higher level suite setup","name":"Log","type":"SETUP"}],"metadata":[["&lt;/script&gt;","<p>&lt; &amp;lt; &lt;/script&gt;</p>"],["Formatting","<p><b>Bold</b> and <i>italics</i></p>"],["Image","<p><img src=\"http://code.google.com/p/robotframework/logo?ext.png\" title=\"http://code.google.com/p/robotframework/logo?ext.png\"></p>"],["URL","<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>"]],"name":"Dir.Suite","numberOfTests":12,"relativeSource":"testdata/dir.suite","source":"/home/<USER>/Devel/robotframework/src/robot/htmldata/testdata/dir.suite","suites":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Test.Suite.1","id":"s1-s2-s1","keywords":[],"metadata":[],"name":"Test.Suite.1","numberOfTests":1,"relativeSource":"testdata/dir.suite/test.suite.1.txt","source":"/home/<USER>/Devel/robotframework/src/robot/htmldata/testdata/dir.suite/test.suite.1.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Test.Suite.1.list test","id":"s1-s2-s1-t1","keywords":[{"arguments":"foo, bar, quux","name":"${list} = Create List","type":"KEYWORD"},{"arguments":"${list}","name":"Log","type":"KEYWORD"}],"name":"list test","tags":["collections","i1","i2"],"timeout":""}]},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Test.Suite.2","id":"s1-s2-s2","keywords":[],"metadata":[],"name":"Test.Suite.2","numberOfTests":1,"relativeSource":"testdata/dir.suite/test.suite.2.txt","source":"/home/<USER>/Devel/robotframework/src/robot/htmldata/testdata/dir.suite/test.suite.2.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Test.Suite.2.Dictionary test","id":"s1-s2-s2-t1","keywords":[{"arguments":"key, value","name":"${dict} = Create Dictionary","type":"KEYWORD"},{"arguments":"${dict}","name":"Log","type":"KEYWORD"}],"name":"Dictionary test","tags":["collections","i1","i2"],"timeout":""}]},{"doc":"<p>Some suite <i>docs</i> with links: <a href=\"http://robotframework.org\">http://robotframework.org</a></p>","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests","id":"s1-s2-s3","keywords":[{"arguments":"Suite setup","name":"Log","type":"SETUP"},{"arguments":"","name":"Fail","type":"TEARDOWN"}],"metadata":[["&lt; &amp;lt; \u00e4","<p>&lt; &amp;lt; \u00e4</p>"],["home *page*","<p><a href=\"http://robotframework.org\">http://robotframework.org</a></p>"]],"name":"Tests","numberOfTests":10,"relativeSource":"testdata/dir.suite/tests.txt","source":"/home/<USER>/Devel/robotframework/src/robot/htmldata/testdata/dir.suite/tests.txt","suites":[],"tests":[{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Simple","id":"s1-s2-s3-t1","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"do nothing","name":"Log","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Simple","tags":["&lt; &amp;lt; \u00e4","default with percent %","force","i1","i2","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Long","id":"s1-s2-s3-t2","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"0.5 seconds","name":"Sleep","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Long","tags":["&lt; &amp;lt; \u00e4","force","i1","i2","long1","long2","long3","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Longer","id":"s1-s2-s3-t3","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"0.7 second","name":"Sleep","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Longer","tags":["&lt; &amp;lt; \u00e4","force","i1","i2","long2","long3","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Longest","id":"s1-s2-s3-t4","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"2 seconds","name":"Sleep","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Longest","tags":["*kek*kone*","&lt; &amp;lt; \u00e4","force","i1","i2","long3","with space"],"timeout":""},{"doc":"<p>This test uses <i><b>formatted</b></i> HTML.</p>\n<table border=\"1\">\n<tr>\n<td>Isn't</td>\n<td>that</td>\n<td><i>cool?</i></td>\n</tr>\n</table>","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Log HTML","id":"s1-s2-s3-t5","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"&lt;blink&gt;&lt;b&gt;&lt;font face=\"comic sans ms\" size=\"42\" color=\"red\"&gt;CAN HAZ HMTL &amp; NO CSS?!?!??!!?&lt;/font&gt;&lt;/b&gt;&lt;/blink&gt;, HTML","name":"Log","type":"KEYWORD"},{"arguments":"&lt;table&gt;&lt;tr&gt;&lt;td&gt;This table&lt;td&gt;should have&lt;tr&gt;&lt;td&gt;no special&lt;td&gt;formatting&lt;/table&gt;, HTML","name":"Log","type":"KEYWORD"},{"arguments":"escape &lt; &amp;lt; &lt;b&gt;no bold&lt;/b&gt;","name":"Log","type":"KEYWORD"},{"arguments":"escape &lt; &amp;lt; &lt;b&gt;no bold&lt;/b&gt;","name":"Fail","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Log HTML","tags":["!\"#%&amp;/()=","&lt; &amp;lt; \u00e4","force","i1","i2","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Unicode","id":"s1-s2-s3-t6","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"hyv\u00e4\u00e4 joulua","name":"Log","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Unicode","tags":["&lt; &amp;lt; \u00e4","force","i1","i2","with space","with unicode \u5b98\u8bdd"],"timeout":""},{"doc":"<p>Test doc</p>","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Complex","id":"s1-s2-s3-t7","keywords":[{"arguments":"in own setup","name":"Log","type":"SETUP"},{"arguments":"in test","name":"Log","type":"KEYWORD"},{"arguments":"","name":"User Kw","type":"KEYWORD"},{"arguments":"","name":"${i} IN [ @{list} ]","type":"FOR"},{"arguments":"in own teardown","name":"Log","type":"TEARDOWN"}],"name":"Complex","tags":["&lt; &amp;lt; \u00e4","force","i1","i2","owner-kekkonen","t1","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Log levels","id":"s1-s2-s3-t8","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"This is a WARNING!\\n\\nWith multiple lines., WARN","name":"Log","type":"KEYWORD"},{"arguments":"This is info, INFO","name":"Log","type":"KEYWORD"},{"arguments":"This is debug, DEBUG","name":"Log","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Log levels","tags":["&lt; &amp;lt; \u00e4","default with percent %","force","i1","i2","with space"],"timeout":""},{"doc":"","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Multi-line failure","id":"s1-s2-s3-t9","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"First failure","name":"Fail","type":"KEYWORD"},{"arguments":"Second failure\\nhas multiple\\nlines","name":"Fail","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Multi-line failure","tags":["&lt; &amp;lt; \u00e4","default with percent %","force","i1","i2","with space"],"timeout":""},{"doc":"<p>&lt;/script&gt;</p>","fullName":"Misc &amp; Dir.Suite.Dir.Suite.Tests.Escape JS &lt;/script&gt; \" <a href=\"http://url.com\">http://url.com</a>","id":"s1-s2-s3-t10","keywords":[{"arguments":"Test Setup","name":"Log","type":"SETUP"},{"arguments":"&lt;/script&gt;","name":"Log","type":"KEYWORD"},{"arguments":"","name":"kw <a href=\"http://url.com\">http://url.com</a>","type":"KEYWORD"},{"arguments":"","name":"&lt;/script&gt;","type":"KEYWORD"},{"arguments":"Test Teardown","name":"Log","type":"TEARDOWN"}],"name":"Escape JS &lt;/script&gt; \" <a href=\"http://url.com\">http://url.com</a>","tags":["&lt; &amp;lt; \u00e4","&lt;/script&gt;","force","i1","i2","with space"],"timeout":""}]}],"tests":[]}],"tests":[]},"title":"Misc & Dir.Suite"};
