<?xml version="1.0" encoding="UTF-8"?>
<keywordspec name="Example" type="LIBRARY" format="ROBOT" scope="GLOBAL" generated="2020-10-22T19:10:45Z" specversion="3" source="Example.py" lineno="8">
<version>42</version>
<doc>Library for `libdoc.py` testing purposes.

This library is only used in an example and it doesn't do anything useful.</doc>
<tags>
<tag>tag1</tag>
<tag>tag2</tag>
</tags>
<inits>
<init lineno="12">
<arguments repr="">
</arguments>
<doc>Creates new Example test library 1</doc>
<shortdoc>Creates new Example test library 1</shortdoc>
</init>
<init>
<arguments repr="arg">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg">
<name>arg</name>
</arg>
</arguments>
<doc>Creates new Example test library 2</doc>
<shortdoc>Creates new Example test library 2</shortdoc>
</init>
<init>
<arguments repr="i">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="i">
<name>i</name>
</arg>
</arguments>
<doc>Creates new Example test library 3</doc>
<shortdoc>Creates new Example test library 3</shortdoc>
</init>
</inits>
<keywords>
<kw name="Keyword" deprecated="false" private="false">
<arguments repr="arg">
<arg kind="POSITIONAL_OR_NAMED" required="true" repr="arg">
<name>arg</name>
</arg>
</arguments>
<doc>Takes one `arg` and *does nothing* with it.

Example:
| Your Keyword | xxx |
| Your Keyword | yyy |

See `My Keyword` for no more information.</doc>
<shortdoc>Takes one `arg` and *does nothing* with it.</shortdoc>
<tags>
<tag>tag1</tag>
<tag>tag2</tag>
</tags>
</kw>
<kw name="My Keyword" lineno="42" deprecated="true" private="true">
<arguments repr="">
</arguments>
<doc>Does nothing &amp; &lt;doc&gt; has "stuff" to 'escape'!! and ignored indentation
Tags: in spec these wont become tags</doc>
<shortdoc>Does nothing &amp; &lt;doc&gt; has "stuff" to 'escape'!! and ignored indentation Tags: in spec these wont become tags</shortdoc>
</kw>
<kw name="Non Ascii Doc" source="Different!" lineno="666">
<arguments repr="">
</arguments>
<doc>Hyvää yötä.

Спасибо!</doc>
<shortdoc>Hyvää yötä.</shortdoc>
</kw>
</keywords>
</keywordspec>
