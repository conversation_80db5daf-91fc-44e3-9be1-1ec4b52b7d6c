import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import utils from '@/utils/utils'

export const useUserStore = defineStore('user', () => {
  // 用户信息状态
  const userInfo = ref<any>(null)
  // 全局配置
  const configs = ref<any>(null)
  const aiConfigs = ref<any>(null)

  // 方法
  const setUserInfo = (info: any) => {
    userInfo.value = info
  }
  // 方法
  const setConfigs = (info: any) => {
    configs.value = info
    debugger;
  }
  // 方法
  const setAiConfigs = (info: any) => {
    aiConfigs.value = info
    debugger;
  }

  const clearUser = () => {
    console.log('setUserInfo 被调用，新值：') // 记录调用来源
    userInfo.value = null
  }
  const clearConfigs = () => {
    configs.value = null
  }
  const clearAiConfigs = () => {
    aiConfigs.value = null
  }

  return {
    userInfo,
    setUserInfo,
    clearUser,

    configs,
    setConfigs,
    clearConfigs,

    aiConfigs,
    setAiConfigs,
    clearAiConfigs
  }
})
