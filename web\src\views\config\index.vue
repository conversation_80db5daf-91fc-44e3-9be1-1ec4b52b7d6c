<template>
    <div class="ai-assistant-page">
        <div class="ai-assistant-page-container">
            <div class="header-area">
                <div class="ai-assistant-page-header">
                    <div class="title">全局配置</div>
                    <div class="sub-title" v-if="currentUser?.tenantName">{{ currentUser.tenantName }}</div>
                </div>
                <div class="ai-assistant-page-toolbar">
                    <div class="toolbar-item" :class="{'active': activeMenu === tab.value}"
                         v-for="(tab, index) in navList" :key="index" @click="onMenuSelect(tab.value)">
                        <el-icon :size="16">
                            <component :is="tab.icon"></component>
                        </el-icon>
                        <div>{{ tab.title }}</div>
                    </div>
                </div>
            </div>
            <div class="comp-box" v-loading="loading">
                <el-button class="save" type="primary" style="padding-left: 24px; padding-right: 24px;" @click="onSubmit">保存</el-button>
                <div class="content-area">
                    <el-scrollbar style="height: 100%;" wrap-style="overflow-x:hidden; padding: 12px; box-sizing: border-box;" ref="scrollbar">
                        <el-form :model="model">
                            <!-- 地址配置 -->
                            <el-row v-show="activeMenu === '12'" :gutter="10">
                                <el-col :span="24">
                                    <el-divider content-position="left">
                                        <span class="desc">注：自定义 一诺AI服务地址</span>
                                    </el-divider>
                                    <el-form-item label="一诺AI服务地址" field="WimAI_Addr" :label-width="120">
                                        <el-input v-model="model.WimAI_Addr" type="text" placeholder="示例：http://ip:port" autocomplete="off" auto-complete="off" clearable/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import utils from '@/utils/utils';
import saasApi from '@/api/index';
import { useUserStore } from "@/stores/user";
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

export default defineComponent({
    name: "config",
    data() {
        return {
            loading: false,
            activeMenu: '12',
            navList: [
                {title: '引用地址配置', icon: 'Link', value: '12'}
            ],
            configList: [],
            model: {
                WimAI_Addr: '', // wimAI地址
            },
        }
    },
    computed: {
        // 用户信息
        currentUser() {
            return userStore.userInfo;
        },
        headers(){
            return {
                Authorization: utils.GetAuthorization()
            }
        }
    },
    mounted() {
        this.AITaskConfigList()
    },
    methods: {
        // 查询配置列表
        AITaskConfigList() {
            saasApi.AITaskConfigList().then(res => {
                if (res && res.length) {
                    this.configList = res
                    console.log('this.configList', this.configList)
                }
            }).finally(() => {
                this.loading = false
                // 回显
                this.configList.forEach(f => {
                    if (f.code) this.model[f.code] = f.value
                })
            })
        },
        // 更新配置列表
        AITaskUpdateConfig() {
            this.loading = true
            saasApi.AITaskUpdateConfig(this.configList).then(res => {
                if (res?.Success === false) {
                    ElMessage({
                        message: '保存失败',
                        type: 'error',
                        showClose: true
                    })
                } else {
                    this.configList = res
                    ElMessage({
                        message: '保存成功',
                        type: 'success',
                        showClose: true
                    })
                    console.log('更新后this.configList', this.configList)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        // 菜单切换
        onMenuSelect(index){
            this.activeMenu = index
        },
        onSubmit() {
            const model = {...this.model}
            // 处理model数据
            Object.keys(model).forEach(f => {
                let item = this.configList.find(k => k.code === f)
                if (item) {
                    item.value = model[f]
                } else {
                    this.configList.push({
                        name: f,
                        code: f,
                        value: model[f]
                    })
                }
            })
            // 更新配置列表
            this.AITaskUpdateConfig()
        },
    }
})
</script>

<style scoped lang="scss">
// 按钮处理
::v-deep(.el-button){
    border-radius: 4px;
    height: 32px;
    padding: 0 16px;
    &.el-button--mini{
        border-radius: 4px;
        height: 32px;
        padding: 0 16px;
    }
    &.el-button--primary{
        color: #FFFFFF;
        background-color: #6128FF;
        border-color: #6128FF;
    }
}
// switch 处理
::v-deep(.el-switch){
    &.is-checked .el-switch__core{
        background-color: #6128FF;
        border-color: #6128FF;
    }
}
::v-deep(.el-input--mini .el-input__inner) {
    height: 32px !important;
    line-height: 32px !important;
}

.ai-assistant-page{
    //user-select: none;
    width: 100%;
    height: 100%;
    background: #FFFFFf;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 111;
    .ai-assistant-page-header{
        width: 100%;
        height: 50px;
        padding: 9px 12px;
        display: flex;
        flex-flow: column nowrap;
        align-items: flex-start;
        justify-content: center;
        box-sizing: border-box;
        .logo{
            height: 28px;
            img{
                height: inherit;
                object-fit: cover;
            }
        }
        .title{
            flex: 1;
            padding: 0 12px;
            font-weight: bold;
            color: #222222;
            font-size: 16px;
        }
        .sub-title{
            font-size: 12px;
            flex: 1;
            padding: 0 6px;
            margin-top: 4px;
        }
        .toolbar{
            display: flex;
            align-items: center;
            color: #666666;
            &.wimpic{
                .el-icon-close{
                    margin-right: 7px;
                }
            }
            > div{
                cursor: pointer;
                text-align: center;
                font-size: 16px;
                & ~ div{
                    margin-left: 6px;
                }
            }
        }
    }
    .ai-assistant-page-container{
        display: flex;
        height: 100%;
        background: #F6F6F8;
        .ai-assistantSkill-list {
            display: flex;
            .ai-assistantSkill-list-item {
                margin-right: 12px;
                // ::v-deep  .el-checkbox__label {
                //     padding-left: 4px;
                // }
            }
        }
    }
    .header-area{
        width: 240px;
        background: #F6F6F8;
        color: #686A82;
        position: relative;
        transition: all .3s;
        .ai-assistant-page-header{
            padding: 20px 16px;
            min-height: 72px;
            height: auto;
            .logo{
                width: 32px;
                height: 32px;
            }
            .title{
                padding: 0 6px;
                white-space: nowrap;
            }
            .title, .toolbar{
                color: #222222;
            }
            .toolbar{
                div[class^='el-icon']{
                    color: #87899D;
                    width: 20px;
                    height: 20px;
                    background: #EEEEF2;
                    border-radius: 4px;
                    font-size: 13px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    & ~ div{
                        margin-left: 10px;
                    }
                }
            }
        }
        .ai-assistant-page-toolbar{
            display: flex;
            flex-direction: column;
            //justify-content: center;
            align-items: center;
            margin-top: 4px;
            .toolbar-item{
                width: calc(100% - 32px);
                height: 40px;
                border-radius: 8px;
                color: #686A82;
                font-size: 16px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                cursor: pointer;
                transition: background-color .3s;
                margin: 8px 0;
                padding: 0 10px;
                box-sizing: border-box;
                &~.toolbar-item{
                    margin-top: 0;
                }
                &.divider {
                    margin-top: 32px;
                    position: relative;
                    &:before{
                        content: '';
                        position: absolute;
                        top: -20px;
                        left: -1px;
                        width: 100%;
                        height: 1px;
                        background: #e9e9e9;
                    }
                }
                &.disabled{
                    opacity: 0.3;
                }
                &.active:not(.disabled),&:hover:not(.disabled){
                    background: #FFFFFF;
                    color: #6128FF;
                    transition: background-color .3s;
                }
                .icon{
                    font-size: 20px;
                    width: 24px;
                    text-align: center;
                }
                > div{
                    line-height: 1;
                    margin-left: 13px;
                    white-space: nowrap;
                }
            }
        }
    }
}
.comp-box {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
    .content-area {
        flex: 1;
        background: #ffffff;
        margin: 12px 12px 12px 0;
        border-radius: 20px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        overflow: hidden;
        padding-top: 60px;
    }
    .save{
        position: absolute;
        right: 32px;
        top: 32px;
        z-index: 1;
    }
}

.el-divider {
    background-color: #efefef;
}

.el-divider__text.is-left {
    left: 0;
    color: #333;
    font-weight: bold;
    padding-left: 0;
}
.flex-row{
    display: flex;
    flex-direction: row;
    align-items: center;
    &~.flex-row{
        margin-top: 4px;
    }
    > span{
        width: 80px;
        text-align: right;
    }
}
.desc {
    font-size: 12px;
    color: #666666;
    margin-left: 12px;
    font-weight: normal;
}
</style>
