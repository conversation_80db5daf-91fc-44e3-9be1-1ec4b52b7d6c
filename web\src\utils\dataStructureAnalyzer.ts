/**
 * 数据结构分析器
 * 用于分析API响应、数据库查询结果等数据源的字段结构
 */

export interface FieldInfo {
  path: string
  type: string
  label: string
  sample?: any
  children?: FieldInfo[]
  isArray?: boolean
  arrayItemType?: string
}

export interface DataSource {
  nodeId: string
  nodeType: string
  label: string
  outputVariable: string
  dataStructure?: any
  sampleData?: any
}

export class DataStructureAnalyzer {
  /**
   * 分析数据结构
   */
  analyzeStructure(data: any, path = '', label = ''): FieldInfo[] {
    if (!data || typeof data !== 'object') {
      return []
    }

    const fields: FieldInfo[] = []

    if (Array.isArray(data)) {
      // 处理数组
      if (data.length > 0) {
        const itemStructure = this.analyzeStructure(data[0], `${path}[]`, `${label}[项目]`)
        fields.push({
          path: `${path}[]`,
          type: 'array',
          label: `${label} (数组)`,
          isArray: true,
          arrayItemType: this.getFieldType(data[0]),
          children: itemStructure,
          sample: `${data.length}项`
        })
      }
    } else {
      // 处理对象
      for (const [key, value] of Object.entries(data)) {
        const fieldPath = path ? `${path}.${key}` : key
        const fieldLabel = label ? `${label}.${key}` : key
        const fieldType = this.getFieldType(value)

        const fieldInfo: FieldInfo = {
          path: fieldPath,
          type: fieldType,
          label: key,
          sample: this.getSampleValue(value)
        }

        if (fieldType === 'object') {
          fieldInfo.children = this.analyzeStructure(value, fieldPath, fieldLabel)
        } else if (fieldType === 'array' && Array.isArray(value) && value.length > 0) {
          fieldInfo.isArray = true
          fieldInfo.arrayItemType = this.getFieldType(value[0])
          fieldInfo.children = this.analyzeStructure(value[0], `${fieldPath}[]`, `${fieldLabel}[项目]`)
          fieldInfo.sample = `${value.length}项`
        }

        fields.push(fieldInfo)
      }
    }

    return fields
  }

  /**
   * 获取字段类型
   */
  getFieldType(value: any): string {
    if (value === null || value === undefined) {
      return 'null'
    }

    if (Array.isArray(value)) {
      return 'array'
    }

    const type = typeof value

    if (type === 'object') {
      return 'object'
    }

    if (type === 'string') {
      // 尝试识别特殊字符串类型
      if (this.isDateString(value)) {
        return 'date'
      }
      if (this.isEmailString(value)) {
        return 'email'
      }
      if (this.isUrlString(value)) {
        return 'url'
      }
      return 'string'
    }

    if (type === 'number') {
      return Number.isInteger(value) ? 'integer' : 'number'
    }

    return type
  }

  /**
   * 获取示例值
   */
  getSampleValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null'
    }

    if (Array.isArray(value)) {
      return `[${value.length}项]`
    }

    if (typeof value === 'object') {
      const keys = Object.keys(value)
      return `{${keys.length}个字段}`
    }

    if (typeof value === 'string') {
      return value.length > 50 ? `${value.substring(0, 50)}...` : value
    }

    return String(value)
  }

  /**
   * 判断是否为日期字符串
   */
  private isDateString(str: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2})?/
    return dateRegex.test(str) && !isNaN(Date.parse(str))
  }

  /**
   * 判断是否为邮箱字符串
   */
  private isEmailString(str: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(str)
  }

  /**
   * 判断是否为URL字符串
   */
  private isUrlString(str: string): boolean {
    try {
      new URL(str)
      return true
    } catch {
      return false
    }
  }

  /**
   * 根据字段路径获取值
   */
  getValueByPath(data: any, path: string): any {
    if (!data || !path) {
      return undefined
    }

    // 处理变量表达式 ${variable.path}
    const cleanPath = path.replace(/^\$\{|\}$/g, '')

    const parts = cleanPath.split('.')
    let current = data

    for (const part of parts) {
      if (part.includes('[]')) {
        // 处理数组路径
        const arrayKey = part.replace('[]', '')
        if (arrayKey) {
          current = current[arrayKey]
        }
        // 如果是数组，返回整个数组用于后续处理
        if (Array.isArray(current)) {
          return current
        }
      } else {
        if (current && typeof current === 'object' && part in current) {
          current = current[part]
        } else {
          return undefined
        }
      }
    }

    return current
  }

  /**
   * 展平嵌套字段结构为平面列表
   */
  flattenFields(fields: FieldInfo[], prefix = ''): FieldInfo[] {
    const result: FieldInfo[] = []

    for (const field of fields) {
      const fullPath = prefix ? `${prefix}.${field.path}` : field.path

      result.push({
        ...field,
        path: fullPath
      })

      if (field.children && field.children.length > 0) {
        result.push(...this.flattenFields(field.children, fullPath))
      }
    }

    return result
  }

  /**
   * 从工作流节点中提取数据源
   */
  extractDataSources(workflowNodes: any[]): DataSource[] {
    const dataSources: DataSource[] = []

    for (const node of workflowNodes) {
      const nodeData = node.data
      if (!nodeData) continue

      // HTTP请求节点
      if (nodeData.componentType === 'http_post' || nodeData.componentType === 'http_get' || nodeData.componentType === 'http_request') {
        dataSources.push({
          nodeId: node.id,
          nodeType: nodeData.componentType,
          label: `${nodeData.label || 'HTTP请求'} (${node.id})`,
          outputVariable: nodeData.config?.response_content_variable || 'response'
        })
      }

      // 数据库查询节点
      if (nodeData.componentType === 'db_query') {
        dataSources.push({
          nodeId: node.id,
          nodeType: nodeData.componentType,
          label: `${nodeData.label || '数据库查询'} (${node.id})`,
          outputVariable: nodeData.config?.response_content_variable || 'query_result'
        })
      }

      // Excel读取节点
      if (nodeData.componentType === 'excel_read') {
        dataSources.push({
          nodeId: node.id,
          nodeType: nodeData.componentType,
          label: `${nodeData.label || 'Excel读取'} (${node.id})`,
          outputVariable: nodeData.outputs?.[0] || 'data'
        })
      }

      // CSV读取节点
      if (nodeData.componentType === 'csv_read') {
        dataSources.push({
          nodeId: node.id,
          nodeType: nodeData.componentType,
          label: `${nodeData.label || 'CSV读取'} (${node.id})`,
          outputVariable: nodeData.outputs?.[0] || 'csv_data'
        })
      }
    }

    return dataSources
  }

  /**
   * 生成字段绑定表达式
   */
  generateBindingExpression(dataSource: DataSource, fieldPath: string): string {
    return `\${${dataSource.outputVariable}.${fieldPath}}`
  }

  /**
   * 获取字段的默认格式
   */
  getDefaultFormat(fieldType: string): string {
    switch (fieldType) {
      case 'date':
        return 'YYYY-MM-DD'
      case 'number':
        return '0.00'
      case 'integer':
        return '0'
      case 'email':
        return ''
      case 'url':
        return ''
      default:
        return ''
    }
  }
}

// 导出单例实例
export const dataStructureAnalyzer = new DataStructureAnalyzer()
