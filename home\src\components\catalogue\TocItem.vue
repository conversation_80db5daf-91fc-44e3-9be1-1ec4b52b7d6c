<template>
  <li 
    class="toc-item"
    :class="['toc-item', `toc-level-${level}`,`toc-level-index-${item.depth}`
    ]"
    @click.stop="clickItem(item)"
  >
    
    <!-- 文本容器：专门用于处理hover效果 -->
    <div class="toc-text-container" :class="[ 
    {'active': item.active },`toc-text-container-${item.depth}`]">
      <span class="circle" :class="[item.depth!==1?'circle-transparent':'']"></span>
      <span class="toc-text">{{ item.content }}</span>
    </div>
    
    <!-- 递归渲染子目录 -->
    <ul v-if="item.children && item.children.length" class="toc-sub-list">
      <TocItem 
        v-for="(child, index) in item.children" 
        :key="index" 
        :index="item.depth"
        :item="child" 
        :level="child.level"
        @scroll-to="emit('scroll-to', $event,child.nodeId)"
      />
    </ul>
  </li>
</template>

<script setup lang="ts">
import { cursorTo } from 'readline';
import { defineProps, defineEmits, ref, computed,watch } from 'vue';
import type { HeadingItem } from './index.vue';
import TocItem from './TocItem.vue';

// 生成唯一ID用于样式隔离
const itemId = `toc-item-${Math.random().toString(36).substring(2, 9)}`;
const currentItem = ref(null)
// 定义事件
const emit = defineEmits<{
  (e: 'scroll-to', element: HTMLElement,nodeId:string): void;
}>();

// 当前项的hover状态
const isHovered = ref(false);

// 计算属性：生成当前项的CSS选择器
const itemSelector = computed(() => `#${itemId}`);

// 点击事件处理
const handleClick = () => {
  if (item.element) {
    emit('scroll-to', item.element);
  }
};

// 接收的props
const props = defineProps<{
  item: HeadingItem;
  level: number;
  index: number;
}>();

const { item } = props;

// 动态添加/移除hover样式
watch(isHovered, (newVal) => {
  const element = document.querySelector(itemSelector.value);
  ;
  if (element) {
    if (newVal) {
      element.classList.add('toc-item--hover');
    } else {
      element.classList.remove('toc-item--hover');
    }
  }
});

const clickItem = (item: HeadingItem) => {
  debugger;
  if (item.element) {
    currentItem.value = item;
    emit('scroll-to', item.element,item.nodeId);
  }
}
</script>

<style scoped lang="scss">
.toc-item {
  // margin: 0.25rem 0;
  padding: 0;
  cursor: pointer;
  /* 关键：父元素本身不响应hover，交给子容器处理 */
  pointer-events: none;
  // border-left:1px solid#D8D8D8;
  // margin-left:2px;
  &.toc-level-index-1{
    border-left:none;
    margin-left:0;
  }
  &.toc-level-index-2{
    border-left:1px solid#D8D8D8;
  }
}
.circle{
  display:block;
  width:5px;
  height:5px;
  border-radius: 50%;
  background:#D8D8D8;
  margin-right:8px;
  flex-basis: 5px;;
  &.circle-transparent{
    background:transparent!important;
  }
}

/* 文本容器：专门处理hover的元素 */
.toc-text-container {
  line-height:32px;
  display: flex;
  align-items: center;
  pointer-events: auto; /* 重新开启hover响应 */
}

.toc-text {
  flex:1;
  display: inline-block;
  color: #222222;
  line-height: 32px;
  transition: all 0.2s ease;
}

/* 不同级别标题的样式和缩进 */
.toc-level-1 .toc-text {
  font-weight: 500;
  padding-left: 0;
  font-size: 14px;
}

.toc-level-2 .toc-text {
  font-weight: 500;
  padding-left: 8px;
  color:#5C5F66;
  font-size: 14px;
}

.toc-level-3 .toc-text {
  padding-left: 8px;
  font-size: 14px;
}

.toc-level-4 .toc-text {
  padding-left: 8px;
  font-size: 14px;
  color: #6b7280;
}

.toc-level-5 .toc-text {
  padding-left: 8px;
  font-size: 14px;
  color: #6b7280;
}

.toc-level-6 .toc-text {
  padding-left: 8px;
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

/* 激活状态样式 */
.toc-text-container.active > .toc-text {
  color: #2563eb;
  // font-weight: 600;
}
.toc-text-container.active > .circle{
  background-color: #2563eb;
}
.toc-text-container.active:before{
  content: "";
  background: #2563eb;
  height: 32px;
  width: 2px;
  position: absolute;
  left: 2px;
}
.toc-text-container-1.active:before{
  display: none;
}


/* 核心：只对当前项的文本容器hover生效 */
.toc-text-container:hover > .toc-text {
  color: #3b82f6;
}

.toc-sub-list {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-left:2.5px;
  .toc-item{
    // border:none
  }
  
}
// .toc-level-1 > .toc-sub-list{
//   border-left:1px solid#D8D8D8;
// }

</style>
