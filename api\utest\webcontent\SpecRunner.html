<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
  "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
  <title>Jasmine Test Runner</title>
  <link rel="stylesheet" type="text/css" href="jasmine-1.0.2/jasmine.css">
  <script type="text/javascript" src="jasmine-1.0.2/jasmine.js"></script>
  <script type="text/javascript" src="jasmine-1.0.2/jasmine-html.js"></script>

  <!-- reporters from external jasmine-reporters dependency -->
  <script type="text/javascript" src="../../ext-lib/jasmine-reporters/src/jasmine.junit_reporter.js"></script>
  <script type="text/javascript" src="../../ext-lib/jasmine-reporters/src/jasmine.console_reporter.js"></script>

  <!-- include source files here... -->
  <script type="text/javascript" src="../../src/robot/htmldata/lib/jsxcompressor.min.js"></script>
  <script type="text/javascript" src="../../src/robot/htmldata/rebot/fileloading.js"></script>
  <script type="text/javascript" src="../../src/robot/htmldata/rebot/model.js"></script>
  <script type="text/javascript" src="../../src/robot/htmldata/rebot/testdata.js"></script>
  <script type="text/javascript" src="../../src/robot/htmldata/rebot/util.js"></script>
  <script type="text/javascript" src="../../src/robot/htmldata/rebot/log.js"></script>

  <!-- include test data for specs here -->
  <script type="text/javascript" src="spec/data/Suite.js"></script>
  <script type="text/javascript" src="spec/data/SetupsAndTeardowns.js"></script>
  <script type="text/javascript" src="spec/data/Messages.js"></script>
  <script type="text/javascript" src="spec/data/TeardownFailure.js"></script>
  <script type="text/javascript" src="spec/data/PassingFailing.js"></script>
  <script type="text/javascript" src="spec/data/TestsAndKeywords.js"></script>
  <script type="text/javascript" src="spec/data/allData.js"></script>
  <script type="text/javascript" src="spec/data/splitting.js"></script>

  <!-- include spec files here... -->
  <script type="text/javascript" src="spec/ParsingSpec.js"></script>
  <script type="text/javascript" src="spec/StatisticsSpec.js"></script>
  <script type="text/javascript" src="spec/ContainsTag.js"></script>
  <script type="text/javascript" src="spec/LogLevelSpec.js"></script>
  <script type="text/javascript" src="spec/UtilSpec.js"></script>

</head>
<body>

<script type="text/javascript">
  jasmine.getEnv().addReporter(new jasmine.TrivialReporter());
  if (jasmine.JUnitXmlReporter != undefined) {
    jasmine.getEnv().addReporter(new jasmine.ConsoleReporter());
    jasmine.getEnv().addReporter(new jasmine.JUnitXmlReporter('jasmine-results/'));
  }
  jasmine.getEnv().execute();
</script>

</body>
</html>
