*** Settings ***
Documentation    {{ workflow.metadata.name }}
...              {{ workflow.metadata.description }}
...              Generated by WimTask RPA Designer
...              Version: {{ workflow.metadata.version }}
...              Created: {{ workflow.metadata.createdAt }}
...              Updated: {{ workflow.metadata.updatedAt }}

# RPA Framework 核心库
{% if use_browser %}
Library          RPA.Browser.Playwright  # 现代Web自动化 (基于Playwright)
{% endif %}
{#Library          RPA.Desktop             # 桌面自动化#}
Library          RPA.Excel.Files         # Excel处理
Library          RPA.Email.ImapSmtp      # 邮件功能
Library          RPA.HTTP                # HTTP/API调用
Library          RPA.Tables              # 数据表处理
Library          RPA.Database            # 数据库操作
Library          RPA.FileSystem          # 文件系统操作

# Robot Framework 内置库
Library          Collections
Library          String
Library          DateTime
Library          OperatingSystem
Library          Process

# excel 相关库
Library    RPA.Excel.Files
Library    openpyxl

# 自定义库
Library          keywords.data_utils
Library          keywords.decode_exec
Library          keywords.sand_box_actuator
Library          keywords.ai_analyze_exec
Library          keywords.text_to_speech_stream
{#Library          keywords.text_to_speech#}
Library          keywords.notifier_send
Library          keywords.get_weather
Library          keywords.time_processing
Library          keywords.excel_operate_exec
Library          keywords.image_recognition
Library          keywords.word_operate_exec
Library          keywords.data_forecast
Library          keywords.data_wash
Library          keywords.water_shutoff_valve
Library          keywords.json_extract
Library          keywords.condition.ConditionTools

# HTTP请求库
Library          RequestsLibrary

# 自定义库
Library          listener.execution_monitor.CustomiseLibrary

*** Variables ***
# Workflow Variables
${WORKFLOW_NAME}        {{ workflow.metadata.name }}
${WORKFLOW_VERSION}     {{ workflow.metadata.version }}
${TASK_ID}              {{ task_id }}

# Default Timeouts
${DEFAULT_TIMEOUT}      30s
${DEFAULT_WAIT}         1s
# 中断的标识
${stop_flag}      False

# Variable Management
&{WORKFLOW_VARIABLES}   # Dictionary to store workflow variables
&{GLOBAL_VARIABLES}     # Dictionary to store global variables
&{LOCAL_VARIABLES}     # Dictionary to store LOCAL variables

{% if use_browser %}
${BROWSER_PATH}        {{ browser_url }}
Log    浏览器路径是${BROWSER_PATH}    INFO
{% endif %}



*** Test Cases ***
Execute Workflow
    [Documentation]    Execute the complete workflow
    [Tags]             workflow    automated

    # Initialize execution
    ${EXECUTION_START}=    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    {% for variable in workflow.variables %}
        {% if variable['type'] == 'currentTime'%}
    ${{ '{'+variable['name']+'_v}' }}=   Get Current Date    result_format={{ variable['value'] }}
    Set Suite Variable    ${{ '{'+variable['name']+'}' }}    ${{ '{'+variable['name']+'_v}' }}
        {% endif %}
    {% endfor %}

    Log    Starting workflow execution: ${WORKFLOW_NAME}    INFO
    Log    Execution started at: ${EXECUTION_START}    INFO

    # Execute workflow steps
    TRY
        Set Local Variable
        Execute Workflow Steps
        Log    Workflow completed successfully    INFO
    EXCEPT    AS    ${error}
        Log    Workflow failed with error: ${error}    ERROR
        Fail    Workflow execution failed: ${error}
    FINALLY
        # Cleanup
        Cleanup Resources
    END

*** Keywords ***
Retry Keyword
    [Arguments]    ${keyword_name}    ${retries}=1    ${interval}=2    ${error_handle}=stop

    ${last_error}=    Set Variable    None

    FOR    ${i}    IN RANGE    ${retries}
        Run Keyword If    '${stop_flag}' == 'True'    Fail    执行被外部中止
        ${status}    ${result}=    Run Keyword And Ignore Error    ${keyword_name}
        Run Keyword If    '${status}' == 'PASS'    Return From Keyword
        Log    [Retry ${i}] failed with: ${result}    WARN
        ${last_error}=    Set Variable    ${result}
        Sleep    ${interval}
    END

    Run Keyword If    '${error_handle}' == 'ignore'    Log    Ignored failure of keyword '${keyword_name}' after ${retries} retries. Last error: ${last_error}    WARN
    Run Keyword If    '${error_handle}' == 'ignore'    Return From Keyword

    Fail    Keyword '${keyword_name}' failed after ${retries} retries. Last error: ${last_error}


Execute Workflow Steps
    [Documentation]    Execute all workflow steps in order

    {%- macro rander_condition_node(node,sub_sequence_map,node_map,ncm,depth) -%}
        {% set space = '    '* depth%}
{{ space }}
        {% for condition in ncm[node.id].conditions %}
{{ space }}${{ '{cond_' }}{{loop.index}}{{ '}' }}=    {{ condition.condition_str }}
        {% endfor %}
        {% for condition in ncm[node.id].conditions %}
            {% if loop.first %}
{{ space }}IF    ${{ '{cond_' }}{{loop.index}}{{ '}' }}
{{ space }}Log    isi    DEBUG
            {% else %}
{{ space }}ELSE IF    ${{ '{cond_' }}{{loop.index}}{{ '}' }}
{{ space }}Log    isei    DEBUG
            {% endif %}
{{ space }}{{- rander_sequence(sub_sequence_map[condition.next_node_id],sub_sequence_map,node_map,ncm,depth + 1) -}}
            {% endfor %}
{{ space }}ELSE
{{ space }}Log    ise    DEBUG
{{ space }}{{- rander_sequence(sub_sequence_map[ncm[node.id].else_next_node_id],sub_sequence_map,node_map,ncm,depth + 1) -}}
{{ space }}
{{ space }}END
    {%- endmacro  -%}
    Sleep    ${DEFAULT_WAIT}
    {%- macro rander_node(node_id,sub_sequence_map,node_map,ncm,depth) -%}
        {% set space = '    '*(depth) %}
        {% with node=node_map[node_id] %}
            {%  if node.data.componentType == 'condition' %}
{{- rander_condition_node(node,sub_sequence_map,node_map,ncm,depth) -}}
            {% else %}
                {% set user_retry = node.data.config.retry_times | default(0) | int %}
                {% set retry_times = [user_retry + 1, 5] | min %}
                {% set retry_times = [retry_times, 1] | max %}

                {% set retry_delay = node.data.config.retry_delay | default(2) | int %}
                {% set retry_delay = [retry_delay, 30] | min %}
                {% set retry_delay = [retry_delay, 2] | max %}
                {% set error_handle = node.data.config.error_handle | default("stop") %}
{{ space }}Retry Keyword    Execute Step {{ node.id }} - {{ node.data.label.replace(' ', '_') }}    {{ retry_times }}    {{ retry_delay }}    {{ error_handle }}
            {% endif %}
        {% endwith %}
    {%- endmacro  -%}
    Sleep    ${DEFAULT_WAIT}
    {%- macro rander_sequence(sequence,sub_sequence_map,node_map,ncm,depth=1) -%}
        {% for node_id in sequence %}
            {{- rander_node(node_id,sub_sequence_map,node_map,ncm,depth) -}}
        {% endfor %}
    {%- endmacro  -%}
    Sleep    ${DEFAULT_WAIT}
{{- rander_sequence(main_sequence,sub_sequence_map,node_map,conditions_map) -}}


{% for node in nodes %}
{% set component = processed_components[node.id] or components[node.data.componentType] %}
Execute Step {{ node.id }} - {{ node.data.label.replace(' ', '_') }}
    [Documentation]    {{ node.data.description or component.description }}
    [Tags]             {{ component.type }}

    Log    Executing: {{ node.data.label }}    INFO
    ${NODE_ID}    Set Variable    {{ node.id }}
    # Set up option.variables from request


    # Set up variables for this step
    {% for key, value in node.data.config.items() %}
    {% if value is string %}
    {% if key == 'code' and node.data.componentType in ['python_execute', 'javascript_execute'] %}
    # Python code - use Robot Framework multiline syntax with proper variable handling
    ${{ '{' }}{{ key }}{{ '}' }}=    Catenate    SEPARATOR=\n
    {% for line in value.split('\n') %}
    ...    {{ line|replace('${re}', '"""${re}"""') }}
    {% endfor %}
    {% else %}
    ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t')|replace('#', '\\#') }}
    {% endif %}
    {% elif value is number %}
    ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value }}
    {% elif value is sameas true or value is sameas false %}
    ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|lower }}
    {% elif key == 'json_data' or key == 'headers' %}
    # Handle JSON data and headers as dictionaries
    ${{ '{' }}{{ key }}{{ '}' }}=    Evaluate    {{ value|tojson|replace('false', 'False')|replace('true', 'True')|replace('null', 'None') }}
    {% else %}
    ${{ '{' }}{{ key }}{{ '}' }}=    Set Variable    {{ value|string|replace('\\', '\\\\')|replace('\n', '\\n')|replace('\r', '\\r')|replace('\t', '\\t') }}
    {% endif %}
    {% endfor %}

    # Execute component logic
    TRY
{{ processed_components[node.id].robot_template | indent(8, True) }}
        {{  "${out_vals_list}" }}    Create Dictionary
        {% for out in node.data.outputs %}
            ...     "{{ out }}"=${{ "{" }}{{ out }}{{ "}" }}
        {% endfor %}
        record_output       ${TASK_ID}          Execute Step {{ node.id }}         {{  "${out_vals_list}" }}
        {{  "${input_vals_list}" }}    Create Dictionary
        {% for input in node.data.inputs %}
            ...     "{{ input }}"=${{ "{" }}{{ input }}{{ "}" }}
        {% endfor %}
        record_input         ${TASK_ID}         Execute Step {{ node.id }}         {{  "${input_vals_list}" }}

        Log    Step completed: {{ node.data.label }}    INFO
    EXCEPT    AS    ${error}
        Log    Step failed: {{ node.data.label }} - ${error}    ERROR
        # Handle error based on configuration
        Handle Step Error    {{ node.data.label }}    ${error}
    END
    Sleep    ${DEFAULT_WAIT}
{% endfor %}

Handle Step Error
    [Documentation]    Handle errors that occur during step execution
    [Arguments]        ${step_name}    ${error_message}

    Log    Error in step ${step_name}: ${error_message}    ERROR

    # You can customize error handling here
    # For now, we'll re-raise the error to stop execution
    Fail    Step ${step_name} failed: ${error_message}

Cleanup Resources
    [Documentation]    Clean up any resources used during workflow execution

    Log    Cleaning up resources...    INFO

    # Close any open browsers
    TRY
        Close Browser    ALL
    EXCEPT
        Log    No browsers to close    DEBUG
    END

    TRY
        Disconnect From Database
        Log    数据库连接已关闭    INFO
    EXCEPT
        Log    数据库未连接或关闭失败，已忽略    DEBUG
    END
    Sleep    ${DEFAULT_WAIT}
    # Add other cleanup tasks as needed
    Log    Cleanup completed    INFO

# Utility Keywords
Wait For Element
    [Documentation]    Wait for an element to be visible with custom timeout
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait Until Element Is Visible    ${locator}    timeout=${timeout}

Safe Click Element
    [Documentation]    Click element with error handling
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    Click Element    ${locator}
    Sleep    ${DEFAULT_WAIT}

Safe Input Text
    [Documentation]    Input text with error handling
    [Arguments]        ${locator}    ${text}    ${clear}=${True}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    Run Keyword If    ${clear}    Clear Element Text    ${locator}
    Input Text    ${locator}    ${text}
    Sleep    ${DEFAULT_WAIT}

Get Element Text Safe
    [Documentation]    Get element text with error handling
    [Arguments]        ${locator}    ${timeout}=${DEFAULT_TIMEOUT}

    Wait For Element    ${locator}    ${timeout}
    ${text}=    Get Text    ${locator}
    RETURN    ${text}

Log Workflow Info
    [Documentation]    Log workflow information
    [Arguments]        ${message}    ${level}=INFO

    ${timestamp}=    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    Log    [${timestamp}] ${message}    ${level}

# Variable Management Keywords
Set Workflow Variable
    [Documentation]    Set a variable in workflow scope
    [Arguments]        ${name}    ${value}    ${scope}=local

    IF    '${scope}' == 'global'
        Set To Dictionary    ${GLOBAL_VARIABLES}    ${name}=${value}
        Set Global Variable    ${${name}}    ${value}
    ELSE IF    '${scope}' == 'workflow'
        Set To Dictionary    ${WORKFLOW_VARIABLES}    ${name}=${value}
        Set Suite Variable    ${${name}}    ${value}
    ELSE
        Set Test Variable    ${${name}}    ${value}
    END
    Sleep    ${DEFAULT_WAIT}
    Log    Variable '${name}' set to: ${value} (scope: ${scope})    INFO

Get Workflow Variable
    [Documentation]    Get a variable value with fallback
    [Arguments]        ${name}    ${default}=

    ${value}=    Get Variable Value    ${${name}}    ${default}
    Sleep    ${DEFAULT_WAIT}
    RETURN    ${value}

List Workflow Variables
    [Documentation]    Log all workflow variables

    Log    === Workflow Variables ===    INFO
    FOR    ${name}    ${value}    IN    &{WORKFLOW_VARIABLES}
        Log    ${name} = ${value}    INFO
    END

    Log    === Global Variables ===    INFO
    FOR    ${name}    ${value}    IN    &{GLOBAL_VARIABLES}
        Log    ${name} = ${value}    INFO
    END
    Sleep    ${DEFAULT_WAIT}

Substitute Variables In Text
    [Documentation]    Replace variable references in text with actual values
    [Arguments]        ${text}

    ${result}=    Set Variable    ${text}

    # Get all variables and substitute them
    FOR    ${name}    ${value}    IN    &{WORKFLOW_VARIABLES}
        ${pattern}=    Set Variable    \${${name}}
        ${result}=    Replace String    ${result}    ${pattern}    ${value}
    END

    FOR    ${name}    ${value}    IN    &{GLOBAL_VARIABLES}
        ${pattern}=    Set Variable    \${${name}}
        ${result}=    Replace String    ${result}    ${pattern}    ${value}
    END
    Sleep    ${DEFAULT_WAIT}
    RETURN    ${result}

# Variable Management LOCAL
Set Local Variable

    {% for variable in workflow.variables %}
        {% if variable['type'] != 'currentTime'%}
    ${{ '{'+variable['name']+'_v}' }}=   Set Variable    {{ variable['value'] }}
    Set Suite Variable    ${{ '{'+variable['name']+'}' }}    ${{ '{'+variable['name']+'_v}' }}
    Set To Dictionary    ${LOCAL_VARIABLES}    ${{ '{'+variable['name']+'}' }}=${{ '{'+variable['name']+'_v}' }}
        {% endif %}
    {% endfor %}
    Sleep    ${DEFAULT_WAIT}