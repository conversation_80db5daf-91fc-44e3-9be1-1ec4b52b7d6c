{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "broken_count": 3, "host": "encrypted-tbn0.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "broken_count": 3, "host": "passwordsleakcheck-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 5, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "broken_until": "**********", "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "broken_until": "**********", "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "broken_until": "**********", "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "1753166740", "host": "www.googleadservices.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients1.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400231737026863", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400231739423536", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400216156071659", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400231740998986", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400231737468220", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397726130050772", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2RsbWVhc3VyZS5jb20AAAA=", false, 0], "network_stats": {"srtt": 216551}, "server": "https://api.mapbox.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2RsbWVhc3VyZS5jb20AAAA=", false, 0], "server": "https://a.basemaps.cartocdn.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2RsbWVhc3VyZS5jb20AAAA=", false, 0], "server": "https://b.basemaps.cartocdn.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2RsbWVhc3VyZS5jb20AAAA=", false, 0], "server": "https://d.basemaps.cartocdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400231811540537", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2RsbWVhc3VyZS5jb20AAAA=", false, 0], "server": "https://c.basemaps.cartocdn.com", "supports_spdy": true}], "supports_quic": {"address": "***********", "used_quic": true}, "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}