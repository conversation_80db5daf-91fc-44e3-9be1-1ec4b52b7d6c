<template>
  <el-dialog
    v-model="visible"
    title="模板设计器"
    fullscreen
    :before-close="handleClose"
    :z-index="1000"
    :show-close="false"
    append-to-body
    :close-on-press-escape="false"
    class="report-designer-dialog"
    body-class="report-designer-dialog__body"
    @closed="onDialogClosed"
  >
    <template #header>
      <div class="report-designer-header">
        <div class="el-dialog__title">
          <el-icon class="back-page-icon" @click="handleClose">
            <Back />
          </el-icon>
          <div class="workflow-info">
            <span class="title">{{ templateName || '未命名的模板' }}<el-icon><EditPen /></el-icon></span>
            <el-input
              v-model="templateName"
              placeholder="未命名的模板"
              class="workflow-name-input"
            />
          </div>
        </div>
        <div class="el-dialog__buttons">
          <span class="preview-text">预览</span>
          <el-switch v-if="from !== 'crud'" v-model="realTimePreviewEnabled" @change="onRealTimePreviewToggle"/>
          <el-divider direction="vertical" style="margin: 0 16px" />
          <el-popover placement="bottom" :width="350" trigger="click" v-model:visible="templateSelectVisible">
            <div class="template-container">
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden; max-height: 350px;">
                <div class="template-item" v-for="(row, index) in templateList" :key="index" @click="selectTemplate(row)">
                  <div class="template-item-name">{{ row.name }}</div>
                  <div class="template-item-time">{{ row.createTime }}</div>
                  <div class="template-item-action"><el-button @click.stop="deleteTemplate(row)" type="danger" size="small"> 删除</el-button></div>
                </div>
                <el-empty v-if="!templateList.length" description="暂无模板数据" style="height: 300px;" :image-size="80"/>
              </el-scrollbar>
            </div>
            <template #reference>
                <el-button @click="loadTemplate" :icon="FolderOpened" :loading="templateLoading">加载模板<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
            </template>
          </el-popover>
          <el-dropdown trigger="hover">
            <el-button style="margin-left: 12px;">更多<el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
            <template #dropdown>
              <div class="template-designer-title" v-if="false">更多功能</div>
              <el-dropdown-menu>
                <el-dropdown-item @click="saveTemplate('saveas')"><el-icon><Document /></el-icon>另存为模板</el-dropdown-item>
                <el-dropdown-item @click="newTemplate"><el-icon><DocumentAdd /></el-icon>新建</el-dropdown-item>
                <el-dropdown-item @click="importTemplate"><el-icon><Upload /></el-icon>导入</el-dropdown-item>
                <el-dropdown-item @click="exportTemplate"><el-icon><Download /></el-icon>导出</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button @click="confirmSave" :icon="FolderChecked" type="primary" style="margin-left: 12px;">保存</el-button>
        </div>
      </div>
    </template>
    <div class="report-designer-container" v-loading="loading">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧数据源面板 -->
        <div class="left-panel" v-if="from !== 'crud'">
          <div class="datasource-panel">
            <div class="panel-header">
              <h5>数据</h5>
            </div>
            <div class="panel-content panel-tree">
              <div
                class="value-input-group select-group"
                :class="{ 'no-select': !selectedDataSource }"
              >
                <el-select
                  v-model="selectedDataSource"
                  placeholder="选择数据源节点"
                  style="width: 100%; margin-bottom: 15px"
                  @change="onDataSourceChange"
                >
                  <el-option
                    v-for="source in dataSources"
                    :key="source.nodeId"
                    :label="source.label"
                    :value="source.nodeId"
                  />
                </el-select>
                <el-button v-if="selectedDataSource" @click="refreshFields" title="重新获取">
                  <el-icon :class="{ 'is-loading': loadingFields }">
                    <Refresh v-if="!loadingFields" />
                    <Loading v-else />
                  </el-icon>
                </el-button>
              </div>

              <!--              <div v-if="selectedDataSourceInfo" class="datasource-info">-->
              <!--                <p><strong>输出变量:</strong> {{ selectedDataSourceInfo.outputVariable }}</p>-->
              <!--                <p><strong>节点类型:</strong> {{ selectedDataSourceInfo.nodeType }}</p>-->
              <!--              </div>-->
              <div v-if="!selectedDataSource" class="no-datasource">请先选择数据源</div>
              <div v-else-if="loadingFields" class="loading-fields">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                分析字段结构中...
              </div>
              <div v-else-if="fieldTree.length === 0" class="no-fields">
                <el-icon>
                  <Warning />
                </el-icon>
                <p>没有可用的字段数据</p>
                <div class="no-fields-actions">
                  <el-button
                    @click="refreshFields"
                    type="primary"
                    size="small"
                    :loading="loadingFields"
                  >
                    {{ loadingFields ? '获取中...' : '获取字段结构' }}
                  </el-button>
                  <el-button @click="showSampleDataDialog" type="success" size="small">
                    添加数据
                  </el-button>
                </div>
              </div>
              <div v-else class="tree-container">
                <!-- 字段树 -->
                <el-tree
                  ref="fieldTreeRef"
                  :data="fieldTree"
                  :props="treeProps"
                  node-key="path"
                  :expand-on-click-node="false"
                  default-expand-all
                  draggable
                  :allow-drop="allowDrop"
                  @node-drag-start="onFieldDragStart"
                  @node-drag-end="onFieldDragEnd"
                  style="width: fit-content"
                >
                  <template #default="{ node, data }">
                    <div class="field-node" @click="selectField(data)">
                      <el-icon class="field-icon">
                        <component :is="getFieldIcon(data.type)" />
                      </el-icon>
                      <span class="field-label">{{ data.label }}</span>
                      <span class="field-type">{{ data.type }}</span>
                      <span v-if="data.isArray && data.arrayLength" class="field-array-info"
                        >{{ data.arrayLength }}项</span
                      >
                      <span v-if="data.sample" class="field-sample">{{ data.sample }}</span>
                    </div>
                  </template>
                </el-tree>
              </div>
            </div>
          </div>
        </div>

        <!-- 左侧面板 -->
<!--        <div class="left-panel">-->
<!--          <div class="fields-panel">-->
<!--            <div class="panel-header">-->
<!--              <h5>数据</h5>-->
<!--            </div>-->
<!--            <div class="panel-content panel-tree-content">-->
<!--                <el-tree v-if="visible" :props="{ isLeaf: 'leaf', children: 'children' }" default-expand-all node-key="path"-->
<!--                         :load="loadDataTreeSource" lazy style="width: fit-content;">-->
<!--                  <template #default="{ node, data }">-->
<!--                    <div class="field-node" @click="selectField(data)">-->
<!--                      <el-icon class="field-icon">-->
<!--                        <component :is="getFieldIcon(data.type)" />-->
<!--                      </el-icon>-->
<!--                      <span class="field-label">{{ data.label }}</span>-->
<!--                      <span class="field-type" v-if="data.type">{{ data.type }}</span>-->
<!--                      <template v-if="data.sid === 'variables'">-->
<!--&lt;!&ndash;                        <span class="field-array-info" v-if="data.isSystem">内置</span>&ndash;&gt;-->
<!--&lt;!&ndash;                        <span class="field-array-info" v-else>工作流</span>&ndash;&gt;-->
<!--                        <span class="field-sample" v-if="data.value">{{ FormatVariableValue(data.value, data.type) }}</span>-->
<!--                      </template>-->
<!--                      <span v-if="data.isArray && data.arrayLength" class="field-array-info"-->
<!--                        >{{ data.arrayLength }}项</span-->
<!--                      >-->
<!--                      <span v-if="data.sample" class="field-sample">{{ data.sample }}</span>-->
<!--                    </div>-->
<!--                  </template>-->
<!--                </el-tree>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

        <!-- 中间表格编辑器 -->
        <div class="center-panel">
          <div class="word-container" style="display:flex;width:100%;height:100%;" v-if="type==='word'">
            <word-designer :allFields="getAllFields()" @click="wordClick" :currentTemplate="currentTemplate" :draggingField="draggingField" @template-saved="wordTemplateSaved" @change-template="changeTemplate" :templateName="templateName" @close="handleClose" @template-selected="templateSelectVisible=false" ref="wordDesignerRef" :dataSources="dataSources" :selectedDataSource="selectedDataSource" :template-list="templateList"></word-designer>
          </div>

          <template v-else>
            <div class="editor-section">
              <div ref="univerContainer" class="univer-container"></div>
            </div>

            <!-- 实时预览区域 - 放在下方 -->
            <div v-show="realTimePreviewEnabled" class="preview-section-bottom">
              <div class="preview-header">
                <h3>实时预览</h3>
                <div class="preview-controls">
                  <el-button
                    @click="refreshRealTimePreview"
                    size="small"
                    :loading="loadingRealTimePreview"
                  >
                    <el-icon v-if="!loadingRealTimePreview" style="margin-right: 4px">
                      <Refresh />
                    </el-icon>
                    刷新
                  </el-button>
                </div>
              </div>
              <div class="preview-container-bottom">
                <div v-if="loadingRealTimePreview" class="preview-loading">
                  <el-icon class="is-loading">
                    <Loading />
                  </el-icon>
                  <span>生成预览中...</span>
                </div>
                <div v-else-if="realTimePreviewError" class="preview-error">
                  <el-icon>
                    <Warning />
                  </el-icon>
                  <span>{{ realTimePreviewError }}</span>
                </div>
                <div class="preview-content-bottom">
                  <div class="preview-grid-bottom">
                    <!-- Excel样式的表格预览 -->
                    <div ref="previewUniverContainer" class="univer-container"></div>
                  </div>
                </div>
              </div>
            </div>
          </template>

        </div>

        <!-- 右侧单元格配置面板 -->
        <div class="right-panel">
          <div class="cell-config-panel">
            <div class="panel-header">
              <h5>{{type==='excel'?'单元格配置':'内容设置'}}</h5>
              <div class="cell-selector">
                <span class="selected-cell">{{ selectedCell || (type==='excel'?'未选择':'') }}</span>
              </div>
            </div>
            <div class="panel-content">
              <div v-if="type==='excel'&&!selectedCell" class="no-selection">
                <el-icon>
                  <Grid />
                </el-icon>
                <p>请点击选择单元格</p>
              </div>
              <div v-else class="cell-config">
                <div class="config-section">
                  <div class="form-group">
                    <label>值:</label>
                    <div class="value-input-group select-group">
                      <el-select
                        v-model="currentCellValue"
                        placeholder="选择字段或输入文本"
                        style="flex: 1"
                        filterable
                        allow-create
                        default-first-option
                        :reserve-keyword="false"
                        @change="onValueChangeImmediate"
                      >
                        <el-option
                          v-for="field in getAllFields()"
                          :key="field.path"
                          :label="field.path"
                          :value="field.path"
                        >
                          <span style="float: left">{{ field.path }}</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">{{
                            field.type
                          }}</span>
                        </el-option>
                      </el-select>
                      <el-button
                        @click="VariableInputFieldRef?.showVariablePicker"
                        :icon="MagicStick"
                        size="default"
                        style="flex-shrink: 0"
                        title="插入变量"
                      >
                      </el-button>
                    </div>
                    <VariableInputField
                      ref="VariableInputFieldRef"
                      :model-value="variableInputValue"
                      @update:model-value="onVariableInput"
                      :field-config="{
                        type: 'string',
                        placeholder: '输入文本或插入变量...',
                        fieldName: '',
                        allValues: {},
                        variableSupport: true,
                      }"
                      style="display: none"
                    />
                  </div>

                  <div class="form-group">
                    <label>扩展方向:</label>
                    <el-radio-group v-model="expandDirection" :disabled="isDisabledExpandDirection">
                      <el-radio value="vertical">纵向</el-radio>
                      <el-radio value="horizontal">横向</el-radio>
                    </el-radio-group>
                  </div>

                  <div class="form-group" v-if="type!=='word'&&selectedCell && cellBindings[selectedCell]">
                    <div class="label-with-button">
                      <label>数据转换:</label>
                      <el-button
                        @click="addCondition"
                        type="primary"
                        size="small"
                        link
                        :icon="Plus"
                      >
                        新建
                      </el-button>
                    </div>
                    <div class="conditional-config">
                      <div class="conditional-settings">
                        <div class="conditions-list">
                          <div
                            v-for="(condition, index) in conditions"
                            :key="index"
                            class="condition-item"
                            :class="{ 'is-else': index === conditions.length - 1 }"
                          >
                            <div class="condition-content">
                              <div v-if="index < conditions.length - 1" class="when-condition">
                                <div class="condition-row">
                                  <span class="condition-prefix block">如果值</span>
                                  <el-select v-model="condition.operator" style="flex: 1">
                                    <el-option label="等于" value="==" />
                                    <el-option label="不等于" value="!=" />
                                    <el-option label="大于" value=">" />
                                    <el-option label="小于" value="<" />
                                    <el-option label="大于等于" value=">=" />
                                    <el-option label="小于等于" value="<=" />
                                    <el-option label="区间内" value="between" />
                                    <el-option label="区间外" value="not_between" />
                                    <el-option label="包含" value="contains" />
                                    <el-option label="不包含" value="not_contains" />
                                    <el-option label="为空" value="is_empty" />
                                    <el-option label="不为空" value="not_empty" />
                                  </el-select>
                                  <el-button
                                    v-if="conditions.length > 2"
                                    @click="removeCondition(index)"
                                    size="small"
                                    :icon="Delete"
                                    link
                                    title="删除条件"
                                  />
                                </div>
                                <div class="condition-row">
                                  <!-- 区间输入 -->
                                  <div
                                    class="range-input"
                                    v-if="['between', 'not_between'].includes(condition.operator)"
                                  >
                                    <el-input
                                      v-model="condition.minValue"
                                      placeholder="最小值"
                                      style="flex: 1"
                                    />
                                    <span class="range-separator">~</span>
                                    <el-input
                                      v-model="condition.maxValue"
                                      placeholder="最大值"
                                      style="flex: 1"
                                    />
                                  </div>
                                  <!-- 单值输入 -->
                                  <el-input
                                    v-else-if="
                                      !['is_empty', 'not_empty'].includes(condition.operator)
                                    "
                                    v-model="condition.value"
                                    placeholder="比较值"
                                    style="flex: 1px"
                                  />
                                </div>
                                <div class="condition-row">
                                  <span class="condition-then">则显示</span>
                                  <el-input
                                    v-model="condition.result"
                                    placeholder="结果"
                                    style="flex: 1"
                                  />
                                </div>
                              </div>

                              <div v-else class="else-condition">
                                <div class="condition-row">
                                  <span class="condition-prefix">否则</span>
                                </div>
                                <div class="condition-row">
                                  <span class="condition-prefix">则显示</span>
                                  <el-input
                                    v-model="condition.result"
                                    placeholder="留空显示原始值"
                                    style="flex: 1"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="condition-preview" v-if="multiConditionPreview">
                          <div class="preview-title">转换规则预览:</div>
                          <div class="preview-rules">
                            <div
                              v-for="(rule, index) in previewRules"
                              :key="index"
                              class="preview-rule"
                            >
                              {{ rule }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-group" v-if="type!=='word'&&selectedCell && cellBindings[selectedCell]">
                    <div class="label-with-button">
                      <label>显示条件:</label>
                      <el-button
                        @click="addDisplayCondition"
                        type="primary"
                        size="small"
                        link
                        :icon="Plus"
                      >
                        新建
                      </el-button>
                    </div>
                    <div class="conditional-config">
                      <div class="conditional-settings">
                        <div class="conditions-list">
                          <div
                            v-for="(condition, index) in displayConditions"
                            class="condition-item"
                            :key="index"
                          >
                            <div class="condition-content">
                              <div class="when-condition">
                                <div class="condition-row">
                                  <!-- 单值输入 -->
                                  <el-select
                                    v-model="condition.field"
                                    placeholder="选择字段或输入文本"
                                    style="width: 100%"
                                    filterable
                                    allow-create
                                    default-first-option
                                    :reserve-keyword="false"
                                  >
                                    <el-option
                                      v-for="field in getAllFields()"
                                      :key="field.path"
                                      :label="field.path"
                                      :value="field.path"
                                    >
                                      <span style="float: left">{{ field.path }}</span>
                                      <span style="float: right; color: #8492a6; font-size: 13px">{{
                                        field.type
                                      }}</span>
                                    </el-option>
                                  </el-select>
                                  <el-select v-model="condition.relation" style="width: 100%">
                                    <el-option label="==" value="==" />
                                    <el-option label="!=" value="!=" />
                                    <el-option label=">" value=">" />
                                    <el-option label="<" value="<" />
                                    <el-option label=">=" value=">=" />
                                    <el-option label="<=" value="<=" />
                                  </el-select>
                                  <el-input
                                    v-model="condition.value"
                                    placeholder="值"
                                    style="width: calc(100% - 30px)"
                                  />
                                  <el-button
                                    @click="removeDisplayCondition(index)"
                                    size="small"
                                    :icon="Delete"
                                    link
                                    title="删除条件"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 表格列配置对话框 -->
  <el-dialog v-model="tableColumnDialogVisible" title="配置表格列" width="700px">
    <div class="table-config">
      <div class="config-header">
        <p><strong>数组字段:</strong> {{ selectedArrayField }}</p>
        <p><strong>起始单元格:</strong> {{ tableStartCell }}</p>
      </div>

      <el-table :data="tableColumns" class="column-config-table">
        <el-table-column label="列" width="80">
          <template #default="{ row, $index }">
            <el-input v-model="row.column" placeholder="A" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="字段路径" min-width="150">
          <template #default="{ row }">
            <span>{{ row.fieldPath }}</span>
          </template>
        </el-table-column>
        <el-table-column label="表头" width="120">
          <template #default="{ row }">
            <el-input v-model="row.header" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="格式" width="120">
          <template #default="{ row }">
            <el-input v-model="row.format" placeholder="可选" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row, $index }">
            <el-button
              @click="removeTableColumn($index)"
              type="danger"
              size="small"
              :icon="Delete"
              link
            />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-actions">
        <el-button @click="addTableColumn" type="primary" size="small"> 添加列</el-button>
        <el-button @click="bindTableData" type="success" size="small"> 绑定表格</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="tableColumnDialogVisible = false">取消</el-button>
        <el-button @click="confirmTableBinding" type="primary">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 数据对话框 -->
  <el-dialog v-model="sampleDataDialogVisible" title="添加数据" width="600px">
    <div class="sample-data-dialog">
      <p>
        为节点 <strong>{{ selectedDataSourceInfo?.label }}</strong> 添加数据：
      </p>
      <el-input
        v-model="sampleDataInput"
        type="textarea"
        :rows="15"
        placeholder="请输入JSON格式的数据..."
        class="sample-data-input"
      />
      <div class="sample-data-tips">
        <p><strong>提示：</strong></p>
        <ul>
          <li>请输入有效的JSON格式数据</li>
          <li>数据结构应该与实际响应或查询结果一致</li>
          <li>可以包含嵌套对象和数组</li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="sampleDataDialogVisible = false">取消</el-button>
        <el-button @click="validateSampleData" type="primary">验证格式</el-button>
        <el-button @click="saveSampleData" type="success">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 预览对话框 -->
  <el-dialog
    v-model="previewDialogVisible"
    title="报表预览"
    width="80%"
    :close-on-click-modal="false"
  >
    <div class="preview-container">
      <div class="preview-content">
        <div class="preview-grid">
          <!-- Excel样式的表格预览 -->
          <table class="excel-preview-table">
            <thead>
              <tr>
                <th class="row-header"></th>
                <th v-for="col in 15" :key="col" class="col-header">
                  {{ String.fromCharCode(64 + col) }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in 25" :key="row">
                <td class="row-header">{{ row }}</td>
                <td v-for="col in 15" :key="col" class="preview-cell">
                  <div class="cell-content">
                    {{ getCellPreviewValue(getCellAddress(row - 1, col - 1)) || '' }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { LoadFunction } from 'element-plus'
import {
  Document,
  DocumentAdd,
  FolderOpened,
  Upload,
  Download,
  Delete,
  Loading,
  Grid,
  DataBoard,
  Warning,
  ArrowDown,
  ArrowRight,
  Refresh,
  MagicStick,
  Plus,
  FolderChecked,
} from '@element-plus/icons-vue'
import VariableInputField from '@/components/common/VariableInputField.vue'
import wordDesigner from './WordDesigner.vue'

import { ConditionUtils } from "@/utils/conditions-utils.ts"

// Univer imports
import { createUniver, LocaleType, merge } from '@univerjs/presets'

import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsDrawingPreset } from '@univerjs/presets/preset-sheets-drawing'
import sheetsDrawingZhCN from '@univerjs/presets/preset-sheets-drawing/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-drawing.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsHyperLinkPreset } from '@univerjs/presets/preset-sheets-hyper-link'
import sheetsHyperLinkZhCN from '@univerjs/presets/preset-sheets-hyper-link/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-hyper-link.css'

import { ClearSelectionAllCommand } from '@univerjs/sheets'
console.warn({commandId: ClearSelectionAllCommand.id})



// 数据分析和绑定引擎
import {
  dataStructureAnalyzer,
  type DataSource,
  type FieldInfo,
} from '@/utils/dataStructureAnalyzer'
import {
  fieldBindingEngine,
  type CellBinding,
  type ColumnBinding,
  type LoopConfig, FieldBindingEngine,
} from '@/utils/fieldBindingEngine'

// 工作流store
import { useWorkflowStore } from '@/stores/workflow'

// @ts-ignore
import SnowflakeId from 'snowflake-id'
import { getVariableType, getAvailableVariables } from "@/utils/availableVariables.ts";

// @ts-ignore
import utils from '@/utils/utils'

const generator = new SnowflakeId({
  offset: (2011 - 1970) * 31536000 * 1000,
})

interface Props {
  modelValue: boolean,
  from?: string
  type: string
  templateId?: string
  isFromNode?: boolean, // 是否来自节点设计点击创建模板打开：true 点击保存后就关闭界面
}

const props = withDefaults(defineProps<Props>(), {
  // 为可选属性设置默认值
  type: 'excel'
});

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: string
  data: any
  bindings: any
  createTime: string
}

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'template-saved': [template: ReportTemplate]
  'template-deleted': [value: string]
  'template-closed': [value]
}>()

// 响应式数据
const visible = ref(false)
const templateSelectVisible = ref(false)
const templateName = ref('')
const currentTemplate = ref()
const templateList = ref<ReportTemplate[]>([])

// Univer容器引用
const univerContainer = ref<HTMLElement>()
// Univer 实例
let univerInstance: any = null
let univerAPIInstance: any = null

// Univer预览容器引用
const previewUniverContainer = ref<HTMLElement>()
// Univer 预览实例
let previewUniverInstance: any = null
let previewUniverAPIInstance: any = null

// 工作流store
const workflowStore = useWorkflowStore()
// 当前选中组件
const selectedNode = computed(() => workflowStore.selectedNode)

//word实例
const wordDesignerRef = ref<HTMLElement>()

// 数据源相关
const dataSources = ref<DataSource[]>([])
const selectedDataSource = ref<string>('')
const loadingFields = ref(false)
const fieldTree = ref<FieldInfo[]>([])

// 字段绑定相关
const selectedCell = ref('')
const selectedFieldPath = ref('')
const bindingFormat = ref('')
const cellBindings = ref<Record<string, CellBinding>>({})

// 当前单元格输入值
const currentCellInput = ref('')

// 变量输入值
const variableInputValue = ref('')

// 变量选择器引用
const VariableInputFieldRef = ref()

// 当前单元格的值（用于回显和编辑）
const currentCellValue = computed({
  get() {
    // 如果用户正在输入，优先返回输入值
    if (currentCellInput.value) {
      return currentCellInput.value
    }

    if (!selectedCell.value) return ''

    // 优先返回单元格的实际值
    try {
      if (univerAPIInstance) {
        const workbook = univerAPIInstance.getActiveWorkbook()
        const worksheet = workbook.getActiveSheet()
        const range = worksheet.getRange(selectedCell.value)
        const cellValue = range.getValue()

        // 如果单元格值是绑定表达式格式，说明是字段绑定，返回字段路径
        if (
          typeof cellValue === 'string' &&
          cellValue.startsWith('${') &&
          cellValue.endsWith('}')
        ) {
          return cellValue.replace(/^\$\{|\}$/g, '')
        }

        // 如果单元格值是 #{} 格式，说明是字段绑定，返回字段路径
        if (
          typeof cellValue === 'string' &&
          cellValue.startsWith('#{') &&
          cellValue.endsWith('}')
        ) {
          return cellValue.replace(/^#\{|\}$/g, '')
        }

        // 如果单元格值是 [字段] 格式，说明是字段绑定，返回字段路径
        if (typeof cellValue === 'string' && cellValue.startsWith('[字段] ')) {
          return cellValue.replace('[字段] ', '')
        }

        // 否则返回实际的单元格内容（固定文本）
        return cellValue || ''
      }
    } catch (error) {
      console.warn('获取单元格值失败:', error)
    }

    // 如果无法获取单元格值，且有绑定记录，返回绑定的字段路径作为后备
    if (cellBindings.value[selectedCell.value]) {
      const binding = cellBindings.value[selectedCell.value]
      const fieldPath =
        binding.fieldPath ||
        binding.expression?.replace(/^\$\{|\}$/g, '').replace(/^#\{|\}$/g, '') ||
        ''
      console.log(
        `currentCellValue getter (后备): 单元格 ${selectedCell.value}, fieldPath = ${fieldPath}`,
      )
      return fieldPath
    }

    return ''
  },
  set(value) {
    currentCellInput.value = value
    selectedFieldPath.value = value
  },
})

// 监听单元格选择变化，清空输入
watch(selectedCell, () => {
  currentCellInput.value = ''
})

// 表格绑定相关
const tableStartCell = ref('A2')
const selectedArrayField = ref('')
const tableHasHeader = ref(true)
const tableColumnDialogVisible = ref(false)
const tableColumns = ref<ColumnBinding[]>([])

// UI状态
const activeTab = ref('datasource')
const bindingSubTab = ref('cell')
const cellConfigTab = ref('single')
const sampleDataDialogVisible = ref(false)
const sampleDataInput = ref('')
const lastFieldResult = ref<any>(null)
const loadingPreview = ref(false)

// 扩展方向配置
const expandDirection = computed({
  get() {
    // 如果当前选中的单元格有循环绑定，返回绑定中的方向
    if (selectedCell.value && cellBindings.value[selectedCell.value]) {
      const binding = cellBindings.value[selectedCell.value]
      if (binding.type === 'loop' && binding.loopConfig) {
        return binding.loopConfig.direction || 'vertical'
      }
    }
    // 否则返回默认值
    return 'vertical'
  },
  set(value: string) {
    ;
    // 如果当前单元格有循环绑定，更新绑定中的方向
    if (selectedCell.value && cellBindings.value[selectedCell.value]) {
      const binding = cellBindings.value[selectedCell.value]
      if (binding.type === 'loop' && binding.loopConfig) {
        binding.loopConfig.direction = value
        if(binding.tdElement){
          let old_conceptId = binding.tdElement.conceptId?binding.tdElement.conceptId:null;
          let old_conceptId_obj = old_conceptId?JSON.parse(old_conceptId):{}
          old_conceptId_obj.direction= value
          binding.tdElement.conceptId = JSON.stringify(old_conceptId_obj)
        }
        console.log(`更新循环绑定方向: ${selectedCell.value} -> ${value}`)
      }
    }
  },
})
// 是否禁用横向纵向
const isDisabledExpandDirection = computed(()=>{
  if (selectedCell.value && cellBindings.value[selectedCell.value]) {
    const binding = cellBindings.value[selectedCell.value]
    if (binding.type === 'loop' && binding.loopConfig) {
      return false
    }
  }
  return true
})

// 条件转换配置（移除开关，直接可用）

// 多条件配置
interface ConditionRule {
  operator: string
  value: string
  result: string
  minValue?: string // 区间最小值
  maxValue?: string // 区间最大值
}

const conditions = ref<ConditionRule[]>([
  { operator: '==', value: '', result: '', minValue: '', maxValue: '' }, // 默认条件
  { operator: '', value: '', result: '', minValue: '', maxValue: '' }, // ELSE条件，空值表示显示原始值
])

// 添加条件
const addCondition = () => {
  // 在ELSE条件之前插入新条件
  conditions.value.splice(conditions.value.length - 1, 0, {
    operator: '==',
    value: '',
    result: '',
    minValue: '',
    maxValue: '',
  })
}

//模板更改
const changeTemplate = (template:any)=>{
  templateName.value = template.name
}


// 移除条件
const removeCondition = (index: number) => {
  if (conditions.value.length > 2 && index < conditions.value.length - 1) {
    conditions.value.splice(index, 1)
  }
}

// 多条件预览
const multiConditionPreview = computed(() => {
  return conditions.value.some((c) => c.result)
})

// 预览规则
const previewRules = computed(() => {
  const rules: string[] = []

  // 处理条件规则
  for (let i = 0; i < conditions.value.length - 1; i++) {
    const condition = conditions.value[i]
    if (condition.operator && condition.result) {
      const operatorText = getOperatorText(condition.operator)
      let valueText = ''

      // 处理区间显示
      if (['between', 'not_between'].includes(condition.operator)) {
        if (condition.minValue && condition.maxValue) {
          valueText = ` ${condition.minValue}~${condition.maxValue}`
        }
      } else if (!['is_empty', 'not_empty'].includes(condition.operator)) {
        valueText = ` ${condition.value}`
      }

      rules.push(`• 如果值${operatorText}${valueText}，则显示"${condition.result}"`)
    }
  }

  // 处理ELSE规则
  const elseCondition = conditions.value[conditions.value.length - 1]
  if (elseCondition.result) {
    rules.push(`• 其他情况显示"${elseCondition.result}"`)
  } else {
    rules.push(`• 其他情况显示原始值`)
  }

  return rules
})

// 获取操作符文本
const getOperatorText = (operator: string) => {
  const operatorMap = {
    '==': '等于',
    '!=': '不等于',
    '>': '大于',
    '<': '小于',
    '>=': '大于等于',
    '<=': '小于等于',
    between: '在区间',
    not_between: '不在区间',
    contains: '包含',
    not_contains: '不包含',
    is_empty: '为空',
    not_empty: '不为空',
  }
  return operatorMap[operator] || operator
}

// 生成条件表达式（JSON格式）
const generateConditionalExpression = () => {
  // 验证条件有效性
  const validConditions = conditions.value.slice(0, -1).filter((c) => {
    if (!c.operator || !c.result) return false

    // 区间条件需要有最小值和最大值
    if (['between', 'not_between'].includes(c.operator)) {
      return (
        c.minValue !== undefined &&
        c.maxValue !== undefined &&
        c.minValue !== '' &&
        c.maxValue !== ''
      )
    }

    // 空值检查不需要比较值
    if (['is_empty', 'not_empty'].includes(c.operator)) {
      return true
    }

    // 其他条件需要有比较值
    return c.value !== undefined && c.value !== ''
  })

  const elseCondition = conditions.value[conditions.value.length - 1]

  // 只要有有效条件就生成表达式，else值可以为空（表示使用原始值）
  if (validConditions.length === 0) return undefined

  return JSON.stringify({
    type: 'multi_condition',
    conditions: validConditions.map((c) => {
      // 根据操作符类型构建条件对象
      const condition: any = {
        operator: c.operator,
        result: c.result,
      }

      // 区间操作需要minValue和maxValue
      if (['between', 'not_between'].includes(c.operator)) {
        condition.minValue = c.minValue
        condition.maxValue = c.maxValue
      }
      // 空值检查不需要额外字段
      else if (['is_empty', 'not_empty'].includes(c.operator)) {
        // 不需要额外字段
      }
      // 其他操作需要value字段
      else {
        condition.value = c.value
      }

      return condition
    }),
    else: elseCondition.result || '', // 空字符串表示使用原始值
  })
}

// 解析条件表达式
const parseConditionalExpression = (expression: string) => {
  if (!expression) {
    resetConditions()
    return
  }

  try {
    // 尝试解析新的JSON格式
    const parsed = JSON.parse(expression)
    if (parsed.type === 'multi_condition' || parsed.type === 'case_when') {
      conditions.value = [
        ...parsed.conditions.map((c: any) => ({
          operator: c.operator,
          value: c.value || '',
          result: c.result,
          minValue: c.minValue || '',
          maxValue: c.maxValue || '',
        })),
        { operator: '', value: '', result: parsed.else || '', minValue: '', maxValue: '' },
      ]
      return
    }
  } catch (e) {
    // 如果不是JSON格式，尝试解析旧的三元运算符格式
    const match = expression.match(/value\s*([><=!]+)\s*(.+?)\s*\?\s*'(.+?)'\s*:\s*'(.+?)'/)
    if (match) {
      conditions.value = [
        {
          operator: match[1],
          value: match[2].trim(),
          result: match[3],
          minValue: '',
          maxValue: '',
        },
        { operator: '', value: '', result: match[4], minValue: '', maxValue: '' },
      ]
      return
    }
  }

  resetConditions()
}

// 重置条件
const resetConditions = () => {
  conditions.value = [
    { operator: '==', value: '', result: '', minValue: '', maxValue: '' },
    { operator: '', value: '', result: '', minValue: '', maxValue: '' }, // else值为空，表示显示原始值
  ]
  displayConditions.value = [
    {
      field: '',
      relation: '==',
      value: ''
    }
  ]
}

// 监听单元格选择变化，更新条件配置
watch(selectedCell, () => {
  if (selectedCell.value && cellBindings.value[selectedCell.value]) {
    const binding = cellBindings.value[selectedCell.value]
    parseConditionalExpression(binding.conditionalExpression || '')
    if (binding.displayConditions) {
      displayConditions.value = JSON.parse(binding.displayConditions)
    } else {
      displayConditions.value = [
        {
          field: '',
          relation: '==',
          value: ''
        }
      ]
    }
  } else {
    resetConditions()
  }
})

// 监听条件配置变化，更新绑定
watch(
  conditions,
  () => {
    if (selectedCell.value && cellBindings.value[selectedCell.value]) {
      const binding = cellBindings.value[selectedCell.value]
      binding.conditionalExpression = generateConditionalExpression()
      console.log(`更新条件表达式: ${selectedCell.value} -> ${binding.conditionalExpression}`)
    }
  },
  { deep: true },
)

// 显示条件配置
interface DisplayConditionRule {
  field: string
  relation: string
  value: string
}

const displayConditions = ref<DisplayConditionRule[]>([
  { field: '', relation: '==', value: '' }, // 默认条件
])

// 添加显示条件
const addDisplayCondition = () => {
  // 在ELSE条件之前插入新条件
  displayConditions.value.splice(displayConditions.value.length - 1, 0, {
    field: '',
    relation: '==',
    value: ''
  })
}

// 移除显示条件
const removeDisplayCondition = (index: number) => {
  displayConditions.value.splice(index, 1)
}

// 监听显示条件配置变化，更新绑定
watch(
  displayConditions,
  () => {
    if (selectedCell.value && cellBindings.value[selectedCell.value]) {
      const binding = cellBindings.value[selectedCell.value]
      const newDisplayConditions = displayConditions.value.filter(it => it.field)
      binding.displayConditions = newDisplayConditions.length > 0 ? JSON.stringify(newDisplayConditions) : ''
      console.log(`更新显示条件表达式: ${selectedCell.value} -> ${binding.displayConditions}`)
    }
  },
  { deep: true },
)

// 预览相关
const previewDialogVisible = ref(false)
const previewCellData = ref<Record<string, any>>({})
const previewData = ref<any>(null)

// 实时预览相关
const realTimePreviewEnabled = ref(false)

const loadingRealTimePreview = ref(false)
const realTimePreviewError = ref('')
const realTimePreviewData = ref<Record<string, any>>({})
const realTimePreviewRawData = ref<any>(null)

// 多级循环表配置
const loopTableConfig = ref({
  startCell: 'A3',
  dataSource: '',
  loops: [
    {
      id: 1,
      arrayField: '',
      direction: 'vertical', // vertical, horizontal
      fields: [
        {
          field: '',
          label: '',
          offset: { row: 0, col: 0 },
        },
      ],
    },
  ],
  showHeaders: true,
  showSubtotals: false,
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label',
}

// 计算属性
const selectedDataSourceInfo = computed(() => {
  return dataSources.value.find((ds) => ds.nodeId === selectedDataSource.value)
})

const arrayFields = computed(() => {
  // 递归获取所有数组字段，包括嵌套的数组字段
  const getAllArrayFields = (fields: FieldInfo[], prefix = ''): FieldInfo[] => {
    let result: FieldInfo[] = []

    for (const field of fields) {
      const fullPath = prefix ? `${prefix}.${field.path}` : field.path

      if (field.isArray || (field.type === 'array' || field.type === 'list')) {
        result.push({
          ...field,
          path: fullPath,
        })
      }

      // 递归处理子字段
      if (field.children && field.children.length > 0) {
        result = result.concat(getAllArrayFields(field.children, fullPath))
      }
    }

    return result
  }

  return getAllArrayFields(fieldTree.value)
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      nextTick(() => {
        initializeDesigner()
      })
    }
  },
  { immediate: true },
)

const currentNodeTemplateId = ref(null)
const loading = ref(false)
// 监听 visible 变化
watch(visible, async (newVal) => {
  emit('update:modelValue', newVal)
  templateName.value = ''
  currentTemplate.value = {}
  if (!newVal) {
    currentNodeTemplateId.value = null
    destroyUniver()
  }
  else{
    // 获取模板列表
    await loadTemplateList()
    // 如果不是来自属性面板打开的，尝试获取当前选中组件绑定模板
    if(!props.isFromNode) {
      // 获取当前选中组件的绑定模板id
      currentNodeTemplateId.value = selectedNode.value?.data?.config?.template_id
      if(currentNodeTemplateId.value){
        try {
          loading.value = true
          const idx = templateList.value.findIndex(it => it.id === currentNodeTemplateId.value)
          if(idx > -1){
            await selectTemplate(templateList.value[idx])
          }
          loading.value = false
        } catch (error) {
          loading.value = false
        }
      }
    }
    // 如果是从服务包导入的，打开后自动加载
    if (props.templateId){
      try {
          loading.value = true
          const idx = templateList.value.findIndex(it => it.id === props.templateId)
          if(idx > -1){
            await selectTemplate(templateList.value[idx])
          }
          loading.value = false
        } catch (error) {
          loading.value = false
        }
    }
  }
})

// 监听数据源变化
watch(selectedDataSource, (newVal) => {
  if (newVal) {
    loadFieldStructure()
  }
})

// 初始化univer配置
const initUniverConfig = (container, extendsConfig = {}) => {
  return createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {},
        UniverPresetSheetsCoreZhCN,
        sheetsConditionalFormattingZhCN,
        sheetsDataValidationZhCN,
        sheetsDrawingZhCN,
        sheetsFilterZhCN,
        sheetsHyperLinkZhCN,
      ),
    },
    // theme: defaultTheme,
    presets: [
      UniverSheetsCorePreset({
        container: container,
        ...extendsConfig,
      }),
      UniverSheetsConditionalFormattingPreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsDrawingPreset(),
      UniverSheetsFilterPreset(),
      UniverSheetsHyperLinkPreset(),
    ],
  })
}

// 初始化设计器
const initializeDesigner = async () => {
  await initUniver()
  if (props.from !== 'crud') {
    await loadDataSources()
  }
}

// 初始化 Univer
const initUniver = async () => {
  if (!univerContainer.value || univerInstance) return

  try {
    const { univer, univerAPI } = initUniverConfig(univerContainer.value)

    univerInstance = univer
    univerAPIInstance = univerAPI

    // 创建默认工作簿
    univerAPI.createWorkbook({
      name: '模板',
      sheets: {
        sheet1: {
          id: 'sheet1',
          name: '模板',
          cellData: {
            0: {
              0: { v: '模板标题' },
              1: { v: '日期: ${innerCurrentTime}' },
            },
            1: {
              0: { v: '数据项' },
              1: { v: '值' },
            },
          },
          rowCount: 30,
          columnCount: 15,
        },
      },
    })

    // 添加延迟确保工作表渲染完成
    await nextTick()

    // 监听在拖动元素放置到单元格时触发
    univerAPI.addEvent(univerAPI.Event.Drop, ({ worksheet, workbook, row, column }) => {
      // 检查是否有正在拖拽的字段
      if (draggingField.value) {
        const cellAddress = worksheet.getRange(row, column).getA1Notation()
        console.log(`将字段 ${draggingField.value.path} 放置到单元格 ${cellAddress}`)

        // 选中该单元格
        worksheet.getRange(cellAddress).activate()
        selectedCell.value = cellAddress

        // 绑定字段到单元格
        bindFieldToCell(draggingField.value, cellAddress)

        // 触发值变更事件
        onValueChangeImmediate(draggingField.value.path)

        // 重置拖拽状态
        draggingField.value = null
      }
    })

    // 监听单元格选择事件
    selectionListenerDisposable = setupCellSelectionListener()
  } catch (error) {
    console.error('初始化 Univer 失败:', error)
    ElMessage.error('模板设计器初始化失败')
  }
}

const wordClick = (event:any,context:any)=>{
  if(!context||!context.tableInfo)return selectedCell.value = null;
  let tableInfo = context.tableInfo
  selectedCell.value = tableInfo.element.conceptId+':'+tableInfo.trIndex+'-'+tableInfo.tdIndex;
  let cell = tableInfo.element.trList[tableInfo.trIndex].tdList[tableInfo.tdIndex]
  let cleanFieldPath = cell.value.map(it=>it.value).join("")
  const isArrayField = cleanFieldPath.includes('[]')
  let conceptId = cell.conceptId;
  let conceptIdObj = {}
  if(conceptId){
    conceptIdObj = JSON.parse(conceptId)
  }
  if(!cellBindings.value[selectedCell.value]){
    cellBindings.value[selectedCell.value] = {
      type: isArrayField?'loop':'string',
      tdElement:tableInfo.element.trList[tableInfo.trIndex].tdList[tableInfo.tdIndex],
      loopConfig: {
        direction: conceptIdObj.direction||'vertical',
      },
    }
  }
  tableInfo.element.trList[tableInfo.trIndex].tdList[tableInfo.tdIndex].conceptId = JSON.stringify({direction:cellBindings.value[selectedCell.value].loopConfig.direction})
}


// 添加绑定字段到单元格的方法
const bindFieldToCell = (field: FieldInfo, cellAddress: string) => {
  // 设置单元格值
  if (univerAPIInstance) {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const worksheet = workbook.getActiveSheet()
    const range = worksheet.getRange(cellAddress)

    // 设置单元格值为字段路径
    range.setValue(field.path)

    ElMessage.success(`已将字段 ${field.path} 绑定到单元格 ${cellAddress}`)
  }
}

// 在现有工作簿中加载模板数据
const loadTemplateDataToExistingWorkbook = async (templateData: any) => {
  if (!univerAPIInstance) {
    throw new Error('Univer实例不存在')
  }

  try {
    console.log('在现有工作簿中加载模板数据:', templateData)

    const workbook = univerAPIInstance.getActiveWorkbook()
    console.warn('当前工作簿:', {workbook})
    // 销毁工作簿
    const unitId = workbook?.getId();
    if(unitId) {
      univerAPIInstance.disposeUnit(unitId)
    }
    // 重新创建工作簿
    const workbookData = {
      ...templateData,
      // 确保必要的字段存在
      name: templateData.name || '模板',
      locale: templateData.locale || 'zhCN',
      sheetOrder: templateData.sheetOrder || ['sheet1'],
    }
    univerAPIInstance.createWorkbook(workbookData)

    console.log('模板数据加载完成')
    ElMessage.success('模板加载成功')
  } catch (error) {
    console.error('在现有工作簿中加载模板数据失败:', error)
    throw error
  }
}

// 新建模板
const newTemplate = async () => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.newTemplate()
    }
    return
  }
  try {
    // 检查是否有未保存的更改
    await ElMessageBox.confirm('是否放弃当前模板编辑内容，重新创建新模板？', '确认新建', {
      type: 'warning',
    })

    // 重置模板名称
    templateName.value = ''
    currentTemplate.value = {}

    // 清除所有绑定数据
    cellBindings.value = {}
    selectedCell.value = ''

    // 暂时禁用单元格变化监听，避免触发重复处理
    const originalListener = selectionListenerDisposable
    if (originalListener) {
      originalListener.dispose()
      selectionListenerDisposable = null
    }

    // 创建新的空白工作簿
    if (univerAPIInstance) {
      // 销毁当前工作簿
      const workbook = univerAPIInstance.getActiveWorkbook()
      if (workbook) {
        const unitId = workbook.getId()
        univerAPIInstance.disposeUnit(unitId)
      }

      // 创建新的空白工作簿
      univerAPIInstance.createWorkbook({
        name: '模板',
        sheets: {
          sheet1: {
            id: 'sheet1',
            name: '模板',
            cellData: {
              0: {
                0: { v: '模板标题' },
                1: { v: '日期: ${innerCurrentTime}' },
              },
              1: {
                0: { v: '数据项' },
                1: { v: '值' },
              },
            },
            rowCount: 30,
            columnCount: 15,
          },
        },
      })

      // 重新设置单元格选择监听器
      selectionListenerDisposable = setupCellSelectionListener()
    }

    ElMessage.success('已新建空白模板')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('新建模板失败:', error)
    }
  }
}

// 加载数据源
const loadDataSources = async () => {
  try {
    const nodes = workflowStore.nodes || []
    dataSources.value = dataStructureAnalyzer.extractDataSources(nodes)

    if (dataSources.value.length === 0) {
      ElMessage.info('当前工作流中没有找到数据源节点，请先添加HTTP请求、数据库查询等组件')
    } else {
      // 自动选择第一个数据源
      selectedDataSource.value = dataSources.value[0].nodeId
      console.log('自动选择第一个数据源:', dataSources.value[0])
    }
  } catch (error) {
    console.error('加载数据源失败:', error)
    ElMessage.error('加载数据源失败')
  }
}

// 数据源变化处理
const onDataSourceChange = () => {
  selectedFieldPath.value = ''
  fieldTree.value = []
  lastFieldResult.value = null
  if (selectedDataSource.value) {
    // 自动切换到字段标签页
    activeTab.value = 'fields'
    // 加载字段结构（使用缓存优先策略）
    loadFieldStructure()
  }
}

// 格式化变量的值
const FormatVariableValue = (value: any, type?: string) => {
  if (!value) return ''
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  // 如果是当前时间类型且值是时间格式字符串
  if (type === 'currentTime' && typeof value === 'string') {
    // 将 % 格式转换为 YYYY-MM-DD HH:mm:ss 格式
    return value
      .replace(/%Y/g, 'YYYY')
      .replace(/%m/g, 'MM')
      .replace(/%d/g, 'DD')
      .replace(/%H/g, 'HH')
      .replace(/%M/g, 'mm')
      .replace(/%S/g, 'ss')
  }
  const str = String(value)
  return str.length > 30 ? str.substring(0, 30) + '...' : str
}

// 从工作流中收集可用变量
const availableVariables = computed(() => getAvailableVariables())

// 获取树形数据源接口
const loadDataTreeSource: LoadFunction = async (node, resolve, reject) => {
  if (node.level === 0) {
    // 按sourceNodeId和sourceNode分组
    const groupedVariables = availableVariables.value.reduce((acc, variable) => {
      const key = `${variable.sourceNodeId}_${variable.sourceNode}`;
      if (!acc[key]) {
        acc[key] = {
          label: variable.sourceNode || '未分组变量',
          nodeId: `group_${variable.sourceNodeId}`,
          children: []
        };
      }
      acc[key].children.push({
        ...variable,
        leaf: true,
        sid: 'variables',
        label: variable.name,
        path: variable.name
      });
      return acc;
    }, {});

    // 转换为树形结构
    const tree = Object.values(groupedVariables).sort((a, b) => {
      if (a.nodeId === 'group_local_variable') return -1;
      if (b.nodeId === 'group_local_variable') return 1;
      return 0;
    });
    return resolve(tree);
  }

  if (node.level >= 1) {
    if (node.data.children) {
      return resolve(node.data.children);
    }
    return resolve([]);
  }
}

// 递归处理数据，如果没有children层级，则添加leaf:true
const processTreeData = (data: any[]) => {
  return data.map(item => {
    const processedItem = { ...item }
    if (!processedItem.children || processedItem.children.length === 0) {
      processedItem.leaf = true
    } else {
      processedItem.children = processTreeData(processedItem.children)
    }
    return processedItem
  })
}

// 刷新字段 - 强制发送真实请求
const refreshFields = async () => {
  if (!selectedDataSource.value) return

  const dataSource = selectedDataSourceInfo.value
  if (!dataSource) return

  loadingFields.value = true

  try {
    // 强制发送真实请求并更新缓存
    const result = await getNodeFieldsResult(dataSource, true)
    lastFieldResult.value = result

    if (result.success && result.fields && result.fields.length > 0) {
      fieldTree.value = result.fields
      ElMessage.success(
        `成功获取到 ${result.fields.length} 个字段 (来源: ${getSourceLabel(result.source)})`,
      )
    } else {
      fieldTree.value = []

      if (result.source === 'real_request_failed') {
        ElMessage.error('真实请求失败，请检查节点配置和网络连接')
      } else {
        ElMessage.warning('无法获取节点字段结构')
      }
    }
  } catch (error) {
    console.error('刷新字段失败:', error)
    ElMessage.error('刷新字段失败: ' + (error as Error).message)
    fieldTree.value = []
    lastFieldResult.value = null
  } finally {
    loadingFields.value = false
  }
}


// 树实例
const fieldTreeRef = ref(null)
// 初始加载字段 - 使用缓存优先策略
const loadFieldStructure = async () => {
  if (!selectedDataSource.value) return

  const dataSource = selectedDataSourceInfo.value
  if (!dataSource) return

  loadingFields.value = true

  try {
    // 初始加载时使用缓存优先策略
    const result = await getNodeFieldsResult(dataSource, false)
    lastFieldResult.value = result

    if (result.success && result.fields && result.fields.length > 0) {
      fieldTree.value = result.fields
    } else {
      fieldTree.value = []
    }
  } catch (error) {
    console.error('加载字段失败:', error)
    fieldTree.value = []
    lastFieldResult.value = null
  } finally {
    loadingFields.value = false
  }
}

// 获取节点字段结构（不执行节点）- 返回完整结果
const getNodeFieldsResult = async (
  dataSource: DataSource,
  forceRealRequest: boolean = false,
): Promise<any> => {
  try {
    console.log(`获取节点 ${dataSource.nodeId} 的字段结构...`)
    console.log(`强制真实请求: ${forceRealRequest}`)

    // 调用新的字段获取API
    const response = await fetch(`http://localhost:39876/api/nodes/${dataSource.nodeId}/fields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        nodes: workflowStore.nodes,
        edges: workflowStore.edges,
        force_real_request: forceRealRequest,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`获取字段失败: ${response.status} ${response.statusText}\n${errorText}`)
    }

    const result = await response.json()
    console.log('字段获取结果:', result)

    if (result.success) {
      console.log(`字段来源: ${result.source} - ${result.message}`)
    }

    return result
  } catch (error) {
    console.error('获取节点字段失败:', error)
    throw error
  }
}

// 执行节点并刷新字段
const executeNodeAndRefreshFields = async (dataSource: DataSource) => {
  try {
    loadingFields.value = true
    ElMessage.info('正在执行节点获取数据...')

    // 执行节点获取真实数据
    const nodeData = await executeNodeForData(dataSource)

    if (nodeData) {
      // 保存执行结果到缓存
      await saveNodeExecutionResult(dataSource.nodeId, nodeData)

      // 重新获取字段结构
      const result = await getNodeFieldsResult(dataSource)

      if (result.success && result.fields && result.fields.length > 0) {
        fieldTree.value = result.fields
        ElMessage.success(`成功获取到 ${result.fields.length} 个字段 (来源: 节点执行结果)`)
      } else {
        // 如果API没有返回字段，直接分析执行结果
        const fields = dataStructureAnalyzer.analyzeStructure(nodeData)
        fieldTree.value = fields
        if (fields.length > 0) {
          ElMessage.success(`成功获取到 ${fields.length} 个字段 (来源: 节点执行结果)`)
        } else {
          ElMessage.warning('节点执行成功，但没有解析到字段结构')
        }
      }
    } else {
      ElMessage.error('节点执行失败，无法获取数据')
    }
  } catch (error) {
    console.error('执行节点并刷新字段失败:', error)
    ElMessage.error('执行节点失败: ' + (error as Error).message)
  } finally {
    loadingFields.value = false
  }
}

// 保存节点执行结果
const saveNodeExecutionResult = async (nodeId: string, outputData: any) => {
  try {
    const response = await fetch(`http://localhost:39876/api/nodes/${nodeId}/sample-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(outputData),
    })

    if (!response.ok) {
      console.warn('保存节点执行结果失败:', response.statusText)
    } else {
      console.log('节点执行结果已保存到缓存')
    }
  } catch (error) {
    console.warn('保存节点执行结果失败:', error)
  }
}

// 获取数据来源标签
const getSourceLabel = (source: string) => {
  const labels = {
    execution_history: '执行历史',
    cached_sample: '缓存数据',
    real_request: '真实请求',
    real_request_failed: '请求失败',
    inferred: '配置推断',
    default_template: '默认模板',
    no_data: '无数据',
  }
  return labels[source] || source
}

// 获取数据来源标签类型
const getSourceTagType = (source: string) => {
  const types = {
    execution_history: 'success',
    cached_sample: 'info',
    real_request: 'success',
    real_request_failed: 'danger',
    inferred: 'warning',
    default_template: 'info',
    no_data: 'danger',
  }
  return types[source] || 'info'
}

// 显示字段来源信息
const showFieldSource = () => {
  if (!lastFieldResult.value) return

  const result = lastFieldResult.value
  const sourceInfo = {
    execution_history: '数据来源于之前的节点执行结果，这是最准确的字段结构。',
    cached_sample: '数据来源于用户提供的示例数据或之前缓存的数据。',
    real_request: '数据来源于直接调用节点配置的API或查询，这是真实的字段结构。',
    real_request_failed: '尝试发送真实请求但失败了，请检查节点配置。',
    inferred: '数据来源于根据节点类型和配置推断的字段结构，可能不完全准确。',
    default_template: '数据来源于默认的字段模板，仅供参考。',
    no_data: '无法获取字段数据。',
  }

  ElMessageBox.alert(
    `${sourceInfo[result.source] || '未知数据来源'}\n\n详细信息：${result.message}`,
    `数据来源：${getSourceLabel(result.source)}`,
    {
      confirmButtonText: '确定',
      type: getSourceTagType(result.source) === 'success' ? 'success' : 'info',
    },
  )
}

// 显示完整返回数据
const showRawData = () => {
  if (!lastFieldResult.value || !lastFieldResult.value.raw_data) return

  const rawData = lastFieldResult.value.raw_data
  const formattedData = JSON.stringify(rawData, null, 2)

  ElMessageBox.alert(
    `<pre style="max-height: 400px; overflow-y: auto; text-align: left; font-family: 'Courier New', monospace; font-size: 12px; background: #f5f7fa; padding: 15px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;">${formattedData}</pre>`,
    `完整返回数据 (${getSourceLabel(lastFieldResult.value.source)})`,
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      customStyle: {
        width: '80%',
        maxWidth: '800px',
      },
    },
  )
}

// 显示请求信息
const showRequestInfo = () => {
  if (!lastFieldResult.value || !lastFieldResult.value.request_info) return

  const requestInfo = lastFieldResult.value.request_info
  const formattedInfo = JSON.stringify(requestInfo, null, 2)

  ElMessageBox.alert(
    `<div style="text-align: left;">
      <h4 style="margin-top: 0; color: #409eff;">实际发送的请求信息：</h4>
      <pre style="max-height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; background: #f5f7fa; padding: 15px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;">${formattedInfo}</pre>
      <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
        <strong>提示：</strong><br>
        • 如果Response为空，请检查URL和请求参数是否正确<br>
        • 确认API是否需要认证信息（API Key、Token等）<br>
        • 检查请求头和请求体格式是否符合API要求
      </div>
    </div>`,
    `请求信息分析`,
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      customStyle: {
        width: '80%',
        maxWidth: '800px',
      },
    },
  )
}

// 预览对话框相关数据已在上面声明

// 实时预览报表
const previewReport = async () => {
  if (!selectedDataSource.value) {
    ElMessage.warning('请先选择数据源')
    return
  }

  // 允许预览空模板，只要有数据源就可以
  if (Object.keys(cellBindings.value).length === 0) {
    ElMessage.info('当前没有字段绑定，将预览空模板')
  }

  loadingPreview.value = true

  try {
    // 1. 直接获取真实数据（不执行工作流）
    ElMessage.info('正在获取最新数据...')
    const dataSource = selectedDataSourceInfo.value
    if (!dataSource) {
      ElMessage.error('数据源信息不完整')
      return
    }

    console.log('开始预览，当前绑定:', cellBindings.value)

    // 强制发送真实请求获取数据
    const result = await getNodeFieldsResult(dataSource, true)
    console.log('获取数据结果:', result)

    if (!result.success || !result.raw_data) {
      ElMessage.error('获取数据失败，无法预览')
      console.error('数据获取失败:', result)
      return
    }

    console.log('原始数据:', result.raw_data)

    // 2. 生成预览数据（不修改原始单元格）
    ElMessage.info('正在生成预览...')
    const cellData = await generatePreviewData(result.raw_data)

    // 3. 显示预览对话框
    previewData.value = result.raw_data
    previewCellData.value = cellData
    previewDialogVisible.value = true

    ElMessage.success('预览生成完成！')
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败: ' + (error as Error).message)
  } finally {
    loadingPreview.value = false
  }
}

// 初始化 预览Univer
const initPreviewUniver = async () => {
  if (!previewUniverContainer.value) return

  try {
    // 初始化预览 Univer 实例
    if(!previewUniverInstance){
      const { univer, univerAPI } = initUniverConfig(previewUniverContainer.value, {
        header: false,
        toolbar: false,
        footer: false,
        contextMenu: false,
      })
      previewUniverInstance = univer
      previewUniverAPIInstance = univerAPI

    }

    // 销毁预览工作簿
    const unitId = previewUniverAPIInstance.getActiveWorkbook()?.getId();
    if(unitId) {
      previewUniverAPIInstance.disposeUnit(unitId)
    }

    // 获取编辑工作簿数据配置
    const workbook = univerAPIInstance.getActiveWorkbook()
    const worksheet = workbook.getActiveSheet()
    const sheetData = worksheet.getSheet().getSnapshot()
    const clonedSheetData = JSON.parse(JSON.stringify(sheetData));
    console.warn('预览表格样式:', {workbook, worksheet, sheetData, clonedSheetData})
    // 创建默认工作簿
    previewUniverAPIInstance.createWorkbook({
      id: 'preview-workbook',
      name: '预览',
      sheetOrder: ['sheet1'],
      sheets: {
        sheet1: {
          ...clonedSheetData,
          id: 'sheet1',
          name: '预览',
          rowCount: clonedSheetData.rowCount || 30,
          columnCount: clonedSheetData.columnCount || 15,
          cellData: clonedSheetData.cellData || {}
        }
      },
      styles: workbook.save().styles || {},
    })
  } catch (error) {
    console.error('初始化 Univer 失败:', error)
    ElMessage.error('模板设计器初始化失败')
  }
}

// 清理预览表数据
const clearPreviewUniver = () => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.clearPreviewUniver()
    }
    return
  }
  if (previewUniverAPIInstance) {
    try {
      const workbook = previewUniverAPIInstance.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      worksheet.clearContents()
      console.log('✅ 已清除预览表格内容')
    } catch (error) {
      console.error('清除预览表格失败:', error)
    }
  }
}

// 生成预览数据（不修改原始单元格）
const generatePreviewData = async (data: any): Promise<Record<string, any>> => {
  const cellData: Record<string, any> = {}

  console.log('开始生成预览数据，绑定数量:', Object.keys(cellBindings.value).length)

  // 1. 首先获取当前工作表的所有单元格数据（包括固定文本和样式）
  if (univerAPIInstance) {
    try {
      const workbook = univerAPIInstance.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const sheetData = worksheet.getSheet().getSnapshot()
      // 获取 cellData 的最大行和列索引
      let maxRow = 0
      let maxCol = 0
      if (sheetData.cellData) {
        for (const rowIndex in sheetData.cellData) {
          const rowNum = parseInt(rowIndex)
          maxRow = Math.max(maxRow, rowNum)
          const rowData = sheetData.cellData[rowIndex]
          if (rowData) {
            for (const colIndex in rowData) {
              const colNum = parseInt(colIndex)
              maxCol = Math.max(maxCol, colNum)
            }
          }
        }
      }

      // 遍历可见区域的所有单元格
      for (let row = 0; row <= maxRow; row++) {
        for (let col = 0; col <= maxCol; col++) {
          try {
            const cellAddress = getCellAddress(row, col)
            const range = worksheet.getRange(cellAddress)
            const cellValue = range.getValue()

            if (cellValue !== null && cellValue !== undefined && cellValue !== '') {
              // 检查是否是绑定字段
              const hasBinding = cellBindings.value[cellAddress]

              if (!hasBinding) {
                // 没有绑定的单元格，检查是否包含变量引用
                const processedValue = processVariableReferences(cellValue, data)
                cellData[cellAddress] = processedValue
                console.log(`✅ 处理单元格 ${cellAddress}: ${cellValue} -> ${processedValue}`)
              } else {
                // 有绑定的单元格，后面会被动态数据覆盖
                console.log(`⚠️ 发现绑定字段 ${cellAddress}: ${cellValue}, 绑定信息:`, hasBinding)
              }
            }
          } catch (error) {
            // 忽略单个单元格的错误
          }
        }
      }
    } catch (error) {
      console.warn('获取工作表数据失败:', error)
    }
  }

  // 2. 然后处理绑定的动态数据，覆盖对应的单元格
  for (const [cellAddress, binding] of Object.entries(cellBindings.value)) {
    try {
      console.log(
        `👋处理单元格 ${cellAddress}, 绑定类型: ${binding.type}, 绑定路径: ${binding.fieldPath}`,
      )

      if (binding.type === 'loop') {
        console.log(`${cellAddress} 是循环绑定`)
        // 处理循环绑定
        const loopData = await generateLoopPreviewData(data, cellAddress, binding)
        Object.assign(cellData, loopData)
      }
      else if (binding.fieldPath.includes('[]')) {
        console.log(`${cellAddress} 是数组绑定`)
        // 处理数组数据绑定
        const arrayData = await generateArrayPreviewData(data, cellAddress, binding)
        Object.assign(cellData, arrayData)
      }
      else {
        console.log(`${cellAddress} 是单值绑定`)
        // 处理单个值绑定
        const value = extractValueFromPath(data, binding.fieldPath)
        let formattedValue = formatValue(value, binding.format)

        // 应用条件表达式（如果有）
        if (binding.conditionalExpression) {
          formattedValue = applyConditionalExpressionInPreview(
            formattedValue,
            binding.conditionalExpression,
          )
          console.log(`✅ 应用条件表达式: ${value} -> ${formattedValue}`)
        }
        // 显示条件表达式（如果有）
        if (binding.displayConditions) {
          const validResult = ConditionUtils.displayConditionExpression({}, data, binding.displayConditions)
          if(!validResult){
            formattedValue = ''
          }
          console.log(`✅ 应用显示条件: ${value} -> ${formattedValue}`)
        }

        cellData[cellAddress] = formattedValue
        console.log(`✅ 预览单元格 ${cellAddress}: ${binding.fieldPath} = ${formattedValue}`)
      }
    } catch (error) {
      console.error(`❌ 生成单元格 ${cellAddress} 预览数据失败:`, error)
      cellData[cellAddress] = '错误'
    }
  }

  console.log('预览数据生成完成:', cellData)
  return cellData
}

// 生成循环预览数据
const generateLoopPreviewData = async (
  data: any,
  startCell: string,
  binding: any,
): Promise<Record<string, any>> => {
  const cellData: Record<string, any> = {}

  try {
    if (!binding.loopConfig) {
      console.error('循环绑定缺少loopConfig')
      return cellData
    }

    const { arrayField, direction, actualField } = binding.loopConfig

    // 获取循环数组数据
    console.log('开始获取循环数组数据:', { arrayField, direction, actualField, data })

    // 检查是否是多层嵌套循环（如 Response[].vals[].xxx）
    if (binding.fieldPath && binding.fieldPath.includes('[]') && arrayField.includes('.')) {
      console.log('检测到多层嵌套循环，使用特殊处理')
      return await generateNestedLoopPreviewData(data, startCell, binding)
    }

    let arrayData
    // 移除 [] 标记来获取实际的路径
    const cleanArrayField = arrayField.replace(/\[\]/g, '')
    console.log('清理后的数组字段路径:', cleanArrayField)

    if (cleanArrayField.includes('.')) {
      // 处理嵌套路径，如 "Response.vals"
      const pathParts = cleanArrayField.split('.')
      let current = data

      console.log('处理嵌套路径:', pathParts)

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        console.log(`处理路径部分 "${part}", 当前数据:`, current)

        if (current && current[part] !== undefined) {
          current = current[part]
          console.log(`获取到 "${part}":`, current)

          // 如果当前是数组且不是最后一个部分，取第一个元素继续遍历
          if (Array.isArray(current) && i < pathParts.length - 1 && current.length > 0) {
            current = current[0]
            console.log('取数组第一个元素继续:', current)
          }
        } else {
          console.warn(`路径部分 "${part}" 不存在`)
          current = null
          break
        }
      }

      arrayData = current
    } else {
      // 简单路径
      arrayData = data[cleanArrayField]
    }

    console.log('最终获取的数组数据:', arrayData)

    if (!Array.isArray(arrayData)) {
      console.warn(`循环数组字段 ${arrayField} 不是数组数据`)
      return cellData
    }

    // 解析起始单元格位置
    const startPos = parseCellAddress(startCell)

    // 根据方向循环填充数据
    for (let i = 0; i < arrayData.length; i++) {
      const item = arrayData[i]

      // 计算当前单元格位置
      let currentRow = startPos.row
      let currentCol = startPos.col

      if (direction === 'horizontal') {
        currentCol += i
      } else {
        currentRow += i
      }

      // 从数组项中提取字段值
      let fieldName = binding.fieldPath

      // 如果有actualField配置，优先使用
      if (binding.loopConfig && binding.loopConfig.actualField) {
        fieldName = binding.loopConfig.actualField
      } else if (fieldName.includes('[]')) {
        // 从完整路径中提取最后的字段名
        const parts = fieldName.split('[]')
        const lastPart = parts[parts.length - 1]
        fieldName = lastPart.startsWith('.') ? lastPart.substring(1) : lastPart
      }

      const fieldValue = item[fieldName] !== undefined ? item[fieldName] : ''
      let formattedValue = formatValue(fieldValue, binding.format)

      // 应用条件表达式（如果有）
      if (binding.conditionalExpression) {
        formattedValue = applyConditionalExpressionInPreview(
          formattedValue,
          binding.conditionalExpression,
        )
      }
      // 显示条件表达式（如果有）
      if (binding.displayConditions) {
        const validResult = ConditionUtils.displayConditionExpression({}, item, binding.displayConditions)
        if(!validResult){
          formattedValue = ''
        }
      }

      const cellAddress = getCellAddress(currentRow, currentCol)

      cellData[cellAddress] = formattedValue
      console.log(`✅ 预览循环单元格 ${cellAddress}: ${formattedValue}`)
    }

    return cellData
  } catch (error) {
    console.error('生成循环预览数据失败:', error)
    return cellData
  }
}

// 生成多层嵌套循环预览数据
const generateNestedLoopPreviewData = async (
  data: any,
  startCell: string,
  binding: any,
): Promise<Record<string, any>> => {
  const cellData: Record<string, any> = {}

  try {
    const { arrayField, direction, actualField } = binding.loopConfig
    console.log('处理多层嵌套循环预览:', { arrayField, direction, actualField })

    // 获取Response数据
    const responseData = data.Response || []
    if (!Array.isArray(responseData) || responseData.length === 0) {
      console.warn('没有Response数据')
      return cellData
    }

    // 解析嵌套路径，如 "Response.vals"
    if (arrayField.endsWith('.vals') || arrayField.includes('vals')) {
      const parts = arrayField.split('.')
      const nestedField = parts[parts.length - 1] // 获取嵌套字段名（如 vals）

      console.log(`嵌套字段: ${nestedField}`)
      console.log(`设备数量: ${responseData.length}`)

      // 解析起始单元格位置
      const startPos = parseCellAddress(startCell)

      // 为每个设备处理其嵌套数组
      for (let deviceIndex = 0; deviceIndex < responseData.length; deviceIndex++) {
        const deviceData = responseData[deviceIndex]
        if (!deviceData || typeof deviceData !== 'object' || !deviceData[nestedField]) {
          console.warn(`设备[${deviceIndex}]没有${nestedField}字段`)
          continue
        }

        const nestedArray = deviceData[nestedField]
        if (!Array.isArray(nestedArray)) {
          console.warn(`设备[${deviceIndex}]的${nestedField}不是数组`)
          continue
        }

        console.log(`设备[${deviceIndex}]有${nestedArray.length}个${nestedField}项`)

        // 为当前设备的嵌套数组生成数据
        for (let itemIndex = 0; itemIndex < nestedArray.length; itemIndex++) {
          const item = nestedArray[itemIndex]

          // 计算当前单元格位置
          let currentRow = startPos.row
          let currentCol = startPos.col

          if (direction === 'horizontal') {
            // 水平方向：每个设备一行，时间点展开为列
            currentRow += deviceIndex
            currentCol += itemIndex
          } else {
            // 垂直方向：每个时间点一行，设备展开为列
            currentRow += itemIndex
            currentCol += deviceIndex
          }

          // 获取字段值
          const fieldValue =
            item && typeof item === 'object' && actualField in item ? item[actualField] : ''
          let formattedValue = formatValue(fieldValue, binding.format)

          // 应用条件表达式（如果有）
          if (binding.conditionalExpression) {
            formattedValue = applyConditionalExpressionInPreview(
              formattedValue,
              binding.conditionalExpression,
            )
          }
          // 显示条件表达式（如果有）
          if (binding.displayConditions) {
            const validResult = ConditionUtils.displayConditionExpression(deviceData, item, binding.displayConditions)
            if(!validResult){
              formattedValue = ''
            }
          }

          const cellAddress = getCellAddress(currentRow, currentCol)

          cellData[cellAddress] = formattedValue
          console.log(
            `✅ 预览嵌套循环单元格 ${cellAddress}: 设备[${deviceIndex}]项[${itemIndex}] = ${formattedValue}`,
          )
        }
      }
    } else {
      console.warn(`不支持的嵌套路径: ${arrayField}`)
    }

    return cellData
  } catch (error) {
    console.error('生成多层嵌套循环预览数据失败:', error)
    return cellData
  }
}

// 生成数组预览数据
const generateArrayPreviewData = async (
  data: any,
  startCell: string,
  binding: any,
): Promise<Record<string, any>> => {
  const cellData: Record<string, any> = {}

  try {
    // 解析数组路径
    const arrayPath = binding.fieldPath.replace(/\[\]/g, '')
    const pathParts = arrayPath.split('.')

    // 获取数组数据
    let arrayData = data
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (pathParts[i]) {
        arrayData = arrayData[pathParts[i]]
      }
    }

    if (!Array.isArray(arrayData)) {
      console.warn(`路径 ${binding.fieldPath} 不是数组数据`)
      return cellData
    }

    const fieldName = pathParts[pathParts.length - 1]

    // 解析起始单元格位置
    const cellMatch = startCell.match(/([A-Z]+)(\d+)/)
    if (!cellMatch) return cellData

    const column = cellMatch[1]
    const startRow = parseInt(cellMatch[2])

    // 遍历数组数据，生成预览数据
    for (let i = 0; i < arrayData.length; i++) {
      const item = arrayData[i]
      const value = fieldName ? item[fieldName] : item
      const formattedValue = formatValue(value, binding.format)

      const currentRow = startRow + i
      const currentCell = `${column}${currentRow}`

      cellData[currentCell] = formattedValue
      console.log(`✅ 预览数组单元格 ${currentCell}: ${formattedValue}`)
    }

    return cellData
  } catch (error) {
    console.error('生成数组预览数据失败:', error)
    return cellData
  }
}

// 获取单元格预览值
const getCellPreviewValue = (cellAddress: string): string => {
  return previewCellData.value[cellAddress] || ''
}

// 检查单元格是否有绑定（保留用于其他用途）
const isCellBound = (cellAddress: string): boolean => {
  return cellBindings.value[cellAddress] !== undefined
}

// 预览功能现在生成预览数据而不是直接修改单元格

// 应用数据到报表（保留用于其他用途）
const applyDataToReport = async (data: any) => {
  if (!univerAPIInstance) {
    throw new Error('报表编辑器未初始化')
  }

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const worksheet = workbook.getActiveSheet()

    let updatedCells = 0

    console.log('开始应用数据到报表，绑定数量:', Object.keys(cellBindings.value).length)
    console.log('数据结构:', data)

    // 遍历所有绑定的单元格
    for (const [cellAddress, binding] of Object.entries(cellBindings.value)) {
      try {
        console.log(
          `处理单元格 ${cellAddress}, 绑定类型: ${binding.type}, 绑定路径: ${binding.fieldPath}`,
        )

        if (binding.type === 'loop') {
          console.log(`${cellAddress} 是循环绑定`)
          // 处理循环绑定
          updatedCells += await applyLoopDataToReport(data, cellAddress, binding, worksheet)
        } else if (binding.fieldPath.includes('[]')) {
          console.log(`${cellAddress} 是数组绑定`)
          // 处理数组数据绑定
          updatedCells += await applyArrayDataToReport(data, cellAddress, binding, worksheet)
        } else {
          console.log(`${cellAddress} 是单值绑定`)
          // 处理单个值绑定
          const value = extractValueFromPath(data, binding.fieldPath)
          console.log(`从路径 ${binding.fieldPath} 提取的值:`, value)

          const formattedValue = formatValue(value, binding.format)
          console.log(`格式化后的值:`, formattedValue)

          const range = worksheet.getRange(cellAddress)
          range.setValue(formattedValue)

          console.log(`✅ 更新单元格 ${cellAddress}: ${binding.fieldPath} = ${formattedValue}`)
          updatedCells++
        }
      } catch (error) {
        console.error(`❌ 更新单元格 ${cellAddress} 失败:`, error)
      }
    }

    console.log(`总共更新了 ${updatedCells} 个单元格`)
    ElMessage.success(`已更新 ${updatedCells} 个单元格`)
  } catch (error) {
    console.error('应用数据到报表失败:', error)
    throw error
  }
}

// 应用循环数据到报表
const applyLoopDataToReport = async (
  data: any,
  startCell: string,
  binding: any,
  worksheet: any,
): Promise<number> => {
  try {
    console.log('处理循环绑定:', binding)

    if (!binding.loopConfig) {
      console.error('循环绑定缺少loopConfig')
      return 0
    }

    const { arrayField, direction } = binding.loopConfig

    // 获取循环数组数据
    // 对于嵌套数组，需要先获取父级数组的第一个元素，再获取子数组
    let arrayData
    if (arrayField.includes('.')) {
      // 处理嵌套路径，如 Response.vals
      const pathParts = arrayField.split('.')
      let current = data

      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i]
        if (current[part]) {
          current = current[part]
          // 如果是数组，取第一个元素
          if (Array.isArray(current) && current.length > 0) {
            current = current[0]
          }
        }
      }

      // 获取最后一级的数组
      const lastPart = pathParts[pathParts.length - 1]
      arrayData = current[lastPart]
    } else {
      // 简单路径
      arrayData = extractValueFromPath(data, arrayField)
    }

    console.log(`从路径 ${arrayField} 获取的数组数据:`, arrayData)

    if (!Array.isArray(arrayData)) {
      console.warn(`循环数组字段 ${arrayField} 不是数组数据`)
      return 0
    }

    // 解析起始单元格位置
    const startPos = parseCellAddress(startCell)
    let updatedCells = 0

    // 根据方向循环填充数据
    for (let i = 0; i < arrayData.length; i++) {
      const item = arrayData[i]

      // 计算当前单元格位置
      let currentRow = startPos.row
      let currentCol = startPos.col

      if (direction === 'horizontal') {
        currentCol += i // 横向：列递增
      } else {
        currentRow += i // 纵向：行递增
      }

      // 从数组项中提取字段值
      // 对于循环绑定，fieldPath应该是相对于数组项的路径
      // 如果fieldPath包含数组路径，需要提取最后的字段名
      let fieldName = binding.fieldPath
      if (fieldName.includes('[]')) {
        // 提取最后一个字段名，例如从 "Response[].vals[].value" 提取 "value"
        const parts = fieldName.split('[]')
        const lastPart = parts[parts.length - 1]
        fieldName = lastPart.startsWith('.') ? lastPart.substring(1) : lastPart
      }

      const fieldValue = item[fieldName] !== undefined ? item[fieldName] : ''

      console.log(`循环项 ${i}: 从 ${binding.fieldPath} 提取值:`, fieldValue)

      const formattedValue = formatValue(fieldValue, binding.format)
      const cellAddress = getCellAddress(currentRow, currentCol)

      const range = worksheet.getRange(cellAddress)
      range.setValue(formattedValue)

      console.log(`✅ 循环更新单元格 ${cellAddress}: ${formattedValue}`)
      updatedCells++
    }

    return updatedCells
  } catch (error) {
    console.error('应用循环数据失败:', error)
    return 0
  }
}

// 应用数组数据到报表
const applyArrayDataToReport = async (
  data: any,
  startCell: string,
  binding: any,
  worksheet: any,
): Promise<number> => {
  try {
    // 解析数组路径，如 Response[].vals[].value
    const arrayPath = binding.fieldPath.replace(/\[\]/g, '')
    const pathParts = arrayPath.split('.')

    // 获取数组数据
    let arrayData = data
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (pathParts[i]) {
        arrayData = arrayData[pathParts[i]]
      }
    }

    if (!Array.isArray(arrayData)) {
      console.warn(`路径 ${binding.fieldPath} 不是数组数据`)
      return 0
    }

    const fieldName = pathParts[pathParts.length - 1]
    let updatedCells = 0

    // 解析起始单元格位置
    const cellMatch = startCell.match(/([A-Z]+)(\d+)/)
    if (!cellMatch) return 0

    const column = cellMatch[1]
    const startRow = parseInt(cellMatch[2])

    // 遍历数组数据，填充到连续的单元格中
    for (let i = 0; i < arrayData.length; i++) {
      const item = arrayData[i]
      const value = fieldName ? item[fieldName] : item
      const formattedValue = formatValue(value, binding.format)

      const currentRow = startRow + i
      const currentCell = `${column}${currentRow}`

      const range = worksheet.getRange(currentCell)
      range.setValue(formattedValue)

      console.log(`更新数组单元格 ${currentCell}: ${formattedValue}`)
      updatedCells++
    }

    return updatedCells
  } catch (error) {
    console.error('应用数组数据失败:', error)
    return 0
  }
}

// 从路径提取值
const extractValueFromPath = (data: any, path: string): any => {
  if (!data || !path) {
    console.log('extractValueFromPath: 数据或路径为空', { data, path })
    return ''
  }

  console.log(`extractValueFromPath: 开始提取路径 "${path}" 的值`)
  console.log('extractValueFromPath: 原始数据结构:', JSON.stringify(data, null, 2))

  try {
    let current = data

    // 处理数组路径，如 Response[].vals[].value
    if (path.includes('[]')) {
      console.log('extractValueFromPath: 检测到数组路径')

      // 对于数组路径，取第一个元素作为示例
      const pathParts = path.split('.')

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        console.log(`extractValueFromPath: 处理路径部分 "${part}"`)

        if (part.endsWith('[]')) {
          // 数组部分，取第一个元素
          const arrayKey = part.replace('[]', '')
          console.log(`extractValueFromPath: 数组键 "${arrayKey}"`)

          if (arrayKey && current[arrayKey]) {
            current = current[arrayKey]
            console.log(`extractValueFromPath: 获取数组 "${arrayKey}":`, current)
          }

          if (Array.isArray(current) && current.length > 0) {
            current = current[0]
            console.log('extractValueFromPath: 取数组第一个元素:', current)
          } else {
            console.log('extractValueFromPath: 数组为空或不是数组')
            return ''
          }
        } else if (part) {
          // 普通属性
          if (current && current[part] !== undefined) {
            current = current[part]
            console.log(`extractValueFromPath: 获取属性 "${part}":`, current)
          } else {
            console.log(`extractValueFromPath: 属性 "${part}" 不存在`)
            return ''
          }
        }
      }

      console.log('extractValueFromPath: 最终提取的值:', current)
      return current
    }

    // 处理普通路径，如 Code, Success, Message
    if (!path.includes('.') && !path.includes('[')) {
      const result = current[path] !== undefined ? current[path] : ''
      console.log(`extractValueFromPath: 简单路径 "${path}" 的值:`, result)
      return result
    }

    // 处理复杂路径
    const parts = path.split('.')
    console.log('extractValueFromPath: 路径分割:', parts)

    for (const part of parts) {
      if (part.includes('[') && part.includes(']')) {
        // 处理数组索引，如 vals[0]
        const [key, indexPart] = part.split('[')
        const index = parseInt(indexPart.replace(']', ''))

        if (key) {
          current = current[key]
          console.log(`extractValueFromPath: 获取键 "${key}":`, current)
        }

        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index]
          console.log(`extractValueFromPath: 获取数组索引 [${index}]:`, current)
        } else {
          console.log(`extractValueFromPath: 数组索引 [${index}] 无效`)
          return ''
        }
      } else {
        if (current && current[part] !== undefined) {
          current = current[part]
          console.log(`extractValueFromPath: 获取属性 "${part}":`, current)
        } else {
          console.log(`extractValueFromPath: 属性 "${part}" 不存在`)
          return ''
        }
      }

      if (current === undefined || current === null) {
        console.log('extractValueFromPath: 当前值为 null 或 undefined')
        return ''
      }
    }

    console.log('extractValueFromPath: 最终结果:', current)
    return current
  } catch (error) {
    console.error(`extractValueFromPath: 提取路径 ${path} 的值失败:`, error)
    return ''
  }
}

// 格式化值
const formatValue = (value: any, format?: string): string => {
  if (value === undefined || value === null) return ''

  if (!format) {
    return String(value)
  }

  try {
    // 简单的格式化处理
    if (format.includes('date') && typeof value === 'string') {
      // 日期格式化
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString()
      }
    } else if (format.includes('number') && typeof value === 'number') {
      // 数字格式化
      const precision = format.match(/\.(\d+)/)
      if (precision) {
        return value.toFixed(parseInt(precision[1]))
      }
    }

    return String(value)
  } catch (error) {
    console.warn('格式化值失败:', error)
    return String(value)
  }
}

// 在预览中应用条件表达式
const applyConditionalExpressionInPreview = (value: any, expression: string): any => {
  try {
    // 尝试解析JSON格式的多条件表达式
    try {
      const parsed = JSON.parse(expression)
      if (parsed.type === 'multi_condition' || parsed.type === 'case_when') {
        return applyMultiConditionInPreview(value, parsed)
      }
    } catch (e) {
      // 不是JSON格式，继续处理三元运算符
    }

    // 解析三元运算符格式: condition ? true_value : false_value
    const ternaryMatch = expression.match(/(.+?)\s*\?\s*(.+?)\s*:\s*(.+)/)

    if (ternaryMatch) {
      const [, condition, trueValue, falseValue] = ternaryMatch

      // 替换条件中的 value 为实际值
      const conditionWithValue = condition.replace(/\bvalue\b/g, JSON.stringify(value))

      try {
        // 使用 Function 构造器安全地评估条件
        const result = new Function('return ' + conditionWithValue)()

        // 处理返回值，去除引号
        const processValue = (val: string) => {
          val = val.trim()
          if (
            (val.startsWith('"') && val.endsWith('"')) ||
            (val.startsWith("'") && val.endsWith("'"))
          ) {
            return val.slice(1, -1)
          }
          return val
        }

        return result ? processValue(trueValue) : processValue(falseValue)
      } catch (evalError) {
        console.warn('条件表达式评估失败:', evalError)
        return value
      }
    }

    return value
  } catch (error) {
    console.warn('应用条件表达式失败:', error)
    return value
  }
}

// 在预览中应用多条件表达式
const applyMultiConditionInPreview = (value: any, parsedExpression: any): any => {
  try {
    const conditions = parsedExpression.conditions || []
    const elseValue = parsedExpression.else

    // 遍历条件，找到第一个匹配的
    for (const condition of conditions) {
      if (evaluateConditionInPreview(value, condition)) {
        return condition.result
      }
    }

    // 如果没有条件匹配，返回else值，如果else值为空则返回原始值
    return elseValue !== undefined && elseValue !== '' ? elseValue : value
  } catch (error) {
    console.warn('应用多条件表达式失败:', error)
    return value
  }
}

// 在预览中评估条件
const evaluateConditionInPreview = (value: any, condition: any): boolean => {
  try {
    const { operator, value: compareValue, minValue, maxValue } = condition

    // 处理空值检查
    if (operator === 'is_empty') {
      return value === null || value === undefined || String(value).trim() === ''
    } else if (operator === 'not_empty') {
      return value !== null && value !== undefined && String(value).trim() !== ''
    }

    // 处理区间条件
    if (operator === 'between' || operator === 'not_between') {
      if (minValue === undefined || maxValue === undefined) return false
      try {
        const valNum = parseFloat(value)
        const minNum = parseFloat(minValue)
        const maxNum = parseFloat(maxValue)
        const inRange = minNum <= valNum && valNum <= maxNum
        return operator === 'between' ? inRange : !inRange
      } catch {
        return false
      }
    }

    // 处理字符串包含
    if (operator === 'contains') {
      return String(value).includes(String(compareValue))
    } else if (operator === 'not_contains') {
      return !String(value).includes(String(compareValue))
    }

    // 处理数值和字符串比较
    if (operator === '==') {
      return compareValuesEqual(value, compareValue)
    } else if (operator === '!=') {
      return !compareValuesEqual(value, compareValue)
    } else if (['>', '<', '>=', '<='].includes(operator)) {
      return compareValuesNumeric(value, compareValue, operator)
    }

    return false
  } catch (error) {
    console.warn('条件评估失败:', error)
    return false
  }
}


// 比较两个值是否相等（预览用）
const compareValuesEqual = (value1: any, value2: any): boolean => {
  try {
    // 尝试数值比较
    const num1 = parseFloat(value1)
    const num2 = parseFloat(value2)
    if (!isNaN(num1) && !isNaN(num2)) {
      return num1 === num2
    }
  } catch {}

  // 字符串比较
  return String(value1) === String(value2)
}

// 数值比较（预览用）
const compareValuesNumeric = (value1: any, value2: any, operator: string): boolean => {
  try {
    const val1 = parseFloat(value1)
    const val2 = parseFloat(value2)

    if (isNaN(val1) || isNaN(val2)) return false

    switch (operator) {
      case '>':
        return val1 > val2
      case '<':
        return val1 < val2
      case '>=':
        return val1 >= val2
      case '<=':
        return val1 <= val2
      default:
        return false
    }
  } catch {
    return false
  }
}

// 执行节点获取数据（保留用于其他用途）
const executeNodeForData = async (dataSource: DataSource): Promise<any> => {
  try {
    console.log(`执行节点 ${dataSource.nodeId} 获取数据...`)

    // 调用后端API执行单个节点
    const response = await fetch('http://localhost:39876/api/execute-node', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        nodeId: dataSource.nodeId,
        nodes: workflowStore.nodes,
        edges: workflowStore.edges,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`执行节点失败: ${response.status} ${response.statusText}\n${errorText}`)
    }

    const result = await response.json()
    console.log('节点执行结果:', result)

    // 返回节点的输出数据
    if (result.success && result.output) {
      // 解析输出变量
      const outputVariable = dataSource.outputVariable
      if (result.output[outputVariable]) {
        let outputData = result.output[outputVariable]

        // 如果是字符串，尝试解析为JSON
        if (typeof outputData === 'string') {
          try {
            outputData = JSON.parse(outputData)
          } catch (e) {
            console.warn('输出数据不是有效的JSON，使用原始字符串')
          }
        }

        return outputData
      } else {
        console.warn(`输出中没有找到变量 ${outputVariable}`)
        return result.output
      }
    } else {
      throw new Error(result.error || '节点执行失败')
    }
  } catch (error) {
    console.error('执行节点失败:', error)
    throw error
  }
}

// 设置单元格选择监听器
const setupCellSelectionListener = () => {
  if (!univerAPIInstance) return

  try {
    // 初始选择A1
    selectedCell.value = 'A1'

    console.log('开始设置单元格选择和内容变化监听器...')

    // 1. 监听单元格选择变化
    const selectionDisposable = univerAPIInstance.addEvent(
      univerAPIInstance.Event.SelectionChanged,
      (params: any) => {
        console.log('SelectionChanged 事件触发:', params)

        try {
          const { worksheet, workbook, selections } = params

          if (selections && selections.length > 0) {
            // 获取第一个选择区域
            const firstSelection = selections[0]
            console.log('第一个选择区域:', firstSelection)

            if (firstSelection && firstSelection.range) {
              const range = firstSelection.range
              const cellAddress = getCellAddress(range.startRow, range.startColumn)
              selectedCell.value = cellAddress
              console.log('✅ 单元格选择更新为:', cellAddress)
            } else if (
              firstSelection &&
              firstSelection.startRow !== undefined &&
              firstSelection.startColumn !== undefined
            ) {
              // 备选方案：直接从选择对象获取坐标
              const cellAddress = getCellAddress(
                firstSelection.startRow,
                firstSelection.startColumn,
              )
              selectedCell.value = cellAddress
              console.log('✅ 单元格选择更新为 (备选):', cellAddress)
            }
          }
        } catch (error) {
          console.warn('处理选择变化失败:', error)
        }
      },
    )

    // 2. 监听单元格内容变化 - 使用正确的事件名称
    let contentDisposable = null

    try {
      // 根据Univer官方文档，使用 SheetValueChanged 事件
      if (univerAPIInstance.Event && univerAPIInstance.Event.SheetValueChanged) {
        console.log('监听 SheetValueChanged 事件...')
        contentDisposable = univerAPIInstance.addEvent(
          univerAPIInstance.Event.SheetValueChanged,
          (params: any) => {
            console.log('SheetValueChanged 事件触发:', params)
            handleSheetValueChangeEvent(params)
          },
        )
        console.log('✅ 成功监听 SheetValueChanged 事件')
      } else {
        console.warn('SheetValueChanged 事件不可用')
      }
    } catch (error) {
      console.warn('监听 SheetValueChanged 事件失败:', error)
    }

    // 3. 监听单元格内容编辑 -


    console.log('✅ 单元格选择和内容变化监听器设置成功')

    // 返回组合的 disposable
    return {
      dispose: () => {
        if (selectionDisposable) selectionDisposable.dispose()
        if (contentDisposable) contentDisposable.dispose()
      },
    }
  } catch (error) {
    console.error('❌ 设置单元格监听器失败:', error)
    return null
  }
}

// 存储选择监听器的 disposable
let selectionListenerDisposable: any = null

// 处理工作表值变化事件
const handleSheetValueChangeEvent = (params: any) => {
  try {
    console.log('⌛️处理 SheetValueChanged 事件:', params)
    console.log('事件参数详细信息:')
    console.log('- payload:', params.payload)
    console.log('- effectedRanges:', params.effectedRanges)

    // 实际的参数结构是 {payload, effectedRanges}
    const { payload, effectedRanges } = params

    // 尝试从payload中获取变化信息
    if (payload && payload.params) {
      console.log('从payload.params获取变化信息:', payload.params)

      const { cellValue, trigger } = payload.params

      if (trigger === 'sheet.command.paste-by-short-key'){
        const fromRange = payload.params.fromRange
        const toRange = payload.params.toRange
        // 剪切后，遍历剪切单元格区域，将原有的单元格区域上的 cellBindings 对应绑定关系，移动到新的 单元格上。并且移除原有的内容
        const rowCount = fromRange.endRow - fromRange.startRow + 1
        const colCount = fromRange.endColumn - fromRange.startColumn + 1

        // 遍历源区域每个单元格
        for (let r = 0; r < rowCount; r++) {
          for (let c = 0; c < colCount; c++) {
            // 计算源单元格位置
            const sourceRow = fromRange.startRow + r
            const sourceCol = fromRange.startColumn + c
            const sourceCell = getCellAddress(sourceRow, sourceCol)

            // 计算目标单元格位置
            const targetRow = toRange.startRow + r
            const targetCol = toRange.startColumn + c
            const targetCell = getCellAddress(targetRow, targetCol)

            // 移动绑定关系
            if (cellBindings.value[sourceCell]) {
              // 复制绑定到新位置
              cellBindings.value[targetCell] = {...cellBindings.value[sourceCell]}
              // 更新绑定的单元格地址
              fieldBindingEngine.bindField(
                targetCell,
                cellBindings.value[targetCell].fieldPath,
                cellBindings.value[targetCell].dataSource.value,
                {
                  type: cellBindings.value[targetCell].type,
                  isArray: cellBindings.value[targetCell].isArray
                },
                cellBindings.value[targetCell].format,
              )
              // 删除原位置的绑定
              delete cellBindings.value[sourceCell]
              fieldBindingEngine.removeCellBinding(sourceCell)

              console.log(`移动绑定 ${sourceCell} -> ${targetCell}`)
            }
          }
        }


      }

      if (cellValue) {
        console.log('单元格值变化信息:', cellValue)
        console.log('触发器:', trigger)

        // cellValue 应该是一个对象，包含行列信息和值
        // 遍历 cellValue 对象的所有属性
        for (const rowKey in cellValue) {
          if (cellValue.hasOwnProperty(rowKey)) {
            const rowData = cellValue[rowKey]
            const row = parseInt(rowKey)

            if (typeof rowData === 'object' && rowData !== null) {
              for (const colKey in rowData) {
                if (rowData.hasOwnProperty(colKey)) {
                  const col = parseInt(colKey)
                  const cellData = rowData[colKey]

                  // 检查 cellData 是否包含 v 字段
                  if (typeof cellData === 'object' && cellData !== null && !('v' in cellData)) {
                    console.log(`单元格 ${getCellAddress(row, col)} 没有 v 字段，跳过处理`)
                    continue;
                  }

                  const cellAddress = getCellAddress(row, col)

                  // 获取新值
                  let newValue = ''
                  if (cellData) {
                    if (typeof cellData === 'object') {
                      newValue = cellData.v || cellData.value || ''
                    } else {
                      newValue = cellData
                    }
                  }

                  console.log(`检测到单元格 ${cellAddress} 内容变化，新值: "${newValue}"`)

                  // 如果是清除操作，newValue 应该是空的
                  if (trigger === 'sheet.command.clear-selection-content') {
                    console.log(`单元格 ${cellAddress} 被清除`)
                    processCellChange(cellAddress, '', 'previous_value')
                  } else {
                    processCellChange(cellAddress, newValue, '')
                  }
                }
              }
            }
          }
        }
      }
    }
    else if (effectedRanges && effectedRanges.length > 0) {
      // 备选方案：尝试从effectedRanges获取信息
      console.log('尝试从effectedRanges获取信息')

      for (const rangeObj of effectedRanges) {
        console.log('处理受影响的范围对象:', rangeObj)

        // 尝试获取范围信息
        try {
          // 如果是Univer的Range对象，尝试获取其属性
          if (rangeObj && typeof rangeObj.getRange === 'function') {
            const range = rangeObj.getRange()
            console.log('从Range对象获取范围:', range)

            if (range) {
              const cellAddress = getCellAddress(range.startRow, range.startColumn)

              // 获取当前值
              let newValue = ''
              try {
                newValue = rangeObj.getValue() || ''
              } catch (error) {
                console.warn(`从Range对象获取值失败:`, error)
              }

              console.log(`检测到单元格 ${cellAddress} 内容变化，当前值: "${newValue}"`)
              processCellChange(cellAddress, newValue, '')
            }
          }
        } catch (error) {
          console.warn('处理Range对象失败:', error)
        }
      }
    }
    else {
      console.log('没有找到变化信息')
    }
  } catch (error) {
    console.warn('处理工作表值变化事件失败:', error)
  }

  // 如果启用了实时预览，触发预览更新
  if (realTimePreviewEnabled.value) {
    setTimeout(() => {
      generateRealTimePreview()
    }, 300) // 延迟300ms，避免频繁更新
  }
}

// 处理单个单元格变化
const processCellChange = (cellAddress: string, newValue: any, oldValue: any) => {
  console.log(`🔄 处理单元格 ${cellAddress} 变化: "${oldValue}" -> "${newValue}"`)
  console.log(`🔄 newValue类型: ${typeof newValue}, 值: "${newValue}"`)
  console.log(`🔄 当前单元格是否有绑定: ${!!cellBindings.value[cellAddress]}`)

  // 检查是否有绑定
  if (cellBindings.value[cellAddress]) {
    console.log(`🔄 单元格 ${cellAddress} 已有绑定:`, cellBindings.value[cellAddress])
    // 如果新值为空或null，自动移除绑定
    if (newValue === '' || newValue === null || newValue === undefined) {
      console.log(`🗑️ 单元格 ${cellAddress} 被清空，自动移除绑定`)
      handleCellCleared(cellAddress)
    } else {
      // 如果内容被修改为其他值，检查是否需要更新绑定
      console.log(`🔄 单元格 ${cellAddress} 内容被修改，检查绑定更新`)
      handleCellContentChanged(cellAddress, String(newValue))
    }
  } else {
    console.log(`🔄 单元格 ${cellAddress} 没有绑定`)
    // 如果没有绑定但有新内容，检查是否需要创建绑定
    if (newValue && String(newValue).trim() !== '') {
      console.log(`🆕 单元格 ${cellAddress} 添加了新内容，检查是否需要创建绑定`)
      handleCellContentAdded(cellAddress, String(newValue))
    } else {
      console.log(`🔄 单元格 ${cellAddress} 新值为空或无效，跳过处理`)
    }
  }
}

// 处理单元格被清空
const handleCellCleared = (cellAddress: string) => {
  const binding = cellBindings.value[cellAddress]

  if (!binding) return

  console.log(`处理单元格 ${cellAddress} 清空，移除绑定:`, binding)

  // 只删除当前单元格的绑定记录，不清理其他相关单元格
  delete cellBindings.value[cellAddress]
  fieldBindingEngine.removeCellBinding(cellAddress)

  console.log(`✅ 已自动移除单元格 ${cellAddress} 的绑定`)
  ElMessage.success(`已自动移除单元格 ${cellAddress} 的字段绑定`)

  // 确保单元格在清空后仍然可以编辑
  nextTick(() => {
    try {
      if (univerAPIInstance) {
        const workbook = univerAPIInstance.getActiveWorkbook()
        const worksheet = workbook.getActiveSheet()

        // 重新选择该单元格以确保编辑状态正常
        const range = worksheet.getRange(cellAddress)
        range.activate()

        console.log(`重新激活单元格 ${cellAddress} 以确保编辑状态`)
      }
    } catch (error) {
      console.warn(`重新激活单元格 ${cellAddress} 失败:`, error)
    }
  })
}

// 处理单元格内容变化
const handleCellContentChanged = (cellAddress: string, newValue: string) => {
  console.log(`🔍 检查单元格 ${cellAddress} 内容: "${newValue}"`)

  // 检查新值是否是字段路径
  const isField = isFieldPath(newValue)
  console.log(`🔍 isFieldPath("${newValue}") = ${isField}`)

  if (isField) {
    console.log(`✅ 单元格 ${cellAddress} 内容识别为字段路径: ${newValue}`)

    // 检查是否是绑定格式
    const bindingMatch = newValue.match(/^#\{(.+)\}$/)
    if (bindingMatch) {
      const innerPath = bindingMatch[1]
      console.log(`检测到绑定格式，内部路径: ${innerPath}`)

      // 检查当前绑定是否与内部路径匹配
      const currentBinding = cellBindings.value[cellAddress]
      if (currentBinding && currentBinding.fieldPath === innerPath) {
        console.log(`✅ 绑定格式匹配现有绑定，保持不变`)
        return // 不需要更新绑定
      }

      // 如果路径不匹配，更新绑定
      console.log(`🔄 绑定格式路径不匹配，更新绑定`)
      bindLoopFieldImmediate(innerPath)
    } else {
      // 直接的字段路径，更新绑定
      console.log(`🔄 直接字段路径，更新绑定`)
      bindLoopFieldImmediate(newValue)
    }
  } else {
    console.log(`❌ 单元格 ${cellAddress} 内容识别为固定文本: ${newValue}`)
    // 移除绑定，保留文本内容
    if (cellBindings.value[cellAddress]) {
      delete cellBindings.value[cellAddress]
      fieldBindingEngine.removeCellBinding(cellAddress)
      console.log(`✅ 已移除单元格 ${cellAddress} 的绑定，保留固定文本`)
    }
  }
}

// 处理单元格添加新内容
const handleCellContentAdded = (cellAddress: string, newValue: string) => {
  console.log(`🆕 单元格 ${cellAddress} 添加新内容: "${newValue}"`)

  // 更新当前选中的单元格
  selectedCell.value = cellAddress

  // 检查新值是否是字段路径
  const isField = isFieldPath(newValue)
  console.log(`🔍 isFieldPath("${newValue}") = ${isField}`)

  if (isField) {
    console.log(`✅ 单元格 ${cellAddress} 添加字段路径: ${newValue}`)

    // 检查是否是绑定格式，如果是则提取内部路径
    const bindingMatch = newValue.match(/^#\{(.+)\}$/)
    if (bindingMatch) {
      const innerPath = bindingMatch[1]
      console.log(`检测到绑定格式，提取内部路径: ${innerPath}`)
      bindLoopFieldImmediate(innerPath)
    } else {
      // 直接的字段路径
      bindLoopFieldImmediate(newValue)
    }
  } else {
    console.log(`❌ 单元格 ${cellAddress} 添加固定文本: ${newValue}`)
    // 固定文本不需要特殊处理
  }
}

// 获取字段图标
const getFieldIcon = (fieldType: string) => {
  switch (fieldType) {
    case 'string':
      return 'Document'
    case 'number':
    case 'integer':
      return 'DataBoard'
    case 'date':
      return 'Calendar'
    case 'email':
      return 'Message'
    case 'url':
      return 'Link'
    case 'array':
    case 'list':
      return 'List'
    case 'object':
      return 'Files'
    default:
      return 'Grid'
  }
}

// 选择字段
const selectField = (fieldInfo: FieldInfo) => {
  if (!selectedDataSourceInfo.value) return

  selectedFieldPath.value = fieldInfo.path
  bindingFormat.value = dataStructureAnalyzer.getDefaultFormat(fieldInfo.type)

  // 自动切换到绑定标签页
  activeTab.value = 'binding'
}


// 更新单元格值
const updateCellValue = (cell: string, value: string) => {
  if (!univerAPIInstance) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const worksheet = workbook.getActiveSheet()
    const range = worksheet.getRange(cell)
    range.setValue(value)
  } catch (error) {
    console.warn('更新单元格值失败:', error)
  }
}

// 移除绑定
const removeBinding = (cell: string) => {
  delete cellBindings.value[cell]
  fieldBindingEngine.removeCellBinding(cell)

  // 清空单元格
  updateCellValue(cell, '')

  ElMessage.success(`已移除单元格 ${cell} 的字段绑定`)
}

// 值变化处理（保留用于其他地方）
const onValueChange = (value: string) => {
  console.log('值变化:', value)
  selectedFieldPath.value = value
}

// 实时值变化处理
const onValueChangeImmediate = (value: string) => {
  console.log('实时值变化:', value)
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.onValueChangeImmediate(value)
    }
    return
  }
  if (!selectedCell.value || !value.trim()) {
    return
  }

  // 立即应用变化
  if (isFieldPath(value)) {
    // 是字段路径，执行字段绑定
    selectedFieldPath.value = value
    bindLoopFieldImmediate(value)
  } else {
    // 是文本值，直接设置到单元格
    setDirectValueImmediate(value)
  }
}

// 处理变量输入
const onVariableInput = (value: string) => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.onVariableInput(value)
    }
    return
  }
  if (!selectedCell.value) return

  // 更新单元格值
  updateCellValue(selectedCell.value, value)

  // 同步更新currentCellValue以保持一致性
  currentCellValue.value = value
}

// 扩展方向变化处理
const onDirectionChange = (direction: string) => {
  console.log('扩展方向变化:', direction)
  // 计算属性的setter会自动处理绑定更新
}

// 检查是否是字段路径
const isFieldPath = (value: string): boolean => {
  if (!value) return false

  // 检查是否是绑定格式 #{...}
  const bindingMatch = value.match(/^#\{(.+)\}$/)
  if (bindingMatch) {
    const innerPath = bindingMatch[1]
    console.log(`检测到绑定格式，提取内部路径: ${innerPath}`)
    // 递归检查内部路径
    return isFieldPath(innerPath)
  }

  // 首先检查是否在可用字段列表中
  const allFields = getAllFields()
  if (allFields.some((field) => field.path === value)) {
    console.log(`在字段列表中找到: ${value}`)
    return true
  }

  // 如果不在字段列表中，检查是否符合字段路径格式
  // 字段路径通常包含：
  // 1. 简单路径：root.fieldName
  // 2. 数组路径：root[].fieldName
  // 3. 嵌套路径：root.data[].item.name

  // 检查是否包含常见的字段路径特征
  const fieldPathPattern = /^[A-Za-z_][A-Za-z0-9_]*(\[\])?(\.[A-Za-z_][A-Za-z0-9_]*(\[\])?)*$/

  if (fieldPathPattern.test(value)) {
    console.log(`识别为字段路径格式: ${value}`)
    return true
  }

  console.log(`不识别为字段路径: ${value}`)
  return false
}

// 统一的绑定/设置值函数
const bindValue = () => {
  const inputValue = currentCellInput.value || currentCellValue.value

  if (!selectedCell.value || !inputValue.trim()) {
    ElMessage.warning('请选择单元格并输入值')
    return
  }

  if (isFieldPath(inputValue)) {
    // 是字段路径，执行字段绑定
    selectedFieldPath.value = inputValue
    bindLoopField()
  } else {
    // 是文本值，直接设置到单元格
    setDirectValue()
  }

  // 清空输入
  currentCellInput.value = ''
}

// 直接设置值到单元格
const setDirectValue = () => {
  const inputValue = currentCellInput.value || currentCellValue.value

  if (!selectedCell.value || !inputValue.trim()) {
    ElMessage.warning('请选择单元格并输入值')
    return
  }

  try {
    // 如果该单元格之前有绑定，先移除绑定
    if (cellBindings.value[selectedCell.value]) {
      console.log(
        `移除单元格 ${selectedCell.value} 的绑定:`,
        cellBindings.value[selectedCell.value],
      )
      delete cellBindings.value[selectedCell.value]
      fieldBindingEngine.removeCellBinding(selectedCell.value)
      console.log(`✅ 已移除单元格 ${selectedCell.value} 的绑定，设置为固定文本`)
      console.log('当前所有绑定:', Object.keys(cellBindings.value))
    }

    // 直接更新单元格值
    updateCellValue(selectedCell.value, inputValue)

    ElMessage.success(`已设置文本值到单元格 ${selectedCell.value}`)

    // 清空选择
    selectedFieldPath.value = ''
    currentCellInput.value = ''
  } catch (error) {
    console.error('设置单元格值失败:', error)
    ElMessage.error('设置单元格值失败')
  }
}

// 实时绑定循环字段
const bindLoopFieldImmediate = (fieldPath: string) => {
  if (!selectedCell.value || !fieldPath) {
    return
  }

  try {
    // 确保fieldPath是纯字段路径，不包含绑定格式
    let cleanFieldPath = fieldPath
    const bindingMatch = fieldPath.match(/^#\{(.+)\}$/)
    if (bindingMatch) {
      cleanFieldPath = bindingMatch[1]
      console.log(`提取纯字段路径: ${cleanFieldPath}`)
    }

    // 检查是否是数组字段，决定绑定类型
    const isArrayField = cleanFieldPath.includes('[]')

    let binding: any = {
      fieldPath: cleanFieldPath,
      type: isArrayField ? 'loop' : 'string',
      format: '',
      expression: `#{${cleanFieldPath}}`,
      dataSource: selectedDataSourceInfo.value,
      isArray: isArrayField,
      conditionalExpression: cellBindings.value[selectedCell.value]?.conditionalExpression || undefined,
      displayConditions: cellBindings.value[selectedCell.value]?.displayConditions || undefined,
    }

    // 如果是数组字段，创建循环配置
    if (isArrayField) {
      const pathParts = cleanFieldPath.split('[]')
      if (pathParts.length >= 2) {
        const arrayPath = pathParts.slice(0, -1).join('[]') + '[]'
        const actualField = pathParts[pathParts.length - 1].startsWith('.')
          ? pathParts[pathParts.length - 1].substring(1)
          : pathParts[pathParts.length - 1]

        binding.loopConfig = {
          arrayField: arrayPath.replace(/\[\]/g, ''),
          direction: 'vertical', // 默认纵向，用户可以后续修改
          actualField: actualField,
        }
      }
    }

    cellBindings.value[selectedCell.value] = binding

    // 更新单元格显示 - 使用绑定格式
    const displayText = `#{${cleanFieldPath}}`
    updateCellValue(selectedCell.value, displayText)

    console.log(`✅ 实时绑定字段 ${cleanFieldPath} 到单元格 ${selectedCell.value}`)
  } catch (error) {
    console.error('实时绑定字段失败:', error)
  }
}

// 实时设置值到单元格
const setDirectValueImmediate = (value: string) => {
  if (!selectedCell.value || !value.trim()) {
    return
  }

  try {
    // 如果该单元格之前有绑定，先移除绑定
    if (cellBindings.value[selectedCell.value]) {
      console.log(
        `移除单元格 ${selectedCell.value} 的绑定:`,
        cellBindings.value[selectedCell.value],
      )
      delete cellBindings.value[selectedCell.value]
      fieldBindingEngine.removeCellBinding(selectedCell.value)
      console.log(`✅ 已移除单元格 ${selectedCell.value} 的绑定，设置为固定文本`)
    }

    // 直接更新单元格值
    updateCellValue(selectedCell.value, value)
    console.log(`✅ 实时设置文本值到单元格 ${selectedCell.value}: ${value}`)
  } catch (error) {
    console.error('实时设置单元格值失败:', error)
  }
}

// 显示表格列配置对话框
const showTableColumnDialog = () => {
  if (!selectedArrayField.value) {
    ElMessage.warning('请先选择数组字段')
    return
  }

  // 获取数组字段的子字段作为可选列
  const arrayField = fieldTree.value.find((f) => f.path === selectedArrayField.value)
  if (arrayField && arrayField.children) {
    tableColumns.value = arrayField.children.map((child) => ({
      column: '',
      fieldPath: child.path,
      header: child.label,
      format: dataStructureAnalyzer.getDefaultFormat(child.type),
    }))
  }

  tableColumnDialogVisible.value = true
}

// 销毁 Univer
const destroyUniver = () => {
  // 清理选择监听器
  if (selectionListenerDisposable) {
    selectionListenerDisposable.dispose()
    selectionListenerDisposable = null
  }

  if (univerInstance) {
    univerInstance.dispose()
    univerInstance = null
  }
  if (univerAPIInstance) {
    univerAPIInstance.dispose()
    univerAPIInstance = null
  }
}

// 保存模板
const saveTemplate = async (saveType = 'save') => {
  // 如果未输入名称，默认设置个名字

  if (!templateName.value.trim()) {
    ElMessage.error('请输入模板名称')
    return
  }
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.saveTemplate(saveType)
    }
    return
  }


  if (!univerAPIInstance) {
    ElMessage.error('模板设计器未初始化')
    return
  }

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const templateData = workbook.save()

    // 导出绑定配置
    const bindingsData = fieldBindingEngine.exportBindings()

    console.log('当前单元格绑定:', cellBindings.value)
    console.log('导出的绑定数据:', bindingsData)

    // 调试：检查每个绑定的循环配置
    Object.entries(cellBindings.value).forEach(([cell, binding]) => {
      console.log(`绑定 ${cell}:`, {
        fieldPath: binding.fieldPath,
        type: binding.type,
        loopConfig: binding.loopConfig,
      })
    })

    // 如果没有通过fieldBindingEngine获取到绑定数据，使用当前的cellBindings
    // let finalBindingsData = bindingsData
    // if (!bindingsData.cellBindings || bindingsData.cellBindings.length === 0) {
      // 将当前的cellBindings转换为导出格式;编辑模板,将当前cellBindings重新赋值
      const finalBindingsData = {
        cellBindings: Object.entries(cellBindings.value).map(([cell, binding]) => {
          // 确保使用完整的字段路径
          let fullFieldPath = binding.fieldPath || ''

          // 如果没有完整路径，尝试从单元格内容中提取
          if (!fullFieldPath || !fullFieldPath.includes('.')) {
            try {
              if (univerAPIInstance) {
                const workbook = univerAPIInstance.getActiveWorkbook()
                const worksheet = workbook.getActiveSheet()
                const range = worksheet.getRange(cell)
                const cellValue = range.getValue()

                if (typeof cellValue === 'string') {
                  // 从 #{...} 格式中提取完整路径
                  if (cellValue.startsWith('#{') && cellValue.endsWith('}')) {
                    fullFieldPath = cellValue.replace(/^#\{|\}$/g, '')
                  }
                  // 从 ${...} 格式中提取完整路径
                  else if (cellValue.startsWith('${') && cellValue.endsWith('}')) {
                    fullFieldPath = cellValue.replace(/^\$\{|\}$/g, '')
                  }
                }
              }
            } catch (error) {
              console.warn(`无法从单元格 ${cell} 提取完整字段路径:`, error)
            }
          }

          return {
            cell,
            expression: binding.expression || `#{${fullFieldPath}}`,
            fieldPath: fullFieldPath,
            dataSourceId: binding.dataSource?.nodeId || selectedDataSource.value,
            fieldType: binding.fieldType || binding.type || 'string',
            format: binding.format || '',
            isArray: binding.isArray || false,
            conditionalExpression: binding.conditionalExpression || undefined,
            displayConditions: binding.displayConditions || undefined,
          }
        }),
        tableBindings: [],
      }
    // }

    console.log('最终绑定数据:', finalBindingsData)

    const template: ReportTemplate = {
      id: generator.generate(),
      name: templateName.value,
      description: `模板 - ${templateName.value}`,
      type: props.type || 'excel',
      data: templateData,
      bindings: finalBindingsData,
      createTime: new Date().toLocaleString(),
    }

    // 调用后端API保存模板
    await saveTemplateToServer(template, saveType)

    ElMessage.success(`模板 "${template.name}" 保存成功`)
    console.log('保存的模板:', template)

    emit('template-saved', template)
  } catch (error) {
    console.error('保存模板失败:', error)
    ElMessage.error('保存模板失败: ' + (error as Error).message)
  }
}

//word保存成功
const wordTemplateSaved = (template:ReportTemplate)=>{
  if(props.isFromNode){
    handleClose()
  }
  emit('template-saved', template)
}

// 保存模板到服务器
const saveTemplateToServer = async (template: ReportTemplate, saveType: string) => {
  try {
    // 转换绑定数据格式以匹配后端API要求
    const convertedBindings: Record<string, any> = {}

    // 如果绑定数据有cellBindings数组，转换为后端期望的格式
    if (template.bindings && template.bindings.cellBindings) {
      for (const cellBinding of template.bindings.cellBindings) {
        if (cellBinding.cell) {
          // 确保保存完整的字段路径
          let fieldPath = cellBinding.fieldPath || ''
          if (!fieldPath && cellBinding.expression) {
            // 从表达式中提取字段路径
            fieldPath = cellBinding.expression.replace(/^\$\{|\}$/g, '').replace(/^#\{|\}$/g, '')
          }

          // 如果还是没有完整路径，记录警告但仍然保存
          if (!fieldPath) {
            console.warn(`单元格 ${cellBinding.cell} 没有有效的字段路径`)
            fieldPath = cellBinding.fieldPath || 'unknown'
          }

          // 获取完整的绑定信息，包括循环配置
          const fullBinding = cellBindings.value[cellBinding.cell]
          const bindingData: any = {
            variable: fieldPath,
            type: cellBinding.fieldType || 'string',
            format: cellBinding.format || '',
          }

          // 如果是循环绑定，保存循环配置
          if (fullBinding && fullBinding.loopConfig) {
            bindingData.loopConfig = {
              arrayField: fullBinding.loopConfig.arrayField,
              direction: fullBinding.loopConfig.direction || 'vertical',
              actualField: fullBinding.loopConfig.actualField,
            }
            console.log(`保存循环配置 ${cellBinding.cell}:`, bindingData.loopConfig)
          }

          // 如果有条件表达式，保存条件表达式
          if (fullBinding && fullBinding.conditionalExpression) {
            bindingData.conditionalExpression = fullBinding.conditionalExpression
            console.log(`保存条件表达式 ${cellBinding.cell}:`, bindingData.conditionalExpression)
          }

          // 如果有条件表达式，保存条件表达式
          if (fullBinding && fullBinding.displayConditions) {
            bindingData.displayConditions = fullBinding.displayConditions
            console.log(`保存条件表达式 ${cellBinding.cell}:`, bindingData.displayConditions)
          }

          convertedBindings[cellBinding.cell] = bindingData

          console.log(`保存绑定 ${cellBinding.cell}: ${fieldPath}`)
        }
      }
    }

    // 如果绑定数据是直接的对象格式，也进行转换
    if (template.bindings && !template.bindings.cellBindings) {
      for (const [cellAddress, binding] of Object.entries(template.bindings)) {
        if (typeof binding === 'object' && binding !== null) {
          // 确保保存完整的字段路径
          let fieldPath = (binding as any).fieldPath || ''
          if (!fieldPath && (binding as any).expression) {
            // 从表达式中提取字段路径
            fieldPath = (binding as any).expression
              .replace(/^\$\{|\}$/g, '')
              .replace(/^#\{|\}$/g, '')
          }

          // 获取完整的绑定信息，包括循环配置
          const fullBinding = cellBindings.value[cellAddress]
          const bindingData: any = {
            variable: fieldPath,
            type: (binding as any).fieldType || (binding as any).type || 'string',
            format: (binding as any).format || '',
          }

          // 如果是循环绑定，保存循环配置
          if (fullBinding && fullBinding.loopConfig) {
            bindingData.loopConfig = {
              arrayField: fullBinding.loopConfig.arrayField,
              direction: fullBinding.loopConfig.direction || 'vertical',
              actualField: fullBinding.loopConfig.actualField,
            }
            console.log(`保存循环配置 ${cellAddress}:`, bindingData.loopConfig)
          }

          convertedBindings[cellAddress] = bindingData
        }
      }
    }

    console.log('原始绑定数据:', template.bindings)
    console.log('转换后的绑定数据:', convertedBindings)

    const requestData = {
      name: template.name,
      description: template.description,
      data: template.data,
      bindings: convertedBindings,
      templateType:props.type||'excel'
    }

    console.log('发送到后端的完整数据:', requestData)
    console.log('发送的绑定数据详细:', JSON.stringify(convertedBindings, null, 2))

    let response = {}
    // 保存类型不是另存为的
    if (currentTemplate.value?.id && saveType !== 'saveas') {
      response = await fetch(`http://localhost:39876/api/report-templates/${currentTemplate.value.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': utils.GetAuthorization(),
        },
        body: JSON.stringify(requestData),
      })
    } else {
      // 查找模板列表中是否存在重复模板
      const idx = templateList.value.findIndex(it => it.name === requestData.name)
      if (idx > -1) {
        throw new Error('模板名称重复')
      }

      response = await fetch('http://localhost:39876/api/report-templates/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': utils.GetAuthorization(),
        },
        body: JSON.stringify(requestData),
      })
    }

    if (!response.ok) {
      const errorText = await response.text()
      console.error('服务器响应错误:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const result = await response.json()
    console.log('模板保存成功:', result)
    currentTemplate.value = result
    return result
  } catch (error) {
    console.error('保存模板到服务器失败:', error)
    throw error
  }
}

const templateLoading = ref(false)
// 加载模板
const loadTemplate = async () => {
  try {
    templateLoading.value = true
    // 获取模板列表
    await loadTemplateList()
    templateLoading.value = false
    templateSelectVisible.value = true
  } catch (error) {
    templateLoading.value = false
    templateSelectVisible.value = true
    // console.error('加载模板列表失败:', error)
    // ElMessage.error('加载模板列表失败')
  }
}

// 加载模板列表
const loadTemplateList = async () => {
  try {
    let url = 'http://localhost:39876/api/report-templates';
    let params = {
      templateType: props.type
    };
    let queryString = new URLSearchParams(params).toString();
    let fullUrl = `${url}?${queryString}`;
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Authorization': utils.GetAuthorization(),
      }
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const templates = await response.json()
    templateList.value = templates.map((template: any) => ({
      ...template,
      createTime: new Date(template.create_time).toLocaleString(),
    }))
  } catch (error) {
    console.error('加载模板列表失败:', error)
    ElMessage.error('加载模板列表失败')
    templateList.value = []
  }
}

// 选择模板
const selectTemplate = async (template: ReportTemplate) => {


  currentTemplate.value = template
  templateName.value = template.name
  selectedCell.value = null;
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.selectTemplate(template)
    }
    return
  }

  try {
    console.log('开始加载模板:', template.name)
    console.log('模板数据:', template.data)

    // 优先使用现有实例加载数据，避免重新创建导致的白屏问题
    if (univerAPIInstance && template.data) {
      try {
        // 方法1：尝试使用现有API加载数据
        await loadTemplateDataToExistingWorkbook(template.data)
      } catch (loadError) {
        console.warn('直接加载失败:', loadError)
        ElMessage.warning('模板数据加载失败，将保持当前工作表')
      }
    }
    else if (!univerAPIInstance) {
      // 如果没有现有实例，初始化默认实例
      await initUniver()
      ElMessage.warning('Univer实例不存在，已创建新的工作表')
    }

    // 导入绑定配置
    if (template.bindings) {
      console.log('开始导入绑定配置:', template.bindings)

      // 暂时禁用单元格变化监听，避免导入时触发重复处理
      const originalListener = selectionListenerDisposable
      if (originalListener) {
        originalListener.dispose()
        selectionListenerDisposable = null
      }

      // 如果绑定数据是后端格式，需要转换
      let bindingsToImport = template.bindings

      if (template.bindings && !template.bindings.cellBindings) {
        // 转换后端格式到前端格式
        bindingsToImport = {
          cellBindings: Object.entries(template.bindings).map(([cell, binding]: [string, any]) => ({
            cell,
            expression: binding.variable ? `#{${binding.variable}}` : '',
            fieldPath: binding.variable || '',
            dataSourceId: selectedDataSource.value || '',
            fieldType: binding.type || 'string',
            format: binding.format || '',
            isArray: binding.type === 'loop' || false,
            loopConfig: binding.loopConfig || null, // 保留循环配置
            conditionalExpression: binding.conditionalExpression || undefined, // 保留条件表达式
            displayConditions: binding.displayConditions || undefined, // 保留显示条件表达式
          })),
          tableBindings: [],
        }
      }

      fieldBindingEngine.importBindings(bindingsToImport, dataSources.value)

      // 直接从导入的绑定数据构建cellBindings，确保fieldPath正确
      if (bindingsToImport.cellBindings) {
        cellBindings.value = {}
        for (const cellBinding of bindingsToImport.cellBindings) {
          if (cellBinding.cell) {
            // 获取完整的字段路径
            let fieldPath = cellBinding.fieldPath || ''

            console.log(
              `导入绑定 ${cellBinding.cell}: fieldPath = ${fieldPath}, expression = ${cellBinding.expression}`,
            )

            // 如果没有fieldPath，尝试从单元格内容中提取
            if (!fieldPath && univerAPIInstance) {
              try {
                const workbook = univerAPIInstance.getActiveWorkbook()
                const worksheet = workbook.getActiveSheet()
                const range = worksheet.getRange(cellBinding.cell)
                const cellValue = range.getValue()

                if (typeof cellValue === 'string') {
                  // 从 #{...} 格式中提取
                  if (cellValue.startsWith('#{') && cellValue.endsWith('}')) {
                    fieldPath = cellValue.replace(/^#\{|\}$/g, '')
                  }
                  // 从 ${...} 格式中提取
                  else if (cellValue.startsWith('${') && cellValue.endsWith('}')) {
                    fieldPath = cellValue.replace(/^\$\{|\}$/g, '')
                  }
                }
              } catch (error) {
                console.warn(`无法从单元格 ${cellBinding.cell} 提取字段路径:`, error)
              }
            }

            if (fieldPath) {
              const bindingType = cellBinding.fieldType || 'string'
              const binding: any = {
                fieldPath: fieldPath,
                type: bindingType,
                format: cellBinding.format || '',
                expression: cellBinding.expression || `#{${fieldPath}}`,
                dataSource: dataSources.value.find((ds) => ds.nodeId === cellBinding.dataSourceId),
                isArray: cellBinding.isArray || false,
                conditionalExpression: cellBinding.conditionalExpression || undefined,
                displayConditions: cellBinding.displayConditions || undefined,
              }

              // 如果是循环绑定，需要重建 loopConfig
              if (bindingType === 'loop' && fieldPath.includes('[]')) {
                console.log(`重建循环绑定配置 ${cellBinding.cell}: ${fieldPath}`)

                // 优先使用保存的循环配置
                if (cellBinding.loopConfig) {
                  binding.loopConfig = {
                    arrayField: cellBinding.loopConfig.arrayField,
                    direction: cellBinding.loopConfig.direction || 'vertical',
                    actualField: cellBinding.loopConfig.actualField,
                  }
                  console.log(`使用保存的循环配置:`, binding.loopConfig)
                } else {
                  // 如果没有保存的配置，从字段路径中提取
                  const pathParts = fieldPath.split('[]')
                  if (pathParts.length >= 2) {
                    // 构建数组路径：取除最后一部分外的所有部分
                    const arrayPath = pathParts.slice(0, -1).join('[]') + '[]'
                    // 获取实际字段名：最后一部分去掉开头的点
                    const actualField = pathParts[pathParts.length - 1].startsWith('.')
                      ? pathParts[pathParts.length - 1].substring(1)
                      : pathParts[pathParts.length - 1]

                    binding.loopConfig = {
                      arrayField: arrayPath.replace(/\[\]/g, ''), // 移除[]标记
                      direction: 'vertical', // 默认纵向
                      actualField: actualField,
                    }

                    console.log(
                      `从字段路径重建循环配置: arrayField=${binding.loopConfig.arrayField}, actualField=${actualField}`,
                    )
                  }
                }
              }

              cellBindings.value[cellBinding.cell] = binding
              console.log(
                `导入绑定 ${cellBinding.cell}: fieldPath = ${fieldPath}, type = ${bindingType}`,
              )
            }
          }
        }
      }

      console.log('导入的绑定配置:', bindingsToImport)
      console.log('当前单元格绑定:', cellBindings.value)

      // 重新启用单元格变化监听
      setTimeout(() => {
        if (!selectionListenerDisposable) {
          selectionListenerDisposable = setupCellSelectionListener()
          console.log('✅ 重新启用单元格监听器')
        }
      }, 100) // 延迟100ms确保导入完成
    }

    templateSelectVisible.value = false
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败: ' + (error as Error).message)

    // 如果加载失败，确保至少有一个可用的Univer实例
    if (!univerAPIInstance) {
      try {
        await initUniver()
      } catch (initError) {
        console.error('恢复默认实例失败:', initError)
      }
    }
  }
}

// 删除模板
const deleteTemplate = async (template: ReportTemplate) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '确认删除', {
      type: 'warning',
    })

    const response = await fetch(`http://localhost:39876/api/report-templates/${template.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': utils.GetAuthorization(),
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    if (template.id === currentTemplate.value?.id) {
      currentTemplate.value = {}
    }

    emit('template-deleted', template.id)
    await loadTemplateList()
    ElMessage.success('模板删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

// 导出模板
const exportTemplate = () => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.exportTemplate()
    }
    return
  }
  if (!univerAPIInstance) {
    ElMessage.error('模板设计器未初始化')
    return
  }

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const templateData = workbook.save()

    const exportData = {
      name: templateName.value,
      data: templateData,
      bindings: cellBindings.value,
      exportTime: new Date().toISOString(),
      exportType: props.type,
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `${templateName.value ? ('Excel模板-' + templateName.value) : ('Excel模板-' + Date.now())}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('模板导出成功')
  } catch (error) {
    console.error('导出模板失败:', error)
    ElMessage.error('导出模板失败')
  }
}


// 导入模板
const importTemplate = () => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.importTemplate()
    }
    return
  }
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const content = await file.text()
      const data = JSON.parse(content)

      // 验证导入数据格式
      if (!data.data || !data.bindings) {
        throw new Error('无效的模板文件格式')
      }

      // 加载模板数据
      if (!univerAPIInstance) {
        await initUniver()
      }
      await loadTemplateDataToExistingWorkbook(data.data)

      // 导入绑定配置
      if (data.bindings) {
        fieldBindingEngine.importBindings(data.bindings, dataSources.value)
        cellBindings.value = data.bindings
      }

      // 设置模板名称
      if (data.name) {
        templateName.value = data.name
      }

    } catch (error) {
      console.error('导入模板失败:', error)
      ElMessage.error('导入模板失败: ' + (error as Error).message)
    }
  }

  input.click()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 确认保存
const confirmSave = async () => {
  if(props.type==='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.confirmSave()
    }
    return
  }
  // 保存前取消编辑状态
  if(univerAPIInstance && selectedCell.value){
    const workbook = univerAPIInstance.getActiveWorkbook()
    await workbook.endEditingAsync(true)
  }
  await saveTemplate()
  if(props.isFromNode){
    handleClose()
  }
}

// 弹窗关闭后事件
const onDialogClosed = () => {
  // 关闭实时预览
  realTimePreviewEnabled.value = false
  // 清空预览数据
  realTimePreviewData.value = {}
  realTimePreviewRawData.value = null
  realTimePreviewError.value = ''
  // 清除所有绑定数据
  cellBindings.value = {}
  // 清除选中的单元格
  selectedCell.value = ''
  // 清除预览表格内容
  clearPreviewUniver()

  emit('template-closed')
}

// 添加表格列
const addTableColumn = () => {
  tableColumns.value.push({
    column: '',
    fieldPath: '',
    header: '',
    format: '',
  })
}

// 移除表格列
const removeTableColumn = (index: number) => {
  tableColumns.value.splice(index, 1)
}

// 绑定表格数据
const bindTableData = () => {
  if (!selectedArrayField.value || !tableStartCell.value || !selectedDataSourceInfo.value) {
    ElMessage.warning('请完善表格配置信息')
    return
  }

  try {
    const tableBinding = fieldBindingEngine.bindTable(
      tableStartCell.value,
      selectedArrayField.value,
      selectedDataSourceInfo.value,
      tableColumns.value,
      tableHasHeader.value,
    )

    ElMessage.success('表格数据绑定成功')
    tableColumnDialogVisible.value = false
  } catch (error) {
    console.error('绑定表格数据失败:', error)
    ElMessage.error('绑定表格数据失败')
  }
}

// 确认表格绑定
const confirmTableBinding = () => {
  bindTableData()
}

// 显示示例数据对话框
const showSampleDataDialog = () => {
  if (!selectedDataSourceInfo.value) {
    ElMessage.warning('请先选择数据源')
    return
  }

  sampleDataInput.value = ''
  sampleDataDialogVisible.value = true
}

// 验证数据格式
const validateSampleData = () => {
  try {
    const data = JSON.parse(sampleDataInput.value)
    const fields = dataStructureAnalyzer.analyzeStructure(data)

    ElMessage.success(`数据格式正确！检测到 ${fields.length} 个字段`)
    console.log('解析的字段结构:', fields)
  } catch (error) {
    ElMessage.error('JSON格式错误，请检查数据格式')
    console.error('JSON解析错误:', error)
  }
}

// 保存数据
const saveSampleData = async () => {
  if (!selectedDataSourceInfo.value) {
    ElMessage.warning('请先选择数据源')
    return
  }

  try {
    // 验证JSON格式
    const data = JSON.parse(sampleDataInput.value)

    // 保存到后端
    const response = await fetch(`/api/nodes/${selectedDataSourceInfo.value.nodeId}/sample-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    ElMessage.success('数据保存成功')
    sampleDataDialogVisible.value = false

    // 重新加载字段结构
    await loadFieldStructure()
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('JSON格式错误，请检查数据格式')
    } else {
      console.error('保存数据失败:', error)
      ElMessage.error('保存数据失败')
    }
  }
}

// 单元格配置相关函数

// 获取所有字段（扁平化）
const getAllFields = () => {
  const flattenFields = (fields: FieldInfo[]): FieldInfo[] => {
    let result: FieldInfo[] = []
    for (const field of fields) {
      result.push(field)
      if (field.children) {
        result = result.concat(flattenFields(field.children))
      }
    }
    return result
  }
  const allFields = flattenFields(fieldTree.value)

  // 检查是否包含当前绑定的路径，如果不在则动态添加
  const currentPath = selectedCell.value && cellBindings.value[selectedCell.value]?.fieldPath
  if (currentPath) {
    const hasCurrentPath = allFields.some((f) => f.path === currentPath)
    if (!hasCurrentPath) {
      allFields.push({
        path: currentPath,
        label: currentPath,
        type: 'string',
        isArray: currentPath.includes('[]'),
        children: [],
      })
    }
  }

  return allFields
}

// 注意：循环绑定时，用户需要手动选择正确的字段路径
// 例如：如果循环数组是 vals，那么绑定字段应该选择 vals[].value 这样的路径

// 数组字段变化处理
const onArrayFieldChange = () => {
  // 清空之前选择的字段
  currentCellLoop.value.field = ''
  currentCellLoop.value.nestedArrayField = ''
}

// 绑定循环字段
const bindLoopField = () => {
  if (!selectedCell.value || !selectedFieldPath.value) {
    ElMessage.warning('请选择单元格和字段')
    return
  }

  // 自动检测字段是否为数组，并设置循环配置
  const fieldPath = selectedFieldPath.value
  let arrayField = ''
  let actualField = fieldPath

  // 检测是否为数组字段路径（如 Response[].vals[].value）
  if (fieldPath.includes('[]')) {
    // 提取数组路径和字段名
    const parts = fieldPath.split('[]')
    if (parts.length >= 2) {
      // 构建完整的数组路径
      // 例如从 "Response[].vals[].value" 需要构建 "Response[].vals[]"
      let arrayPath = ''
      for (let i = 0; i < parts.length - 1; i++) {
        if (i === 0) {
          arrayPath = parts[i] + '[]'
        } else {
          arrayPath += parts[i] + '[]'
        }
      }
      arrayField = arrayPath

      // 获取字段名（去掉开头的点）
      const lastPart = parts[parts.length - 1]
      actualField = lastPart.startsWith('.') ? lastPart.substring(1) : lastPart

      console.log('解析数组字段路径:', {
        原始路径: fieldPath,
        数组字段: arrayField,
        实际字段: actualField,
        分割部分: parts,
      })
    }
  } else {
    // 如果不是数组字段，尝试从可用的数组字段中找到合适的
    const availableArrays = arrayFields.value
    if (availableArrays.length > 0) {
      // 使用第一个可用的数组字段
      arrayField = availableArrays[0].path
      actualField = fieldPath
    } else {
      // 没有数组字段，创建单值绑定
      const binding: CellBinding = {
        fieldPath: fieldPath, // 保存完整路径
        format: bindingFormat.value,
        type: 'single',
      }

      cellBindings.value[selectedCell.value] = binding
      const displayText = `#{${fieldPath}}`
      updateCellValue(selectedCell.value, displayText)

      ElMessage.success(`单元格 ${selectedCell.value} 绑定成功`)

      // 清空选择
      selectedFieldPath.value = ''
      return
    }
  }

  // 创建循环绑定 - 保存完整的字段路径
  const binding: CellBinding = {
    fieldPath: fieldPath, // 保存完整路径，不是actualField
    format: bindingFormat.value,
    type: 'loop',
    loopConfig: {
      arrayField: arrayField,
      direction: expandDirection.value,
      hasNested: false,
      nestedArrayField: '',
      nestedDirection: 'vertical',
      actualField: actualField, // 保存实际字段名用于数据提取
    },
  }

  cellBindings.value[selectedCell.value] = binding

  // 在单元格中显示循环绑定标识
  const displayText = `#{${fieldPath}}`
  updateCellValue(selectedCell.value, displayText)

  ElMessage.success(`单元格 ${selectedCell.value} 循环绑定成功`)

  // 清空选择
  selectedFieldPath.value = ''
}


// 添加拖拽相关方法
const draggingField = ref<FieldInfo | null>(null)

// 添加allow-drop方法，始终返回false阻止树内部拖拽
const allowDrop = () => {
  return false
}

// 字段拖拽开始
const onFieldDragStart = (node: any, evt: DragEvent) => {
  draggingField.value = node.data
  // evt.dataTransfer?.setData('text/plain', node.data.path)
  console.log('开始拖拽字段:', node.data)
}
const onFieldDragEnd = (draggingNode: any, dropNode: any, dropType: string) => {
  // 如果拖拽结束位置不是外部（即仍在树内部），则重置拖拽状态
  if (dropType !== 'none' && dropNode) {
    draggingField.value = null
  }
  console.log('结束拖拽字段')
}

// 多级循环表相关函数

// 获取可用的数组字段（根据循环层级）
const getArrayFieldsForLoop = (loopIndex: number) => {
  if (loopIndex === 0) {
    // 第一级循环，返回所有数组字段
    return arrayFields.value
  } else {
    // 后续循环，返回上级循环字段的子数组字段
    const parentLoop = loopTableConfig.value.loops[loopIndex - 1]
    if (parentLoop.arrayField) {
      return getFieldsForLoop(parentLoop.arrayField).filter(
        (field) => field.isArray || (field.type === 'array' || field.type === 'list'),
      )
    }
  }
  return []
}

// 获取循环字段的子字段
const getFieldsForLoop = (arrayFieldPath: string) => {
  if (!arrayFieldPath) return []

  const arrayField = fieldTree.value.find((field) => field.path === arrayFieldPath)
  if (arrayField && arrayField.children) {
    return arrayField.children
  }
  return []
}

// 添加循环层级
const addLoop = () => {
  const newId = Math.max(...loopTableConfig.value.loops.map((l) => l.id)) + 1
  loopTableConfig.value.loops.push({
    id: newId,
    arrayField: '',
    direction: 'vertical',
    fields: [
      {
        field: '',
        label: '',
        offset: { row: 0, col: 0 },
      },
    ],
  })
}

// 删除循环层级
const removeLoop = (index: number) => {
  loopTableConfig.value.loops.splice(index, 1)
}

// 添加字段
const addField = (loopIndex: number) => {
  loopTableConfig.value.loops[loopIndex].fields.push({
    field: '',
    label: '',
    offset: { row: 0, col: 0 },
  })
}

// 删除字段
const removeField = (loopIndex: number, fieldIndex: number) => {
  loopTableConfig.value.loops[loopIndex].fields.splice(fieldIndex, 1)
}

// 生成多级循环表
const generateLoopTable = async () => {
  if (!loopTableConfig.value.dataSource || !loopTableConfig.value.startCell) {
    ElMessage.warning('请配置数据源和起始单元格')
    return
  }

  try {
    // 获取真实数据
    const dataSource = selectedDataSourceInfo.value
    if (!dataSource) {
      ElMessage.error('数据源信息不完整')
      return
    }

    const result = await getNodeFieldsResult(dataSource, true)
    if (!result.success || !result.raw_data) {
      ElMessage.error('获取数据失败')
      return
    }

    // 生成循环表结构
    await generateLoopTableStructure(result.raw_data)

    ElMessage.success('多级循环表生成成功！')
  } catch (error) {
    console.error('生成多级循环表失败:', error)
    ElMessage.error('生成失败: ' + (error as Error).message)
  }
}

// 生成循环表结构
const generateLoopTableStructure = async (data: any) => {
  if (!univerAPIInstance) {
    throw new Error('报表编辑器未初始化')
  }

  const workbook = univerAPIInstance.getActiveWorkbook()
  const worksheet = workbook.getActiveSheet()

  // 解析起始位置
  const startPos = parseCellAddress(loopTableConfig.value.startCell)
  let currentRow = startPos.row
  let currentCol = startPos.col

  // 递归生成多级循环
  await generateNestedLoops(data, worksheet, 0, currentRow, currentCol)
}

// 递归生成嵌套循环
const generateNestedLoops = async (
  data: any,
  worksheet: any,
  loopLevel: number,
  startRow: number,
  startCol: number,
) => {
  if (loopLevel >= loopTableConfig.value.loops.length) {
    return { nextRow: startRow, nextCol: startCol }
  }

  const loop = loopTableConfig.value.loops[loopLevel]
  const arrayData = extractValueFromPath(data, loop.arrayField)

  if (!Array.isArray(arrayData)) {
    console.warn(`字段 ${loop.arrayField} 不是数组`)
    return { nextRow: startRow, nextCol: startCol }
  }

  let currentRow = startRow
  let currentCol = startCol
  let maxCol = startCol

  // 遍历数组数据
  for (let i = 0; i < arrayData.length; i++) {
    const item = arrayData[i]

    // 填充当前层级的字段
    for (const field of loop.fields) {
      if (!field.field) continue

      const value = extractValueFromPath(item, field.field.replace(loop.arrayField + '.', ''))
      const cellRow = currentRow + field.offset.row
      const cellCol = currentCol + field.offset.col

      const range = worksheet.getRange(getCellAddress(cellRow, cellCol))
      range.setValue(value)

      maxCol = Math.max(maxCol, cellCol)
    }

    // 递归处理下一层级
    if (loopLevel + 1 < loopTableConfig.value.loops.length) {
      const result = await generateNestedLoops(
        item,
        worksheet,
        loopLevel + 1,
        currentRow,
        currentCol + 1,
      )
      maxCol = Math.max(maxCol, result.nextCol)
    }

    // 根据扩展方向移动位置
    if (loop.direction === 'vertical') {
      currentRow++
    } else {
      currentCol = maxCol + 1
    }
  }

  return {
    nextRow: loop.direction === 'vertical' ? currentRow : startRow,
    nextCol: loop.direction === 'horizontal' ? currentCol : maxCol,
  }
}

// 解析单元格地址
const parseCellAddress = (address: string) => {
  const match = address.match(/([A-Z]+)(\d+)/)
  if (!match) throw new Error('无效的单元格地址')

  const col = columnToNumber(match[1])
  const row = parseInt(match[2]) - 1 // 转为0基索引

  return { row, col }
}

// 生成单元格地址
const getCellAddress = (row: number, col: number) => {
  return numberToColumn(col) + (row + 1)
}

// 列字母转数字
const columnToNumber = (column: string) => {
  let result = 0
  for (let i = 0; i < column.length; i++) {
    result = result * 26 + (column.charCodeAt(i) - 64)
  }
  return result - 1 // 转为0基索引
}

// 数字转列字母
const numberToColumn = (num: number) => {
  let result = ''
  num++ // 转为1基索引
  while (num > 0) {
    num--
    result = String.fromCharCode(65 + (num % 26)) + result
    num = Math.floor(num / 26)
  }
  return result
}

// 预览循环表结构
const previewLoopTable = () => {
  if (!loopTableConfig.value.dataSource) {
    ElMessage.warning('请先配置数据源')
    return
  }

  let preview = '多级循环表结构预览:\n\n'
  preview += `起始位置: ${loopTableConfig.value.startCell}\n`
  preview += `数据源: ${loopTableConfig.value.dataSource}\n\n`

  loopTableConfig.value.loops.forEach((loop, index) => {
    preview += `第${index + 1}级循环:\n`
    preview += `  数组字段: ${loop.arrayField}\n`
    preview += `  扩展方向: ${loop.direction === 'vertical' ? '纵向（向下）' : '横向（向右）'}\n`
    preview += `  字段:\n`

    loop.fields.forEach((field) => {
      if (field.field) {
        preview += `    - ${field.field} (${field.label || '未命名'}) 偏移: +${field.offset.row}行 +${field.offset.col}列\n`
      }
    })
    preview += '\n'
  })

  ElMessageBox.alert(preview, '循环表结构预览', {
    confirmButtonText: '确定',
  })
}

// 实时预览相关函数

// 切换实时预览
const onRealTimePreviewToggle = (enabled: boolean) => {
  console.log('实时预览切换:', enabled)
  if(props.type=='word'){
    if(wordDesignerRef){
      wordDesignerRef.value.onRealTimePreviewToggle(enabled)
    }
    return
  }
  if (enabled) {
    // 启用实时预览时，立即生成预览
    generateRealTimePreview()
  } else {
    // 关闭实时预览时，清空数据
    realTimePreviewData.value = {}
    realTimePreviewRawData.value = null
    realTimePreviewError.value = ''
  }
}

// 刷新实时预览
const refreshRealTimePreview = () => {
  console.log('手动刷新实时预览')
  generateRealTimePreview()
}

// 生成实时预览
const generateRealTimePreview = async () => {
  ;
  if (!selectedDataSource.value) {
    realTimePreviewError.value = '请先选择数据源'
    return
  }

  // 初始化预览univer
  await initPreviewUniver()

  loadingRealTimePreview.value = true
  realTimePreviewError.value = ''

  try {
    console.log('开始生成实时预览，当前绑定:', cellBindings.value)

    // 1. 获取真实数据
    const dataSource = selectedDataSourceInfo.value
    if (!dataSource) {
      realTimePreviewError.value = '数据源信息不完整'
      return
    }

    const result = await getNodeFieldsResult(dataSource, true)
    console.log('实时预览获取数据结果:', result)

    if (!result.success || !result.raw_data) {
      realTimePreviewError.value = '获取数据失败，无法预览'
      console.error('实时预览数据获取失败:', result)
      return
    }

    console.log('实时预览原始数据:', result.raw_data)

    // 2. 生成预览数据
    const cellData = await generatePreviewData(result.raw_data)

    // 3. 更新实时预览数据
    realTimePreviewData.value = cellData
    realTimePreviewRawData.value = result.raw_data

    // 4. 清除previewUniverAPIInstance实例中的sheet的数据，并且写入cellData
    if (previewUniverAPIInstance) {
      const workbook = previewUniverAPIInstance.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      worksheet.clearContents()
      console.warn({cellData})
      // 遍历cellData，写入单元格
      for (const cellAddress in cellData) {
        const value = cellData[cellAddress]
        const range = worksheet.getRange(cellAddress)
        if (range) {
          range.setValue(value)
        }
      }
      // 禁用当前工作表操作权限
      const previewUnitId = previewUniverAPIInstance.getActiveWorkbook()?.getId();
      const permission = previewUniverAPIInstance.getPermission();
      const workbookEditablePermission = permission.permissionPointsDefinition.WorkbookEditablePermission;
      permission.setWorkbookPermissionPoint(previewUnitId, workbookEditablePermission, false)
      // 移除权限确认框
      permission.setPermissionDialogVisible(false);
    }

    console.log('实时预览数据生成完成:', cellData)
  } catch (error) {
    console.error('生成实时预览失败:', error)
    realTimePreviewError.value = '预览失败: ' + (error as Error).message
  } finally {
    loadingRealTimePreview.value = false
  }
}

// 获取实时预览单元格值
const getRealTimePreviewValue = (cellAddress: string): string => {
  return realTimePreviewData.value[cellAddress] || ''
}

// 监听绑定变化，自动更新实时预览
watch(
  () => cellBindings.value,
  () => {
    if (realTimePreviewEnabled.value) {
      // 延迟更新，避免频繁刷新
      debugger;
      setTimeout(() => {
        if(props.type==='excel'){
          generateRealTimePreview()
        }else{
          wordDesignerRef.value.refreshRealTimePreview()
        }
      }, 500)
    }
  },
  { deep: true },
)

// 从工作流节点中查找变量
const findWorkflowVariable = (variableName: string, previewData?: any) => {
  // 遍历工作流中的所有节点，收集输出变量
  for (const node of workflowStore.nodes) {
    const nodeData = node.data
    const config = nodeData.config || {}

    // 收集用户输入组件的变量
    if (['user_input', 'user_choice', 'user_confirm'].includes(<string>nodeData.componentType)) {
      if (config.variable_name === variableName) {
        return {
          name: variableName,
          value: config.user_provided_value || config.default_value || '',
          type: getVariableType(config),
          scope: 'workflow',
          description: `来自用户输入: ${config.prompt_message || nodeData.label}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集设置变量组件的变量
    if (nodeData.componentType === 'set_variable') {
      if (config.variable_name === variableName) {
        return {
          name: variableName,
          value: config.variable_value || '',
          type: config.value_type || 'string',
          scope: config.scope || 'local',
          description: `设置的变量: ${config.description || ''}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集http请求组件的变量
    if (nodeData.componentType === 'http_request') {
      // if (config.response_content_variable === variableName) {
      //   // 直接返回预览数据
      //   let realValue = '响应内容'
      //   if (previewData) {
      //     if (typeof previewData === 'string') {
      //       realValue = previewData
      //     } else if (previewData && typeof previewData === 'object') {
      //       // 如果是对象，序列化为JSON字符串
      //       realValue = JSON.stringify(previewData)
      //     }
      //   }
      //   return {
      //     name: variableName,
      //     value: realValue,
      //     type: config.response_content_variable$$type || 'json',
      //     scope: 'workflow',
      //     description: 'HTTP响应内容',
      //     sourceNode: nodeData.label || node.id,
      //   }
      // }
      if (config.extract_variable) {
        const obj = (config.extract_variable || []).find((it: { variable: string, realkey: string, type: string, desc: string, example: object | null }) => it.variable === variableName)
        if (obj) {
          return {
            name: variableName,
            value: obj.realkey,
            type: obj.type || 'string',
            scope: 'workflow',
            description: obj.desc || 'http_request提取的变量',
            sourceNode: nodeData.label || node.id,
          }
        }
      }
    }

    // 收集天气组件的变量
    if (nodeData.componentType === 'weather_query') {
      if (config.response_content_variable === variableName) {
        return {
          name: variableName,
          value: '天气信息',
          type: config.response_content_variable$$type || 'string',
          scope: 'workflow',
          description: '天气信息',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.weather_text === variableName) {
        return {
          name: variableName,
          value: '天气数据',
          type: config.weather_text$$type || 'string',
          scope: 'workflow',
          description: '天气数据输出变量',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 网页截图组件的变量
    if (nodeData.componentType === 'take_screenshot') {
      if (config.screenshot_path===variableName) {
        return {
          name: variableName,
          value: null,
          type: config.screenshot_path$$type || 'string',
          scope: 'workflow',
          description: `网页截图响应内容的变量名`,
          sourceNode: nodeData.label || node.id,
          sourceNodeId: node.id,
        }
      }
    }

    // 收集增加、减少时间组件的变量
    if (nodeData.componentType === 'add_time') {
      if (config.response_timestamp_variable === variableName) {
        return {
          name: variableName,
          value: '时间戳',
          type: config.response_timestamp_variable$$type || 'number',
          scope: 'workflow',
          description: '增加时间后的时间戳',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_date_variable === variableName) {
        return {
          name: variableName,
          value: '日期格式',
          type: config.response_date_variable$$type || 'string',
          scope: 'workflow',
          description: '增加时间后的日期格式',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_week_variable === variableName) {
        return {
          name: variableName,
          value: '星期格式',
          type: config.response_week_variable$$type || 'string',
          scope: 'workflow',
          description: '增加时间后的星期格式',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集Python执行器组件的输出变量
    if (nodeData.componentType === 'python_execute') {
      if (config.output_variable) {
        const obj = (config.output_variable || []).find((it: { variable: string, realkey: string, type: string, desc: string, example: object | null } | string) => {
          if(typeof it === 'string') {
            return it === variableName
          }
          return it.variable === variableName
        })
        if (obj) {
          if(typeof obj === 'string') {
            return {
              name: variableName,
              value: 'Python输出结果',
              type: 'string',
              scope: 'workflow',
              description: 'Python代码执行的输出结果',
              sourceNode: nodeData.label || node.id,
              sourceNodeId: node.id,
            }
          }
          return {
            name: variableName,
            value: 'Python输出结果',
            type: obj.type || 'string',
            scope: 'workflow',
            description: obj.desc || 'http_request提取的变量',
            sourceNode: nodeData.label || node.id,
            sourceNodeId: node.id,
          }
        }
      }
      if (config.error_variable === variableName) {
        return {
          name: variableName,
          value: 'Python错误信息',
          type: 'string',
          scope: 'workflow',
          description: 'Python代码执行的错误信息',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.return_code_variable === variableName) {
        return {
          name: variableName,
          value: 0,
          type: 'number',
          scope: 'workflow',
          description: 'Python代码执行的返回码',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集Python表达式求值组件的输出变量
    if (nodeData.componentType === 'python_evaluate') {
      if (config.result_variable === variableName) {
        return {
          name: variableName,
          value: '表达式求值结果',
          type: config.result_variable$$type || 'string',
          scope: 'workflow',
          description: 'Python表达式求值的输出结果',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集JavaScript表达式求值组件的输出变量
    if (nodeData.componentType === 'javascript_execute') {
      if (config.output_variable === variableName) {
        return {
          name: variableName,
          value: '表达式求值结果',
          type: config.output_variable$$type || 'json',
          scope: 'workflow',
          description: 'JavaScript表达式求值的输出结果',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集Python模块导入组件的输出变量
    if (nodeData.componentType === 'python_import') {
      if (config.import_success_variable === variableName) {
        return {
          name: variableName,
          value: true,
          type: config.import_success_variable$$type || 'boolean',
          scope: 'workflow',
          description: 'Python模块导入的输出结果',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集发票识别组件的输出变量
    if (nodeData.componentType === 'invoice_recognition') {
      if (config.recognition_results === variableName) {
        return {
          name: variableName,
          value: true,
          type: config.recognition_result$$type || 'string',
          scope: 'workflow',
          description: '发票识别响应内容变量',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集图片识别组件的输出变量
    if (nodeData.componentType === 'img_recognition') {
      if (config.recognition_result === variableName) {
        return {
          name: variableName,
          value: true,
          type: config.recognition_result$$type || 'string',
          scope: 'workflow',
          description: '图片识别响应内容变量',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集数据库查询组件的变量
    if (nodeData.componentType === 'db_query') {
      if (config.response_content_variable === variableName) {
        return {
          name: variableName,
          value: null,
          type: config.response_content_variable$$type || 'json',
          scope: 'workflow',
          description: `数据库查询结果: ${config.query || '未指定查询语句'}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集AI分析变量
    if (nodeData.componentType === 'ai_analyze') {
      if (config.ai_analyze_response === variableName) {
        return {
          name: variableName,
          value: '响应内容',
          type: config.ai_analyze_response$$type || 'string',
          scope: 'workflow',
          description: `AI分析`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集监测数据组件的响应变量
    if (nodeData.componentType === 'monitor_data') {
      if (config.response_variable === variableName) {
        return {
          name: variableName,
          value: '监测数据响应对象',
          type: config.response_variable$$type || 'string',
          scope: 'workflow',
          description: `监测数据响应内容`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_content_variable === variableName) {
        return {
          name: variableName,
          value: '监测数据响应内容',
          type: config.response_content_variable$$type || 'string',
          scope: 'workflow',
          description: `监测数据HTTP响应内容`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_status_variable === variableName) {
        return {
          name: variableName,
          value: 200,
          type: config.response_status_variable$$type || 'number',
          scope: 'workflow',
          description: `监测数据HTTP状态码`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_headers_variable === variableName) {
        return {
          name: variableName,
          value: '监测数据响应头',
          type: config.response_headers_variable$$type || 'json',
          scope: 'workflow',
          description: `监测数据HTTP响应头`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集文本模板变量
    if (nodeData.componentType === 'text_template') {
      if (config.output_variable === variableName) {
        return {
          name: variableName,
          value: '输出变量',
          type: 'string',
          scope: 'workflow',
          description: `存储格式化后的变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集置信区间的响应变量
    if (nodeData.componentType === 'limit_interval') {
      if (config.warning_time === variableName) {
        return {
          name: variableName,
          value: '置信区间时间',
          type: config.warning_time$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间时间变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.warning_up === variableName) {
        return {
          name: variableName,
          value: '置信区间下限',
          type: config.warning_up$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间下限变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.warning_down === variableName) {
        return {
          name: variableName,
          value: '置信区间上限',
          type: config.warning_down$$type || 'list',
          scope: 'workflow',
          description: `存储置信区间上限变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.outlier_count === variableName) {
        return {
          name: variableName,
          value: '异常值数量',
          type: config.outlier_count$$type || 'number',
          scope: 'workflow',
          description: `存储异常值数量变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集数据预测的响应变量
    if (nodeData.componentType === 'data_forecast') {
      if (config.forecast_data === variableName) {
        return {
          name: variableName,
          value: '预测值',
          type: config.forecast_data$$type || 'list',
          scope: 'workflow',
          description: `存储预测值变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.forecast_img === variableName) {
        return {
          name: variableName,
          value: '数据预测曲线截图',
          type: config.forecast_img$$type || 'string',
          scope: 'workflow',
          description: `存储数据预测曲线截图变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.forecast_avg === variableName) {
        return {
          name: variableName,
          value: '预测平均值',
          type: config.forecast_avg$$type || 'number',
          scope: 'workflow',
          description: `存储预测平均值变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.forecast_sum === variableName) {
        return {
          name: variableName,
          value: '预测累计值',
          type: config.forecast_sum$$type || 'number',
          scope: 'workflow',
          description: `存储预测累计值变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.forecast_max === variableName) {
        return {
          name: variableName,
          value: '预测最大值',
          type: config.forecast_max$$type || 'number',
          scope: 'workflow',
          description: `存储预测最大值变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.forecast_min === variableName) {
        return {
          name: variableName,
          value: '预测最小值',
          type: config.forecast_min$$type || 'number',
          scope: 'workflow',
          description: `存储预测最小值变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集数据清洗的响应变量
    if (nodeData.componentType === 'data_wash') {
      if (config.wash_win === variableName) {
        return {
          name: variableName,
          value: '清洗后数据',
          type: config.wash_win$$type || 'list',
          scope: 'workflow',
          description: `存储清洗后数据变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.wash_time === variableName) {
        return {
          name: variableName,
          value: '清洗后数据时间',
          type: config.wash_time$$type || 'list',
          scope: 'workflow',
          description: `存储清洗后数据时间变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.miss_count === variableName) {
        return {
          name: variableName,
          value: '缺失数据数量',
          type: config.miss_count$$type || 'number',
          scope: 'workflow',
          description: `存储缺失数据数量变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.invalid_count === variableName) {
        return {
          name: variableName,
          value: '无效数据数量',
          type: config.invalid_count$$type || 'number',
          scope: 'workflow',
          description: `存储无效数据数量变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.valid_count === variableName) {
        return {
          name: variableName,
          value: '有效数据数量',
          type: config.valid_count$$type || 'number',
          scope: 'workflow',
          description: `存储有效数据数量变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.grade === variableName) {
        return {
          name: variableName,
          value: '历史数据评分',
          type: config.grade$$type || 'number',
          scope: 'workflow',
          description: `存储历史数据评分变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集停水关阀分析
    if (nodeData.componentType === 'water_shutoff_valve') {
      if (config.response_content_variable === variableName){
        return {
          name: variableName,
          value: '返回结果',
          type: config.response_content_variable$$type || 'json',
          scope: 'workflow',
          description: `存储关阀分析返回结果变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 可以继续添加其他组件类型的变量收集逻辑...
  }

  return null
}

// 处理变量引用
const processVariableReferences = (text: any, previewData?: any): any => {
  if (typeof text !== 'string') {
    return text
  }
  console.warn({text, previewData, localVariable: workflowStore.variables})

  // 匹配 ${variableName} 格式的变量引用
  const variablePattern = /\$\{([^}]+)\}/g

  return text.replace(variablePattern, (match, variableName) => {
    // 首先从局部变量中查找
    const localVariable = workflowStore.variables?.find((v) => v.name === variableName)

    if (localVariable) {
      // 如果是currentTime类型的变量，生成当前时间
      if (localVariable.type === 'currentTime') {
        const now = new Date()
        const format = localVariable.value || '%Y-%m-%d %H:%M:%S'
        return formatCurrentTime(now, format)
      }
      // 如果是token
      if (localVariable.name === 'token') {
        return utils.GetAuthorization()
      }

      // 其他类型的变量，返回其值
      return localVariable.value !== undefined ? String(localVariable.value) : `[${variableName}]`
    }

    // 如果在局部变量中找不到，从工作流节点中查找
    const workflowVariable = findWorkflowVariable(variableName, previewData)
    console.warn(text, {match, variableName, workflowVariable, localVariable})
    if (workflowVariable) {
      // 其他类型的变量，返回其值
      return workflowVariable.value !== undefined
        ? String(workflowVariable.value)
        : `[${variableName}]`
    }

    // 如果找不到变量，返回原始文本
    return match
  })
}

// 格式化当前时间
const formatCurrentTime = (date: Date, format: string): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('%Y', String(year))
    .replace('%m', month)
    .replace('%d', day)
    .replace('%H', hours)
    .replace('%M', minutes)
    .replace('%S', seconds)
}

// 组件卸载时清理
onBeforeUnmount(() => {
  destroyUniver()
})
</script>
<style lang="scss">
.report-designer-dialog {
  padding: 0;
  .el-dialog__header {
    padding: 0;
  }
}
.report-designer-dialog__body {
  height: calc(100% - 54px);
  padding: 12px;
  box-sizing: border-box;
  background: #f2f4f8;
}
</style>

<style scoped lang="scss">
.report-designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 54px;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  box-sizing: border-box;
  .el-dialog__title {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}
.preview-text {
  font-size: 14px;
  line-height: 32px;
  display: inline-flex;
  margin-right: 6px;
}

.back-page-icon {
  cursor: pointer;
}

.workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .workflow-name-input {
    display: none;
  }

  .title {
    color: var(--font-color-1);
    font-size: 14px;
    font-weight: bold;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .el-icon{
      margin-left: 8px;
      color: #BCBFC3;
    }
  }

  &:hover,
  &:focus-within {
    .workflow-name-input {
      display: block;
    }

    .title {
      display: none;
    }
  }
}

.workflow-name-input {
  width: 160px;
}

.report-designer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.main-content {
  display: flex;
  flex: 1;
  gap: 12px;
  flex-flow: row nowrap;
  overflow: hidden;
}

/* 左侧数据源面板 */
.left-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.datasource-panel,
.fields-panel {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
  overflow: hidden;
}

.datasource-panel {
  flex: 1;
}

.fields-panel {
  flex: 1;
  min-height: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.panel-content {
  padding: 15px;
  height: calc(100% - 49px);
  overflow-y: auto;
  box-sizing: border-box;
}
.panel-tree-content {
  padding: 12px;
  height: calc(100% - 43px);
  box-sizing: border-box;
}

.panel-tree {
  padding: 0;
  height: calc(100% - 43px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .select-group {
    padding: 12px 12px 0;
  }
}
.tree-container {
  flex: 1;
  overflow: auto;
}

/* 中间表格编辑器 */
.center-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.editor-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5.5px 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}

.editor-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.editor-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.univer-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  min-height: 300px;
  flex: 1;
}

/* 底部实时预览区域 - 上下各占一半 */
.preview-section-bottom {
  flex: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  background: white;
  overflow: hidden;
}

.preview-container-bottom {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.preview-content-bottom {
  flex: 1;
  overflow: auto;
}

.preview-grid-bottom {
  width: 100%;
  height: 100%;
}

.excel-preview-table-bottom {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-preview-table-bottom .row-header,
.excel-preview-table-bottom .col-header {
  background: #f5f7fa;
  border: 1px solid #d4d4d4;
  padding: 2px 6px;
  text-align: center;
  font-weight: 600;
  color: #666;
  font-size: 10px;
  min-width: 30px;
  width: 30px;
}

.excel-preview-table-bottom .col-header {
  height: 20px;
}

.excel-preview-table-bottom .row-header {
  width: 30px;
  height: 18px;
}

.preview-cell-bottom {
  border: 1px solid #d4d4d4;
  padding: 0;
  margin: 0;
  height: 18px;
  min-width: 60px;
  max-width: 120px;
  overflow: hidden;
}

.cell-content-bottom {
  padding: 2px 4px;
  font-size: 11px;
  line-height: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 100%;
  display: flex;
  align-items: center;
}

/* 实时预览区域 */
.preview-section {
  width: 400px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.preview-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-container-inline {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 12;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  box-sizing: border-box;
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  box-sizing: border-box;
}

.preview-error {
  color: #f56c6c;
}

.preview-content-inline {
  flex: 1;
  overflow: auto;
}

.preview-grid-inline {
  width: 100%;
  height: 100%;
}

.excel-preview-table-inline {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-preview-table-inline .row-header,
.excel-preview-table-inline .col-header {
  background: #f5f7fa;
  border: 1px solid #d4d4d4;
  padding: 2px 6px;
  text-align: center;
  font-weight: 600;
  color: #666;
  font-size: 10px;
  min-width: 30px;
  width: 30px;
}

.excel-preview-table-inline .col-header {
  height: 20px;
}

.excel-preview-table-inline .row-header {
  width: 30px;
  height: 18px;
}

.preview-cell-inline {
  border: 1px solid #d4d4d4;
  padding: 0;
  margin: 0;
  height: 18px;
  min-width: 60px;
  max-width: 120px;
  overflow: hidden;
}

.cell-content-inline {
  padding: 2px 4px;
  font-size: 11px;
  line-height: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 100%;
  display: flex;
  align-items: center;
}

/* 右侧单元格配置面板 */
.right-panel {
  width: 320px;
  flex-shrink: 0;
}

.cell-config-panel {
  height: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.selected-cell {
  font-size: 12px;
  color: #409eff;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: 600;
}

.cell-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-selection {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.no-selection .el-icon {
  font-size: 32px;
  margin-bottom: 15px;
  color: #c0c4cc;
}

.cell-config {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.config-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.config-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
}

.config-section {
  height: 100%;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.select-group {
  display: flex;
  &:not(.no-select) :deep(.el-select .el-select__wrapper) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    &:hover,
    &:focus-within {
      z-index: 2;
    }
  }
  .el-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: -1px;
    z-index: 1;
  }
}

.form-actions {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;
}

.nested-config {
  margin-top: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.current-binding {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  border-top: 1px solid #e4e7ed;
}

.current-binding h6 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #606266;
  font-weight: 600;
}

.current-binding .binding-info p {
  margin: 5px 0;
  font-size: 12px;
  color: #303133;
}

.loop-binding-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.loop-binding-info p {
  margin: 3px 0;
  font-size: 11px;
  color: #606266;
}

.binding-tabs {
  height: 100%;
}

.binding-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
  padding: 15px;
}

.datasource-section,
.fields-section,
.binding-section,
.table-section {
  height: 100%;
}

.datasource-section h5,
.fields-section h5,
.binding-section h5,
.table-section h5 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.binding-section h6 {
  margin: 15px 0 10px 0;
  color: #606266;
  font-size: 12px;
  font-weight: 500;
}

.datasource-info {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.no-datasource,
.loading-fields,
.no-fields {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-size: 14px;
}

.no-fields {
  border: 1px dashed #e4e7ed;
  border-radius: 4px;
  margin: 10px 0;
}

.no-fields .el-icon {
  font-size: 24px;
  color: #f56c6c;
  margin-bottom: 10px;
}

.no-fields p {
  margin: 5px 0;
}

.no-fields .hint {
  font-size: 12px;
  color: #c0c4cc;
  margin-bottom: 15px;
}

.no-fields-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.sample-data-dialog {
  padding: 10px 0;
}

.sample-data-dialog p {
  margin-bottom: 15px;
  color: #303133;
}

.sample-data-input {
  margin-bottom: 15px;
}

.sample-data-tips {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.sample-data-tips p {
  margin: 0 0 10px 0;
  color: #303133;
  font-weight: 600;
}

.sample-data-tips ul {
  margin: 0;
  padding-left: 20px;
}

.sample-data-tips li {
  margin-bottom: 5px;
  color: #606266;
  font-size: 13px;
}

.fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.fields-actions {
  display: flex;
  gap: 8px;
}

.fields-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field-source-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 12px;
}

.source-message {
  color: #606266;
  flex: 1;
}

.field-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.field-node:hover {
  background-color: #f5f7fa;
}

.field-icon {
  font-size: 14px;
  color: #409eff;
}

.field-label {
  flex: 1;
  font-size: 13px;
  color: #303133;
}

.field-type {
  font-size: 11px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 2px;
}

.field-sample {
  font-size: 11px;
  color: #67c23a;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field-array-info {
  font-size: 11px;
  color: #e6a23c;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: 600;
}

.bind-btn {
  font-size: 11px;
  padding: 2px 8px;
}

.binding-controls {
  margin-bottom: 20px;
}

.bound-fields {
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
}

.binding-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.binding-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cell-address {
  font-weight: 600;
  color: #303133;
  font-size: 12px;
}

.field-path {
  color: #606266;
  font-size: 11px;
}

.format {
  color: #909399;
  font-size: 10px;
}

.table-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.table-config {
  padding: 15px 0;
}

.config-header {
  margin-bottom: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.column-config-table {
  margin-bottom: 15px;
}

.table-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.dialog-footer {
  text-align: right;
}

/* 多级循环表样式 */
.loop-table-config {
  padding: 10px 0;
}

.config-form {
  margin-bottom: 20px;
}

.loop-levels {
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
}

.loop-level {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 15px;
  overflow: hidden;
}

.loop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.loop-title {
  font-weight: 600;
  color: #303133;
  font-size: 13px;
}

.loop-config {
  padding: 15px;
}

.fields-config {
  margin-top: 15px;
  border-top: 1px solid #f0f2f5;
  padding-top: 15px;
}

.fields-config h7 {
  display: block;
  margin-bottom: 10px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.field-config {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #f0f2f5;
  border-radius: 4px;
  background: #fafafa;
}

.field-row {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.field-row .el-select,
.field-row .el-input {
  flex: 1;
}

.field-offset {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
}

.field-offset label {
  color: #909399;
  margin: 0;
}

.field-offset .el-input-number {
  width: 80px;
}

.loop-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

/* 预览对话框样式 */
.preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-header {
}

.preview-content {
  max-height: 500px;
  overflow: auto;
  border: 2px solid #d4d4d4;
  border-radius: 4px;
  background: white;
}

.excel-preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-preview-table th.row-header,
.excel-preview-table td.row-header {
  background: #f2f2f2;
  border: 1px solid #d4d4d4;
  padding: 4px 8px;
  text-align: center;
  font-weight: 600;
  color: #666;
  min-width: 40px;
  width: 40px;
  font-size: 11px;
}

.excel-preview-table th.col-header {
  background: #f2f2f2;
  border: 1px solid #d4d4d4;
  padding: 4px 8px;
  text-align: center;
  font-weight: 600;
  color: #666;
  min-width: 80px;
  font-size: 11px;
}

.excel-preview-table td.preview-cell {
  border: 1px solid #d4d4d4;
  padding: 4px 8px;
  min-width: 80px;
  height: 24px;
  background: white;
  text-align: left;
  vertical-align: middle;
}

/* 移除特殊样式，保持与原始模板一致 */

.cell-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表单操作按钮样式 */
.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

/* 当前绑定信息显示样式 */
.current-binding-info {
  margin-top: 12px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.binding-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.binding-label {
  color: #606266;
  font-weight: 500;
}

.binding-value {
  color: #409eff;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.binding-direction {
  color: #909399;
  font-style: italic;
}

/* 条件配置样式 */
.conditional-config {
}

.conditional-settings {
  margin-top: 10px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
  position: relative;
  min-height: 32px;
  &:last-of-type {
    margin-bottom: 0;
  }
}

.condition-label {
  font-size: 12px;
  color: #333333;
  white-space: nowrap;
}

.condition-preview {
  margin-top: 8px;
  padding: 6px;
  background: #ecf5ff;
  border-radius: 3px;
  border-left: 3px solid #409eff;
}

/* 区间输入样式 */
.range-input {
  display: flex;
  align-items: center;
  gap: 4px;
}

.range-separator {
  font-size: 12px;
  color: #909399;
  margin: 0 2px;
}

/* 条件配置样式优化 */
.label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.label-with-button label {
  margin-bottom: 0;
}

.condition-item {
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f1f2f4;
  border-radius: 4px;
  background: #f1f2f4;
}

.condition-content {
  width: 100%;
}

.when-condition,
.else-condition {
  width: 100%;
}

.condition-prefix {
  font-size: 12px;
  color: #333333;
  margin-right: 4px;
}

.block {
  display: block;
}

.condition-then {
  font-size: 12px;
  color: #333333;
  margin: 0 4px;
}

.preview-title {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
}

.preview-rules {
  font-size: 11px;
  color: #333333;
}

.preview-rule {
  margin-bottom: 2px;
  line-height: 1.4;
}

.template-container{
  max-height: 350px;
  height: auto;
  overflow: hidden;
  .template-item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 4px;
    padding: 8px;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    transition: background 0.3s ease;
    &:hover{
      background: #eaeaea;
      .template-item-action{
        visibility: visible;
      }
    }
    .template-item-name{
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .template-item-time{
      color: #666666;
      font-size: 12px;
      margin-top: 3px;
    }
    .template-item-action{
      position: absolute;
      right: 10px;
      bottom: 8px;
      visibility: hidden;
    }
  }
}
</style>
