/**
 * HTTP/API组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const weatherQuerySchema: ComponentConfigSchema = {
  componentType: 'weather_query',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'response',
      label: '指令输出',
      // description: '要发送的数据配置',
      icon: 'Document',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      // description: 'HTTP请求头的配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    }
  ],

  fields: {
    city: {
      type: 'string',
      label: '城市',
      description: '',
      placeholder: '',
      group: 'input',
      required:true,
      order: 1,
      variableSupport: true
    },
    days: {
      type: 'select',
      label: '选择日期',
      description: '变量值的数类型',
      group: 'input',
      order: 3,
      required:true,
      default: 'string',
      options: [
        { label: '今天', value: 0, description: '' },
        { label: '明天', value: 1, description: '' },
        { label: '未来3日', value: 3, description: '' },
        { label: '未来7日', value: 7, description: '' },
        { label: '未来15日', value: 15, description: '' },
      ],
    },
    weather_type: {
      type: 'multiselect',
      label: '选择天气数据',
      placeholder: '请选择需要输出的天气数据项',
      description: '',
      group: 'input',
      order: 4,
      multiple: true,
      options: [
        { label: '日期', value: 'date', description: '显示具体日期，如2025-07-22', group: '基础信息' },
        { label: '星期', value: 'week', description: '显示星期几，如星期二', group: '基础信息' },
        { label: '白天天气', value: 'text_day', description: '白天天气描述，如多云', group: '天气状况' },
        { label: '夜间天气', value: 'text_night', description: '夜间天气描述，如多云', group: '天气状况' },
        { label: '降水概率', value: 'pop', description: '降水概率，单位%', group: '天气状况' },
        { label: '紫外线指数', value: 'uv', description: '紫外线指数等级', group: '天气状况' },
        { label: '最高温度', value: 'high', description: '当日最高温度', group: '温度信息' },
        { label: '最低温度', value: 'low', description: '当日最低温度', group: '温度信息' },
        { label: '白天风向', value: 'wd_day', description: '白天风向，如南风', group: '风力信息' },
        { label: '白天风力', value: 'wc_day', description: '白天风力等级，如<3级', group: '风力信息' },
        { label: '夜间风向', value: 'wd_night', description: '夜间风向，如东南风', group: '风力信息' },
        { label: '夜间风力', value: 'wc_night', description: '夜间风力等级，如<3级', group: '风力信息' },
        { label: '白天风向角度', value: 'wa_day', description: '白天风向角度，单位度', group: '风力信息' },
        { label: '夜间风向角度', value: 'wa_night', description: '夜间风向角度，单位度', group: '风力信息' },
        { label: '白天风速', value: 'ws_day', description: '白天风速，单位m/s', group: '风力信息' },
        { label: '夜间风速', value: 'ws_night', description: '夜间风速，单位m/s', group: '风力信息' },
        { label: '最大湿度', value: 'maxrh', description: '当日最大相对湿度，单位%', group: '湿度信息' },
        { label: '最小湿度', value: 'minrh', description: '当日最小相对湿度，单位%', group: '湿度信息' },
        { label: '大气压强', value: 'pressure', description: '大气压强，单位百帕', group: '大气信息' },
        { label: '能见度', value: 'vis', description: '能见度，单位米', group: '大气信息' },
        { label: '白天云量', value: 'clouds_day', description: '白天云量，单位%', group: '大气信息' },
        { label: '夜间云量', value: 'clouds_night', description: '夜间云量，单位%', group: '大气信息' },
      ],
    },
    weather_text: {
      type: 'string',
      label: '天气数据输出变量',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'weather_text',
      group: 'response',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    response_content_variable: {
      type: 'string',
      label: '响应内容变量名',
      // description: '存储响应内容的变量名（可选）',
      placeholder: 'response_content',
      group: 'response',
      order: 2,
      required: true,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' }
      ]
    }
  },

  examples: [
  ],
}
