import json
from typing import Dict, Optional

from actions.data_process import group
from core.executor import ExecutionContext, ActionContext


@group.action(
    type="variable_assignment",
    label="变量赋值",
    description="变量赋值",
    category="data_pocess",
    icon="data_pocess",
    config_schema={
        "variables": {"type": "string", "required": True},
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
)
async def variable_assignment(context: ActionContext, config: Dict):
    variables = config.get("variables", "[]")
    await context.send_data({"ss": "s"})
    l = json.loads(variables)

    for variable in l:
        field = variable.get("field", "")
        value = variable.get("value", "")
        context.set_variable(field, value)
