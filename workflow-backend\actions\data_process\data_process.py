import json
from typing import Dict, Optional

from actions.data_process import data_process_group
from core.executor import ExecutionContext


@data_process_group.action(
    type="variable_assignment",
    label="变量赋值",
    description="变量赋值",
    category="data_pocess",
    icon="data_pocess",
    config_schema={
        "variables": {"type": "string", "required": True},
        "retry_times": {"type": "number", "default": 0},
        "retry_delay": {"type": "number", "default": 2},
        "error_handle": {"type": "string", "default": "stop"},
    },
)
def variable_assignment(context: ExecutionContext, config: Dict, options: Dict):
    variables = config.get("variables", "[]")

    l = json.loads(variables)

    for variable in l:
        field = variable.get("field", "")
        value = variable.get("value", "")
        context.set_variable(field, value)
