
import json
import os
from docx import Document
import re
from robot.libraries.BuiltIn import BuiltIn
import requests
from models.workflow import CustomKeyWordResult


def fix_query():
    # 获取query 变量
    query = BuiltIn().get_variable_value("${query}")
    if isinstance(query, list):

        text = "\n".join(str(line).strip() for line in query)
        BuiltIn().set_suite_variable("${query}", text)

def re_path():
    save_path = BuiltIn().get_variable_value("${save_path}")
    filename = BuiltIn().get_variable_value("${filename}")
    save_path = save_path.replace("\\", "/")
    shot_path = os.path.join(save_path, filename).replace("\\", "/")
    BuiltIn().set_suite_variable("${save_path}", save_path)
    BuiltIn().set_suite_variable("${shot_path}", shot_path)


def table_to_json(table):
    data = []

    # 将表格每一行转换为字典
    for i in range(len(table)):
        row_dict = table.get_row(i, as_list=False)
        data.append(row_dict)

    # 获取变量名，例如 "RecognitionResults"
    variable_name = BuiltIn().get_variable_value("${response_content_variable}")

    # 包装为对象形式：{ "RecognitionResults": [...] }
    wrapped = {variable_name: data}

    # 转为 JSON 字符串
    result = json.dumps(wrapped, ensure_ascii=False, indent=2, default=str)

    # 设置变量值：${RecognitionResults} = '{"RecognitionResults": [...]}'
    BuiltIn().set_suite_variable(f"${{{variable_name}}}", result)

    return result

def sanitize_filename(name: str) -> str:
    return re.sub(r'[\\/:*?"<>|]', '', name)

def create_word_document(content_file, output_path, title="工作报告"):
    """
    根据临时文件内容创建Word文档

    Args:
        content_file: 包含内容的临时文件路径
        output_path: 输出的Word文档路径
        title: 文档标题
    Returns:
        tuple: (success: bool, message: str)
    """
    try:
        if not output_path.lower().endswith(('.doc', '.docx')):
            output_path = output_path + '.docx'
        # 读取临时文件内容
        with open(content_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 创建Word文档
        doc = Document()
        doc.add_heading(title, level=0)
        doc.add_paragraph(content)
        doc.save(output_path)

        return True, f"Word文档已创建: {output_path}"
    except Exception as e:
        return False, f"Word文档创建失败: {str(e)}"

def decode_js_output(raw_output: str) -> str | None:
    """尝试对 JS 执行结果进行编码转换，失败则返回 None。"""
    try:
        return (
            raw_output.encode()
            .decode('unicode_escape')
            .encode('latin-1')
            .decode('utf-8')
        )
    except (UnicodeEncodeError, UnicodeDecodeError, AttributeError):
        return None



def assign_variables(json_str):
    """
    接收一个包含 field 和 value 的列表，并将它们赋值给 Robot Framework 变量。

    示例输入:
        [{"field": "test1", "value": "123"}, {"field": "test2", "value": "321"}]
    """
    variables = json.loads(json_str)
    for item in variables:
        var_name = item['field']
        var_value = item['value']
        BuiltIn().set_suite_variable(f"${{{var_name}}}", var_value)


# 转换任何形式的响应结果为markdown格式
def response_to_markdown(response):
    # 如果响应结果是一个字符串、数字、布尔值、则直接返回
    try:
        if isinstance(response, (str, int, float, bool)):
            return str(response)
        #取响应内容,结果的key可能是response或Response或data或Data
        if 'response' in response:
            content = response["response"]
        elif 'Response' in response:
            content = response["Response"]
        elif 'data' in response:
            content = response["data"]
        elif 'Data' in response:
            content = response["Data"]
        else:
            return "无法识别的响应结果"
        items = []
        # 如果content是一个字典，放到一个空数组中
        if isinstance(content, dict):
            items.append(content)
        elif isinstance(content, list):
            items = content
        else:
            return "无法识别的响应结果"

        markdown_table = json_to_markdown_table(items)
        return markdown_table
    except:
        return "无法识别的响应结果"



def json_to_markdown_table(json_data):
    if not json_data:
        return ""
    markdown_list = []
    # 添加数据行
    for data in json_data:
        markdown = "```markdown\n"
        # 定义一个markdown表格的头
        markdown += "| 名称 | 值 |\n| --- | --- |\n"

        items = [{"key": k, "value": v} for k, v in data.items()]
        for item in items:
            if not isinstance(item['value'], (dict,list)):

                markdown += f"| {item['key']} | {item['value']} |\n"
        markdown += "```"
        markdown_list.append(markdown)


    return markdown_list


def response_to_markdownv2(response):
    # 如果响应结果是一个字符串、数字、布尔值、则直接返回
    if isinstance(response, (str, int, float, bool)):
        return str(response)
    #取响应内容,结果的key可能是response或Response或data或Data
    if 'response' in response:
        content = response["response"]
    elif 'Response' in response:
        content = response["Response"]
    elif 'data' in response:
        content = response["data"]
    elif 'Data' in response:
        content = response["Data"]
    else:
        return "无法识别的响应结果"
    items = []
    # 如果content是一个字典，放到一个空数组中,对报警查询 返回结果筛选rows
    if isinstance(content, dict):
        if "rows" in content:
            if isinstance(content["rows"], (list, tuple)):
                items.extend(content["rows"])
        else:
            items.append(content)
    elif isinstance(content, list):
        items = content
    else:
        return "无法识别的响应结果"

    markdown_table = json_to_markdown_tablev2(items)
    return markdown_table


def json_to_markdown_tablev2(json_data):
    if json_data:
        markdown_list = []
        for data in json_data:
            markdown = "```markdown\n"
            # 定义一个markdown表格的头
            markdown += "| 名称 | 值 |\n| --- | --- |\n"

            for key, value in data.items():
                if isinstance(value, dict):
                    pass
                    # for sub_key, sub_value in data[key].items():
                    #     if not isinstance(sub_value, (dict, list)):
                    #         markdown += f"| {sub_key} | {sub_value} |\n"
                elif isinstance(value, (list, tuple)):
                    if key in ["rows"]:
                        dict_items = list(filter(lambda x: isinstance(x, dict), value))
                        for dict_item in dict_items:
                            for sub_key, sub_value in dict_item.items():
                                if not isinstance(sub_value, (dict, list)):
                                    markdown += f"| {sub_key} | {sub_value} |\n"
                else:
                    markdown += f"| {key} | {value} |\n"
            markdown += "```"
            markdown_list.append(markdown)
        return markdown_list
    return ''


def send_http_request(
                          url_method,
                          url,
                          json_data=None,
                          headers=None,
                          error_handle=False,
                          response_content_variable='http_response',
                          timeout=30,
                          verify_ssl=True):
    """
    发送 HTTP 请求 支持不同请求方式
    """
    response = None
    method = url_method.strip().upper()
    if method not in ["POST", "GET"]:
        raise ValueError(f"暂不支持的请求方法: {method}")
    try:
        headers_dict = headers
        payload = json_data

        if method == "POST":
            response = requests.post(
                url,
                json=payload,
                headers=headers_dict,
                timeout=timeout,
                verify=verify_ssl
            )
        elif method == "GET":
            response = requests.get(
                url,
                params=payload,
                headers=headers_dict,
                timeout=timeout,
                verify=verify_ssl
            )

        # 判断响应是否为 JSON
        content_type = response.headers.get("Content-Type", "")
        is_json = "application/json" in content_type.lower()


        if response.status_code == 200:
            BuiltIn().set_suite_variable("${is_dict}", is_json)
            if is_json:
                data = response.json()
                json_str = json.dumps(data, indent=2, ensure_ascii=False)
                BuiltIn().set_suite_variable(f"${{{response_content_variable}}}", json_str)
                return data
            else:
                BuiltIn().set_suite_variable(f"${{{response_content_variable}}}", response.text)
                # 设置固定
                BuiltIn().set_suite_variable("${response_text}", response.text)
                return response.text
        else:
            BuiltIn().set_suite_variable(f"${{{response_content_variable}}}", f"请求失败: 状态码 {response.status_code}, 响应内容: {response.text}")
            CustomKeyWordResult.stdout = ""
            CustomKeyWordResult.stderr = "请求失败"
            CustomKeyWordResult.rc = response.status_code
            return CustomKeyWordResult


    except requests.exceptions.RequestException as e:
        CustomKeyWordResult.stdout = ""
        CustomKeyWordResult.stderr = "请求失败"
        CustomKeyWordResult.rc = 0
        return CustomKeyWordResult
