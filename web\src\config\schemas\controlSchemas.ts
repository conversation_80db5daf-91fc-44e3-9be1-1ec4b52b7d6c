/**
 * 控制流组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const setVariableSchema: ComponentConfigSchema = {
  componentType: 'set_variable',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '变量设置的基本参数',
      icon: 'Setting',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '变量处理的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    variable_name: {
      type: 'string',
      label: '变量名称',
      description: '要设置的变量名称',
      placeholder: 'my_variable',
      required: true,
      group: 'basic',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'required',
          message: '变量名称不能为空',
        },
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    value: {
      type: 'textarea',
      label: '变量值',
      description: '要设置的变量值',
      placeholder: '输入变量值',
      required: true,
      group: 'basic',
      order: 2,
      rows: 3,
    },

    value_type: {
      type: 'select',
      label: '值类型',
      description: '变量值的数据类型',
      group: 'basic',
      order: 3,
      default: 'string',
      options: [
        { label: '字符串', value: 'string', description: '文本类型' },
        { label: '数字', value: 'number', description: '数值类型' },
        { label: '布尔值', value: 'boolean', description: 'true/false' },
        { label: 'JSON', value: 'json', description: 'JSON对象' },
        { label: '列表', value: 'list', description: '数组类型' },
      ],
    },

    scope: {
      type: 'select',
      label: '变量作用域',
      description: '变量的作用范围',
      group: 'advanced',
      order: 1,
      default: 'local',
      options: [
        { label: '局部变量', value: 'local', description: '仅在当前工作流中有效' },
        { label: '全局变量', value: 'global', description: '在整个执行过程中有效' },
        { label: '环境变量', value: 'environment', description: '设置为系统环境变量' },
      ],
    },

    overwrite: {
      type: 'boolean',
      label: '覆盖已存在的变量',
      description: '如果变量已存在，是否覆盖其值',
      group: 'advanced',
      order: 2,
      default: true,
    },

    log_value: {
      type: 'boolean',
      label: '记录变量值',
      description: '是否在日志中记录变量值（敏感信息请勿开启）',
      group: 'advanced',
      order: 3,
      default: false,
    },
  },

  presets: {
    string: {
      label: '字符串变量',
      description: '设置字符串类型的变量',
      config: {
        value_type: 'string',
        scope: 'local',
        overwrite: true,
      },
    },

    number: {
      label: '数字变量',
      description: '设置数字类型的变量',
      config: {
        value_type: 'number',
        scope: 'local',
        overwrite: true,
      },
    },

    config: {
      label: '配置变量',
      description: '设置配置类型的变量',
      config: {
        value_type: 'json',
        scope: 'global',
        overwrite: false,
        log_value: false,
      },
    },
  },
}

export const logMessageSchema: ComponentConfigSchema = {
  componentType: 'log_message',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '日志记录的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '日志输出的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    message: {
      type: 'textarea',
      label: '日志消息',
      description: '要记录的日志内容',
      placeholder: '输入日志消息',
      required: true,
      group: 'basic',
      order: 1,
      rows: 3,
    },

    level: {
      type: 'select',
      label: '日志级别',
      description: '日志消息的重要程度',
      group: 'basic',
      order: 2,
      default: 'INFO',
      options: [
        { label: 'DEBUG', value: 'DEBUG', description: '调试信息' },
        { label: 'INFO', value: 'INFO', description: '一般信息' },
        { label: 'WARN', value: 'WARN', description: '警告信息' },
        { label: 'ERROR', value: 'ERROR', description: '错误信息' },
        { label: 'CRITICAL', value: 'CRITICAL', description: '严重错误' },
      ],
    },

    include_timestamp: {
      type: 'boolean',
      label: '包含时间戳',
      description: '是否在日志中包含时间戳',
      group: 'advanced',
      order: 1,
      default: true,
    },

    include_variables: {
      type: 'boolean',
      label: '包含变量信息',
      description: '是否在日志中包含当前变量状态',
      group: 'advanced',
      order: 2,
      default: false,
    },

    console_output: {
      type: 'boolean',
      label: '控制台输出',
      description: '是否同时输出到控制台',
      group: 'advanced',
      order: 3,
      default: true,
    },
  },

  presets: {
    info: {
      label: '信息日志',
      description: '记录一般信息的配置',
      config: {
        level: 'INFO',
        include_timestamp: true,
        console_output: true,
      },
    },

    debug: {
      label: '调试日志',
      description: '记录调试信息的配置',
      config: {
        level: 'DEBUG',
        include_timestamp: true,
        include_variables: true,
        console_output: true,
      },
    },

    error: {
      label: '错误日志',
      description: '记录错误信息的配置',
      config: {
        level: 'ERROR',
        include_timestamp: true,
        include_variables: false,
        console_output: true,
      },
    },
  },
}

export const userInputSchema: ComponentConfigSchema = {
  componentType: 'user_input',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '用户输入对话框的基本参数',
      icon: 'User',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '输入验证和显示选项',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
  ],

  fields: {
    prompt_message: {
      type: 'textarea',
      label: '提示消息',
      description: '在对话框中显示给用户的提示信息',
      placeholder: '请输入您的信息',
      required: true,
      group: 'basic',
      order: 1,
      rows: 2,
    },

    default_value: {
      type: 'textarea',
      label: '默认值',
      description: '输入框的默认值（可选）',
      placeholder: '默认值',
      group: 'basic',
      order: 2,
      rows: 2,
    },

    input_type: {
      type: 'select',
      label: '输入类型',
      description: '输入框的类型',
      group: 'basic',
      order: 3,
      default: 'text',
      options: [
        { label: '单行文本', value: 'text', description: '普通单行文本输入' },
        { label: '多行文本', value: 'multiline', description: '多行文本输入' },
        { label: '密码', value: 'password', description: '密码输入（隐藏字符）' },
        { label: '数字', value: 'number', description: '数字输入' },
        { label: '邮箱', value: 'email', description: '邮箱地址输入' },
      ],
    },

    multiline: {
      type: 'boolean',
      label: '多行输入',
      description: '是否支持多行文本输入',
      group: 'basic',
      order: 4,
      default: false,
      dependsOn: {
        field: 'input_type',
        value: 'multiline',
      },
    },

    required: {
      type: 'boolean',
      label: '必填项',
      description: '是否要求用户必须输入值',
      group: 'advanced',
      order: 1,
      default: true,
    },

    hidden: {
      type: 'boolean',
      label: '隐藏输入',
      description: '是否隐藏用户输入的内容（如密码）',
      group: 'advanced',
      order: 2,
      default: false,
    },

    variable_name: {
      type: 'string',
      label: '存储变量名（可选）',
      description: '存储用户输入的变量名称，留空则不存储变量',
      placeholder: 'user_input',
      required: false,
      group: 'advanced',
      order: 3,
      outputVariable: true,
      validation: [
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
  },

  presets: {
    simple_input: {
      label: '简单输入',
      description: '获取用户简单文本输入，不存储变量',
      config: {
        prompt_message: '请输入内容',
        input_type: 'text',
        required: true,
        variable_name: '',
      },
    },

    multiline_input: {
      label: '多行文本输入',
      description: '获取用户多行文本输入',
      config: {
        prompt_message: '请输入多行文本内容',
        input_type: 'multiline',
        multiline: true,
        required: true,
        variable_name: 'user_text',
      },
    },

    password_input: {
      label: '密码输入',
      description: '获取用户密码输入',
      config: {
        prompt_message: '请输入密码',
        input_type: 'password',
        required: true,
        hidden: true,
        variable_name: 'user_password',
      },
    },

    number_input: {
      label: '数字输入',
      description: '获取用户数字输入',
      config: {
        prompt_message: '请输入数字',
        input_type: 'number',
        required: true,
        variable_name: 'user_number',
      },
    },
  },

  examples: [
    {
      title: '简单文本输入',
      description: '获取用户文本输入，不存储变量',
      config: {
        prompt_message: '请输入您的姓名',
        input_type: 'text',
        required: true,
        variable_name: '',
      },
    },
    {
      title: '多行备注输入',
      description: '获取用户多行备注信息',
      config: {
        prompt_message: '请输入备注信息',
        input_type: 'multiline',
        multiline: true,
        default_value: '请在此输入详细备注...',
        required: false,
        variable_name: 'user_notes',
      },
    },
    {
      title: '获取用户名并存储',
      description: '运行时弹框获取用户名并存储到变量',
      config: {
        prompt_message: '请输入您的用户名',
        input_type: 'text',
        required: true,
        variable_name: 'username',
      },
    },
    {
      title: '获取密码',
      description: '运行时弹框获取密码',
      config: {
        prompt_message: '请输入您的密码',
        input_type: 'password',
        required: true,
        hidden: true,
        variable_name: 'password',
      },
    },
  ],
}

export const userChoiceSchema: ComponentConfigSchema = {
  componentType: 'user_choice',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '用户选择对话框的基本参数',
      icon: 'Select',
      order: 1,
      collapsible: false,
    },
  ],

  fields: {
    variable_name: {
      type: 'string',
      label: '变量名称',
      description: '存储用户选择的变量名称',
      placeholder: 'user_choice',
      required: true,
      group: 'basic',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'required',
          message: '变量名称不能为空',
        },
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    prompt_message: {
      type: 'textarea',
      label: '提示消息',
      description: '在选择对话框中显示的提示信息',
      placeholder: '请选择一个选项',
      required: true,
      group: 'basic',
      order: 2,
      rows: 2,
    },

    choices: {
      type: 'textarea',
      label: '选择选项',
      description: '可选择的选项，用逗号分隔',
      placeholder: '选项1,选项2,选项3',
      required: true,
      group: 'basic',
      order: 3,
      rows: 3,
    },

    default_choice: {
      type: 'string',
      label: '默认选择',
      description: '默认选中的选项（可选）',
      placeholder: '选项1',
      group: 'basic',
      order: 4,
    },
  },

  presets: {
    yes_no: {
      label: '是/否选择',
      description: '简单的是否选择',
      config: {
        choices: '是,否',
        default_choice: '是',
      },
    },

    priority: {
      label: '优先级选择',
      description: '选择任务优先级',
      config: {
        choices: '高,中,低',
        default_choice: '中',
      },
    },
  },

  examples: [
    {
      title: '选择环境',
      description: '让用户选择部署环境',
      config: {
        variable_name: 'deploy_env',
        prompt_message: '请选择部署环境',
        choices: '开发环境,测试环境,生产环境',
        default_choice: '测试环境',
      },
    },
  ],
}

export const userConfirmSchema: ComponentConfigSchema = {
  componentType: 'user_confirm',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '用户确认对话框的基本参数',
      icon: 'QuestionFilled',
      order: 1,
      collapsible: false,
    },
  ],

  fields: {
    variable_name: {
      type: 'string',
      label: '变量名称',
      description: '存储用户确认结果的变量名称',
      placeholder: 'user_confirm',
      required: true,
      group: 'basic',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'required',
          message: '变量名称不能为空',
        },
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },

    prompt_message: {
      type: 'textarea',
      label: '确认消息',
      description: '向用户显示的确认信息',
      placeholder: '确定要执行此操作吗？',
      required: true,
      group: 'basic',
      order: 2,
      rows: 2,
    },

    default_answer: {
      type: 'boolean',
      label: '默认答案',
      description: '默认的确认结果',
      group: 'basic',
      order: 3,
      default: true,
    },
  },

  examples: [
    {
      title: '删除确认',
      description: '确认是否删除文件',
      config: {
        variable_name: 'confirm_delete',
        prompt_message: '确定要删除这个文件吗？此操作不可撤销。',
        default_answer: false,
      },
    },
  ],
}

export const notifierSendSchema: ComponentConfigSchema = {
  componentType: 'notifer_send',
  version: '1.0.0',

  groups: [
    {
      id: 'input',
      label: '指令输入',
      description: '',
      icon: 'Connection',
      order: 1,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'other',
      label: '其他设置',
      // description: 'HTTP请求头的配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    title: {
      type: 'textarea',
      label: '消息标题',
      description: '',
      placeholder: '',
      group: 'input',
      required:true,
      order: 1,
      rows: 3,
      variableSupport: true
    },
    content: {
      type: 'textarea',
      label: '消息内容',
      description: '',
      placeholder: '',
      group: 'input',
      required:true,
      rows: 3,
      order: 2,
      variableSupport: true
    },
    users: {
      type: 'usertree',
      label: '通知对象',
      description: '',
      group: 'input',
      order: 3,
      required:true,
    },
    methods:{
      type: 'checkbox',
      label: '通知方式',
      description: '',
      group: 'input',
      order: 4,
      required:true,
      options: [
        { label: '通知', value: 'sys' },
        { label: '短信', value: 'sms' },
        { label: '消息', value: 'chat' },
      ]
    },
    link:{
      type: 'string',
      label: '跳转链接',
      placeholder: '请输入需要跳转的链接地址',
      group: 'input',
      order: 5,
    },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children:[
        {
          id:'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id:'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        }
      ]
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' }
      ]
    }
    
  },

  examples: [
  ],
}



