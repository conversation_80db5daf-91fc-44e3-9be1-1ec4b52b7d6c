import { createRouter, createWebHashHistory } from 'vue-router'
import { useLoadingStore } from '@/stores/loading';
import utils from "@/utils/utils";


const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/manager',
    },
    // {
    //   path: '/homepage',
    //   name: 'homepage',
    //   component: () => import('../views/homepage/index.vue'),
    //   meta: {
    //     title: '介绍页',
    //   },
    // },
    // {
    //   path: '/loading',
    //   name: 'loading',
    //   component: () => import('../components/pageLoading/index.vue'),
    //   meta: {
    //     title: '介绍页',
    //   },
    // },
    // {
    //   path: '/homepage',
    //   name: 'homepage',
    //   component: () => import('../views/homepage/indexV2.vue'),
    //   meta: {
    //     title: '介绍页',
    //   },
    // },
    // {
    //   path: '/serviceMarket',
    //   name: 'serviceMarket',
    //   component: () => import('../views/serviceMarket/index.vue'),
    //   meta: {
    //     title: '服务市场',
    //   },
    // },
    {
      path: '/manager',
      name: 'manager',
      component: () => import('../views/manager/index.vue'),
      meta: {
        title: '任务管理',
      },
    },
    {
      path: '/record',
      name: 'record',
      component: () => import('../views/record/index.vue'),
      meta: {
        title: '任务记录',
      },
    },
    {
      path: '/config',
      name: 'Config',
      component: () => import('../views/config/index.vue'),
      meta: {
        title: '全局配置',
      },
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('../views/settings/index.vue'),
      meta: {
        title: '任务配置',
      },
    },
    {
      path: '/designer',
      name: 'Designer',
      component: () => import('../views/designer/index.vue'),
      meta: {
        title: '任务设计',
      },
    },
    {
      path: '/taskMonitor',
      name: 'TaskMonitor',
      component: () => import('../views/taskMonitor/index.vue'),
      meta: {
        title: '任务监控',
      },
    },
    {
      path: '/filePreview',
      name: 'FilePreview',
      component: () => import('../views/filePreview/index.vue'),
      meta: {
        title: '文件预览',
      },
    },
    {
      path: '/info',
      name: 'info',
      component: () => import('../views/info/index.vue'),
      meta: {
        title: '产品展示',
      },
    }
  ],
})
// 3. 在 router.js 中添加全局前置守卫
router.beforeEach(async (to, from, next) => {

  // try {
  //   const loadingStore = useLoadingStore()
  //   if(loadingStore.isFirst&&to.path==='/homepage') {
  //     const response = await fetch('http://localhost:39876/checkUpdate?uniwater_utokn='+utils.GetAuthorization(), {
  //       method: 'GET',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Authorization': utils.GetAuthorization(),
  //       }
  //     })
  //     const result = await response.json()
  //     const versionResponse = await fetch('http://localhost:39876/version?uniwater_utokn='+utils.GetAuthorization(), {
  //       method: 'GET',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Authorization': utils.GetAuthorization(),
  //       }
  //     })
  //     let currentVersion = '1.0.0'
  //     if(versionResponse.ok){
  //       currentVersion = await versionResponse.json()
  //     }
  //     const loading = document.getElementById('loading');
  //     if (loading) {
  //       loading.classList.add('loading-hidden');
  //       loading.style.display = 'none';
  //       loading.remove();
  //     }
  //     loadingStore.startLoading()

  //     loadingStore.setCurrentVersion(currentVersion)
  //     if(result.Response) {
  //       loadingStore.setLatest(false)
  //       loadingStore.setPublishType(result.Response.publishType)
  //       loadingStore.setPublishResponse(result.Response)
  //     }else{
  //       loadingStore.setLatest(true)
  //     }
  //     // loadingStore.startLoading()
  //     // await simulateAsyncOperation()
  //     loadingStore.setFirst(false)
  //   }else{

  //   }

    // 继续路由跳转
    next()
  // } catch (error) {
  //   // 错误处理
  //   next()
  // } finally {
  // }
})

router.afterEach(() => {
})

export default router
