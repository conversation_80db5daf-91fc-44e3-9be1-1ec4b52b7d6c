<template>
  <el-form-item
    v-if="shouldShow"
    :label="fieldDefinition.label || fieldName"
    :required="fieldDefinition.required"
    :error="validationError"
    :class="['smart-config-field', `field-type-${fieldDefinition.type}`, fieldDefinition.labelHidden ? 'label-hidden' : '']"
  >
    <!-- form-item标题,支持右侧新增按钮 -->
    <template #label>
      <span @click.stop.prevent>{{ fieldDefinition.label || fieldName }}</span>
      <!-- 单位显示 -->
      <span v-if="fieldDefinition.unit && fieldDefinition.type !== 'boolean'" class="field-unit">
        ({{ fieldDefinition.unit }})
      </span>
      <el-button v-if="fieldDefinition.addVariableDatas" type="primary" @click.stop.prevent="addVariableInputField" class="add-button" link title="插入变量">
        <el-icon class="right-btn"  size="14">
          <Plus />
        </el-icon>
        <span style="line-height:14px;font-size:12px;">新增</span>
      </el-button>
      <!-- 提取变量的新增按钮（外部） -->
      <el-button v-if="fieldDefinition.type === 'variables_map'" type="primary" @click.stop.prevent="addVariablesMap" class="add-button" link title="新增变量">
        <el-icon class="right-btn"  size="14">
          <Plus />
        </el-icon>
        <span style="line-height:14px;font-size:12px;">新增</span>
      </el-button>
    </template>

    <!-- 滑块 -->
    <div v-if="fieldDefinition.enabled === true" class="boolean-field position-switch">
      <el-switch :model-value="allValues[`${fieldName}_enabled`]" @change="handleEnabled" />
    </div>
    <!-- <div v-if="fieldDefinition.type==='errorretry'" class="boolean-field position-switch">
      <el-switch :model-value="allValues[`${fieldName}`]" @change="handleEnabled($event,fieldName)" />
    </div> -->

    <!-- 变量选择器 -->
    <el-select
      v-if="fieldDefinition.type === 'variable_picker'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder || '选择工作流变量...'"
      clearable
      filterable
      class="variable-select select-12"
    >
      <!-- 按sourceNodeId分组的变量 -->
      <el-option-group
        v-for="group in groupedVariables"
        :key="group.label"
        :label="group.label"
        class="variable-select-popper_option_group"
      >
        <el-option
          v-for="variable in group.options"
          :key="variable.name"
          :label="`${variable.name} - ${variable.description || variable.type}`"
          :value="variable.name"
        >
          <div class="variable-option-content">
            <span class="variable-name">${{ variable.name }}</span>
            <el-tag :type="getScopeTagType(variable.scope)" size="small" class="variable-scope">
              {{ getScopeLabel(variable.scope) }}
            </el-tag>
          </div>
          <div class="variable-description">{{ variable.description || variable.type }}</div>
        </el-option>
      </el-option-group>
    </el-select>

    <!-- 支持变量的字符串/文本输入 -->
    <VariableInputField
      ref="VariableInputFieldRef"
      v-else-if="
        (fieldDefinition.type === 'string' || fieldDefinition.type === 'textarea' || fieldDefinition.type === 'folder') &&
        fieldDefinition.variableSupport
        || (fieldDefinition.type === 'stringArray')
      "
      :model-value="value"
      @update:model-value="handleUpdate"
      @change-prefix="changePrefix"
      @change-append="changeAppend"
      :all-values="allValues"
      :field-name="fieldName"
      :field-config="fieldDefinition"
      :disabled="fieldDefinition.readonly"
      :type="fieldDefinition.type"
      :language="fieldDefinition.language"
    />

    <!-- 普通字符串输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'string'"
      :model-value="value"
      @update:model-value="handleUpdate"
      @input="validateVariableName"
      :placeholder="fieldDefinition.placeholder"
      :readonly="fieldDefinition.readonly"
      :class="{ 'input-with-prefix': fieldDefinition.prefix && typeof fieldDefinition.prefix === 'object' && fieldDefinition.prefix.options }"
      clearable
    >
      <template v-if="fieldDefinition.prefix" #prefix>
         <el-select
            v-if="fieldDefinition.prefix.options"
            size="mini"
            :style="{width:`${fieldDefinition.prefix.width||'90px'}`}"
            class="input-prefix-select select-12"
            popper-class="input-prefix-select-popper select-12-popper"
            @change="changePrefix"
            v-model="allValues[fieldDefinition.prefix.key||`${fieldName}_method`]"
          >
            <el-option
              v-for="item in fieldDefinition.prefix.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        <span v-else>{{ fieldDefinition.prefix }}</span>
      </template>
      <template v-if="fieldDefinition.suffix" #suffix>
        <span>{{ fieldDefinition.suffix }}</span>
      </template>
      <!-- 变量类型设置 -->
      <template v-if="fieldDefinition.outputVariable" #append>
        <el-select v-model="allValues[`${fieldName}$$type`]" @change="changeVariableType" placeholder="设置类型" style="width: 95px;" class="el-select-small"
          :class="{'right-radius-0': allValues[`${fieldName}$$type`] === 'json' || allValues[`${fieldName}$$type`] === 'list'}">
          <el-option label="对象" value="json" />
          <el-option label="列表" value="list" />
          <el-option label="字符" value="string" />
          <el-option label="数字" value="number" />
          <el-option label="布尔" value="boolean" />
        </el-select>
        <template v-if="allValues[`${fieldName}$$type`] === 'json' || allValues[`${fieldName}$$type`] === 'list'">
          <el-tooltip content="配置示例数据，用于excel/word模板绑定" placement="top" effect="light">
            <div class="smart-config-variable-demo-button" @click="openDemoDialog" title="">示例</div>
          </el-tooltip>
          <el-dialog
            v-model="showDemoDialog"
            title="配置示例数据"
            width="60%"
            append-to-body
            :close-on-click-modal="false"
          >
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <div style="color:#666;">用于excel/word模板绑定</div>
              <el-button @click="formatJson" :icon="Document" class="format-button" size="small">
                格式化
              </el-button>
            </div>
            <div>
              <el-input
                type="textarea"
                :rows="10"
                v-model="demoContent"
                placeholder="请输入JSON格式的Demo数据"
              />
            </div>
            <template #footer>
              <el-button @click="showDemoDialog = false">取消</el-button>
              <el-button type="primary" @click="saveDemoData">确定</el-button>
            </template>
          </el-dialog>
        </template>
      </template>
    </el-input>

    <!-- 普通多行文本 -->
    <el-input
      v-else-if="fieldDefinition.type === 'textarea'"
      :model-value="value"
      @update:model-value="handleUpdate"
      type="textarea"
      :rows="fieldDefinition.rows || 3"
      :placeholder="fieldDefinition.placeholder"
      :readonly="fieldDefinition.readonly"
    />
    <!-- 数字输入 -->
    <el-input-number
      v-else-if="fieldDefinition.type === 'number'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :min="fieldDefinition.min"
      :class="[fieldDefinition.suffix&&fieldDefinition.suffix.options?'inputnumber-with-suffix':'']"
      :controls="fieldDefinition.suffix&&fieldDefinition.suffix.options?false:true"
      :max="fieldDefinition.max"
      :step="fieldDefinition.step || 1"
      :precision="fieldDefinition.precision"
      :placeholder="fieldDefinition.placeholder"
      :readonly="fieldDefinition.readonly"
      style="width: 100%"
    >
      <template #decrease-icon>
        <el-icon>
          <Minus />
        </el-icon>
      </template>
      <template #increase-icon>
        <el-icon>
          <Plus />
        </el-icon>
      </template>
      <template #suffix>
        <el-select
          v-if="fieldDefinition.suffix&&fieldDefinition.suffix.options"
          size="mini"
          :style="{width:`${fieldDefinition.suffix.width||'90px'}`}"
          class="input-prefix-select select-12"
          popper-class="input-prefix-select-popper select-12-popper"
          @change="changeSuffix"
          v-model="allValues[fieldDefinition.suffix.key||`${fieldName}_suffix_unit`]"
        >
          <el-option
            v-for="item in fieldDefinition.suffix.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span v-else>{{fieldDefinition.suffixUnit}}</span>
      </template>
    </el-input-number>

    <!-- 布尔值开关 -->
    <div v-else-if="fieldDefinition.type === 'boolean'" class="boolean-field">
      <el-switch
        :model-value="value"
        @update:model-value="handleUpdate"
        :disabled="fieldDefinition.readonly"
      />
      <span v-if="fieldDefinition.switchTrue && fieldDefinition.switchFalse" class="field-unit">{{ value ? fieldDefinition.switchTrue : fieldDefinition.switchFalse }}</span>
      <span v-if="fieldDefinition.unit" class="field-unit">{{ fieldDefinition.unit }}</span>
    </div>

    <!-- 日期时间选择器 -->
    <el-date-picker
      v-else-if="fieldDefinition.type === 'datetime'"
      :model-value="value"
      @update:model-value="handleUpdate"
      type="datetime"
      :placeholder="fieldDefinition.placeholder || '选择日期时间'"
      :disabled="fieldDefinition.readonly"
      :shortcuts="fieldDefinition.shortcuts"
      :format="fieldDefinition.format || 'YYYY-MM-DD HH:mm:ss'"
      :value-format="fieldDefinition.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
      style="width: 100%"
    />

    <!-- 模板选择器（带创建按钮） -->
    <div v-else-if="fieldDefinition.type === 'select' && fieldName === 'template_id'" class="template-select-container">
      <el-select
        :model-value="value"
        @update:model-value="handleUpdate"
        :placeholder="loadingOptions ? '正在加载选项...' : fieldDefinition.placeholder"
        :disabled="fieldDefinition.readonly || loadingOptions"
        :loading="loadingOptions"
        clearable
        popper-class="select-12-popper"
        class="template-select select-12"
      >
        <el-option-group v-for="group in groupedOptions" :key="group.label" :label="group.label">
          <el-option
            v-for="option in group.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          >
            <div class="option-content">
              <el-icon v-if="option.icon" class="option-icon">
                <component :is="option.icon" />
              </el-icon>
              <div class="option-text">
                <span class="option-label">{{ option.label }}</span>
                <span v-if="option.description" class="option-description">
                  {{ option.description }}
                </span>
              </div>
            </div>
          </el-option>
        </el-option-group>

        <!-- 无分组的选项 -->
        <el-option
          v-for="option in ungroupedOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
        >
          <div class="option-content">
            <el-icon v-if="option.icon" class="option-icon">
              <component :is="option.icon" />
            </el-icon>
            <div class="option-text">
              <span class="option-label">{{ option.label }}</span>
              <span v-if="option.description" class="option-description">
                {{ option.description }}
              </span>
            </div>
          </div>
        </el-option>
      </el-select>

      <el-button
        @click="openReportDesigner(fieldDefinition.template_type)"
        :icon="Grid"
        type="primary"
        size="default"
        class="create-template-btn"
        title="创建模板"
      >
        创建模板
      </el-button>
    </div>

    <!-- 普通选择器 -->
    <el-select
      v-else-if="fieldDefinition.type === 'select' && fieldName !== 'template_id'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="loadingOptions ? '正在加载选项...' : fieldDefinition.placeholder"
      :disabled="fieldDefinition.readonly || loadingOptions"
      :loading="loadingOptions"
      clearable
      filterable
      @change="handleSelectChange"
      popper-class="select-12-popper"
      class="common-select select-12"
      style="width: 100%"
    >
      <el-option-group v-for="group in groupedOptions" :key="group.label" :label="group.label"
        class="smart-config-select-group">
        <el-option
          v-for="option in group.options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
          :class="{'has-description': !!option.description}"
        >
          <div class="option-content">
            <el-icon v-if="option.icon" class="option-icon">
              <component :is="option.icon" />
            </el-icon>
            <div class="option-text">
              <span class="option-label">{{ option.label }}</span>
              <span v-if="option.description" class="option-description">
                {{ option.description }}
              </span>
            </div>
          </div>
        </el-option>
      </el-option-group>

      <!-- 无分组的选项 -->
      <el-option
        v-for="option in ungroupedOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
        :class="{'has-description': !!option.description}"
      >
        <div class="option-content">
          <el-icon v-if="option.icon" class="option-icon">
            <component :is="option.icon" />
          </el-icon>
          <div class="option-text">
            <span class="option-label">{{ option.label }}</span>
            <span v-if="option.description" class="option-description">
              {{ option.description }}
            </span>
          </div>
        </div>
      </el-option>
    </el-select>

    <!-- 多选器 -->
    <el-select
      v-else-if="fieldDefinition.type === 'multiselect'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder"
      :disabled="fieldDefinition.readonly"
      multiple
      filterable
      clearable
      popper-class="select-12-popper"
      class="multiselect select-12"
      style="width: 100%"
    >
      <el-option-group v-for="group in groupedOptions" :key="group.label" :label="group.label"
        class="smart-config-select-group">
        <el-option
          v-for="option in group.options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
          :class="{'has-description': !!option.description}"
        >
          <div class="option-content">
            <el-icon v-if="option.icon" class="option-icon">
              <component :is="option.icon" />
            </el-icon>
            <div class="option-text">
              <span class="option-label">{{ option.label }}</span>
              <span v-if="option.description" class="option-description">
                {{ option.description }}
              </span>
            </div>
          </div>
        </el-option>
      </el-option-group>

      <!-- 无分组的选项 -->
      <el-option
        v-for="option in ungroupedOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
        :class="{'has-description': !!option.description}"
      >
        <div class="option-content">
          <el-icon v-if="option.icon" class="option-icon">
            <component :is="option.icon" />
          </el-icon>
          <div class="option-text">
            <span class="option-label">{{ option.label }}</span>
            <span v-if="option.description" class="option-description">
              {{ option.description }}
            </span>
          </div>
        </div>
      </el-option>
    </el-select>

    <!-- 文件选择 -->
    <div v-else-if="fieldDefinition.type === 'file'" class="file-field">
      <el-input
        :model-value="value"
        @update:model-value="handleUpdate"
        :placeholder="fieldDefinition.placeholder || '选择文件'"
        :readonly="fieldDefinition.readonly"
      >
        <template #append>
          <el-button @click="selectFile">
            <el-icon>
              <Folder />
            </el-icon>
            浏览
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 文件夹选择 -->
    <div v-else-if="fieldDefinition.type === 'folder'" class="folder-field">
      <el-input
        :model-value="value"
        @update:model-value="handleUpdate"
        :placeholder="fieldDefinition.placeholder || '选择文件夹'"
        :readonly="fieldDefinition.readonly"
      >
        <template #append>
          <el-button @click="selectFolder">
            <el-icon>
              <FolderOpened />
            </el-icon>
            选择文件夹
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 颜色选择 -->
    <div v-else-if="fieldDefinition.type === 'color'" class="color-field">
      <el-color-picker
        :model-value="value"
        @update:model-value="handleUpdate"
        :disabled="fieldDefinition.readonly"
      />
      <el-input
        :model-value="value"
        @update:model-value="handleUpdate"
        :placeholder="fieldDefinition.placeholder || '#000000'"
        :readonly="fieldDefinition.readonly"
        style="margin-left: 8px; flex: 1"
      />
    </div>

    <!-- URL输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'url'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder || 'https://'"
      :readonly="fieldDefinition.readonly"
      clearable
    >
      <template #prefix>
        <el-icon>
          <Link />
        </el-icon>
      </template>
    </el-input>

    <!-- 邮箱输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'email'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder || '<EMAIL>'"
      :readonly="fieldDefinition.readonly"
      clearable
    >
      <template #prefix>
        <el-icon>
          <Message />
        </el-icon>
      </template>
    </el-input>

    <!-- 密码输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'password'"
      :model-value="value"
      @update:model-value="handleUpdate"
      type="password"
      :placeholder="fieldDefinition.placeholder"
      :readonly="fieldDefinition.readonly"
      show-password
      clearable
    />

    <!-- XPath输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'xpath'"
      :model-value="value"
      @update:model-value="handleUpdate"
      type="textarea"
      :rows="fieldDefinition.rows || 2"
      :placeholder="fieldDefinition.placeholder || '//div[@class=&quot;example&quot;]'"
      :readonly="fieldDefinition.readonly"
    >
      <template #prefix>
        <span class="xpath-prefix">XPath</span>
      </template>
    </el-input>

    <!-- CSS选择器输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'css'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder || '.class-name'"
      :readonly="fieldDefinition.readonly"
      clearable
    >
      <template #prefix>
        <span class="css-prefix">CSS</span>
      </template>
    </el-input>

    <!-- 正则表达式输入 -->
    <el-input
      v-else-if="fieldDefinition.type === 'regex'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder || '^[a-zA-Z0-9]+$'"
      :readonly="fieldDefinition.readonly"
      clearable
    >
      <template #prefix>
        <span class="regex-prefix">RegEx</span>
      </template>
    </el-input>

    <!-- 代码编辑器 -->
    <div v-else-if="fieldDefinition.type === 'code'" class="code-field">
      <el-input
        :model-value="value"
        @update:model-value="handleUpdate"
        type="textarea"
        :rows="fieldDefinition.rows || 5"
        :placeholder="fieldDefinition.placeholder"
        :readonly="fieldDefinition.readonly"
        class="code-input"
      />
    </div>

    <!-- 支持变量的JSON编辑器 -->
    <VariableInputField
      v-else-if="fieldDefinition.type === 'json' && fieldDefinition.variableSupport"
      :model-value="jsonValue"
      @update:model-value="handleJsonUpdate"
      :field-config="fieldDefinition"
      :disabled="fieldDefinition.readonly"
      :type="fieldDefinition.type"
      :language="fieldDefinition.language"
    />

    <!-- 普通JSON编辑器 -->
    <div v-else-if="fieldDefinition.type === 'json'" class="json-field">
      <el-input
        :model-value="jsonValue"
        @update:model-value="handleJsonUpdate"
        type="textarea"
        :rows="fieldDefinition.rows || 8"
        :placeholder="fieldDefinition.placeholder || '{ &quot;key&quot;: &quot;value&quot; }'"
        :readonly="fieldDefinition.readonly"
        class="json-input"
      />
    </div>

    <!-- 默认文本 -->
    <div v-else-if="fieldDefinition.value">{{ fieldDefinition.value }}</div>

    <!-- 单选框 -->
    <el-radio-group
      v-else-if="fieldDefinition.type === 'radio'"
      :model-value="value"
      @update:model-value="handleUpdate"
      class="smart-config-radio-group"
    >
      <el-radio
        v-for="option in fieldDefinition.options || []"
        :key="option.value"
        :value="option.value"
        >{{ option.label }}</el-radio
      >
    </el-radio-group>

    <!-- 复选框 -->
    <el-checkbox-group
      v-else-if="fieldDefinition.type === 'checkbox'"
      :model-value="value"
      @update:model-value="handleUpdate"
      class="smart-config-radio-group"
    >
      <el-checkbox
        v-for="option in fieldDefinition.options || []"
        :key="option.value"
        :value="option.value"
        >{{ option.label }}</el-checkbox
      >
    </el-checkbox-group>

    <!-- 条件配置 -->
    <ConditionsField
      v-else-if="fieldDefinition.type === 'conditions'"
      :model-value="value"
      @update:model-value="handleUpdate"
    />

    <!-- 多输出变量 -->
    <VariablesMapField
      v-else-if="fieldDefinition.type === 'variables_map'"
      ref="VariablesMapRef"
      :model-value="value"
      @update:model-value="handleUpdate"
      :hide-real-key="fieldDefinition.hideRealKey"
      :validation-errors="validationErrors"
    />


    <!-- 变量配置 -->
    <VariablesField
      v-else-if="fieldDefinition.type === 'variables'"
      :model-value="value"
      @update:model-value="handleUpdate"
    />

    <!-- 人员选择 -->
    <TreePickerUser
      v-else-if="fieldDefinition.type === 'usertree'"
      :model-value="value"
      @update:model-value="handleUpdate"
    />

    <!-- 开始节点编辑任务详情 -->
    <MissionEdit
      v-else-if="fieldDefinition.type === 'missionEdit'"
    />
    <!-- 失败时重试 -->
    <el-form
      v-else-if="fieldDefinition.type === 'errorretry'"
      :model="allValues"
      class="error-retry"
      label-position="left"
    >
      <el-form-item
        v-for="child in fieldDefinition.children"
        :key="child.id"
        :label="child.label"
        :required="child.required"
        :error="GetValidationErrors(child.id)"
        :class="['smart-config-field']"
      >
        <el-input-number
          :model-value="allValues[child.id]"
          @update:model-value="handleUpdate($event, child.id,true)"
          :min="child.min"
          :max="child.max"
          :step="child.step || 1"
          :controls="false"
          :precision="child.precision"
          :placeholder="child.placeholder"
          :readonly="child.readonly"
          style="width: 100%"
        >
          <template #decrease-icon>
            <el-icon>
              <Minus />
            </el-icon>
          </template>
          <template #increase-icon>
            <el-icon>
              <Plus />
            </el-icon>
          </template>
          <template #suffix>
            {{child.suffixUnit}}
          </template>
        </el-input-number>
      </el-form-item>
    </el-form>

    <!-- 自定义流程表单渲染（根据流程配置获取） -->
    <ProcessFormField
      v-else-if="fieldDefinition.type === 'processForm'"
      :model-value="value"
      @update:model-value="handleUpdate"
      :all-values="allValues"
      :field-name="fieldName"
      :field-config="fieldDefinition"
      :component-type="componentType"
    />


    <!-- 默认文本输入 -->
    <el-input
      v-else
      :model-value="value"
      @update:model-value="handleUpdate"
      :placeholder="fieldDefinition.placeholder"
      :readonly="fieldDefinition.readonly"
      clearable
    />


    <!-- 帮助文本 -->
    <div v-if="fieldDefinition.help" class="field-help">
      <el-icon :size="12">
        <QuestionFilled />
      </el-icon>
      <span>{{ fieldDefinition.help }}</span>
    </div>

    <!-- 字段描述 -->
    <div v-if="fieldDefinition.description" class="field-description">
      {{ fieldDefinition.description }}
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted,watch } from 'vue'
import {
  Folder,
  FolderOpened,
  Link,
  Message,
  QuestionFilled,
  Grid, Document,
} from '@element-plus/icons-vue'
import type { ConfigFieldDefinition } from '@/types/config'
import { shouldShowField, getFieldValidationErrors } from '@/config/schemaRegistry'
import VariableInputField from '@/components/common/VariableInputField.vue'
import ConditionsField from '@/components/common/ConditionsField.vue'
import VariablesField from '@/components/common/VariablesField.vue'
import ProcessFormField from '@/components/common/ProcessFormField.vue'
import VariablesMapField from '@/components/common/VariablesMapField.vue'

import { useWorkflowStore } from '@/stores/workflow'
import { getAvailableVariables } from '@/utils/availableVariables.ts'
import TreePickerUser from '@/components/treePickerUser.vue'
import MissionEdit from '@/components/missionEdit.vue'
import {ElMessage} from "element-plus";

interface Props {
  fieldName: string
  fieldDefinition: ConfigFieldDefinition
  value: any
  allValues: Record<string, any>
  componentType: string
}

interface Emits {
  (e: 'update:value', fieldName: string, value: any): void

  (e: 'validate', fieldName: string, isValid: boolean, message: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取工作流store
const workflowStore = useWorkflowStore()

// 初始化时处理默认值和异步选项加载
onMounted(async () => {
  if (props.fieldDefinition.default !== undefined && props.value === undefined) {
    handleUpdate(props.fieldDefinition.default)
  }
  // // 当字段类型为 errotretry 时，设置开关默认开启
  // if (props.fieldDefinition.type === 'errorretry') {
  //   const enabledKey = `${props.fieldName}`
  //   if (props.allValues[enabledKey] === undefined) {
  //     props.allValues[enabledKey] = true
  //     emit('update:value', enabledKey, true)
  //   }
  // }
  // 加载异步选项
  if (props.fieldDefinition.async && props.fieldDefinition.optionsLoader) {
    await loadAsyncOptions()
  }

  document.addEventListener('refresh-template-select', handleRefreshTemplateSelect as EventListener)
})

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('refresh-template-select', handleRefreshTemplateSelect as EventListener)
})


// 监听来自配置面板的模板设计器打开事件
const handleRefreshTemplateSelect = (event: CustomEvent) => {
  if(props.fieldDefinition.type === 'select' && props.fieldName === 'template_id'){
    console.log('收到刷新模板列表时间:', event.detail)
    if (props.fieldDefinition.async && props.fieldDefinition.optionsLoader) {
      loadAsyncOptions()
    }
  }
}

// 计算属性
const shouldShow = computed(() => {
  return shouldShowField(props.componentType, props.fieldName, props.allValues)
})

const validationErrors = computed(() => {
  return getFieldValidationErrors(
    props.componentType,
    props.fieldName,
    props.value,
    props.allValues,
  )
})

const validationError = computed(() => {
  return validationErrors.value.length > 0 ? validationErrors.value[0].message : ''
})

// 从工作流中收集可用变量（与VariableInputField保持一致）
const availableVariables = computed(() => getAvailableVariables())

// 变量作用域标签类型
const getScopeTagType = (scope: string) => {
  switch (scope) {
    case 'global':
      return 'danger'
    case 'workflow':
      return 'warning'
    case 'local':
      return 'info'
    default:
      return 'info'
  }
}

// 变量作用域标签文本
const getScopeLabel = (scope: string) => {
  switch (scope) {
    case 'global':
      return '全局'
    case 'workflow':
      return '工作流'
    case 'local':
      return '局部'
    default:
      return scope
  }
}

// 打开模板设计器
const openReportDesigner = (template_type:string) => {
  // 通过事件总线或者直接调用父组件的方法来打开模板设计器
  // 这里我们使用一个简单的方法：触发一个自定义事件
  const event = new CustomEvent('open-report-designer', {
    bubbles: true,
    detail: { source: 'template-selector',template_type }
  })
  document.dispatchEvent(event)
}



const groupedOptions = computed(() => {
  if (!currentOptions.value) return []

  const groups: Record<string, any[]> = {}
  currentOptions.value.forEach((option) => {
    if (option.group) {
      if (!groups[option.group]) {
        groups[option.group] = []
      }
      groups[option.group].push(option)
    }
  })

  return Object.entries(groups).map(([label, options]) => ({
    label,
    options,
  }))
})

const ungroupedOptions = computed(() => {
  if (!currentOptions.value) return []
  return currentOptions.value.filter((option) => !option.group)
})

// 异步选项加载
const currentOptions = ref(props.fieldDefinition.options || [])
const loadingOptions = ref(false)

// 加载异步选项
const loadAsyncOptions = async () => {
  if (!props.fieldDefinition.optionsLoader || !props.fieldDefinition.async) {
    return
  }

  loadingOptions.value = true
  try {
    const options = await props.fieldDefinition.optionsLoader()
    currentOptions.value = options || []
  } catch (error) {
    console.error('加载异步选项失败:', error)
    currentOptions.value = [{ label: '加载失败，请重试', value: '' }]
  } finally {
    loadingOptions.value = false
  }
}

const jsonValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }
  try {
    return JSON.stringify(props.value, null, 2)
  } catch {
    return ''
  }
})

const isShowControl =  computed(() => {
  let show = true
  if(props.fieldDefinition.enabled===true && props.allValues[`${props.fieldName}_enabled`] === false ) show = false;
  return show
})


const GetValidationErrors =  (fieldName: string)=>{
  let errors = getFieldValidationErrors(
    props.componentType,
    fieldName,
    props.allValues[fieldName],
    props.allValues,
    true
  )
  return errors.length > 0 ? errors[0].message : ''
}

// 方法
const handlePrefix = (value: string) => {
  emit('update:value', value, props.fieldDefinition.prefix.key||`${props.fieldName}_method`)
}

// 方法
const handleEnabled = (newValue: any,fieldName: string) => {
  emit('update:value', fieldName||`${props.fieldName}_enabled`, newValue)
  if(props.fieldDefinition.type === 'errorretry') {
    if(props.fieldDefinition.children){
      props.fieldDefinition.children.forEach(child=>{
        props.allValues[child.id] = null;
        emit('update:value', `${child.id}`, newValue ? 1 : null)
      })
    }
  }
}

const handleSelectChange = (newValue: string) => {
  if(props.fieldDefinition.changeEvent){
    props.fieldDefinition.changeEvent(newValue, currentOptions.value, emit)
  }
}


const changePrefix = (newValue: any, fieldName: string) => {
  emit('update:value', props.fieldDefinition.prefix.key || fieldName || props.fieldName, newValue)
}

const changeAppend = (newValue: any, fieldName: string) => {
  emit('update:value', props.fieldDefinition.append.key, newValue)
}

const changeSuffix = (newValue: any,fieldName: string) => {
  emit('update:value', props.fieldDefinition.suffix.key||fieldName||props.fieldName, newValue)
}

const changeVariableType = (newValue: any, fieldName: string) => {
  emit('update:value', fieldName||`${props.fieldName}$$example`, null)
  emit('update:value', fieldName||`${props.fieldName}$$type`, newValue)
}

// 方法
const handleUpdate = (newValue: any,fieldName: string,isChildren: boolean) => {
  emit('update:value', fieldName || props.fieldName, newValue)

  // 触发验证
  const errors = getFieldValidationErrors(props.componentType, fieldName||props.fieldName, newValue, {
    ...props.allValues,
    [props.fieldName]: newValue
  },isChildren)

  emit('validate', props.fieldName, errors.length === 0, errors[0]?.message || '')
}

// 输入内容校验
const validateVariableName = (inputValue: string) => {
  if (props.fieldDefinition.outputVariable) {
    // 实时过滤非法字符，只能包含字母、数字、横线、下划线，且不能以数字开头
    let filteredValue = inputValue.replace(/[^a-zA-Z0-9_-]/g, '')

    // 如果以数字开头，则移除开头的数字
    if (/^[0-9]/.test(filteredValue)) {
      filteredValue = filteredValue.replace(/^[0-9]+/, '')
    }

    if (filteredValue !== inputValue) {
      // 立即更新输入框的值
      emit('update:value', props.fieldName, filteredValue)
    }
  }
}

const handleJsonUpdate = (newValue: string) => {
  try {
    const parsed = JSON.parse(newValue)
    emit('update:value', props.fieldName, parsed)
  } catch {
    emit('update:value', props.fieldName, newValue)
  }
}

// 新增变量相关方法
const addVariableDatas = ref([] as string[])
// 新增变量回显
if (props.fieldDefinition.addVariableDatas) {
  addVariableDatas.value = typeof props.value === 'string' ? [props.value] : (props.value || [])
}
// 新增：监听value
watch(
  () =>  props.value,
  () => {
    addVariableDatas.value = typeof props.value === 'string' ? [props.value] : (props.value || [])
  },
  { deep: true }
)
const VariableInputFieldRef = ref(null)
// 新增变量输入框及字段
const addVariableInputField = () => {
  VariableInputFieldRef.value?.addVariableFnc()
}

// 新增多输出变量新增相关方法
const VariablesMapRef = ref(null)
const addVariablesMap = () => {
  VariablesMapRef.value?.addVariableGroup()
}

const selectFile = async () => {
  // Web环境下的文件选择
  const input = document.createElement('input')
  input.type = 'file'
  if (props.fieldDefinition.accept) {
    input.accept = props.fieldDefinition.accept
  }
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      handleUpdate(file.path || file.name)
    }
  }
  input.click()
}

const selectFolder = async () => {
  if (window.electronAPI) {
    // todo 一诺桌面端使用
  } else {
    // Web环境下的文件夹选择（HTML5 webkitdirectory）
    const input = document.createElement('input')
    input.type = 'file'
    input.webkitdirectory = true
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      if (files && files.length > 0) {
        // 获取第一个文件的路径，去掉文件名部分
        const firstFile = files[0]
        const path = firstFile.webkitRelativePath
        const folderPath = path.substring(0, path.indexOf('/'))
        handleUpdate(folderPath)
      }
    }
    input.click()
  }
}

// 按sourceNodeId分组的变量
const groupedVariables = computed(() => {
  const groups: Record<string, any[]> = {}

  availableVariables.value.forEach((variable) => {
    const groupKey = variable.sourceNodeId ? String(variable.sourceNodeId) : '其他'
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(variable)
  })

  return Object.entries(groups).map(([sourceNodeId, options]) => ({
    label: workflowStore.getNodeById(sourceNodeId)?.data?.label || sourceNodeId || '其他',
    options
  }))
})

const showDemoDialog = ref(false)
const demoContent = ref('')
const openDemoDialog = () => {
  demoContent.value = props.allValues[`${props.fieldName}$$example`]?.demo || ''
  // todo key-value map配置回显
  showDemoDialog.value = true
}
const formatJson = () => {
  try {
    const defaultType = props.allValues[`${props.fieldName}$$type`] === 'json' ? '{}' : '[]'
    const parsed = JSON.parse(demoContent.value || defaultType)
    demoContent.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

const saveDemoData = () => {
  try {
    // 验证JSON格式
    if (demoContent.value) {
      JSON.parse(demoContent.value)
    }
    emit('update:value', `${props.fieldName}$$example`, {
      demo: demoContent.value,
      // todo key-value map配置保存
    })
    showDemoDialog.value = false
  } catch (error) {
    ElMessage.error('请输入有效的JSON格式数据')
  }
}
</script>

<style>
.variable-select-popper_option_group{
  .el-select-group__title{
    font-weight: bold;
    color: #333333;
    font-size: 11px;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 1;
  }
  .el-select-dropdown__item{
    height: auto;
  }
}
.select-12-popper .el-select-dropdown__item{
  font-size:12px;
}
.has-description.el-select-dropdown__item{
  height: 44px;
  line-height: 16px;
  padding-top: 5px;
  padding-bottom: 5px;
}
.smart-config-select-group{
  .el-select-group__title{
    font-weight: bold;
  }
}
</style>

<style scoped>
.add-button{
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 6px;
}
.add-button .right-btn{
  margin-top:0
}
.smart-config-field {
  position: relative;
}

.label-hidden > :deep(.el-form-item__label){
  display: none;
}

.inputnumber-with-suffix :deep(.el-input__wrapper){
  padding:0;
}

.input-with-prefix :deep(.el-input__wrapper){
  padding:0;
}

.select-12 :deep(.el-select__selected-item){
  font-size:12px;
}


.error-retry{
  width: 100%;
  display: flex; /* 使用 flexbox 布局 */
  gap: 4px; /* 元素间的间距 */
}

.error-retry :deep(.el-form-item){
  flex:1;
  margin-bottom:0;
}

.right-btn {
  float: right;
  cursor: pointer;
  margin-top: 2px;
}

.add-variable-item {
  width: 100%;
  margin-top: 8px;
  display: flex;
  align-items: center;
}
.add-variable-item .add-variable-item-delete {
  margin-left: 8px;
  cursor: pointer;
}
.add-variable-item .el-input {
  flex: 1;
}

.boolean-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.position-switch {
  position: absolute;
  right: 0;
  top: -32px;
}

.file-field,
.folder-field {
  width: 100%;
}

.color-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-field,
.json-field {
  position: relative;
  width: 100%;
}

.code-input,
.json-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.field-unit {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.field-help {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #909399;
  margin-left: 4px;
}

.field-description {
  margin-top: 4px;
  margin-bottom: 3px;
  padding-left: 4px;
  font-size: 12px;
  color: #bcbfc3;
  line-height: 1.4;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.option-icon {
  flex-shrink: 0;
}

.option-text {
  flex: 1;
  min-width: 0;
}

.option-label {
  display: block;
  font-weight: 500;
}

.option-description {
  display: block;
  font-size: 11px;
  color: #bbbbbb;
  margin-top: 2px;
  font-weight: normal;
}

.xpath-prefix,
.css-prefix,
.regex-prefix {
  font-size: 10px;
  font-weight: 500;
  color: #409eff;
  background: #ecf5ff;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 4px;
}

.css-prefix {
  color: #67c23a;
  background: #f0f9ff;
}

.regex-prefix {
  color: #e6a23c;
  background: #fdf6ec;
}

:deep(.smart-config-radio-group) {
  .el-radio:not(:last-child) {
    margin-right: 16px;
  }
}

/* 字段类型特定样式 */
.field-type-xpath :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.field-type-css :deep(.el-input__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.field-type-regex :deep(.el-input__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.field-type-code :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.field-type-json :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* 验证状态样式 */
.smart-config-field.is-error :deep(.el-input__wrapper) {
  //box-shadow: 0 0 0 1px #f56c6c inset;
}

.smart-config-field.is-error :deep(.el-textarea__inner) {
  //box-shadow: 0 0 0 1px #f56c6c inset;
}

/* 只读状态样式 */
.smart-config-field :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 日期时间选择器样式 */
.field-type-datetime :deep(.el-input__wrapper) {
  padding: 1px 11px;
}

.field-type-datetime :deep(.el-input__inner) {
  text-align: left;
}

/* 变量选择器样式 */
.variable-select {
  width: 100%;
}


.variable-option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.variable-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #409eff;
}

.variable-scope {
  margin-left: 8px;
}

.variable-description {
  font-size: 12px;
  color: #aaaaaa;
  line-height: 1.2;
  margin-bottom: 8px;
}

/* 模板选择器样式 */
.template-select-container {
  display: flex;
  gap: 8px;
  align-items: stretch;
  width: 100%;
}

.template-select {
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.create-template-btn {
  flex-shrink: 0;
  white-space: nowrap;
  height: 32px; /* 与el-select默认高度对齐 */
  padding: 0 12px;
  font-size: 14px;
}
.smart-config-variable-demo-button{
  margin: 0 -15px 0 20px;
  padding: 0 10px;
  cursor: pointer;
  font-size: var(--el-font-size-extra-small);
  color: var(--el-text-color-primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .color-field {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }

  .option-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .field-type-datetime {
    width: 100%;
  }
}
</style>
