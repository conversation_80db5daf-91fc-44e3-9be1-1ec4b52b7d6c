<template>
  <div class="monitor">
    <van-empty description="无运行监控" v-if="!props.isLoading && !dataListKeys.length" />
    <van-collapse v-model="activeNames" ref="collapse" :lazy-render="false" :is-link="false" :border="false" class="view-table-card-box">
      <van-collapse-item v-for="(key, keyIndex) in dataListKeys" :key="keyIndex" :name="key">
        <template #title>
          <div class="title-box">
            <div class="icon-box" :style="{ 'background': handleNodesIcon('color', key) }">
              <i class="action-iconfont" :class="handleNodesIcon('icon', key)"></i>
            </div>
            <span class="title">{{ getNodeTitle(key) }}</span>
          </div>
          <span class="time">{{ getNodeExcTime(key) }}</span>
        </template>
        <div class="view-table-cards-title" v-if="getNodeType(key) === 'invoice_recognition'">
          <div class="cards-title-item">
            <div class="cards-title-item-label">
              <span>识别总数：</span>
            </div>
            <div class="cards-title-item-value">
              {{ dataList[key][0]?.total || dataList[key].length }}
            </div>
          </div>
          <div class="cards-title-item">
            <div class="cards-title-item-label">
              <span>成功：</span>
            </div>
            <div class="cards-title-item-value success">
              {{ dataList[key].filter((item: object) => item.type)?.length }}
            </div>
          </div>
          <div class="cards-title-item">
            <div class="cards-title-item-label">
              <span>失败：</span>
            </div>
            <div class="cards-title-item-value fail">
              {{ dataList[key].filter((item: object) => !item.type)?.length }}
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-if="getNodeType(key) === 'excel_create'">
          <div class="view-table-card excel" style="height: 500px" v-for="(item, index) in dataList[key]" :key="'card' + key + index">
            <div class="table-card-content">
              <file-preview :configId="templateExcel.configId" :missionId="missionId" :historyId="item.historyId" :config="templateExcel.config"></file-preview>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'data_forecast'">
          <div class="left-table">
            <el-table class="mask-table" :data="dataList[key][0].tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" style="width: 100%; height: 100%">
              <el-table-column type="index" label="序号" align="center" width="80" />
              <el-table-column v-for="it in dataList[key][0].tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                <template v-if="it.data == 'time'" #default="{ row }">
                  {{ formatTime(row.time, 'MM-DD HH:mm') }}
                </template>
                <template v-else-if="it.data == 'value'" #default="{ row }">
                  {{ row.value }}
                </template>
              </el-table-column>
              <template #empty>
                <el-empty description="暂无数据" style="height: 10vh" />
              </template>
            </el-table>
          </div>
          <div class="right-echarts">
            <div class="right-table-text">
              <p>
                预测平均值
                <span class="right-table-text-bold">{{ dataList[key][0].forecast_avg }}</span>
              </p>
              <p>
                预测累计值
                <span class="right-table-text-bold">{{ dataList[key][0].forecast_sum }}</span>
              </p>
              <p>
                预测最大值
                <span class="right-table-text-bold">{{ dataList[key][0].forecast_max }}</span>
              </p>
              <p>
                预测最小值
                <span class="right-table-text-bold">{{ dataList[key][0].forecast_min }}</span>
              </p>
            </div>
            <div :ref="
                (el) => {
                  if (el) {
                    if (!echartRefs[key + 'echarts']) echartRefs[key + 'echarts'] = []
                    echartRefs[key + 'echarts'].push(el)
                  }
                }
              " class="echarts-box">
              {{ initCharts(key, dataList[key][0]) }}
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'water_shutoff_valve'">
          <map-frame :nodes="nodes" :config="dataList[key][0]" />
        </div>
        <div
          class="view-table-cards"
          v-else-if="getNodeType(key) === 'http_post' || getNodeType(key) === 'http_get' ||getNodeType(key) === 'http_request'">
          <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card' + key + index">
            <div class="table-card-content-title">
              {{ item.desc }}
            </div>
            <div class="table-card-content http" v-if="handleHttpType(item.result) === 'object'">
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <el-input
                  type="textarea"
                  :model-value="handleHttpJSON(item.result)"
                  :rows="6"
                  :readonly="true"
                />
              </el-scrollbar>
            </div>
            <div class="table-card-content http-table" v-else-if="handleHttpType(item.result) === 'array'">
              <el-table class="mask-table" ref="httpRef" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                <el-table-column type="index" label="序号" align="center" width="80" />
                <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed" :render-header="renderHeader">
                  <template v-if="it.dType === 'date'" #default="{ row }">
                    {{ formatTime(Number(row[it.data]) * 1000, 'MM-DD HH:mm') }}
                  </template>
                  <template v-else-if="it.data == 'value'" #default="{ row }">
                    {{ row.value }}
                  </template>
                  <template v-else #default="{ row }">
                    <el-link type="primary" v-if="handleHttpType(row[it.data]) === 'array'" @click="openTableDialog(row[it.data])">
                      <span style="font-size: 12px">详情</span>
                    </el-link>
                    <el-link type="primary" v-else-if="handleHttpType(row[it.data]) === 'object'" @click="openTableDialog([row[it.data]])">
                      <span style="font-size: 12px">详情</span>
                    </el-link>
                    <span v-else>
                      {{handleHttpType(row[it.data]) === 'array' || handleHttpType(row[it.data]) === 'object' ? '' : row[it.data] }}
                    </span>
                  </template>
                </el-table-column>
                <template #empty>
                  <el-empty description="暂无数据" style="height: 40vh" />
                </template>
              </el-table>
            </div>
            <div class="table-card-content http" v-else>
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <div class="table-card-content-text">
                  {{ item.result }}
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'invoice_recognition'">
          <div
            class="view-table-card card4"
            :class="{
              train: item.invoice_type === 'trainTickets' || item.invoice_type === 'bankreceipt',
              taxi: item.invoice_type === 'taxiTickets' || item.invoice_type === 'highwayToll',
              addedTax: item.invoice_type === 'invoice',
              fail: !item.type,
            }"
            v-for="(item, index) in dataList[key]" :key="'card' + key + index">
            <div class="table-card-content">
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <div class="card-box" v-if="item.type">
                  <div class="view-table-card-title-box">
                    <div class="view-table-card-title" :title="item.type">{{ item.type }}</div>
                    <!--                    <div class="view-table-card-title-btn" @click="openFile(item, 'file')">查看原件</div>-->
                  </div>
                  <div class="view-table-card-baseInfo">
                    <template v-for="(it, i) in (item.invoice_dict || []).filter((e: object) => !['tax_rate', 'amount', 'number'].includes(e.value))">
                      <div class="baseInfo-item" v-if="item[it.value]" :key="i">
                        <div class="baseInfo-item-label">
                          <div class="baseInfo-item-label-icon"></div>
                          <span>{{ it.name }}</span>
                        </div>
                        <div class="baseInfo-item-value" :title="item[it.value]">
                          {{ item[it.value] }}
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="view-table-card-line"></div>
                  <div class="view-table-card-baseInfo" style="min-height: 24px">
                    <div class="baseInfo-item" v-if="item.tax_rate">
                      <div class="baseInfo-item-label">
                        <span>税率</span>
                      </div>
                      <div class="baseInfo-item-value info" :title="item.tax_rate">
                        {{ item.tax_rate }}
                      </div>
                    </div>
                    <div class="baseInfo-item" v-if="item.amount">
                      <div class="baseInfo-item-label">
                        <span>{{ item.tax_rate ? '价税合计总额' : '金额' }}</span>
                      </div>
                      <div class="baseInfo-item-value info" :title="item.amount">
                        {{ item.amount }}
                      </div>
                    </div>
                  </div>
                  <div class="view-table-card-line"></div>
                  <div class="view-table-card-baseInfo">
                    <div class="baseInfo-item">
                      <div class="baseInfo-item-label">
                        <span>票据号码</span>
                      </div>
                      <div class="baseInfo-item-value" :title="item.number">
                        {{ item.number }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-box" v-else>
                  <div class="view-table-card-title-box">
                    <div class="view-table-card-title">未识别</div>
                    <!--                    <div class="view-table-card-title-btn" @click="openFile(item, 'file')">查看原件</div>-->
                  </div>
                  <div class="view-table-card-baseInfo info-box">
                    <van-icon name="cross" />
                    <span class="info-text">图片模糊无法识别</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
          <div class="view-table-card card4 loading" v-if="dataList[key].length !== (dataList[key][0]?.total || dataList[key].length)">
            <div class="table-card-content">
              <div class="loading-box">
                <div class="is-loading">
                  <img :src="loadingIcon" alt="" />
                </div>
                <span>
                  正在识别
                  <span style="margin-left: 8px">
                    {{`(${dataList[key].length}/${dataList[key][0]?.total || dataList[key].length})...` }}
                  </span>
                </span>
                <el-progress
                  class="loading-progress"
                  :stroke-width="4"
                  :percentage="handlePercentage(key)"
                  color="#0054D2"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'img_recognition'">
          <div class="view-table-card block image-card" v-for="(item, index) in dataList[key]" :key="index">
            <div class="image-box">
              <img v-for="(it, i) in item.url" :key="i" :src="it" alt="" />
            </div>
            <div class="image-text">
              <div class="image-text-title">识别结果：</div>
              <div class="image-text-desc">
                <div v-html="item.content"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'text_template'">
          <div class="view-table-card block">
            <div class="table-card-content table-card-content-scroll" style="height: auto" v-html="dataList[key]"></div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'db_query'">
          <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="index">
            <div class="table-card-content http-table">
              <el-table class="mask-table" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                <el-table-column type="index" label="序号" align="center" width="80" />
                <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
                  <template v-if="it.dType == 'date'" #default="{ row }">
                    {{ formatTime(Number(row[it.data]) * 1000, 'MM-DD HH:mm') }}
                  </template>
                  <template v-else-if="it.data == 'value'" #default="{ row }">
                    {{ row.value }}
                  </template>
                </el-table-column>
                <template #empty>
                  <el-empty description="暂无数据" style="height: 30vh" />
                </template>
              </el-table>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else-if="getNodeType(key) === 'ai_analyze'">
          <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card' + key + index">
            <div class="table-card-content">
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <markdownIt
                  class="table-card-content-scroll"
                  :content="handleMarkDown(item)"
                ></markdownIt>
              </el-scrollbar>
            </div>
          </div>
        </div>
        <div class="view-table-cards" v-else>
          <div class="view-table-card block" v-for="(item, index) in dataList[key]" :key="'card' + key + index">
            <div class="table-card-content-title">
              {{ item.desc }}
            </div>
            <div class="table-card-content http" v-if="handleHttpType(item.result) === 'object'">
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <el-input
                  type="textarea"
                  :model-value="handleHttpJSON(item.result)"
                  :rows="6"
                  :readonly="true"
                />
              </el-scrollbar>
            </div>
            <div class="table-card-content http-table" v-else-if="handleHttpType(item.result) === 'array'">
              <el-table class="mask-table" ref="httpRef" :data="item.tableData" border :show-overflow-tooltip="true" :highlight-current-row="true" :max-height="860">
                <el-table-column type="index" label="序号" align="center" width="80" />
                <el-table-column v-for="it in item.tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed" :render-header="renderHeader">
                  <template v-if="it.dType == 'date'" #default="{ row }">
                    {{ formatTime(Number(row[it.data]) * 1000, 'MM-DD HH:mm') }}
                  </template>
                  <template v-else-if="it.data == 'value'" #default="{ row }">
                    {{ row.value }}
                  </template>
                  <template v-else #default="{ row }">
                    <el-link type="primary" v-if="handleHttpType(row[it.data]) === 'array'" @click="openTableDialog(row[it.data])">
                      <span style="font-size: 12px">详情</span>
                    </el-link>
                    <el-link type="primary" v-else-if="handleHttpType(row[it.data]) === 'object'" @click="openTableDialog([row[it.data]])">
                      <span style="font-size: 12px">详情</span>
                    </el-link>
                    <span v-else>
                      {{handleHttpType(row[it.data]) === 'array' || handleHttpType(row[it.data]) === 'object' ? '' : row[it.data] }}
                    </span>
                  </template>
                </el-table-column>
                <template #empty>
                  <el-empty description="暂无数据" style="height: 40vh" />
                </template>
              </el-table>
            </div>
            <div class="table-card-content http" v-else>
              <el-scrollbar style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;">
                <div class="table-card-content-text">
                  {{ item.result }}
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>
    <!-- 表格详情 -->
    <van-popup
      v-model:show="tableDialog"
      destroy-on-close
      closeable
      round
      position="bottom"
      :style="{ height: '60vh' }"
      @closed="closeTableDialog"
    >
      <div class="history-container">
        <div class="history-title">表格详情</div>
        <div class="history-list">
          <el-table
            class="mask-table"
            :data="tableDialogData"
            border
            :show-overflow-tooltip="true"
            :highlight-current-row="true"
            height="100%"
          >
            <el-table-column type="index" label="序号" align="center" width="80" />
            <el-table-column v-for="it in tableDialogColumns" :key="it.data" :prop="it.data" :label="it.title" align="center" :width="it.width" :minWidth="it.minWidth" :fixed="it.fixed">
              <template v-if="it.dType == 'date'" #default="{ row }">
                {{ formatTime(row[it.data] * 1000, 'MM-DD HH:mm') }}
              </template>
              <template v-else-if="it.data == 'value'" #default="{ row }">
                {{ row.value }}
              </template>
            </el-table-column>
            <template #empty>
              <el-empty description="暂无数据" style="height: 30vh" />
            </template>
          </el-table>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import api from '@/api'
import { isNull } from '@/utils/validate.ts'
import * as echarts from 'echarts'
import FilePreview from './components/filePreview/index.vue'
import MapFrame from './components/mapFrame/index.vue'
import markdownIt from './components/markdownIt/index.vue'

import loadingIcon from '@/assets/images/loading.gif'
import {getCategoryColor} from "@/utils/componentCategories.ts";

//  route 参数
const route = useRoute()
const missionId = route.query.missionId || ''

// 接收props数据
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  historyExcId: {
    type: String,
    default: '',
  },
  recordData: {
    type: Array,
    default: () => [],
  },
  missionDetail: {
    type: Object,
    default: () => null,
  },
})

// 当前激活面板
const activeNames = ref([])

// 当前任务的配置的nodes
const nodes = computed(() => {
  return props.missionDetail?.nodes || []
})

// excel模板
const templateExcel = ref({})
// 根据当前任务是否有创建excel节点优先展示excel模板
const queryTaskExcel = () => {
  return new Promise((resolve) => {
    // 判断当前节点中是否有excel节点
    const haveExcel = nodes.value.some((it: object) => it.data?.componentType === 'excel_create')
    if (!missionId || !haveExcel) {
      resolve(true)
      return
    }
    api
      .AIGetTaskTemplateList(missionId, { baseURL: '' })
      .then((res) => {
        if (res?.config || res?.id) {
          templateExcel.value = {
            nodeId: 'templateExcel',
            configId: res.id || '',
            config: res.config || '',
            missionId: missionId,
            historyId: '',
            nodeType: 'excel_create',
          }
        }
        resolve(true)
      })
      .catch(() => {
        templateExcel.value = {}
        resolve(true)
      })
  })
}

// 节点配置show_monitor为true的节点id
const showMonitorIds = computed(() => {
  return nodes.value
    .filter((it: object) => it.data?.config?.show_monitor)
    .map((it: object) => it.id)
})
// 数据列表
const dataList = ref({})
const dataListKeys = ref([])
const collapse = ref(null)

interface MonitorItem {
  nodeId: string
  nodeType: string
  output: string
  describe: string
  title: string
}

// 处理节点数据
const handleMotitorList = async (rows: Array<MonitorItem>) => {
  activeNames.value = []
  rows.forEach((item: MonitorItem) => {
    // 节点配置show_monitor为true时展示数据
    if (showMonitorIds.value.includes(item.nodeId)) {
      const newData = JSON.parse(isNull(item.output) ? '{}' : item.output)
      if (!dataListKeys.value.includes(item.nodeId)) {
        dataListKeys.value.push(item.nodeId)
      }
      if (!dataList.value[item.nodeId]) {
        dataList.value[item.nodeId] = []
      }
      if (item.nodeType === 'ai_analyze') {
        if (typeof item.output === 'string')
          item.output = item.output.replace('<think>', '').replace('</think>', '')
        item.output = JSON.parse(item.output)
        dataList.value[item.nodeId] = [item]
      } else if (
        item.nodeType === 'http_post' ||
        item.nodeType === 'http_get' ||
        item.nodeType === 'http_request'
      ) {
        const childData = (newData && newData[`response_text`]) || {}
        // 重置数据
        dataList.value[item.nodeId] = []
        Object.keys(childData).forEach((it) => {
          dataList.value[item.nodeId].push(childData[it])
        })
        // 数据处理,根据结果类型转换为字符串或对象或数组
        dataList.value[item.nodeId] = handleDataList(item.nodeId)
      } else if (item.nodeType === 'python_execute') {
        const childData = (newData && newData[`python_output`]) || {}
        const variable =
          nodes.value.find((it: object) => it.id === item.nodeId)?.data?.config?.output_variable ||
          []
        const variableObj: any = {}
        variable.forEach((it: object) => {
          variableObj[it.variable] = it.desc
        })
        // 重置数据
        dataList.value[item.nodeId] = []
        if (handleHttpType(childData) === 'object') {
          Object.keys(childData).forEach((it) => {
            dataList.value[item.nodeId].push({
              desc: variableObj[it] || it,
              result: childData[it],
            })
          })
        } else {
          dataList.value[item.nodeId].push({
            desc: item.describe || item.title,
            result: childData,
          })
        }
        // 数据处理,根据结果类型转换为字符串或对象或数组
        dataList.value[item.nodeId] = handleDataList(item.nodeId)
      } else if (item.nodeType === 'excel_create') {
        // // 先删除模板数据
        // if (dataList.value.templateExcel) {
        //   delete dataList.value.templateExcel
        // }
        // // 添加新数据
        dataList.value[item.nodeId] = [item]
      } else if (item.nodeType === 'word_create') {
      } else if (item.nodeType === 'data_forecast') {
        dataList.value[item.nodeId] = [handleDataList(item.nodeId, item)]
      } else if (item.nodeType === 'water_shutoff_valve') {
        dataList.value[item.nodeId] = [handleDataList(item.nodeId, item)]
      }
      // 发票识别
      else if (item.nodeType === 'invoice_recognition') {
        dataList.value[item.nodeId] = (newData && newData[`recognition_results`]) || []
      }
      // 图片识别
      else if (item.nodeType === 'img_recognition') {
        const childData = (newData && newData[`recognition_result`]) || {}
        if (typeof childData === 'string') {
        } else {
          if (!childData.url) childData.url = []
          if (childData.content) childData.content = childData.content.replace(/\n/g, '<br>')
          dataList.value[item.nodeId] = [childData]
        }
      }
      // 文本模板
      else if (item.nodeType === 'text_template') {
        dataList.value[item.nodeId] = ((newData && newData[`output_variable`]) || '').replace(
          /\n/g,
          '<br>',
        )
      }
      // sql执行
      else if (item.nodeType === 'db_query') {
        let childData = []
        try {
          childData = newData?.result ? JSON.parse(newData.result) || [] : []
        } catch (error) {
          childData = []
        }
        // 数据处理,根据结果类型转换为字符串或对象或数组
        const tableColumns = Object.keys(childData[0] || {}).map((it) => ({
          data: it,
          title: it,
        }))
        item = {
          ...item,
          tableColumns,
          result: childData,
          pageSizes: [4, 8, 12, 16],
          pagination: {
            page: 1,
            size: 8,
            total: childData.length || 0,
          },
        }
        handleTableList(item)
        dataList.value[item.nodeId] = [item]
        if (dataListKeys.value.indexOf(item.nodeId) < 0) dataListKeys.value.push(item.nodeId)
        if (tableColumns.length === 0) {
          dataListKeys.value = dataListKeys.value.filter((it) => it !== item.nodeId)
        }
      }
      // 节点通用展示
      else {
        const codes = nodes.value.find((it: object) => it?.data?.componentType === item.nodeType)
          ?.data?.outputs
        const code = (codes && codes[0]) || ''
        const childData = (newData && newData[code]) || {}
        // 重置数据
        dataList.value[item.nodeId] = []
        if (handleHttpType(childData) === 'object') {
          Object.keys(childData).forEach((it) => {
            dataList.value[item.nodeId].push({
              desc: it,
              result: childData[it],
            })
          })
        } else {
          dataList.value[item.nodeId].push({
            desc: item.describe || item.title,
            result: childData,
          })
        }
        // 数据处理,根据结果类型转换为字符串或对象或数组
        dataList.value[item.nodeId] = handleDataList(item.nodeId)
      }
    }
  })
  await nextTick()
  collapse.value?.toggleAll(true)
}

const tableDialog = ref(false)
const tableDialogColumns = ref([])
const tableDialogData = ref([])
// http结果表格数据为数组时，处理表格数据
const openTableDialog = (data: Array[]) => {
  tableDialog.value = true
  tableDialogColumns.value = Object.keys(data[0] || {}).map((it) => ({
    data: it,
    title: it,
  }))
  tableDialogData.value = data
}
const closeTableDialog = () => {
  tableDialog.value = false
  tableDialogColumns.value = []
  tableDialogData.value = []
}

// 根据 nodeId 获取 nodeType
const getNodeType = (nodeId: string) => {
  const node = props.recordData.find((it: object) => it.nodeId === nodeId)
  return node?.nodeType || ''
}
// 根据 nodeId 获取 title
const getNodeTitle = (nodeId: string) => {
  const node = props.recordData.find((item: object) => item.nodeId === nodeId)
  return node?.title
}
// 根据 nodeId 获取 excTime
const getNodeExcTime = (nodeId: string) => {
  const node = props.recordData.find((item: object) => item.nodeId === nodeId)
  return node?.excTime ? moment(node?.excTime).format('HH:mm:ss') : ''
}
// 根据 nodeId 获取节点图标及图标背景色
const handleNodesIcon = (type: string, nodeId: string) => {
  const node = nodes.value.find((it: object) => it.id === nodeId)
  if (type === 'icon') {
    return node?.data?.icon || ''
  }
  if (type === 'color') {
    return getCategoryColor(node?.data?.category)
  }
}

// 处理数据结构
const handleDataList = (key: string, item?: object) => {
  const nodeType = getNodeType(key)
  if (nodeType === 'http_post' || nodeType === 'http_get' || nodeType === 'http_request') {
    const data = JSON.parse(JSON.stringify(dataList.value[key]))
    data.forEach((item: object) => {
      if (handleHttpType(item.result) === 'object') {
        item.result = item.result ? item.result : {}
      } else if (handleHttpType(item.result) === 'array') {
        if (item.result && item.result[0]) {
          if (typeof item.result[0] === 'object') {
            item.tableColumns = Object.keys(item.result[0]).map((key) => {
              return {
                data: key,
                title: key,
                dType: key === 'st' || key === 'et' ? 'date' : null,
              }
            })
          } else {
            item.tableColumns = item.tableColumns = [{ data: 'value', title: item.desc }]
            item.result = item.result.map((it: any) => {
              return {
                value: it,
              }
            })
          }
        } else {
          item.tableColumns = [{ data: 'value', title: item.desc }]
          item.result = []
        }
        item.pageSizes = [4, 8, 12, 16]
        item.pagination = {
          page: 1,
          size: 8,
          total: item.result?.length || 0,
        }
        handleTableList(item)
      }
    })
    return data
  } else if (nodeType === 'data_forecast') {
    const objData = item ? item : dataList.value[key][0] || {}
    const dataItem = JSON.parse(isNull(objData.output) ? '{}' : objData.output)
    const data = JSON.parse(dataItem?.forecast_data || '{}')
    const obj = {
      ...data,
      nodeId: item.nodeId,
      tableData: (data.forecast_time || []).map((it, index) => {
        return {
          time: it * 1000,
          value: data.forecast_data && data.forecast_data[index],
        }
      }),
      historyTableData: (data.his_win_time || []).map((it, index) => {
        return {
          time: it * 1000,
          value: data.his_win_data && data.his_win_data[index],
        }
      }),
      tableColumns: [
        { data: 'time', title: '时间' },
        { data: 'value', title: '预测数据' }, // , width: 160
      ],
    }
    return obj
  } else if (nodeType === 'water_shutoff_valve') {
    const objData = item ? item : dataList.value[key][0] || {}
    try {
      objData.output = JSON.parse(objData.output)
    } catch (e) {
      objData.output = {}
    }
    return objData
  } else {
    const data = JSON.parse(JSON.stringify(dataList.value[key]))
    if (Array.isArray(data)) {
      data.forEach((item) => {
        if (handleHttpType(item.result) === 'object') {
          item.result = item.result ? item.result : {}
        } else if (handleHttpType(item.result) === 'array') {
          if (item.result && item.result[0]) {
            if (typeof item.result[0] === 'object') {
              item.tableColumns = Object.keys(item.result[0]).map((key) => {
                return {
                  data: key,
                  title: key,
                  dType: key === 'st' || key === 'et' ? 'date' : null,
                }
              })
            } else {
              item.tableColumns = item.tableColumns = [{ data: 'value', title: item.desc }]
              item.result = item.result.map((it) => {
                return {
                  value: it,
                }
              })
            }
          } else {
            item.tableColumns = [{ data: 'value', title: item.desc }]
            item.result = []
          }
          item.pageSizes = [4, 8, 12, 16]
          item.pagination = {
            page: 1,
            size: 8,
            total: item.result?.length || 0,
          }
          handleTableList(item)
        }
      })
    }
    return data
  }
}
// 处理http结果数据类型
const handleHttpType = (data: object | string) => {
  let type = ''
  const row = data || ''
  if (typeof row === 'object' && row !== null) {
    if (row.constructor === Array) {
      type = 'array'
    } else {
      type = 'object'
    }
  } else {
    type = typeof row
  }
  return type
}
// 处理表格数据
const handleTableList = (item: object) => {
  item.tableData = item.result
}
// 处理http结果json数据
const handleHttpJSON = (data: object) => {
  return JSON.stringify(data || {}, null, 2)
}
// 处理markdown表格数据
const handleMarkDown = (data: object) => {
  let tableHtml = ''
  if (data.nodeType === 'ai_analyze') {
    const newData = data.output
    tableHtml = newData[`ai_analyze_response`] || ''
    // if (tableHtml) tableHtml += `<p class="markdown-time">${utils.formatTime(data.excTime*1000)}</p>`
  }
  return tableHtml
}
// 处理echart数据
const echartRefs = ref<Record<string, HTMLElement | null>>({})
const initCharts = async (key: number, obj: object) => {
  await nextTick()
  try {
    const chartRef = echartRefs.value[key + 'echarts']
    if (!chartRef) return
    const myChart = echarts.init(chartRef[0])
    const date = obj.historyTableData.map((it) => {
      return moment(it.time).format('MM-DD HH:mm')
    })
    obj.tableData.forEach((it: object) => {
      date.push(moment(it.time).format('MM-DD HH:mm'))
    })
    const data = []
    const hisData = obj.historyTableData.map((it: object) => {
      data.push(null)
      return it.value
    })
    obj.tableData.map((it) => {
      data.push(it.value)
    })
    const options = {
      tooltip: {
        trigger: 'axis',
        position: (pt: Array[]) => {
          return [pt[0], '10%']
        },
      },
      toolbox: {
        show: false, // 禁用工具箱
      },
      legend: {
        data: ['历史值', '预测值'],
        itemWidth: 12,
        itemHeight: 2,
        bottom: '50px',
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          //x轴线的颜色以及宽度
          show: true,
          lineStyle: {
            color: '#BCBFC3',
            width: 0,
            type: 'solid',
          },
        },
        axisLabel: {
          margin: 16,
        },
        data: date,
      },
      yAxis: {
        type: 'value',
        axisLine: {
          //x轴线的颜色以及宽度
          show: true,
          lineStyle: {
            color: '#BCBFC3',
            width: 0,
            type: 'solid',
          },
        },
        axisLabel: {
          margin: 16,
        },
        boundaryGap: [0, '100%'],
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 10,
        },
        {
          start: 0,
          end: 10,
        },
      ],
      series: [
        {
          name: '历史值',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: '#999999',
          },
          data: hisData,
        },
        {
          name: '预测值',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: '#FF8E32',
          },
          data: data,
        },
      ],
    }
    myChart.setOption(options)
  } catch (e) {}
}
// table表头计算宽度
const renderHeader = ({ column }) => {
  //创建一个元素用于存放表头信息
  const span = document.createElement('span')
  // 将表头信息渲染到元素上
  span.innerText = column.label
  // 在界面中添加该元素
  document.body.appendChild(span)
  //获取该元素的宽度（包含内外边距等信息）
  const spanWidth = span.getBoundingClientRect().width + 20 //渲染后的 div 内左右 padding 都是 10，所以 +20
  //判断是否小于element的最小宽度，两者取最大值
  column.minWidth = column.minWidth > spanWidth ? column.minWidth : spanWidth
  // 计算完成后，删除该元素
  document.body.removeChild(span)
  return column.label
}
// 处理时间
const formatTime = (data?: string | number, format?: string) => {
  if (!data) return ''
  return moment(data).format(format)
}
// 发票节点时间轴
const handlePercentage = (key) => {
  let percentage = 20
  if (dataList.value[key] && dataList.value[key].length > 0) {
    percentage = Math.floor(
      (dataList.value[key].length / (dataList.value[key][0].total || dataList.value[key].length)) *
        100,
    )
  }
  return percentage
}

// 清空重置数据
const clearData = () => {
  dataList.value = {}
  dataListKeys.value = []
  echartRefs.value = {}
}

watch(
  () => props.isLoading,
  (newVal) => {
    if(newVal){
      clearData()
    }
  }
)

// 监听recordData变化
watch(
  () => props.recordData,
  async (newVal, oldVal) => {
    if (
      newVal &&
      oldVal &&
      JSON.stringify(newVal) !== JSON.stringify(oldVal) &&
      JSON.stringify(newVal) !== '[]'
    ) {
      // 清除数据
      clearData()
      // 查询excel模板
      queryTaskExcel()
      // 处理监控数据
      handleMotitorList(newVal)
    }
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
.monitor {
  overflow-y: auto;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  background: #f1f3f5;

  :deep(.van-collapse) {
    .van-collapse-item {
      border-radius: 4px;
      overflow: hidden;

      &:not(:first-child) {
        margin-top: 8px;
      }
    }
  }

  .title-box{
    display: inline-flex;
    align-items: center;
  }

  .icon-box {
    width: 24px;
    height: 24px;
    color: #ffffff;
    font-size: 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  .title {
    font-weight: bold;
  }

  .time {
    color: #999999;
    font-size: 12px;
    margin-left: 8px;
  }
}

.view-table-card-box {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // margin-bottom: 16px;
  &:last-child {
    border-bottom: none;
  }

  .view-table-card-box-title {
    height: 52px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-title-left {
      display: flex;
      align-items: center;


    }

    .card-title-right {
      span {
        font-weight: 400;
        font-size: 12px;
        color: #bcbfc3;
      }
    }
  }

  .view-table-cards-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 46px;
    background: #f7f7f9;
    border-radius: 4px;
    padding: 0 16px;
    margin-bottom: 16px;

    .cards-title-item {
      display: flex;
      justify-content: space-between;

      .cards-title-item-label {
        font-weight: 400;
        font-size: 12px;
        color: #5c5f66;
        line-height: 24px;
        display: flex;
        align-items: center;
      }

      .cards-title-item-value {
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 24px;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.success {
          color: #67c23a;
        }

        &.fail {
          color: #f56c6c;
        }
      }
    }
  }

  .view-table-cards {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .view-table-card {
      width: 100%;
      border: 1px solid #e6e7e9;
      margin-bottom: 16px;
      box-sizing: border-box;
      display: inline-block;
      position: relative;
      border-radius: 4px;

      &.image-card {
        display: flex;
      }

      .image-box {
        width: calc(100% - 367px);
        max-height: 480px;
        padding: 16px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        overflow-y: auto;

        img {
          max-width: 80%;
          max-height: 430px;
          margin-bottom: 12px;
          object-fit: contain;
        }
      }

      .image-text {
        width: 360px;
        max-height: 480px;
        margin-left: 16px;
        // color: red;
        .image-text-title {
          font-weight: 500;
          font-size: 14px;
          color: #bcbfc3;
          margin-bottom: 10px;
        }

        .image-text-desc {
          height: calc(100% - 28px);
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          overflow-y: auto;
        }
      }

      .table-card-close {
        width: 14px;
        height: 0;
        float: right;
        margin-right: 12px;
        transform: translateY(10px);

        i {
          color: #222222;
          font-size: 14px;
          cursor: pointer;
        }
      }

      .table-card-content-scroll {
        padding-right: 20px;
        line-height: 2;
      }

      .table-card-content-title {
        font-weight: 400;
        font-size: 12px;
        color: #bcbfc3;
        margin-bottom: 12px;
      }

      .table-card-content {
        width: 100%;
        // height: 300px;
        color: #333333;

        .markdown-string {
          padding: 16px;

          :deep(.markdown-time) {
            color: #999999;
          }
        }

        :deep(table) {
          width: 100%;
          border-spacing: 0px;
          font-size: 12px;
          color: #222222;

          thead {
            tr {
              position: sticky;
              top: 0;
              z-index: 5;
            }
          }

          tr {
            th:first-child {
              width: 30%;
              border-right: 1px solid #ececec;
            }

            th {
              // text-align: left;
              height: 40px;
              line-height: 40px;
              // padding-left: 15px;
              font-weight: 500;
              color: #5c5f66;
              background: #f7f7f9;
            }

            td:first-child {
              width: 30%;
              background: #fefeff;
            }

            td {
              // text-align: left;
              height: 40px;
              line-height: 40px;
              // padding-left: 15px;
              //border-top: 1px solid #ECECEC;
              border-right: 1px solid #ececec;
            }
          }
        }
      }

      .view-table-card-title-box {
        display: flex;
        justify-content: space-between;
        position: relative;

        .view-table-card-title-bg {
          width: 80px;
          height: 10px;
          background: #27b47a33;
          border-radius: 100px 0 0 0;
          position: absolute;
          top: 12px;
        }

        .view-table-card-title {
          font-weight: 500;
          font-size: 14px;
          color: #222222;
          margin-bottom: 20px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .view-table-card-title-btn {
          font-weight: 400;
          font-size: 12px;
          color: #0054d2;
          cursor: pointer;
        }
      }

      .view-table-card-baseInfo {
        .baseInfo-item {
          display: flex;
          justify-content: space-between;

          .baseInfo-item-label {
            font-weight: 400;
            font-size: 12px;
            color: #5c5f66;
            line-height: 24px;
            display: flex;
            align-items: center;
            margin-right: 16px;

            .baseInfo-item-label-icon {
              width: 6px;
              height: 6px;
              background: #d4f0e4;
              border-radius: 50%;
              display: inline-block;
              margin-right: 4px;
            }
          }

          .baseInfo-item-value {
            flex: 1;
            font-weight: 400;
            font-size: 12px;
            color: #222222;
            line-height: 24px;
            text-align: right;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.info {
              font-weight: 700;
              color: #e6a23c;
            }
          }
        }

        &.info-box {
          height: 190px;
          display: flex;
          justify-content: center;
          align-items: center;

          .info-icon {
            font-size: 14px;
            margin-right: 8px;
          }

          .info-text {
            font-weight: 400;
            font-size: 12px;
            color: #5c5f66e6;
          }
        }
      }

      .view-table-card-line {
        height: 0;
        border: 1px dashed #dddddd;
        margin: 10px 0;
      }
    }

    .view-table-card:nth-child(3n) {
      margin-right: 0;
    }

    .view-table-card.card4 {
      width: 100%;

      &.loading {
        border: none;
      }

      .table-card-content {
        //min-height: 285px;

        .card-box {
          padding: 16px;
          box-sizing: border-box;
        }

        .loading-box {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;

          .is-loading {
            img {
              width: 40px;
            }
          }

          span {
            font-weight: 400;
            font-size: 12px;
            color: #bcbfc3;
            margin-top: 16px;
          }

          .loading-progress {
            width: 200px;
            margin-top: 16px;

            :deep(.el-progress-bar__outer) {
              background-color: #e9eaee;
            }

            :deep(.el-progress__text) {
              font-size: 12px !important;
              color: #222222 !important;
            }
          }
        }
      }
    }

    //.view-table-card.card4:nth-child(3n) {
    //  margin-right: 16px;
    //}
    //.view-table-card.card4:nth-child(4n) {
    //  margin-right: 0;
    //}
    .view-table-card.train {
      .baseInfo-item-label-icon {
        color: #3b94e6;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -0.5px;
        width: calc(100% + 1px);
        height: 5px;
        background: #3b94e6;
        border-radius: 4px 4px 0 0;
      }

      .view-table-card-title-bg {
        background: rgba(59, 148, 230, 0.2);
      }
    }

    .view-table-card.taxi {
      .baseInfo-item-label-icon {
        color: #ffe9c1;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -0.5px;
        width: calc(100% + 1px);
        height: 5px;
        background: #ffa125;
        border-radius: 4px 4px 0 0;
      }

      .view-table-card-title-bg {
        background: rgba(59, 148, 230, 0.2);
      }
    }

    .view-table-card.addedTax {
      .baseInfo-item-label-icon {
        color: #d4f0e4;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -0.5px;
        width: calc(100% + 1px);
        height: 5px;
        background: #27b47a;
        border-radius: 4px 4px 0 0;
      }

      .view-table-card-title-bg {
        background: rgba(39, 180, 122, 0.2);
      }
    }

    .view-table-card.fail {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -0.5px;
        width: calc(100% + 1px);
        height: 5px;
        background: #c1c5ce;
        border-radius: 4px 4px 0 0;
      }
    }

    .block {
      width: 100%;
      margin-right: 0;
      border: none;

      .table-card-content {
        // height: 400px;
        &.http {
          height: auto;
        }

        // &.http-table {
        //   height: 300px;
        // }
      }
    }

    .excel {
      width: 100%;
      margin-right: 0;
      border: none;

      .table-card-content {
        height: 100%;
      }
    }

    .left-table {
      width: 100%;
      height: 440px;
      margin-right: 16px;
      margin-bottom: 20px;
    }

    .right-echarts {
      width: 100%;
      height: 440px;
      margin-bottom: 20px;

      .right-table-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c5f66;

        .right-table-text-bold {
          font-weight: 500;
          font-size: 16px;
          color: #0054d2;
          margin-left: 8px;
          margin-right: 40px;
        }
      }

      .echarts-box {
        width: 100%;
        height: 100%;
      }
    }
  }

  .view-table-pagination {
    padding: 16px;
    display: flex;
    justify-content: flex-end;

    :deep(.el-pagination.is-background .el-pager li.is-active) {
      background-color: rgba(0, 84, 217, 0.08);
      color: rgba(0, 84, 217, 1);
    }
  }
}

.view-table-card-box:last-child {
  margin-bottom: 0;
}

.history-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .history-title {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    color: #333333;
    flex-shrink: 0;
    font-weight: bold;
    text-align: center;
  }

  .history-list {
    flex: 1;
    overflow: hidden;

    .active {
      background: #efefef;
    }
  }

  :deep(table) {
    width: 100%;
    border-spacing: 0px;
    font-size: 12px;
    color: #222222;

    thead {
      tr {
        position: sticky;
        top: 0;
        z-index: 5;
      }
    }

    tr {
      th:first-child {
        width: 30%;
        border-right: 1px solid #ececec;
      }

      th {
        // text-align: left;
        height: 40px;
        line-height: 40px;
        // padding-left: 15px;
        font-weight: 500;
        color: #5c5f66;
        background: #f7f7f9;
      }

      td:first-child {
        width: 30%;
        background: #fefeff;
      }

      td {
        // text-align: left;
        height: 40px;
        line-height: 40px;
        // padding-left: 15px;
        //border-top: 1px solid #ECECEC;
        border-right: 1px solid #ececec;
      }
    }
  }
}

:deep(.mask-table) {
  thead {
    color: #999999 !important;
  }

  thead th {
    font-weight: 500 !important;
  }

  th.el-table__cell.is-leaf,
  .mask-table td.el-table__cell {
    border-bottom: 1px solid #ebeef5 !important;
  }

  .el-table__cell {
    border-right: 1px solid #ebeef5 !important;
  }

  .el-table__border-left-patch {
    background-color: #ebeef5;
  }

  &::after {
    background-color: #ebeef5 !important;
  }
}
</style>
