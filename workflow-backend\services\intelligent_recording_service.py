"""
智能录制服务
智能录制功能实现
支持一键录制、自动截屏、实时操作捕获、智能元素识别等
"""

import subprocess
import os
import tempfile
import json
import uuid
import time
import re
import threading
import queue
import asyncio
import base64
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import logging
from datetime import datetime
from PIL import Image, ImageDraw
import io

logger = logging.getLogger(__name__)


class ScreenshotCapture:
    """自动截屏捕获组件"""

    def __init__(self, temp_dir: str):
        self.temp_dir = temp_dir
        self.screenshot_dir = os.path.join(temp_dir, "screenshots")
        os.makedirs(self.screenshot_dir, exist_ok=True)

    def capture_screenshot(self, operation_id: str, region: Optional[Dict] = None) -> str:
        """捕获截屏"""
        try:
            screenshot_path = os.path.join(self.screenshot_dir, f"screenshot_{operation_id}.png")

            # 这里使用Playwright的截屏功能
            # 实际实现中会通过Playwright API进行截屏
            # 暂时创建一个占位图片
            img = Image.new('RGB', (800, 600), color='white')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), f"Screenshot for operation {operation_id}", fill='black')
            img.save(screenshot_path)

            logger.info(f"截屏已保存: {screenshot_path}")
            return screenshot_path

        except Exception as e:
            logger.error(f"截屏失败: {e}")
            return ""


class OperationAnalyzer:
    """操作分析器"""

    def __init__(self):
        self.operation_patterns = {
            'click': [r'\.click\(\)', r'page\.click\('],
            'fill': [r'\.fill\(', r'page\.fill\('],
            'type': [r'\.type\(', r'page\.type\('],
            'goto': [r'page\.goto\('],
            'screenshot': [r'page\.screenshot\(']
        }

    def classify_operation(self, operation_data: str) -> str:
        """分类操作类型"""
        for op_type, patterns in self.operation_patterns.items():
            for pattern in patterns:
                if re.search(pattern, operation_data):
                    return op_type
        return 'unknown'

    def extract_element_info(self, operation_data: str, operation_type: str) -> Dict[str, Any]:
        """提取元素信息"""
        element_info = {}

        if operation_type == 'click':
            # 提取点击目标
            if '.getByRole(' in operation_data:
                role_match = re.search(r'\.getByRole\(["\']([^"\']+)["\']', operation_data)
                name_match = re.search(r'name:\s*["\']([^"\']+)["\']', operation_data)
                if role_match:
                    element_info['role'] = role_match.group(1)
                    element_info['name'] = name_match.group(1) if name_match else ''
            elif 'page.click(' in operation_data:
                selector_match = re.search(r'page\.click\(["\']([^"\']+)["\']', operation_data)
                if selector_match:
                    element_info['selector'] = selector_match.group(1)

        elif operation_type == 'fill':
            print(f"[DEBUG] 解析fill操作: {operation_data}")
            fill_match = re.search(r'\.fill\(["\']([^"\']+)["\'],\s*["\']([^"\']*)["\']', operation_data)
            if fill_match:
                element_info['selector'] = fill_match.group(1)
                element_info['text'] = fill_match.group(2)
                print(f"[DEBUG] 提取到selector: {element_info['selector']}, text: {element_info['text']}")
            else:
                print(f"[DEBUG] fill操作正则匹配失败: {operation_data}")

        elif operation_type == 'goto':
            url_match = re.search(r'page\.goto\(["\']([^"\']+)["\']', operation_data)
            if url_match:
                element_info['url'] = url_match.group(1)

        return element_info


class WorkflowGenerator:
    """工作流生成器"""

    def __init__(self):
        self.node_counter = 0

    def generate_node(self, operation_type: str, element_info: Dict[str, Any],
                     screenshot_path: str = "") -> Dict[str, Any]:
        """生成工作流节点"""
        self.node_counter += 1
        node_id = f"node_{self.node_counter}"

        if operation_type == 'click':
            if 'role' in element_info:
                label = f"点击{element_info['role']}"
                if element_info.get('name'):
                    label += f": {element_info['name']}"
                selector = f"role={element_info['role']}"
                if element_info.get('name'):
                    selector += f", name='{element_info['name']}'"
            else:
                label = "点击元素"
                selector = element_info.get('selector', '')

            return {
                'id': node_id,
                'type': 'click',
                'label': label,
                'category': 'interaction',
                'config': {
                    'selector': selector,
                    'timeout': 10000,
                    'force': False,
                    'delay': 0
                },
                'inputs': ['page_id'],
                'outputs': [],
                'screenshot': screenshot_path
            }

        elif operation_type == 'fill':
            return {
                'id': node_id,
                'type': 'fill_text',
                'label': '填写文本',
                'category': 'interaction',
                'config': {
                    'selector': element_info.get('selector', ''),
                    'text': element_info.get('text', ''),
                    'timeout': 10000,
                    'force': False
                },
                'inputs': ['page_id', 'text'],
                'outputs': [],
                'screenshot': screenshot_path
            }

        elif operation_type == 'goto':
            return {
                'id': node_id,
                'type': 'go_to',
                'label': '导航到页面',
                'category': 'browser',
                'config': {
                    'url': element_info.get('url', ''),
                    'timeout': 30000
                },
                'inputs': ['page_id'],
                'outputs': [],
                'screenshot': screenshot_path
            }

        return {
            'id': node_id,
            'type': operation_type,
            'label': f'{operation_type}操作',
            'category': 'interaction',
            'config': {},
            'inputs': ['page_id'],
            'outputs': [],
            'screenshot': screenshot_path
        }

    def add_browser_nodes(self, nodes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """添加浏览器相关节点"""
        if not nodes:
            return nodes

        # 添加浏览器创建节点
        browser_node = {
            'id': 'browser_node',
            'type': 'new_browser',
            'label': '创建浏览器',
            'category': 'browser',
            'config': {
                'browser': 'chromium',
                'headless': False,
                'timeout': 30000
            },
            'inputs': [],
            'outputs': ['browser_id'],
            'screenshot': ''
        }

        # 检查是否有goto操作
        goto_nodes = [node for node in nodes if node['type'] == 'go_to']

        if goto_nodes:
            # 使用第一个goto的URL创建页面节点
            first_url = goto_nodes[0]['config']['url']
            page_node = {
                'id': 'page_node',
                'type': 'new_page',
                'label': '打开新页面',
                'category': 'browser',
                'config': {
                    'url': first_url,
                    'viewport_width': 1920,
                    'viewport_height': 1080
                },
                'inputs': ['browser_id'],
                'outputs': ['page_id'],
                'screenshot': ''
            }

            # 移除第一个goto节点
            nodes = [node for node in nodes if node != goto_nodes[0]]
        else:
            # 创建空白页面
            page_node = {
                'id': 'page_node',
                'type': 'new_page',
                'label': '打开新页面',
                'category': 'browser',
                'config': {
                    'url': 'about:blank',
                    'viewport_width': 1920,
                    'viewport_height': 1080
                },
                'inputs': ['browser_id'],
                'outputs': ['page_id'],
                'screenshot': ''
            }

        return [browser_node, page_node] + nodes


class IntelligentRecordingService:
    """智能录制服务 - 智能录制功能"""

    def __init__(self):
        self.active_recordings: Dict[str, Dict] = {}
        self.temp_dir = tempfile.mkdtemp(prefix="intelligent_recording_")

        # 录制状态管理
        self.recording_status = "idle"  # idle, recording, paused, stopping
        self.current_recording_id = None

        # 组件初始化
        self.screenshot_capture = ScreenshotCapture(self.temp_dir)
        self.operation_analyzer = OperationAnalyzer()
        self.workflow_generator = WorkflowGenerator()

        # 操作队列和处理
        self.operation_queue = queue.Queue()
        self.processed_operations = []
        self.generated_nodes = []
        self.operation_processor = None

        # 录制统计
        self.recording_stats = {
            'start_time': None,
            'operation_count': 0,
            'last_operation_time': None,
            'total_duration': 0
        }

        # 回调函数
        self.operation_callbacks: List[Callable] = []

        logger.info("智能录制服务初始化完成")

    def _find_npx_command(self) -> str:
        """查找npx命令的完整路径"""
        import shutil

        # 首先尝试直接找到npx
        npx_path = shutil.which('npx')
        if npx_path:
            logger.info(f"找到npx路径: {npx_path}")
            return npx_path

        # 在Windows上，尝试常见的Node.js安装路径
        if os.name == 'nt':  # Windows
            possible_paths = [
                r"C:\Program Files\nodejs\npx.cmd",
                r"C:\Program Files (x86)\nodejs\npx.cmd",
                os.path.expanduser(r"~\AppData\Roaming\npm\npx.cmd"),
                os.path.expanduser(r"~\AppData\Local\npm\npx.cmd")
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    logger.info(f"找到npx路径: {path}")
                    return path

        # 如果都找不到，返回默认的npx
        logger.warning("未找到npx的完整路径，使用默认命令")
        return 'npx'

    def start_recording(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """开始智能录制 - 一键录制"""
        try:
            if self.recording_status != "idle":
                return {
                    'success': False,
                    'error': '已有录制会话正在进行中'
                }

            recording_id = str(uuid.uuid4())
            self.current_recording_id = recording_id

            # 初始化录制状态
            self.recording_status = "recording"
            self.recording_stats = {
                'start_time': datetime.now(),
                'operation_count': 0,
                'last_operation_time': None,
                'total_duration': 0
            }

            # 清空队列和历史
            while not self.operation_queue.empty():
                self.operation_queue.get()
            self.processed_operations.clear()
            self.generated_nodes.clear()
            self.workflow_generator.node_counter = 0

            # 启动智能录制引擎
            result = self._start_intelligent_recording_engine(recording_id, config)

            if result['success']:
                # 启动操作处理线程
                self._start_operation_processor()

                logger.info(f"智能录制启动成功，录制ID: {recording_id}")
                return {
                    'success': True,
                    'recording_id': recording_id,
                    'message': '智能录制已开始，浏览器将自动打开',
                    'features': {
                        'real_time_capture': True,
                        'auto_screenshot': True,
                        'smart_element_recognition': True,
                        'operation_optimization': True
                    }
                }
            else:
                self.recording_status = "idle"
                self.current_recording_id = None
                return result

        except Exception as e:
            self.recording_status = "idle"
            self.current_recording_id = None
            logger.error(f"启动智能录制失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _start_intelligent_recording_engine(self, recording_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动智能录制引擎"""
        try:
            browser = config.get('browser', 'chromium')

            # 创建增强的录制脚本
            enhanced_script_path = os.path.join(self.temp_dir, f"enhanced_recording_{recording_id}.py")

            # 查找npx命令
            npx_cmd = self._find_npx_command()

            # 构建增强的录制命令
            cmd = [
                npx_cmd, 'playwright', 'codegen',
                '--browser', browser,
                '--output', enhanced_script_path,
                '--target', 'python-async'  # 使用异步Python目标
            ]

            logger.info(f"启动智能录制命令: {' '.join(cmd)}")
            logger.info(f"工作目录: {self.temp_dir}")

            # 在Windows上，需要使用shell=True
            shell_needed = os.name == 'nt'

            # 启动录制进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.temp_dir,
                shell=shell_needed,
                encoding='utf-8',
                errors='ignore'
            )

            # 等待进程启动
            time.sleep(2)

            if process.poll() is not None:
                try:
                    stdout, stderr = process.communicate(timeout=5)
                    error_msg = stderr or stdout or "未知错误"
                except Exception as e:
                    error_msg = f"无法获取进程输出: {str(e)}"

                logger.error(f"智能录制引擎启动失败: {error_msg}")
                return {
                    'success': False,
                    'error': f'智能录制引擎启动失败: {error_msg}'
                }

            # 保存录制信息
            self.active_recordings[recording_id] = {
                'process': process,
                'config': config,
                'output_file': enhanced_script_path,
                'start_time': time.time(),
                'status': 'recording',
                'type': 'intelligent'
            }

            logger.info(f"智能录制引擎启动成功，PID: {process.pid}")
            return {'success': True}

        except FileNotFoundError as e:
            error_msg = f'找不到npx命令。请确保Node.js已正确安装并在PATH中。错误: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            logger.error(f"启动智能录制引擎失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _start_operation_processor(self):
        """启动操作处理线程"""
        if self.operation_processor and self.operation_processor.is_alive():
            return

        self.operation_processor = threading.Thread(
            target=self._process_operations_continuously,
            daemon=True
        )
        self.operation_processor.start()
        logger.info("操作处理线程已启动")

    def _process_operations_continuously(self):
        """持续处理操作队列"""
        while self.recording_status in ["recording", "paused"]:
            try:
                # 从队列中获取操作（超时1秒）
                operation = self.operation_queue.get(timeout=1.0)

                if self.recording_status == "recording":
                    # 处理操作
                    processed_op = self._process_single_operation(operation)
                    if processed_op:
                        self.processed_operations.append(processed_op)
                        self.recording_stats['operation_count'] += 1
                        self.recording_stats['last_operation_time'] = datetime.now()

                        # 生成工作流节点
                        node = self._generate_workflow_node(processed_op)
                        if node:
                            self.generated_nodes.append(node)

                        # 通知回调函数
                        self._notify_operation_callbacks(processed_op)

                self.operation_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"处理操作时出错: {e}")

    def _process_single_operation(self, operation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理单个操作"""
        try:
            operation_data = operation.get('data', '')

            # 智能操作分类
            operation_type = self.operation_analyzer.classify_operation(operation_data)

            if operation_type == 'unknown':
                return None

            # 提取元素信息
            element_info = self.operation_analyzer.extract_element_info(operation_data, operation_type)

            # 生成操作ID并截屏
            operation_id = str(uuid.uuid4())
            screenshot_path = self.screenshot_capture.capture_screenshot(operation_id)

            return {
                'id': operation_id,
                'timestamp': datetime.now().isoformat(),
                'type': operation_type,
                'data': operation_data,
                'element_info': element_info,
                'screenshot': screenshot_path,
                'processed': True
            }

        except Exception as e:
            logger.error(f"处理操作失败: {e}")
            return None

    def _generate_workflow_node(self, processed_op: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """生成工作流节点"""
        try:
            return self.workflow_generator.generate_node(
                processed_op['type'],
                processed_op['element_info'],
                processed_op['screenshot']
            )
        except Exception as e:
            logger.error(f"生成工作流节点失败: {e}")
            return None

    def _notify_operation_callbacks(self, operation: Dict[str, Any]):
        """通知操作回调函数"""
        for callback in self.operation_callbacks:
            try:
                callback(operation)
            except Exception as e:
                logger.error(f"回调函数执行失败: {e}")

    def add_operation_callback(self, callback: Callable):
        """添加操作回调函数"""
        self.operation_callbacks.append(callback)

    def remove_operation_callback(self, callback: Callable):
        """移除操作回调函数"""
        if callback in self.operation_callbacks:
            self.operation_callbacks.remove(callback)

    def stop_recording(self, recording_id: str = None) -> Dict[str, Any]:
        """停止智能录制"""
        try:
            if self.recording_status == "idle":
                return {
                    'success': False,
                    'error': '当前没有活动的录制会话'
                }

            # 使用当前录制ID如果没有指定
            if recording_id is None:
                recording_id = self.current_recording_id

            if recording_id not in self.active_recordings:
                return {
                    'success': False,
                    'error': '录制会话不存在'
                }

            self.recording_status = "stopping"
            recording = self.active_recordings[recording_id]
            process = recording['process']

            # 终止录制进程
            process.terminate()

            # 等待进程结束
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()

            # 处理最终的录制文件
            output_file = recording['output_file']
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        playwright_code = f.read()

                    # 解析最终的代码并补充节点
                    final_nodes = self._parse_final_playwright_code(playwright_code)

                    # 合并实时生成的节点和最终解析的节点
                    all_nodes = self._merge_nodes(self.generated_nodes, final_nodes)

                except Exception as e:
                    logger.error(f"解析最终录制代码失败: {e}")
                    all_nodes = self.generated_nodes
            else:
                all_nodes = self.generated_nodes

            # 添加浏览器相关节点
            final_workflow_nodes = self.workflow_generator.add_browser_nodes(all_nodes)

            # 清理录制信息
            del self.active_recordings[recording_id]
            self.recording_status = "idle"
            self.current_recording_id = None

            logger.info(f"智能录制已停止，生成了 {len(final_workflow_nodes)} 个节点")

            # 安全地计算录制时长
            start_time = self.recording_stats.get('start_time')
            if start_time and isinstance(start_time, datetime):
                duration = (datetime.now() - start_time).total_seconds()
            else:
                duration = 0
                logger.warning("录制开始时间未正确设置，使用默认时长0")

            return {
                'success': True,
                'workflow_nodes': final_workflow_nodes,
                'message': f'智能录制已停止，生成了 {len(final_workflow_nodes)} 个操作节点',
                'stats': {
                    'total_operations': self.recording_stats.get('operation_count', 0),
                    'duration': duration,
                    'nodes_generated': len(final_workflow_nodes)
                }
            }

        except Exception as e:
            logger.error(f"停止智能录制失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _parse_final_playwright_code(self, code: str) -> List[Dict[str, Any]]:
        """解析最终的Playwright代码"""
        nodes = []

        try:
            lines = code.split('\n')

            for i, line in enumerate(lines):
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('import'):
                    continue

                operation_type = self.operation_analyzer.classify_operation(line)
                if operation_type != 'unknown':
                    element_info = self.operation_analyzer.extract_element_info(line, operation_type)
                    node = self.workflow_generator.generate_node(operation_type, element_info)
                    if node:
                        nodes.append(node)

        except Exception as e:
            logger.error(f"解析最终代码失败: {e}")

        return nodes

    def _merge_nodes(self, real_time_nodes: List[Dict], final_nodes: List[Dict]) -> List[Dict]:
        """合并实时节点和最终节点"""
        # 简单实现：优先使用实时节点，补充缺失的最终节点
        if real_time_nodes:
            return real_time_nodes
        return final_nodes

    def get_recording_status(self) -> Dict[str, Any]:
        """获取录制状态"""
        if self.recording_status == "idle":
            return {
                'success': True,
                'status': 'idle',
                'message': '当前没有活动的录制会话'
            }

        current_time = datetime.now()
        duration = (current_time - self.recording_stats['start_time']).total_seconds() if self.recording_stats['start_time'] else 0

        return {
            'success': True,
            'status': self.recording_status,
            'recording_id': self.current_recording_id,
            'stats': {
                'start_time': self.recording_stats['start_time'].isoformat() if self.recording_stats['start_time'] else None,
                'duration': duration,
                'operation_count': self.recording_stats['operation_count'],
                'last_operation_time': self.recording_stats['last_operation_time'].isoformat() if self.recording_stats['last_operation_time'] else None,
                'operations_per_minute': (self.recording_stats['operation_count'] / duration * 60) if duration > 0 else 0,
                'nodes_generated': len(self.generated_nodes)
            }
        }

    def get_real_time_operations(self) -> Dict[str, Any]:
        """获取实时操作列表"""
        return {
            'success': True,
            'operations': self.processed_operations[-10:],  # 返回最近10个操作
            'total_count': len(self.processed_operations),
            'nodes': self.generated_nodes[-5:] if self.generated_nodes else []  # 返回最近5个节点
        }

    def pause_recording(self) -> Dict[str, Any]:
        """暂停录制"""
        if self.recording_status != "recording":
            return {
                'success': False,
                'error': '当前没有正在进行的录制'
            }

        self.recording_status = "paused"
        logger.info("智能录制已暂停")

        return {
            'success': True,
            'message': '智能录制已暂停',
            'status': 'paused'
        }

    def resume_recording(self) -> Dict[str, Any]:
        """恢复录制"""
        if self.recording_status != "paused":
            return {
                'success': False,
                'error': '录制未处于暂停状态'
            }

        self.recording_status = "recording"
        logger.info("智能录制已恢复")

        return {
            'success': True,
            'message': '智能录制已恢复',
            'status': 'recording'
        }

    def cleanup(self):
        """清理资源"""
        # 终止所有活动的录制
        for recording_id, recording in self.active_recordings.items():
            try:
                process = recording['process']
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
            except:
                pass

        self.active_recordings.clear()
        self.recording_status = "idle"
        self.current_recording_id = None

        # 清理临时目录
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass

        logger.info("智能录制服务已清理")


# 全局智能录制服务实例
intelligent_recording_service = IntelligentRecordingService()