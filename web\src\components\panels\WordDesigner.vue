<template xmlns="">
  <div class="center-panel">
    <div class="editor-section">
      <div ref="univerContainer" class="word-container">
        <wordEditor
          :draggingField="draggingField"
          @content-change="generateRealTimePreview"
          @click="handleClick"
          id="edit"
          :allFields="allFields"
          :commentList.sync="commentList"
          ref="wordEditorRef"
          :isView="false"
        ></wordEditor>
      </div>
    </div>

    <!-- 实时预览区域 - 放在下方 -->
    <div v-show="realTimePreviewEnabled" class="preview-section-bottom">
      <div class="preview-header">
        <h3>实时预览</h3>
        <div class="preview-controls">
          <el-button @click="refreshRealTimePreview" size="small" :loading="loadingRealTimePreview">
            <el-icon v-if="!loadingRealTimePreview" style="margin-right: 4px">
              <Refresh />
            </el-icon>
            刷新
          </el-button>
        </div>
      </div>
      <div class="preview-container-bottom">
        <div v-if="loadingRealTimePreview" class="preview-loading">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>生成预览中...</span>
        </div>
        <div v-else-if="realTimePreviewError" class="preview-error">
          <el-icon>
            <Warning />
          </el-icon>
          <span>{{ realTimePreviewError }}</span>
        </div>
        <div class="preview-content-bottom">
          <div class="preview-grid-bottom">
            <div ref="previewUniverContainer" class="word-container">
              <wordEditor :commentList.sync="commentList" id="preview" ref="previewWordEditorRef" :isView="true"></wordEditor>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { convertHtml } from '@/components/doc/canvas.js'

import { PlaceholderProcessor, type PlaceholderOptions } from '@/utils/placeholderProcessor'
import { ref, onBeforeUnmount, watch, nextTick, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import wordEditor from '@/components/doc/wordEditor.vue'

import {getVariableType, getAvailableVariables} from '@/utils/availableVariables.ts'
import utils  from '@/utils/utils'

const options = reactive<PlaceholderOptions>({
  arrayDisplay: 'inline',
  separator: ', ',
  showIndex: false,
  maxItems: 50,
})

// 数据分析和绑定引擎
import {
  type DataSource,
  type FieldInfo,
} from '@/utils/dataStructureAnalyzer'
import {
  fieldBindingEngine,
  type CellBinding,
} from '@/utils/fieldBindingEngine'

// 工作流store
import { useWorkflowStore } from '@/stores/workflow'

// @ts-ignore
import SnowflakeId from 'snowflake-id'

const generator = new SnowflakeId({
  offset: (2011 - 1970) * 31536000 * 1000,
})

interface Props {
  allFields:Object,
  draggingField:Object
  templateName: String
  selectedDataSource: Object
  currentTemplate:Object
  dataSources: Object
  modelValue: boolean
  from?: string
  isFromNode?: boolean // 是否来自节点设计点击创建模板打开：true 点击保存后就关闭界面
  templateList: Array<Object>
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  data: any
  bindings: any
  templateType: string
  createTime: string
}

const props = defineProps<Props>()
const emit = defineEmits([
  'change-template',
  'close',
  'update:modelValue',
  'template-saved',
  'template-deleted',
  'template-closed',
  'click'
])

// 响应式数据
const commentList = ref([])
const templateSelectVisible = ref(false)

//canvas editor

const previewWordEditorRef = ref<HTMLElement>()
const wordEditorRef = ref<HTMLElement>()

// 工作流store
const workflowStore = useWorkflowStore()

const fieldTree = ref<FieldInfo[]>([])

// 字段绑定相关
const selectedCell = ref('')
const selectedFieldPath = ref('')
const bindingFormat = ref('')
const cellBindings = ref<Record<string, CellBinding>>({})

const selectedDataSource = computed({
  get() {
    return props.selectedDataSource
  },
  set(value) {},
})

const dataSources = computed({
  get() {
    return props.dataSources
  },
  set(value) {},
})

const currentTemplate = computed({
  get() {
    return props.currentTemplate
  },
  set(value) {},
})



/**
 * 根据一个复杂的路径字符串，从一个对象中提取一个或多个值。
 * 支持 simple.path, array[0].path, 和 array[].path 格式。
 * @param {object} data - 数据源对象。
 * @param {string} path - 路径字符串, e.g., "Response[].vals[0].Pic"。
 * @returns {Array} - 返回一个包含所有找到的值的数组。
 */
const getValueFromPath = (data, path) => {
  const segments = path.split('.')

  function resolve(currentData, remainingSegments) {
    if (remainingSegments.length === 0) {
      return Array.isArray(currentData) ? currentData : [currentData]
    }
    if (currentData === null || currentData === undefined) {
      return []
    }

    const currentSegment = remainingSegments[0]
    const nextSegments = remainingSegments.slice(1)

    const iterationMatch = currentSegment.match(/^(\w+)\[\]$/)
    if (iterationMatch) {
      const key = iterationMatch[1]
      const arrayData = currentData[key]
      if (!Array.isArray(arrayData)) {
        return []
      }
      if(isTwoDimensionalArray(arrayData)){
        return arrayData
      }
      // === 修改点：使用 reduce 替代 flatMap ===
      return arrayData.reduce(function (accumulator, item) {
        return accumulator.concat(resolve(item, nextSegments))
      }, [])
    }

    const indexMatch = currentSegment.match(/^(\w+)\[(\d+)\]$/)
    if (indexMatch) {
      const key = indexMatch[1]
      const index = parseInt(indexMatch[2], 10)
      const arrayData = currentData[key]
      if (!Array.isArray(arrayData) || index >= arrayData.length) {
        return []
      }
      return resolve(arrayData[index], nextSegments)
    }

    return resolve(currentData[currentSegment], nextSegments)
  }

  return resolve(data, segments)
}

/**
 * 转换模板数据，将复杂的占位符替换为格式化的图片对象。
 * @param {Array} templateArray - 包含占位符的原始模板数组。
 * @param {Object} responseData - 包含真实数据的对象。
 * @returns {Array} - 转换后的新数组。
 */
// 确保您有这个 Polyfill (如果环境较旧)

// --- 1. 健壮的分词器 ---
interface Token {
    type: 'text' | 'placeholder';
    value?: string;
    path?: string;
}

const tokenize = (str: string): Token[] => {
    const placeholderRegex = /#[\{](.+?)[\}]/g;
    const tokens: Token[] = [];
    let lastIndex = 0;
    const matches = Array.from(str.matchAll(placeholderRegex));
  ;
    matches.forEach(match => {
        if (match.index! > lastIndex) {
            tokens.push({ type: 'text', value: str.substring(lastIndex, match.index) });
        }
        tokens.push({ type: 'placeholder', path: match[1] });
        lastIndex = match.index! + match[0].length;
    });

    if (lastIndex < str.length) {
        tokens.push({ type: 'text', value: str.substring(lastIndex) });
    }
    return tokens;
};

// --- 2. 最终的转换函数 ---
/**
 * [已修正]
 * 使用 for...of 循环来正确处理异步操作。
 * @param templateArray 原始模板数组
 * @param responseData API返回的数据
 */
const transformTemplate = async (templateArray: any[], responseData: any): Promise<any[]> => {
    // 1. 初始化一个空数组来存放最终结果，替代 reduce
    const accumulator: any[] = [];
    // 2. 使用 for...of 循环遍历模板数组
    for (const item of templateArray) {
        if (!item || (!!item.type&&item.type!=='chart'&&item.type!=='image')) {
            accumulator.push(item);
            continue; // 跳到下一个 item
        }

        let tokens = tokenize(item.value); // (假设 tokenize 函数已存在)
        let config = null 
        if( item.conceptId){
          config = JSON.parse(item.conceptId)
        }
        if(item.type==='image'&&config&&config.variable){
          let path = '';
          const placeholderRegex = /#{(\w+)}/;
          let match = config.variable.match(placeholderRegex)
          ;
          tokens = [
            {...item,path:match[1]}
          ]
        }  
        // 3. 使用 for...of 循环遍历词元
        for (const token of tokens) {
            if (token.type === 'text' && token.value) {
                accumulator.push({ ...item, value: token.value });
            }else if(item.type==='image'){
              if(config){
                let resolvedValues = getValueFromPath(responseData, token.path);
                for (const resolvedValue of resolvedValues) {
                  if (utils.isImage(resolvedValue)) {
                    let img_base64 = resolvedValue;
                    try {
                        // 修正：是 startsWith 而不是 startWidth
                        if (!resolvedValue.startsWith('data:image/')) {
                            console.log(`正在转换图片URL: ${resolvedValue}`);
                            // 现在 await 可以正常工作了！
                            img_base64 = await utils.imageUrlToBase64(resolvedValue);

                            ;
                            console.log('转换成功！');
                        }
                    } catch (err) {
                        console.error(`图片转换失败: ${resolvedValue}`, err);
                        // 转换失败时，可以选择保留原始URL或直接忽略
                        img_base64 = resolvedValue; // 保留原始URL
                    }
                    accumulator.push({ ...item, value: img_base64 }); 
                  }
                }
              }
            }
            else if (token.type == 'placeholder' && token.path) {
                let resolvedValues = getValueFromPath(responseData, token.path);

                // 4. 使用 for...of 循环遍历解析出的值
                for (const resolvedValue of resolvedValues) {
                    // (假设 utils.isImage 和 getValueFromPath 已存在)
                    if (utils.isImage(resolvedValue)) {
                        let img_base64 = resolvedValue;
                        try {
                            // 修正：是 startsWith 而不是 startWidth
                            if (!resolvedValue.startsWith('data:image/')) {
                                console.log(`正在转换图片URL: ${resolvedValue}`);
                                // 现在 await 可以正常工作了！
                                img_base64 = await utils.imageUrlToBase64(resolvedValue);

                                ;
                                console.log('转换成功！');
                            }
                        } catch (err) {
                            console.error(`图片转换失败: ${resolvedValue}`, err);
                            // 转换失败时，可以选择保留原始URL或直接忽略
                            img_base64 = resolvedValue; // 保留原始URL
                        }
                        let width = 300;
                        let height = 300;
                        try{
                          let imageInfo = await utils.getBase64ImageDimensions(img_base64)
                          width = imageInfo.width
                          height = imageInfo.height
                        }catch(err){

                        }
                        accumulator.push({ ...item, type: 'image', value: img_base64, width: width||300, height: height||200 });
                    } else {
                      if (item.type==='chart') {
                          // It's an object! We replace the placeholder with the actual object.
                          // We keep other properties from the original item (like width, height, type).
                          accumulator.push({ ...item, value: JSON.stringify(resolvedValue) });
                      }else{
                        accumulator.push({ ...item, value: String(resolvedValue) });
                      }

                    }
                }
            }
        }
    }

    // 5. 所有异步操作完成后，返回最终的数组
    return accumulator;
};


// 实时预览相关
const realTimePreviewEnabled = ref(false)

const loadingRealTimePreview = ref(false)
const realTimePreviewError = ref('')
const realTimePreviewData = ref<Record<string, any>>({})
const realTimePreviewRawData = ref<any>(null)

// 多级循环表配置
const loopTableConfig = ref({
  startCell: 'A3',
  dataSource: '',
  loops: [
    {
      id: 1,
      arrayField: '',
      direction: 'vertical', // vertical, horizontal
      fields: [
        {
          field: '',
          label: '',
          offset: { row: 0, col: 0 },
        },
      ],
    },
  ],
  showHeaders: true,
  showSubtotals: false,
})

// 计算属性
const selectedDataSourceInfo = computed(() => {
  return dataSources.value.find((ds) => ds.nodeId === selectedDataSource.value)
})

const arrayFields = computed(() => {
  // 递归获取所有数组字段，包括嵌套的数组字段
  const getAllArrayFields = (fields: FieldInfo[], prefix = ''): FieldInfo[] => {
    let result: FieldInfo[] = []

    for (const field of fields) {
      const fullPath = prefix ? `${prefix}.${field.path}` : field.path

      if (field.isArray || field.type === 'array') {
        result.push({
          ...field,
          path: fullPath,
        })
      }

      // 递归处理子字段
      if (field.children && field.children.length > 0) {
        result = result.concat(getAllArrayFields(field.children, fullPath))
      }
    }

    return result
  }

  return getAllArrayFields(fieldTree.value)
})

// 在现有工作簿中加载模板数据
const loadTemplateDataToExistingWorkbook = async (templateData: any) => {
  try {
    console.log('在现有工作簿中加载模板数据:', templateData)
    // 重新创建工作簿
    let data = JSON.parse(JSON.stringify(templateData))
    commentList.value = data.commentList||[];
    ;
    wordEditorRef.value.instance.commentList =  data.commentList||[];
    if (data.data) wordEditorRef.value.instance.command.executeSetValue(data.data)
    if (data.options) wordEditorRef.value.instance.command.executeUpdateOptions(data.options)
    console.log('模板数据加载完成')
    ElMessage.success('模板加载成功')
  } catch (error) {
    console.error('在现有工作簿中加载模板数据失败:', error)
    throw error
  }
}

// 新建模板
const newTemplate = async () => {
  try {
    // 检查是否有未保存的更改
    await ElMessageBox.confirm('是否放弃当前模板编辑内容，重新创建新模板？', '确认新建', {
      type: 'warning',
    })
    wordEditorRef.value.instance.command.executeSetValue({
      header: [],
      main: [],
      footer: [],
    })
    previewWordEditorRef.value.instance.command.executeSetValue({
      header: [],
      main: [],
      footer: [],
    })
    emit('change-template', { name: '' })
    ElMessage.success('已新建空白模板')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('新建模板失败:', error)
    }
  }
}

// 清理预览表数据
const clearPreviewUniver = () => {
  realTimePreviewEnabled.value = false
  if (wordEditorRef) {
    wordEditorRef.value.initEditor()
  }
  if (previewWordEditorRef) {
    previewWordEditorRef.value.initEditor()
  }
}

// 从路径提取值
const extractValueFromPath = (data: any, path: string): any => {
  if (!data || !path) {
    console.log('extractValueFromPath: 数据或路径为空', { data, path })
    return ''
  }

  console.log(`extractValueFromPath: 开始提取路径 "${path}" 的值`)
  console.log('extractValueFromPath: 原始数据结构:', JSON.stringify(data, null, 2))

  try {
    let current = data

    // 处理数组路径，如 Response[].vals[].value
    if (path.includes('[]')) {
      console.log('extractValueFromPath: 检测到数组路径')

      // 对于数组路径，取第一个元素作为示例
      const pathParts = path.split('.')

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        console.log(`extractValueFromPath: 处理路径部分 "${part}"`)

        if (part.endsWith('[]')) {
          // 数组部分，取第一个元素
          const arrayKey = part.replace('[]', '')
          console.log(`extractValueFromPath: 数组键 "${arrayKey}"`)

          if (arrayKey && current[arrayKey]) {
            current = current[arrayKey]
            console.log(`extractValueFromPath: 获取数组 "${arrayKey}":`, current)
          }

          if (Array.isArray(current) && current.length > 0) {
            current = current[0]
            console.log('extractValueFromPath: 取数组第一个元素:', current)
          } else {
            console.log('extractValueFromPath: 数组为空或不是数组')
            return ''
          }
        } else if (part) {
          // 普通属性
          if (current && current[part] !== undefined) {
            current = current[part]
            console.log(`extractValueFromPath: 获取属性 "${part}":`, current)
          } else {
            console.log(`extractValueFromPath: 属性 "${part}" 不存在`)
            return ''
          }
        }
      }

      console.log('extractValueFromPath: 最终提取的值:', current)
      return current
    }

    // 处理普通路径，如 Code, Success, Message
    if (!path.includes('.') && !path.includes('[')) {
      const result = current[path] !== undefined ? current[path] : ''
      console.log(`extractValueFromPath: 简单路径 "${path}" 的值:`, result)
      return result
    }

    // 处理复杂路径
    const parts = path.split('.')
    console.log('extractValueFromPath: 路径分割:', parts)

    for (const part of parts) {
      if (part.includes('[') && part.includes(']')) {
        // 处理数组索引，如 vals[0]
        const [key, indexPart] = part.split('[')
        const index = parseInt(indexPart.replace(']', ''))

        if (key) {
          current = current[key]
          console.log(`extractValueFromPath: 获取键 "${key}":`, current)
        }

        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index]
          console.log(`extractValueFromPath: 获取数组索引 [${index}]:`, current)
        } else {
          console.log(`extractValueFromPath: 数组索引 [${index}] 无效`)
          return ''
        }
      } else {
        if (current && current[part] !== undefined) {
          current = current[part]
          console.log(`extractValueFromPath: 获取属性 "${part}":`, current)
        } else {
          console.log(`extractValueFromPath: 属性 "${part}" 不存在`)
          return ''
        }
      }

      if (current === undefined || current === null) {
        console.log('extractValueFromPath: 当前值为 null 或 undefined')
        return ''
      }
    }

    console.log('extractValueFromPath: 最终结果:', current)
    return current
  } catch (error) {
    console.error(`extractValueFromPath: 提取路径 ${path} 的值失败:`, error)
    return ''
  }
}

// 在editor插入文本
const executeInsertElementList = (html: string) => {
  let instance = wordEditorRef.value?.instance
  const options = instance.command.getOptions()
  const margins = options.margins
  const direction = options.paperDirection == 'horizontal' ? 1 : 0

  const innerWidth = (direction === 0 ? options.width : options.height) - margins[1] - margins[3]
  const innerHeight =
    (direction === 1 ? options.width : options.height) - margins[0] - margins[2] - 100
  const element = convertHtml(html, {
    innerWidth,
  })

  const width = innerWidth
  const height = innerHeight
  // 处理图片过大的问题
  element.forEach((it) => {
    if (it.type == 'image') {
      let oldWidth = it.width
      let oldHeight = it.height
      if (oldWidth > width) {
        it.width = width
        it.height = oldHeight * (it.width / oldWidth)
        oldWidth = it.width
        oldHeight = it.height
      }
      if (oldHeight > height) {
        it.height = height
        it.width = oldWidth * (it.height / oldHeight)
      }
    }
  })
  // 插入元素
  instance.command.executeInsertElementList(element)
}

// 更新单元格值 - 修复版本
const updateCellValue = (cell: string, value: string) => {

  try {
    executeInsertElementList(value)
  } catch (error) {
    console.error('插入文本时发生错误:', error)
  }
}

// 实时值变化处理
const onValueChangeImmediate = (value: string) => {
  console.log('实时值变化:', value)

  // 立即应用变化
  if (isFieldPath(value)) {
    // 是字段路径，执行字段绑定
    selectedFieldPath.value = value
    bindLoopFieldImmediate(value)
  } else {
    // 是文本值，直接设置到单元格
    setDirectValueImmediate(value)
  }
}

// 实时绑定循环字段
const bindLoopFieldImmediate = (fieldPath: string) => {
  if (!fieldPath) {
    return
  }
  ;
  try {
    // 确保fieldPath是纯字段路径，不包含绑定格式
    let cleanFieldPath = fieldPath
    const bindingMatch = fieldPath.match(/^#\{(.+)\}$/)
    if (bindingMatch) {
      cleanFieldPath = bindingMatch[1]
      console.log(`提取纯字段路径: ${cleanFieldPath}`)
    }

    // 检查是否是数组字段，决定绑定类型
    const isArrayField = cleanFieldPath.includes('[]')

    let binding: any = {
      fieldPath: cleanFieldPath,
      type: isArrayField ? 'loop' : 'string',
      format: '',
      expression: `#{${cleanFieldPath}}`,
      dataSource: selectedDataSourceInfo.value,
      isArray: isArrayField,
      conditionalExpression: cellBindings.value[selectedCell.value]?.conditionalExpression || undefined,
      displayConditions: cellBindings.value[selectedCell.value]?.displayConditions || undefined,
    }

    // 如果是数组字段，创建循环配置
    if (isArrayField) {
      const pathParts = cleanFieldPath.split('[]')
      if (pathParts.length >= 2) {
        const arrayPath = pathParts.slice(0, -1).join('[]') + '[]'
        const actualField = pathParts[pathParts.length - 1].startsWith('.')
          ? pathParts[pathParts.length - 1].substring(1)
          : pathParts[pathParts.length - 1]

        binding.loopConfig = {
          arrayField: arrayPath.replace(/\[\]/g, ''),
          direction: 'vertical', // 默认纵向，用户可以后续修改
          actualField: actualField,
        }
      }
    }

    cellBindings.value[cleanFieldPath] = binding
    ;

    // 更新单元格显示 - 使用绑定格式
    const displayText = `#{${cleanFieldPath}}`
    updateCellValue(selectedCell.value, displayText)

    console.log(`✅ 实时绑定字段 ${cleanFieldPath} 到单元格 ${selectedCell.value}`)
  } catch (error) {
    console.error('实时绑定字段失败:', error)
  }
}

// 实时设置值到单元格
const setDirectValueImmediate = (value: string) => {
  if (!selectedCell.value || !value.trim()) {
    return
  }

  try {
    // 如果该单元格之前有绑定，先移除绑定
    if (cellBindings.value[selectedCell.value]) {
      console.log(
        `移除单元格 ${selectedCell.value} 的绑定:`,
        cellBindings.value[selectedCell.value],
      )
      delete cellBindings.value[selectedCell.value]
      fieldBindingEngine.removeCellBinding(selectedCell.value)
      console.log(`✅ 已移除单元格 ${selectedCell.value} 的绑定，设置为固定文本`)
    }

    // 直接更新单元格值
    updateCellValue(selectedCell.value, value)
    console.log(`✅ 实时设置文本值到单元格 ${selectedCell.value}: ${value}`)
  } catch (error) {
    console.error('实时设置单元格值失败:', error)
  }
}

// 处理变量输入
const onVariableInput = (value: string) => {
  // 更新单元格值
  updateCellValue(selectedCell.value, value)
}

// 检查是否是字段路径
const isFieldPath = (value: string): boolean => {
  if (!value) return false

  // 检查是否是绑定格式 #{...}
  const bindingMatch = value.match(/^#\{(.+)\}$/)
  if (bindingMatch) {
    const innerPath = bindingMatch[1]
    console.log(`检测到绑定格式，提取内部路径: ${innerPath}`)
    // 递归检查内部路径
    return isFieldPath(innerPath)
  }

  // 首先检查是否在可用字段列表中
  const allFields = getAllFields()
  if (allFields.some((field) => field.path === value)) {
    console.log(`在字段列表中找到: ${value}`)
    return true
  }

  // 如果不在字段列表中，检查是否符合字段路径格式
  // 字段路径通常包含：
  // 1. 简单路径：root.fieldName
  // 2. 数组路径：root[].fieldName
  // 3. 嵌套路径：root.data[].item.name

  // 检查是否包含常见的字段路径特征
  const fieldPathPattern = /^[A-Za-z_][A-Za-z0-9_]*(\[\])?(\.[A-Za-z_][A-Za-z0-9_]*(\[\])?)*$/

  if (fieldPathPattern.test(value)) {
    console.log(`识别为字段路径格式: ${value}`)
    return true
  }

  console.log(`不识别为字段路径: ${value}`)
  return false
}

// 保存模板
const saveTemplate = async (saveType = 'save') => {
  // 如果未输入名称，默认设置个名字

  try {
    const templateData = wordEditorRef.value.instance.command.getValue()
    let templateName = props.templateName
    if (!templateName.trim()) {
      templateName = '未命名的模板'
    }
    templateData.type='word';
    templateData.commentList = commentList.value
    const template: ReportTemplate = {
      id: generator.generate(),
      name: templateName,
      templateType:'word',
      description: `模板 - ${templateName}`,
      data: templateData,
      bindings: {},
      createTime: new Date().toLocaleString(),
    }

    // 调用后端API保存模板
    await saveTemplateToServer(template, saveType)

    ElMessage.success(`模板 "${template.name}" 保存成功`)
    console.log('保存的模板:', template)
    emit('template-saved', template)
  } catch (error) {
    console.error('保存模板失败:', error)
    ElMessage.error('保存模板失败: ' + (error as Error).message)
  }
}

// 保存模板到服务器
const saveTemplateToServer = async (template: ReportTemplate, saveType: string) => {
  try {

    console.log('原始绑定数据:', template.bindings)

    const requestData = {
      name: template.name,
      description: template.description,
      data: template.data,
      bindings: {},
      templateType: 'word',
    }

    console.log('发送到后端的完整数据:', requestData)

    let response = {}
    // 保存类型不是另存为的
    if (currentTemplate.value?.id && saveType !== 'saveas') {
      response = await fetch(
        `http://localhost:39876/api/report-templates/${currentTemplate.value.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': utils.GetAuthorization(),
          },
          body: JSON.stringify(requestData),
        },
      )
    } else {
      // 查找模板列表中是否存在重复模板
      const idx = props.templateList.findIndex(it => it.name === requestData.name)
      if (idx > -1) {
        throw new Error('模板名称重复')
      }

      response = await fetch('http://localhost:39876/api/report-templates/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': utils.GetAuthorization(),
        },
        body: JSON.stringify(requestData),
      })
    }

    if (!response.ok) {
      const errorText = await response.text()
      console.error('服务器响应错误:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const result = await response.json()
    emit('update:currentTemplate', result)
    emit('template-saved', template)
    console.log('模板保存成功:', result)
    return result
  } catch (error) {
    console.error('保存模板到服务器失败:', error)
    throw error
  }
}

const handleClick = (event,context)=>{
  emit('click',event,context)
}

// 选择模板
const selectTemplate = async (template: ReportTemplate) => {
  try {
    console.log('开始加载模板:', template.name)
    console.log('模板数据:', template.data)

    // 优先使用现有实例加载数据，避免重新创建导致的白屏问题
    if (wordEditorRef) {
      try {
        // 方法1：尝试使用现有API加载数据
        await loadTemplateDataToExistingWorkbook(template.data)
      } catch (loadError) {
        console.warn('直接加载失败:', loadError)
        ElMessage.warning('模板数据加载失败，将保持当前工作表')
      }
    }

    templateSelectVisible.value = false
    emit('template-selected', template)
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败: ' + (error as Error).message)
  }
}

// 导出模板
const exportTemplate = () => {
  try {
    const templateData = wordEditorRef.value?.instance.command.getValue()

    const exportData = {
      name: props.templateName,
      data: templateData,
      bindings: cellBindings.value,
      exportTime: new Date().toISOString(),
      exportType: 'word',
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `${props.templateName ? 'Word模板-' + props.templateName : 'Excel模板-' + Date.now()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('模板导出成功')
  } catch (error) {
    console.error('导出模板失败:', error)
    ElMessage.error('导出模板失败')
  }
}

// 导入模板
const importTemplate = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const content = await file.text()
      const data = JSON.parse(content)

      // 验证导入数据格式
      if (!data.data || !data.bindings) {
        throw new Error('无效的模板文件格式')
      }

      // 加载模板数据
      // if (!univerAPIInstance) {
      //   await initUniver()
      // }
      // destroyUniver()
      await loadTemplateDataToExistingWorkbook(data.data)

      // 导入绑定配置
      if (data.bindings) {
        fieldBindingEngine.importBindings(data.bindings, dataSources.value)
        cellBindings.value = data.bindings
      }

      if (data.name) {
        emit('change-template', data)
      }
    } catch (error) {
      console.error('导入模板失败:', error)
      ElMessage.error('导入模板失败: ' + (error as Error).message)
    }
  }

  input.click()
}

// 关闭对话框
const handleClose = () => {
  emit('close')
}

// 确认保存
const confirmSave = async () => {
  await saveTemplate()
  if (props.isFromNode) {
    handleClose()
  }
}

// 获取所有字段（扁平化）
const getAllFields = () => {
  const flattenFields = (fields: FieldInfo[]): FieldInfo[] => {
    let result: FieldInfo[] = []
    for (const field of fields) {
      result.push(field)
      if (field.children) {
        result = result.concat(flattenFields(field.children))
      }
    }
    return result
  }
  const allFields = flattenFields(fieldTree.value)

  // 检查是否包含当前绑定的路径，如果不在则动态添加
  const currentPath = selectedCell.value && cellBindings.value[selectedCell.value]?.fieldPath
  if (currentPath) {
    const hasCurrentPath = allFields.some((f) => f.path === currentPath)
    if (!hasCurrentPath) {
      allFields.push({
        path: currentPath,
        label: currentPath,
        type: 'string',
        isArray: currentPath.includes('[]'),
        children: [],
      })
    }
  }

  return allFields
}


// 在editor插入图片
const executeInsertPic = async (url) => {
  const self = this
  // const options = await this.$utils.base64ToImg(url);
  const image = new Image()
  // 先设置图片跨域属性
  image.crossOrigin = 'Anonymous'
  // 再给image赋值src属性，先后顺序不能颠倒
  image.src = url
  // 获取图片的宽高
  image.onload = function () {
    const options = {
      value: url,
      width: image.width,
      height: image.height,
    }
    self.instance.command.executeImage(options)
  }
}

// 实时预览相关函数

// 切换实时预览
const onRealTimePreviewToggle = (enabled: boolean) => {
  console.log('实时预览切换:', enabled)
  realTimePreviewEnabled.value = enabled
  if (enabled) {
    // 启用实时预览时，立即生成预览
    generateRealTimePreview()
  } else {
    // 关闭实时预览时，清空数据
    realTimePreviewData.value = {}
    realTimePreviewRawData.value = null
    realTimePreviewError.value = ''
  }
}

// // 刷新实时预览
const refreshRealTimePreview = () => {
  console.log('手动刷新实时预览')
  generateRealTimePreview()
}

// 初始化 预览Univer
const initPreviewUniver = async () => {
  try {
    if (wordEditorRef) {

      let docData = wordEditorRef.value.instance.command.getValue()
      previewWordEditorRef.value.instance.command.executeSetValue(docData.data)
    }
  } catch (error) {
    console.error('初始化 Univer 失败:', error)
    ElMessage.error('模板设计器初始化失败')
  }
}

// 获取节点字段结构（不执行节点）- 返回完整结果
const getNodeFieldsResult = async (
  dataSource: DataSource,
  forceRealRequest: boolean = false,
): Promise<any> => {
  try {
    console.log(`获取节点 ${dataSource.nodeId} 的字段结构...`)
    console.log(`强制真实请求: ${forceRealRequest}`)

    // 调用新的字段获取API
    const response = await fetch(`http://localhost:39876/api/nodes/${dataSource.nodeId}/fields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        nodes: workflowStore.nodes,
        edges: workflowStore.edges,
        force_real_request: forceRealRequest,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`获取字段失败: ${response.status} ${response.statusText}\n${errorText}`)
    }

    const result = await response.json()

    console.log('字段获取结果:', result)

    if (result.success) {
      console.log(`字段来源: ${result.source} - ${result.message}`)
    }

    return result
  } catch (error) {
    console.error('获取节点字段失败:', error)
    throw error
  }
}

/**
 * 一个辅助函数，用于根据点分隔的路径从对象中安全地获取嵌套值。
 * @param {object} context - 数据源对象。
 * @param {string} path - 点分隔的路径，例如 "product.tags"。
 * @returns {any} - 找到的值，如果路径无效则返回 undefined。
 */
function getValueFromPath2(context, path) {
    return path.split('.').reduce((obj, key) => {
        return obj && obj[key] !== undefined ? obj[key] : undefined;
    }, context);
}

/**
 * 辅助函数：检查 parentRow 是否是 childRow 的父级。
 * 规则：childRow 中的元素块以 parentRow 的对应元素开头。
 * @param {Array<string>} parentRow 
 * @param {Array<string>} childRow 
 * @returns {boolean}
 */
function isParentOf(parentRow, childRow) {
    if (!parentRow || !childRow || parentRow.length === 0 || childRow.length < parentRow.length) {
        return false;
    }
    let childIndex = 0;
    for (const parent of parentRow) {
        if (childIndex >= childRow.length || !String(childRow[childIndex]).startsWith(String(parent))) {
            return false; // 如果子行已耗尽，或当前子元素不以父元素开头，则关系不成立
        }
        // 寻找这个父节点对应的所有子节点块
        while (childIndex < childRow.length && String(childRow[childIndex]).startsWith(String(parent))) {
            childIndex++;
        }
    }
    return true;
}

/**
 * 核心解析函数：动态分析数据，计算表头结构，并分离出数据体。
 * @param {Array<Array<string>>} data - 完整的二维表格数据
 * @returns {{headers: Array<Array<object>>, body: Array<Array<string>>}}
 */
function parseDynamicTableData(data) {
    if (!data || data.length === 0) {
        return { headers: [], body: [] };
    }

    // 1. 动态探测表头行数
    let headerRowCount = 1;
    if (data.length > 1) {
        for (let i = 0; i < data.length - 1; i++) {
            if (isParentOf(data[i], data[i + 1])) {
                headerRowCount++;
            } else {
                break;
            }
        }
    }

    const headerData = data.slice(0, headerRowCount);
    const bodyData = data.slice(headerRowCount);

    // 如果只有单行表头或无表头，直接返回
    if (headerRowCount <= 1) {
        const headers = headerData.map(row => row.map(cell => ({ value: cell, colspan: 1, rowspan: 1 })));
        return { headers, body: bodyData };
    }

    // 2. 初始化结构化表头，所有合并值默认为1
    let structuredHeaders = headerData.map(row =>
        row.map(cell => ({ value: cell, colspan: 1, rowspan: 1 }))
    );

    // 3. 从下至上计算 colspan
    for (let i = headerRowCount - 2; i >= 0; i--) {
        let childColIndex = 0;
        for (let j = 0; j < structuredHeaders[i].length; j++) {
            const parentCell = structuredHeaders[i][j];
            let accumulatedColspan = 0;
            let tempChildIndex = childColIndex;
            
            while (tempChildIndex < structuredHeaders[i + 1].length && 
                   String(structuredHeaders[i + 1][tempChildIndex].value).startsWith(String(parentCell.value))) {
                accumulatedColspan += structuredHeaders[i + 1][tempChildIndex].colspan;
                tempChildIndex++;
            }

            if (accumulatedColspan > 0) {
                parentCell.colspan = accumulatedColspan;
                childColIndex = tempChildIndex;
            }
        }
    }
    
    // (rowspan的计算可以根据需要进一步完善，此处专注于colspan)

    return { headers: structuredHeaders, body: bodyData };
}
/**
 * 【最终方法】重构后的主渲染函数，支持动态多级表头
 * @param {object} templateJson - 原始模板JSON
 * @param {object} renderData - 包含 Response.table 的业务数据
 * @returns {object} - 渲染完成后的新JSON
 */
function renderTableWithDynamicHeaders(tableElement, renderData) {
    const options = wordEditorRef.value.instance.command.getOptions()
    const margins = options.margins
    const direction = options.paperDirection == 'horizontal' ? 1 : 0
    ;
    const innerWidth = (direction === 0 ? options.width : options.height) - margins[1] - margins[3]
    const element = JSON.parse(JSON.stringify(tableElement));

        const table = element;
        let target = null;
        let tableData = null;

        // 1. 查找并获取表格数据
        for (const tr of table.trList) {
            for (const td of tr.tdList) {
                if (td.value && td.value[0] && td.value[0].value) {
                    const match = td.value[0].value.match(/(\$|\#)\{(.+?)}/);
                    if (match) {
                        const path = match[2].split('.'); // e.g., ["Response", "table"]
                        let dataObject = renderData;
                        // for (const key of path) {
                        //    dataObject = dataObject ? dataObject[key] : undefined;
                        // }
                        
                        if (Array.isArray(dataObject) && Array.isArray(dataObject[0])) {
                            tableData = dataObject;
                            target = table;
                        }
                        break;
                    }
                }
            }
            if (target) break;
        }

        if (target && tableData) {
            // 2. 动态解析表头和数据体
            const { headers, body } = parseDynamicTableData(tableData);

            // 3. 开始重构表格
            const newTrList = [];
            const sampleTd = table.trList[0]?.tdList[0] || {};
            const sampleTrHeight = table.trList[0]?.height || 42;

            // 生成表头行
            for (const headerRow of headers) {
                const newTr = { height: sampleTrHeight, minHeight: 42, tdList: [] };
                for (const cellInfo of headerRow) {
                    const newTd = JSON.parse(JSON.stringify(sampleTd));
                    newTd.value = [{ value: String(cellInfo.value) }];
                    newTd.colspan = cellInfo.colspan;
                    newTd.rowspan = cellInfo.rowspan;
                    newTr.tdList.push(newTd);
                }
                newTrList.push(newTr);
            }

            // 生成数据行
            for (const bodyRow of body) {
                const newTr = { height: sampleTrHeight, minHeight: 42, tdList: [] };
                for (const cellData of bodyRow) {
                    const newTd = JSON.parse(JSON.stringify(sampleTd));
                    newTd.value = [{ value: String(cellData) }];
                    newTd.colspan = 1;
                    newTd.rowspan = 1;
                    newTr.tdList.push(newTd);
                }
                newTrList.push(newTr);
            }
            
            target.trList = newTrList;

            // 4. 重构列定义 (colgroup)
            const numCols = body.length > 0 ? body[0].length : (headers.length > 0 ? headers[headers.length-1].length : 0);
            const newColgroup = [];
            ;
            const sampleColWidth = innerWidth/numCols;
            for (let i = 0; i < numCols; i++) {
                newColgroup.push({ width: sampleColWidth });
            }
            target.colgroup = newColgroup;

            // 5. 更新表格总尺寸
            target.width = newColgroup.reduce((sum, col) => sum + col.width, 0);
            target.height = newTrList.reduce((sum, row) => sum + row.height, 0);
        }

    return table;
}
// =======================================================================
//  处理器函数 (每个处理器负责一种渲染模式)
// =======================================================================

function isTwoDimensionalArray(arr) {
  // 首先检查是否为数组
  if (!Array.isArray(arr)) {
    return false;
  }
  
  // 检查数组中的每个元素是否都是数组
  return arr.every(item => Array.isArray(item));
}
/**
 * 处理器 1: 处理竖向扩展 (v)
 * @param {object} table - 表格节点
 * @param {Array<object>} data - 对象数组, e.g., [{code:1, name:'A'}, ...]
 * @param {object} location - 指令所在的位置 {rowIndex, cellIndex}
 */
function handleVerticalExpansionV2(table, dataArray, templateRowIndex) {
    const templateRow = table.trList[templateRowIndex];
    if (!Array.isArray(dataArray)) return; // 安全检查

    const newRows = [];
    ;
    // 遍历数据数组中的每一个对象 (item)
    for (const item of dataArray) {
        // 深度克隆模板行，为每个数据项创建新行
        const newRow = JSON.parse(JSON.stringify(templateRow));

        // 关键：在新行中移除所有 conceptId，以防止在未来的处理中被误判为模板
        for (const td of newRow.tdList) {
            delete td.conceptId;
        }

        // 遍历新行的每个单元格，并用 item 中的数据替换变量
        for (const td of newRow.tdList) {
            if (td.value && td.value[0] && td.value[0].value) {
                const originalValue = td.value[0].value;
                // 使用正则表达式查找所有 #{...[].property} 格式的变量
                const replacedValue = originalValue.replace(/#\{(.+?)\[\]\.(.+?)\}/g, (match, arrayPath, property) => {
                    // 因为我们已经在循环 item，所以直接用 property 从 item 中取值
                    ;
                    return item !== undefined ? item : '';
                });
                td.value[0].value = replacedValue;
            }
        }
        newRows.push(newRow);
    }

    // 用新生成的行列表替换掉原来的单行模板
    if (newRows.length > 0) {
        table.trList.splice(templateRowIndex, table.trList.length-templateRowIndex, ...newRows);
    } else {
        // 如果数据数组为空，则直接删除模板行
        // table.trList.splice(templateRowIndex, 1);
    }
}


/**
 * 处理器 2: 处理横向扩展 (h)
 * @param {object} table - 表格节点
 * @param {Array<string|number>} data - 简单数组, e.g., ['Design', 'Dev']
 * @param {object} location - 指令所在的位置 {rowIndex, cellIndex}
 */
function handleHorizontalExpansion(table, data, location) {
    if (data.length === 0) {
        table.trList[location.rowIndex].tdList[location.cellIndex].value = [];
        return;
    }

    const { rowIndex, cellIndex } = location;
    const numToAdd = data.length - 1;

    // 1. 扩展目标行
    const targetRow = table.trList[rowIndex];
    const originalCell = targetRow.tdList[cellIndex];
    if(data.length>targetRow.tdList.length){
      data = data.slice(0,targetRow.tdList.length)
    }
    const newCells = data.map(item => {
        const newCell = JSON.parse(JSON.stringify(originalCell));
        newCell.value = [{ value: String(item) }];
        return newCell;
    });
    ;
    targetRow.tdList.splice(cellIndex,targetRow.tdList.length-cellIndex,...newCells);

    // 2. 扩展其他所有行
    // for (let i = 0; i < table.trList.length; i++) {
    //     if (i === rowIndex) continue;
    //     const currentRow = table.trList[i];
    //     if (currentRow.tdList.length > cellIndex) {
    //         const emptyCell = JSON.parse(JSON.stringify(currentRow.tdList[cellIndex]));
    //         emptyCell.value = [];
    //         const cellsToInsert = Array(numToAdd).fill(null).map(() => JSON.parse(JSON.stringify(emptyCell)));
    //         currentRow.tdList.splice(cellIndex + 1, 0, ...cellsToInsert);
    //     }
    // }

    // 3. 扩展列定义
    const originalCol = table.colgroup[cellIndex];
    // const colsToInsert = Array(numToAdd).fill(null).map(() => JSON.parse(JSON.stringify(originalCol)));
    // table.colgroup.splice(cellIndex + 1, 0, ...colsToInsert);
}


/**
 * 处理器 3: 处理全表替换 ([])
 * @param {object} table - 表格节点
 * @param {Array<Array<string>>} data - 二维数组
 */
function handleFullReplacement(table, data) {
    const { headers, body } = parseDynamicTableData(data);
    const newTrList = [];
    const sampleTd = table.trList[0]?.tdList[0] || {};
    const sampleTrHeight = table.trList[0]?.height || 42;

    // 生成表头行
    for (const headerRow of headers) {
        const newTr = { height: sampleTrHeight, tdList: [] };
        for (const cellInfo of headerRow) {
            const newTd = JSON.parse(JSON.stringify(sampleTd));
            newTd.value = [{ value: String(cellInfo.value) }];
            newTd.colspan = cellInfo.colspan;
            newTd.rowspan = cellInfo.rowspan;
            newTr.tdList.push(newTd);
        }
        newTrList.push(newTr);
    }
    // 生成数据行... (与上一回答相同)
    table.trList = newTrList;
    // 重构列定义和更新尺寸... (与上一回答相同)
}


// =======================================================================
//  统一渲染引擎 (主函数)
// =======================================================================

/**
 * 【最终统一引擎】根据单元格内的指令渲染表格
 * @param {object} templateJson 
 * @param {object} renderData 
 * @returns {object}
 */
function renderTableEngine(tableElement, renderData) {
    const element = JSON.parse(JSON.stringify(tableElement));
    let table = element;
    let directiveFound = false;

    for (let i = 0; i < table.trList.length; i++) {
        for (let j = 0; j < table.trList[i].tdList.length; j++) {
            const td = table.trList[i].tdList[j];
            if (!td.value || !td.value[0] || !td.value[0].value) continue;

            const cellValue = td.value[0].value;
            const match = cellValue.match(/(\$|\#)\{(.+?)}/);

            if (match) {
                const variablePath = match[2]; // e.g., "Response.table"
                const conceptId = td.conceptId
                const extendInfo = conceptId?JSON.parse(conceptId):null
                const directive = extendInfo?.direction;  // "v", "h", or undefined
                
                // 获取数据
                // let data = renderData;
                let data = getValueFromPath(renderData, variablePath);
                // for (let key of variablePath.split('.')) {
                //   data = data ? data[key] : undefined;
                // }
                
                if (data === undefined) continue;
                ;
                if(!isTwoDimensionalArray(data)){
                  if (directive === 'vertical') {
                      // 竖向扩展
                      handleVerticalExpansionV2(table, data, i);
                      directiveFound = true;
                  } else if (directive === 'horizontal') {
                      // 横向扩展
                      handleHorizontalExpansion(table, data, { rowIndex: i, cellIndex: j });
                      directiveFound = true;
                  }
                }else if (isTwoDimensionalArray(data)) {
                    // 全表替换 (指令为 [])
                    table = renderTableWithDynamicHeaders(table, data);
                    directiveFound = true;
                }
                break;
            }
        }
        if (directiveFound) break;
    }
    return table;
}



// const dataContext = {
//             "product": {
//                 "name": "智能手表",
//                 "tags": ["运动", "健康", "防水", "NFC"]
//             }
//         };
// const Response ={
//   Response:{
//     table: [
//   ["标题1","标题2","标题3","标题1","标题2","标题3"],
//   // ["标题1-1","标题1-2","标题2-1","标题2-2","标题3-1","标题3-2"],
//   ["内容1-1","内容1-2","内容1-3","内容1-4","内容1-5","内容1-6"],
//   ["内容2-1","内容2-2","内容2-3","内容2-4","内容2-5","内容2-6"],
//   ["内容3-1","内容3-2","内容3-3","内容3-4","内容3-5","内容3-6"],
// ]
// }
// }
const Response ={
  Response:{
    testrows: [
      {"code":1,"name":"1111"},
      {"code":2,"name":"2222"},
      {"code":3,"name":"3333"},
      {"code":4,"name":"4444"},
      {"code":5,"name":"5555"}

    ],
    table: [
      ["标题1","标题2","标题3","标题1","标题2","标题3"],
      // ["标题1-1","标题1-2","标题2-1","标题2-2","标题3-1","标题3-2"],
      ["内容1-1","内容1-2","内容1-3","内容1-4","内容1-5","内容1-6"],
      ["内容2-1","内容2-2","内容2-3","内容2-4","内容2-5","内容2-6"],
      ["内容3-1","内容3-2","内容3-3","内容3-4","内容3-5","内容3-6"],
    ]
    }
}


  // --- 执行流程 ---

// 使用 ES6 语法实现对象合并（不覆盖已有属性）
const mergeObjects = (...sources) => {
  return sources.reduce((target, source) => {
    // 只处理有效的非数组对象
    if (source && typeof source === 'object' && !Array.isArray(source)) {
      // 使用 Object.keys 获取属性并遍历
      Object.keys(source).forEach(key => {
        // 仅当目标中不存在该属性时才添加
        if (!(key in target)) {
          target[key] = source[key];
        }
      });
    }
    return target;
  }, {}); // 初始值为空对象
};

// 生成实时预览
const generateRealTimePreview = async () => {

  if (!selectedDataSource.value) {
    realTimePreviewError.value = '请先选择数据源'
    return
  }
  await initPreviewUniver()
  loadingRealTimePreview.value = true
  realTimePreviewError.value = ''

  try {
    console.log('开始生成实时预览，当前绑定:', cellBindings.value)

    // 1. 获取真实数据
    const dataSource = selectedDataSourceInfo.value
    if (!dataSource) {
      realTimePreviewError.value = '数据源信息不完整'
      return
    }

    const result = await getNodeFieldsResult(dataSource, true)
    console.log('实时预览获取数据结果:', result)

    if (!result.success || !result.raw_data) {
      realTimePreviewError.value = '获取数据失败，无法预览'
      console.error('实时预览数据获取失败:', result)
      return
    }

    console.log('实时预览原始数据:', result.raw_data)

    let docData = wordEditorRef.value.instance.command.getValue()
    let content = docData?.data
    if (content) {
      let params = {}
      let tableArray = content.main.filter(it=>it.type=='table');
      tableArray.forEach(tb=>{
        // 1. 处理JSON，展开占位符
        ;
        const processedTable = renderTableEngine(tb, mergeObjects(result.raw_data,Response));

        // 2. 根据处理后的JSON生成HTML
        // const tableHtml = generateHtmlFromTableJson(processedTable);
//         const tableHtml = `<table>
//     <thead>
//         <tr id="header-row">
//             <th>ID</th>
//             <th>名称</th>
//             <th colspan="4">产品标签</th>
//         </tr>
//     </thead>
//     <tbody>
//         <tr id="data-row-9dbc828b">
//             <td>101</td>
//             <td>智能手表</td>
//             <td>运动</td>
//             <td>健康</td>
//             <td>防水</td>
//             <td>NFC</td>
//         </tr>
//     </tbody>
// </table>`
        // const tableElement = convertHtml(tableHtml,innerWidth)
          previewWordEditorRef.value.instance.command.executeUpdateElementById(
            {conceptId:tb.conceptId,properties:processedTable
            }
          )
        
      })
      let preViewDocData = previewWordEditorRef.value.instance.command.getValue()
      let preViewContent = preViewDocData?.data
      // return
      availableVariables.value.forEach((vari) => {

        let realValue = processVariableReferences('${' + vari.name + '}', result.raw_data)
        params[vari.name] = realValue
      })

      let test_data_json = {
        chart:{
  xAxis: {
    type: 'category',
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '30%']
  },
  visualMap: {
    type: 'piecewise',
    show: false,
    dimension: 0,
    seriesIndex: 0,
    pieces: [
      {
        gt: 1,
        lt: 3,
        color: 'rgba(0, 0, 180, 0.4)'
      },
      {
        gt: 5,
        lt: 7,
        color: 'rgba(0, 0, 180, 0.4)'
      }
    ]
  },
  series: [
    {
      type: 'line',
      smooth: 0.6,
      symbol: 'none',
      lineStyle: {
        color: '#5470C6',
        width: 5
      },
      markLine: {
        symbol: ['none', 'none'],
        label: { show: false },
        data: [{ xAxis: 1 }, { xAxis: 3 }, { xAxis: 5 }, { xAxis: 7 }]
      },
      areaStyle: {},
      data: [
        ['2019-10-10', 200],
        ['2019-10-11', 560],
        ['2019-10-12', 750],
        ['2019-10-13', 580],
        ['2019-10-14', 250],
        ['2019-10-15', 300],
        ['2019-10-16', 450],
        ['2019-10-17', 300],
        ['2019-10-18', 100]
      ]
    }
  ]
},
        pic: 'https://www.dlmeasure.com/uniwim/uploads/2024/8/5c44206d28c94b04b3b4661af424a058.png',
      }

      const finalData =  await transformTemplate(preViewContent.main, {
        ...test_data_json,
        ...result.raw_data,
        ...params,
      })
      ;
      preViewContent.main = finalData
      ;
      let processor = new PlaceholderProcessor({ ...result.raw_data, ...params}, options)
      let str = processor.processText(JSON.stringify(preViewContent))

      let parse_json = JSON.parse(str)

      previewWordEditorRef.value.instance.command.executeSetValue(parse_json)
    }

  } catch (error) {
    console.error('生成实时预览失败:', error)
    realTimePreviewError.value = '预览失败: ' + (error as Error).message
  } finally {
    loadingRealTimePreview.value = false
  }
}

// 从工作流中收集可用变量
const availableVariables = computed(() => getAvailableVariables())

// 从工作流节点中查找变量
const findWorkflowVariable = (variableName: string, previewData?: any): any => {
  // 遍历工作流中的所有节点，收集输出变量
  for (const node of workflowStore.nodes) {
    const nodeData = node.data
    const config = nodeData.config || {}

    // 收集用户输入组件的变量
    if (['user_input', 'user_choice', 'user_confirm'].includes(nodeData.componentType)) {
      if (config.variable_name === variableName) {
        return {
          name: variableName,
          value: config.user_provided_value || config.default_value || '',
          type: getVariableType(config),
          scope: 'workflow',
          description: `来自用户输入: ${config.prompt_message || nodeData.label}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集HTTP请求组件的响应变量
    if (nodeData.componentType === 'http_post') {
      if (config.response_variable === variableName) {
        // 直接返回预览数据
        const realValue = previewData || '响应对象'
        return {
          name: variableName,
          value: realValue,
          type: 'object',
          scope: 'workflow',
          description: `HTTP响应对象`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_content_variable === variableName) {
        // 直接返回预览数据
        let realValue = '响应内容'
        if (previewData) {
          if (typeof previewData === 'string') {
            realValue = previewData
          } else if (previewData && typeof previewData === 'object') {
            // 如果是对象，序列化为JSON字符串
            realValue = JSON.stringify(previewData)
          }
        }
        return {
          name: variableName,
          value: realValue,
          type: 'string',
          scope: 'workflow',
          description: `HTTP响应内容`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_status_variable === variableName) {
        return {
          name: variableName,
          value: 200,
          type: 'number',
          scope: 'workflow',
          description: `HTTP状态码`,
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.response_headers_variable === variableName) {
        return {
          name: variableName,
          value: '响应头',
          type: 'object',
          scope: 'workflow',
          description: `HTTP响应头`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集设置变量组件的变量
    if (nodeData.componentType === 'set_variable') {
      if (config.variable_name === variableName) {
        return {
          name: variableName,
          value: config.variable_value || '',
          type: config.value_type || 'string',
          scope: config.scope || 'local',
          description: `设置的变量: ${config.description || ''}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集Python执行器组件的输出变量
    if (nodeData.componentType === 'python_execute') {
      if (config.output_variable === variableName) {
        return {
          name: variableName,
          value: 'Python输出结果',
          type: 'string',
          scope: 'workflow',
          description: 'Python代码执行的输出结果',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.error_variable === variableName) {
        return {
          name: variableName,
          value: 'Python错误信息',
          type: 'string',
          scope: 'workflow',
          description: 'Python代码执行的错误信息',
          sourceNode: nodeData.label || node.id,
        }
      }
      if (config.return_code_variable === variableName) {
        return {
          name: variableName,
          value: 0,
          type: 'number',
          scope: 'workflow',
          description: 'Python代码执行的返回码',
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集数据库查询组件的变量
    if (nodeData.componentType === 'db_query') {
      if (config.response_content_variable === variableName) {
        return {
          name: variableName,
          value: null,
          type: 'any',
          scope: 'workflow',
          description: `数据库查询结果: ${config.query || '未指定查询语句'}`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集AI分析变量
    if (nodeData.componentType === 'ai_analyze') {
      if (config.ai_analyze_response === variableName) {
        return {
          name: variableName,
          value: '响应内容',
          type: 'string',
          scope: 'workflow',
          description: `AI分析`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 收集文本模板变量
    if (nodeData.componentType === 'text_template') {
      if (config.output_variable === variableName) {
        return {
          name: variableName,
          value: '输出变量',
          type: 'string',
          scope: 'workflow',
          description: `存储格式化后的变量名`,
          sourceNode: nodeData.label || node.id,
        }
      }
    }

    // 可以继续添加其他组件类型的变量收集逻辑...
  }

  return null
}


// 处理变量引用
const processVariableReferences = (text: any, previewData?: any): any => {
  if (typeof text !== 'string') {
    return text
  }

  // 匹配 ${variableName} 格式的变量引用
  const variablePattern = /\$\{([^}]+)\}/g

  return text.replace(variablePattern, (match, variableName) => {
    // 首先从局部变量中查找

    const localVariable = workflowStore.variables?.find((v) => v.name === variableName)

    if (localVariable) {
      // 如果是currentTime类型的变量，生成当前时间
      if (localVariable.type === 'currentTime') {
        const now = new Date()
        const format = localVariable.value || '%Y-%m-%d %H:%M:%S'
        return formatCurrentTime(now, format)
      }

      // 其他类型的变量，返回其值
      return localVariable.value !== undefined ? String(localVariable.value) : `[${variableName}]`
    }

    // 如果在局部变量中找不到，从工作流节点中查找
    const workflowVariable = findWorkflowVariable(variableName, previewData)
    if (workflowVariable) {
      // 如果是currentTime类型的变量，生成当前时间
      if (workflowVariable.type === 'currentTime') {
        const now = new Date()
        const format = workflowVariable.value || '%Y-%m-%d %H:%M:%S'
        return formatCurrentTime(now, format)
      }

      // 其他类型的变量，返回其值
      return workflowVariable.value !== undefined
        ? String(workflowVariable.value)
        : `[${variableName}]`
    }

    // 如果找不到变量，返回原始文本
    return match
  })
}

// 格式化当前时间
const formatCurrentTime = (date: Date, format: string): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('%Y', String(year))
    .replace('%m', month)
    .replace('%d', day)
    .replace('%H', hours)
    .replace('%M', minutes)
    .replace('%S', seconds)
}

// 组件卸载时清理
onBeforeUnmount(() => {})

defineExpose({
  onValueChangeImmediate,
  onRealTimePreviewToggle,
  saveTemplate,
  selectTemplate,
  newTemplate,
  exportTemplate,
  importTemplate,
  confirmSave,
  onVariableInput,
  clearPreviewUniver,
  refreshRealTimePreview
})
</script>
<style lang="scss">
</style>

<style scoped lang="scss">
/* 中间表格编辑器 */
.center-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.editor-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5.5px 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}

.editor-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.editor-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.word-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  min-height: 300px;
  flex: 1;
  overflow:hidden;
  position: relative;
}

/* 底部实时预览区域 - 上下各占一半 */
.preview-section-bottom {
  flex: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  background: white;
  overflow: hidden;
}

.preview-container-bottom {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.preview-content-bottom {
  flex: 1;
  overflow: hidden;
}

.preview-grid-bottom {
  width: 100%;
  height: 100%;
}

.excel-preview-table-bottom {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-preview-table-bottom .row-header,
.excel-preview-table-bottom .col-header {
  background: #f5f7fa;
  border: 1px solid #d4d4d4;
  padding: 2px 6px;
  text-align: center;
  font-weight: 600;
  color: #666;
  font-size: 10px;
  min-width: 30px;
  width: 30px;
}

.excel-preview-table-bottom .col-header {
  height: 20px;
}

.excel-preview-table-bottom .row-header {
  width: 30px;
  height: 18px;
}

.preview-cell-bottom {
  border: 1px solid #d4d4d4;
  padding: 0;
  margin: 0;
  height: 18px;
  min-width: 60px;
  max-width: 120px;
  overflow: hidden;
}

.cell-content-bottom {
  padding: 2px 4px;
  font-size: 11px;
  line-height: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 100%;
  display: flex;
  align-items: center;
}

/* 实时预览区域 */
.preview-section {
  width: 400px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.preview-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-container-inline {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 12;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  box-sizing: border-box;
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  box-sizing: border-box;
}

.preview-error {
  color: #f56c6c;
}

.preview-content-inline {
  flex: 1;
  overflow: auto;
}

.preview-grid-inline {
  width: 100%;
  height: 100%;
}

.excel-preview-table-inline {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-preview-table-inline .row-header,
.excel-preview-table-inline .col-header {
  background: #f5f7fa;
  border: 1px solid #d4d4d4;
  padding: 2px 6px;
  text-align: center;
  font-weight: 600;
  color: #666;
  font-size: 10px;
  min-width: 30px;
  width: 30px;
}

.excel-preview-table-inline .col-header {
  height: 20px;
}

.excel-preview-table-inline .row-header {
  width: 30px;
  height: 18px;
}

.preview-cell-inline {
  border: 1px solid #d4d4d4;
  padding: 0;
  margin: 0;
  height: 18px;
  min-width: 60px;
  max-width: 120px;
  overflow: hidden;
}

.cell-content-inline {
  padding: 2px 4px;
  font-size: 11px;
  line-height: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
