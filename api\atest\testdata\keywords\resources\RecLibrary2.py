class RecLibrary2:

    def keyword_only_in_library_2(self):
        print("Keyword from library 2")

    def keyword_in_both_libraries(self):
        print("Keyword from library 2")

    def keyword_in_all_resources_and_libraries(self):
        print("Keyword from library 2")

    def keyword_everywhere(self):
        print("Keyword from library 2")

    def no_operation(self):
        print("Overrides keyword from BuiltIn library")

    def similar_kw_4(self):
        pass
