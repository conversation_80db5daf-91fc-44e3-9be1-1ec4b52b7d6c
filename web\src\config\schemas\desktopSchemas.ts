/**
 * 桌面操作组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const desktopClickSchema: ComponentConfigSchema = {
  componentType: 'desktop_click',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '桌面点击的基本参数',
      icon: 'Mouse',
      order: 1,
      collapsible: false,
    },
    {
      id: 'location',
      label: '定位方式',
      description: '元素定位的配置',
      icon: 'Location',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '点击行为的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    locator_type: {
      type: 'select',
      label: '定位方式',
      description: '选择元素定位的方式',
      group: 'location',
      order: 1,
      default: 'image',
      options: [
        { label: '图像识别', value: 'image', description: '通过截图匹配定位元素' },
        { label: '坐标定位', value: 'coordinates', description: '通过屏幕坐标定位' },
        { label: '文本识别', value: 'text', description: '通过OCR识别文本定位' },
        { label: '窗口元素', value: 'element', description: '通过窗口元素属性定位' },
      ],
    },

    image_path: {
      type: 'file',
      label: '参考图像',
      description: '用于匹配的参考图像文件',
      group: 'location',
      order: 2,
      accept: '.png,.jpg,.jpeg,.bmp',
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'image',
        },
      ],
      validation: [
        {
          type: 'required',
          message: '请选择参考图像文件',
        },
      ],
    },

    confidence: {
      type: 'number',
      label: '匹配精度',
      description: '图像匹配的精度阈值（0-1）',
      group: 'location',
      order: 3,
      default: 0.8,
      min: 0.1,
      max: 1.0,
      step: 0.01,
      precision: 2,
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'image',
        },
      ],
    },

    x_coordinate: {
      type: 'number',
      label: 'X坐标',
      description: '点击位置的X坐标',
      group: 'location',
      order: 4,
      min: 0,
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'coordinates',
        },
      ],
      validation: [
        {
          type: 'required',
          message: 'X坐标不能为空',
        },
      ],
    },

    y_coordinate: {
      type: 'number',
      label: 'Y坐标',
      description: '点击位置的Y坐标',
      group: 'location',
      order: 5,
      min: 0,
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'coordinates',
        },
      ],
      validation: [
        {
          type: 'required',
          message: 'Y坐标不能为空',
        },
      ],
    },

    text_to_find: {
      type: 'string',
      label: '目标文本',
      description: '要查找和点击的文本内容',
      placeholder: '按钮文本或标签',
      group: 'location',
      order: 6,
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'text',
        },
      ],
      validation: [
        {
          type: 'required',
          message: '目标文本不能为空',
        },
      ],
    },

    element_locator: {
      type: 'string',
      label: '元素定位器',
      description: '窗口元素的定位表达式',
      placeholder: 'name:Button1 或 id:submit',
      group: 'location',
      order: 7,
      conditions: [
        {
          field: 'locator_type',
          operator: 'equals',
          value: 'element',
        },
      ],
      validation: [
        {
          type: 'required',
          message: '元素定位器不能为空',
        },
      ],
    },

    action: {
      type: 'select',
      label: '点击动作',
      description: '执行的点击类型',
      group: 'basic',
      order: 1,
      default: 'click',
      options: [
        { label: '左键单击', value: 'click', description: '普通的左键点击' },
        { label: '右键单击', value: 'right_click', description: '右键点击，通常显示上下文菜单' },
        { label: '双击', value: 'double_click', description: '快速连续两次左键点击' },
        { label: '中键点击', value: 'middle_click', description: '鼠标中键点击' },
      ],
    },

    timeout: {
      type: 'number',
      label: '超时时间',
      description: '等待元素出现的最大时间',
      group: 'basic',
      order: 2,
      default: 10,
      min: 1,
      max: 300,
      unit: '秒',
    },

    offset_x: {
      type: 'number',
      label: 'X偏移',
      description: '相对于找到位置的X轴偏移量',
      group: 'advanced',
      order: 1,
      default: 0,
      min: -1000,
      max: 1000,
      unit: '像素',
    },

    offset_y: {
      type: 'number',
      label: 'Y偏移',
      description: '相对于找到位置的Y轴偏移量',
      group: 'advanced',
      order: 2,
      default: 0,
      min: -1000,
      max: 1000,
      unit: '像素',
    },

    wait_before: {
      type: 'number',
      label: '点击前等待',
      description: '点击前的等待时间',
      group: 'advanced',
      order: 3,
      default: 0,
      min: 0,
      max: 10,
      step: 0.1,
      precision: 1,
      unit: '秒',
    },

    wait_after: {
      type: 'number',
      label: '点击后等待',
      description: '点击后的等待时间',
      group: 'advanced',
      order: 4,
      default: 0.5,
      min: 0,
      max: 10,
      step: 0.1,
      precision: 1,
      unit: '秒',
    },

    retry_count: {
      type: 'number',
      label: '重试次数',
      description: '如果点击失败，重试的次数',
      group: 'advanced',
      order: 5,
      default: 3,
      min: 0,
      max: 10,
    },
  },

  presets: {
    image_click: {
      label: '图像点击',
      description: '基于图像识别的点击配置',
      config: {
        locator_type: 'image',
        action: 'click',
        confidence: 0.8,
        timeout: 10,
        retry_count: 3,
      },
    },

    coordinate_click: {
      label: '坐标点击',
      description: '基于坐标的精确点击配置',
      config: {
        locator_type: 'coordinates',
        action: 'click',
        timeout: 5,
        wait_after: 0.5,
      },
    },

    text_click: {
      label: '文本点击',
      description: '基于文本识别的点击配置',
      config: {
        locator_type: 'text',
        action: 'click',
        timeout: 15,
        retry_count: 2,
      },
    },
  },


}

export const desktopTypeSchema: ComponentConfigSchema = {
  componentType: 'desktop_type',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '桌面输入的基本参数',
      icon: 'EditPen',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '输入行为的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    text: {
      type: 'textarea',
      label: '输入文本',
      description: '要输入的文本内容',
      placeholder: '请输入要发送的文本',
      required: true,
      group: 'basic',
      order: 1,
      rows: 3,
    },

    clear_first: {
      type: 'boolean',
      label: '输入前清空',
      description: '输入前是否清空当前内容（Ctrl+A）',
      group: 'basic',
      order: 2,
      default: false,
    },

    typing_speed: {
      type: 'select',
      label: '输入速度',
      description: '模拟打字的速度',
      group: 'advanced',
      order: 1,
      default: 'normal',
      options: [
        { label: '很慢', value: 'very_slow', description: '每个字符间隔300ms' },
        { label: '慢', value: 'slow', description: '每个字符间隔150ms' },
        { label: '正常', value: 'normal', description: '每个字符间隔50ms' },
        { label: '快', value: 'fast', description: '每个字符间隔20ms' },
        { label: '很快', value: 'very_fast', description: '每个字符间隔5ms' },
        { label: '瞬间', value: 'instant', description: '直接粘贴文本' },
      ],
    },

    press_enter: {
      type: 'boolean',
      label: '输入后按回车',
      description: '输入完成后是否按回车键',
      group: 'advanced',
      order: 2,
      default: false,
    },

    press_tab: {
      type: 'boolean',
      label: '输入后按Tab',
      description: '输入完成后是否按Tab键',
      group: 'advanced',
      order: 3,
      default: false,
    },

    use_clipboard: {
      type: 'boolean',
      label: '使用剪贴板',
      description: '是否通过剪贴板粘贴文本（适合大量文本）',
      group: 'advanced',
      order: 4,
      default: false,
    },
  },

  presets: {
    normal_typing: {
      label: '正常输入',
      description: '模拟正常的打字输入',
      config: {
        clear_first: false,
        typing_speed: 'normal',
        use_clipboard: false,
      },
    },

    fast_input: {
      label: '快速输入',
      description: '快速输入大量文本',
      config: {
        clear_first: true,
        typing_speed: 'instant',
        use_clipboard: true,
      },
    },

    form_fill: {
      label: '表单填写',
      description: '适合表单字段填写的配置',
      config: {
        clear_first: true,
        typing_speed: 'fast',
        press_tab: true,
        use_clipboard: false,
      },
    },
  },
}
