<template>
  <div class="box" v-loading="loading">
    <div class="table-box">
      <div class="table-header">
        <div class="table-header-title">
          <!-- <i class="table-header-icon iconfont icon-yingyong01"></i> -->
          <el-dropdown @command="handleCommand" trigger="click">
            <span class="el-dropdown-link">
              {{ type == 'manager' ? '工作台' : '任务记录' }}<el-icon class="table-header-icon-right el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :class="{ selected: type === 'manager' }" command="manager">
                  <span class="el-dropdown-item-title">工作台</span>
                </el-dropdown-item>
                <el-dropdown-item :class="{ selected: type === 'record' }" command="record">
                  <span class="el-dropdown-item-title">任务记录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="table-header-tools">
          <el-button class="table-header-tools-item" v-if="$route.query.missionName" @click="handleCommand('manager')">
            <el-icon :size="16" title="返回">
              <Back />
            </el-icon>
            <span>返回</span>
          </el-button>
          <el-button class="table-header-tools-item" @click="onReset">
            <el-icon :size="16" title="重置">
              <RefreshLeft />
            </el-icon>
            <span>重置</span>
          </el-button>
        </div>
      </div>
      <div class="table-condition">
        <el-form :inline="true" :model="params">
          <el-form-item label="状态">
            <el-select v-model="params.executeState" placeholder="请选择状态" style="width: 100px">
              <el-option v-for="it in state" :key="it.Value" :label="it.Name" :value="it.Value" />
            </el-select>
          </el-form-item>
          <el-form-item label="运行时间" style="margin-right: 8px;">
            <el-date-picker v-model="params.executeTimeStart" type="date" value-format="YYYY-MM-DD" style="width: 150px" @change="changeTimeStart" />
          </el-form-item>
          <span style="fontSize: 12px;">至</span>
          <el-form-item label="" style="margin-left: 8px;">
            <el-date-picker v-model="params.executeTimeEnd" type="date" value-format="YYYY-MM-DD" style="width: 150px" :disabled-date="disabledDateEnd" />
          </el-form-item>
          <el-form-item label="任务名称">
            <el-input v-model="params.missionName" placeholder="请输入任务名称" :disabled="$route.query.missionName" clearable style="width: 160px" @keyup.enter="onSubmit" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">
              <template #icon>
                <i class="action-iconfont icon-sousuofangdajing" style="font-size: 12px;"></i>
              </template>
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-content crud-tag-style">
        <el-table :data="tableData" stripe border :show-overflow-tooltip="true" :highlight-current-row="true"
          style="width: 100%;height: calc(100% - 48px)" @rowClick="handleRowClick">
          <el-table-column type="index" label="序号" :index="1 + pagination.pageSize * (pagination.currentPage - 1)" align="center" width="60" />
          <el-table-column v-for="it in tableColumns" :key="it.data" :prop="it.data" :label="it.title" align="center"
            :width="it.width">
            <template v-if="it.data == 'executeState'" #default="scope">
              <el-tag type="info" round v-if="scope.row.executeState == -1">失败</el-tag>
              <el-tag type="danger" round v-else-if="scope.row.executeState == 0">执行中</el-tag>
              <el-tag type="success" round v-else-if="scope.row.executeState == 1">成功</el-tag>
            </template>
            <template v-else-if="it.dtype == 'date' && it.format" #default="scope">
              {{ formatTime(scope.row[it.data], it.format) }}
            </template>
            <template v-else-if="it.data == 'handle'" #default="scope">
              <el-link type="primary" @click="openMonitorDialog(scope.row)">运行详情</el-link>
              <el-link v-if="scope.row.executeState == 1 && !!scope.row.docId" type="primary" @click="openReport(scope.row)" style="margin-left: 12px;">查看报告</el-link>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 50vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery" />
        </div>
      </div>
    </div>
    <el-dialog
        :title="dialogData?.missionName || '运行日志'"
        v-model="showDialog"
        width="500px"
        :before-close="handleClose"
        @open="onOpen">
        <div class="monitor-task-page-content">
            <el-scrollbar v-if="showDialog" style="height: 100%; width: 100%" wrap-style="overflow-x:hidden;" ref="scrollbar">
                <div class="monitor-box" v-loading="loading">
                    <el-timeline>
                        <el-timeline-item v-for="(it, index) in monitorList" :key="index" timestamp="" placement="top">
                            <template #dot>
                                <div class="time-icon" :class="{ 'icon-execute': index === monitorList.length-1 }"></div>
                                <div class="time-box">
                                    <div class="time-left">
                                        <div class="time-time">{{ it.excTime }}</div>
                                        <div class="time-title" :class="{ isover: !it.finishTime }" :title="it.title">{{ it.title }}</div>
                                    </div>
                                    <div class="time-right-btn">
                                        <span v-if="!it.finishTime">
                                            <i class="el-icon-loading"></i>
                                            <span>执行中</span>
                                        </span>
                                    </div>
                                </div>
                            </template>
                            <div class="time-content">
                                <div class="content-item" v-if="it.input">
                                    <div class="item-title">
                                        <span class="complete">
                                            输入：
                                        </span>
                                        <div class="complete-content" :class="{ 'text-clamp-2': !it.inputs_expand }" :title="it.input">
                                            {{ it.input }}
                                        </div>
                                    </div>
                                    <div class="item-state">

                                    </div>
                                    <div class="item-expand" @click="it.inputs_expand = !it.inputs_expand">
                                        <el-icon v-if="!it.inputs_expand">
                                          <ArrowDown />
                                        </el-icon>
                                        <el-icon v-else>
                                          <ArrowUp />
                                        </el-icon>
                                    </div>
                                </div>
                                <div class="content-item" v-if="it.output">
                                    <div class="item-title">
                                        <span class="complete">
                                            输出：
                                        </span>
                                        <div class="complete-content" :class="{ fail: it.state !== 'passed', 'text-clamp-2': !it.expand }" :title="it.output">
                                            {{ it.output }}
                                        </div>
                                    </div>
                                    <div class="item-state">
<!--                                        <div class="item-delete" v-if="it.status !== 'succeeded'">-->
<!--                                            <i class="iconfont icon-guanbixiaohao"></i>-->
<!--                                        </div>-->
                                    </div>
                                    <div class="item-expand" @click="it.expand = !it.expand">
                                        <el-icon v-if="!it.expand">
                                          <ArrowDown />
                                        </el-icon>
                                        <el-icon v-else>
                                          <ArrowUp />
                                        </el-icon>
                                    </div>
                                </div>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                    <el-empty v-if="!monitorList.length" description="暂无数据" style="height: 50vh;" />
                </div>
            </el-scrollbar>
        </div>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import moment from "moment";
import utils from '@/utils/utils';
import {isNull} from '@/utils/validate';
import saasApi from '@/api/index';
import { ElMessage } from 'element-plus'
import { useUserStore } from "@/stores/user";

const userStore = useUserStore()

export default defineComponent({
  name: 'record',
  // components: { FailDialog },
  data() {
    return {
      loading: false,
      type: 'record',
      params: {
        executeState: 'all',
        executeTimeStart: null,
        executeTimeEnd: null,
        missionName: "",
      },
      pagination: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      },
      state: [
        { Name: '全部', Value: 'all'  },
        { Name: '成功', Value: '1' },
        { Name: '失败', Value: '-1' },
      ],
      currentRow: null,
      tableData: [],
      tableColumns: [
        {data: 'missionName', title: '任务名称', orderable: true, filterable: true},
        // {data: 'missionType', title: '任务类型', orderable: true, filterable: true},
        {data: 'executeUser', title: '运行人', orderable: true, filterable: true},
        {data: 'executeTime', title: '运行时间', dtype: 'date', format: 'yyyy-MM-DD HH:mm', orderable: true, filterable: true},
        {data: 'executeState', title: '状态', scoped: 'executeState', width: 120, fixed: 'right', dtype: 'code', format: 'executeState', orderable: true, filterable: true},
        {data: 'handle', title: '操作', scoped: 'handle', width: 200, fixed: 'right'},
      ],

      dialogData: {},
      showDialog: false,
      monitorList: [],
      dialogLoading: false,
    }
  },
  computed: {
    headers() {
      return {
        Authorization: utils.GetAuthorization()
      }
    },
    // 是否是集成到一诺桌面端
    isUniwimPc() {
      return this.$route.query.uniwim === 'pc'
    },
    // 用户信息
    currentUser() {
      return userStore.userInfo
    },
  },
  mounted() {
    if (this.$route.query.missionName) {
      this.params.missionName = this.$route.query.missionName
    }
    this.tableQuery()
  },
  methods: {
    changeTimeStart(val) {
      if (this.params.executeTimeEnd && moment(val).valueOf() > moment(this.params.executeTimeEnd).valueOf()) {
        this.params.executeTimeEnd = null
      }
    },
    disabledDateEnd(date) {
      let disable = false
      if (this.params.executeTimeStart && date <= moment(this.params.executeTimeStart).valueOf()) {
        disable = true
      }
      return disable
    },
    handleCommand(command) {
      this.type = command;
      this.$router.push({ name: this.type, query: {
        uniwim: this.$route.query.uniwim
      }});
    },
    tableQuery() {
      this.loading = true
      this.currentRow = null
      const params = {
        conditions: [],
        data: {},
        index: this.pagination.currentPage,
        size: this.pagination.pageSize,
      }
      if (this.$route.query.id){
        params.conditions.push({
          Field: "missionId",
          Group: 1,
          Operate: "=",
          Relation: "and",
          Value: this.$route.query.id
        })
      }


      if(this.params.missionName){
        params.conditions.push({
          Field: "missionName",
          Group: 1,
          Operate: "like",
          Relation: "and",
          Value: this.params.missionName
        })
      }
      if(this.params.executeState !== 'all'){
        params.conditions.push({
          Field: "executeState",
          Group: 1,
          Operate: "=",
          Relation: "and",
          Value: Number(this.params.executeState)
        })
      }
      if (this.params.executeTimeStart) {
        params.conditions.push({
          Field: "executeTime",
          Group: 1,
          Operate: ">=",
          Relation: "and",
          Value: this.params.executeTimeStart + ' 00:00:00'
        })
      }
      if (this.params.executeTimeEnd) {
        params.conditions.push({
          Field: "executeTime",
          Group: 1,
          Operate: "<=",
          Relation: "and",
          Value: this.params.executeTimeEnd + ' 23:59:59'
        })
      }
      params.conditions.forEach(cond => {
        if(cond.Field === 'executeTime'){
            cond.Value = moment(cond.Value).valueOf()
        }
      })
      saasApi.AIAgentMissionHistoryQuery(params).then(res => {
        console.log(res)
        if (typeof res?.rows == 'object') {
          this.pagination = {
            currentPage: res.current,
            pageSize: res.size,
            total: res.total
          }
          this.tableData = res.rows
        }
      }).finally(() => {
        this.loading = false
      })
    },

    formatTime(data, format) {
      if (!data) return ''
      return moment(data).format(format)
    },

    changeView() {
      this.$router.push({ name: this.type })
    },
    // 运行记录弹窗
    openMonitorDialog(row){
      // this.dialogData = row || {}
      // this.showDialog = true
      let urlOrigin = location.origin + location.pathname + `#/taskMonitor?historyId=${row.id}&missionId=${row.missionId}`
      if (this.headers?.Authorization) urlOrigin += `&uniwater_utoken=${this.headers.Authorization}`
      this.isUniwimPc
        ? window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
        : window.open(urlOrigin)
    },
    // 查看报告
    openReport(row){
      const token = utils.GetAuthorization();
      const url = row.docId + `${row.docId.indexOf('?') > -1 ? '&' : '?'}uniwater_utoken=${token}`
      if (this.isUniwimPc) {
        window.open(url, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
      } else {
        window.open(url)
      }
    },
    handleRowClick(row) {
      this.currentRow = row
    },
    onSubmit() {
      this.tableQuery()
    },
    onReset(){
      this.params = {
        executeState: 'all',
        executeTimeStart: null,
        executeTimeEnd: null,
        missionName: this.$route.query.missionName || "",
      }
      this.pagination = {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
      this.currentRow = null
      this.tableData = []
      this.tableQuery()
    },

    handleClose(){
      this.showDialog = false
    },
    onOpen(){
      if(!this.dialogData?.id) {
        ElMessage({
          message: '缺少记录参数，无法获取运行日志',
          type: 'error',
          showClose: true
        })
        return
      }
      console.warn({dialogData: this.dialogData})
      this.dialogLoading = true
      const params = {
        data: {
          historyId: this.dialogData.id
        },
        index: 1,
        size: -1
      }
      saasApi.AIAgentMissionLogQuery(params).then(res=>{
        let array = null
        try {
          array = typeof res === 'string' ? JSON.parse(res)?.rows : res?.rows
        } catch (e) {

        }
        if(Array.isArray(array) && array.length > 0){
          // array.reverse()
          this.monitorList = array.map(item => {
            return {
              ...item,
              inputs_expand: false,
              expand: false,
              input: isNull(item.input) ? '无' : item.input,
              output: isNull(item.output) ? '无' : item.output,
              excTime: this.formatTime(item.excTime * 1000, 'YYYY-MM-DD HH:mm:ss'),
              finishTime: this.formatTime(item.finishTime * 1000, 'YYYY-MM-DD HH:mm:ss')
            }
          })
        }else{
          this.monitorList = []
        }
        console.warn(this.monitorList)
      }).catch(()=>{
        this.monitorList = []
      }).finally(()=>{
        this.dialogLoading = false
      })

    }
  }
})
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 16px;
  box-sizing: border-box;
  background: #f7f7f9 !important;
  .table-box {
    width: 100%;
    height: 100%;
    background: white !important;
    display: flex;
    flex-flow: row nowrap;
    flex-direction: column;
    .table-header {
      padding: 8px 16px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      .table-header-title {
        display: flex;
        align-items: center;
        .table-header-icon {
          font-size: 18px;
          width: 24px;
          height: 24px;
          background: #ff884a;
          color: #fff;
          display: -webkit-inline-box;
          display: -ms-inline-flexbox;
          display: inline-flex;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack: center;
          -ms-flex-pack: center;
          justify-content: center;
          border-radius: 2px;
          margin-right: 8px;
        }
        .table-header-icon-right {
          vertical-align: bottom;
        }
        ::v-deep(.el-dropdown) {
          font-size: 16px;
          cursor: pointer;
        }
      }
      .table-header-tools {
        display: flex;
        align-items: center;
        .table-header-tools-item {
          margin-left: 8px;
          padding: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          // color: #222222;
          font-weight: 400;
          // cursor: pointer;
          span {
            margin-left: 4px;
            line-height: 17px;
          }
          // &:hover {
          //   color: rgba(92, 95, 102, 0.9);
          // }
          // &:active {
          //   color: #0054D2;
          // }
          // &.disabled {
          //   color: #BCBFC3;
          //   cursor: not-allowed;
          // }
        }
      }
    }
    .table-condition {
      padding: 16px;
      box-sizing: border-box;
      // border-top: solid 1px #e8ecf0;
      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }
    }
    .table-content {
      height: calc(100% - 112px);
      padding: 0 16px;
      box-sizing: border-box;
      .table-content-pagination {
        height: 48px;
        // padding: 0 12px;
        display: flex;
        justify-content: right;
        align-items: center;
      }
      ::v-deep(.el-scrollbar__view) {
        height: 100%;
      }
    }
  }
}
.el-link.task-link{
  font-size: 12px;
  &~.task-link{
    margin-left: 12px;
  }
}
.el-link.is-disabled{
  opacity: 0.5;
}


.text-clamp-2 {
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.transition-max-height {
    transition: max-height 0.3s ease-in-out;
}
::v-deep(.el-dialog__body) {
    background: #ffffff;
}
.monitor-task-page-content{
    height: 600px;
    max-height: 70vh;
}
.monitor-box {
    padding: 16px;
    .el-timeline {
      padding-left: 0;
    }
    .el-timeline-item {
        padding-bottom: 10px;
    }
    .time-content {
        width: 100%;
        border-radius: 2px;
        .content-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 8px 12px;
            border-radius: 4px;
            background: #F2F2F2;
            line-height: 18px;
            .item-title {
                display: flex;
                align-items: flex-start;
                color: #222222;
                .complete {
                    font-weight: bold;
                    color: #222222;
                    flex-shrink: 0;
                }
                .fail {
                    color: #ff7676;
                }
            }
            .item-state {
                .item-delete {
                    width: 14px;
                    height: 14px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    background: #ff7676;
                    cursor: pointer;
                    i {
                        color: #ffffff;
                        font-size: 8px;
                    }
                }
                // img {
                //     width: 14px;
                //     height: 14px;
                // }
            }
            .item-expand{
                flex-shrink: 0;
                cursor: pointer;
                padding-left: 7px;
                width: 14px;
                height: 24px;
            }
        }
    }
    ::v-deep(.el-timeline-item__wrapper) {
        padding-left: 16px;
        .el-timeline-item__content {
            margin-top: 38px;
        }
    }
    ::v-deep(.el-timeline-item__tail) {
        top: 12px;
    }
    ::v-deep(.el-timeline-item__dot) {
        width: 100%;
        justify-content: flex-start;
        z-index: 1;
        .time-icon {
            width: 5px;
            height: 5px;
            background: #FFFFFF;
            border: 2px solid #E2E2E2;
            border-radius: 50%;
            flex-shrink: 0;
        }
        .icon-execute {
            border: 2px solid #0099CB;
        }
        .time-box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 32px;
            &:hover {
                background-color: #F2F2F2;
                border-radius: 2px;
            }
            .time-left {
                display: flex;
                align-items: center;
                cursor: pointer;
                flex: 1;
                .time-time {
                    font-size: 12px;
                    color: #999999;
                    margin-left: 8px;
                    flex-shrink: 0;
                }
                .time-title {
                    flex: 1;
                    font-size: 14px;
                    color: #222222;
                    margin-left: 8px;
                    flex-shrink: 0;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .isover {
                    color: #0099CB;
                }
                i {
                    color: rgba(255, 255, 255, .9);
                    margin-left: 10px;
                }
            }
        }
        .time-right-btn{
            margin-right: 8px;
            cursor: pointer;
            span {
                font-size: 12px;
                color: #0099CB;
                .el-icon-loading {
                    font-size: 14px;
                    margin-right: 8px;
                }
            }
        }
    }
}
</style>
