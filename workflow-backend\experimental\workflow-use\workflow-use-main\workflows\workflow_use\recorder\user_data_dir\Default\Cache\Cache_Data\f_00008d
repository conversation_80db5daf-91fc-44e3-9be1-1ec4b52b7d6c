(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f9b9e012"],{"133e":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAb1JREFUOE+tU81qE2EUPedLRBe1L6ALwWRKaUHRFxAX+gZSUBdKG6RmQl257d5FdKJF0qKLSttX0IX4AopifzBG6KI+gKkLxZl75JvOhOm0ZuXdfffn3HvPPR9Rsgc1TcW0WZHXCZwB4CDswuktnVuOdrhZLGH+aFzWiVP79lhgA0C1DJy9jdDzX6fdQvc9/3hfCuCLTw70BsSVfxQedgvvfo/zmgdJAVpB8kzgfKnYJM6SfhotlacitBT1KvfZmtS0En1Kdy0YqbXoS+Vm1mBV4K1Sg7hqvMhwInkCsVUKDmCc6vS55/1hTefh9AHA+GEC1WZYt88gprNATGhD5hY7fX4rJoc1nZWzRwRv5NMK2GEY2D6AsYNkNjo9Lo8islnXHVIvspyBB/iRjybx7tOvfDkKIAw0B6ib5fxkM7BtApPDO1MbStzDfP8czPNAZ4sCZ4YXETYZ1pM2yIVjSLyU8+D3h9NWmURQEVPpOn085s6vol7ltgduBsk6DzoXzVjhhVFCigHOS4hJrRzRSS6k/yLlHCT7TPfK3Qpze510j3ym4mKptM3mYLwK4hwAE/Cd0uuq3Eq7T0/m0P4CWgXKoj9vP5cAAAAASUVORK5CYII="},"219b":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAABXRJREFUWEfNmFtsFGUUx39nti3CtmpUQIIIFGS3pRARo0EwKMFLgoZry0UK3RYrPEgkJoCCCQ8aE4MaYrmE2m4pRIpNxMTgA+I1QR+MSiDtbk2DVZGEqDSkLaXtdI7MdJZsy7adbRH6Pc6ec77fd+Y7/3NmhSG+ZIjzMWQBi2dqamMm1pADzJ+ko1JSWQQ8ayk/DQFAlYJsRhsWsxUWACuB29yrd+mWAhZn6RgT1qkyD3gYSO9REzU3HXDtZJ1k+JghQpHCXGB4b4Wqwr6bBhiaqtlqskLEeY1BYEQ/CqLA+v8N0K7C9stMkK7XtxSYD0mpxgVDyLvhgLmoz5/NNFGKsZiFkAOk9JItFeUrFaYDI7vZKKfUuEGAdrautJJjKLMM5SXFgTL6eIUmynEMjqKEgMcS2B7VK6waVAYVlaIgTwHLFJ4ExgOp/dytCyglAictYavA0wnthbfCEdmeNGD+dPUb7QQFnhNhOUqWx3b5D3Cs02JbisH9CtXA2N58BZ4pj8pxz4DFM3XElSbmGgZFAjOATI9gtlmt0cnmYal8e7mTOSK8C2T34a+YjA7Xy999AhYGNMMWUDWYp+qAjUkCShUaBN4T5UBTC6Y/ncUCZXGdordwteGoTLV/TAiYe58O9/tZ6WrWbGBUkhJhRz6Esrc8yg+CaCigGxG2ubH6PKcoB8vrZE03wNBkHSk+chRWISy8ruy9pc4S5WtL2FURlc9sl6KpepdlsQllu7cQWMCGcFT2O4DFmXpHRypLEPLpkofueuQxKnBRYJ9psafyV86D6BNoysQgu4EXAL+XUAKNCHnlETnhAIaC+j0wy4tzIhs7oApfqLKlIioNMZvCaZppdfCOdHWRZNYvho+8shqpdwALA1qptlxAWjJRXNuzAptT2jmx/6xcivkXT9F7OgyOAI970MXu2yqfN/tYXl0rze4dVCkIMl5gmZtJu+2MA4b1Aqwo5yyDko4USj86I43xdoVTdI4a7AIeGsCB7eJ6MxyRN2K+3arY0bpWAoYSEOV5cBr9vfEbKRy7OrftnBDlux2IfaGvrYJsfVAsDgOBpKvejSLCkvKIHE0I2PPEubnqSz/DHEuYbyi59sa+NMZ9eFrOdYdWCWWxWpS3++oOHjLaickYW6A9AXbLTkALRdhrCEvKImJn0Vk7UOP3gKMCewahALFwDeGo3aHEngWd5bnVFUzRoBh8LMqpJh/rqmul3Q5QkKX5opQAt3vIUH8mleGorI038gz48mQd1pxC2J6G42UglKVHUPL629nL76q8UlEndoFdW54BbY9QQBciVKkQqohIlfMsqD/TNTwMdl20DPIO1MqXAwZ0uk4af12t8sNldfKi84qDej7JIaK3g9TgIy9cI7UDBnSBDomyIDWdsY2tmOkWrf1Mz54ya8tXi8GKmEAnXSQxh8IsXaNKqXSyoG040bR2/uxB0Ab85n65eYJzy/X9cIRX4ys4qSqO7RSrZoQfUSqBb+Io2gS2+U1KmnysF2GT+xnQL6gKiysi8mlPw6SKxHaOq+YHBKoUdrpB21TZm9HJ1g/qpS03W9P8Jo+KQanbWfqENA3GH6yVPwYN6NzDLF0k6gwDtuLb3xVtCq9nmOy24eI3WZejoztNttgf4X38i9AQjsrERCdIOoN2EHeGbEC404a7OiXvSTd5rSdcbEO7x5stbFDYnHCiFj4JRyThWDYgQDeLK1BWC1Snm1T1BhefFef++ihFeSRuvFNVNvUU6AFXcczRHiQyTjOivE6a+q2AOIOCoE4QnOLZ6D7+1zJY3lOgBw2YDFQi28KgLlUhD4uTzS2UVp8TW0+vWwN+xYMFjCnC3fV09Jwr42PfUkAvhxzygP8BCn/UwqTNWiYAAAAASUVORK5CYII="},2203:function(e,t,i){e.exports=i.p+"static/img/knowledge_icon_pdf.2d02238d.png"},2285:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAA71JREFUSEu1ll1oHFUUx//nzq5MpIkPVqGKD/qiD9JC9dFaP6KNgkWoLhjiSsPOnAtChaJWEI3UtIqInwnuvZONQiO1a0GhUlAaP6qtIArii6AvitVqtCRqMIbM3uPecVdGqCalkwPzMHfunB//e8495xDO0MbGxs4vl8saQAwgJKKDzrnntNZfr8QVrWRTfo+1dqeIPA5AADgA5wKYjuN4gIj82v/aGQONMT8AUKVSaWNvb+/s3NzcQRG5VUT6tdbThQKbzeaa2dnZX4noSBzHW7xzY8ztAPa11Y3EcfxMoUBjzBUAviSi/XEcD3rn9Xr9aiJqAjjEzPcVCqzX63cQ0evtuD3FzLu88yRJ1jvnPPATZq4WBhwfH19TLpdfFpGBIAgGa7XaoQ7wqg7wrUIVWmtvEZFXiOgbpVSlVqt964HW2n4RaSqlno6iaO9ZKxQRstZeCeAwgLVENBjH8Rtdx8aYR9pZu1tENmutj541cGJi4rZWqzUCYD2AlwDcz8xLOeBnADaIyKZSqfS7c078EwRBdieJaCkMw5mhoaHfsvdGo3FRmqaXKKWo1WotMPMX1trLANyslNrqnBsAMENEe0UkYeY/urBGo7EhTdPPl1MFIPU3CMAeMsb449no4QAWANwEwKe8j4dfmw6C4Pm+vr7DlUqllXc+OTl5wdLS0gMAzvPFoPuNiPIFhURkrRcAYNQDW0R0SkTeBvDL4uLiQ2EY7hKRHUEQPFir1SZXoGDZLUmSvOqcu8YnREtEjjPzpu5fSZKMOOfuEZGK1vrTZb2tYEO9Xj9ARFszoHPumNb62lwiPAagWhSwk+nfATjVVfgRM29eLaAxZh2AE0qpfT6GTin1YRRF/wCTJNntnBsqSqG1tiIi+wFwBgRwlJmvy8WwaOCTIhJ5ARmQiD6I4/j6HHDUOTdYhMKpqam++fn5JhFdGATB30AA7zPzDbkYjvq7WASw0WhcnqapB54Iw7Dik8Y5597TWt+YA+4BcFcRQGvtFhE5oJR6IYqiRzOgiLzLzP2rATTG3AvgRSLa5ou+P1JfZI8wsy9pmRljClPYnhJeA3BnT0/Pumq1OrPqQGvtV+26fE4URZf6qa4LfIeZs6GoSIWdTvQ9gClmvjtrT50jXRVgkiTbnXMTSqkdURSNd4G+JXn7mYhOioi/jw8D8G3nRwB/druNiOQH3dMNvfm1cnvGuhjAmwB2MvPJLvAJpVS/cy4A8BOAbUqp7b4ynK4J/Jv5321CRBaUUh+XSqVnh4eHfeHO7C9fMB0D6lSEOgAAAABJRU5ErkJggg=="},"35b4":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAA9pJREFUSEudln9o1HUYx1/P93u3XGJOI/sB/SQYDdl9z5EwdTGwLOmPaNIFBRUU1Ga/LGmm37lvu9tyqAvU/qkwI5ExixD8w6JciVaS292IhqHYD2qtIhtr5na77/dpn3M3djnXXc+fX577vD7P87yf9+eES4R2PWDTf/JGQqFVKLWg1cBVQAbhV1Q/Q6QbsQ+zsWdQBJ3pKJnpoypC3KlHeAyIAmngBDCIqo3I9cAS0AzIUYRd4qYOFARQz7kJmzagDjSJSAfh84ek8du/ph+gXuUibHsNqk8jlANb8Mc7xPvm7PS8vArUqw1hD+0H7gXdgVg7xU3+cKk2mu/aEo0g6gKrUV6XzanGGQHq1c7BHmoB6hFtF7cvMdvB+dVgYUf2gDwI+hR+3zviEZicqQq0rbIG39oHcoxQpl5e/vrPQgHZSl69/RYy43uBMPCQNKVO5QPijmnNKny5TbzkQDGH53K1pbIGsboR9XIdEPUqSgiX3EVAJ8h3+GPLxOsf+V+ALVXzGffNzUfAasAfPSwad5pBNyAygLIIOIIffly8rwaLgWjr4qsJQruBOxB+Q/U6sNoM4AzwM2o9jwT3Aw0gMWlKflwUIB69E7QLdBdqH0CCHQjXGIDZwK3SlHpJE84ylKOTgPeKAiSiZie6EGu5uL1fajyyDeTFmQDHEGLipszQCw6dBeCDviZNfes1EVmBivGYmLjJ97PyM55k9Bzb71+kfWot8T7NZPOmANSIm/pc404H8Jyp4CeQM9iswdcngA0EGpPmvg+zP4wvWYcEtripbXmAhHMfyD3iJuuzea9E7sYSM4M2fH0b2zIXvNkA1gJma8tMHrAHf3x9zlM0Ed2J6pOgdcy9/BOGLxsnPLSYgINYDMim1NIsoG3plfjp7cCjkxcZmnBeV7S9fB7p0pWgKxAGyFid0xdNWyJRRN4FFk74zXHg/AUnZQHIWmlKTolh0g2OQFYoWyn5u3tGu/73ZLW56gZCGVNlBYixgh8Rq9WoZWqLPaeMEG8AVQRWTDb39uRZxX/JRTuqSxkbuZaMhPFLfxHv+HDeTOKRdRPW5hHomyw4t0mePT1WHMA8Qu1VVzBqzKznbM4t1ateiD36MOh2lI8I0nXi9ZsHKhsFteiCSqIrEX0GYS5IJ25yNwnnVpQEwmqEE1h2g2zsOTm9soIAk0L4HmEYxbSmAugFHOB3YB9+2p1+86Iq0FbHISCZNUXsUxAYUxtF9AMs2cuc0l554QujrouiwAqWzyN97vTk4/8HaDli1YibNH8EZo2CANkZtEQfAW1EmA+8hV+WyNnEbIR/AErjoBeJf8pyAAAAAElFTkSuQmCC"},3762:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABEpJREFUSEutlk1oXFUUx//nZpJO0iZNqfUjlSKCCuKiK41IEXEhbhQEU4xfMTD35Y0ZNRK0BRezUdMaTUggee9MCMGAxI9F/QIRwXbRgoIggSwaUMGWBIOL6CQxmcy7x9xxZvoyncxMTB8Mw3vn3PO759x7Pgglj9a6SUSOK6UeFpH7AdwH4BCAAwAiAOpKlhgiMiKyCiBNRPMiMhuJRD7NZrM/M/NaWJ/CL8lkMrKwsHAWQAeAo6Wb2eX7VQAf+b5/ioiksLYI7O/v359Op98WkVfzwj8BzBPRHyLyN4AVItokosAYU2QTkbWhRKSRiFpE5FYA9wBos0pENLy2tvbW9PS0jQCKQNd1TwRB8DGA2wBcjkQirxhj5owxS8y8WYt3IkKO4xwmontFJLXl4d0AFpVSnZ7nnd8GdBznPRHptwoAnmXm72uB7KTjum573oFjRDTm+/7L24Ba6+8APArgglKqw/O8pb0AE4lESyaT+UREHlNKXfI876FS4DyAuwDM+L7faQ/add07jTHjAG4Oh7/MRkREvm5qahocHh5eLsgdx5kUkZds1Jj5vzMtCLXW1qMjW4fupVIp137XWtv/sRo9/dUY0zExMfFTCDhsLyERrfq+b9NqGzADoB7A+8xszxK9vb1tmUxmRERaKkHz1/5CJBIZGRsbWwk5cQbAGwACZrY5vA0Y2OsN4Cwzv1lY1NXVFW1tbY0uLxcjdR27oaFB2tra0slk8lq+bMXRcZwBEbG2DDPnCkY4pLnkVEoNeJ53usYwVlTTWr8L4JRVYuYc6zoggAFmzgHj8fgBY8xpETkSspwhom88z/uq2qZ2DdRa9wH4oIzhtO/7B8Mlqxx810DHcR4Ukc/t7Q17KCJfMHPHDQdWC1k1eah61XaG1QxWkicSiX0bGxvTAJ4GIMxsM6DypfmfQIrH4/uDIDgpIu/kq9QmMzfcEKDruoeMMU+JSHu+Pdlm3EJEtnnflN/0CjM37xmotbY39dv8ZLBjQIjoiu/7x/ac+D09Pc8bYz6sFnoi+sH3/fZSoC1LtoGfsWNBNSNWHovFRogoUYPuEDO/XgpcB7APQFFYzZDWegrAi1X00gBeYOZzpcDfANwhIp9Fo9HnRkdHN6oBQ/2ukupMNpvVk5OTFnwtLRzHOSciTwL4pa6u7uT4+Hixr5Wz1tfX17i6ujoD4IkKtDWl1COe5/1Y0CkW71gs9jgR2UQ9vJWsc0QUa25unh0cHMxNW+HHtqxoNNppjBkoKXthNTvtvZZKpeymik8R2N3d3VxfX2/HxMIl+J2IZkXkylaLsedrB17bwuqNMUeJ6ASAW3bw7i+llG5sbPxyaGjon7JA+9EOwouLi4Mi8ky+QlQ7xrDcbmZJRGzbGkqlUnPlFm+bvK2C7YFBEDwgIt0AjgO4nYjs+J8bEWxdzP/sSLJORHYWuioi55VSF9fX1y9NTU3ZiJR9/gWRsxY76PBaKwAAAABJRU5ErkJggg=="},"38d7":function(e,t){e.exports="data:image/png;base64,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"},"45dc":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAwNJREFUSEvNlU1oE0EYht/ZSXbbWEtJrZYc/Ecr4g/0UhC9eFLE6qEXD1IK3WxDiwRBEISuB1ELhWKwYdIIgrdqLyIIFsWjHhQ9WIo/BxFXIiqipmCanc/OkkBa02Sb0urckrzzPTPv+80XhlVebJV5qAo0TTMO4ByA1hoPRwC+ENEtwzCG/QA/LQNWesZZTdOuVQV2d3fXLeVmuq4bRLSBc35CShkDsKlk/1cPaJpmMBQKrVlK4XJaXdfdoaGhn+q3rq4uHg6Ho0R0vURLzDTNswAuAGhaLrCw/y1j7LwQ4k5/f38kl8t9LK2rgO8BbAQgGWNZAG6N4AARKZeUa89TqVS7bdsBx3FmFwJVF7mMsQ4hxDMA6vOSl23bmuM4RwDcU/WEEEFVJBqNyr+AjLEZIYSXYTwer89ms+0AQoZhPEkkEj/80m3b1h3H+a30kUiEDw4OkmVZ3wDUE5GhvleWkrJSCNFQsGACwDEAmrImFAodHhkZ+e4HWrilF4kC2rbt3S4WizXk8/l3ANbPA/b29u5gjE0B4EUA57wzmUzerRVoWdZJKWULgGEADfOAfX19W13XnQbg+a8WER0dGxu7XwtQWbpYhp6l6u00NzffkFKeAhBgjD1SD3h0dPRXJeD4+DifnJxs0zQtQkQPfGeohCrHTCazTUpZp+v6dCKR8Jpg4VITKBgM7mGMWXNZHwewrlRTmmFPT8/aQCCgMmyZZ6kf25RmYGDAyOVyV4moE8DmcvuKwIoZ+gFalnVgrsUvE9HBSvrisyibIYBZxthuIcSbSkWi0eghIkoD2F6YKIvKqwHVxg9EdIWIJtLp9OfixFGN1NjYuIVzfgbAaQCNfpwoWmqaZhvnvMV1XfW0mrwMSwrMAHgK4AWAaU3T1FzcJ6XsALC/2q3KNU0sFmvN5/PqHT4GEF4I9HN4Xxo/lvoq5Ff0vwCnVtxSy7IeFkZkhohuriiw+G9RGsM/Ab4EsNdvM/jUvU6lUjvLaVlh1l0CsMtnsWqyV5zzi8lk8nY54R8BhIcpJ6jXIwAAAABJRU5ErkJggg=="},"5ca0":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAe9JREFUSEvtlr9v01AQx793kQk7LcJmYKJrd2bYmSqxEaE6Fh4ZkBiqV3VAsCABluKzUEZ+DAyMiJn/hIKUIsFGYvyOvKgNiZUfdptm6pss+9773Pfe+e4Ia160Zh7GwFartdlsNmNr7S6AawC4pjM9IjrwfT8xxth5e8fAKIqMtfYxgMs1QZPmv4ho1/f9j/OgY2AYhocAfGa+Z639AiADcJeI3gB4VXaCmYOiKJ4DuMnMD1T1lqreBzAAsB8EwctZ0ElgcRzGTRE5arfb71V1h4iepmn6pAyM4/jGYDD4QETbx3ZfiWhfVR8CmKt0EqjuUM/zNpIk+VkXmGXZJ1WlKIpeL1K6UqBzOAzDjUVKVw500LJSItpL03SUB+cCnKG0JyJXzxXoDo/j+Eqe50fuWURG4qoozBqNxotylhZFEaiqe7/lstQlzYxMrgVMVTWsUAR+A9gRkc9nBd5R1cT92AugSkTvVPWRiHw/E/BkszHm0gKgNcb8nfe91h1WCOVSkwvgVC1dGq8KBhchXXtIpxpwhStaauJa1bCk9gBYEWmUa+m3YY0NTkYMz/NGik+78jwnZr5trX07bJOHInJ9CriiIWqWf3+Y+Vmn0zFTwBWMiWWYGxV/MHPW7/eTbrfrQvu/PZ02dHX3rX3y/gcnJtksmSNYIgAAAABJRU5ErkJggg=="},"5e64":function(e,t){e.exports="data:image/png;base64,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"},"6db7":function(e,t,i){e.exports=i.p+"static/img/hcs2.5fbe7eb6.png"},"83cf":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABF9JREFUSEu1lm1oW1UYx//PuUmsVASdbUwaRZiidTAmTgURdbIx/KIOaQv7MlFyc3PbKkWHUmTGiS9QXZE2LzcxdPuoxQ23D6Od+IbF+bbNsVd14AtN11YrU2ttcu95zClNadObJup2vgRyzzm/c57n/3/OQ6gyOjo6gvl8/mYAG5n5DiK6AYCfiHzMfIGITkopj2maNgzgRCqVGl1pS6r0sbOz8zLbtjdLKbcz8y0ArqlytgsATgLoCQaD+2OxmHSbvwwYi8XExMTE2kKhYBHRegB5AGeKN/xUCPERM5/2eDzjMzMzeZ/PtwrAbcy8gZkfALAawIgQYmsqlZqoCRgOhx8kotcArAUwTkQ7ARwKBALnKp1aHXJsbExF4VZmPh8MBg/ncrl6n89X39/fn1sMXrihWjQ6OrqZiN4DIIq52k1EL1TLSaUwRyKRIWa+z3GcYDabnSrNWwCaprnOtu09ANYA2KNp2jPJZPK3aqJaAfgsM6+rr69/vLe3d2YJUAkkn89/wsx3ElFWCLH9/8AWH6KlpcXX0NDgn5yczA0ODjpzN4xGow85jrMPwKQQ4vb/Gka32xqGoZS+g4jesCxrLymfFQqFt5l5PTM/nclkEm4Lw+Hw3cyshUKhz2KxmF1rqCORSBsz7wZwRAjRSrqubyiq6x0APzuO05rNZr9320zX9Z8AeInoLU3T4olE4nwtUNM0m23bVvtfJ4RoI8MwXpZSdjNzXzqdfqqoTC7fSCk4l8s5i/6fBPA8gAPpdHpsJfD82l4ATxLRLiqGapiINhHRoyrGbotdgGraX8z8hRCiOxAIfF7Jo2qiruuPANhHRB+okH4L4CYiWmNZ1ik3IDNTJBJxLVUAZonogBDiJb/ff8otv7quXw/gx7lCouu6qoFXOo6zarFBF4OrAEtTjwAIp9Np9btkmKZ5hW3bfwD4uybgfFiW5XZJySI6DMCwLOubcmBXV9fV09PTvwL4vaaQVgHOENG7AF6dmpo6q8xdDtR1XdXZ0wC+q0k0FYB/FsU2wszdlmUddVN3CWwYxhYp5V5mPlSTLVyAo0TUnc/nDw4MDCiLVBwq/4ZhvMnMnUKIV/6N8U8AuLxo4KzX67Xi8bjKSdVhmuaNJeMDaK25tEWjUfUoe0Oh0NGVPOeSv2jRBbuI6Cuv19t2SYv3fD/0NYBGTdO2JJPJ/XNA9TzNzs5+DOCui/U8RaPRq6SUPcz8hKpIdXV19/b19c1ekgdYwRzHeR3ANtVYeTyebYlE4pi63EVvMQzDaCoq80VmfgyAZOaHm5qahkp5X9a1lTdRzLzT6/UOx+Pxc5W8pqTf3t6+2rbtTUXIDgDXAjjOzM9lMpmDSypSuaoqtImqSowQ0YeqZbRte+4tJCK/pmnNUsr7iegeAM0AVIOsFBlpbGw8Xq7oi9kI/0JEZ4QQPR6PZ0gJxM2kFYGlyaVWXwix0XGchVZ//vt4MVc/aJr2pZTyfZ/Pd7a8Dy2H/gMzK2+qk8igfAAAAABJRU5ErkJggg=="},"84d2":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAXdJREFUSEvtlr1LQlEYxn/vlcqlrSACqa0b/TVtbXoJcquhralJaOtjKSnUpmhxbax/ISivW1GIUFtQV9T7xhEUM01Nb4R4tsu55/md5znv+RDH1mMgCoQJtnlARhxbP/4AVrfiGaAGa+yr+hjYLu0CFvlah88SMN/Pkvwm0mTKlbiBOLaeABtjYHMC/zBSZUsgVZ+lX6KcfhBzahBb1LA1xUS9T8FBOPhpTXtxGE+5kuylMBxbTQGZQurYugIFEijZuoJYvJzl5NF8ry/rgvrMNtSFVYWdgYDAq8JbU2wXGVdqolFbEwJrjcnANDAzKLB1/IjvQ4W0pWzWTjbhUCDWS0E1Yu/7elKeRLgzAqqsIESCBfaj3ubfrttiQP1vw0ceWO7k0AdyAtVhRapQEbhqBywJnGuIfakMD1itUnn3KLQCixZsR+a43L2WyrDcdboP8+bgTblkBQns6Wgcego3WETT91IMwlWrw6PQJHunt/IcNMzofwKxObrKYD2jFAAAAABJRU5ErkJggg=="},9222:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABDtJREFUSEu1lm1sU2UUx//nuZdujIlB0MFwxmRru5eI6CZ8MSJGXOCuFWLWGT+IYW/MRDRRoiFGq8mMyRSjRpLbsYTFD8Yt2aRlkg0iaiQaGEjQuq3diMa4F18wk7mtXe9zzG02qN3K3cbot9t77vM75zzn5U+w+DndTdkwYk5D4FGS9ACAuwmcxYANwCgTBQFcUBldkowfQ8fqfrvekZTqZd72D9IUkV4qifeDkQ9gjYVvowQEIdAQKh7yw+uVc9nPBnq9wn4mewMIOsAlAKIAeonwDRN9yULtUcX4CBvLoySiq2VMuQ+ErSz5EQC5IJyWy+RTA+11v88L6HD5tsPgt5mwAcAICXpTYXmiomR4wJvCa3i9Iu/7tfmKgUIGDWePDX135fZ1K8Yi6oo+f+VgIvhahF6vcJzLLmXJRwEIAh0BGa9b3UmqNDt36J2SsMWWoWQHW6suz9hdBdp3+DYSuJkJRSBqTufYSz90PPu3VVGlBGq+lyXxxjXLV+35ttUz8T+gWSAk0r8GeBOBmtIQ238jsEQnCstbbJHoWNb9tszB1laPEY/QqfncEtwO4A8iWbzYNM4Vbb7mK5XAa6zQu2F/dRuZfSZl7FMwSkjQi6FAzaGFpLGwTL9ritECYDMgasMd1b7E7x1ljRXM8ggB50HSQ05N3yqBFhB+lULxDPir+pcSaNcaCwBpOpSjgCooz6XXk8QBEvRhKFD9PECcCMzT9BfMZ0pb5g+37bmU7IxVhGbLOLvXvydZ7iOmg2TXfF0AbwP4iXDH3rbkA+2aHneASXH3H6sKLBgIwOHSd7JEOxN/YQJDANttKhcFj+79yQrodDfdwjK6EiQioeKhy4Xd6+683h2a501n4ReARsih6aMMrLRlKKsTG3QGnByho6zxabCslcB5mYFX0ydwqyWw/KPMqXH1CoBJcmi+UQbPH6jpBxioJ+AUBDwqI8MKWFR++LbouPEXgf5ZcEodiwA6XHo+S/QAFF5w0SwKWObbxcxtAJ2wbItZd7jgCJnsWuP7AD/HAm9ZNv6NAovch/Oi0mgBI0cAHsvR5nDp9dMV+3EoUNub7/JtkeDHCOJSJuQnhmKz/RubepKIcwD6LBSoOZvYWk5Nr5PAQRC6hVArburwNuc0G7FzDNwhQLv6Omr8ceD0evoK4M1LtZ7u0Q6tikBtYHAlQGdYTj7Uf3xf5KYsYBM2Seo7YN5NjCCDdoc/r7kQn8lX871EEuPesub1Exx5g8HPAJAk6PFQ8WDnjIqbpdrmElEGia4Bf+VA8ia5VhxMue6mXNUwtrG5bIG1xLgIhV4JBWqOzy2iZv6dSyYSegh0mlmesqnohU0dNs2j45wFGAUkxMPM/CAYBYgLZOoGoza8afBisj5dSiH8Jwi9gqnBkJOdZoHMS5cmG6WS+vEdCRoB8DMLPqtInISi9iXr0OTz/gM8rZasvmQywQAAAABJRU5ErkJggg=="},"94cb":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAP9JREFUOE+lk9ENwjAMRK+b0EmASYBJgEmASYBJYBT6Kl/lJIr6gaXKbXI+22d30J82VPFnSUdJm8l/4+46fd/j7Bb+JOnFfSbgchdggiA5JELwJiIYTEHwDNb5IhlEmCsqcLkCslPFuCJLl4BMH0l799ch6hKA77WRucA8Qo9CA0BMgEl4CvSNYO9g2E7+kqusx+g2UJssBKAND2Q+t6BNBW5jKXFtz+oKwNMCGRETy0vV8JnASwOA7AjFONGE0dqsCb5YJAIg8YryzTtAL5KrsS6cj1TACwEYIqE4Z6hvsUyCh4DKsJnAzPTN7gPKWXPfELqN5l+oBcoky9hq0A9iPj8R3UHBHwAAAABJRU5ErkJggg=="},9550:function(e,t){e.exports="data:image/png;base64,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"},"994a":function(e,t,i){e.exports=i.p+"static/img/knowledge_icon_img.0acfb0b5.png"},accb:function(e,t,i){e.exports=i.p+"static/img/<EMAIL>"},aede:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAA3VJREFUSEvtll+IVFUcx7+/c3d2cKAeIsJZyf48RCUp9BKSIPhQLxpEEFZY9Oeec3diF7YgW/HPVUojrKVdnL33zERRSD4EQflSD2JkQS8RiiZRLiTdLfEhElbHnTk/97feWSb33h1zzad+Twd+5/w+v/8cwg0WusE8dAUODQ0tmZqaWsHM6wCsAfAAgFtTR88COAbgCBEdKpVKx0dGRs4vFMSCwEqlsrTVau1h5rUA7koNOQB/p+ebAaj0PEFEX3ueN1ytVv/Ig+YC+/v7n3PO7WLmZQBOAfhMKfVloVD4dmxsrCEGBwYGitPT0w875x4F8DiAu4nodwDb4jj+KAuaCdRaPwJgf5q6j4lotFwu/xCGoUQ3T8IwVJOTkw8y8yCATQAk1c9Ya7+68vI8oNb6oRS2lIiqcRy/9m8ayxjzNjNXAEhaBfp95/t5QN/360T0PICDAF601oq3Vy1aa2mo9wGsZ+YParXaS7nAIAhuc879BsDr6elZWa1Wf7pqUsfFSqVyX7PZPDpTy5ZSankURWfa6n9EaIzZwMyfAzje19e3Mq9m3ZyQmiZJIsAVRPRYHMdf5AFfYeZ3AByw1j7VzfBCeq31JwA2EtGrcRy/mwnUWu+YUYREVIvjWC8GaIyxzOyLPWvtzjxgCECg1lprsoDGmM3S/p7nbRwfH/8mzymtdQxAnN5prRW7szJbQ2amwcHBmxqNxjCA1/OAYRj2JEnyC4A7iGh/qVTy81ZZB/CtYrG4Z3R09BwRMWmtxYvtAGSjtCUzwiAInnDOfZpemlRKPR1F0eGsKDuAbbVsoF0ClDG4/YpHc8AgCIacc7IMZGfeL503k4ULAIoA9nqed8Q59yQz986mjOhQo9H4sLe39700pZ2mTwuQMzycA2qtf00Xd3uEThDRPmYeA3BOZg3ALW0bzHy4UCg822w2t2YA0RVojFkt6ZY6i1HP8063Wq2jRPQdM69KQTJzb6QRTpTL5R+TJNl3TcC8LvR9/wXZtQCmlVJ+FEUHOu9m1PByl3ZLaR4wCII7mbnOzH85516u1+t//qfAbkvhukd4zUBjzClmbn8fxI503XZr7e5uRhfSa623yNxJn81tGaIJCoJgk3NOlPcCuEhEB51zw7Va7efFAH3fv0cpJf+h9QBkRk96nvdm11/bYqBZb/8HXu+M4hJwgHuowTme/wAAAABJRU5ErkJggg=="},b617:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABCJJREFUSEvtll1oHFUUx/9nJruGCLYbManTgtiKPqgU8a00qMEPFKsPZddqSVvpzt3Jpiuholh9GYsf4IN5SNbde2etkRRbEqmKH0UriiIoNdhqQbEPWkEXKrKxghuzZu9xb9kNk83Wpts38T4NZ845v/vxP+dewgWM4eHhleVy+XUAfQCiJpSITjLzbqXUu8tJRctxavikUqn7mfkNAN8C+K1u3wjgLaXU5uXkuiCg67ouEb1kWVYin88bMIQQZ4johJTSgM87/gcu2qL/1pbG43G7p6dnXaVSubKxTCLaBOCRmkoTSqk366KZAfAzM+9q+HV0dMzNzs4eHx8f/6tZRS1Fk8lkLpmbm8sB2AbAbgr6GsBArQxOGLvruq8RURxAR5PfMcuydubz+WNhe0tgKpXaxMyHABy1LOtII0BrXbFt+6Pe3t5p3/fnjT2ZTF5t2/ZdRLQq5Ge+XQCTSqkH/xWYTqdXaa0Paq3X1Lfuq/MWVwsHIcQ7tQnfY9v2hlwu98XCsTT7ep6X0Fq/TET7o9Ho8Ojo6Fw7QM/zNtfzHOrq6hoaGRmZPdsKw8ni8Xg0FosdBbA2EoncmM1mf2oHZmIymcxllUolYObbmHl7EASHlwBd1zVncZiZJ4Ig2N4urBEnhLgTwPsA9iulBpYAhRAvmvZIRCNa67MqvNhhcpmbRSl1RSugkffkxUJaxL+tlLpvCdAY6odtVnoGQAHA3+1MgIhuZeY4EeWY+UmllMm3WDR14E1aa7PKacdxtvq+r9sBJpPJfsuyDgD4wHGcnb7vV84F7DdAItonpXy8HVhIpZPMvFprnSgUCt+dC7ijVvQFy7JEPp/f1y7QxAkhTPwdRJSQUn7eEiiEmACwNRqNrhsbG/uxATTNPBaL9TiOc7p5m33ft4rFYu/MzMyvU1NT1VBZ7K09RXYxcyIIgg+XAH3f7ygWi6eIqFoqldaGgz3PM2f7HBE9L6X8NLxyz/P6mflRy7L25HK5b5YNFEKYsjgIYK9S6ulwUiHEYwBeqInp3uYXmhDiGQDp8EqMID3Pe0VrfUvd/uWiFQohVjBzgYg2MvOWIAg+CQNd1/2YiDYAWK2UarzYYLa6u7t7nJn76o8r0xoxMDBwaWdnpxHfGtu2E7lc7vtFwFQq9TAzjwF4tVKp7A5fnoODg7dXq9UjRHRASvlQeCLpdPqa+fl5U0Z/aK0fKBQKp81/13WvJyID/KFcLm+ZmJj4sxn4GTPfHIlE1mez2ZONpENDQ1dVq9WC1no9Ee2QUr4XBqZSqW3MbC7rMcdx9jQE5bru3WaCtXsyL6V8ohGzcFsIIU4BWEFEx8MJmdkBcC2A3+sP4ObOcwOAywEYsZjnRmNcB2AlEfVJKadbAQdrbewpc0YXU3sLiYl+AaBKpdKzYbX/A3+89SxXWenxAAAAAElFTkSuQmCC"},bfc4:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAPJJREFUSEvtlr9qAkEQxn9zLyCWKXyAFHa+h0LA3maX6y1SW/kIyxQp8zBpI6SOQq61UjAc4x9QNIbcngGv2W13vvlmvpmPXeHOR+7MR21C733fzCYiUojIOIQwq1P0LYQjM3sBPrMsG4YQ3hLhuQJJ0qt98N6npfnTJc0tjXOuG2ngATABCmAMvMfgVPUQd+rQOfcRAwRawAPwDXwB6xicqj7+JLQY4K0xqnpo7rzD58hkvZ2MT8BSRF7NbBGDU9XpBWEMaB+TfFilVHM+rKrseJ9mWKVUmmHzD3Ce522gU5blBpir6qpqbv/6RNVJ/lvsFhHN2R2Ve2urAAAAAElFTkSuQmCC"},cf0f:function(e,t,i){e.exports=i.p+"static/img/knowledge_icon_mp3.b423f088.png"},cfce:function(e,t,i){e.exports=i.p+"static/img/knowledge_icon_mp4.37216904.png"},dcee:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAA9ZJREFUSEu1lltoHFUYx//f2Q2hipoVjVrq5UEFX0SoCFWxlHiBYkGlbi1a0CRzzkjSeEFEay2WIlgrSgXJnDNDLGlMZa0gClrbFC/toxYafNAXUQQFLw8momN253zuF3bDUHPZJtsDC8PZ75zf/L/rEHJrZGTkylqt9pT3fp1SalX+v+YzM/N8+2fuKaUS7/3bAN4HcJlSKgMwQU1Da+0VAF4HcB+AnwFU57l4zp6IZp/P4MteJ4DLAewD8DIRfcbMcvelYp8HDgLYT0R9WusDraiYzyaKopuJqALgkDHmhZygTwHcnQceBPAIgNXGmF/aDXTOHWXmu5puoTiOf2Dmf40x1y8XJucWUXiMiHpmgcPDw91KKVH1njHmoXMOjOP4Ae/9YaXU9iAI3joXwCiKjiulNswqdM7tYebtRLRFay3BXfaK43it916S5t180jjnjjPzBhodHT0/TdMKM68pFovlvr6+75ZNA5ADSpbubN7lnJPyWE9JklybZZkAf52ZmSkPDQ1NtQNIRONa6xf/B4yiqEfqhohirfVzK4HJ2aZCpdQ7QRDsytXh5wDuIOdcwMwREW3VWovvV7QWAsZx/IX3/napvwPe+20A1qyk4JtvmYvhQWPMS839KIq+VErdRtbaSSIqBUFwFRG11JgXc8FCQGvtCSK6VYD/MPOHYRhuWZEvG4dzwFFjzO5c0pxk5nUC/FEpdTIIgofbAUySpDfLsleJ6M00Tfd2dna+AuASIrqHmS8WoHR0qZejAH4Xt+ZnXvO54e4/Ozo69vX29v6Wf7lKpVKYmpramGXZEwB6ZGoR0fPMfAjAMQAyWyVcpwQos0qgBkBxKZXMfFMYhqdz6X4eEUmm7wDQDeATAB+JAK3199baGwuFwirvPReLxZ/mxtPY2NiFaZp2M3OHXJZlGSmlZn+1Wu2Ceqc/AeC0MWZtDia2rwF4HMBkoVDY3d/fL7AF1xxwMaN6W1rPzFK4u4wxe3KJcD8zj0soAGzUWn+zVKa3BIzjeIf3/hkiKmutJwSYJMnV0qSZ+RoielRrLa5ccrUEtNbuB3CvUqocBMHXDeCmLMvGiehItVp9bGBg4K8laflvmsWMrbWjAG5pACfF1lq7F8CzzPxgGIaHW4GJzdko3MTM5TAMv5KDzrlxZt4K4Ib6V8K3bQU6555mZuka24wxHzQUyqC+s1QqXVQul1tyZ8sKGyNsgog+7urq2jw9PV2q1Wqn6lnrjTGrW1XXMrCuTsbYkUYX+RuAaoRjpzHmjbYD5cIoiq5TSj3JzJsBpBLGarUaDQ4O/nE2wP8AcDv9uk7i52gAAAAASUVORK5CYII="},e015:function(e,t,i){e.exports=i.p+"static/img/chat_hcs_pop.cbd09c8d.png"},e383:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAetJREFUSEu9lj9v00AYxp/ncslCFzYGpK60nbq3fAMYq7Ix2Y7Tr9ApHwAk6ovthYiFqJlhYkD8mZkozJWgpRkACRb34hddSqqocpoEHHs+3+/uved39xIVf6yYB7ZarVVr7b6I7AK4saQF/BaRXqPRaNPzvEcA9gDoJcHG01qSkQOekbRKqY1Op/M9DMM1a+1zAPV6vb4TRdHHRRbi+/66iBwCsFrrB8aYT81m8+ZwODwCUHPAnOS7JEnuuonDMFyx1rofVv8TeKy13jHG/HLz+r7/RkS2HFBIvq0IuD0CAjjNsmyt2+3+8DzvDoBeGSUFsJum6eeJkt4aA4uO6WhcUhFxab49GAy+9fv97LoznTjDjaJxcwGDINgUEUPyfZ7n7TRNf06DlgV8mOf5UwcheQzgCclncRyfXQWXDvwLOAfwiuRBkiQvJ6FzAUmejD0sCk0QBJc7vLojkociEidJ8pqkzPDwIjSztLgOCMCl/ItS6n4cxx8mgEUeXmhRAvCrUure3EDnodZ60xhzWklJp0T80sOCko5CAyBK0/TFwqFZBFiVFtWKX/rVNsvDEt/D+Tz8R+BUDyt/gN0FPKzVauuuxVjye6jo+/5jEWlV1EQdjNrELMv2SS61TSTZ01q3q2+EF0lgGWP/ANJRPStTH8e0AAAAAElFTkSuQmCC"},ee09:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAABKpJREFUSEudlX2IlFUUh5/zvjMqpq0f+5FgZpZZZtvObFpkgUWklSiIiinZSqG4O2tqCWEkKyla+LHqrqu5KiRpGRGmmVSGhYQhuzPuHwoqWpHfI6hofszM/bXvrK6au2Kev96Xe8597jn3d841rtny/r08l35P8DpwTHLTON15OxU70s0+d/Fh2Zjqvh1Mbb8CXgIOgXUDHZNpAmWJ3+5i3+YQY1nhg+b7NTgGGlS7WPx9vyo61KEvgaPy3SAm7znSHFHZr8ALhefI6GPoT4dtBdtNWf3Blg5iXlVkiaDUYKFLaT7TEmeowPNyixYKi5lsrWtjM5hUdza7QU1hvpfxPhI2DMgFMpjtldNcTuds+m9JzaoiSWR1Kq8ffNMJFhd18trYAok3hWYRS8y75YQ1hfmkvfHmeWORHjO02rnz7zLl4OVrvlmAmb53ZYk3btlgaWF38/z9wF7F4k9l15c+3Na3joMznrrg/F2crttPfmFvk78K8UyQNcn6WipwgbvZsugWUFQdcnoxYcelZsjGUT4nD7xo2DZkPzVnGNyZ53+KeAHwTFbrSM8jE85YyK0BesvcOMr27MwCqIqWGar0zEZkyuo3X5dt4fPm/BVAvqRSyhOBymBlcZgrPIplooaNuaq875SMjyAv+rJJW02qdOWJ6U2A5U/0Mhf6AnRZvsZkFbOyONdSbh9wr6QJxBIbMHRLCYV5yyOrJEoEE4jF11lVJI4oUEH8fkaTMYJSnDgQM7N5wmYSq6+ksl+BhcKHAF9p7xGm1v3Vai9UFz1rsg0GO13YK/FSbplgkhz5TImfamq0ikEhyz37K1g/pXmOqfUNVEWmG8wBbVPYn8ikumSLkCDbK/oc02VdaD+O9v+UmPGJvNDjlO4+1AQIbFnkFTNqQb8r3XYy/hXfjHXA05IqKE8saBEgjOriAdm1ZN1uCp7sRsYfQNi2MKkudR0QOC4vettkVQYbXP7liZxq18OkH4EegnGEva+DoFbL1cLCdcBVjXtexzWCkTKLkVe/hhPRkWZaAlySNKNZTXdIuRkQBAWzxg8vlvGaYDbJ4ZV03TzMzNU0jsU8wXRS+iw7UgKrjg4x6a1GAXeT9C1t/JXNYyUr05assriHhdwm4AHRKNNkYjNdo6PMNL+xl7sYbHFh7x1Srq/BeqA9cD6AGFrjZLMpjx9t6oPWbEVRb0tbNdBfMJ9kzkLyzvU0VIMYCFwMOhmxR6KUcCbpZfwPBJOBw4ISkjm7WwcE4CZI0MEPScwk1a6WtukclB5tMBc4LsdwyuP7so24uk9H7+I9HwqVAkfkKLs9IHsnxT28kPs4uHiMb5TJTGdKw98sLupE2qWY0XDhpiJU4PldI0OdEbwnN/TB7VRR3f8+z6UXyRgFbJezCqbU72o1ZCO+nYwczw7DO1QbrB3UjgvnxlvQdEZnyeZAej3lDYdv2iP74rWZLzTOYMWdA7IjBY/coiEGs8CKgAbBIi5mNmdLtRHfOxmtFQqm7M9K6Q7uoKUUKwaFyDtXbtJYIAKcaXwffsHoDkSBH3QxMzqA/r8MboQFU/j4oZ6Ye9WMIcHMatK91jmfJUxO/BH8/wu8XQGIzg2kzgAAAABJRU5ErkJggg=="},f42c:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAp9JREFUSEvtlkFoE1EQhv95b+1FBWOVhloQ8aAHQRA8KHgQPVcQGjx4C9lNhKLQk2I1V8GTJezmFQIWwepJPIpIESyCVURQbyIeVBCxImib9L3Rqdtis1tJYupBHHiH3Z2Zb+e9mTdDWEUKhcKgUmqEmQ8C8FbTa/H9LIDr1tpLlGYwPDy8dX5+/gmAgRYdtqK2AGA8FVgsFnPOuRuxl68AngP43IrXFJ0dAGQpALOpQN/3fQDV2HhSKXWWiDoCOucOMPMVADvF32rALVrr7aLged6bsbGxDx1Gh1KplLHW3gRwdBkoZzY3N3eYiDZ16ljslFILRDTT19f3olwuy5klgZKNRFTpYoLUlVIT2Wy2JNBEhEEQ3GfmQ38SWYptXWu9PwzDZwmg7/uNuM4kG28DmALA7f6AUmqAmU8ys2SkbG8+iqJaGnDJ+SOlVC6KotftwkR/aGhI9/b2XnTOjcb2gTHG/A54V2udC8PwUydAsWkqp/9A/BtbGgTBXma+BmDP4k3j+/5SlnY9QqXUHefc5I9y2wdgHYBvawrUWj+21s7EWc9EdOtvAqeJ6PSaAgHI2e2WXlqtVl8RES8Dieie53m5SqXysdPCLxQKp+JGIC4WC7/ZlwBd3BffK6UuWGune3p62mY2Go0MEZWZ+Uh8l/pRFI2nAd8ByMYfZNh52zbtp8FGAP0AtDxorY+FYSjNYIVIhOcAyFrfIajZzBHRU2l5xhjpQCuB+Xx+s+d5JwAcZ2bp+LsAbEiBvwSQcNCkJx3/ITNPGGNk6ktIYqbxff8ygJFfNGWbK8aY893YgQSwVCoNWmslneVMrFLqTL1ev1qr1b6sCbBYLG5zzsmU1S/jYRRFcjV1TRIRSufOZDKjRPSAmaeMMTKCdE2+A/6cax2EaYUtAAAAAElFTkSuQmCC"},f778:function(e,t,i){"use strict";i("d9e2"),i("14d9"),i("2c66"),i("249d"),i("40e9"),i("e9f5"),i("910d"),i("7d54"),i("ab43"),i("907a"),i("986a"),i("1d02"),i("3c5d"),i("6ce5"),i("2834"),i("4ea1"),i("c73d");var o=i("a026"),n=i("b2de"),s=i("df53"),a=i("313e"),r=i.n(a),A=i("08a9"),l=i("21a6"),c=i.n(l),d=i("4f15"),h=i.n(d),u=i("c1df"),g=i.n(u),p=i("037d"),m=i("4371"),w={components:{VueMarkdown:i("4180").a,Introduce:n.a,Correct:s.a},props:{agent:{type:String,default:null},customTitle:{type:String,default:null}},data(){return{defaultImage:i("3dcb"),aiModelType:"deepseek",aiModelVisible:!1,aiModelList:[{name:"DeepSeek",value:"deepseek",icon:i("accb"),desc:"高效，擅长数学、编程和推理"},{name:"DeepSeek(在线)",value:"deepseek_api",icon:i("accb"),desc:"在线版本，高效，擅长数学、编程和推理"},{name:"通义千问",value:"qwen",icon:i("5e64"),desc:"多模态，支持文本、图像、语音与视频输入"}],correct:{visible:!1,content:"",question:""},tools:{disabled:!0,selection:null,position:{x:0,y:0},visible:!1,keyword:""},summaryArc:{id:null,content:null},icon:{hcsLogo:i("7bae"),hcsLogoPop:i("e015"),logo:i("6a9b"),logo1:i("6a9b"),logo2:i("6a9b"),user:i("da8b"),uploader:i("9550"),send:i("219b"),up:i("2285"),down:i("dcee"),mp3:i("cf0f"),mp3_2:i("aede"),mp4:i("cfce"),mp4_2:i("e383"),pdf:i("2203"),pdf_2:i("3762"),img:i("994a"),img_2:i("45dc"),link:i("d324"),link_2:i("f42c"),play:i("83cf"),pause:i("9222"),add:i("bfc4"),del:i("b617"),save:i("5ca0"),empty:i("38d7"),nodata:i("3dcb"),deepseek:i("accb"),qwen:i("5e64"),chat_new:i("84d2"),chat_new1:i("56c9"),chat_history:i("9d25"),chat_tool:i("4fd6"),chat_at:i("6725"),chat_data:i("0ba4"),chat_knowledge:i("8820"),chat_refresh:i("b940"),hcs:i("6db7"),hcs3:i("35b4"),hcs4:i("ee09"),twin_flag:i("94cb"),twin_question:i("133e")},isRecording:!1,mediaRecorderLoading:!1,mediaRecorder:null,audioContext:null,analyser:null,silenceDuration:0,domIndex:0,audioLoading:!1,audioPlayer:new Audio,audioIsPlaying:!1,audioController:null,audioSource:null,allAudioSources:[],audioCompletedCount:0,options:{xAxis:{type:"category",boundaryGap:!1,data:[],show:!0,axisLabel:{color:"#bbb"},axisLine:{lineStyle:{color:"#bbb"}}},yAxis:{type:"value",show:!0,axisLabel:{color:"#bbb"},axisLine:{lineStyle:{color:"#bbb"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross",animation:!1,label:{backgroundColor:"rgba(0,0,0,0.67)",borderRadius:4}}},title:{left:"left",text:"",subtext:"",textStyle:{fontSize:16,fontWeight:400}},series:[{data:[],type:"line",smooth:!0,showSymbol:!1,areaStyle:{color:{type:"linear",x2:0,y2:1,colorStops:[{offset:0,color:"rgba(61, 179, 249, 0.5)"},{offset:1,color:"rgba(61, 179, 249, 0)"}]}},itemStyle:{normal:{color:"#3DB3F9",lineStyle:{color:"#3DB3F9",width:2}}}}]},recognitionShow:!1,mapParams:null,suggestionController:new AbortController,isEvokeYiNuo:"1"===this.$route.query.evokeYiNuo}},mounted(){"default"===this.currentUser.tenantId&&(this.aiModelList=this.aiModelList.filter((e=>"deepseek_api"===e.value)),this.aiModelType="deepseek_api"),this.isHCS&&(this.aiModelType="deepseek"),window.addEventListener("message",this.receive,!1),document.addEventListener("click",this.documentClick)},activated(){this.initVoiceMonitor(),this.$nextTick((()=>{this.startVoiceMonitor(),!this.isEvokeYiNuo||this.answering||this.isMobile||(this.recognitionShow=!0,this.startRecording(!0))}))},deactivated(){this.onMixinsTextToVoiceStop(null,this.params.answerList)},destroyed(){window.removeEventListener("message",this.receive,!1),this.onMixinsTextToVoiceStop()},computed:{currentUser(){return this.$store.getters.currentUser},aiConfigs(){return this.$store.getters.configs},aiTemplate(){return this.$store.getters.template},agentIntroduce(){let e=this.aiConfigs[this.agent+"_introduce_imgs"]?this.aiConfigs[this.agent+"_introduce_imgs"].split(","):[];return e.length>0?e:null},aiName(){return this.isHCS?"popup"==this.mode?"AI客服":"一诺小助理":this.aiConfigs.name||"一诺AI"},aiLogo(){return this.isHCS?"popup"==this.mode?this.icon.hcsLogoPop:this.icon.hcsLogo:this.aiConfigs.logo||this.icon.logo},aiLogo1(){return this.isHCS?"popup"==this.mode?this.icon.hcsLogoPop:this.icon.hcsLogo:this.aiConfigs.logo||this.icon.logo1},aiModelDesc(){return this.aiConfigs.modelDesc||{}},isDefaultTenant(){return"default"===(this.currentUser||{}).tenantId},isShowVoiceToText:()=>location.href.startsWith("https")||location.href.startsWith("http://localhost")||location.href.startsWith("file://"),hideToolBar(){return this.$route.query.hideToolBar},isMobile(){return this.$utils.isMobile()},isHdkj(){return this.$utils.isHdkj()},isSafari(){return this.$utils.isSafari()},isUniwimPc(){return"uniwimpc"===this.$route.params.mode||"pc"===this.$route.query.uniwim},isWimpic(){return"wimpic"===this.$route.params.mode||"wimpic"===this.$route.query.from},isHCS(){return"hcs"===this.$route.params.mode},isTwin(){return"twin"===this.$route.params.mode},linkOfKnowledgeDetail(){let e=this.aiConfigs.knowledge_detail_link||"";return e.endsWith("/")&&(e=e.substring(0,e.length-1)),e},linkOfWimPicDetail(){let e=this.aiConfigs.wimpic_detail_link||"";return e.endsWith("/")&&(e=e.substring(0,e.length-1)),e},linkOfWimPicJumpDetail(){let e=this.aiConfigs.wimpic_detail_jump_link||this.aiConfigs.wimpic_detail_link||"";return e.endsWith("/")&&(e=e.substring(0,e.length-1)),e},linkOfVideoDetailUrl(){let e=this.aiConfigs.videoDetailUrl||"";return e.endsWith("/")&&(e=e.substring(0,e.length-1)),e},hideWebSearch(){return"0"!==this.aiConfigs.hideWebSearch||this.$route.query.hideWebSearch},hideDeepThink(){return"0"!==this.aiConfigs.hideDeepThink||this.$route.query.hideDeepThink},hideCorrect(){return"1"===this.aiConfigs.hideCorrect||this.$route.query.hideCorrect},hideHistory(){return this.$route.query.hideHistory||this.isWimpic},hideRecomment(){return"1"===this.aiConfigs.hideRecomment||this.$route.query.hideRecomment},hideKnowledgeType(){return"1"===this.aiConfigs.hideKnowledgeType||this.$route.query.hideKnowledgeType},hideKnowledgeTag(){return"1"===this.aiConfigs.hideKnowledgeTag||this.$route.query.hideKnowledgeTag},hideReportIndicators(){return"1"===this.aiConfigs.hideReportIndicators||this.$route.query.hideReportIndicators},hideChatRoleTypeCategory(){return"1"===this.aiConfigs.chat_roleType_hideCategory},hideVoicePlay(){return"1"===this.aiConfigs.hideVoicePlay||this.$route.query.hideVoicePlay},hideLike(){return"1"===this.aiConfigs.hideLike||this.$route.query.hideLike},hideCopy(){return"1"===this.aiConfigs.hideCopy||this.$route.query.hideCopy},hideDoc(){return"1"===this.aiConfigs.hideDoc},hideDownload(){return"1"===this.aiConfigs.hideDownload||this.$route.query.hideDownload},hideExportQuest(){return"1"===this.aiConfigs.hideExportQuest||this.$route.query.hideExportQuest},hideTools(){return"1"===this.aiConfigs.hideTools||this.$route.query.hideTools},historyDrawWidth(){const e=Number(this.$route.query.screenScale);if(!isNaN(e)&&e>16){if(e>40)return 750;if(e>30)return 600}return 376},from(){return this.$route.query.from}},methods:{receive(e){try{var t,i;if(null==e||null===(t=e.data)||void 0===t||!t.action||"mapEvent"===(null==e||null===(i=e.data)||void 0===i?void 0:i.action)){var o;const{method:t,args:i}=null==e||null===(o=e.data)||void 0===o?void 0:o.params,n=JSON.parse(i);"mapParams"===t&&(console.warn("孪生图地图参数",{method:t,args:n}),this.mapParams=n,this.getMapData())}if("string"!=typeof e.data||!e.data.startsWith("*#hd#*"))return;let{action:n,params:s}=JSON.parse(e.data.substring(6));"AI_ASSISTANT_SUMMARY_PAGE_ARC"===n&&(this.summaryArc=s)}catch(e){}},formatFullName:e=>e.replace(/\/$/,""),documentClick(e){if(!(window.ClickTapTime&&Date.now()-window.ClickTapTime<700)&&(window.ClickTapTime=Date.now(),"I"===e.target.tagName&&e.target.className.includes("table-markdown-fullscreen")&&e.target.parentNode.previousSibling.outerHTML&&this.$alert('<div class="table-markdown-alert">'+e.target.parentNode.previousSibling.outerHTML+"</div>","预览",{dangerouslyUseHTMLString:!0,customClass:"table-markdown-alert-dialog",closeOnPressEscape:!0}),"IMG"===e.target.tagName)){let t=e.target.parentElement,i=!1;for(;t;){if(t.classList.contains("content-result")){i=!0;break}t=t.parentElement}i&&e.target.src&&(console.log("点击的图片地址为:",e.target.src),this.handleImageClick([e.target.src]))}},trackEvent(e={}){e.tenantId=this.currentUser.tenantId;let t="web";this.isMobile?t="app":this.isUniwimPc&&(t="desk"),this.$track(e,t)},hideToolsBtn:e=>"抱歉！我找不到相关信息。"!==e&&"已中止查询相关信息。"!==e&&"当前访问人数过多，请稍候再试!"!==e&&"菜单不存在"!==e,isVoiceAutoPlay(e){return!!e&&"1"===this.aiConfigs[e+"_voice_autoplay"]},isHdkjPromise(){try{this.$utils.isHdkj()&&this.$native.check_permission({params:{permission:["audio"]},cb:e=>{var t;null!=e&&null!==(t=e.status)&&void 0!==t&&t.audio?console.warn("已获取音频权限"):(console.warn("未获取音频权限，申请获取"),this.$native.request_permission({params:{permission:["audio"]},cb:e=>{if(console.warn("音频权限检测情况",{cb:e}),this.$utils.isIos())if(1===(null==e?void 0:e.state))confirm("请前往系统设置中打开麦克风权限");else if(0===(null==e?void 0:e.state)){confirm("需要重启应用才能使用使用音频")&&this.$native.kill_app({params:{},cb:e=>{}})}}}))}})}catch(e){}},initVoiceMonitor(){console.warn("init-voice"),window.aiRrecognition&&(this.stopVoiceMonitor(),window.aiRrecognition=null),window.aiRrecognition=new(window.SpeechRecognition||window.webkitSpeechRecognition),window.aiRrecognition.lang="zh-CN",window.aiRrecognition.interimResults=!1,window.aiRrecognition.onresult=e=>{const t=e.results[0][0].transcript,i=Object(m.a)(t,{toneType:"none"}),o=Object(m.a)(this.aiConfigs.evokeYiNuoWords||"一诺一诺",{toneType:"none"});i.includes(o)&&!this.answering&&(this.recognitionShow=!0,this.startRecording(!0)),console.warn({result:"识别结果: "+t,pinyinResult:i})},window.aiRrecognition.onerror=e=>{"not-allowed"===e.error&&(window.aiRrecognitionMessage||(window.aiRrecognitionMessage=this.$message({message:"请在浏览器设置中开启麦克风权限",type:"warning"})))},window.aiRrecognition.onend=()=>{this.isRecording||setTimeout((()=>{window.aiRrecognition.start()}),500)}},startVoiceMonitor(){this.recognitionShow=!1,!this.isEvokeYiNuo&&"1"!==this.aiConfigs.evokeYiNuo||this.isMobile||(this.stopVoiceMonitor(),this.$nextTick((()=>{var e;null===(e=window.aiRrecognition)||void 0===e||e.start()})))},stopVoiceMonitor(){var e;null===(e=window.aiRrecognition)||void 0===e||e.stop()},formatDateByDD(e){if(!e)return"";let t="";return t=g()(e).format("YYYY-MM-DD")==g()().format("YYYY-MM-DD")?g()(e).format("HH:mm"):g()(e).format("YYYY")==g()().format("YYYY")?g()(e).format("MM-DD HH:mm"):g()(e).format("YYYY-MM-DD HH:mm"),t},onBlur(){this.$refs.input.blur()},scrollBind(){this.$nextTick((()=>{let e=this.$refs.scrollbar&&this.$refs.scrollbar.wrap;e&&(e.onscroll=()=>{this.params.scrollEnd=e.scrollTop+e.clientHeight+10>=e.scrollHeight,this.params.isShowScrollBottom=e.scrollTop+e.clientHeight+40<e.scrollHeight})}))},onScrollBottom(){this.$nextTick((()=>{this.$refs.scrollbar&&this.$refs.scrollbar.wrap&&this.$refs.scrollbar.wrap.scrollTo({top:this.$refs.scrollbar.wrap.scrollHeight}),this.params.scrollEnd=!0}))},onResetScrollbar(){this.$nextTick((()=>{this.$refs.scrollbar&&this.$refs.scrollbar.wrap&&this.params.scrollEnd&&this.params.answerList.length&&(this.$refs.scrollbar.wrap.scrollTop=this.$refs.scrollbar.wrap.scrollHeight)}))},handleImageClick(e,t=0){const i=new(o.default.extend({template:'\n                    <el-image-viewer\n                        :url-list="images"\n                        :initial-index="index"\n                        :on-close="closeViewer"\n                    ></el-image-viewer>\n                ',data:()=>({images:e,index:t}),methods:{closeViewer(){this.$destroy(),this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)}},components:{ElImageViewer:A.a}}))({parent:this});i.$mount(),document.body.appendChild(i.$el)},onMixinsClickResourcesItem(e){var t;const{document_id:i,doc_metadata:o,doc_source:n}=e;if(!i)return;let s=(this.linkOfKnowledgeDetail?this.linkOfKnowledgeDetail:location.origin)+"/uniwim/knowledge/app/index.html#/knowledge/knowledgeDetail?id="+i,a=(this.linkOfKnowledgeDetail?this.linkOfKnowledgeDetail:"")+"/kmc/userClient.html#/knowledge/details?id="+i;if(null!=o&&null!==(t=o.dly_source)&&void 0!==t&&t.toLocaleLowerCase().startsWith("wimpic")||null!=n&&n.toLocaleLowerCase().startsWith("wimpic")){const e=this.$utils.GetAuthorization();s=(this.linkOfWimPicJumpDetail||location.origin)+`/wimpich5/index.html#/document?id=${i}&hideNativeTitle=true`,a=(this.linkOfWimPicJumpDetail||"")+`/wimpic/index.html#/document?id=${i}&showAi=true&uniwater_utoken=${e}`}this.isHdkj?this.$native.openWebview({params:{url:s},cb:()=>{}}):(this.$utils.GetAuthorization(),this.isUniwimPc?window.open(a,"_blank",`width=${window.screen.width}, height=${window.screen.height}, toolbar=no`):window.open(a))},onMixinsOpenDoc({docId:e,content:t}){if(e)if(this.isHdkj)this.$native.openWebview({params:{url:(this.linkOfWimPicJumpDetail||location.origin)+"/wimpich5/index.html#/document?id="+e+"&name="+t+"&hideNativeTitle=true"},cb:e=>{console.log(e)}});else{const i=this.$utils.GetAuthorization();this.isUniwimPc?window.open((this.linkOfWimPicJumpDetail||"")+`/wimpic/index.html#/document?id=${e}&name=${t}&showAi=true&uniwater_utoken=${i}`,"_blank",`width=${window.screen.width}, height=${window.screen.height}, toolbar=no`):window.open((this.linkOfWimPicJumpDetail||"")+`/wimpic/index.html#/document?id=${e}&name=${t}&showAi=true&uniwater_utoken=${i}`)}},onMixinsDocumentPaste({docId:e,content:t}){let i=t+"\r\n";i+=`原文地址：${this.linkOfWimPicJumpDetail||location.origin}/wimpic/index.html#/document?id=${e}&name=${t}\r\n`;const o=document.createElement("textarea");o.readOnly="readonly",o.style.position="absolute",o.style.left="-9999px",o.value=i||"",document.body.appendChild(o),o.select(),document.execCommand("copy"),o.blur(),document.body.removeChild(o),this.$message.success("复制成功")},onMixinsOpenMenu(e){let t=e.link||"",i=e.id;if("video"===(null==e?void 0:e.type)&&(i=null,t=(this.linkOfVideoDetailUrl||location.origin)+`/video/home.html#/player?groupId=${e.id}&grid=1&autoPlay=1&expendAll=1&streamType=2`,this.isMobile&&(t=(this.linkOfVideoDetailUrl||location.origin)+`/static/apps/video/index.html#/zlm?groupId=${e.id}&autoplay=1&streamType=2`)),"device"!==(null==e?void 0:e.type)&&"place"!==(null==e?void 0:e.type)||(i=null),this.isMobile)if(this.isHdkj)this.$native.openWebview({params:{url:t},cb:e=>{console.log(e)}});else{const e=this.$utils.GetAuthorization();this.isUniwimPc?window.open(`${t}${t.indexOf("?")>-1?"&":"?"}?token=${e}`,"_blank",`width=${window.screen.width}, height=${window.screen.height}, toolbar=no`):window.open(`${t}${t.indexOf("?")>-1?"&":"?"}&token=${e}`)}else if(top!=self){let o={Id:e.id,Name:e.name,Val:t,link:t,target:e.target,mid:i};window.top.postMessage("*#hd#*"+JSON.stringify({action:"OPEN_TAG",params:o}),"*"),console.warn("菜单点击内容：top != self，使用OPEN_TAG打开， 参数：",o),console.warn("具体参数：","*#hd#*"+JSON.stringify({action:"OPEN_TAG",params:o}))}else{const e=this.$utils.GetAuthorization();this.isUniwimPc?window.open(`${t}${t.indexOf("?")>-1?"&":"?"}uniwater_utoken=${e}`,"_blank",`width=${window.screen.width}, height=${window.screen.height}, toolbar=no`):window.open(`${t}${t.indexOf("?")>-1?"&":"?"}uniwater_utoken=${e}`),console.warn(`菜单点击内容：${t}${t.indexOf("?")>-1?"&":"?"}uniwater_utoken=${e}`)}},onMixinsBack(){this.isHdkj?this.$native.goback({params:{},cb:e=>{console.log(e)}}):window.history.back()},shortenText(e,t,i){return this.$utils.shortenText(e,t,i)},formatAnswerAvatar(e){switch(e.modelType){case"deepseek":return{icon:this.icon.deepseek,title:"由 DeepSeek 提供",name:"DeepSeek"};case"deepseek_api":return{icon:this.icon.deepseek,title:"由 DeepSeek 提供",name:"DeepSeek(在线)"};case"yinuo":return{icon:this.aiLogo,title:`由 ${this.aiName} 提供`,name:this.aiName};default:return{icon:this.icon.qwen,title:"由 通义千问 提供",name:"通义千问"}}},removeMarkdownFormatting:e=>e=(e=(e=(e=(e=(e=e.replace(/^(#+)\s/gm,"")).replace(/^([-*+]\s|\d+\.\s)/gm,"")).replace(/(\*\*|__|\*|_)(.*?)\1/g,"$2")).replace(/!\[.*?\]\((.*?)\)/g,"$1")).replace(/```[\s\S]*?```/g,"")).replace(/`([^`]+)`/g,"$1"),chunkData(e){if(!e)return[];let t=[];return t=e.split("\n").filter((e=>!!e)).map((e=>(e.startsWith("data:")&&(e=e.slice(5,e.length)),e))),t},removeEchartsCodeBlock:e=>e.replace(/```\s*echarts([\s\S]*?)```/g,""),removeThinkingProcess:e=>(e?e.replace(/<think[\s\S]*?<\/think>/g,""):"").replace(/<\/think>/g,""),extractThinkingProcess(e){const t=e?e.match(/<think([\s\S]*?)<\/think>/):"";return t?t[0].trim():null},async startRecording(e=!1){let t=[];this.audioContext=null,this.analyser=null,this.silenceDuration=0,this.isStopped=!1;try{const e=await navigator.permissions.query({name:"microphone"});console.warn({auths:e})}catch(e){}try{const i=await navigator.mediaDevices.getUserMedia({audio:!0});console.warn({stream:i}),this.mediaRecorder=new MediaRecorder(i),this.mediaRecorder.ondataavailable=e=>{e.data.size>0&&t.push(e.data)},this.mediaRecorder.onstop=()=>{this.startVoiceMonitor();const i=new Blob(t,{type:"audio/wav"}),o=new File([i],"voice",{type:i.type});t=[],this.mediaRecorderLoading=!0,this.$saasApi.AIVoiceToText(o).then((t=>{var i;const o=null==t||null===(i=t.result)||void 0===i?void 0:i[0];if(!o||!o.text)throw Error("");this.params.keyword||(this.params.keyword=""),this.params.keyword+=o.text,!0===e&&this.getAnswer()})).catch((e=>{})).finally((()=>{this.mediaRecorderLoading=!1,this.isRecording=!1}))},this.stopVoiceMonitor(),this.mediaRecorder.start(),this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2048;this.audioContext.createMediaStreamSource(i).connect(this.analyser),this.checkVolume(),this.isRecording=!0}catch(e){this.$message.error("语音录制失败"),localStorage.debug&&alert(e),console.error("录音出错:",{error:e}),this.startVoiceMonitor()}},stopRecording(){if(this.isStopped=!0,this.mediaRecorder){this.isRecording=!1,this.mediaRecorder.stop();this.mediaRecorder.stream.getTracks().forEach((e=>e.stop()))}this.audioContext&&this.audioContext.close()},checkVolume(){if(this.isStopped)return;const e=this.analyser.frequencyBinCount,t=new Uint8Array(e);this.analyser.getByteTimeDomainData(t);let i=0;for(let o=0;o<e;o++)i+=Math.abs(t[o]-128);const o=i/e;console.warn(o),o<6.5?(this.silenceDuration+=100,this.silenceDuration>=(this.isEvokeYiNuo?3e3:2500)&&this.stopRecording()):this.silenceDuration=0,setTimeout(this.checkVolume,100)},formatContent(e,t=null,i){return t&&this.markdownContent(t,i),e},tableContent(e,t){this.$nextTick((()=>{var t;const i=null===(t=this.$refs[e])||void 0===t||null===(t=t[0])||void 0===t?void 0:t.$el.querySelectorAll("table");i&&i.forEach(((e,t)=>{if("table-wrapper"!==e.parentNode.className)try{const t=document.createElement("div");t.className="table-wrapper",e.parentNode.insertBefore(t,e),t.appendChild(e)}catch(e){}}))}))},formatEchartOptions(e){return e.chartType?e.data&&e.data.length?{...this.options,title:{...this.options.title,text:e.placeName||"",subtext:e.name||""},tooltip:{...this.options.tooltip,formatter:t=>t[0].marker+(t[0].name||"")+"："+(t[0].value||"")+" "+(e.unit||"")},xAxis:{...this.options.xAxis,data:e.data.map((e=>e.t?e.t.replace(" ","\n"):e.t))},series:[{...this.options.series[0],data:e.data.map((e=>e.v))}]}:null:e},markdownContent(e,t){this.$nextTick((()=>{var i;const o=null===(i=this.$refs)||void 0===i||null===(i=i[e])||void 0===i||null===(i=i[0])||void 0===i?void 0:i.$el.querySelectorAll("pre code.language-echarts");o&&o.forEach(((e,i)=>{try{let o=e.textContent;o=o.replace(/[\r\n\t]/g,"").trim();const n=JSON.parse(o),s=document.createElement("div");s.className="echarts-wrapper",s.dataset.option=o,s.style.width="100%",s.style.height="260px";const a=document.createElement("div");s.appendChild(a),a.style.width="100%",a.style.height="260px",e.parentNode.replaceChild(s,e);const A=r.a.init(a);A.setOption(n),this.$refs["aiEchart"+t+"_c_"+i]=A,window.addEventListener("resize",(()=>{setTimeout((()=>{A.resize()}),150)}))}catch(e){console.error("解析 ECharts 配置时出错:",e)}}))}))},splitAndAppendDelimiter(e){let t=150,i=e.replace(/\n/g,"。");i=i.replace(/。+/g,"。"),i=i.replace(/```json[\s\S]*?```/g,"");let o=[];const n=/([，。])/;let s=i;for(;s.length>0;){const e=s.match(n);if(e){const t=e.index;o.push(s.slice(0,t)),o.push(e[0]),s=s.slice(t+e[0].length)}else o.push(s),s=""}o=o.filter((e=>e.length>0));let a=[];for(let e=0;e<o.length;e++)if(o[e].match(/[，。]/))a.length>0&&(a[a.length-1]+=o[e]);else if(o[e].length<t){let i=a.length-1,n=e+1,s=!1;i>=0&&a[i].length<t?(a[i]+=" "+o[e],s=!0):n<o.length&&o[n].length<t&&!o[n].match(/[，。]/)?(o[n]=o[e]+" "+o[n],s=!0):i>=0&&n<o.length&&a[i].length>=t&&o[n].length>=t&&!o[n].match(/[，。]/)&&(a[i].length<=o[n].length?a[i]+=" "+o[e]:o[n]=o[e]+" "+o[n],s=!0),s||a.push(o[e])}else a.push(o[e]);return a},getTextWithoutTableContent(e){const t=e.cloneNode(!0);t.querySelectorAll("table").forEach((e=>e.remove()));t.querySelectorAll(".echarts-wrapper").forEach((e=>e.remove()));return t.querySelectorAll('[class^="language-"]').forEach((e=>e.remove())),t.textContent},async onMixinsTextToVoicePlay(e,t,i){if(e.isVoiceSpeaking){if(this.audioLoading||this.audioIsPlaying)return}else this.onMixinsTextToVoiceStop();this.$set(e,"isVoiceSpeaking",!0),this.audioLoading=!0,this.$saasApi.AISessionRecordUpdate({id:e.recordId,clickedVoice:1});const o=this.getTextWithoutTableContent(this.$refs["markdownRef_"+t][0].$el),n=this.splitAndAppendDelimiter(o).filter((e=>"\n"!==e&&""!==e.trim())).join("");if(!o||!o.replace(/\n/g,""))return this.audioLoading=!1,void this.$set(e,"isVoiceSpeaking",!1);setTimeout((async()=>{this.audioController=new AbortController;const t=this.audioController.signal;this.allAudioSources=[],this.audioContext=new(window.AudioContext||window.webkitAudioContext);try{const i=await this.$saasApi.AITextToSoundStream(n,t);if(this.audioLoading=!1,!i.ok)throw new Error("Network response was not ok");const o=i.body.getReader(),s=this.audioContext.createGain();s.gain.value=.9,s.connect(this.audioContext.destination);const a=22050,r=1;let A=this.audioContext.currentTime,l=new Uint8Array;for(;;){if(null!=t&&t.aborted){console.log("请求已中止，停止读取响应流");break}const{done:i,value:n}=await o.read();if(i)break;let c=n;if(l.length>0){const e=new Uint8Array(l.length+c.length);e.set(l,0),e.set(c,l.length),c=e,l=new Uint8Array}c.length%2!=0&&(l=c.slice(c.length-1),c=c.slice(0,c.length-1));const d=new Int16Array(c.buffer,c.byteOffset,c.length/2),h=new Float32Array(d.length);for(let e=0;e<d.length;e++)h[e]=Math.max(-1,Math.min(1,d[e]/32768));const u=this.audioContext.createBuffer(r,h.length,a);u.copyToChannel(h,0),this.audioSource=this.audioContext.createBufferSource(),this.audioSource.buffer=u,this.audioSource.connect(s),this.audioSource.start(A),this.allAudioSources.push(this.audioSource),A+=u.duration,this.audioIsPlaying=!0,this.$set(e,"isVoiceSpeaking",!0)}this.audioCompletedCount=0;const c=e=>{this.audioCompletedCount++,(this.audioCompletedCount>=this.allAudioSources.length||e===this.allAudioSources.length-1)&&(console.warn("所有音频块都播放完成"),this.onMixinsTextToVoiceStop())};this.allAudioSources.forEach(((e,t)=>{e.onended=()=>c(t)}))}catch(t){console.error("Error::",t),this.audioIsPlaying=!1,this.$set(e,"isVoiceSpeaking",!1)}}),100)},onMixinsTextToVoiceStop(e=null,t=this.params.answerList){e&&this.$set(e,"isVoiceSpeaking",!1),t&&(t||[]).forEach((e=>{this.$set(e,"isVoiceSpeaking",!1)})),this.audioIsPlaying=!1,this.audioLoading=!1,this.audioController&&(this.audioController.abort(),this.audioController=null),this.audioContext&&this.allAudioSources.length>0&&(this.allAudioSources.forEach((e=>{e.stop(0)})),this.allAudioSources=[]),this.audioContext&&"closed"!==this.audioContext.state&&(this.audioContext.close().catch(console.error),this.audioContext=null)},blobToBase64:e=>new Promise(((t,i)=>{const o=new FileReader;o.onloadend=()=>t(o.result),o.onerror=i,o.readAsDataURL(e)})),onMixinsInsertDoc(e){var t,i;console.warn(null==e?void 0:e.content),console.warn(this.removeThinkingProcess(null==e||null===(t=e.content)||void 0===t?void 0:t.answer));let o="*#hd#*"+JSON.stringify({action:"AI_ASSISTANT_INSERT_DOC",params:{type:"insert",content:this.removeThinkingProcess(null==e||null===(i=e.content)||void 0===i?void 0:i.answer),origin:e.content.origin}});window.parent.postMessage(o,"*")},onMixinsOpenFile(e){e.startsWith("blob:")||(this.isHdkj?this.$native.openWebview({params:{url:e},cb:e=>{console.log(e)}}):(this.$utils.GetAuthorization(),this.isUniwimPc?window.open(e,"_blank",`width=${window.screen.width}, height=${window.screen.height}, toolbar=no`):window.open(e)))},onMixinsPaste(e){var t;if(null==e||null===(t=e.content)||void 0===t||!t.answer)return;this.$saasApi.AISessionRecordUpdate({id:e.recordId,copiedContent:1});let i=this.removeEchartsCodeBlock(e.content.answer);i=this.removeMarkdownFormatting(i),i+="\r\n";const o=document.createElement("textarea");o.readOnly="readonly",o.style.position="absolute",o.style.left="-9999px",o.value=(i||"").trim(),document.body.appendChild(o),o.select(),document.execCommand("copy"),o.blur(),document.body.removeChild(o),this.$message.success("复制成功")},getEchartsOption(e){let t=e;for(;t&&t!==document.documentElement;){if(t.classList.contains("echarts-wrapper"))return t.dataset.option;t=t.parentNode}return null},onMixinsCorrect(e,t){console.warn({item:e});const i=this.params.answerList[t-1];this.correct.question=i.content;let o=this.removeThinkingProcess(e.content.answer);o=this.removeEchartsCodeBlock(o),this.correct.content=o,this.correct.visible=!0},onMixinsDownload(e,t){const i=this.params.answerList[t-1],o=this.$refs["markdownRef_"+t][0].$el.cloneNode(!0);o.querySelectorAll("img").forEach((e=>{e.style.maxWidth="300px",e.style.maxHeight="300px",e.style.height="auto",e.style.display="block",e.style.margin="0 auto",e.setAttribute("width","300"),e.setAttribute("height","auto")}));const n=o.querySelectorAll("canvas[data-zr-dom-id]");n.length?(n.forEach((e=>{const i=this.$refs["markdownRef_"+t][0].$el.querySelector(`canvas[data-zr-dom-id="${e.dataset.zrDomId}"]`);let o=this.getEchartsOption(i);if(o)try{e.width=692;r.a.init(e).setOption(JSON.parse(o))}catch(e){}})),setTimeout((()=>{this.onDownloadNext(o,i,e)}),1e3)):this.onDownloadNext(o,i,e)},onDownloadNext(e,t,i){e.querySelectorAll("canvas").forEach((e=>{const t=new Image;t.src=e.toDataURL("image/png");const i=document.createElement("div");i.appendChild(t),console.warn({src:t.src}),e.parentNode.replaceChild(i,e)}));const o=`\n                <!DOCTYPE html>\n                <html>\n                  <head>\n                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\n                    <style>\n                        hr {\n                            display: none;\n                        }\n                        table{\n                            border-spacing: 0;\n                            border-collapse: collapse;\n                            width: 100%;\n                            //text-align: center;\n                            margin-bottom: 20px;\n                            display: table;\n                        }\n                        table tr{\n                            border: 0;\n                            border-top: 1px solid #EFEFEF;\n                            background-color: #fff;\n                        }\n                        table tr:nth-child(2n) {\n                            background-color: #FCFCFC;\n                        }\n                        table tr td, table tr th {\n                            font-size: 14px;\n                            color: var(--sds-color-text-primary-1);\n                            line-height: 22px;\n                            border: 1px solid #EFEFEF;\n                            padding: 8px;\n                            word-break: normal !important;\n                            vertical-align: middle;\n                        }\n                        table tr th{\n                            font-weight: 700;\n                            background: #F6F6F6;\n                        }\n                    </style>\n                  </head>\n                  <body>\n                    ${e.innerHTML}\n                  </body>\n                </html>\n            `;this.$saasApi.AISessionRecordUpdate({id:i.recordId,clickedDownload:1});const n=h.a.asBlob(o);c.a.saveAs(n,(t.content||"文档")+".docx")},async onMixinsShareToWimPic(e,t,i=""){const o=this.params.answerList[t-1];if(!o)return;this.params.suggestedMessage=[],this.answering=!0,"reset"===i?this.params.answerList.pop():"noOperate"===i||this.params.answerList.push({role:"user",content:"生成在线文档"});const n=Object(p.g)();this.params.answerList.push({id:n,role:"document",loading:!0,question:"生成在线文档",content:"",docId:null,index:t,model:e});const s=Object.keys(this.$refs).filter((e=>e.startsWith(`aiEchart${t}_`))).map((e=>this.$refs[e]));let a=[];if(s&&s.length){console.warn("开始获取图表信息");for(let e=0;e<s.length;e++)try{var A;let t=(null===(A=s[e])||void 0===A||null===(A=A[0])||void 0===A||null===(A=A.$refs)||void 0===A||null===(A=A.hdchart)||void 0===A?void 0:A.chart)||s[e];const i=t._dom.parentElement.cloneNode(!0);i.style.width="800px";const o=r.a.init(i);o.setOption(t.getOption());const n=new Promise((e=>{o.on("finished",(()=>{e()}))}));await n;const l=o.getDataURL({type:"png",pixelRatio:1,backgroundColor:"#fff"});a.push(l),o.dispose(),document.body.removeChild(i)}catch(e){}console.warn("完成图表信息获取")}let l=this.removeThinkingProcess(e.content.answer);l=this.removeEchartsCodeBlock(l),this.$saasApi.AIShareToWimPic({name:o.content,content:l,images:a},this.linkOfWimPicDetail).then((t=>{if(!t||!t.path)throw Error();{let i=this.params.answerList.findIndex((e=>e.id===n));this.$set(this.params.answerList[i],"content",t.name),this.$set(this.params.answerList[i],"docId",t.id),this.$set(this.params.answerList[i],"flag",!0),this.onMixinsOpenDoc(this.params.answerList[i]),this.$saasApi.AISessionRecordUpdate({id:e.recordId,clickedGenerateWimpic:1})}})).catch((()=>{let e=this.params.answerList.findIndex((e=>e.id===n));this.$set(this.params.answerList[e],"content","生成失败"),this.$set(this.params.answerList[e],"flag",!1)})).finally((()=>{let e=this.params.answerList.findIndex((e=>e.id===n));this.$set(this.params.answerList[e],"loading",!1),this.answering=!1,setTimeout((()=>{this.$refs.scrollbar.wrap.scrollTop=this.$refs.scrollbar.wrap.scrollHeight}),10)}))},getMapData(){this.$saasApi.TwinsMapData(this.mapParams).then((e=>{this.$store.commit("twinmap/SET_MAP_DATA",e)}))},handleTwinMap(e){const t=e.target.closest(".operate-btn"),i=e.target.closest(".operate-input");if(t||i){if(t){const e=t.dataset.params;try{const t=JSON.parse(e);console.log("获取孪生图参数:",t),window.parent.postMessage(t,"*")}catch(e){console.error("解析参数失败:",e)}}else i&&(this.params.keyword=e.target.innerText);e.stopPropagation()}},filterCodeBlocks(e,t){let i="",o=e;for(;o.length>0;)if(t){const e=o.indexOf("```");-1===e?o="":(o=o.substring(e+3),t=!1)}else{const e=o.indexOf("```");-1===e?(i+=o,o=""):(i+=o.substring(0,e),o=o.substring(e+3),t=!0)}return{processed:i,isInCodeBlock:t}},filterJsonCodeBlocks(e,t){let i="",o=e;for(;o.length>0;)if(t){const e=o.indexOf("```");-1===e?o="":(o=o.substring(e+3),t=!1)}else{const e=o.indexOf("```json");-1===e?(i+=o,o=""):(i+=o.substring(0,e),o=o.substring(e+3),t=!0)}return{processed:i,isInCodeBlock:t}},async TextToVoice(e){if(this.audioPlayer=new Audio,this.audioPlayer.src=await this.$saasApi.AITextToSound(e),this.audioPlayer.type="audio/wav",this.$utils.isIosAndMac())try{this.audioPlayer.muted=!0,this.audioPlayer.play(),this.audioPlayer.pause(),this.audioPlayer.muted=!1}catch(e){console.error("iOS播放准备失败:",e)}await this.audioPlayer.pause(),await this.audioPlayer.play()},getMixinsSuggestedMessage(e,t){this.$saasApi.AISuggestedMessage({recordId:e},t).then((e=>{e&&e.length&&(this.params.suggestedMessage=e.slice(0,3),this.onResetScrollbar())}))}}},f=w,b=i("2877"),E=Object(b.a)(f,undefined,undefined,!1,null,null,null);t.a=E.exports}}]);