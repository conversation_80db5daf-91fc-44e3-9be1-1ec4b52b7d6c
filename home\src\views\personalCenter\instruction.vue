<template>
  <div class="service-package" v-loading="loading">
    <div class="header">
      <div class="title-section">
        我的指令
        <div class="header-tools">
          <a href="javascript:void(0)" @click="jumpTo('/comment?from=个人中心')">没有想要的指令?请联系我们</a>
        </div>
      </div>
      <div class="condition-section">
        <el-form :inline="true" :model="params">
          <el-form-item label="分类">
              <el-select v-model="params.commandType" @change="(value)=>{changeSelectValue(value,'commandType')}" placeholder="请选择分类" style="width: 160px" clearable>
                  <el-option label="全部分类" value="all" />
                  <el-option v-for="it in instructionTypes" :key="it.Value" :label="it.Name" :value="it.Value" />
              </el-select>
          </el-form-item>
          <el-form-item label="状态">
              <el-select v-model="params.authStatus" @change="(value)=>{changeSelectValue(value,'authStatus')}" placeholder="请选择状态" style="width: 160px" clearable>
                  <el-option label="全部状态" value="all" />
                  <el-option v-for="it in authStatusList" :key="it.Value" :label="it.Name" :value="it.Value" />
              </el-select>
          </el-form-item>
          <el-form-item label="">
              <el-input v-model="params.name" placeholder="搜索指令" clearable style="width: 160px" @keyup.enter="tableQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="" @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-section">
      <div class="table-content" v-show="tabType == 'table'">
        <el-table ref="tableRef" :data="tableData" border :show-overflow-tooltip="true"
          :highlight-current-row="true" style="width: 100%;height: calc(100% - 48px)" @rowClick="handleRowClick">
          <el-table-column type="index" label="序号" :index="1 + pagination.pageSize * (pagination.currentPage - 1)"
            align="center" width="60" />
          <el-table-column v-for="it in tableColumns" :key="it.data" :header-align="it.headerAlign||'center'" :prop="it.data" :label="it.title" :align="it.align||'center'"
            :width="it.width" :minWidth="100" :fixed="it.fixed">
            <template v-if="it.scoped == 'status'" #default="{ row }">
                <el-tag type="danger" round v-if="row.authStatus === -1">
                    已过期
                </el-tag>
                <el-tag type="info" round v-if="row.authStatus === 0">
                    未生效
                </el-tag>
                <el-tag type="success" round v-else-if="row.authStatus === 1">
                    生效中
                </el-tag>
            </template>
            <template v-else-if="it.scoped == 'endTime'" #default="{ row }">
                {{ row.endTime?moment(row.endTime*1000).format('YYYY-MM-DD HH:mm:ss'):''}}
            </template>
            <template v-else-if="it.scoped == 'iconPath'" #default="{ row }">
                <i class="icon action-iconfont" :class="row.iconPath"></i>
            </template>
            <template v-else-if="it.scoped == 'commandType'" #default="{ row }">
                {{ instructionTypes.find(it => it.Value === row.commandType)?.Name || '' }}
            </template>
            
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" style="height: 50vh;" />
          </template>
        </el-table>
        <div class="table-content-pagination">
          <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
            :page-sizes="[30, 60, 120, 300]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
            @change="tableQuery" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { componentCategories } from "@/utils/componentCategories"
import { ref, reactive, computed, onMounted } from 'vue'
import utils from '@/utils/utils'
import moment from "moment";
import saasApi from '@/api/index';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from "@/stores/user";
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

//
const pages = ref([
  { "title": "服务包管理", "code": "service-package" }
])
const Authorization = ref('')
Authorization.value = utils.GetAuthorization()
const headers = ref({
  "Authorization":Authorization.value,
  "FROM_CHANNEL":"web"
})
const instructionTypes = computed(()=>{
    let types:any = []
    componentCategories.forEach(category=>{
        types.push({
            Name:category.label,
            Value:category.name
        })
    })
    console.log('componentCategories',types)
    return types
})
const type = ref('service-package')
const currentRow = ref(null)
const loading = ref(false)
const pagination = ref({
  currentPage: 1,
  pageSize: 30,
  total: 0
})
const authStatusList = ref([
  { Name: '已过期', Value: -1 },
  { Name: '未生效', Value: 0 },
  { Name: '生效中', Value: 1 },
])
const tabType = ref('table')
const params = ref({
  authStatus: 'all',
  commandType: "all",
  name: ""
})
const classify = ref([])
const tableData = ref([])
const tableColumns = ref([
    { data: 'iconPath', title: '图标', scoped: 'iconPath', width: 120, orderable: true, filterable: true },
    { data: 'name', title: '名称', minWidth: 200, orderable: true, filterable: true },
    { data: 'commandType', title: '分类', scoped: 'commandType', minWidth: 160, orderable: true, filterable: true },
    { data: 'description', title: '简介', orderable: true, filterable: true },
    { data: 'updated', title: '授权时间', minWidth: 240, orderable: true, filterable: true },
    { data: 'endTime', title: '到期时间',scoped:"endTime", minWidth: 240, orderable: true, filterable: true },
    { data: 'authStatus', title: '状态', scoped: 'status', minWidth: 120, orderable: true, filterable: true },
])



let page_map = ref({
})
pages.value.forEach(page => {
  page_map.value[page.code] = page.title
})



//ref
const tableRef = ref<HTMLElement>()


//computed
// isUniwimPc
const isUniwimPc = computed(() => {
  return route.query.uniwim === 'pc'
})

const currentUser = computed(() => {
  return userStore.userInfo
})



//方法
const jumpTo = (url:string)=>{
  let path = utils.generateUrlWithQuery(
    url
  )
  router.push(path)
}
const changeSelectValue = (val:any,key:string) => {
    if (!val&&val!==0) {
        params.value[key] = 'all'
    }
    // params.value.name = ''
    pagination.value = {
        currentPage: 1,
        pageSize: 30,
        total: 0
    }
    tableQuery()
}

const formatCategoryIds = (data: any, text: any) => {
  debugger;
    (data || []).forEach((item, index) => {
        const name = classify.value.find(it => it.Value === item)?.Name || ''
        if (name) {
            if (index === 0) {
                text += `${name}`
            } else {
                text += `,${name}`
            }
        }
    })
    return text
}


const handleRowClick = (row:any, show:boolean) => {
  currentRow.value = row
}

const tableQuery = (noloading: boolean = false) => {
  if (!noloading) {
    loading.value = true

  }
  ;
  currentRow.value = null
  const query_params: any = {
    conditions: [],
    data: {
    },
    isAuthFilter:1,
    order:[],
    index: pagination.value.currentPage,
    size: pagination.value.pageSize,
  }
  if (params.value.authStatus !== 'all') {
    query_params.authStatus = ''+params.value.authStatus
  }
  if (params.value.commandType !== 'all') {
    query_params.data.commandType = params.value.commandType
  }
  if (params.value.name) {
    query_params.data.name = params.value.name
  }
  saasApi.AIAgentWimtaskCommandQuery(query_params).then((res:any) => {
    if (typeof res?.rows == 'object') {
      pagination.value = {
        currentPage: res.current,
        pageSize: res.size,
        total: res.total
      }
      tableData.value = res.rows
    }
  }).finally(() => {
    if (!noloading) loading.value = false
  })
}


const onSubmit = () => {
  tableQuery()
}


const onReset = () => {
  params.value = {
    authStatus: 'all',
    name: "",
    commandType: "all",
  }
  pagination.value = {
    currentPage: 1,
    pageSize: 30,
    total: 0
  }
  currentRow.value = null
  tableData.value = []
  tableQuery()
}




const openDetail = (type: string) => {
  const urlDict = {
    yuanxing: 'http://192.168.100.205:25427/Products/金聪_WimTask/',
    xiangshe: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2MyYjhiYWRjOWVjMDRmMGViNWQxNDljODQ1ZTRmYzg4OzA7MQ==&showAi=true&hideNativeTitle=true',
    guihua: 'https://www.dlmeasure.com/wimpic/document.html?baseStr=NWQ4OTkxNzcxMjQ0MWQ3YTUwNzMwNThjO2JjMWE0NWNmMGI5YTQ2ZjU4YjY5ZTJlNTY4YjJmNjgyOzE7Mg==&showAi=true&hideNativeTitle=true'
  }
  const urlToken = utils.GetAuthorization();
  let urlOrigin:string = `${urlDict[type]}`
  if (type !== 'yuanxing') urlOrigin += `&uniwater_utoken=${urlToken}`
  if (isUniwimPc) {
    window.open(urlOrigin, '_blank', `width=${window.screen.width}, height=${window.screen.height}, toolbar=no`)
  } else {
    window.open(urlOrigin)
  }
}

onMounted(async() => {
    tableQuery()
})
</script>
<style scoped lang="scss">
:deep(.el-upload--text){
  width:64px;
}
:deep(.el-upload-dragger){
  padding:0;
  border:none;
}
:deep(.el-dialog__title){
  font-size:14px;
  font-weight: bold;
}
.service-package {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 12px;
  padding-top:72px;
  box-sizing: border-box;
  background: #f7f7f9 !important;
  display: flex;
  flex-direction: column;

  .header {
    background: #fff;
    width: 100%;
    height: 112px;
    box-sizing: border-box;
    overflow: hidden;

    .title-section {
      height: 64px;
      width: 100%;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: SourceHanSansSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      .el-dropdown-link {
        height: 24px;
        font-family: SourceHanSansSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        display: flex;
        align-items: center;
      }
    }

    .header-tools {
      display: flex;
      align-items: center;
      a{
        color:#0054D2;
        text-decoration: none;
      }
      .header-tools-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #222222;
        font-weight: 400;
        cursor: pointer;

        .action-iconfont {
          margin-right: 4px;
          font-size: 14px;
        }

        span {
          margin-left: 6px;
          line-height: 17px;
        }

        &:hover {
          color: rgba(0, 84, 210, 0.8);
        }

        &:active {
          color: #0044A9;
        }

        &.is-disabled {
          color: #BCBFC3;
          cursor: not-allowed;
        }
      }
    }

    .condition-section {
      padding: 8px 16px;
      box-sizing: border-box;
      border-top: solid 1px #e8ecf0;
      // display: flex;
      justify-content: space-between;

      .el-form-item {
        margin-bottom: 0;
      }

      .tab-list {
        .tab-list-item {
          width: 80px;
          height: 32px;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          border: 1px solid #E6E7E9;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.left {
            border-radius: 4px 0 0 4px;
          }

          &.right {
            border-radius: 0 4px 4px 0;
          }

          &.active {
            color: #FFFFFF;
            background: #0054D9;
            border-color: #0054D9;
          }
        }
      }
    }
  }

  .table-section {
    // flex: 1;
    height:calc(100% - 112px);
    background: #fff;
  }

  .table-content {
    height: 100%;
    :deep(.el-table){
      .el-table__cell, td.el-table__cell {
        border-bottom:1px solid #EEEEEE!important
      }
    } 
    .table-icon {
        width: 24px;
        height: 24px;
        display: block;
        margin: 0 auto;
    }
    .el-link.task-link{
      font-size: 12px;
      &~.task-link{
        margin-left: 12px;
      }
    }
    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }

    ::v-deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .card-content {
    height: 100%;
    padding: 16px;
    box-sizing: border-box;

    .card-item-box {
      height: calc(100% - 48px);
      overflow-y: auto;
      margin-bottom: 16px;

      .card-item {
        width: calc(25% - 16px);
        min-width: 445px;
        height: 300px;
        display: inline-block;
        margin-right: 16px;
        padding: 16px;
        box-sizing: border-box;
        border: 1px solid #E6E7E9;
        border-radius: 4px;
        margin-bottom: 16px;
        cursor: pointer;

        &:hover {
          border-color: #0054D9;
        }

        &.active {
          border-color: #0054D9;
        }

        .card-item-title {
          height: 20px;
          line-height: 20px;
          font-weight: 500;
          font-size: 14px;
          color: rgba(34, 34, 34, 0.9);
          margin-bottom: 10px;
        }

        .card-item-tag-time {
          .el-tag {
            border-radius: 12px;
          }

          .card-item-tag-time-text {
            font-weight: 400;
            font-size: 12px;
            color: #BCBFC3;
            margin-left: 10px;
          }
        }

        .card-item-info {
          display: flex;
          margin-top: 12px;

          .card-item-info-item {
            font-weight: 400;
            font-size: 12px;
            color: #5C5F66;
            letter-spacing: 0;
            margin-right: 20px;
          }
        }

        .card-item-line {
          width: 100%;
          height: 0.5px;
          background: #EEEEEE;
          border-radius: 4px;
          margin: 16px 0;
        }

        .card-item-two-text {
          display: flex;
          flex-wrap: wrap;

          .card-item-two-text-item {
            width: 50%;
            display: inline-block;
            margin-bottom: 16px;

            .card-item-text1 {
              font-weight: 400;
              font-size: 12px;
              color: #BCBFC3;
              margin-bottom: 10px;
            }

            .card-item-text2 {
              height: 18px;
              line-height: 18px;
              font-weight: 400;
              font-size: 12px;
              color: #222222;
            }
          }
        }

        .card-item-bottons {
          .card-item-botton {
            display: inline-flex;
            align-content: center;
            justify-content: center;
            padding: 8px 16px;
            box-sizing: border-box;
            background: #FFFFFF;
            border: 1px solid #E6E7E9;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 16px;
            font-size: 12px;

            &.disabled {
              color: #a8abb2;
              border-color: 1px solid #e4e7ed;
              cursor: not-allowed;
            }

            &:not(.disabled):hover {
              color: #0054D2;
              border: 1px solid #0054D2;
            }
          }
        }
      }

      // .card-item:nth-child(4n) {
      //   margin-right: 0;
      // }
    }

    .table-content-pagination {
      height: 48px;
      padding: 0 12px;
      display: flex;
      justify-content: right;
      align-items: center;
    }
  }
}
</style>
