import json
import uuid
from time import sleep

import requests
import jwt
from datetime import datetime
from cachetools import TTL<PERSON>ache
from requests import Response
from robot.api.deco import keyword
from config.env_config import DLY_URL,get_config_item,AI_RUL,BPM_URL,YN_URL,config,WIMTASK_API


# 创建缓存：最大200个条目，每个条目最多存活600秒
cache = TTLCache(maxsize=200, ttl=600)


def get_output(work_id,node_id):
    return cache.get(work_id + "_" + node_id + "_output")


def get_input(work_id,node_id):
    return cache.get(work_id + "_" + node_id + "_input")

def record_variable(work_id,node_id,value):
    vars_key = work_id + "_" + node_id + "_vars"
    if vars_key not in cache.keys() :
        vars_list = []
    else:
        vars_list = cache[vars_key]
    vars_list.append(value)
    cache[vars_key] = vars_list

class ExecutionMonitor:
    ROBOT_LISTENER_API_VERSION = 2
    # test env
    # WIM_TASK = "http://saasbate.dlmeasure.com:1801"

    # prod env
    WIM_TASK = WIMTASK_API
    def __init__(self,token,task_id,history_id):
        self.step_history = []
        self.token = token
        self.logs: object = None
        self.history_id = history_id
        self.task_id = task_id
        self.user = jwt.decode(token, options={"verify_signature": False})
        self.headers =  {
                "Content-Type": "application/json",
                "Authorization": self.token  # "Bearer empty" # 如果需要授权，tokens 从params 获取
            }

    def start_keyword(self, name, attributes):
        """在关键字执行前触发"""
        keyword_name = name.name if hasattr(name, 'name') else str(name)
        if keyword_name.startswith("Execute Step"):

            step_name = keyword_name.split(" - ", 1)[1] if " - " in keyword_name else keyword_name
            time = self.convert_time_str(date_str=str(attributes["starttime"]))
            status = "running"
            if step_name == "结束":
                status = "passed"
            self.step_history.append({
                "title": step_name,
                "name": step_name,
                "start_time": time,
                "time": time,
                "content": attributes["doc"],
                "status": status
            })

            param = {
                "missionId": "1",
                "missionExecId": "2",
                "content": attributes["doc"],
                "title": step_name,
                "userIds": [self.user["id"]],
                "extend": self.step_history
            }
            sleep(1)
            resp = requests.post("https://www.dlmeasure.com/wimai/api/tool/msgSend",json=param,headers=self.headers)
            print(f"\n>>> STEP STARTED: {step_name} <<<")

    def end_keyword(self, name, attributes):
        """在关键字执行后触发"""
        if name.startswith("Execute Step"):
            step_name = name.split(" - ", 1)[1] if " - " in name else name
            duration = attributes['elapsedtime'] / 1000  # 转换为秒

            for step in self.step_history:
                if step['name'] == step_name and step['status'] == "running":
                    time = self.convert_time_str(date_str = str(attributes["endtime"]))
                    output = cache.get(self.task_id + "_" + name + "_output")
                    step.update({
                        "title": step_name,
                        "end_time": time,
                        "time": time,
                        "duration": duration,
                        "status": "passed" if attributes['status'] == 'PASS' else "failed",
                        "output": output,
                        "content": attributes["doc"],
                        "error": attributes.get('error', '')
                    })
                # 记录日志
            self.save_exc_log(attributes['status'],
                              step_name,
                              self.convert_time_long(attributes["starttime"]),
                              self.convert_time_long(attributes["endtime"]),
                              name.split(" - ", 1)[0] if " - " in name else name)
            status_icon = "✅" if attributes['status'] == 'PASS' else "❌"
            if step_name == "结束":
                self.save_history(attributes)

            self
            print(f"\n<<< STEP FINISHED: {step_name} {status_icon} "
                  f"({duration:.2f}s) >>>")
    def get_step_history(self):
        """获取步骤执行历史"""
        return self.step_history


    def convert_time_long(self, date_str:str) -> str:
        try:
            # 解析输入字符串
            dt = datetime.strptime(date_str, "%Y%m%d %H:%M:%S.%f")
            # 格式化为目标格式
            return dt.timestamp()
        except ValueError as e:
            raise ValueError("Invalid date format or value") from e

    def convert_time_str(self, date_str:str) -> str:
        try:
            # 解析输入字符串

            dt = datetime.strptime(date_str, "%Y%m%d %H:%M:%S.%f")
            # 格式化为目标格式
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError as e:
            raise ValueError("Invalid date format or value") from e

    def save_exc_log(self,status,name,sdt,edt,node_id):
        exc_boyd = {
            "missionId": self.task_id,
            "historyId": self.history_id,
            "title": name,
            "state": "passed" if status == 'PASS' else "failed",
            "output": json.dumps(get_output(self.task_id,node_id),ensure_ascii=False),
            "input": json.dumps(get_input(self.task_id,node_id),ensure_ascii=False),
            "excTime":int(sdt),
            "finishTime":int(edt)
        }

        resp = requests.post(f"{WIMTASK_API}/wimai/api/task/excLogs/save", json=exc_boyd,
                      headers=self.headers)
        print(json.dumps(exc_boyd,ensure_ascii=False))
        if resp.status_code != 200:
            print(f">>> EXC LOG FAILED: {name},response body{resp.text} <<<")

    def save_history(self,attributes):
        history_boyd = {
            "id": self.history_id,
            "missionId": self.task_id,
            "result": attributes['status'],
            "executeUser": self.user["name"],
            "executeTime": datetime.strptime(self.step_history[0]["time"], "%Y-%m-%d %H:%M:%S").timestamp(),
            "executeState": 1 if attributes['status'] == "PASS" else -1
        }

        resp= requests.post(f"{WIMTASK_API}/wimai/api/taskHistory/history/saveLog", json=history_boyd,
                      headers=self.headers)
        if resp.status_code != 200:
            print(f">>> save_history: ,response body{resp.text} <<<")


class CustomiseLibrary:
    def __init__(self):
        pass

    @keyword
    def record_input(self,work_id,node_id,val):
        new_dict = {}
        for k, v in val.items():
            if type(v) == Response:
                v = v.text
            new_dict[k] = v
        cache[work_id + "_" + node_id + "_input"] = new_dict

    @keyword
    def record_output(self,work_id : str,node_id : str,val:dict):
        new_dict = {}
        for k,v in val.items():
            if type(v) == Response :
                v = v.text
            new_dict[k] = v
        cache[work_id + "_" + node_id + "_output"] = new_dict