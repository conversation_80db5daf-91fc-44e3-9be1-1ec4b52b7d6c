/**
 * 文件操作组件的配置Schema
 */

import type { ComponentConfigSchema } from '@/types/config'

export const excelCreateSchema: ComponentConfigSchema = {
  componentType: 'excel_create',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Excel文档创建的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
  ],

  fields: {
    file_name: {
      type: 'string',
      label: '文件名',
      description: 'Excel文档文件名，支持变量替换',
      placeholder: '填写文件名称',
      required: true,
      group: 'basic',
      order: 1,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
      ],
      append: {
        key: 'extend_format',
        placeholder: '扩展名',
        clearable: true,
        header: '设置文件扩展名',
        options: [
          { label: '年月日', value: 'YYYYMMDD' },
          { label: '年月日时分秒', value: 'YYYYMMDDHHmmss' },
          { label: '时间戳', value: 'timestamp' },
        ],
      }
    },

    use_template: {
      type: 'boolean',
      label: '使用模板',
      description: '启用后可以选择模板来创建Excel文件',
      group: 'basic',
      order: 2,
      default: false,
    },

    template_id: {
      type: 'select',
      template_type: 'excel',
      label: '选择模板',
      description: '选择要使用的模板',
      group: 'basic',
      order: 3,
      required: false,
      options: [],
      async: true,
      placeholder: '请选择或创建模板',
      optionsLoader: async () => {
        try {
          // 使用动态导入来避免循环依赖
          const { request } = await import('@/utils/axios')
          const options = await request.get('/report-templates/options/list?templateType=excel')
          if (options && typeof options === 'object' && Array.isArray(options)) {
            return [ ...options]
          }
          return []
        } catch (error) {
          console.error('获取模板选项失败:', error)
        }
        return []
      },
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }],
      validation: [
        {
          type: 'custom',
          validator: () => true, // 禁用选项验证，因为选项是异步加载的
          message: '',
        },
      ],
    },

    response_variable: {
      type: 'variable_picker',
      label: '数据源变量',
      description: '选择包含HTTP响应数据的工作流变量',
      placeholder: '选择工作流变量...',
      group: 'basic',
      order: 4,
      required: false,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }],
    },

    excel_response: {
      type: 'textarea',
      label: '工作表内容',
      description: '',
      placeholder: `[
  {"id":"001","名称":"zmt"},
  {"id":"002","名称":"lch"}
]`,
      required: false,
      group: 'basic',
      order: 5,
      rows: 5,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }],
    },

    // worksheet: {
    //   type: 'string',
    //   label: '工作表名',
    //   description: 'Excel文档工作表名',
    //   placeholder: 'Sheet1',
    //   required: false,
    //   group: 'basic',
    //   order: 6,
    //   conditions: [{
    //     field: 'use_template',
    //     operator: 'equals',
    //     value: false,
    //   }],
    // },

    // fmt: {
    //   type: 'string',
    //   label: '工作表后缀',
    //   description: 'Excel文档后缀名',
    //   placeholder: 'xlsx',
    //   required: false,
    //   group: 'basic',
    //   order: 7,
    // },

    file_path: {
      type: 'folder',
      label: '文件保存路径',
      description: '选择Excel文档的保存路径',
      placeholder: '选择保存路径...',
      required: true,
      group: 'basic',
      order: 8,
      validation: [
        {
          type: 'required',
          message: '保存路径不能为空',
        },
      ],
    },
    is_open_folder: {
      type: 'boolean',
      label: '运行结束后自动打开文件目录',
      description: '',
      group: 'basic',
      order: 9,
      default: true,
      switchTrue: '打开',
      switchFalse: '不打开'
    },
  },
}

export const excelOpenSchema: ComponentConfigSchema = {
  componentType: 'excel_open',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Excel文件打开的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: 'Excel文件处理的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {
    path: {
      type: 'file',
      label: '文件路径',
      description: 'Excel文件的完整路径',
      placeholder: 'C:\\data\\workbook.xlsx',
      required: true,
      group: 'basic',
      order: 1,
      accept: '.xlsx,.xls,.xlsm',
      validation: [
        {
          type: 'required',
          message: '文件路径不能为空',
        },
        {
          type: 'pattern',
          value: '\\.(xlsx|xls|xlsm)$',
          message: '文件必须是Excel格式（.xlsx, .xls, .xlsm）',
        },
      ],
    },

    read_only: {
      type: 'boolean',
      label: '只读模式',
      description: '是否以只读模式打开文件',
      help: '只读模式可以防止意外修改文件，但无法保存更改',
      group: 'basic',
      order: 2,
      default: true,
    },

    password: {
      type: 'password',
      label: '文件密码',
      description: '如果文件有密码保护，请输入密码',
      placeholder: '输入文件密码',
      group: 'advanced',
      order: 1,
    },

    data_only: {
      type: 'boolean',
      label: '仅读取数据',
      description: '是否只读取单元格数据，忽略格式和公式',
      help: '启用此选项可以提高读取速度',
      group: 'advanced',
      order: 2,
      default: false,
    },

    keep_vba: {
      type: 'boolean',
      label: '保留VBA代码',
      description: '是否保留文件中的VBA宏代码',
      group: 'advanced',
      order: 3,
      default: false,
    },
  },

  presets: {
    read_only: {
      label: '只读访问',
      description: '安全的只读访问配置',
      config: {
        read_only: true,
        data_only: true,
        keep_vba: false,
      },
    },

    edit_mode: {
      label: '编辑模式',
      description: '允许修改文件的配置',
      config: {
        read_only: false,
        data_only: false,
        keep_vba: true,
      },
    },
  },
}

export const excelWriteSchema: ComponentConfigSchema = {
  componentType: 'excel_write',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Excel数据写入的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'report',
      label: '模板配置',
      description: '使用模板设计器创建的模板',
      icon: 'Grid',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '数据处理的高级配置',
      icon: 'Setting',
      order: 3,
      collapsible: true,
      collapsed: true,
      advanced: true,
    },
  ],

  fields: {

    excel_response: {
      type: 'textarea',
      label: '工作表内容',
      description: '',
      placeholder: `[
  {"id":"001","名称":"zmt"},
  {"id":"002","名称":"lch"}
]`,
      required: false,
      group: 'basic',
      order: 0,
      rows: 5,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }],
    },
    worksheet: {
      type: 'string',
      label: '工作表名',
      description: 'Excel文档工作表名',
      placeholder: 'Sheet1',
      required: false,
      group: 'basic',
      order: 1,
    },
     file_name: {
      type: 'string',
      label: '文件名',
      description: 'Excel文档文件名，支持变量替换',
      placeholder: '填写文件名称',
      required: true,
      group: 'basic',
      order: 2,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
      ],
    },

    file_path: {
      type: 'folder',
      label: '文件保存路径',
      description: '选择Excel文档的保存路径',
      placeholder: '选择保存路径...',
      required: true,
      group: 'basic',
      order: 3,
      validation: [
        {
          type: 'required',
          message: '保存路径不能为空',
        },
      ],
    },

    fmt: {
      type: 'string',
      label: '工作表后缀',
      description: 'Excel文档后缀名',
      placeholder: 'xlsx',
      required: false,
      group: 'basic',
      order: 4,
    },

    use_template: {
      type: 'boolean',
      label: '使用模板',
      description: '是否使用模板设计器创建的模板',
      group: 'report',
      order: 1,
      default: false,
    },

    response_variable: {
      type: 'variable_picker',
      label: '数据源变量',
      description: '选择包含HTTP响应数据的工作流变量',
      placeholder: '选择工作流变量...',
      group: 'report',
      order: 3,
      required: false,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }]
    },
    template_id: {
      template_type:'excel',
      type: 'select',
      label: '选择模板',
      description: '选择要使用的模板',
      group: 'report',
      order: 2,
      required: false,
      options: [{ label: '请先创建模板', value: '' }],
      async: true,
      optionsLoader: async () => {
        try {
          // 使用动态导入来避免循环依赖
          const { request } = await import('@/utils/axios')
          const options = await request.get('/report-templates/options/list?templateType=excel')
          return [{ label: '请选择模板', value: '' }, ...options]
        } catch (error) {
          console.error('获取模板选项失败:', error)
        }
        return [{ label: '请先创建模板', value: '' }]
      },
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }],
      validation: [
        {
          type: 'custom',
          validator: () => true, // 禁用选项验证，因为选项是异步加载的
          message: '',
        },
      ],
    },

    /*data: {
      type: 'textarea',
      label: '数据内容',
      description: '要写入的数据，支持变量替换',
      placeholder: '输入要写入的数据或变量名',
      required: false,
      group: 'basic',
      order: 2,
      rows: 4,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }]
    },*/

    start_cell: {
      type: 'string',
      label: '起始单元格',
      description: '数据写入的起始位置',
      placeholder: 'A1',
      required: false,
      group: 'basic',
      order: 3,
      default: 'A1',
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }]
    },

    variable_mapping: {
      type: 'json',
      label: '变量映射',
      description: '模板变量与工作流变量的映射关系',
      group: 'report',
      order: 3,
      required: false,
      placeholder: '{"template_var": "workflow_var"}',
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }]
    },

    overwrite: {
      type: 'boolean',
      label: '覆盖现有数据',
      description: '是否覆盖目标区域的现有数据',
      group: 'advanced',
      order: 1,
      default: true,
    },

    auto_fit: {
      type: 'boolean',
      label: '自动调整列宽',
      description: '写入数据后自动调整列宽',
      group: 'advanced',
      order: 2,
      default: false,
    },
  },

  presets: {
    simple: {
      label: '简单写入',
      description: '直接写入数据到指定位置',
      config: {
        use_template: false,
        start_cell: 'A1',
        overwrite: true,
      },
    },
    template: {
      label: '模板写入',
      description: '使用报表模板写入数据',
      config: {
        use_template: true,
        overwrite: true,
        auto_fit: true,
      },
    },
  },
}

export const excelReadSchema: ComponentConfigSchema = {
  componentType: 'excel_read',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Excel数据读取的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'response',
      label: '指令输出',
      icon: 'Document',
      order: 2,
      collapsible: false,
    },
  ],

  fields: {
    mode: {
      type: 'radio',
      label: '读取模式',
      description: '',
      placeholder: ' ',
      group: 'basic',
      order: 1,
      options: [
        { label: '文件读取', value: 'file' },
        { label: '文件夹读取', value: 'folder' },
      ]
    },
    file_info: {
      type: 'read_files',
      label: '文件路径',
      description: '',
      placeholder: '',
      required: true,
      group: 'basic',
      order: 2,
      conditions: [{
        field: 'mode',
        operator: 'equals',
        value: 'file',
      }]
    },
    folder_info: {
      type: 'read_folders',
      label: '文件夹路径',
      description: '要读取的Excel文件夹路径',
      placeholder: '选择Excel文件夹...',
      required: true,
      group: 'basic',
      order: 2,
      conditions: [{
        field: 'mode',
        operator: 'equals',
        value: 'folder',
      }]
    },
    max_files: {
      type: 'number',
      label: '最大文件数',
      description: '',
      // required: true,
      group: 'basic',
      order: 3,
      default: 20,
      min: 1,
      max: 100,
      conditions: [{
        field: 'mode',
        operator: 'equals',
        value: 'folder',
      }]
    },



    variable_name: {
      type: 'string',
      label: 'Excel数据变量名',
      description: '',
      placeholder: 'excel_data',
      required: true,
      group: 'response',
      order: 1,
      outputVariable: true,
      validation: [
        {
          type: 'required',
          message: '变量名称不能为空',
        },
        {
          type: 'pattern',
          value: '^[a-zA-Z_][a-zA-Z0-9_]*$',
          message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
        },
      ],
    },
  },

  presets: {

  },
}

export const pdfCreateSchema: ComponentConfigSchema = {
  componentType: 'pdf_create',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'PDF文档创建的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'layout',
      label: '页面布局',
      description: 'PDF页面布局设置',
      icon: 'Grid',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    html_content: {
      type: 'textarea',
      label: 'HTML内容',
      description: '要转换为PDF的HTML内容，支持变量替换',
      placeholder: '<h1>${title}</h1><p>${content}</p>',
      required: true,
      group: 'basic',
      order: 1,
      rows: 6,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'HTML内容不能为空',
        },
      ],
    },

    output_folder: {
      type: 'folder',
      label: '输出文件夹',
      description: '选择PDF文件的保存文件夹',
      placeholder: '选择保存文件夹...',
      required: true,
      group: 'basic',
      order: 2,
      validation: [
        {
          type: 'required',
          message: '输出文件夹不能为空',
        },
      ],
    },

    output_filename: {
      type: 'string',
      label: '文件名',
      description: 'PDF文件名，支持变量替换',
      placeholder: 'report_${date}.pdf',
      required: true,
      group: 'basic',
      order: 3,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
        {
          type: 'pattern',
          value: '\\.pdf$',
          message: '文件名必须以.pdf结尾',
        },
      ],
    },

    page_size: {
      type: 'select',
      label: '页面大小',
      description: 'PDF页面的大小',
      group: 'layout',
      order: 1,
      default: 'A4',
      options: [
        { label: 'A4', value: 'A4' },
        { label: 'A3', value: 'A3' },
        { label: 'A5', value: 'A5' },
        { label: 'Letter', value: 'Letter' },
        { label: 'Legal', value: 'Legal' },
      ],
    },

    orientation: {
      type: 'select',
      label: '页面方向',
      description: 'PDF页面的方向',
      group: 'layout',
      order: 2,
      default: 'Portrait',
      options: [
        { label: '纵向', value: 'Portrait' },
        { label: '横向', value: 'Landscape' },
      ],
    },
  },

  presets: {
    standard: {
      label: '标准文档',
      description: 'A4纵向标准配置',
      config: {
        html_content: '<h1>${title}</h1><p>${content}</p>',
        output_filename: 'document_${date}.pdf',
        page_size: 'A4',
        orientation: 'Portrait',
      },
    },

    report: {
      label: '报告格式',
      description: 'A4横向报告配置',
      config: {
        html_content:
          '<h1>${report_title}</h1><h2>报告日期：${date}</h2><div>${report_content}</div>',
        output_filename: 'report_${date}.pdf',
        page_size: 'A4',
        orientation: 'Landscape',
      },
    },
  },
}

export const wordCreateSchema: ComponentConfigSchema = {
  componentType: 'word_create',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '指令输入',
      // description: 'Word文档创建的基本参数',
      icon: 'Connection',
      order: 1,
      collapsible: false,
    },
    {
      id: 'other',
      label: '其他设置',
      // description: 'Word文档格式配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: false,
    },
  ],

  fields: {
    output_filename: {
      type: 'string',
      label: '文件名',
      // description: 'Word文档文件名，支持变量替换',
      placeholder: '请输入文件名',
      required: true,
      group: 'basic',
      order: 1,
      suffix: 'docx',
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
        // {
        //   type: 'pattern',
        //   value: '\\.(docx|doc)$',
        //   message: '文件名必须以.docx或.doc结尾',
        // },
      ],
      append: {
        key: 'extend_format',
        placeholder: '扩展名',
        clearable: true,
        header: '设置文件扩展名',
        options: [
          { label: '年月日', value: 'YYYYMMDD' },
          { label: '年月日时分秒', value: 'YYYYMMDDHHmmss' },
          { label: '时间戳', value: 'timestamp' },
        ],
      }
    },
    use_template: {
      type: 'boolean',
      label: '使用模板',
      description: '启用后可以选择模板来创建Excel文件',
      group: 'basic',
      order: 2,
      default: false,
    },
    template_id: {
      type: 'select',
      label: '选择模板',
      description: '选择要使用的模板',
      group: 'basic',
      order: 3,
      required: false,
      options: [],
      template_type: 'word',
      async: true,
      placeholder: '请选择或创建模板',
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }],
      optionsLoader: async () => {
        try {
          // 使用动态导入来避免循环依赖
          const { request } = await import('@/utils/axios')
          const options = await request.get('/report-templates/options/list?templateType=word')
          debugger;
          if (options && typeof options === 'object' && Array.isArray(options)) {
            return [ ...options]
          }
          return []
        } catch (error) {
          console.error('获取模板选项失败:', error)
        }
        return []
      },
    },
    response_variable: {
      type: 'variable_picker',
      label: '数据源变量',
      description: '选择包含HTTP响应数据的工作流变量',
      placeholder: '选择工作流变量...',
      group: 'basic',
      order: 4,
      required: false,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: true,
      }],
    },
    title: {
      type: 'string',
      label: '文档标题',
      // description: '文档的标题（可选），支持变量替换',
      placeholder: '请输入文档标题',
      group: 'basic',
      order: 5,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }],
    },
    content: {
      type: 'textarea',
      label: '文档内容',
      description: '按输入框填写的内容生成文档',
      placeholder: '日期：${innerCurrentTime}\n内容：',
      // required: true,
      group: 'basic',
      order: 6,
      rows: 8,
      variableSupport: true,
      conditions: [{
        field: 'use_template',
        operator: 'equals',
        value: false,
      }],
      validation: [
        {
          type: 'required',
          message: '文档内容不能为空',
        },
      ],
    },
    // font_size: {
    //   type: 'number',
    //   label: '字体大小',
    //   // description: '文档内容的字体大小',
    //   group: 'basic',
    //   order: 7,
    //   default: 12,
    //   min: 8,
    //   max: 72,
    //   step: 1,
    // },
    output_folder: {
      type: 'folder',
      label: '保存文件夹',
      description: '选择Word文档的保存文件夹',
      placeholder: '选择保存文件夹...',
      required: true,
      group: 'basic',
      order: 8,
      validation: [
        {
          type: 'required',
          message: '输出文件夹不能为空',
        },
      ],
    },
    is_open_folder: {
      type: 'boolean',
      label: '运行结束后自动打开文件目录',
      description: '',
      group: 'basic',
      order: 9,
      default: true,
      switchTrue: '打开',
      switchFalse: '不打开'
    },
    timeout: {
      type: 'number',
      label: '超时时间（秒）',
      // description: '请求的最大等待时间',
      required: true,
      group: 'other',
      order: 2,
      default: 60,
      min: 1,
      max: 600,
      unit: '',
    },
    error: {
      type: 'errorretry',
      label: '失败时重试',
      description: '',
      placeholder: ' ',
      group: 'other',
      order: 2,
      children: [
        {
          id: 'retry_times',
          label: '重试次数',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '次',
        },
        {
          id: 'retry_delay',
          label: '重试间隔',
          description: '',
          placeholder: ' ',
          required: true,
          group: 'other',
          order: 2,
          default: 1,
          min: 1,
          max: 10,
          suffixUnit: '秒',
        },
      ],
    },
    error_handle: {
      type: 'radio',
      label: '错误处理方式',
      description: '',
      placeholder: ' ',
      required: true,
      group: 'other',
      order: 4,
      options: [
        { label: '终止流程', value: 'stop' },
        { label: '忽略并继续执行', value: 'ignore' },
      ],
    },
  },

  presets: {
    simple: {
      label: '简单文档',
      description: '基本的文档配置',
      config: {
        output_filename: 'document_${date}.docx',
        font_size: 12,
      },
    },

    report: {
      label: '报告文档',
      description: '带标题的报告配置',
      config: {
        title: '${project_name} - 工作报告',
        output_filename: 'report_${project_name}_${date}.docx',
        font_size: 11,
      },
    },

    large_text: {
      label: '大字体文档',
      description: '适合阅读的大字体配置',
      config: {
        output_filename: 'document_large_${date}.docx',
        font_size: 14,
      },
    },
  },
}

export const markdownSaveSchema: ComponentConfigSchema = {
  componentType: 'markdown_save',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: 'Markdown文件保存的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: 'Markdown文件的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
  ],

  fields: {
    content: {
      type: 'textarea',
      label: 'Markdown内容',
      description: '要保存的Markdown内容，支持变量替换',
      placeholder:
        '# ${title}\n\n## 概述\n${overview}\n\n## 详细内容\n${details}\n\n- 项目：${project_name}\n- 状态：${status}',
      required: true,
      group: 'basic',
      order: 1,
      rows: 10,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: 'Markdown内容不能为空',
        },
      ],
    },

    output_folder: {
      type: 'folder',
      label: '输出文件夹',
      description: '选择Markdown文件的保存文件夹',
      placeholder: '选择保存文件夹...',
      required: true,
      group: 'basic',
      order: 2,
      validation: [
        {
          type: 'required',
          message: '输出文件夹不能为空',
        },
      ],
    },

    output_filename: {
      type: 'string',
      label: '文件名',
      description: 'Markdown文件名，支持变量替换',
      placeholder: '${title}_${version}.md',
      required: true,
      group: 'basic',
      order: 3,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
        {
          type: 'pattern',
          value: '\\.(md|markdown)$',
          message: '文件名必须以.md或.markdown结尾',
        },
      ],
    },

    encoding: {
      type: 'select',
      label: '文件编码',
      description: 'Markdown文件的字符编码',
      group: 'advanced',
      order: 1,
      default: 'utf-8',
      options: [
        { label: 'UTF-8', value: 'utf-8' },
        { label: 'UTF-8 BOM', value: 'utf-8-sig' },
        { label: 'GBK', value: 'gbk' },
        { label: 'ASCII', value: 'ascii' },
      ],
    },
  },

  presets: {
    standard: {
      label: '标准Markdown',
      description: 'UTF-8编码的标准配置',
      config: {
        output_filename: '${title}_${date}.md',
        encoding: 'utf-8',
      },
    },

    chinese: {
      label: '中文文档',
      description: '适合中文的编码配置',
      config: {
        output_filename: '${title}_${version}.md',
        encoding: 'utf-8-sig',
      },
    },
  },
}

export const textFileSaveSchema: ComponentConfigSchema = {
  componentType: 'text_file_save',
  version: '1.0.0',

  groups: [
    {
      id: 'basic',
      label: '基本配置',
      description: '文本文件保存的基本参数',
      icon: 'Document',
      order: 1,
      collapsible: false,
    },
    {
      id: 'advanced',
      label: '高级选项',
      description: '文本文件的高级配置',
      icon: 'Setting',
      order: 2,
      collapsible: true,
      collapsed: true,
    },
  ],

  fields: {
    content: {
      type: 'textarea',
      label: '文本内容',
      description: '要保存的文本内容，支持变量替换',
      placeholder:
        '日志时间：${timestamp}\n操作用户：${username}\n执行结果：${result}\n详细信息：${details}',
      required: true,
      group: 'basic',
      order: 1,
      rows: 8,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文本内容不能为空',
        },
      ],
    },

    output_folder: {
      type: 'folder',
      label: '输出文件夹',
      description: '选择文本文件的保存文件夹',
      placeholder: '选择保存文件夹...',
      required: true,
      group: 'basic',
      order: 2,
      validation: [
        {
          type: 'required',
          message: '输出文件夹不能为空',
        },
      ],
    },

    output_filename: {
      type: 'string',
      label: '文件名',
      description: '文本文件名，支持变量替换',
      placeholder: 'app_${date}_${time}.log',
      required: true,
      group: 'basic',
      order: 3,
      variableSupport: true,
      validation: [
        {
          type: 'required',
          message: '文件名不能为空',
        },
      ],
    },

    encoding: {
      type: 'select',
      label: '文件编码',
      description: '文本文件的字符编码',
      group: 'advanced',
      order: 1,
      default: 'utf-8',
      options: [
        { label: 'UTF-8', value: 'utf-8' },
        { label: 'UTF-8 BOM', value: 'utf-8-sig' },
        { label: 'GBK', value: 'gbk' },
        { label: 'ASCII', value: 'ascii' },
        { label: 'Latin-1', value: 'latin-1' },
      ],
    },

    append: {
      type: 'boolean',
      label: '追加模式',
      description: '是否追加到文件末尾而不是覆盖',
      help: '启用后内容将添加到文件末尾，而不是替换整个文件',
      group: 'advanced',
      order: 2,
      default: false,
    },
  },

  presets: {
    new_file: {
      label: '新建文件',
      description: '创建新文件的配置',
      config: {
        output_filename: 'file_${date}.txt',
        encoding: 'utf-8',
        append: false,
      },
    },

    append_log: {
      label: '追加日志',
      description: '追加到日志文件的配置',
      config: {
        output_filename: 'app_${date}.log',
        encoding: 'utf-8',
        append: true,
      },
    },

    chinese_file: {
      label: '中文文件',
      description: '适合中文的编码配置',
      config: {
        output_filename: '${name}_${date}.txt',
        encoding: 'utf-8-sig',
        append: false,
      },
    },
  },
}
