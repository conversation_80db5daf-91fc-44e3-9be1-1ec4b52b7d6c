from typing import Dict, List, Set

from core import logger

from models.workflow import (
    WorkflowNode,
    WorkflowEdge,
    WorkflowNodeConditions,
    WorkflowCondition,
)


class NodeTopology:
    prev_nodes_map: Dict[str, List[str]]
    next_nodes_map: Dict[str, List[str]]
    node_conditions_map: Dict[str, WorkflowNodeConditions]
    start_id: str
    end_id: str

    def __init__(
        self, edge_map: Dict[str, WorkflowEdge], node_list: List[WorkflowNode]
    ):
        self.prev_nodes_map = NodeTopology.generate_prev_node(edge_map)
        self.next_nodes_map = NodeTopology.generate_next_node(edge_map)
        self.node_conditions_map = NodeTopology.generate_conditions(node_list, edge_map)
        start_nodes = [
            node.id for node in node_list if node.data.componentType == "workflow_start"
        ]
        end_nodes = [
            node.id for node in node_list if node.data.componentType == "workflow_end"
        ]
        self.start_id, self.end_id = start_nodes[0], end_nodes[0]

    def generate_topology(
        self,
    ) -> (List[str], Dict[str, List[str]], Dict[str, WorkflowNodeConditions]):

        waiting_queue: List[str] = []
        sub_queues: Dict[str, List[str]] = {}
        passed_nodes: set[str] = set()
        main_queue: List[str] = []

        self._check_normal_node(self.start_id, passed_nodes, main_queue, waiting_queue)

        for node in waiting_queue:
            main_queue.append(node)

        while len(waiting_queue) > 0:
            node_id = waiting_queue.pop(0)
            self._check_condition_node(node_id, passed_nodes, [], sub_queues)

        main_queue.append(self.end_id)

        return main_queue, sub_queues, self.node_conditions_map

    def _check_normal_node(
        self,
        node_id: str,
        passed_nodes: Set[str],
        queue: List[str],
        waiting_queue: List[str],
    ):
        if self._normal_can_done(node_id, passed_nodes):
            if node_id == self.end_id:
                return
            # print(f"发现可以添加的node {node_id}")

            node_conditions = self.node_conditions_map.get(node_id)
            # 是条件节点
            if node_conditions is not None:
                waiting_queue.append(node_id)
            else:
                passed_nodes.add(node_id)
                queue.append(node_id)
                next_nodes = self.next_nodes_map.get(node_id, [])
                for next_node in next_nodes:
                    self._check_normal_node(
                        next_node, passed_nodes, queue, waiting_queue
                    )

    def _check_condition_node(
        self,
        node_id: str,
        passed_nodes: Set[str],
        queue: List[str],
        sub_queues: Dict[str, List[str]],
    ):

        sub_queue_first = ""

        if len(queue) > 0:
            sub_queue_first = queue[0]

        if self._condition_can_done(node_id, sub_queue_first, passed_nodes):
            if node_id == self.end_id:
                return
            passed_nodes.add(node_id)
            queue.append(node_id)
            node_conditions = self.node_conditions_map.get(node_id)
            # 是条件节点
            if node_conditions is not None:
                for node_condition in node_conditions.conditions:
                    queue: List[str] = []
                    self._check_condition_node(
                        node_condition.next_node_id, passed_nodes, queue, sub_queues
                    )
                    sub_queues[node_condition.next_node_id] = queue
                queue: List[str] = []
                self._check_condition_node(
                    node_conditions.else_next_node_id, passed_nodes, queue, sub_queues
                )
                sub_queues[node_conditions.else_next_node_id] = queue
            else:
                for next_node in self.next_nodes_map.get(node_id, []):
                    self._check_condition_node(
                        next_node, passed_nodes, queue, sub_queues
                    )

    def _normal_can_done(self, node_id: str, passed_nodes: set[str]) -> bool:
        prev_nodes = self.prev_nodes_map.get(node_id, [])
        for prev_node in prev_nodes:
            if prev_node not in passed_nodes:
                return False
        return True

    def _condition_can_done(
        self, node_id: str, sub_queue_first: str, passed_nodes: set[str]
    ) -> bool:
        prev_nodes = self.prev_nodes_map.get(node_id, [])
        for prev_node in prev_nodes:
            if prev_node not in passed_nodes:
                paths: List[List[str]] = []
                self._get_prev_condition_end_with_node(
                    node_id, sub_queue_first, [], paths
                )
                for path in paths:
                    for node in path:
                        if node not in passed_nodes:
                            return False
        return True

    def _get_prev_condition_end_with_node(
        self, from_node: str, end_node: str, current: List[str], paths: List[List[str]]
    ):
        prev_nodes = self.prev_nodes_map.get(from_node, [])

        if len(prev_nodes) == 0:
            # paths.append(current)
            return

        if from_node == end_node:
            if len(current) > 0:
                paths.append(current)
            return

        for prev_node in prev_nodes:
            n = current.copy()
            n.append(prev_node)
            self._get_prev_condition_end_with_node(prev_node, end_node, n, paths)

    def _get_prev_condition(
        self, node_id: str, current: List[str], paths: List[List[str]]
    ):
        prev_nodes = self.prev_nodes_map.get(node_id, [])

        if len(prev_nodes) == 0:
            paths.append(current)
            return

        for prev_node in prev_nodes:
            if prev_node in self.node_conditions_map:
                n = current.copy()
                n.append(prev_node)
                self._get_prev_condition(prev_node, n, paths)
            else:
                self._get_prev_condition(prev_node, current.copy(), paths)

    @staticmethod
    def generate_prev_node(edge_map: Dict[str, WorkflowEdge]) -> Dict[str, List[str]]:
        prev_nodes = {}
        for edge in edge_map.values():
            nodes = prev_nodes.get(edge.target)
            if nodes is None:
                nodes = [edge.source]
            else:
                nodes.append(edge.source)
            prev_nodes[edge.target] = nodes
        return prev_nodes

    @staticmethod
    def generate_next_node(edge_map: Dict[str, WorkflowEdge]) -> Dict[str, List[str]]:
        next_nodes = {}
        for edge in edge_map.values():
            nodes = next_nodes.get(edge.source)
            if nodes is None:
                nodes = [edge.target]
            else:
                nodes.append(edge.target)
            next_nodes[edge.source] = nodes
        return next_nodes

    @staticmethod
    def generate_conditions(
        node_list: List[WorkflowNode], edge_map: Dict[str, WorkflowEdge]
    ) -> Dict[str, WorkflowNodeConditions]:
        node_conditions: Dict[str, WorkflowNodeConditions] = {}
        end_source_name = NodeTopology.get_end_source_name(edge_map)
        for node in node_list:
            if node.data.componentType == "condition":
                node_condition = WorkflowNodeConditions(node.id, [])
                # node_condition.node_id = node.id
                for condition in node.data.config.get("conditions", []):
                    # 条件id
                    condition_id = condition["condition_id"]
                    next_node_id = NodeTopology.__get_condition_next(
                        node.id, condition_id, edge_map
                    )

                    if next_node_id == "":
                        logger.error(
                            f"错误的配置，condition的id{condition_id}edge中没有对应的next node"
                        )
                        continue
                    c = WorkflowCondition(
                        condition_id,
                        next_node_id,
                        NodeTopology.__parse_condition(condition),
                    )
                    node_condition.conditions.append(c)
                node_condition.else_next_node_id = NodeTopology.__get_condition_next(
                    node.id, end_source_name, edge_map
                )
                node_conditions[node.id] = node_condition
        return node_conditions

    @staticmethod
    def get_end_source_name(edge_map: Dict[str, WorkflowEdge]) -> str:
        s = ["target_false", "node_end"]
        for end_name in s:
            for edge in edge_map.values():
                if edge.sourceHandle == end_name:
                    return end_name
        return "node_end"

    @staticmethod
    def __get_condition_next(
        node_id, condition_id: str, edge_map: Dict[str, WorkflowEdge]
    ) -> str:
        for edge in edge_map.values():
            if edge.source == node_id and edge.sourceHandle == condition_id:
                return edge.target
        return ""

    @staticmethod
    def __parse_condition(condition) -> str:
        if condition["conditions"] is None:
            return ""
        return NodeTopology.__parse_condition_expression(condition)

    @staticmethod
    def __parse_condition_expression(condition_config):
        def _parse_single_condition(cond):
            field = cond.get("field", "")
            value = cond.get("value", "")
            operator = cond.get("operator", "equals")

            if operator == "equals":
                is_number = True
                try:
                    float(value)
                except ValueError as _:
                    is_number = False
                if is_number:
                    return f"{field} == {value}"
                else:
                    return f"'{field}' == '{value}'"
            elif operator == "notEquals":
                is_number = True
                try:
                    float(value)
                except ValueError as _:
                    is_number = False
                if is_number:
                    return f"{field} != {value}"
                else:
                    return f"'{field}' != '{value}'"
            elif operator == "contains":
                if isinstance(value, str):
                    return f"'{value}' in '{field}'"
                return f"{value} in {field}"
            elif operator == "notContains":
                if isinstance(value, str):
                    return f"'{value}' not in '{field}'"
                return f"{value} not in {field}"
            elif operator == "greaterThan":
                return f"{field} > {value}"
            elif operator == "greaterThanOrEqual":
                return f"{field} >= {value}"
            elif operator == "lessThan":
                return f"{field} < {value}"
            elif operator == "lessThanOrEqual":
                return f"{field} <= {value}"
            elif operator == "isEmpty":
                return f"not {field}"
            elif operator == "isNotEmpty":
                return f"{field}"
            else:
                return f"{field} == {value}"

        relation = condition_config.get("relation", "and")
        conds = condition_config.get("conditions", [])
        parsed = [_parse_single_condition(cond) for cond in conds]

        joiner = f" {relation} "
        return f"{joiner.join(parsed)}"
