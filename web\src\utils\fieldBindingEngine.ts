/**
 * 字段绑定引擎
 * 处理单元格与数据字段的绑定关系
 */

import type { DataSource, FieldInfo } from './dataStructureAnalyzer'

export interface CellBinding {
  cell?: string
  expression?: string
  fieldPath: string
  dataSource?: DataSource
  fieldType?: string
  format?: string
  isArray?: boolean
  arrayBinding?: ArrayBinding
  type?: 'single' | 'loop'
  loopConfig?: LoopConfig
  conditionalExpression?: string // 条件表达式，如 "value == 0 ? '否' : '是'"
  displayConditions?: string // 条件表达式，如 "value == 0 ? '否' : '是'"
}

export interface LoopConfig {
  arrayField: string
  direction: 'vertical' | 'horizontal'
  hasNested?: boolean
  nestedArrayField?: string
  nestedDirection?: 'vertical' | 'horizontal'
}

export interface ArrayBinding {
  startRow: number
  endRow?: number
  columns: ColumnBinding[]
}

export interface ColumnBinding {
  column: string
  fieldPath: string
  header?: string
  format?: string
}

export interface TableBinding {
  dataSource: DataSource
  arrayPath: string
  startCell: string
  columns: ColumnBinding[]
  hasHeader: boolean
}

export class FieldBindingEngine {
  private cellBindings: Map<string, CellBinding> = new Map()
  private tableBindings: Map<string, TableBinding> = new Map()

  /**
   * 绑定单个字段到单元格
   */
  bindField(
    cellAddress: string,
    fieldPath: string,
    dataSource: DataSource,
    fieldInfo: FieldInfo,
    format?: string
  ): CellBinding {
    const expression = `\${${dataSource.outputVariable}.${fieldPath}}`

    const binding: CellBinding = {
      cell: cellAddress,
      expression,
      fieldPath,
      dataSource,
      fieldType: fieldInfo.type,
      format: format || this.getDefaultFormat(fieldInfo.type),
      isArray: fieldInfo.isArray
    }

    this.cellBindings.set(cellAddress, binding)
    return binding
  }

  /**
   * 绑定数组字段到表格区域
   */
  bindTable(
    startCell: string,
    arrayFieldPath: string,
    dataSource: DataSource,
    columns: ColumnBinding[],
    hasHeader = true
  ): TableBinding {
    const tableId = `${dataSource.nodeId}_${arrayFieldPath}`

    const tableBinding: TableBinding = {
      dataSource,
      arrayPath: arrayFieldPath,
      startCell,
      columns,
      hasHeader
    }

    this.tableBindings.set(tableId, tableBinding)
    return tableBinding
  }

  /**
   * 获取单元格绑定
   */
  getCellBinding(cellAddress: string): CellBinding | undefined {
    return this.cellBindings.get(cellAddress)
  }

  /**
   * 获取所有单元格绑定
   */
  getAllCellBindings(): Map<string, CellBinding> {
    return this.cellBindings
  }

  /**
   * 获取表格绑定
   */
  getTableBinding(tableId: string): TableBinding | undefined {
    return this.tableBindings.get(tableId)
  }

  /**
   * 获取所有表格绑定
   */
  getAllTableBindings(): Map<string, TableBinding> {
    return this.tableBindings
  }

  /**
   * 移除单元格绑定
   */
  removeCellBinding(cellAddress: string): boolean {
    return this.cellBindings.delete(cellAddress)
  }

  /**
   * 移除表格绑定
   */
  removeTableBinding(tableId: string): boolean {
    return this.tableBindings.delete(tableId)
  }

  /**
   * 清空所有绑定
   */
  clearAllBindings(): void {
    this.cellBindings.clear()
    this.tableBindings.clear()
  }

  /**
   * 解析绑定表达式，获取实际值
   */
  resolveBinding(binding: CellBinding, data: any): any {
    try {
      const value = this.getValueByPath(data, binding.expression)
      const formattedValue = this.formatValue(value, binding.format, binding.fieldType)

      // 如果有条件表达式，应用条件转换
      if (binding.conditionalExpression) {
        return this.applyConditionalExpression(formattedValue, binding.conditionalExpression)
      }

      return formattedValue
    } catch (error) {
      console.warn('解析绑定失败:', error)
      return binding.expression // 返回原始表达式
    }
  }

  /**
   * 应用条件表达式
   */
  private applyConditionalExpression(value: any, expression: string): any {
    try {
      // 简单的条件表达式解析，支持三元运算符格式
      // 例如: "value == 0 ? '否' : '是'"
      const ternaryMatch = expression.match(/(.+?)\s*\?\s*(.+?)\s*:\s*(.+)/)

      if (ternaryMatch) {
        const [, condition, trueValue, falseValue] = ternaryMatch

        // 替换条件中的 value 为实际值
        const conditionWithValue = condition.replace(/\bvalue\b/g, JSON.stringify(value))

        try {
          // 使用 Function 构造器安全地评估条件
          const result = new Function('return ' + conditionWithValue)()

          // 处理返回值，去除引号
          const processValue = (val: string) => {
            val = val.trim()
            if ((val.startsWith('"') && val.endsWith('"')) || (val.startsWith("'") && val.endsWith("'"))) {
              return val.slice(1, -1)
            }
            return val
          }

          return result ? processValue(trueValue) : processValue(falseValue)
        } catch (evalError) {
          console.warn('条件表达式评估失败:', evalError)
          return value
        }
      }

      return value
    } catch (error) {
      console.warn('应用条件表达式失败:', error)
      return value
    }
  }

  /**
   * 解析表格绑定，获取表格数据
   */
  resolveTableBinding(tableBinding: TableBinding, data: any): any[][] {
    try {
      const arrayData = this.getValueByPath(data, `\${${tableBinding.dataSource.outputVariable}.${tableBinding.arrayPath}}`)

      if (!Array.isArray(arrayData)) {
        return []
      }

      const result: any[][] = []

      // 添加表头
      if (tableBinding.hasHeader) {
        const headers = tableBinding.columns.map(col => col.header || col.fieldPath)
        result.push(headers)
      }

      // 添加数据行
      for (const item of arrayData) {
        const row = tableBinding.columns.map(col => {
          const value = this.getValueByPath(item, col.fieldPath)
          return this.formatValue(value, col.format, this.getFieldType(value))
        })
        result.push(row)
      }

      return result
    } catch (error) {
      console.warn('解析表格绑定失败:', error)
      return []
    }
  }

  /**
   * 根据路径获取值
   */
  private getValueByPath(data: any, path: string): any {
    if (!data || !path) {
      return undefined
    }

    // 移除变量表达式包装
    const cleanPath = path.replace(/^\$\{|\}$/g, '')

    const parts = cleanPath.split('.')
    let current = data

    for (const part of parts) {
      if (part.includes('[]')) {
        // 处理数组路径
        const arrayKey = part.replace('[]', '')
        if (arrayKey) {
          current = current[arrayKey]
        }
        return current // 返回数组
      } else {
        if (current && typeof current === 'object' && part in current) {
          current = current[part]
        } else {
          return undefined
        }
      }
    }

    return current
  }

  /**
   * 格式化值
   */
  private formatValue(value: any, format?: string, fieldType?: string): any {
    if (value === null || value === undefined) {
      return ''
    }

    if (!format) {
      return value
    }

    try {
      switch (fieldType) {
        case 'date':
          if (value instanceof Date) {
            return this.formatDate(value, format)
          }
          if (typeof value === 'string') {
            const date = new Date(value)
            if (!isNaN(date.getTime())) {
              return this.formatDate(date, format)
            }
          }
          break

        case 'number':
        case 'integer':
          if (typeof value === 'number') {
            return this.formatNumber(value, format)
          }
          break

        case 'string':
          if (format.includes('%s')) {
            return format.replace('%s', String(value))
          }
          break
      }
    } catch (error) {
      console.warn('格式化值失败:', error)
    }

    return value
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date, format: string): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  /**
   * 格式化数字
   */
  private formatNumber(value: number, format: string): string {
    if (format.includes('%')) {
      return `${(value * 100).toFixed(2)}%`
    }

    const decimalPlaces = (format.match(/\.(\d+)/) || ['', '0'])[1].length
    return value.toFixed(decimalPlaces)
  }

  /**
   * 获取字段类型
   */
  private getFieldType(value: any): string {
    if (value === null || value === undefined) return 'null'
    if (Array.isArray(value)) return 'array'
    if (value instanceof Date) return 'date'

    const type = typeof value
    if (type === 'number') {
      return Number.isInteger(value) ? 'integer' : 'number'
    }

    return type
  }

  /**
   * 获取默认格式
   */
  private getDefaultFormat(fieldType: string): string {
    switch (fieldType) {
      case 'date':
        return 'YYYY-MM-DD'
      case 'number':
        return '0.00'
      case 'integer':
        return '0'
      default:
        return ''
    }
  }

  /**
   * 导出绑定配置
   */
  exportBindings(): any {
    return {
      cellBindings: Array.from(this.cellBindings.entries()).map(([cell, binding]) => ({
        cell,
        expression: binding.expression,
        fieldPath: binding.fieldPath,
        dataSourceId: binding.dataSource.nodeId,
        fieldType: binding.fieldType,
        format: binding.format,
        isArray: binding.isArray
      })),
      tableBindings: Array.from(this.tableBindings.entries()).map(([id, binding]) => ({
        id,
        dataSourceId: binding.dataSource.nodeId,
        arrayPath: binding.arrayPath,
        startCell: binding.startCell,
        columns: binding.columns,
        hasHeader: binding.hasHeader
      }))
    }
  }

  /**
   * 导入绑定配置
   */
  importBindings(bindingsData: any, dataSources: DataSource[]): void {
    this.clearAllBindings()

    // 创建数据源映射
    const dataSourceMap = new Map(dataSources.map(ds => [ds.nodeId, ds]))

    // 导入单元格绑定
    if (bindingsData.cellBindings) {
      for (const bindingData of bindingsData.cellBindings) {
        const dataSource = dataSourceMap.get(bindingData.dataSourceId)
        if (dataSource) {
          const binding: CellBinding = {
            cell: bindingData.cell,
            expression: bindingData.expression,
            fieldPath: bindingData.fieldPath,
            dataSource,
            fieldType: bindingData.fieldType,
            format: bindingData.format,
            isArray: bindingData.isArray
          }
          this.cellBindings.set(bindingData.cell, binding)
        }
      }
    }

    // 导入表格绑定
    if (bindingsData.tableBindings) {
      for (const bindingData of bindingsData.tableBindings) {
        const dataSource = dataSourceMap.get(bindingData.dataSourceId)
        if (dataSource) {
          const binding: TableBinding = {
            dataSource,
            arrayPath: bindingData.arrayPath,
            startCell: bindingData.startCell,
            columns: bindingData.columns,
            hasHeader: bindingData.hasHeader
          }
          this.tableBindings.set(bindingData.id, binding)
        }
      }
    }
  }
}

// 导出单例实例
export const fieldBindingEngine = new FieldBindingEngine()
