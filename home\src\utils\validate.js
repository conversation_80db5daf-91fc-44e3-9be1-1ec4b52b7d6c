/**
 *  判断是否为空
 * @param val  检测的数据
 * @returns {boolean}   不为空 返回 true
 */
export function isNull(val) {
    if (typeof val == 'boolean') {
        return false;
    }
    if (typeof val == 'number') {
        return false;
    }
    if (val instanceof Array) {
        if (val.length == 0) {
            return true;
        }
    } else if (val instanceof Object) {
        if (JSON.stringify(val) === '{}') {
            return true;
        }
    } else {
        return val == 'null' || val == null || val === undefined || val == 'undefined' || val == undefined || val == '';
    }
    return false;
}

//手机号码验证规则
export function checkIsPhone(str) {
    // let reg = /^1([358][0-9|*]|4[56789|*]|6[2567|*]|7[012345678]|9[189])[0-9|*]{8}$/;
    let reg = /^1[3-9]\d{9}$/;
    return reg.test(str);
}

// 定时器
export const timer = (intDiff, _tip, _callback) => {
    let interval = setInterval(() => {
        let day = 0;
        let hour = 0;
        let minute = 0;
        let second = 0;
        if (intDiff > 0) {
            second = Math.floor(intDiff) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60;
        }
        if (second <= 9) second = '0' + second;
        _tip(second);
        intDiff--;
        if (intDiff === 0) {
            _callback();
            clearInterval(interval);
        }
    }, 1000);
};