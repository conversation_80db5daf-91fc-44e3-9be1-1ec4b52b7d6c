Translations
============

Robot Framework supports translating `section headers`_, settings_,
`Given/When/Then prefixes`__ used in Behavior Driven Development (BDD)
as well as `true and false strings`__ used in automatic Boolean argument
conversion. This appendix lists all translations for all languages,
excluding English, that Robot Framework supports out-of-the-box.

How to actually activate translations is explained in the Localization_ section.
That section also explains how to create custom language definitions and
how to contribute new translations.

__ `Behavior-driven style`_
__ `Supported conversions`_

.. contents::
   :depth: 1
   :local:

.. START GENERATED CONTENT
.. Generated by translations.py used by ug2html.py.

Arabic (ar)
-----------

New in Robot Framework 7.3.

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - الإعدادات
    * - Variables
      - المتغيرات
    * - Test Cases
      - وضعيات الاختبار
    * - Tasks
      - المهام
    * - Keywords
      - الأوامر
    * - Comments
      - التعليقات

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - المكتبة
    * - Resource
      - المورد
    * - Variables
      - المتغيرات
    * - Name
      - الاسم
    * - Documentation
      - التوثيق
    * - Metadata
      - البيانات الوصفية
    * - Suite Setup
      - إعداد المجموعة
    * - Suite Teardown
      - تفكيك المجموعة
    * - Test Setup
      - تهيئة الاختبار
    * - Task Setup
      - تهيئة المهمة
    * - Test Teardown
      - تفكيك الاختبار
    * - Task Teardown
      - تفكيك المهمة
    * - Test Template
      - قالب الاختبار
    * - Task Template
      - قالب المهمة
    * - Test Timeout
      - مهلة الاختبار
    * - Task Timeout
      - مهلة المهمة
    * - Test Tags
      - علامات الاختبار
    * - Task Tags
      - علامات المهمة
    * - Keyword Tags
      - علامات الأوامر
    * - Tags
      - العلامات
    * - Setup
      - إعداد
    * - Teardown
      - تفكيك
    * - Template
      - قالب
    * - Timeout
      - المهلة الزمنية
    * - Arguments
      - المعطيات

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - بافتراض
    * - When
      - عندما, لما
    * - Then
      - إذن, عندها
    * - And
      - و
    * - But
      - لكن

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - نعم, صحيح
    * - False
      - لا, خطأ

Bulgarian (bg)
--------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Настройки
    * - Variables
      - Променливи
    * - Test Cases
      - Тестови случаи
    * - Tasks
      - Задачи
    * - Keywords
      - Ключови думи
    * - Comments
      - Коментари

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Библиотека
    * - Resource
      - Ресурс
    * - Variables
      - Променлива
    * - Name
      -
    * - Documentation
      - Документация
    * - Metadata
      - Метаданни
    * - Suite Setup
      - Първоначални настройки на комплекта
    * - Suite Teardown
      - Приключване на комплекта
    * - Test Setup
      - Първоначални настройки на тестове
    * - Task Setup
      - Първоначални настройки на задачи
    * - Test Teardown
      - Приключване на тестове
    * - Task Teardown
      - Приключване на задачи
    * - Test Template
      - Шаблон за тестове
    * - Task Template
      - Шаблон за задачи
    * - Test Timeout
      - Таймаут за тестове
    * - Task Timeout
      - Таймаут за задачи
    * - Test Tags
      - Етикети за тестове
    * - Task Tags
      - Етикети за задачи
    * - Keyword Tags
      - Етикети за ключови думи
    * - Tags
      - Етикети
    * - Setup
      - Първоначални настройки
    * - Teardown
      - Приключване
    * - Template
      - Шаблон
    * - Timeout
      - Таймаут
    * - Arguments
      - Аргументи

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - В случай че
    * - When
      - Когато
    * - Then
      - Тогава
    * - And
      - И
    * - But
      - Но

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Вярно, Да, Включен
    * - False
      - Невярно, Не, Изключен, Нищо

Bosnian (bs)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Postavke
    * - Variables
      - Varijable
    * - Test Cases
      - Test Cases
    * - Tasks
      - Taskovi
    * - Keywords
      - Keywords
    * - Comments
      - Komentari

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Biblioteka
    * - Resource
      - Resursi
    * - Variables
      - Varijable
    * - Name
      -
    * - Documentation
      - Dokumentacija
    * - Metadata
      - Metadata
    * - Suite Setup
      - Suite Postavke
    * - Suite Teardown
      - Suite Teardown
    * - Test Setup
      - Test Postavke
    * - Task Setup
      - Task Postavke
    * - Test Teardown
      - Test Teardown
    * - Task Teardown
      - Task Teardown
    * - Test Template
      - Test Template
    * - Task Template
      - Task Template
    * - Test Timeout
      - Test Timeout
    * - Task Timeout
      - Task Timeout
    * - Test Tags
      - Test Tagovi
    * - Task Tags
      - Task Tagovi
    * - Keyword Tags
      - Keyword Tagovi
    * - Tags
      - Tagovi
    * - Setup
      - Postavke
    * - Teardown
      - Teardown
    * - Template
      - Template
    * - Timeout
      - Timeout
    * - Arguments
      - Argumenti

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Uslovno
    * - When
      - Kada
    * - Then
      - Tada
    * - And
      - I
    * - But
      - Ali

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      -
    * - False
      -

Czech (cs)
----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Nastavení
    * - Variables
      - Proměnné
    * - Test Cases
      - Testovací případy
    * - Tasks
      - Úlohy
    * - Keywords
      - Klíčová slova
    * - Comments
      - Komentáře

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Knihovna
    * - Resource
      - Zdroj
    * - Variables
      - Proměnná
    * - Name
      - Název
    * - Documentation
      - Dokumentace
    * - Metadata
      - Metadata
    * - Suite Setup
      - Příprava sady
    * - Suite Teardown
      - Ukončení sady
    * - Test Setup
      - Příprava testu
    * - Task Setup
      - Příprava úlohy
    * - Test Teardown
      - Ukončení testu
    * - Task Teardown
      - Ukončení úlohy
    * - Test Template
      - Šablona testu
    * - Task Template
      - Šablona úlohy
    * - Test Timeout
      - Časový limit testu
    * - Task Timeout
      - Časový limit úlohy
    * - Test Tags
      - Štítky testů
    * - Task Tags
      - Štítky úloh
    * - Keyword Tags
      - Štítky klíčových slov
    * - Tags
      - Štítky
    * - Setup
      - Příprava
    * - Teardown
      - Ukončení
    * - Template
      - Šablona
    * - Timeout
      - Časový limit
    * - Arguments
      - Argumenty

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Pokud
    * - When
      - Když
    * - Then
      - Pak
    * - And
      - A
    * - But
      - Ale

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Pravda, Ano, Zapnuto
    * - False
      - Nepravda, Ne, Vypnuto, Nic

German (de)
-----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Einstellungen
    * - Variables
      - Variablen
    * - Test Cases
      - Testfälle
    * - Tasks
      - Aufgaben
    * - Keywords
      - Schlüsselwörter
    * - Comments
      - Kommentare

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Bibliothek
    * - Resource
      - Ressource
    * - Variables
      - Variablen
    * - Name
      - Name
    * - Documentation
      - Dokumentation
    * - Metadata
      - Metadaten
    * - Suite Setup
      - Suitevorbereitung
    * - Suite Teardown
      - Suitenachbereitung
    * - Test Setup
      - Testvorbereitung
    * - Task Setup
      - Aufgabenvorbereitung
    * - Test Teardown
      - Testnachbereitung
    * - Task Teardown
      - Aufgabennachbereitung
    * - Test Template
      - Testvorlage
    * - Task Template
      - Aufgabenvorlage
    * - Test Timeout
      - Testzeitlimit
    * - Task Timeout
      - Aufgabenzeitlimit
    * - Test Tags
      - Testmarker
    * - Task Tags
      - Aufgabenmarker
    * - Keyword Tags
      - Schlüsselwortmarker
    * - Tags
      - Marker
    * - Setup
      - Vorbereitung
    * - Teardown
      - Nachbereitung
    * - Template
      - Vorlage
    * - Timeout
      - Zeitlimit
    * - Arguments
      - Argumente

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Angenommen
    * - When
      - Wenn
    * - Then
      - Dann
    * - And
      - Und
    * - But
      - Aber

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Wahr, Ja, An, Ein
    * - False
      - Falsch, Nein, Aus, Unwahr

Spanish (es)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Configuraciones
    * - Variables
      - Variables
    * - Test Cases
      - Casos de prueba
    * - Tasks
      - Tareas
    * - Keywords
      - Palabras clave
    * - Comments
      - Comentarios

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Biblioteca
    * - Resource
      - Recursos
    * - Variables
      - Variable
    * - Name
      - Nombre
    * - Documentation
      - Documentación
    * - Metadata
      - Metadatos
    * - Suite Setup
      - Configuración de la Suite
    * - Suite Teardown
      - Desmontaje de la Suite
    * - Test Setup
      - Configuración de prueba
    * - Task Setup
      - Configuración de tarea
    * - Test Teardown
      - Desmontaje de la prueba
    * - Task Teardown
      - Desmontaje de tareas
    * - Test Template
      - Plantilla de prueba
    * - Task Template
      - Plantilla de tareas
    * - Test Timeout
      - Tiempo de espera de la prueba
    * - Task Timeout
      - Tiempo de espera de las tareas
    * - Test Tags
      - Etiquetas de la prueba
    * - Task Tags
      - Etiquetas de las tareas
    * - Keyword Tags
      - Etiquetas de palabras clave
    * - Tags
      - Etiquetas
    * - Setup
      - Configuración
    * - Teardown
      - Desmontaje
    * - Template
      - Plantilla
    * - Timeout
      - Tiempo agotado
    * - Arguments
      - Argumentos

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Dado
    * - When
      - Cuando
    * - Then
      - Entonces
    * - And
      - Y
    * - But
      - Pero

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Verdadero, Si, On
    * - False
      - Falso, No, Off, Ninguno

Finnish (fi)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Asetukset
    * - Variables
      - Muuttujat
    * - Test Cases
      - Testit
    * - Tasks
      - Tehtävät
    * - Keywords
      - Avainsanat
    * - Comments
      - Kommentit

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Kirjasto
    * - Resource
      - Resurssi
    * - Variables
      - Muuttujat
    * - Name
      - Nimi
    * - Documentation
      - Dokumentaatio
    * - Metadata
      - Metatiedot
    * - Suite Setup
      - Setin Alustus
    * - Suite Teardown
      - Setin Alasajo
    * - Test Setup
      - Testin Alustus
    * - Task Setup
      - Tehtävän Alustus
    * - Test Teardown
      - Testin Alasajo
    * - Task Teardown
      - Tehtävän Alasajo
    * - Test Template
      - Testin Malli
    * - Task Template
      - Tehtävän Malli
    * - Test Timeout
      - Testin Aikaraja
    * - Task Timeout
      - Tehtävän Aikaraja
    * - Test Tags
      - Testin Tagit
    * - Task Tags
      - Tehtävän Tagit
    * - Keyword Tags
      - Avainsanan Tagit
    * - Tags
      - Tagit
    * - Setup
      - Alustus
    * - Teardown
      - Alasajo
    * - Template
      - Malli
    * - Timeout
      - Aikaraja
    * - Arguments
      - Argumentit

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Oletetaan
    * - When
      - Kun
    * - Then
      - Niin
    * - And
      - Ja
    * - But
      - Mutta

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Tosi, Kyllä, Päällä
    * - False
      - Epätosi, Ei, Pois

French (fr)
-----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Paramètres
    * - Variables
      - Variables
    * - Test Cases
      - Unités de test
    * - Tasks
      - Tâches
    * - Keywords
      - Mots-clés
    * - Comments
      - Commentaires

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Bibliothèque
    * - Resource
      - Ressource
    * - Variables
      - Variable
    * - Name
      - Nom
    * - Documentation
      - Documentation
    * - Metadata
      - Méta-donnée
    * - Suite Setup
      - Mise en place de suite
    * - Suite Teardown
      - Démontage de suite
    * - Test Setup
      - Mise en place de test
    * - Task Setup
      - Mise en place de tâche
    * - Test Teardown
      - Démontage de test
    * - Task Teardown
      - Démontage de test
    * - Test Template
      - Modèle de test
    * - Task Template
      - Modèle de tâche
    * - Test Timeout
      - Délai de test
    * - Task Timeout
      - Délai de tâche
    * - Test Tags
      - Étiquette de test
    * - Task Tags
      - Étiquette de tâche
    * - Keyword Tags
      - Etiquette de mot-clé
    * - Tags
      - Étiquette
    * - Setup
      - Mise en place
    * - Teardown
      - Démontage
    * - Template
      - Modèle
    * - Timeout
      - Délai d'attente
    * - Arguments
      - Arguments

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Étant donné, Étant donné que, Étant donné qu', Soit, Sachant que, Sachant qu', Sachant, Etant donné, Etant donné que, Etant donné qu', Etant donnée, Etant données
    * - When
      - Lorsque, Quand, Lorsqu'
    * - Then
      - Alors, Donc
    * - And
      - Et, Et que, Et qu'
    * - But
      - Mais, Mais que, Mais qu'

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Vrai, Oui, Actif
    * - False
      - Faux, Non, Désactivé, Aucun

Hindi (hi)
----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - स्थापना
    * - Variables
      - चर
    * - Test Cases
      - नियत कार्य प्रवेशिका
    * - Tasks
      - कार्य प्रवेशिका
    * - Keywords
      - कुंजीशब्द
    * - Comments
      - टिप्पणी

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - कोड़ प्रतिबिंब संग्रह
    * - Resource
      - संसाधन
    * - Variables
      - चर
    * - Name
      -
    * - Documentation
      - प्रलेखन
    * - Metadata
      - अधि-आंकड़ा
    * - Suite Setup
      - जांच की शुरुवात
    * - Suite Teardown
      - परीक्षण कार्य अंत
    * - Test Setup
      - परीक्षण कार्य प्रारंभ
    * - Task Setup
      - परीक्षण कार्य प्रारंभ
    * - Test Teardown
      - परीक्षण कार्य अंत
    * - Task Teardown
      - परीक्षण कार्य अंत
    * - Test Template
      - परीक्षण ढांचा
    * - Task Template
      - परीक्षण ढांचा
    * - Test Timeout
      - परीक्षण कार्य समय समाप्त
    * - Task Timeout
      - कार्य समयबाह्य
    * - Test Tags
      - जाँचका उपनाम
    * - Task Tags
      - कार्यका उपनाम
    * - Keyword Tags
      - कुंजीशब्द का उपनाम
    * - Tags
      - निशान
    * - Setup
      - व्यवस्थापना
    * - Teardown
      - विमोचन
    * - Template
      - साँचा
    * - Timeout
      - समय समाप्त
    * - Arguments
      - प्राचल

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - दिया हुआ
    * - When
      - जब
    * - Then
      - तब
    * - And
      - और
    * - But
      - परंतु

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - यथार्थ, निश्चित, हां, पर
    * - False
      - गलत, नहीं, हालाँकि, यद्यपि, नहीं, हैं

Italian (it)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Impostazioni
    * - Variables
      - Variabili
    * - Test Cases
      - Casi Di Test
    * - Tasks
      - Attività
    * - Keywords
      - Parole Chiave
    * - Comments
      - Commenti

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Libreria
    * - Resource
      - Risorsa
    * - Variables
      - Variabile
    * - Name
      - Nome
    * - Documentation
      - Documentazione
    * - Metadata
      - Metadati
    * - Suite Setup
      - Configurazione Suite
    * - Suite Teardown
      - Distruzione Suite
    * - Test Setup
      - Configurazione Test
    * - Task Setup
      - Configurazione Attività
    * - Test Teardown
      - Distruzione Test
    * - Task Teardown
      - Distruzione Attività
    * - Test Template
      - Modello Test
    * - Task Template
      - Modello Attività
    * - Test Timeout
      - Timeout Test
    * - Task Timeout
      - Timeout Attività
    * - Test Tags
      - Tag Del Test
    * - Task Tags
      - Tag Attività
    * - Keyword Tags
      - Tag Parola Chiave
    * - Tags
      - Tag
    * - Setup
      - Configurazione
    * - Teardown
      - Distruzione
    * - Template
      - Template
    * - Timeout
      - Timeout
    * - Arguments
      - Parametri

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Dato
    * - When
      - Quando
    * - Then
      - Allora
    * - And
      - E
    * - But
      - Ma

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Vero, Sì, On
    * - False
      - Falso, No, Off, Nessuno

Japanese (ja)
-------------

New in Robot Framework 7.0.1.

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - 設定
    * - Variables
      - 変数
    * - Test Cases
      - テスト ケース
    * - Tasks
      - タスク
    * - Keywords
      - キーワード
    * - Comments
      - コメント

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - ライブラリ
    * - Resource
      - リソース
    * - Variables
      - 変数
    * - Name
      - 名前
    * - Documentation
      - ドキュメント
    * - Metadata
      - メタデータ
    * - Suite Setup
      - スイート セットアップ
    * - Suite Teardown
      - スイート ティアダウン
    * - Test Setup
      - テスト セットアップ
    * - Task Setup
      - タスク セットアップ
    * - Test Teardown
      - テスト ティアダウン
    * - Task Teardown
      - タスク ティアダウン
    * - Test Template
      - テスト テンプレート
    * - Task Template
      - タスク テンプレート
    * - Test Timeout
      - テスト タイムアウト
    * - Task Timeout
      - タスク タイムアウト
    * - Test Tags
      - テスト タグ
    * - Task Tags
      - タスク タグ
    * - Keyword Tags
      - キーワード タグ
    * - Tags
      - タグ
    * - Setup
      - セットアップ
    * - Teardown
      - ティアダウン
    * - Template
      - テンプレート
    * - Timeout
      - タイムアウト
    * - Arguments
      - 引数

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - 仮定, 指定, 前提条件
    * - When
      - 条件, 次の場合, もし, 実行条件
    * - Then
      - アクション, その時, 動作
    * - And
      - および, 及び, かつ, 且つ, ならびに, 並びに, そして, それから
    * - But
      - ただし, 但し

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - 真, 有効, はい, オン
    * - False
      - 偽, 無効, いいえ, オフ

Korean (ko)
-----------

New in Robot Framework 7.1.

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - 설정
    * - Variables
      - 변수
    * - Test Cases
      - 테스트 사례
    * - Tasks
      - 작업
    * - Keywords
      - 키워드
    * - Comments
      - 의견

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - 라이브러리
    * - Resource
      - 자료
    * - Variables
      - 변수
    * - Name
      - 이름
    * - Documentation
      - 문서
    * - Metadata
      - 메타데이터
    * - Suite Setup
      - 스위트 설정
    * - Suite Teardown
      - 스위트 중단
    * - Test Setup
      - 테스트 설정
    * - Task Setup
      - 작업 설정
    * - Test Teardown
      - 테스트 중단
    * - Task Teardown
      - 작업 중단
    * - Test Template
      - 테스트 템플릿
    * - Task Template
      - 작업 템플릿
    * - Test Timeout
      - 테스트 시간 초과
    * - Task Timeout
      - 작업 시간 초과
    * - Test Tags
      - 테스트 태그
    * - Task Tags
      - 작업 태그
    * - Keyword Tags
      - 키워드 태그
    * - Tags
      - 태그
    * - Setup
      - 설정
    * - Teardown
      - 중단
    * - Template
      - 템플릿
    * - Timeout
      - 시간 초과
    * - Arguments
      - 주장

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - 주어진
    * - When
      - 때
    * - Then
      - 보다
    * - And
      - 그리고
    * - But
      - 하지만

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - 참, 네, 켜기
    * - False
      - 거짓, 아니오, 끄기

Dutch (nl)
----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Instellingen
    * - Variables
      - Variabelen
    * - Test Cases
      - Testgevallen
    * - Tasks
      - Taken
    * - Keywords
      - Actiewoorden
    * - Comments
      - Opmerkingen

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Bibliotheek
    * - Resource
      - Resource
    * - Variables
      - Variabele
    * - Name
      - Naam
    * - Documentation
      - Documentatie
    * - Metadata
      - Metadata
    * - Suite Setup
      - Suitevoorbereiding
    * - Suite Teardown
      - Suite-afronding
    * - Test Setup
      - Testvoorbereiding
    * - Task Setup
      - Taakvoorbereiding
    * - Test Teardown
      - Testafronding
    * - Task Teardown
      - Taakafronding
    * - Test Template
      - Testsjabloon
    * - Task Template
      - Taaksjabloon
    * - Test Timeout
      - Testtijdslimiet
    * - Task Timeout
      - Taaktijdslimiet
    * - Test Tags
      - Testlabels
    * - Task Tags
      - Taaklabels
    * - Keyword Tags
      - Actiewoordlabels
    * - Tags
      - Labels
    * - Setup
      - Voorbereiding
    * - Teardown
      - Afronding
    * - Template
      - Sjabloon
    * - Timeout
      - Tijdslimiet
    * - Arguments
      - Parameters

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Stel, Gegeven
    * - When
      - Als
    * - Then
      - Dan
    * - And
      - En
    * - But
      - Maar

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Waar, Ja, Aan
    * - False
      - Onwaar, Nee, Uit, Geen

Polish (pl)
-----------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Ustawienia
    * - Variables
      - Zmienne
    * - Test Cases
      - Przypadki Testowe
    * - Tasks
      - Zadania
    * - Keywords
      - Słowa Kluczowe
    * - Comments
      - Komentarze

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Biblioteka
    * - Resource
      - Zasób
    * - Variables
      - Zmienne
    * - Name
      - Nazwa
    * - Documentation
      - Dokumentacja
    * - Metadata
      - Metadane
    * - Suite Setup
      - Inicjalizacja Zestawu
    * - Suite Teardown
      - Ukończenie Zestawu
    * - Test Setup
      - Inicjalizacja Testu
    * - Task Setup
      - Inicjalizacja Zadania
    * - Test Teardown
      - Ukończenie Testu
    * - Task Teardown
      - Ukończenie Zadania
    * - Test Template
      - Szablon Testu
    * - Task Template
      - Szablon Zadania
    * - Test Timeout
      - Limit Czasowy Testu
    * - Task Timeout
      - Limit Czasowy Zadania
    * - Test Tags
      - Znaczniki Testu
    * - Task Tags
      - Znaczniki Zadania
    * - Keyword Tags
      - Znaczniki Słowa Kluczowego
    * - Tags
      - Znaczniki
    * - Setup
      - Inicjalizacja
    * - Teardown
      - Ukończenie
    * - Template
      - Szablon
    * - Timeout
      - Limit Czasowy
    * - Arguments
      - Argumenty

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Zakładając, Zakładając, że, Mając
    * - When
      - Jeżeli, Jeśli, Gdy, Kiedy
    * - Then
      - Wtedy
    * - And
      - Oraz, I
    * - But
      - Ale

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Prawda, Tak, Włączone
    * - False
      - Fałsz, Nie, Wyłączone, Nic

Portuguese (pt)
---------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Definições
    * - Variables
      - Variáveis
    * - Test Cases
      - Casos de Teste
    * - Tasks
      - Tarefas
    * - Keywords
      - Palavras-Chave
    * - Comments
      - Comentários

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Biblioteca
    * - Resource
      - Recurso
    * - Variables
      - Variável
    * - Name
      - Nome
    * - Documentation
      - Documentação
    * - Metadata
      - Metadados
    * - Suite Setup
      - Inicialização de Suíte
    * - Suite Teardown
      - Finalização de Suíte
    * - Test Setup
      - Inicialização de Teste
    * - Task Setup
      - Inicialização de Tarefa
    * - Test Teardown
      - Finalização de Teste
    * - Task Teardown
      - Finalização de Tarefa
    * - Test Template
      - Modelo de Teste
    * - Task Template
      - Modelo de Tarefa
    * - Test Timeout
      - Tempo Limite de Teste
    * - Task Timeout
      - Tempo Limite de Tarefa
    * - Test Tags
      - Etiquetas de Testes
    * - Task Tags
      - Etiquetas de Tarefas
    * - Keyword Tags
      - Etiquetas de Palavras-Chave
    * - Tags
      - Etiquetas
    * - Setup
      - Inicialização
    * - Teardown
      - Finalização
    * - Template
      - Modelo
    * - Timeout
      - Tempo Limite
    * - Arguments
      - Argumentos

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Dado
    * - When
      - Quando
    * - Then
      - Então
    * - And
      - E
    * - But
      - Mas

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Verdadeiro, Verdade, Sim, Ligado
    * - False
      - Falso, Não, Desligado, Desativado, Nada

Brazilian Portuguese (pt-BR)
----------------------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Configurações
    * - Variables
      - Variáveis
    * - Test Cases
      - Casos de Teste
    * - Tasks
      - Tarefas
    * - Keywords
      - Palavras-Chave
    * - Comments
      - Comentários

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Biblioteca
    * - Resource
      - Recurso
    * - Variables
      - Variável
    * - Name
      - Nome
    * - Documentation
      - Documentação
    * - Metadata
      - Metadados
    * - Suite Setup
      - Configuração da Suíte
    * - Suite Teardown
      - Finalização de Suíte
    * - Test Setup
      - Inicialização de Teste
    * - Task Setup
      - Inicialização de Tarefa
    * - Test Teardown
      - Finalização de Teste
    * - Task Teardown
      - Finalização de Tarefa
    * - Test Template
      - Modelo de Teste
    * - Task Template
      - Modelo de Tarefa
    * - Test Timeout
      - Tempo Limite de Teste
    * - Task Timeout
      - Tempo Limite de Tarefa
    * - Test Tags
      - Test Tags
    * - Task Tags
      - Task Tags
    * - Keyword Tags
      - Keyword Tags
    * - Tags
      - Etiquetas
    * - Setup
      - Inicialização
    * - Teardown
      - Finalização
    * - Template
      - Modelo
    * - Timeout
      - Tempo Limite
    * - Arguments
      - Argumentos

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Dado
    * - When
      - Quando
    * - Then
      - Então
    * - And
      - E
    * - But
      - Mas

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Verdadeiro, Verdade, Sim, Ligado
    * - False
      - Falso, Não, Desligado, Desativado, Nada

Romanian (ro)
-------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Setari
    * - Variables
      - Variabile
    * - Test Cases
      - Cazuri De Test
    * - Tasks
      - Sarcini
    * - Keywords
      - Cuvinte Cheie
    * - Comments
      - Comentarii

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Librarie
    * - Resource
      - Resursa
    * - Variables
      - Variabila
    * - Name
      - Nume
    * - Documentation
      - Documentatie
    * - Metadata
      - Metadate
    * - Suite Setup
      - Configurare De Suita
    * - Suite Teardown
      - Configurare De Intrerupere
    * - Test Setup
      - Setare De Test
    * - Task Setup
      - Configuarare activitate
    * - Test Teardown
      - Inrerupere De Test
    * - Task Teardown
      - Intrerupere activitate
    * - Test Template
      - Sablon De Test
    * - Task Template
      - Sablon de activitate
    * - Test Timeout
      - Timp Expirare Test
    * - Task Timeout
      - Timp de expirare activitate
    * - Test Tags
      - Taguri De Test
    * - Task Tags
      - Etichete activitate
    * - Keyword Tags
      - Etichete metode
    * - Tags
      - Etichete
    * - Setup
      - Setare
    * - Teardown
      - Intrerupere
    * - Template
      - Sablon
    * - Timeout
      - Expirare
    * - Arguments
      - Argumente

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Fie ca
    * - When
      - Cand
    * - Then
      - Atunci
    * - And
      - Si
    * - But
      - Dar

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Adevarat, Da, Cand
    * - False
      - Fals, Nu, Oprit, Niciun

Russian (ru)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Настройки
    * - Variables
      - Переменные
    * - Test Cases
      - Заголовки тестов
    * - Tasks
      - Задача
    * - Keywords
      - Ключевые слова
    * - Comments
      - Комментарии

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Библиотека
    * - Resource
      - Ресурс
    * - Variables
      - Переменные
    * - Name
      -
    * - Documentation
      - Документация
    * - Metadata
      - Метаданные
    * - Suite Setup
      - Инициализация комплекта тестов
    * - Suite Teardown
      - Завершение комплекта тестов
    * - Test Setup
      - Инициализация теста
    * - Task Setup
      - Инициализация задания
    * - Test Teardown
      - Завершение теста
    * - Task Teardown
      - Завершение задания
    * - Test Template
      - Шаблон теста
    * - Task Template
      - Шаблон задания
    * - Test Timeout
      - Лимит выполнения теста
    * - Task Timeout
      - Лимит задания
    * - Test Tags
      - Теги тестов
    * - Task Tags
      - Метки заданий
    * - Keyword Tags
      - Метки ключевых слов
    * - Tags
      - Метки
    * - Setup
      - Инициализация
    * - Teardown
      - Завершение
    * - Template
      - Шаблон
    * - Timeout
      - Лимит
    * - Arguments
      - Аргументы

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Дано
    * - When
      - Когда
    * - Then
      - Тогда
    * - And
      - И
    * - But
      - Но

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      -
    * - False
      -

Swedish (sv)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Inställningar
    * - Variables
      - Variabler
    * - Test Cases
      - Testfall
    * - Tasks
      - Taskar
    * - Keywords
      - Nyckelord
    * - Comments
      - Kommentarer

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Bibliotek
    * - Resource
      - Resurs
    * - Variables
      - Variabel
    * - Name
      - Namn
    * - Documentation
      - Dokumentation
    * - Metadata
      - Metadata
    * - Suite Setup
      - Svit konfigurering
    * - Suite Teardown
      - Svit nedrivning
    * - Test Setup
      - Test konfigurering
    * - Task Setup
      - Task konfigurering
    * - Test Teardown
      - Test nedrivning
    * - Task Teardown
      - Task nedrivning
    * - Test Template
      - Test mall
    * - Task Template
      - Task mall
    * - Test Timeout
      - Test timeout
    * - Task Timeout
      - Task timeout
    * - Test Tags
      - Test taggar
    * - Task Tags
      - Arbetsuppgift taggar
    * - Keyword Tags
      - Nyckelord taggar
    * - Tags
      - Taggar
    * - Setup
      - Konfigurering
    * - Teardown
      - Nedrivning
    * - Template
      - Mall
    * - Timeout
      - Timeout
    * - Arguments
      - Argument

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Givet
    * - When
      - När
    * - Then
      - Då
    * - And
      - Och
    * - But
      - Men

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Sant, Ja, På
    * - False
      - Falskt, Nej, Av, Ingen

Thai (th)
---------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - การตั้งค่า
    * - Variables
      - กำหนดตัวแปร
    * - Test Cases
      - การทดสอบ
    * - Tasks
      - งาน
    * - Keywords
      - คำสั่งเพิ่มเติม
    * - Comments
      - คำอธิบาย

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - ชุดคำสั่งที่ใช้
    * - Resource
      - ไฟล์ที่ใช้
    * - Variables
      - ชุดตัวแปร
    * - Name
      -
    * - Documentation
      - เอกสาร
    * - Metadata
      - รายละเอียดเพิ่มเติม
    * - Suite Setup
      - กำหนดค่าเริ่มต้นของชุดการทดสอบ
    * - Suite Teardown
      - คืนค่าของชุดการทดสอบ
    * - Test Setup
      - กำหนดค่าเริ่มต้นของการทดสอบ
    * - Task Setup
      - กำหนดค่าเริ่มต้นของงาน
    * - Test Teardown
      - คืนค่าของการทดสอบ
    * - Task Teardown
      - คืนค่าของงาน
    * - Test Template
      - โครงสร้างของการทดสอบ
    * - Task Template
      - โครงสร้างของงาน
    * - Test Timeout
      - เวลารอของการทดสอบ
    * - Task Timeout
      - เวลารอของงาน
    * - Test Tags
      - กลุ่มของการทดสอบ
    * - Task Tags
      - กลุ่มของงาน
    * - Keyword Tags
      - กลุ่มของคำสั่งเพิ่มเติม
    * - Tags
      - กลุ่ม
    * - Setup
      - กำหนดค่าเริ่มต้น
    * - Teardown
      - คืนค่า
    * - Template
      - โครงสร้าง
    * - Timeout
      - หมดเวลา
    * - Arguments
      - ค่าที่ส่งเข้ามา

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - กำหนดให้
    * - When
      - เมื่อ
    * - Then
      - ดังนั้น
    * - And
      - และ
    * - But
      - แต่

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      -
    * - False
      -

Turkish (tr)
------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Ayarlar
    * - Variables
      - Değişkenler
    * - Test Cases
      - Test Durumları
    * - Tasks
      - Görevler
    * - Keywords
      - Anahtar Kelimeler
    * - Comments
      - Yorumlar

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Kütüphane
    * - Resource
      - Kaynak
    * - Variables
      - Değişkenler
    * - Name
      -
    * - Documentation
      - Dokümantasyon
    * - Metadata
      - Üstveri
    * - Suite Setup
      - Takım Kurulumu
    * - Suite Teardown
      - Takım Bitişi
    * - Test Setup
      - Test Kurulumu
    * - Task Setup
      - Görev Kurulumu
    * - Test Teardown
      - Test Bitişi
    * - Task Teardown
      - Görev Bitişi
    * - Test Template
      - Test Taslağı
    * - Task Template
      - Görev Taslağı
    * - Test Timeout
      - Test Zaman Aşımı
    * - Task Timeout
      - Görev Zaman Aşımı
    * - Test Tags
      - Test Etiketleri
    * - Task Tags
      - Görev Etiketleri
    * - Keyword Tags
      - Anahtar Kelime Etiketleri
    * - Tags
      - Etiketler
    * - Setup
      - Kurulum
    * - Teardown
      - Bitiş
    * - Template
      - Taslak
    * - Timeout
      - Zaman Aşımı
    * - Arguments
      - Argümanlar

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Diyelim ki
    * - When
      - Eğer ki
    * - Then
      - O zaman
    * - And
      - Ve
    * - But
      - Ancak

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Doğru, Evet, Açik
    * - False
      - Yanliş, Hayir, Kapali

Ukrainian (uk)
--------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Налаштування
    * - Variables
      - Змінні
    * - Test Cases
      - Тест-кейси
    * - Tasks
      - Завдань
    * - Keywords
      - Ключових слова
    * - Comments
      - Коментарів

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Бібліотека
    * - Resource
      - Ресурс
    * - Variables
      - Змінна
    * - Name
      -
    * - Documentation
      - Документація
    * - Metadata
      - Метадані
    * - Suite Setup
      - Налаштування Suite
    * - Suite Teardown
      - Розбірка Suite
    * - Test Setup
      - Налаштування тесту
    * - Task Setup
      - Налаштування завдання
    * - Test Teardown
      - Розбирання тестy
    * - Task Teardown
      - Розбір завдання
    * - Test Template
      - Тестовий шаблон
    * - Task Template
      - Шаблон завдання
    * - Test Timeout
      - Час тестування
    * - Task Timeout
      - Час очікування завдання
    * - Test Tags
      - Тестові теги
    * - Task Tags
      - Теги завдань
    * - Keyword Tags
      - Теги ключових слів
    * - Tags
      - Теги
    * - Setup
      - Встановлення
    * - Teardown
      - Cпростовувати пункт за пунктом
    * - Template
      - Шаблон
    * - Timeout
      - Час вийшов
    * - Arguments
      - Аргументи

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Дано
    * - When
      - Коли
    * - Then
      - Тоді
    * - And
      - Та
    * - But
      - Але

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      -
    * - False
      -

Vietnamese (vi)
---------------

New in Robot Framework 6.1.

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - Cài Đặt
    * - Variables
      - Các biến số
    * - Test Cases
      - Các kịch bản kiểm thử
    * - Tasks
      - Các nghiệm vụ
    * - Keywords
      - Các từ khóa
    * - Comments
      - Các chú thích

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - Thư viện
    * - Resource
      - Tài nguyên
    * - Variables
      - Biến số
    * - Name
      - Tên
    * - Documentation
      - Tài liệu hướng dẫn
    * - Metadata
      - Dữ liệu tham chiếu
    * - Suite Setup
      - Tiền thiết lập bộ kịch bản kiểm thử
    * - Suite Teardown
      - Hậu thiết lập bộ kịch bản kiểm thử
    * - Test Setup
      - Tiền thiết lập kịch bản kiểm thử
    * - Task Setup
      - Tiền thiểt lập nhiệm vụ
    * - Test Teardown
      - Hậu thiết lập kịch bản kiểm thử
    * - Task Teardown
      - Hậu thiết lập nhiệm vụ
    * - Test Template
      - Mẫu kịch bản kiểm thử
    * - Task Template
      - Mẫu nhiễm vụ
    * - Test Timeout
      - Thời gian chờ kịch bản kiểm thử
    * - Task Timeout
      - Thời gian chờ nhiệm vụ
    * - Test Tags
      - Các nhãn kịch bản kiểm thử
    * - Task Tags
      - Các nhãn nhiệm vụ
    * - Keyword Tags
      - Các từ khóa nhãn
    * - Tags
      - Các thẻ
    * - Setup
      - Tiền thiết lập
    * - Teardown
      - Hậu thiết lập
    * - Template
      - Mẫu
    * - Timeout
      - Thời gian chờ
    * - Arguments
      - Các đối số

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - Đã cho
    * - When
      - Khi
    * - Then
      - Thì
    * - And
      - Và
    * - But
      - Nhưng

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - Đúng, Vâng, Mở
    * - False
      - Sai, Không, Tắt, Không Có Gì

Chinese Simplified (zh-CN)
--------------------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - 设置
    * - Variables
      - 变量
    * - Test Cases
      - 用例
    * - Tasks
      - 任务
    * - Keywords
      - 关键字
    * - Comments
      - 备注

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - 程序库
    * - Resource
      - 资源文件
    * - Variables
      - 变量文件
    * - Name
      -
    * - Documentation
      - 说明
    * - Metadata
      - 元数据
    * - Suite Setup
      - 用例集启程
    * - Suite Teardown
      - 用例集终程
    * - Test Setup
      - 用例启程
    * - Task Setup
      - 任务启程
    * - Test Teardown
      - 用例终程
    * - Task Teardown
      - 任务终程
    * - Test Template
      - 用例模板
    * - Task Template
      - 任务模板
    * - Test Timeout
      - 用例超时
    * - Task Timeout
      - 任务超时
    * - Test Tags
      - 用例标签
    * - Task Tags
      - 任务标签
    * - Keyword Tags
      - 关键字标签
    * - Tags
      - 标签
    * - Setup
      - 启程
    * - Teardown
      - 终程
    * - Template
      - 模板
    * - Timeout
      - 超时
    * - Arguments
      - 参数

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - 假定
    * - When
      - 当
    * - Then
      - 那么
    * - And
      - 并且
    * - But
      - 但是

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - 真, 是, 开
    * - False
      - 假, 否, 关, 空

Chinese Traditional (zh-TW)
---------------------------

Section headers
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Header
      - Translation
    * - Settings
      - 設置
    * - Variables
      - 變量
    * - Test Cases
      - 案例
    * - Tasks
      - 任務
    * - Keywords
      - 關鍵字
    * - Comments
      - 備註

Settings
~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Setting
      - Translation
    * - Library
      - 函式庫
    * - Resource
      - 資源文件
    * - Variables
      - 變量文件
    * - Name
      -
    * - Documentation
      - 說明
    * - Metadata
      - 元數據
    * - Suite Setup
      - 測試套啟程
    * - Suite Teardown
      - 測試套終程
    * - Test Setup
      - 測試啟程
    * - Task Setup
      - 任務啟程
    * - Test Teardown
      - 測試終程
    * - Task Teardown
      - 任務終程
    * - Test Template
      - 測試模板
    * - Task Template
      - 任務模板
    * - Test Timeout
      - 測試逾時
    * - Task Timeout
      - 任務逾時
    * - Test Tags
      - 測試標籤
    * - Task Tags
      - 任務標籤
    * - Keyword Tags
      - 關鍵字標籤
    * - Tags
      - 標籤
    * - Setup
      - 啟程
    * - Teardown
      - 終程
    * - Template
      - 模板
    * - Timeout
      - 逾時
    * - Arguments
      - 参数

BDD prefixes
~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - Prefix
      - Translation
    * - Given
      - 假定
    * - When
      - 當
    * - Then
      - 那麼
    * - And
      - 並且
    * - But
      - 但是

Boolean strings
~~~~~~~~~~~~~~~

.. list-table::
    :class: tabular
    :width: 40em
    :widths: 2 3
    :header-rows: 1

    * - True/False
      - Values
    * - True
      - 真, 是, 開
    * - False
      - 假, 否, 關, 空
