import os
import shutil
import sys
from pathlib import Path

from loguru import logger


def get_base_path() -> Path:
    if is_pyinstaller_pack():
        return Path(sys._MEIPASS)
    else:
        return Path(__file__).parent.parent


def is_pyinstaller_pack() -> bool:
    return getattr(sys, "frozen", False) and hasattr(sys, "_MEIPASS")


def get_resource_path(relative_path: str) -> str:
    base_path = get_base_path()
    resource_path = base_path / relative_path  # 使用 Path 对象的 / 操作符拼接路径
    return str(resource_path.as_posix())  # 转换为使用斜杠的字符串


def print_path(path):
    logger.info("打印路径做打包路径测试-----------------------------------------------")
    logger.info(f"getr {get_resource_path(path)}")
    logger.info(f"path {Path(path).absolute()}")
    logger.info("-----------------------------------------------------------------")


def copy_packaged_files_to_current_dir(
    resource_paths,
    overwrite_existing=False,  # 新增：是否覆盖已存在的文件/目录
    not_frozen_return=True,
):
    """
    将PyInstaller打包的资源文件/目录复制到当前工作目录（带存在性检查）

    参数:
        resource_paths: 列表，包含要复制的资源路径（相对于打包根目录）
        overwrite_existing: 布尔值，若为True则覆盖已存在的目标，默认False
    """
    # 判断是否为打包后的环境
    if getattr(sys, "frozen", False):
        package_root = Path(sys._MEIPASS)  # 打包后的资源根目录
    else:
        if not_frozen_return:
            return
        package_root = Path(os.path.dirname(os.path.abspath(__file__)))  # 开发环境路径

    current_dir = Path(os.getcwd())
    print(f"当前工作目录: {current_dir}")

    for resource in resource_paths:
        source = package_root / resource
        destination = current_dir / resource

        # 检查源资源是否存在
        if not source.exists():
            print(f"警告: 源资源不存在 - {source}，跳过")
            continue

        # 检查目标是否已存在
        if destination.exists():
            if overwrite_existing:
                print(f"目标已存在，将覆盖 - {destination}")
                # 先删除已存在的目标
                if destination.is_dir():
                    shutil.rmtree(destination)
                else:
                    os.remove(destination)
            else:
                print(f"目标已存在，跳过复制 - {destination}")
                continue  # 不覆盖，直接跳过

        # 执行复制操作
        try:
            if source.is_dir():
                shutil.copytree(source, destination)
                print(f"目录复制成功: {source} -> {destination}")
            else:
                # 确保目标文件的父目录存在
                if not destination.parent.exists():
                    destination.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, destination)  # 保留文件元数据
                print(f"文件复制成功: {source} -> {destination}")
        except Exception as e:
            print(f"复制失败 {resource}: {str(e)}")
