import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 用户信息状态
  const userInfo = ref<object | null>(null)
  // 全局配置
  const configs = ref<object | null>(null)

  // 方法
  const setUserInfo = (info: object) => {
    userInfo.value = info
  }
  // 方法
  const setConfigs = (info: object) => {
    configs.value = info
  }

  const clearUser = () => {
    userInfo.value = null
  }
  const clearConfigs = () => {
    configs.value = null
  }

  return {
    userInfo,
    setUserInfo,
    clearUser,

    configs,
    setConfigs,
    clearConfigs
  }
})
