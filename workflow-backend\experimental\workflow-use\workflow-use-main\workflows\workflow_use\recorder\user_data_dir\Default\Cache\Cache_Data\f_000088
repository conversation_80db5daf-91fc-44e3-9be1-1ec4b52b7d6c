(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-620caac4"],{"6a9b":function(e,t,i){e.exports=i.p+"static/img/logo3.695e28b5.png"},"7bae":function(e,t,i){e.exports=i.p+"static/img/chat_hcs.d6cf5217.png"},"8cfa":function(e,t,i){},aa58:function(e,t,i){"use strict";i.r(t);var s=(i("e9f5"),i("910d"),i("f665"),i("7d54"),{name:"aiAssistant",components:{Chat:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-6464ad6a")]).then(i.bind(null,"ba4f")),Chat_hcs:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-093d85e6")]).then(i.bind(null,"7843")),Report:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-4c34b870")]).then(i.bind(null,"b143")),Device_purchase:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-734bb9b9")]).then(i.bind(null,"5b74")),Service:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-2df4f603")]).then(i.bind(null,"1101")),Ps_dispatching:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-1f7c93c6")]).then(i.bind(null,"6481")),Gs_dispatching:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-0399d562")]).then(i.bind(null,"0791")),Duty_robot:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-ede49ca0")]).then(i.bind(null,"e96e")),Twinmap:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-2a6aa068")]).then(i.bind(null,"6f190")),Leaky_guard:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-24f3f763")]).then(i.bind(null,"7342")),CustomUrl:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-2d88fc4a")]).then(i.bind(null,"c7b4")),Hydraulic_model:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-615c639c")]).then(i.bind(null,"a218")),Search:()=>i.e("chunk-c35539f8").then(i.bind(null,"308b")),Noise_model:()=>Promise.all([i.e("chunk-commons"),i.e("chunk-0df06a5a"),i.e("chunk-f9b9e012"),i.e("chunk-33fa93ec")]).then(i.bind(null,"8471"))},data:()=>({innerHeight:null,originWidth:null,isMaxScreen:!1,isFullscreen:!1,isFullscreenFold:!1,isWebShowMobile:!1,icon:{hcsLogo:i("7bae"),logo:i("6a9b")},menuList:[{title:"对话",icon:"icon-duihua",ref:"chat"},{title:"简报",icon:"icon-jianbao",ref:"report"},{title:"设备比选",icon:"icon-shebeibixuan",ref:"device_purchase"},{title:"营销客服",icon:"icon-kefu1",ref:"service"},{title:"孪生图",icon:"icon-ditu",ref:"twinmap"},{title:"排水调度",icon:"icon-gongshui",ref:"ps_dispatching"},{title:"供水调度",icon:"icon-paishui1",ref:"gs_dispatching"},{title:"值班机器人",icon:"icon-jiqiren",ref:"duty_robot"},{title:"漏控卫士",icon:"icon-loudian",ref:"leaky_guard"},{title:"水力模型",icon:"icon-moxing1",ref:"hydraulic_model"},{title:"噪声大模型",icon:"icon-zaoyinjiance-",ref:"noise_model"}],currentTab:"",isInputFocus:!1,isKeyboard:!1,showDrawer:!1}),computed:{currentComponent(){let e=this.menuList.find((e=>e.title===this.currentTab))||{},t=e.ref,i=e.ref,s=e.ref,n=null;return!this.aiConfigs[e.ref+"_customUrl"]||this.isTwin||this.isMobile&&!this.isService||(t="customUrl",n=this.aiConfigs[e.ref+"_customUrl"],i=this.aiConfigs[e.ref+"_customUrl"]),{ref:t,url:n,key:i,agent:s,theme:null,customTitle:e.title}},currentUser(){return this.$store.getters.currentUser},theme(){return this.$store.getters.theme},aiConfigs(){return this.$store.getters.configs},aiTitle(){return this.$route.query.title||this.aiConfigs.name||"一诺AI"},aiLogo(){return this.isHCS?this.icon.hcsLogo:this.aiConfigs.logo||this.icon.logo},isIframe(){return!("iframe"!==this.$route.params.mode&&!this.$route.query.isIframe||this.isUniwimPc)},isUniwimPc(){return"uniwimpc"===this.$route.params.mode||"pc"===this.$route.query.uniwim},isWimpic(){return"wimpic"===this.$route.params.mode||"wimpic"===this.$route.query.from},isBigScreen(){return"bigscreen"===this.$route.params.mode},isHCS(){return"hcs"===this.$route.params.mode},isChat(){return"chat"===this.$route.params.mode},isReport(){return"report"===this.$route.params.mode},isPurchase(){return"devicepurchase"===this.$route.params.mode||"device_purchase"===this.$route.params.mode||"purchase"===this.$route.params.mode},isTwin(){return"twin"===this.$route.params.mode||"twinmap"===this.$route.params.mode},isScada(){return"scada"===this.$route.params.mode},isPsDispatching(){return"psdispatching"===this.$route.params.mode||"ps_dispatching"===this.$route.params.mode},isGsDispatching(){return"gsdispatching"===this.$route.params.mode||"gs_dispatching"===this.$route.params.mode},isDutyRobot(){return"dutyrobot"===this.$route.params.mode||"duty_robot"===this.$route.params.mode},isHydraulicModel(){return"hydraulicmodel"===this.$route.params.mode||"hydraulic_model"===this.$route.params.mode},isService(){return"service"===this.$route.params.mode},isLeakyGuard(){return"leakyguard"===this.$route.params.mode||"leaky_guard"===this.$route.params.mode},isNoiseModel(){return"noisemodel"===this.$route.params.mode||"noise_model"===this.$route.params.mode},hideNavLogo(){return this.$route.query.hideNavLogo},source(){return this.$route.query.source},isMobile(){return this.$utils.isMobile()},defaultMenu(){return this.$route.query.defaultMenu},hideMenu(){return this.isHCS||this.$route.query.hideMenu||this.menuList.length<=1},isShowFooterBar(){return!this.isMobile||!this.isInputFocus&&!this.isKeyboard}},watch:{$route:{immediate:!0,handler(){this.$nextTick((()=>{this.$route.query.currentTab&&this.menuList.find((e=>e.title===this.$route.query.currentTab))&&(this.currentTab=this.$route.query.currentTab)}))}},"currentComponent.agent":{deep:!0,handler(e){this.$nextTick((()=>{var t;if(null!==(t=this.$refs[e])&&void 0!==t&&t.$refs){var i;const t=Object.keys(null===(i=this.$refs[e])||void 0===i?void 0:i.$refs).filter((e=>e.startsWith("aiEchart")));t.forEach((t=>{var i;null===(i=this.$refs[e])||void 0===i||null===(i=i.$refs[t])||void 0===i||i.resize()}))}}))}}},mounted(){if(this.innerHeight=window.innerHeight,this.originWidth=document.documentElement.clientWidth||document.body.clientWidth,this.isHdkjPromise(),this.setBodyStyle(),(this.isIframe||this.isUniwimPc)&&(this.isFullscreenFold=!0),this.$nextTick((()=>{this.resize()})),this.isMobile&&document.body.classList.add("is-mobile"),this.handleKeyboard(),window.addEventListener("resize",this.resize),this.$route.query.showAgent){const e=this.$route.query.showAgent.split(",");this.menuList=this.menuList.filter((t=>e.indexOf(t.ref)>-1)),this.$nextTick((()=>{this.currentTab=this.menuList.find((t=>t.ref===e[0])).title}))}else if(this.isChat)this.menuList=[{title:"对话",icon:"icon-duihua",ref:"chat"}];else if(this.isReport)this.menuList=[{title:"简报",icon:"icon-jianbao",ref:"report"}];else if(this.isPurchase)this.menuList=[{title:"设备比选",icon:"icon-shebeibixuan",ref:"device_purchase"}];else if(this.isHCS)this.menuList=[{title:"AI助理",icon:"icon-duihua",ref:"chat_hcs"}],document.title="AI助理";else if(this.isTwin)this.aiConfigs.theme="bigscreen1",document.documentElement.setAttribute("data-ai-theme",this.aiConfigs.theme),localStorage.setItem("wimAiTheme"+this.currentUser.tenantId,this.aiConfigs.theme),this.menuList=[{title:"对话",icon:"icon-duihua",ref:"chat"},{title:"排水调度",icon:"icon-gongshui",ref:"ps_dispatching"},{title:"供水调度",icon:"icon-paishui1",ref:"gs_dispatching"},{title:"值班机器人",icon:"icon-jiqiren",ref:"duty_robot"},{title:"水力模型",icon:"icon-moxing1",ref:"hydraulic_model"},{title:"孪生图",icon:"icon-ditu",ref:"twinmap"}],this.currentTab="孪生图";else if(this.isHydraulicModel)this.menuList=[{title:"水力模型",icon:"icon-moxing1",ref:"hydraulic_model"}];else if(this.isNoiseModel)this.menuList=[{title:"噪声大模型",icon:"icon-zaoyinjiance-",ref:"noise_model"}];else if(this.isScada)this.menuList=[{title:"排水调度",icon:"icon-gongshui",ref:"ps_dispatching"},{title:"供水调度",icon:"icon-paishui1",ref:"gs_dispatching"}];else if(this.isDutyRobot)this.menuList=[{title:"值班机器人",icon:"icon-jiqiren",ref:"duty_robot"}];else if(this.isService)this.menuList=[{title:"营销客服",icon:"icon-kefu1",ref:"service"}];else if(this.isLeakyGuard)this.menuList=[{title:"漏控卫士",icon:"icon-jiqiren",ref:"leaky_guard"}];else if(this.isPsDispatching)this.menuList=[{title:"排水调度",icon:"icon-gongshui",ref:"ps_dispatching"}];else if(this.isGsDispatching)this.menuList=[{title:"供水调度",icon:"icon-paishui1",ref:"gs_dispatching"}];else if(this.menuList=this.menuList.filter((e=>!!this.isShowMenu(e.ref,e.title))),this.$route.query.hideBar){const e=this.$route.query.hideBar.split(",");this.menuList=this.menuList.filter((t=>!e.includes(t.title)))}this.defaultMenu&&(this.currentTab=this.menuList.find((e=>e.title===this.defaultMenu&&this.isShowMenu(e.ref,e.title))).title||""),!this.currentTab&&(this.currentTab=this.menuList[0].title),window.addEventListener("message",this.receive,!1),document.body.addEventListener("touchstart",(function(){}))},destroyed(){window.removeEventListener("message",this.receive,!1),window.removeEventListener("focusin",this.keyboardShow),window.removeEventListener("focusout",this.keyboardHide)},methods:{toggleTheme(){let e=document.documentElement.getAttribute("data-ai-theme");const t="dark"===e||"bigscreen1"===e,i="light"!==this.aiConfigs.theme?this.aiConfigs.theme:"dark";document.documentElement.setAttribute("data-ai-theme",t?"light":i),localStorage.setItem("wimAiTheme"+this.currentUser.tenantId,t?"light":i)},isHdkjPromise(){try{this.$utils.isHdkj()&&this.$native.check_permission({params:{permission:["audio"]},cb:e=>{var t;null!=e&&null!==(t=e.status)&&void 0!==t&&t.audio?console.warn("已获取音频权限"):(console.warn("未获取音频权限，申请获取"),this.$native.request_permission({params:{permission:["audio"]},cb:e=>{if(console.warn("音频权限检测情况",{cb:e}),this.$utils.isIos())if(1===(null==e?void 0:e.state))confirm("请前往系统设置中打开麦克风权限");else if(0===(null==e?void 0:e.state)){confirm("需要重启应用才能使用使用音频")&&this.$native.kill_app({params:{},cb:e=>{}})}}}))}})}catch(e){}},isShowMenu(e,t){var i;const s=this.isMobile?"App":"Web",n=(null===(i=this.aiConfigs)||void 0===i||null===(i=i.sceneType)||void 0===i?void 0:i[e])||[];let o=(this.aiConfigs.menu||"").split(",").indexOf(t)>-1;if(!this.aiConfigs.hasOwnProperty("sceneType")&&!this.aiConfigs.menu){o=["chat","report"].includes(e)}return n.length?n.includes(s):o},handleKeyboard(){window.addEventListener("focusin",this.keyboardShow),window.addEventListener("focusout",this.keyboardHide)},keyboardShow(){const e=document.documentElement.clientWidth||document.body.clientWidth,t=document.activeElement.tagName,i=document.activeElement.offsetParent;return null!=i&&i.className.includes("correct")?this.isKeyboard=!1:e===this.originWidth&&["INPUT","TEXTAREA"].includes(t)?void(this.isKeyboard=!0):this.isKeyboard=!1},keyboardHide(){this.isKeyboard=!1},setBodyStyle(){this.isMobile&&(document.body.style.setProperty("--font-size-12","14px"),document.body.style.setProperty("--font-size-12-mb","16px"),document.body.style.setProperty("--font-size-14","16px"),document.body.style.setProperty("--font-size-16","18px"),document.body.style.setProperty("--font-size-18","20px"),document.body.style.setProperty("--font-size-20","22px"),document.body.style.setProperty("--font-size-22","24px"),document.body.style.setProperty("--font-size-24","26px"));const e=Number(this.$route.query.screenScale),t=[12,14,16,18,20,22,24];if(!isNaN(e)&&e>16){const i=e-16;t.forEach((e=>{document.body.style.setProperty("--font-size-"+e,e+i+"px"),12===e&&document.body.style.setProperty(`--font-size-${e}-mb`,e+i+2+"px")})),e>40?(document.body.style.setProperty("--block-width","24px"),document.body.style.setProperty("--content-width","690px"),document.body.style.setProperty("--content-width-fullscreen","1680px"),document.body.style.setProperty("--header-area-width","380px"),document.body.style.setProperty("--voice-bar-height","8px"),document.body.style.setProperty("--voice-bar-height-max","32px"),document.body.style.setProperty("--voice-bar-width","5px"),document.body.style.setProperty("--file-img-width","180px"),document.body.style.setProperty("--file-img-height","100px"),document.body.style.setProperty("--result-img-height","500px"),document.body.style.setProperty("--fold-result","240px")):e>=30&&(document.body.style.setProperty("--block-width","20px"),document.body.style.setProperty("--content-width","480px"),document.body.style.setProperty("--content-width-fullscreen","890px"),document.body.style.setProperty("--header-area-width",260+e+"px"),document.body.style.setProperty("--voice-bar-height","8px"),document.body.style.setProperty("--voice-bar-height-max","32px"),document.body.style.setProperty("--voice-bar-width","5px"),document.body.style.setProperty("--file-img-width","180px"),document.body.style.setProperty("--file-img-height","100px"),document.body.style.setProperty("--result-img-height","500px"),document.body.style.setProperty("--fold-result","200px"))}},receive(e){try{if("string"!=typeof e.data||!e.data.startsWith("*#hd#*"))return;let{action:t,params:i}=JSON.parse(e.data.substring(6));if(i.isFullScreen&&(this.isMaxScreen=i.isFullScreen),"INIT_AI_PARAMS"===t||"OPEN_AI_PANEL"===t)if(console.warn({action:t,params:i}),i.type){this.currentTab=i.type;const e=this.menuList.find((e=>e.title===i.type))||{};if(!e.ref)return;setTimeout((()=>{this.$refs[e.ref]&&(this.$refs[e.ref].params.keyword=i.prompt,this.$refs[e.ref].getAnswer())}),200)}else if(i.agent){const e=this.menuList.find((e=>e.ref===i.agent))||{};if(this.currentTab=e.title,!e.ref)return;setTimeout((()=>{this.$refs[e.ref]&&(this.$refs[e.ref].params.keyword=i.prompt,this.$refs[e.ref].getAnswer())}),200)}"CLOSE_AI_PAGE"===t&&(this.$utils.isHdkj()?this.$native.goback({params:{},cb:e=>{console.log(e)}}):window.history.back())}catch(e){}},resize(){const e=window.innerWidth,t=this.$refs.headerArea&&this.$refs.headerArea.offsetWidth||0;this.isFullscreen=e-t>760,this.isFullscreenFold=e<=1080,this.isMobile||(this.isWebShowMobile=e<=800);const i=Number(this.$route.query.screenScale);if(!isNaN(i)&&i>16&&(i>=40?(this.isFullscreen=e-t>1260,this.isFullscreenFold=e<=1280):i>=30&&(this.isFullscreen=e-t>960,this.isFullscreenFold=e<=1280)),!this.$utils.isAndroid())return;const s=this.menuList.find((e=>e.title===this.currentTab)),n=window.innerHeight;this.innerHeight>n?this.isKeyboard=!0:this.$nextTick((()=>{this.$refs[s.ref].onBlur(),this.isKeyboard=!1;try{let e=this.$refs[s.ref].$refs.scrollbar&&this.$refs[s.ref].$refs.scrollbar.wrap;e&&(this.$refs[s.ref].params.scrollEnd=e.scrollTop+e.clientHeight+10>=e.scrollHeight)}catch(e){}}))},closeClick(){const e="*#hd#*"+JSON.stringify({action:"CLOSE_AI_ASSISTANT",params:{visible:!1}});window.parent.postMessage(e,"*"),this.$nextTick((()=>{this.isMaxScreen=!1}))},fullMinimizeClick(){const e="*#hd#*"+JSON.stringify({action:"MINIMIZE_AI_ASSISTANT",params:{isMinimize:!0}});window.parent.postMessage(e,"*")},fullScreenClick(){this.isMaxScreen=!this.isMaxScreen,this.isMaxScreen?this.isIframe&&(this.isFullscreenFold=!1):this.isFullscreenFold=!0;const e="*#hd#*"+JSON.stringify({action:"FULLSCREEN_AI_ASSISTANT",params:{isMaxScreen:this.isMaxScreen}});window.parent.postMessage(e,"*")},changeTab(e,t){e.disabled?this.$message.warning("正在建设中"):this.currentTab=e.title},handleSearch(){this.showDrawer=!this.showDrawer}}}),n=s,o=(i("c402"),i("2877")),r=Object(o.a)(n,(function(){var e=this,t=e._self._c;return t("div",{ref:"aiAssistantPage",staticClass:"ai-assistant-page",class:{fullscreen:e.isMaxScreen,"is-mobile":e.isMobile,"is-wim-pc":e.isUniwimPc,"is-show-header-bar":e.isWimpic||e.isTwin||e.isBigScreen,"is-hcs":e.isHCS}},[e.isWimpic||e.isTwin||e.isBigScreen?t("div",{staticClass:"ai-assistant-page-header",class:{"is-twin":e.isTwin}},[t("img",{staticClass:"logo",attrs:{src:e.aiLogo,alt:""}}),t("div",{staticClass:"title"},[e._v(e._s(e.aiTitle))]),t("div",{staticClass:"toolbar",class:{"close-button-right":e.isWimpic||e.isTwin}},[t("el-tooltip",{attrs:{content:"关闭",disabled:e.isMobile,placement:"top","open-delay":200,"hide-after":800}},[t("div",{staticClass:"icon-button fold-button el-icon-close",on:{click:e.closeClick}})])],1)]):e._e(),t("div",{ref:"pageContainer",staticClass:"ai-assistant-page-container",class:{"has-footer-toolbar":e.isMobile&&e.isShowFooterBar&&!e.hideMenu,"has-header-toolbar":e.isWimpic||e.isTwin||e.isBigScreen}},[e.isMobile?e._e():t("div",{ref:"headerArea",staticClass:"header-area",class:{"is-fold":e.isFullscreenFold}},[e.isWimpic||e.isTwin||e.hideNavLogo||e.isBigScreen?e._e():t("div",{staticClass:"ai-assistant-page-header"},[t("img",{staticClass:"logo",attrs:{src:e.aiLogo,alt:""}}),t("div",{staticClass:"title"},[e._v(e._s(e.aiTitle))]),e._e()]),t("div",{staticClass:"ai-assistant-page-toolbar"},e._l(e.menuList,(function(i,s){return t("div",{key:s,staticClass:"toolbar-item",class:{active:e.currentTab===i.title,disabled:i.disabled},on:{click:function(t){return e.changeTab(i,s)}}},[t("el-tooltip",{key:s,attrs:{placement:"right",disabled:!e.isFullscreenFold,"open-delay":200,"hide-after":800},scopedSlots:e._u([{key:"content",fn:function(){return[e._v(e._s(i.title))]},proxy:!0}],null,!0)},[t("i",{staticClass:"iconfont",class:i.icon})]),t("div",[e._v(e._s(i.title))])],1)})),0),t("div",{staticClass:"ai-assistant-page-toolbar-fixed"},[t("el-tooltip",{attrs:{content:"主题切换",placement:"top","open-delay":200,"hide-after":800}},[t("i",{staticClass:"icon-button fold-button el-icon-sunny",on:{click:e.toggleTheme}})]),!e.isIframe||e.isWimpic||e.isTwin?e._e():t("el-tooltip",{attrs:{content:"最小化",placement:"top","open-delay":200,"hide-after":800}},[t("i",{staticClass:"icon-button fold-button no-rotate iconfont icon-jianhao",on:{click:e.fullMinimizeClick}})]),!e.isIframe||e.isWimpic||e.isTwin?e._e():t("el-tooltip",{attrs:{content:"全屏",placement:"top","open-delay":200,"hide-after":800}},[t("i",{staticClass:"icon-button fold-button el-icon-copy-document",on:{click:e.fullScreenClick}})]),!e.isWimpic&&!e.isTwin&&!e.isIframe||e.isMaxScreen?t("el-tooltip",{attrs:{content:e.isFullscreenFold?"展开":"折叠",placement:"top","open-delay":200,"hide-after":800}},[e.isFullscreenFold?t("i",{staticClass:"icon-button fold-button el-icon-upload2",on:{click:function(t){e.isFullscreenFold=!e.isFullscreenFold}}}):t("i",{staticClass:"icon-button fold-button el-icon-download",on:{click:function(t){e.isFullscreenFold=!e.isFullscreenFold}}})]):e._e(),!e.isIframe||e.isWimpic||e.isTwin?e._e():t("el-tooltip",{attrs:{content:"关闭",placement:"top","open-delay":200,"hide-after":800}},[t("i",{staticClass:"icon-button fold-button el-icon-close",on:{click:function(t){return e.closeClick()}}})])],1)]),t("keep-alive",[t(e.currentComponent.ref,e._b({key:e.currentComponent.key,ref:e.currentComponent.ref,tag:"component",attrs:{"custom-title":e.currentComponent.customTitle,"is-full-screen":e.isFullscreen||e.isMaxScreen,isWebShowMobile:e.isWebShowMobile},on:{"input-focus":t=>e.isInputFocus=t,openSearchResult:e.handleSearch}},"component",e.currentComponent,!1))],1),e.isMobile||e.isWebShowMobile||!e.showDrawer?e._e():t("div",{staticClass:"ai-assistant-page-draw",class:{visible:e.showDrawer}},[t("div",{staticClass:"container"},[t("Search",{on:{close:function(t){e.showDrawer=!1}}})],1)])],1),e.isMobile&&e.isShowFooterBar&&!e.hideMenu?t("div",{staticClass:"ai-assistant-footerbar"},e._l(e.menuList,(function(i){return t("div",{key:i.icon,staticClass:"footerbar-item",class:{active:i.title===e.currentTab},on:{click:function(t){e.currentTab=i.title,e.isInputFocus=!1}}},[t("i",{staticClass:"iconfont",class:i.icon}),t("span",{staticClass:"title"},[e._v(e._s(i.title))])])})),0):e._e()])}),[],!1,null,"17054d3e",null);t.default=r.exports},c402:function(e,t,i){"use strict";i("8cfa")}}]);