var background=function(){"use strict";var O,D;function x(e){return e==null||typeof e=="function"?{main:e}:e}var b=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(b||{}),S=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(S||{});const N=x(()=>{const e={},l={};let d=!0,R=null,C=null;const M="http://127.0.0.1:7331/event";async function A(o){const n=new TextEncoder().encode(o),r=await crypto.subtle.digest("SHA-256",n);return Array.from(new Uint8Array(r)).map(c=>c.toString(16).padStart(2,"0")).join("")}async function v(o){try{await fetch(M,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})}catch(s){console.warn(`Failed to send event to Python server at ${M}:`,s)}}async function g(){const o=Object.keys(e).flatMap(i=>{const c=parseInt(i,10);return P(e[c]||[])}).sort((i,c)=>i.timestamp-c.timestamp);console.log("polling allSteps",o);const s={name:"Recorded Workflow",description:`Recorded on ${new Date().toLocaleString()}`,version:"1.0.0",input_schema:[],steps:o},n=JSON.stringify(o),r=await A(n);if(R!==null&&r===R)return s;R=r;const t={type:"WORKFLOW_UPDATE",timestamp:Date.now(),payload:s};v(t);debugger;return s}function U(){const o=d?"recording":"stopped";chrome.tabs.query({},s=>{s.forEach(n=>{n.id&&chrome.tabs.sendMessage(n.id,{type:"SET_RECORDING_STATUS",payload:d}).catch(r=>{})})}),chrome.runtime.sendMessage({type:"recording_status_updated",payload:{status:o}}).catch(s=>{})}function y(o,s){if(!d)return;console.log(`Sending ${o}:`,s);const n=s.tabId;C=n,n?(e[n]||(e[n]=[]),e[n].push({messageType:o,timestamp:Date.now(),tabId:n,...s}),g()):console.warn("Tab event received without tabId in payload:",o,s)}chrome.tabs.onCreated.addListener(o=>{y("CUSTOM_TAB_CREATED",{tabId:o.id,openerTabId:o.openerTabId,url:o.pendingUrl||o.url,windowId:o.windowId,index:o.index})}),chrome.tabs.onUpdated.addListener((o,s,n)=>{(s.url||s.status==="complete")&&(y("CUSTOM_TAB_UPDATED",{tabId:o,changeInfo:s,windowId:n.windowId,url:n.url,title:n.title}),d&&s.url&&(console.log(`Page navigation detected for tab ${o}, ensuring recording status is synced`),setTimeout(()=>{chrome.tabs.sendMessage(o,{type:"SET_RECORDING_STATUS",payload:d}).catch(r=>{console.debug(`Could not sync recording status to tab ${o} after navigation: ${r.message}`)})},1e3)))}),chrome.tabs.onActivated.addListener(o=>{y("CUSTOM_TAB_ACTIVATED",{tabId:o.tabId,windowId:o.windowId})}),chrome.tabs.onRemoved.addListener((o,s)=>{y("CUSTOM_TAB_REMOVED",{tabId:o,windowId:s.windowId,isWindowClosing:s.isWindowClosing})});function P(o){var n;const s=[];for(const r of o)switch(r.messageType){case"CUSTOM_CLICK_EVENT":{const t=r;if(t.url&&t.frameUrl&&t.xpath&&t.elementTag){const i={type:"click",timestamp:t.timestamp,tabId:t.tabId,url:t.url,uuid:t.uuid,isExtract:t.isExtract||!1,frameUrl:t.frameUrl,xpath:t.xpath,cssSelector:t.cssSelector,elementTag:t.elementTag,elementText:t.elementText,screenshot:t.screenshot};s.push(i)}else console.warn("Skipping incomplete CUSTOM_CLICK_EVENT:",t);break}case"SCREENSHOT":{const t=r,i={type:"take_screenshot",isExtract:t.isExtract||!1,timestamp:t.timestamp,screenshotType:"full_page",tabId:t.tabId};s.push(i);break}case"CUSTOM_INPUT_EVENT":{const t=r;if(t.url&&t.xpath&&t.elementTag){const i=s.length>0?s[s.length-1]:null;if(i&&i.type==="input"&&i.tabId===t.tabId&&i.url===t.url&&i.frameUrl===t.frameUrl&&i.xpath===t.xpath&&i.cssSelector===t.cssSelector&&i.elementTag===t.elementTag)i.value=t.value,i.timestamp=t.timestamp,i.screenshot=t.screenshot;else{const c={type:"input",isExtract:t.isExtract||!1,timestamp:t.timestamp,tabId:t.tabId,url:t.url,frameUrl:t.frameUrl,uuid:t.uuid,xpath:t.xpath,cssSelector:t.cssSelector,elementTag:t.elementTag,value:t.value,screenshot:t.screenshot};s.push(c)}}else console.warn("Skipping incomplete CUSTOM_INPUT_EVENT:",t);break}case"CUSTOM_KEY_EVENT":{const t=r;if(t.url&&t.key){const i={type:"key_press",isExtract:t.isExtract||!1,timestamp:t.timestamp,tabId:t.tabId,url:t.url,frameUrl:t.frameUrl,key:t.key,xpath:t.xpath,uuid:t.uuid,cssSelector:t.cssSelector,elementTag:t.elementTag,screenshot:t.screenshot};s.push(i)}else console.warn("Skipping incomplete CUSTOM_KEY_EVENT:",t);break}case"RRWEB_EVENT":{const t=r;if(t.type===b.IncrementalSnapshot&&t.data.source===S.Scroll){const i=t.data,c=l[t.tabId],u=s.length>0?s[s.length-1]:null;if(u&&u.type==="scroll"&&u.tabId===t.tabId&&u.targetId===i.id)u.scrollX=i.x,u.scrollY=i.y,u.timestamp=t.timestamp,u.isExtract=t.isExtract||!1,u.uuid=t.uuid;else{const a={type:"scroll",isExtract:t.isExtract||!1,timestamp:t.timestamp,tabId:t.tabId,uuid:t.uuid,targetId:i.id,scrollX:i.x,scrollY:i.y,url:c==null?void 0:c.url};s.push(a)}}else if(t.type===b.Meta&&((n=t.data)!=null&&n.href)){const i=t.data,c={type:"navigation",isExtract:t.isExtract||!1,timestamp:t.timestamp,tabId:t.tabId,uuid:t.uuid,url:i.href};s.push(c)}break}}return s}chrome.runtime.onMessage.addListener((o,s,n)=>{var i,c,u;let r=!1;const t=["CUSTOM_CLICK_EVENT","CUSTOM_INPUT_EVENT","CUSTOM_SELECT_EVENT","CUSTOM_KEY_EVENT"];if(o.type==="UPDATE_EVENT_METADATA"){const{tabId:a,timestamp:f,uuid:h,metadata:E}=o.payload,T=e[a].filter(p=>p.timestamp===f&&h==p.uuid);T&&T.forEach(p=>{p.isExtract=E.isExtract}),console.log("polling",e),g()}if(o.type==="RRWEB_EVENT"||t.includes(o.type)){if(!d)return!1;if(!((i=s.tab)!=null&&i.id))return console.warn("Received event without tab ID:",o),!1;const a=s.tab.id,f=t.includes(o.type),h=(E,T)=>{var m,I;e[a]||(e[a]=[]),l[a]||(l[a]={}),(m=s.tab)!=null&&m.url&&!l[a].url&&(l[a].url=s.tab.url),(I=s.tab)!=null&&I.title&&!l[a].title&&(l[a].title=s.tab.title);const p={...E,tabId:a,messageType:o.type,screenshot:T};e[a].push(p),g()};f&&((c=s.tab)!=null&&c.windowId)?(r=!0,chrome.tabs.captureVisibleTab(s.tab.windowId,{format:"jpeg",quality:75},E=>{chrome.runtime.lastError?(console.error("Screenshot failed:",chrome.runtime.lastError.message),h(o.payload)):h(o.payload,E)})):o.type==="RRWEB_EVENT"?h(o.payload):f&&(console.warn("Storing custom event without screenshot due to missing windowId or other issue."),h(o.payload))}else{if(o.type==="GET_RECORDING_DATA")return r=!0,(async()=>{const a=await g(),f=d?"recording":a.steps.length>0?"stopped":"idle";n({workflow:a,recordingStatus:f})})(),r;if(o.type==="START_RECORDING"){if(console.log("Received START_RECORDING request."),Object.keys(e).forEach(a=>delete e[parseInt(a)]),Object.keys(l).forEach(a=>delete l[parseInt(a)]),console.log("Cleared previous recording data."),!d){d=!0,console.log("Recording status set to: true"),U();const a={type:"RECORDING_STARTED",timestamp:Date.now(),payload:{message:"Recording has started"}};v(a)}n({status:"started"})}else if(o.type==="STOP_RECORDING"){if(console.log("Received STOP_RECORDING request."),d){d=!1,console.log("Recording status set to: false"),U();const a={type:"RECORDING_STOPPED",timestamp:Date.now(),payload:{message:"Recording has stopped"}};v(a)}n({status:"stopped"})}else if(o.type==="SCREENSHOT"){if(console.log("Received SCREENSHOT request."),!C)return console.warn("Received event without tab ID:",o),!1;const a=C||1;((h,E)=>{var p,m;e[a]||(e[a]=[]),l[a]||(l[a]={}),(p=s.tab)!=null&&p.url&&!l[a].url&&(l[a].url=s.tab.url),(m=s.tab)!=null&&m.title&&!l[a].title&&(l[a].title=s.tab.title);const T={...h,tabId:a,timestamp:Date.now(),messageType:o.type,screenshot:E};e[a].push(T),g()})(o.payload)}else o.type==="REQUEST_RECORDING_STATUS"&&((u=s.tab)!=null&&u.id)&&(console.log(`Sending initial status (${d}) to tab ${s.tab.id}`),n({isRecordingEnabled:d}))}return r}),console.log("Background script loaded. Initial recording status:",d,"(EventType:",b,", IncrementalSource:",S,")"),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!0}).catch(o=>console.error("Failed to set panel behavior:",o))});function V(){}(D=(O=globalThis.browser)==null?void 0:O.runtime)!=null&&D.id?globalThis.browser:globalThis.chrome;function w(e,...l){}const k={debug:(...e)=>w(console.debug,...e),log:(...e)=>w(console.log,...e),warn:(...e)=>w(console.warn,...e),error:(...e)=>w(console.error,...e)};let _;try{_=N.main(),_ instanceof Promise&&console.warn("The background's main() function return a promise, but it must be synchronous")}catch(e){throw k.error("The background crashed on startup!"),e}return _}();
background;
