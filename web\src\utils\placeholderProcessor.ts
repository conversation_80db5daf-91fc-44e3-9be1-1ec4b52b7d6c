/**
 * 占位符处理器
 * 支持多种占位符格式：
 * - #{Response[].vals[].timeStr} - 井号格式
 * - ${Code} - 美元符号格式
 * - ${Response[].Name} - 美元符号数组格式
 * - ${Response[0].Name} - 美元符号索引格式
 * - ${Response[].val[].time} - 美元符号嵌套数组格式
 */

export interface PlaceholderData {
  [key: string]: any
}

export interface PlaceholderOptions {
  // 数组展示方式
  arrayDisplay?: 'list' | 'table' | 'inline' | 'custom'
  // 分隔符（用于inline模式）
  separator?: string
  // 最大显示数量
  maxItems?: number
  // 自定义格式化函数
  formatter?: (value: any, path: string) => string
  // 是否显示索引
  showIndex?: boolean
  // 支持的占位符格式
  supportedFormats?: ('hash' | 'dollar')[]
}

export class PlaceholderProcessor {
  private data: PlaceholderData
  private options: PlaceholderOptions

  constructor(data: PlaceholderData, options: PlaceholderOptions = {}) {
    this.data = data
    this.options = {
      arrayDisplay: 'list',
      separator: ', ',
      maxItems: 100,
      showIndex: false,
      supportedFormats: ['hash', 'dollar'],
      ...options
    }
  }

  /**
   * 处理文本中的所有占位符
   * 支持 #{path} 和 ${path} 两种格式
   */
  processText(text: string): string {
    let result = text

    // 处理 #{path} 格式的占位符
    if (this.options.supportedFormats?.includes('hash')) {
      const hashRegex = /#\{([^}]+)\}/g
      result = result.replace(hashRegex, (match, path) => {
        try {
          return this.resolvePlaceholder(path.trim())
        } catch (error) {
          console.warn(`占位符解析失败: ${match}`, error)
          return match // 保留原始占位符
        }
      })
    }

    // 处理 ${path} 格式的占位符
    if (this.options.supportedFormats?.includes('dollar')) {
      const dollarRegex = /\$\{([^}]+)\}/g
      result = result.replace(dollarRegex, (match, path) => {
        try {
          return this.resolvePlaceholder(path.trim())
        } catch (error) {
          console.warn(`占位符解析失败: ${match}`, error)
          return match // 保留原始占位符
        }
      })
    }

    return result
  }

  /**
   * 解析单个占位符路径
   */
  private resolvePlaceholder(path: string): string {
    try {
      ;
      const segments = this.parsePath(path)
      console.log(`解析占位符: ${path}`, { segments, data: this.data })

      const result = this.getValue(this.data, segments)
      console.log(`获取结果: ${path}`, { result })

      if (result === undefined || result === null) {
        return `[未找到: ${path}]`
      }

      const formatted = this.formatValue(result, path)
      console.log(`格式化结果: ${path}`, { formatted })

      return formatted
    } catch (error) {
      console.error(`占位符处理错误: ${path}`, error)
      return `[错误: ${path}]`
    }
  }

  /**
   * 解析路径，处理数组标记 [] 和数组索引 [0]
   * 支持多种格式：
   * - innerCurrentTime (简单字段)
   * - Response[].Name (数组遍历)
   * - Response[0].Name (数组索引)
   * - Response[].vals[].time (嵌套数组)
   * - Response[0].vals[1].timeStr (具体索引)
   */
  private parsePath(path: string): Array<string | symbol | number> {
    const segments: Array<string | symbol | number> = []

    // 清理路径，移除首尾空格
    const cleanPath = path.trim()

    // 如果是简单字段（不包含点和括号），直接返回
    if (!cleanPath.includes('.') && !cleanPath.includes('[')) {
      return [cleanPath]
    }

    // 分割路径，但保留括号信息
    // 使用正则表达式匹配 field[index] 或 field[] 或 field 的模式
    const parts = cleanPath.split('.')

    for (const part of parts) {
      if (!part) continue // 跳过空部分

      // 检查是否包含数组标记
      const arrayMatch = part.match(/^([^[\]]+)(\[(\d*)\])?$/)

      if (arrayMatch) {
        const fieldName = arrayMatch[1].trim()
        const bracketPart = arrayMatch[2] // 整个括号部分 [xxx]
        const indexContent = arrayMatch[3] // 括号内的内容

        // 添加字段名
        if (fieldName) {
          segments.push(fieldName)
        }

        // 处理数组标记
        if (bracketPart) {
          if (indexContent === '' || indexContent === undefined) {
            // 空索引 [] - 表示遍历所有元素
            segments.push('__ARRAY__')
          } else {
            // 具体索引 [0], [1] 等
            const index = parseInt(indexContent, 10)
            if (!isNaN(index) && index >= 0) {
              segments.push(index)
            } else {
              console.warn(`无效的数组索引: ${indexContent}`)
            }
          }
        }
      } else {
        // 如果正则匹配失败，可能是复杂的格式，尝试简单处理
        console.warn(`无法解析路径部分: ${part}`)
        segments.push(part)
      }
    }

    return segments
  }

  /**
   * 根据路径获取值
   */
  private getValue(obj: any, segments: Array<string | symbol | number>): any {
    let current = obj

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]

      if (segment === '__ARRAY__') {
        // 处理数组遍历 []
        if (!Array.isArray(current)) {
          console.warn(`期望数组但得到:`, typeof current, current)
          return undefined
        }

        // 如果这是最后一个段，返回整个数组
        if (i === segments.length - 1) {
          return current
        }

        // 否则，需要遍历数组获取后续字段
        const nextSegments = segments.slice(i + 1)
        const results: any[] = []

        current.forEach((item) => {
          const value = this.getValue(item, nextSegments)
          if (value !== undefined) {
            // 如果返回的是数组，展开它
            if (Array.isArray(value)) {
              results.push(...value)
            } else {
              results.push(value)
            }
          }
        })

        return results.length > 0 ? results : undefined
      } else if (typeof segment === 'number') {
        // 处理数组索引 [0], [1] 等
        if (!Array.isArray(current)) {
          console.warn(`期望数组但得到:`, typeof current, current)
          return undefined
        }

        if (segment < 0 || segment >= current.length) {
          console.warn(`数组索引超出范围: ${segment}, 数组长度: ${current.length}`)
          return undefined
        }

        current = current[segment]
      } else {
        // 普通字段
        if (current && typeof current === 'object' && String(segment) in current) {
          current = current[String(segment)]
        } else {
          console.warn(`字段不存在: ${String(segment)}`, current)
          return undefined
        }
      }
    }

    return current
  }

  /**
   * 格式化值
   */
  private formatValue(value: any, path: string): string {
    if (this.options.formatter) {
      return this.options.formatter(value, path)
    }

    if (Array.isArray(value)) {
      return this.formatArray(value, path)
    }

    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2)
    }

    return String(value)
  }

  /**
   * 格式化数组
   */
  private formatArray(array: any[], path: string): string {
    const maxItems = this.options.maxItems || 100
    const items = array.slice(0, maxItems)
    
    switch (this.options.arrayDisplay) {
      case 'inline':
        return items.map(item => String(item)).join(this.options.separator || ', ')
      
      case 'list':
        return items.map((item, index) => {
          const prefix = this.options.showIndex ? `${index + 1}. ` : '• '
          return `${prefix}${String(item)}`
        }).join('\n')
      
      case 'table':
        return this.formatAsTable(items)
      
      case 'custom':
        // 自定义格式化由formatter处理
        return items.map(item => String(item)).join('\n')
      
      default:
        return items.map(item => String(item)).join('\n')
    }
  }

  /**
   * 格式化为表格
   */
  private formatAsTable(items: any[]): string {
    if (items.length === 0) return ''
    
    // 如果是对象数组，创建表格
    if (typeof items[0] === 'object' && items[0] !== null) {
      const keys = Object.keys(items[0])
      const header = keys.join('\t')
      const rows = items.map(item => 
        keys.map(key => String(item[key] || '')).join('\t')
      )
      return [header, ...rows].join('\n')
    }
    
    // 简单值数组
    return items.map((item, index) => `${index + 1}\t${String(item)}`).join('\n')
  }

  /**
   * 获取文本中的所有占位符
   * 支持 #{path} 和 ${path} 两种格式
   */
  static extractPlaceholders(text: string): string[] {
    const placeholders: string[] = []

    // 提取 #{path} 格式的占位符
    const hashRegex = /#\{([^}]+)\}/g
    let match
    while ((match = hashRegex.exec(text)) !== null) {
      placeholders.push(match[1].trim())
    }

    // 提取 ${path} 格式的占位符
    const dollarRegex = /\$\{([^}]+)\}/g
    while ((match = dollarRegex.exec(text)) !== null) {
      placeholders.push(match[1].trim())
    }

    return [...new Set(placeholders)] // 去重
  }

  /**
   * 验证占位符路径是否有效
   */
  validatePlaceholder(path: string): { valid: boolean, error?: string } {
    try {
      const segments = this.parsePath(path)
      const result = this.getValue(this.data, segments)
      return { valid: true }
    } catch (error: any) {
      return { valid: false, error: error.message }
    }
  }

  /**
   * 预览占位符的值
   */
  previewPlaceholder(path: string): string {
    try {
      return this.resolvePlaceholder(path)
    } catch (error: any) {
      return `[错误: ${error.message}]`
    }
  }
}

/**
 * 便捷函数：处理文本中的占位符
 */
export function processPlaceholders(
  text: string, 
  data: PlaceholderData, 
  options?: PlaceholderOptions
): string {
  const processor = new PlaceholderProcessor(data, options)
  return processor.processText(text)
}

/**
 * 便捷函数：提取占位符
 */
export function extractPlaceholders(text: string): string[] {
  return PlaceholderProcessor.extractPlaceholders(text)
}

/**
 * 示例数据和用法
 */
export const exampleData = {
  innerCurrentTime: '2024-07-03 15:30:00',
  Code: 'ABC123',
  Response: [
    {
      Name: 'Device1',
      deviceName: 'Temperature Sensor 1',
      vals: [
        { timeStr: '2024-01-01 10:00:00', time: '10:00', value: 100 },
        { timeStr: '2024-01-01 11:00:00', time: '11:00', value: 200 },
        { timeStr: '2024-01-01 12:00:00', time: '12:00', value: 150 }
      ],
      val: [
        { time: '10:00:00', data: 'A' },
        { time: '11:00:00', data: 'B' }
      ],
      status: 'success'
    },
    {
      Name: 'Device2',
      deviceName: 'Humidity Sensor 1',
      vals: [
        { timeStr: '2024-01-02 10:00:00', time: '10:00', value: 300 },
        { timeStr: '2024-01-02 11:00:00', time: '11:00', value: 250 }
      ],
      val: [
        { time: '10:30:00', data: 'C' },
        { time: '11:30:00', data: 'D' }
      ],
      status: 'success'
    }
  ],
  User: {
    name: 'John Doe',
    email: '<EMAIL>'
  }
}

// 使用示例:
// const processor = new PlaceholderProcessor(exampleData, { arrayDisplay: 'inline', separator: ', ' })
//
// 支持的占位符格式：
// ${innerCurrentTime} -> '2024-07-03 15:30:00'
// ${Code} -> 'ABC123'
// ${Response[].Name} -> 'Device1, Device2'
// ${Response[0].Name} -> 'Device1'
// ${Response[].vals[].time} -> '10:00, 11:00, 12:00, 10:00, 11:00'
// #{Response[].vals[].timeStr} -> '2024-01-01 10:00:00, 2024-01-01 11:00:00, ...'
//
// 实际使用：
// const result1 = processor.processText('当前时间: ${innerCurrentTime}')
// const result2 = processor.processText('设备名称: ${Response[].Name}')
// const result3 = processor.processText('第一个设备: ${Response[0].Name}')
// const result4 = processor.processText('所有时间: ${Response[].vals[].time}')
// const result5 = processor.processText('混合格式: ${innerCurrentTime} - ${Response[].Name}')
