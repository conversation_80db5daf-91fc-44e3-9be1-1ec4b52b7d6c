// Vant 全局主题色
:root:root {
  --van-primary-color: #0054D9;
  --van-nav-bar-icon-color: #333333;
  --van-nav-bar-title-text-color: #333333;
  --van-nav-bar-arrow-size: 18px;
  --van-tab-active-text-color: #0054D9;
  --van-tabs-bottom-bar-width: 20px;
  --van-cell-vertical-padding: 8px;
}

body {
  font-family: Inter, system-ui, Avenir, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

* {
  user-select: none;
}

input,
textarea,
[contenteditable] {
  user-select: auto;
}



.wim-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .wim-header {
    display: flex;
    flex-direction: column;
    min-height: var(--van-nav-bar-height);
    flex-shrink: 0;
  }
  .wim-body {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  .wim-footer{
    height: 44px;
    flex-shrink: 0;
  }
}

.el-table {
  th.el-table__cell {
    background: #F7F7F9 !important;
    font-weight: 500;
  }
}
